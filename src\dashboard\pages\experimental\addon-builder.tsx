import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../api/auth/[...nextauth]';
import { Box, VStack, Heading, Alert, AlertIcon, AlertDescription, Spinner, Center } from '@chakra-ui/react';
import Layout from '../../components/Layout';
import AddonBuilder from '../../components/AddonBuilder';
import useExperimentalFeatures from '../../hooks/useExperimentalFeatures';

export default function AddonBuilderPage() {
  const { hasAccess, isLoading, reason } = useExperimentalFeatures();

  if (isLoading) {
    return (
      <Layout>
        <Center p={8}>
          <VStack spacing={4}>
            <Spinner size="xl" />
            <Heading size="md">Checking access...</Heading>
          </VStack>
        </Center>
      </Layout>
    );
  }

  if (!hasAccess) {
    return (
      <Layout>
        <Box p={8}>
          <Alert status="warning">
            <AlertIcon />
            <AlertDescription>
              You need experimental features access to use the Addon Builder. 
              {reason === 'unauthenticated' && ' Please sign in first.'}
              {reason === 'no_access' && ' Please apply for experimental features access from the overview page.'}
            </AlertDescription>
          </Alert>
        </Box>
      </Layout>
    );
  }

  return (
    <Layout>
      <VStack spacing={8} p={8} align="stretch" maxW="100%" overflow="hidden">
        <Box textAlign="center">
          <Heading size="xl" mb={2}>
            ⚗️ Addon Builder
          </Heading>
          <Alert status="info" mb={4}>
            <AlertIcon />
            <AlertDescription>
              <strong>Experimental Feature:</strong> This is a beta feature for creating custom Discord bot addons. 
              Use with caution and report any issues you encounter.
            </AlertDescription>
          </Alert>
        </Box>

        <AddonBuilder />
      </VStack>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);
  
  if (!session) {
    return {
      redirect: {
        destination: '/api/auth/signin?callbackUrl=%2Fexperimental%2Faddon-builder',
        permanent: false,
      },
    };
  }

  return { props: {} };
}; 