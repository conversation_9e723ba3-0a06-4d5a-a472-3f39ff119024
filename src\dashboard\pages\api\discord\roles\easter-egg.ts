import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

// Easter egg role configuration
const EASTER_EGG_ROLE = {
  name: "Easter Egg Hunter",
  color: "#FFD700", // Gold color
  permissions: 0n, // No special permissions
  hoist: true, // Show separately in member list
  mentionable: true,
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get user's Discord ID from session
    const userId = (session.user as any).id;
    if (!userId) {
      return res.status(400).json({ error: 'User ID not found' });
    }

    // Get guild ID from environment
    const guildId = process.env.DISCORD_GUILD_ID;
    const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/roles`, {
      headers: {
        Authorization: `Bot ${process.env.DISCORD_BOT_TOKEN}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch guild roles');
    }

    const roles = await response.json();
    let easterEggRole = roles.find((role: any) => role.name === EASTER_EGG_ROLE.name);

    // Create role if it doesn't exist
    if (!easterEggRole) {
      const createResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}/roles`, {
        method: 'POST',
        headers: {
          Authorization: `Bot ${process.env.DISCORD_BOT_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...EASTER_EGG_ROLE,
          color: parseInt(EASTER_EGG_ROLE.color.replace('#', ''), 16),
        }),
      });

      if (!createResponse.ok) {
        throw new Error('Failed to create easter egg role');
      }

      easterEggRole = await createResponse.json();
    }

    // Add role to user
    const addRoleResponse = await fetch(
      `https://discord.com/api/v10/guilds/${guildId}/members/${userId}/roles/${easterEggRole.id}`,
      {
        method: 'PUT',
        headers: {
          Authorization: `Bot ${process.env.DISCORD_BOT_TOKEN}`,
        },
      }
    );

    if (!addRoleResponse.ok) {
      throw new Error('Failed to add role to user');
    }

    // Send congratulatory message in system channel
    const systemChannelId = process.env.DISCORD_SYSTEM_CHANNEL_ID;
    if (systemChannelId) {
      await fetch(`https://discord.com/api/v10/channels/${systemChannelId}/messages`, {
        method: 'POST',
        headers: {
          Authorization: `Bot ${process.env.DISCORD_BOT_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          embeds: [{
            title: '🎉 Easter Egg Master!',
            description: `Congratulations <@${userId}>! You've found all the dashboard easter eggs and earned the ${EASTER_EGG_ROLE.name} role!`,
            color: parseInt(EASTER_EGG_ROLE.color.replace('#', ''), 16),
            timestamp: new Date().toISOString(),
          }],
        }),
      });
    }

    return res.status(200).json({ success: true });
  } catch (error: any) {
    console.error('Error in easter-egg role handler:', error);
    return res.status(500).json({ error: error.message });
  }
} 