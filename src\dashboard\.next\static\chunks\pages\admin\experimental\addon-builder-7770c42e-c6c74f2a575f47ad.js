(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5301],{7369:(e,o,t)=>{"use strict";t.r(o),t.d(o,{default:()=>S});var s=t(94513),r=t(94285),n=t(87405),l=t(49008),i=t(53424),a=t(58686),d=t(27263),c=t(6179);t(24472);var u=t(89654),h=t(24641),x=t(31337),g=t(99538),p=t(94069),m=t(95916);let b={gradient:e=>{let{id:o,sourceX:t,sourceY:r,targetX:n,targetY:l,sourcePosition:i,targetPosition:a,style:d,markerEnd:u,data:h,animated:x}=e,g="gradient-".concat(o),[p]=(0,c.oN)({sourceX:t,sourceY:r,sourcePosition:i,targetX:n,targetY:l,targetPosition:a,borderRadius:20});return(0,s.jsxs)(s.Frag<PERSON>,{children:[(0,s.jsx)("defs",{children:(0,s.jsxs)("linearGradient",{id:g,x1:t,y1:r,x2:n,y2:l,gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:(null==h?void 0:h.sourceColor)||"#6b7280"}),(0,s.jsx)("stop",{offset:"100%",stopColor:(null==h?void 0:h.targetColor)||"#6b7280"})]})}),(0,s.jsx)("path",{d:p,stroke:"url(#".concat(g,")"),strokeWidth:2,fill:"none",markerEnd:u,style:d,className:x?"react-flow__edge-path-animated":""})]})}},f=e=>r.useMemo(()=>{var o;return{trigger:(null==e||null==(o=e.colors)?void 0:o.primary)||"#6b7280",command:"#3b82f6",event:"#10b981",action:"#a855f7",apiRequest:"#06b6d4",condition:"#f59e0b"}},[e]),j=[{id:"1",type:"trigger",position:{x:100,y:200},data:{label:"Start Here"}}],w=[],S=()=>{var e;let{data:o}=(0,i.useSession)(),t=(0,a.useRouter)(),{currentScheme:S}=(0,d.DP)(),y=f(S),[C,v,k]=(0,c.ck)(j),[T,D,A]=(0,c.fM)(w),[B,z]=(0,r.useState)(null),[F,O]=(0,r.useState)(!1),E=(0,r.useRef)(null),{isOpen:I,onOpen:M,onClose:H}=(0,n.useDisclosure)(),{isOpen:N,onOpen:V,onClose:R}=(0,n.useDisclosure)(),{isOpen:W,onOpen:L,onClose:P}=(0,n.useDisclosure)(),{isOpen:_,onOpen:G,onClose:U}=(0,n.useDisclosure)(),{isOpen:J,onOpen:Y,onClose:Z}=(0,n.useDisclosure)(),{isOpen:q,onOpen:K,onClose:X}=(0,n.useDisclosure)(),[Q,$]=(0,r.useState)([]),[ee,eo]=(0,r.useState)([]),[et,es]=(0,r.useState)([]),[er,en]=(0,r.useState)({name:"",description:"",author:"",version:"1.0.0",tags:[],isPublic:!1}),[el,ei]=(0,r.useState)(""),[ea,ed]=(0,r.useState)(null),[ec,eu]=(0,r.useState)(""),[eh,ex]=(0,r.useState)(""),eg=(0,r.useRef)(null),ep=(0,n.useToast)(),em=(null==o||null==(e=o.user)?void 0:e.id)==="933023999770918932";if(r.useEffect(()=>{o&&!em&&t.push("/")},[o,em,t]),o&&!em)return null;let eb=(0,r.useCallback)((e,o)=>{v(t=>t.map(t=>t.id===e?{...t,data:{...t.data,...o}}:t))},[v]),ef=(0,r.useMemo)(()=>({command:e=>(0,s.jsx)(u.A,{...e,updateNodeData:eb}),event:e=>(0,s.jsx)(h.A,{...e,updateNodeData:eb}),action:e=>(0,s.jsx)(x.A,{...e,updateNodeData:eb}),condition:e=>(0,s.jsx)(g.A,{...e,updateNodeData:eb}),trigger:p.A,apiRequest:e=>(0,s.jsx)(m.A,{...e,updateNodeData:eb})}),[eb]),ej=(0,r.useCallback)(e=>{let o=C.find(o=>o.id===e.source),t=C.find(o=>o.id===e.target);if(!o||!t)return;if("trigger"===o.type){let o=T.filter(o=>o.source===e.source);if(o.length>0){var s;let e=null==(s=C.find(e=>e.id===o[0].target))?void 0:s.type;if(e&&e!==t.type)return void alert("❌ Flow Rule Violation!\n\nYou can only connect Start to EITHER:\n• Command (for slash commands)\n• Event (for automatic triggers)\n\nNot both! Please choose one path.")}if("command"!==t.type&&"event"!==t.type)return void alert("❌ Invalid Connection!\n\nStart can only connect to:\n• Command blocks\n• Event blocks")}if("action"===o.type)return void alert("❌ Invalid Connection!\n\nAction blocks should be at the end of your flow.\nThey cannot connect to other blocks.");let r=y[o.type]||"#6b7280",n=y[t.type]||"#6b7280",l={...e,type:"gradient",animated:!0,markerEnd:{type:c.TG.ArrowClosed,color:n},style:{strokeWidth:2},data:{sourceColor:r,targetColor:n}};D(e=>(0,c.rN)(l,e))},[D,C,T]),ew=(0,r.useCallback)(e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},[]),eS=(0,r.useCallback)(e=>{var o;e.preventDefault();let t=null==(o=E.current)?void 0:o.getBoundingClientRect(),s=e.dataTransfer.getData("application/reactflow");if(void 0===s||!s||!t)return;let r=null==B?void 0:B.project({x:e.clientX-t.left,y:e.clientY-t.top}),n={id:"".concat(s,"-").concat(Date.now()),type:s,position:r||{x:0,y:0},data:{label:"New ".concat(s)}};v(e=>e.concat(n))},[B,v]),ey=async e=>{try{let o={name:e.name,description:e.description,author:e.author,version:e.version,nodes:C,edges:T},t=await fetch("/api/admin/experimental/addon-builder/build",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(t.ok)ep({title:"Addon Built Successfully!",description:"Your addon has been built and is now available in the bot.",status:"success",duration:5e3,isClosable:!0}),eC();else{let e=await t.json();if(e.errors&&Array.isArray(e.errors)){let o=e.errors.map((e,o)=>"".concat(o+1,". ").concat(e)).join("\n");ep({title:"Configuration Issues Found",description:"Please fix these issues:\n\n".concat(o),status:"warning",duration:1e4,isClosable:!0})}else ep({title:"Build Failed",description:e.details||e.message||"Failed to build addon. Please check your flow and try again.",status:"error",duration:5e3,isClosable:!0})}}catch(e){ep({title:"Build Error",description:"An error occurred while building the addon.",status:"error",duration:5e3,isClosable:!0})}finally{O(!1)}},eC=async()=>{let e=localStorage.getItem("addon-builder-saved-flows"),o=[];if(e)try{o=JSON.parse(e)}catch(e){}let t=[];try{let e=await fetch("/api/admin/addons");if(e.ok)for(let o of((await e.json()).customAddons||[]).filter(e=>e.isCustomAddon))try{var s,r,n;let e=await fetch("/api/admin/addons/".concat(o.name,"/flow")),l=null,i=!1;e.ok&&(l=await e.json(),i=!0),t.push({id:"built-".concat(o.name),name:o.name,description:o.description||"Generated addon from visual builder",author:"Addon Builder",version:o.version||"1.0.0",tags:i?["built-addon","recoverable"]:["built-addon","template-only"],isBuilt:!0,hasOriginalFlow:i,isPublic:!1,createdAt:(null==l||null==(s=l.metadata)?void 0:s.createdAt)||new Date().toISOString(),updatedAt:new Date().toISOString(),nodeCount:(null==l||null==(r=l.nodes)?void 0:r.length)||0,edgeCount:(null==l||null==(n=l.edges)?void 0:n.length)||0,nodes:(null==l?void 0:l.nodes)||[],edges:(null==l?void 0:l.edges)||[]})}catch(e){t.push({id:"built-".concat(o.name),name:o.name,description:o.description||"Generated addon from visual builder",author:"Addon Builder",version:o.version||"1.0.0",tags:["built-addon","template-only"],isBuilt:!0,hasOriginalFlow:!1,isPublic:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),nodeCount:0,edgeCount:0,nodes:[],edges:[]})}}catch(e){}es([...o,...t])},ev=e=>{let o=(e,o)=>e.map(e=>{let t=o.find(o=>o.id===e.source),s=o.find(o=>o.id===e.target);if(t&&s){let o=y[t.type]||"#6b7280",r=y[s.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:c.TG.ArrowClosed,color:r},style:{strokeWidth:2},data:{sourceColor:o,targetColor:r}}}return e});if(e.isBuilt)if(e.hasOriginalFlow&&e.nodes.length>0)v(e.nodes),D(o(e.edges,e.nodes)),ep({title:"Original Flow Recovered!",description:'"'.concat(e.name,'" original flow loaded successfully. You can now edit and rebuild it.'),status:"success",duration:5e3,isClosable:!0});else{let o=[{id:"1",type:"trigger",position:{x:100,y:200},data:{label:"Start Here"}},{id:"info-note",type:"command",position:{x:300,y:200},data:{label:"".concat(e.name," (Template)"),commandName:"example",description:"This is a basic template. The original flow data was not saved when this addon was built."}}],t=[{id:"start-to-command",source:"1",target:"info-note",type:"gradient",animated:!0,markerEnd:{type:c.TG.ArrowClosed,color:y.command},style:{strokeWidth:2},data:{sourceColor:y.trigger,targetColor:y.command}}];v(o),D(t),ep({title:"Template Loaded",description:'"'.concat(e.name,'" basic template loaded. Original flow data not available - this addon was built before flow recovery was implemented.'),status:"warning",duration:6e3,isClosable:!0})}else v(e.nodes),D(o(e.edges,e.nodes)),ep({title:"Flow Loaded!",description:'"'.concat(e.name,'" has been loaded successfully.'),status:"success",duration:3e3,isClosable:!0});U(),setTimeout(()=>{B&&B.fitView({padding:.2,includeHiddenNodes:!1,minZoom:.5,maxZoom:1.5})},100)},ek=e=>{ed(e)},eT=async()=>{if(ea)if(ed(null),ea.isBuilt)try{let e=await fetch("/api/admin/addons/".concat(ea.name),{method:"DELETE",credentials:"include"});if(!e.ok){let o=await e.json();throw Error(o.details||o.error||"Failed to delete addon")}es(e=>e.filter(e=>e.id!==ea.id)),ep({title:"Addon Deleted",description:"The built addon has been deleted successfully.",status:"success",duration:3e3,isClosable:!0})}catch(e){ep({title:"Delete Failed",description:e.message,status:"error",duration:5e3,isClosable:!0})}else{let e=localStorage.getItem("addon-builder-saved-flows");if(e)try{let o=JSON.parse(e).filter(e=>e.id!==ea.id);localStorage.setItem("addon-builder-saved-flows",JSON.stringify(o)),es(e=>e.filter(e=>e.id!==ea.id)),ep({title:"Flow Deleted",description:"The saved flow has been deleted successfully.",status:"info",duration:3e3,isClosable:!0})}catch(e){ep({title:"Delete Failed",description:"Failed to delete the saved flow.",status:"error",duration:3e3,isClosable:!0})}}},eD=()=>{el.trim()&&!er.tags.includes(el.trim())&&(en(e=>({...e,tags:[...e.tags,el.trim()]})),ei(""))},eA=e=>{en(o=>({...o,tags:o.tags.filter(o=>o!==e)}))},eB=async()=>{try{let e=await fetch("/api/admin/experimental/addon-builder/templates");if(e.ok){let o=await e.json();$(o.templates),eo(o.categories)}}catch(e){}},ez=e=>{v(e.nodes),D(((e,o)=>e.map(e=>{let t=o.find(o=>o.id===e.source),s=o.find(o=>o.id===e.target);if(t&&s){let o=y[t.type]||"#6b7280",r=y[s.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:c.TG.ArrowClosed,color:r},style:{strokeWidth:2},data:{sourceColor:o,targetColor:r}}}return e}))(e.edges,e.nodes)),R(),setTimeout(()=>{B&&B.fitView({padding:.2,includeHiddenNodes:!1,minZoom:.5,maxZoom:1.5})},100),ep({title:"Template Loaded!",description:'"'.concat(e.name,'" has been loaded successfully.'),status:"success",duration:3e3,isClosable:!0})},eF=e=>{let o=btoa(JSON.stringify(e)),t=[];t.push((e.name||"addon").toLowerCase().replace(/[^a-z0-9]/g,""));for(let e=0;e<o.length;e+=8)t.push(o.slice(e,e+8));return t.join("_")},eO=async e=>{try{await navigator.clipboard.writeText(e),ep({title:"Copied!",description:"Flow data copied to clipboard.",status:"success",duration:2e3,isClosable:!0})}catch(e){ep({title:"Copy Failed",description:"Failed to copy to clipboard. Please manually copy the text.",status:"error",duration:3e3,isClosable:!0})}},eE=e=>{try{let o=e.split("_");o[0];let t=o.slice(1).join(""),s=atob(t);return JSON.parse(s)}catch(e){throw Error("Invalid flow code format")}},eI=async()=>{if(!eh.trim())return void ep({title:"Import Error",description:"Please paste the flow code to import.",status:"error",duration:3e3,isClosable:!0});try{let o;if(!(o=eh.trim().startsWith("{")?JSON.parse(eh):eE(eh.trim())).nodes||!o.edges||!Array.isArray(o.nodes)||!Array.isArray(o.edges))throw Error("Invalid flow data structure. Missing nodes or edges.");if(0===o.nodes.length)throw Error("Flow appears to be empty. No nodes found.");let t=null;if(o.name&&o.isBuilt)try{let s=await fetch("/api/admin/addons");if(s.ok){var e;t=(null==(e=(await s.json()).customAddons)?void 0:e.find(e=>e.name===o.name))?"exists":"deleted"}}catch(e){}v(o.nodes),D(((e,o)=>e.map(e=>{let t=o.find(o=>o.id===e.source),s=o.find(o=>o.id===e.target);if(t&&s){let o=y[t.type]||"#6b7280",r=y[s.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:c.TG.ArrowClosed,color:r},style:{strokeWidth:2},data:{sourceColor:o,targetColor:r}}}return e}))(o.edges,o.nodes)),en({name:o.name||"Imported Flow",description:o.description||"Imported from shared flow",author:o.author||"Unknown",version:o.version||"1.0.0",tags:o.tags||[],isPublic:!1});let s='Successfully imported "'.concat(o.name,'" with ').concat(o.nodeCount||o.nodes.length," nodes."),r="success";"deleted"===t?(s='Flow imported from "'.concat(o.name,'" but the original addon has been deleted. You have the flow structure and can rebuild it.'),r="warning"):"exists"===t?s='Flow imported from "'.concat(o.name,'". The original addon still exists, so you can compare or make modifications.'):o.isBuilt&&(s='Flow imported from "'.concat(o.name,'" (built addon). Status of original addon unknown.'),r="warning"),ep({title:"Flow Imported!",description:s,status:r,duration:5e3,isClosable:!0}),ex(""),X(),setTimeout(()=>{B&&B.fitView({padding:.2,includeHiddenNodes:!1,minZoom:.5,maxZoom:1.5})},100)}catch(e){ep({title:"Import Failed",description:"Failed to import flow: ".concat(e.message),status:"error",duration:5e3,isClosable:!0})}},eM=(0,r.useCallback)(()=>{let e=C.filter(e=>e.selected);e.some(e=>"trigger"===e.type)&&1===e.length||(v(e=>e.filter(e=>!e.selected||"trigger"===e.type)),D(e=>e.filter(e=>!e.selected)))},[v,D,C]),eH=(0,r.useCallback)(e=>{let o=e.target;"INPUT"===o.tagName||"TEXTAREA"===o.tagName||o.isContentEditable||("Delete"===e.key&&(e.preventDefault(),eM()),"Backspace"===e.key&&(e.preventDefault(),e.stopPropagation()))},[eM]),eN=(0,r.useCallback)(()=>{},[]),eV=(0,r.useCallback)(()=>{},[]),eR=(0,r.useCallback)(()=>{D(e=>e.map(e=>{if("gradient"!==e.type){let o=C.find(o=>o.id===e.source),t=C.find(o=>o.id===e.target);if(o&&t){let s=y[o.type]||"#6b7280",r=y[t.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:c.TG.ArrowClosed,color:r},style:{strokeWidth:2},data:{sourceColor:s,targetColor:r}}}}return e}))},[C,D]);return r.useEffect(()=>{D(e=>e.map(e=>{let o=C.find(o=>o.id===e.source),t=C.find(o=>o.id===e.target);if(!o||!t)return e;let s=y[o.type]||"#6b7280",r=y[t.type]||"#6b7280";return{...e,data:{...e.data,sourceColor:s,targetColor:r}}}))},[S,C,y,D]),r.useEffect(()=>{eB(),eC()},[]),r.useEffect(()=>{eR()},[eR]),r.useEffect(()=>(document.addEventListener("keydown",eH),()=>{document.removeEventListener("keydown",eH)}),[eH]),(0,s.jsxs)(n.Box,{h:"100vh",w:"100%",bg:S.colors.background,children:[(0,s.jsxs)(n.VStack,{spacing:0,h:"full",children:[(0,s.jsx)(n.Box,{w:"full",bg:S.colors.surface,borderBottom:"1px solid",borderColor:S.colors.border,px:6,py:4,children:(0,s.jsxs)(n.HStack,{justify:"space-between",align:"center",children:[(0,s.jsxs)(n.VStack,{align:"start",spacing:1,children:[(0,s.jsxs)(n.HStack,{align:"center",spacing:3,children:[(0,s.jsx)(n.IconButton,{icon:(0,s.jsx)(l.kRp,{}),"aria-label":"Go Back",size:"md",colorScheme:"blue",variant:"solid",onClick:()=>t.back(),bg:"blue.500",color:"white",_hover:{bg:"blue.600"},_active:{bg:"blue.700"},border:"1px solid",borderColor:"blue.400"}),(0,s.jsx)(n.Heading,{size:"lg",color:S.colors.text,children:"\uD83C\uDFA8 Visual Addon Builder"})]}),(0,s.jsx)(n.Text,{fontSize:"sm",color:S.colors.textSecondary,children:"Build addons with drag-and-drop building blocks"})]}),(0,s.jsxs)(n.HStack,{spacing:2,children:[(0,s.jsx)(n.Tooltip,{label:"Help & Documentation",children:(0,s.jsx)(n.IconButton,{icon:(0,s.jsx)(l.lrG,{}),"aria-label":"Help",size:"sm",variant:"ghost",colorScheme:"yellow",onClick:M})}),(0,s.jsx)(n.Button,{leftIcon:(0,s.jsx)(l.B88,{}),size:"sm",variant:"ghost",colorScheme:"blue",onClick:()=>{eC(),G()},children:"Load"}),(0,s.jsx)(n.Button,{leftIcon:(0,s.jsx)(l.Pum,{}),size:"sm",variant:"ghost",colorScheme:"purple",onClick:()=>{var e,t;if(!B)return void ep({title:"Error",description:"Flow instance not available.",status:"error",duration:3e3,isClosable:!0});let s=B.toObject();eu(eF({name:er.name||"Untitled Flow",description:er.description||"Exported flow from Visual Addon Builder",author:er.author||(null==o||null==(e=o.user)?void 0:e.email)||"Unknown",version:er.version||"1.0.0",tags:er.tags||[],exportedAt:new Date().toISOString(),exportedBy:(null==o||null==(t=o.user)?void 0:t.email)||"Unknown",nodeCount:s.nodes.length,edgeCount:s.edges.length,builderVersion:"1.0.0",nodes:s.nodes,edges:s.edges})),Y()},children:"Export Code"}),(0,s.jsx)(n.Button,{leftIcon:(0,s.jsx)(l.a4x,{}),size:"sm",variant:"ghost",colorScheme:"green",onClick:K,children:"Import Code"}),(0,s.jsx)(n.Button,{leftIcon:(0,s.jsx)(l.aze,{}),size:"sm",colorScheme:"green",isLoading:F,loadingText:"Building...",onClick:()=>{var e;O(!0),en({name:"Custom Addon",description:"Generated addon from visual builder",author:(null==o||null==(e=o.user)?void 0:e.name)||"Addon Builder",version:"1.0.0",tags:[],isPublic:!1}),L()},children:"Build Addon"})]})]})}),(0,s.jsx)(n.Box,{flex:"1",w:"full",position:"relative",children:(0,s.jsx)(c.Ln,{children:(0,s.jsx)("div",{ref:E,style:{width:"100%",height:"100%"},children:(0,s.jsxs)(c.Ay,{nodes:C,edges:T,onNodesChange:k,onEdgesChange:A,onNodesDelete:eN,onEdgesDelete:eV,onConnect:ej,onInit:e=>{z(e)},onDrop:eS,onDragOver:ew,nodeTypes:ef,edgeTypes:b,fitView:!0,proOptions:{hideAttribution:!0},deleteKeyCode:null,multiSelectionKeyCode:null,style:{backgroundColor:S.colors.background},children:[(0,s.jsx)(c.VS,{color:S.colors.border,gap:16}),(0,s.jsx)(c.Zk,{position:"top-left",children:(0,s.jsxs)(n.VStack,{bg:S.colors.surface,border:"1px solid",borderColor:S.colors.border,borderRadius:"lg",p:4,spacing:3,align:"stretch",minW:"220px",maxH:"80vh",overflowY:"auto",children:[(0,s.jsxs)(n.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",color:S.colors.text,children:"\uD83C\uDFA8 Building Blocks"}),(0,s.jsxs)(n.HStack,{spacing:1,children:[(0,s.jsx)(n.Tooltip,{label:"Load pre-built templates",children:(0,s.jsx)(n.IconButton,{icon:(0,s.jsx)(l.est,{}),size:"xs",variant:"ghost",colorScheme:"teal","aria-label":"Templates",onClick:V})}),(0,s.jsx)(n.Tooltip,{label:"Hover blocks for detailed info",children:(0,s.jsx)(n.IconButton,{icon:(0,s.jsx)(l.lrG,{}),size:"xs",variant:"ghost","aria-label":"Help"})})]})]}),[{type:"command",label:"Command",icon:"⚡",color:"blue",description:"Slash commands users can execute\n• /ping, /ban, /kick\n• Click to configure options",rules:"Connect directly from Start (Command path)"},{type:"event",label:"Event",icon:"\uD83D\uDCE1",color:"green",description:"Discord events that trigger actions\n• Member joins/leaves\n• Message reactions\n• Voice changes",rules:"Connect directly from Start (Event path)"},{type:"action",label:"Action",icon:"\uD83C\uDFAF",color:"purple",description:"What happens when triggered\n• Send messages/embeds\n• Add/remove roles\n• Kick/ban users",rules:"Must connect to Command/Event/Condition"},{type:"apiRequest",label:"API Request",icon:"\uD83C\uDF10",color:"teal",description:"Make HTTP requests to external APIs\n• GET, POST, PUT, DELETE\n• Custom headers and body\n• Save response to variable",rules:"Can be used anywhere in your flow"},{type:"condition",label:"Condition",icon:"❓",color:"orange",description:"Check if something is true\n• User has role/permission\n• Message contains text\n• Two outputs: TRUE/FALSE",rules:"Connect TRUE/FALSE paths to different actions"}].map(e=>(0,s.jsx)(n.Tooltip,{label:(0,s.jsxs)(n.Box,{p:2,children:[(0,s.jsx)(n.Text,{fontWeight:"bold",mb:1,children:e.label}),(0,s.jsx)(n.Text,{fontSize:"xs",mb:2,whiteSpace:"pre-line",children:e.description}),(0,s.jsxs)(n.Text,{fontSize:"xs",color:"yellow.300",fontWeight:"bold",children:["\uD83D\uDCA1 ",e.rules]})]}),placement:"right",hasArrow:!0,bg:S.colors.surface,color:S.colors.text,borderRadius:"md",p:0,children:(0,s.jsx)(n.Box,{draggable:!0,onDragStart:o=>{o.dataTransfer.setData("application/reactflow",e.type),o.dataTransfer.effectAllowed="move"},bg:S.colors.background,border:"1px solid",borderColor:S.colors.border,borderRadius:"md",p:3,cursor:"grab",_hover:{bg:S.colors.surface,transform:"scale(1.02)",borderColor:S.colors.primary},_active:{cursor:"grabbing",transform:"scale(0.98)"},transition:"all 0.2s",children:(0,s.jsxs)(n.VStack,{spacing:2,children:[(0,s.jsxs)(n.HStack,{spacing:2,width:"full",children:[(0,s.jsx)(n.Text,{fontSize:"lg",children:e.icon}),(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"medium",color:S.colors.text,children:e.label})]}),(0,s.jsx)(n.Badge,{size:"sm",colorScheme:e.color,width:"full",children:"Drag to canvas"})]})})},e.type))]})})]})})})})]}),(0,s.jsxs)(n.Modal,{isOpen:I,onClose:H,size:"xl",children:[(0,s.jsx)(n.ModalOverlay,{}),(0,s.jsxs)(n.ModalContent,{bg:S.colors.background,children:[(0,s.jsx)(n.ModalHeader,{color:S.colors.text,children:"\uD83C\uDFA8 How to Use the Visual Addon Builder"}),(0,s.jsx)(n.ModalCloseButton,{}),(0,s.jsx)(n.ModalBody,{pb:6,children:(0,s.jsxs)(n.VStack,{spacing:4,align:"start",children:[(0,s.jsxs)(n.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsxs)(n.VStack,{align:"start",spacing:1,children:[(0,s.jsx)(n.Text,{fontWeight:"bold",children:"Welcome to the Visual Addon Builder!"}),(0,s.jsx)(n.Text,{fontSize:"sm",children:"Create Discord bot addons using intuitive drag-and-drop building blocks."})]})]}),(0,s.jsxs)(n.Box,{children:[(0,s.jsx)(n.Text,{fontWeight:"bold",mb:2,color:S.colors.text,children:"\uD83D\uDD27 Building Blocks:"}),(0,s.jsxs)(n.VStack,{align:"start",spacing:1,pl:4,children:[(0,s.jsxs)(n.Text,{fontSize:"sm",children:["⚡ ",(0,s.jsx)("strong",{children:"Command"})," - Slash commands users can execute"]}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["\uD83D\uDCE1 ",(0,s.jsx)("strong",{children:"Event"})," - Discord events like messages or reactions"]}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["\uD83C\uDFAF ",(0,s.jsx)("strong",{children:"Action"})," - What happens when triggered"]}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["❓ ",(0,s.jsx)("strong",{children:"Condition"})," - Check if something is true"]})]})]}),(0,s.jsxs)(n.Box,{children:[(0,s.jsx)(n.Text,{fontWeight:"bold",mb:2,color:S.colors.text,children:"\uD83C\uDFAE How to Create Flows:"}),(0,s.jsxs)(n.VStack,{align:"start",spacing:2,pl:4,children:[(0,s.jsxs)(n.Text,{fontSize:"sm",children:["1. ",(0,s.jsx)("strong",{children:"Start:"}),' Every flow begins with the "Start Here" trigger']}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["2. ",(0,s.jsx)("strong",{children:"Add Logic:"})," Drag Command or Event blocks to define when things happen"]}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["3. ",(0,s.jsx)("strong",{children:"Connect:"})," Drag from the bottom of one block to the top of another"]}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["4. ",(0,s.jsx)("strong",{children:"Configure:"})," Click the ⚙️ icon to set up each block"]}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["5. ",(0,s.jsx)("strong",{children:"Add Actions:"})," End with Action blocks to define what happens"]}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["6. ",(0,s.jsx)("strong",{children:"Save & Build:"}),' Save your work and click "Build Addon"']})]})]}),(0,s.jsxs)(n.Box,{children:[(0,s.jsx)(n.Text,{fontWeight:"bold",mb:2,color:S.colors.text,children:"\uD83D\uDD17 Connection Rules:"}),(0,s.jsxs)(n.VStack,{align:"start",spacing:1,pl:4,children:[(0,s.jsxs)(n.Text,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Choose ONE path:"})," Either Command OR Event from Start"]}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Commands:"})," For slash commands (/ping, /ban)"]}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Events:"})," For automatic triggers (joins, reactions)"]}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Conditions:"})," Have TWO outputs (True/False)"]}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Actions:"})," Should be at the end of each path"]})]})]}),(0,s.jsxs)(n.Box,{children:[(0,s.jsx)(n.Text,{fontWeight:"bold",mb:2,color:S.colors.text,children:"\uD83D\uDDD1️ Deleting Blocks:"}),(0,s.jsxs)(n.VStack,{align:"start",spacing:1,pl:4,children:[(0,s.jsx)(n.Text,{fontSize:"sm",children:"1. Click a block to select it (it will highlight)"}),(0,s.jsxs)(n.Text,{fontSize:"sm",children:["2. Press ",(0,s.jsx)("strong",{children:"Delete"})," key (not Backspace)"]}),(0,s.jsx)(n.Text,{fontSize:"sm",children:"3. The block and its connections will be removed"}),(0,s.jsx)(n.Text,{fontSize:"sm",color:"green.400",children:"✅ Backspace works in text fields for typing"}),(0,s.jsx)(n.Text,{fontSize:"sm",color:"yellow.400",children:"\uD83D\uDCA1 You cannot delete the Start block"})]})]}),(0,s.jsxs)(n.Box,{children:[(0,s.jsx)(n.Text,{fontWeight:"bold",mb:2,color:S.colors.text,children:"\uD83D\uDCA1 Pro Tips:"}),(0,s.jsxs)(n.VStack,{align:"start",spacing:1,pl:4,children:[(0,s.jsx)(n.Text,{fontSize:"sm",children:"• Use Templates for quick starts"}),(0,s.jsx)(n.Text,{fontSize:"sm",children:"• Hover over blocks in the palette for help"}),(0,s.jsx)(n.Text,{fontSize:"sm",children:"• Conditions let you create smart logic"}),(0,s.jsx)(n.Text,{fontSize:"sm",children:"• Always test with simple flows first"})]})]})]})})]})]}),(0,s.jsxs)(n.Modal,{isOpen:N,onClose:R,size:"4xl",children:[(0,s.jsx)(n.ModalOverlay,{}),(0,s.jsxs)(n.ModalContent,{bg:S.colors.background,children:[(0,s.jsx)(n.ModalHeader,{color:S.colors.text,children:"\uD83C\uDFA8 Choose a Template"}),(0,s.jsx)(n.ModalCloseButton,{}),(0,s.jsx)(n.ModalBody,{pb:6,children:(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(n.Text,{fontSize:"sm",color:S.colors.textSecondary,children:"Start with a pre-built template to create your addon faster"}),(0,s.jsx)(n.Box,{maxH:"500px",overflowY:"auto",pr:2,children:(0,s.jsx)(n.VStack,{spacing:6,align:"stretch",children:ee.map(e=>(0,s.jsxs)(n.Box,{children:[(0,s.jsx)(n.Text,{fontSize:"lg",fontWeight:"bold",color:S.colors.text,mb:4,children:e}),(0,s.jsx)(n.Box,{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:4,children:Q.filter(o=>o.category===e).map(e=>(0,s.jsx)(n.Box,{bg:S.colors.background,border:"1px solid",borderColor:S.colors.border,borderRadius:"lg",p:4,cursor:"pointer",position:"relative",_hover:{bg:S.colors.surface,borderColor:S.colors.primary,transform:"translateY(-2px)",boxShadow:"lg"},transition:"all 0.2s",onClick:()=>ez(e),children:(0,s.jsxs)(n.VStack,{spacing:3,align:"stretch",children:[(0,s.jsx)(n.HStack,{justify:"space-between",align:"start",children:(0,s.jsxs)(n.HStack,{spacing:3,children:[(0,s.jsx)(n.Box,{bg:S.colors.primary,color:"white",borderRadius:"lg",p:2,fontSize:"xl",children:e.thumbnail}),(0,s.jsxs)(n.VStack,{align:"start",spacing:1,children:[(0,s.jsx)(n.Text,{fontSize:"md",fontWeight:"bold",color:S.colors.text,children:e.name}),(0,s.jsxs)(n.Badge,{colorScheme:"blue",size:"sm",children:[e.nodes.length," blocks"]})]})]})}),(0,s.jsx)(n.Text,{fontSize:"sm",color:S.colors.textSecondary,lineHeight:"1.4",children:e.description}),(0,s.jsx)(n.Button,{size:"sm",colorScheme:"blue",variant:"ghost",width:"full",onClick:o=>{o.stopPropagation(),ez(e)},children:"Use Template"})]})},e.id))})]},e))})})]})})]})]}),(0,s.jsxs)(n.Modal,{isOpen:W,onClose:P,size:"lg",children:[(0,s.jsx)(n.ModalOverlay,{}),(0,s.jsxs)(n.ModalContent,{bg:S.colors.background,children:[(0,s.jsx)(n.ModalHeader,{color:S.colors.text,children:"\uD83D\uDCBE Save Addon Flow"}),(0,s.jsx)(n.ModalCloseButton,{}),(0,s.jsx)(n.ModalBody,{pb:6,children:(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(n.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsx)(n.AlertDescription,{fontSize:"sm",children:"Save your addon flow with metadata for easy organization and sharing."})]}),(0,s.jsxs)(n.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(n.FormControl,{isRequired:!0,children:[(0,s.jsx)(n.FormLabel,{color:S.colors.text,children:"Name"}),(0,s.jsx)(n.Input,{value:er.name,onChange:e=>en(o=>({...o,name:e.target.value})),placeholder:"My Awesome Addon",bg:S.colors.background,color:S.colors.text,borderColor:S.colors.border})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:S.colors.text,children:"Version"}),(0,s.jsx)(n.Input,{value:er.version,onChange:e=>en(o=>({...o,version:e.target.value})),placeholder:"1.0.0",bg:S.colors.background,color:S.colors.text,borderColor:S.colors.border})]})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:S.colors.text,children:"Author"}),(0,s.jsx)(n.Input,{value:er.author,onChange:e=>en(o=>({...o,author:e.target.value})),placeholder:"Your Name",bg:S.colors.background,color:S.colors.text,borderColor:S.colors.border})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:S.colors.text,children:"Description"}),(0,s.jsx)(n.Textarea,{value:er.description,onChange:e=>en(o=>({...o,description:e.target.value})),placeholder:"Describe what your addon does...",bg:S.colors.background,color:S.colors.text,borderColor:S.colors.border,rows:3})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:S.colors.text,children:"Tags"}),(0,s.jsxs)(n.HStack,{spacing:2,children:[(0,s.jsx)(n.Input,{value:el,onChange:e=>ei(e.target.value),placeholder:"Add tags (press Enter)",onKeyDown:e=>"Enter"===e.key&&eD(),bg:S.colors.background,color:S.colors.text,borderColor:S.colors.border}),(0,s.jsx)(n.Button,{size:"sm",onClick:eD,children:"Add"})]}),(0,s.jsx)(n.Flex,{wrap:"wrap",gap:2,mt:2,children:er.tags.map(e=>(0,s.jsxs)(n.Tag,{size:"sm",colorScheme:"blue",variant:"solid",children:[(0,s.jsx)(n.TagLabel,{children:e}),(0,s.jsx)(n.TagCloseButton,{onClick:()=>eA(e)})]},e))})]}),(0,s.jsxs)(n.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsx)(n.AlertDescription,{fontSize:"sm",children:"If an addon with the same name exists, it will be overwritten."})]})]})}),(0,s.jsxs)(n.ModalFooter,{children:[(0,s.jsx)(n.Button,{variant:"ghost",mr:3,onClick:P,children:"Cancel"}),(0,s.jsx)(n.Button,{colorScheme:"blue",onClick:()=>{if(!er.name.trim())return void ep({title:"Name Required",description:"Please enter a name for your addon.",status:"error",duration:3e3,isClosable:!0});if(F){ey(er),P();return}if(!B)return void ep({title:"Error",description:"Flow instance not available.",status:"error",duration:3e3,isClosable:!0});let e=B.toObject(),o={id:Date.now().toString(),name:er.name,description:er.description,author:er.author,version:er.version,tags:er.tags,isPublic:er.isPublic,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),nodeCount:e.nodes.length,edgeCount:e.edges.length,nodes:e.nodes,edges:e.edges},t=localStorage.getItem("addon-builder-saved-flows"),s=[];if(t)try{s=JSON.parse(t)}catch(e){}let r=s.findIndex(e=>e.name===o.name);-1!==r?s[r]={...s[r],...o,updatedAt:new Date().toISOString()}:s.push(o),localStorage.setItem("addon-builder-saved-flows",JSON.stringify(s)),ep({title:"Flow Saved!",description:'"'.concat(o.name,'" has been saved successfully.'),status:"success",duration:3e3,isClosable:!0}),P()},children:"Save Flow"})]})]})]}),(0,s.jsxs)(n.Modal,{isOpen:_,onClose:U,size:"4xl",children:[(0,s.jsx)(n.ModalOverlay,{}),(0,s.jsxs)(n.ModalContent,{bg:S.colors.background,children:[(0,s.jsx)(n.ModalHeader,{color:S.colors.text,children:"\uD83D\uDCC2 Load Addon Flow"}),(0,s.jsx)(n.ModalCloseButton,{}),(0,s.jsx)(n.ModalBody,{pb:6,children:(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(n.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(n.Text,{fontSize:"sm",color:S.colors.textSecondary,children:"Choose a saved addon flow to load"}),(0,s.jsxs)(n.Text,{fontSize:"sm",color:S.colors.textSecondary,children:[et.length," saved flows"]})]}),0===et.length?(0,s.jsxs)(n.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsx)(n.AlertDescription,{children:"No saved flows found. Create and save your first addon flow to see it here!"})]}):(0,s.jsx)(n.Box,{maxH:"500px",overflowY:"auto",pr:2,children:(0,s.jsx)(n.SimpleGrid,{columns:[1,2],spacing:4,children:et.map(e=>(0,s.jsxs)(n.Card,{bg:S.colors.background,borderColor:S.colors.border,children:[(0,s.jsx)(n.CardHeader,{pb:2,children:(0,s.jsxs)(n.HStack,{justify:"space-between",align:"start",children:[(0,s.jsxs)(n.VStack,{align:"start",spacing:1,children:[(0,s.jsx)(n.Text,{fontSize:"md",fontWeight:"bold",color:S.colors.text,children:e.name}),(0,s.jsxs)(n.HStack,{spacing:2,children:[(0,s.jsxs)(n.Badge,{size:"sm",colorScheme:"blue",children:["v",e.version]}),e.isBuilt?e.hasOriginalFlow?(0,s.jsx)(n.Badge,{size:"sm",colorScheme:"green",children:"✓ Recoverable"}):(0,s.jsx)(n.Badge,{size:"sm",colorScheme:"orange",children:"Template Only"}):(0,s.jsxs)(n.Badge,{size:"sm",colorScheme:"blue",children:[e.nodeCount," blocks"]})]})]}),(0,s.jsxs)(n.Menu,{children:[(0,s.jsx)(n.MenuButton,{as:n.IconButton,icon:(0,s.jsx)(l.ZZB,{}),variant:"ghost",size:"sm"}),(0,s.jsx)(n.MenuList,{bg:S.colors.surface,children:(0,s.jsx)(n.MenuItem,{icon:(0,s.jsx)(l.IXo,{}),onClick:()=>ek(e),color:"red.400",children:e.isBuilt?"Delete Addon":"Delete Flow"})})]})]})}),(0,s.jsx)(n.CardBody,{pt:0,children:(0,s.jsxs)(n.VStack,{align:"start",spacing:2,children:[(0,s.jsx)(n.Text,{fontSize:"sm",color:S.colors.textSecondary,noOfLines:2,children:e.isBuilt?e.hasOriginalFlow?"".concat(e.description," • Original flow data available! You can recover and edit the exact flow that was used to build this addon."):"".concat(e.description," • This addon was built before flow recovery was implemented. Only a basic template can be provided."):e.description||"No description available"}),(0,s.jsx)(n.HStack,{justify:"space-between",align:"center",width:"full",children:(0,s.jsxs)(n.HStack,{spacing:2,children:[(0,s.jsxs)(n.HStack,{spacing:1,children:[(0,s.jsx)(l.JXP,{size:12}),(0,s.jsx)(n.Text,{fontSize:"xs",color:S.colors.textSecondary,children:e.author})]}),(0,s.jsxs)(n.HStack,{spacing:1,children:[(0,s.jsx)(l.Ohp,{size:12}),(0,s.jsx)(n.Text,{fontSize:"xs",color:S.colors.textSecondary,children:new Date(e.updatedAt).toLocaleDateString()})]})]})}),e.tags&&e.tags.length>0&&(0,s.jsxs)(n.Flex,{wrap:"wrap",gap:1,children:[e.tags.slice(0,3).map(e=>(0,s.jsx)(n.Tag,{size:"sm",colorScheme:"gray",variant:"outline",children:e},e)),e.tags.length>3&&(0,s.jsxs)(n.Tag,{size:"sm",colorScheme:"gray",variant:"outline",children:["+",e.tags.length-3]})]})]})}),(0,s.jsx)(n.CardFooter,{pt:0,children:(0,s.jsx)(n.Tooltip,{label:e.isBuilt?e.hasOriginalFlow?"Load the original flow - fully recoverable!":"Load a basic template - original flow not saved":"Load the exact saved flow",placement:"top",children:(0,s.jsx)(n.Button,{size:"sm",colorScheme:e.isBuilt?e.hasOriginalFlow?"green":"orange":"blue",width:"full",onClick:()=>ev(e),children:e.isBuilt?e.hasOriginalFlow?"Recover Flow":"Load Template":"Load Flow"})})})]},e.id))})})]})})]})]}),(0,s.jsxs)(n.Modal,{isOpen:J,onClose:Z,size:"3xl",children:[(0,s.jsx)(n.ModalOverlay,{}),(0,s.jsxs)(n.ModalContent,{bg:S.colors.background,children:[(0,s.jsx)(n.ModalHeader,{color:S.colors.text,children:"\uD83D\uDE80 Export Addon Flow"}),(0,s.jsx)(n.ModalCloseButton,{}),(0,s.jsx)(n.ModalBody,{pb:6,children:(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(n.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsx)(n.AlertDescription,{children:"Share your addon flow with others! Copy the flow code below and share it anywhere. Others can import this code to reproduce your exact flow."})]}),(0,s.jsxs)(n.VStack,{align:"stretch",spacing:3,children:[(0,s.jsxs)(n.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",color:S.colors.text,children:"Flow Code"}),(0,s.jsx)(n.HStack,{spacing:2,children:(0,s.jsx)(n.Button,{size:"sm",leftIcon:(0,s.jsx)(l.nxz,{}),colorScheme:"blue",onClick:()=>eO(ec),children:"Copy Code"})})]}),(0,s.jsx)(n.Box,{p:4,bg:S.colors.surface,borderColor:S.colors.border,borderWidth:"2px",borderRadius:"md",borderStyle:"dashed",children:(0,s.jsx)(n.Text,{fontFamily:"monospace",fontSize:"lg",fontWeight:"bold",color:S.colors.primary,textAlign:"center",wordBreak:"break-all",userSelect:"all",children:ec})})]}),(0,s.jsxs)(n.Box,{p:3,bg:S.colors.surface,borderRadius:"md",borderColor:S.colors.border,borderWidth:"1px",children:[(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",color:S.colors.text,mb:2,children:"\uD83D\uDCCB How to Share:"}),(0,s.jsxs)(n.VStack,{align:"start",spacing:1,fontSize:"sm",color:S.colors.textSecondary,children:[(0,s.jsx)(n.Text,{children:"• Copy the flow code and paste it in Discord, email, or any text platform"}),(0,s.jsx)(n.Text,{children:"• Much easier to share than long JSON data!"}),(0,s.jsx)(n.Text,{children:'• The recipient can use the "Import Code" button to load your flow'}),(0,s.jsx)(n.Text,{children:"• All your block configurations and connections will be preserved"})]})]})]})})]})]}),(0,s.jsxs)(n.Modal,{isOpen:q,onClose:X,size:"2xl",children:[(0,s.jsx)(n.ModalOverlay,{}),(0,s.jsxs)(n.ModalContent,{bg:S.colors.background,children:[(0,s.jsx)(n.ModalHeader,{color:S.colors.text,children:"\uD83D\uDCE5 Import Addon Flow"}),(0,s.jsx)(n.ModalCloseButton,{}),(0,s.jsx)(n.ModalBody,{pb:6,children:(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(n.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsx)(n.AlertDescription,{children:"Import an addon flow from someone else! Paste the flow code below to load their flow configuration. This will replace your current flow."})]}),(0,s.jsxs)(n.VStack,{align:"stretch",spacing:3,children:[(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",color:S.colors.text,children:"Paste Flow Code"}),(0,s.jsx)(n.Input,{value:eh,onChange:e=>ex(e.target.value),fontFamily:"monospace",fontSize:"sm",bg:S.colors.surface,borderColor:S.colors.border,placeholder:"addon_xxxxxx_xxxxxx_xxxxxx...",size:"lg"})]}),(0,s.jsxs)(n.Box,{p:3,bg:S.colors.surface,borderRadius:"md",borderColor:S.colors.border,borderWidth:"1px",children:[(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",color:S.colors.text,mb:2,children:"\uD83D\uDD27 Import Instructions:"}),(0,s.jsxs)(n.VStack,{align:"start",spacing:1,fontSize:"sm",color:S.colors.textSecondary,children:[(0,s.jsx)(n.Text,{children:"• Paste the flow code (looks like: addon_xxxxxx_xxxxxx_xxxxxx)"}),(0,s.jsx)(n.Text,{children:"• Much simpler than JSON - just a single line of code!"}),(0,s.jsx)(n.Text,{children:'• Click "Import Flow" to load the configuration'}),(0,s.jsx)(n.Text,{children:"• Your current flow will be replaced with the imported one"}),(0,s.jsx)(n.Text,{children:"• Legacy JSON format is still supported for backward compatibility"})]})]})]})}),(0,s.jsxs)(n.ModalFooter,{children:[(0,s.jsx)(n.Button,{variant:"ghost",mr:3,onClick:X,children:"Cancel"}),(0,s.jsx)(n.Button,{colorScheme:"green",onClick:eI,isDisabled:!eh.trim(),children:"Import Flow"})]})]})]}),(0,s.jsx)(n.AlertDialog,{isOpen:!!ea,onClose:()=>ed(null),leastDestructiveRef:eg,children:(0,s.jsx)(n.AlertDialogOverlay,{children:(0,s.jsxs)(n.AlertDialogContent,{bg:S.colors.background,children:[(0,s.jsx)(n.AlertDialogHeader,{fontSize:"lg",fontWeight:"bold",color:S.colors.text,children:(null==ea?void 0:ea.isBuilt)?"Delete Addon":"Delete Flow"}),(0,s.jsx)(n.AlertDialogBody,{color:S.colors.textSecondary,children:(null==ea?void 0:ea.isBuilt)?(0,s.jsxs)(s.Fragment,{children:["Are you sure you want to delete the ",(0,s.jsxs)("strong",{children:['"',null==ea?void 0:ea.name,'"']})," addon? This will permanently remove it from both the source code and the bot. This action cannot be undone."]}):(0,s.jsxs)(s.Fragment,{children:["Are you sure you want to delete the ",(0,s.jsxs)("strong",{children:['"',null==ea?void 0:ea.name,'"']})," flow? This will permanently remove it from your saved flows. This action cannot be undone."]})}),(0,s.jsxs)(n.AlertDialogFooter,{children:[(0,s.jsx)(n.Button,{ref:eg,onClick:()=>ed(null),children:"Cancel"}),(0,s.jsx)(n.Button,{colorScheme:"red",onClick:eT,ml:3,children:(null==ea?void 0:ea.isBuilt)?"Delete Addon":"Delete Flow"})]})]})})})]})}}},e=>{var o=o=>e(e.s=o);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,3123,1126,6156,6099,2437,636,7398,1203,8792],()=>o(14691)),_N_E=e.O()}]);