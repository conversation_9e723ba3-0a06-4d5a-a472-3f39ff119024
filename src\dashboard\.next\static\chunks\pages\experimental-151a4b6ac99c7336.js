(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3106],{12625:(e,s,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/experimental",function(){return r(67905)}])},67905:(e,s,r)=>{"use strict";r.r(s),r.d(s,{__N_SSP:()=>N,default:()=>P});var n=r(94513),a=r(67116),i=r(39905),l=r(1648),t=r(19237),d=r(43700),o=r(79028),c=r(64349),x=r(5142),h=r(28365),u=r(7476),j=r(36468),p=r(1871),m=r(78813),b=r(29484),g=r(30301),f=r(84748),_=r(5130),w=r(3037),y=r(97119),z=r(58686),v=r(99500),E=r(77072),S=r.n(E),T=r(94285);let k=S()(()=>r.e(142).then(r.bind(r,90142)),{loadableGenerated:{webpack:()=>[90142]},loading:()=>(0,n.jsx)(f.y,{size:"lg"}),ssr:!1}),A=S()(()=>Promise.resolve().then(r.bind(r,38262)),{loadableGenerated:{webpack:()=>[38262]},ssr:!1}),C=[{id:"addon-builder",name:"Addon Builder",description:"Create custom Discord bot addons with a visual interface",icon:v.FSj,status:"beta",enabled:!0,path:"/experimental/addon-builder"}];function F(e){let{feature:s}=e,r=(0,z.useRouter)(),{hasAccess:a}=A();return(0,n.jsx)(x.Z,{cursor:a&&s.enabled?"pointer":"not-allowed",onClick:()=>{a&&s.enabled&&r.push(s.path)},opacity:a&&s.enabled?1:.6,_hover:a&&s.enabled?{transform:"translateY(-2px)",shadow:"xl"}:{},transition:"all 0.2s",border:"1px solid",borderColor:a&&s.enabled?"transparent":"gray.300",bg:a&&s.enabled?"white":"gray.50",children:(0,n.jsx)(h.b,{children:(0,n.jsxs)(w.T,{align:"start",spacing:3,children:[(0,n.jsxs)(p.z,{children:[(0,n.jsx)(b.I,{as:s.icon,boxSize:6,color:"purple.500"}),(0,n.jsx)(w.T,{align:"start",spacing:0,flex:1,children:(0,n.jsxs)(p.z,{children:[(0,n.jsx)(_.E,{fontWeight:"bold",fontSize:"lg",children:s.name}),(0,n.jsx)(d.E,{colorScheme:{alpha:"red",beta:"orange",stable:"green"}[s.status],size:"sm",children:s.status.toUpperCase()})]})}),!s.enabled&&(0,n.jsx)(b.I,{as:v.JhU,boxSize:4,color:"gray.400"})]}),(0,n.jsx)(_.E,{color:"gray.600",fontSize:"sm",children:s.description}),(0,n.jsx)(c.$,{size:"sm",colorScheme:"purple",variant:"outline",isDisabled:!a||!s.enabled,leftIcon:(0,n.jsx)(b.I,{as:v.uoG}),children:a?s.enabled?"Try Now":"Coming Soon":"Access Required"})]})})})}var N=!0;function P(){let{hasAccess:e,isLoading:s,reason:r}=A();return s?(0,n.jsx)(y.A,{children:(0,n.jsx)(u.m,{maxW:"container.xl",py:8,children:(0,n.jsxs)(w.T,{spacing:8,children:[(0,n.jsx)(f.y,{size:"xl"}),(0,n.jsx)(m.D,{size:"md",children:"Loading experimental features..."})]})})}):e?(0,n.jsx)(y.A,{children:(0,n.jsx)(u.m,{maxW:"container.xl",py:8,children:(0,n.jsxs)(w.T,{spacing:8,align:"stretch",children:[(0,n.jsxs)(o.a,{textAlign:"center",children:[(0,n.jsx)(m.D,{size:"lg",mb:4,children:"⚗️ Experimental Features"}),(0,n.jsx)(_.E,{color:"gray.600",fontSize:"lg",children:"Cutting-edge features in development"})]}),(0,n.jsxs)(a.F,{status:"info",borderRadius:"md",children:[(0,n.jsx)(l._,{}),(0,n.jsxs)(o.a,{children:[(0,n.jsx)(t.X,{children:"Beta Testing Program"}),(0,n.jsxs)(i.T,{children:[(0,n.jsx)("strong",{children:"Welcome to Experimental Features!"})," These features are in active development. Your feedback helps us improve them before general release."]})]})]}),(0,n.jsx)(g.r,{columns:{base:1,md:2,lg:3},spacing:6,children:C.map(e=>(0,n.jsx)(F,{feature:e},e.id))}),(0,n.jsxs)(o.a,{children:[(0,n.jsx)(j.c,{mb:4}),(0,n.jsx)(T.Suspense,{fallback:(0,n.jsx)(f.y,{}),children:(0,n.jsx)(k,{})})]}),(0,n.jsxs)(a.F,{status:"warning",borderRadius:"md",children:[(0,n.jsx)(l._,{}),(0,n.jsx)(i.T,{children:"Experimental features may change or be removed without notice. Use at your own discretion."})]})]})})}):(0,n.jsx)(y.A,{children:(0,n.jsx)(u.m,{maxW:"container.xl",py:8,children:(0,n.jsxs)(w.T,{spacing:8,children:[(0,n.jsx)(b.I,{as:v.XcJ,boxSize:16,color:"purple.500"}),(0,n.jsx)(m.D,{size:"lg",textAlign:"center",children:"⚗️ Experimental Features"}),(0,n.jsxs)(a.F,{status:"warning",borderRadius:"md",children:[(0,n.jsx)(l._,{}),(0,n.jsxs)(o.a,{children:[(0,n.jsx)(t.X,{children:"Access Required!"}),(0,n.jsxs)(i.T,{children:["You need experimental features access to use these features.","no_access"===r&&" Please apply for experimental features access from the overview page."]})]})]})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,636,7398,1203,8792],()=>s(12625)),_N_E=e.O()}]);