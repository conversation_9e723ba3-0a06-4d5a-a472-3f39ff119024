"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/experimental/addon-builder/templates";
exports.ids = ["pages/api/admin/experimental/addon-builder/templates"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fexperimental%2Faddon-builder%2Ftemplates&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cexperimental%5Caddon-builder%5Ctemplates.ts&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fexperimental%2Faddon-builder%2Ftemplates&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cexperimental%5Caddon-builder%5Ctemplates.ts&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_experimental_addon_builder_templates_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\experimental\\addon-builder\\templates.ts */ \"(api-node)/./pages/api/admin/experimental/addon-builder/templates.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_experimental_addon_builder_templates_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_experimental_addon_builder_templates_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/experimental/addon-builder/templates\",\n        pathname: \"/api/admin/experimental/addon-builder/templates\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_admin_experimental_addon_builder_templates_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fexperimental%2Faddon-builder%2Ftemplates&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cexperimental%5Caddon-builder%5Ctemplates.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/admin/experimental/addon-builder/templates.ts":
/*!*****************************************************************!*\
  !*** ./pages/api/admin/experimental/addon-builder/templates.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n\n\nconst DEVELOPER_ID = '933023999770918932';\nconst templates = [\n    {\n        id: 'ping-command',\n        name: 'Ping Command',\n        description: 'Simple ping command that responds with latency',\n        category: 'Basic',\n        thumbnail: '🏓',\n        nodes: [\n            {\n                id: 'trigger-1',\n                type: 'trigger',\n                position: {\n                    x: 300,\n                    y: 50\n                },\n                data: {\n                    label: 'Start'\n                }\n            },\n            {\n                id: 'command-1',\n                type: 'command',\n                position: {\n                    x: 300,\n                    y: 180\n                },\n                data: {\n                    label: 'Ping Command',\n                    commandName: 'ping',\n                    description: 'Shows bot latency and status'\n                }\n            },\n            {\n                id: 'action-1',\n                type: 'action',\n                position: {\n                    x: 300,\n                    y: 310\n                },\n                data: {\n                    label: 'Send Response',\n                    actionType: 'sendEmbed',\n                    message: 'Pong! 🏓\\nLatency: {ping}ms'\n                }\n            }\n        ],\n        edges: [\n            {\n                id: 'e1-2',\n                source: 'trigger-1',\n                target: 'command-1',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e2-3',\n                source: 'command-1',\n                target: 'action-1',\n                type: 'smoothstep',\n                animated: true\n            }\n        ]\n    },\n    {\n        id: 'welcome-system',\n        name: 'Welcome System',\n        description: 'Welcomes new members with a message',\n        category: 'Events',\n        thumbnail: '👋',\n        nodes: [\n            {\n                id: 'trigger-1',\n                type: 'trigger',\n                position: {\n                    x: 300,\n                    y: 50\n                },\n                data: {\n                    label: 'Start'\n                }\n            },\n            {\n                id: 'event-1',\n                type: 'event',\n                position: {\n                    x: 300,\n                    y: 180\n                },\n                data: {\n                    label: 'Member Joined',\n                    eventType: 'guildMemberAdd'\n                }\n            },\n            {\n                id: 'action-1',\n                type: 'action',\n                position: {\n                    x: 300,\n                    y: 310\n                },\n                data: {\n                    label: 'Welcome Message',\n                    actionType: 'sendMessage',\n                    message: 'Welcome to the server, {user}! 🎉',\n                    channel: 'welcome'\n                }\n            }\n        ],\n        edges: [\n            {\n                id: 'e1-2',\n                source: 'trigger-1',\n                target: 'event-1',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e2-3',\n                source: 'event-1',\n                target: 'action-1',\n                type: 'smoothstep',\n                animated: true\n            }\n        ]\n    },\n    {\n        id: 'moderation-kick',\n        name: 'Moderation Kick',\n        description: 'Kick command with permission check',\n        category: 'Moderation',\n        thumbnail: '🥾',\n        nodes: [\n            {\n                id: 'trigger-1',\n                type: 'trigger',\n                position: {\n                    x: 300,\n                    y: 50\n                },\n                data: {\n                    label: 'Start'\n                }\n            },\n            {\n                id: 'command-1',\n                type: 'command',\n                position: {\n                    x: 300,\n                    y: 180\n                },\n                data: {\n                    label: 'Kick Command',\n                    commandName: 'kick',\n                    description: 'Kick a member from the server'\n                }\n            },\n            {\n                id: 'condition-1',\n                type: 'condition',\n                position: {\n                    x: 300,\n                    y: 310\n                },\n                data: {\n                    label: 'Permission Check',\n                    conditionType: 'userPermission',\n                    operator: 'equals',\n                    value: 'KICK_MEMBERS'\n                }\n            },\n            {\n                id: 'action-1',\n                type: 'action',\n                position: {\n                    x: 150,\n                    y: 450\n                },\n                data: {\n                    label: 'Kick User',\n                    actionType: 'kickUser',\n                    message: 'User {user} has been kicked from the server.'\n                }\n            },\n            {\n                id: 'action-2',\n                type: 'action',\n                position: {\n                    x: 450,\n                    y: 450\n                },\n                data: {\n                    label: 'No Permission',\n                    actionType: 'sendMessage',\n                    message: 'You do not have permission to kick members.'\n                }\n            }\n        ],\n        edges: [\n            {\n                id: 'e1-2',\n                source: 'trigger-1',\n                target: 'command-1',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e2-3',\n                source: 'command-1',\n                target: 'condition-1',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e3-4',\n                source: 'condition-1',\n                target: 'action-1',\n                sourceHandle: 'true',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e3-5',\n                source: 'condition-1',\n                target: 'action-2',\n                sourceHandle: 'false',\n                type: 'smoothstep',\n                animated: true\n            }\n        ]\n    },\n    {\n        id: 'reaction-roles',\n        name: 'Reaction Roles',\n        description: 'Give roles based on message reactions',\n        category: 'Utility',\n        thumbnail: '🎭',\n        nodes: [\n            {\n                id: 'trigger-1',\n                type: 'trigger',\n                position: {\n                    x: 300,\n                    y: 50\n                },\n                data: {\n                    label: 'Start'\n                }\n            },\n            {\n                id: 'event-1',\n                type: 'event',\n                position: {\n                    x: 300,\n                    y: 180\n                },\n                data: {\n                    label: 'Reaction Added',\n                    eventType: 'messageReactionAdd'\n                }\n            },\n            {\n                id: 'condition-1',\n                type: 'condition',\n                position: {\n                    x: 300,\n                    y: 310\n                },\n                data: {\n                    label: 'Check Emoji',\n                    conditionType: 'reactionEmoji',\n                    operator: 'equals',\n                    value: '✅'\n                }\n            },\n            {\n                id: 'action-1',\n                type: 'action',\n                position: {\n                    x: 150,\n                    y: 450\n                },\n                data: {\n                    label: 'Add Role',\n                    actionType: 'addRole',\n                    role: 'Member',\n                    message: 'Added {role} role to {user}!'\n                }\n            },\n            {\n                id: 'action-2',\n                type: 'action',\n                position: {\n                    x: 450,\n                    y: 450\n                },\n                data: {\n                    label: 'Wrong Reaction',\n                    actionType: 'sendMessage',\n                    message: 'That emoji is not configured for reaction roles.'\n                }\n            }\n        ],\n        edges: [\n            {\n                id: 'e1-2',\n                source: 'trigger-1',\n                target: 'event-1',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e2-3',\n                source: 'event-1',\n                target: 'condition-1',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e3-4',\n                source: 'condition-1',\n                target: 'action-1',\n                sourceHandle: 'true',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e3-5',\n                source: 'condition-1',\n                target: 'action-2',\n                sourceHandle: 'false',\n                type: 'smoothstep',\n                animated: true\n            }\n        ]\n    },\n    {\n        id: 'auto-moderation',\n        name: 'Auto Moderation',\n        description: 'Automatically delete messages with bad words',\n        category: 'Moderation',\n        thumbnail: '🛡️',\n        nodes: [\n            {\n                id: 'trigger-1',\n                type: 'trigger',\n                position: {\n                    x: 300,\n                    y: 50\n                },\n                data: {\n                    label: 'Start'\n                }\n            },\n            {\n                id: 'event-1',\n                type: 'event',\n                position: {\n                    x: 300,\n                    y: 180\n                },\n                data: {\n                    label: 'Message Created',\n                    eventType: 'messageCreate'\n                }\n            },\n            {\n                id: 'condition-1',\n                type: 'condition',\n                position: {\n                    x: 300,\n                    y: 310\n                },\n                data: {\n                    label: 'Contains Bad Word',\n                    conditionType: 'messageContains',\n                    operator: 'contains',\n                    value: 'badword'\n                }\n            },\n            {\n                id: 'action-1',\n                type: 'action',\n                position: {\n                    x: 150,\n                    y: 450\n                },\n                data: {\n                    label: 'Delete Message',\n                    actionType: 'deleteMessage',\n                    message: 'Message deleted for containing inappropriate content.'\n                }\n            },\n            {\n                id: 'action-2',\n                type: 'action',\n                position: {\n                    x: 450,\n                    y: 450\n                },\n                data: {\n                    label: 'Allow Message',\n                    actionType: 'doNothing',\n                    message: 'Message is clean - no action needed'\n                }\n            }\n        ],\n        edges: [\n            {\n                id: 'e1-2',\n                source: 'trigger-1',\n                target: 'event-1',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e2-3',\n                source: 'event-1',\n                target: 'condition-1',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e3-4',\n                source: 'condition-1',\n                target: 'action-1',\n                sourceHandle: 'true',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e3-5',\n                source: 'condition-1',\n                target: 'action-2',\n                sourceHandle: 'false',\n                type: 'smoothstep',\n                animated: true\n            }\n        ]\n    },\n    {\n        id: 'custom-embed',\n        name: 'Custom Embed Command',\n        description: 'Create beautiful embed messages',\n        category: 'Utility',\n        thumbnail: '📋',\n        nodes: [\n            {\n                id: 'trigger-1',\n                type: 'trigger',\n                position: {\n                    x: 100,\n                    y: 100\n                },\n                data: {\n                    label: 'Start'\n                }\n            },\n            {\n                id: 'command-1',\n                type: 'command',\n                position: {\n                    x: 100,\n                    y: 200\n                },\n                data: {\n                    label: 'Embed Command',\n                    commandName: 'embed',\n                    description: 'Create a custom embed message'\n                }\n            },\n            {\n                id: 'action-1',\n                type: 'action',\n                position: {\n                    x: 100,\n                    y: 300\n                },\n                data: {\n                    label: 'Send Embed',\n                    actionType: 'sendEmbed',\n                    message: 'This is a custom embed!\\n\\nYou can customize:\\n• Title\\n• Description\\n• Color\\n• Fields\\n• And more!'\n                }\n            }\n        ],\n        edges: [\n            {\n                id: 'e1-2',\n                source: 'trigger-1',\n                target: 'command-1',\n                type: 'smoothstep',\n                animated: true\n            },\n            {\n                id: 'e2-3',\n                source: 'command-1',\n                target: 'action-1',\n                type: 'smoothstep',\n                animated: true\n            }\n        ]\n    }\n];\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            message: 'Method not allowed'\n        });\n    }\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session || session.user?.id !== DEVELOPER_ID) {\n            return res.status(403).json({\n                message: 'Unauthorized'\n            });\n        }\n        const { category } = req.query;\n        let filteredTemplates = templates;\n        if (category && typeof category === 'string') {\n            filteredTemplates = templates.filter((template)=>template.category.toLowerCase() === category.toLowerCase());\n        }\n        return res.status(200).json({\n            templates: filteredTemplates,\n            categories: Array.from(new Set(templates.map((t)=>t.category)))\n        });\n    } catch (error) {\n        console.error('Error fetching templates:', error);\n        return res.status(500).json({\n            message: 'Internal server error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/admin/experimental/addon-builder/templates.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fexperimental%2Faddon-builder%2Ftemplates&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cexperimental%5Caddon-builder%5Ctemplates.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();