"use strict";(()=>{var e={};e.id=8696,e.ids=[8696],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},71331:(e,t,o)=>{o.r(t),o.d(t,{config:()=>l,default:()=>p,routeModule:()=>g});var a={};o.r(a),o.d(a,{default:()=>c});var i=o(93433),n=o(20264),s=o(20584),d=o(15806),r=o(94506);let m=[{id:"ping-command",name:"Ping Command",description:"Simple ping command that responds with latency",category:"Basic",thumbnail:"\uD83C\uDFD3",nodes:[{id:"trigger-1",type:"trigger",position:{x:300,y:50},data:{label:"Start"}},{id:"command-1",type:"command",position:{x:300,y:180},data:{label:"Ping Command",commandName:"ping",description:"Shows bot latency and status"}},{id:"action-1",type:"action",position:{x:300,y:310},data:{label:"Send Response",actionType:"sendEmbed",message:"Pong! \uD83C\uDFD3\nLatency: {ping}ms"}}],edges:[{id:"e1-2",source:"trigger-1",target:"command-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"command-1",target:"action-1",type:"smoothstep",animated:!0}]},{id:"welcome-system",name:"Welcome System",description:"Welcomes new members with a message",category:"Events",thumbnail:"\uD83D\uDC4B",nodes:[{id:"trigger-1",type:"trigger",position:{x:300,y:50},data:{label:"Start"}},{id:"event-1",type:"event",position:{x:300,y:180},data:{label:"Member Joined",eventType:"guildMemberAdd"}},{id:"action-1",type:"action",position:{x:300,y:310},data:{label:"Welcome Message",actionType:"sendMessage",message:"Welcome to the server, {user}! \uD83C\uDF89",channel:"welcome"}}],edges:[{id:"e1-2",source:"trigger-1",target:"event-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"event-1",target:"action-1",type:"smoothstep",animated:!0}]},{id:"moderation-kick",name:"Moderation Kick",description:"Kick command with permission check",category:"Moderation",thumbnail:"\uD83E\uDD7E",nodes:[{id:"trigger-1",type:"trigger",position:{x:300,y:50},data:{label:"Start"}},{id:"command-1",type:"command",position:{x:300,y:180},data:{label:"Kick Command",commandName:"kick",description:"Kick a member from the server"}},{id:"condition-1",type:"condition",position:{x:300,y:310},data:{label:"Permission Check",conditionType:"userPermission",operator:"equals",value:"KICK_MEMBERS"}},{id:"action-1",type:"action",position:{x:150,y:450},data:{label:"Kick User",actionType:"kickUser",message:"User {user} has been kicked from the server."}},{id:"action-2",type:"action",position:{x:450,y:450},data:{label:"No Permission",actionType:"sendMessage",message:"You do not have permission to kick members."}}],edges:[{id:"e1-2",source:"trigger-1",target:"command-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"command-1",target:"condition-1",type:"smoothstep",animated:!0},{id:"e3-4",source:"condition-1",target:"action-1",sourceHandle:"true",type:"smoothstep",animated:!0},{id:"e3-5",source:"condition-1",target:"action-2",sourceHandle:"false",type:"smoothstep",animated:!0}]},{id:"reaction-roles",name:"Reaction Roles",description:"Give roles based on message reactions",category:"Utility",thumbnail:"\uD83C\uDFAD",nodes:[{id:"trigger-1",type:"trigger",position:{x:300,y:50},data:{label:"Start"}},{id:"event-1",type:"event",position:{x:300,y:180},data:{label:"Reaction Added",eventType:"messageReactionAdd"}},{id:"condition-1",type:"condition",position:{x:300,y:310},data:{label:"Check Emoji",conditionType:"reactionEmoji",operator:"equals",value:"✅"}},{id:"action-1",type:"action",position:{x:150,y:450},data:{label:"Add Role",actionType:"addRole",role:"Member",message:"Added {role} role to {user}!"}},{id:"action-2",type:"action",position:{x:450,y:450},data:{label:"Wrong Reaction",actionType:"sendMessage",message:"That emoji is not configured for reaction roles."}}],edges:[{id:"e1-2",source:"trigger-1",target:"event-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"event-1",target:"condition-1",type:"smoothstep",animated:!0},{id:"e3-4",source:"condition-1",target:"action-1",sourceHandle:"true",type:"smoothstep",animated:!0},{id:"e3-5",source:"condition-1",target:"action-2",sourceHandle:"false",type:"smoothstep",animated:!0}]},{id:"auto-moderation",name:"Auto Moderation",description:"Automatically delete messages with bad words",category:"Moderation",thumbnail:"\uD83D\uDEE1️",nodes:[{id:"trigger-1",type:"trigger",position:{x:300,y:50},data:{label:"Start"}},{id:"event-1",type:"event",position:{x:300,y:180},data:{label:"Message Created",eventType:"messageCreate"}},{id:"condition-1",type:"condition",position:{x:300,y:310},data:{label:"Contains Bad Word",conditionType:"messageContains",operator:"contains",value:"badword"}},{id:"action-1",type:"action",position:{x:150,y:450},data:{label:"Delete Message",actionType:"deleteMessage",message:"Message deleted for containing inappropriate content."}},{id:"action-2",type:"action",position:{x:450,y:450},data:{label:"Allow Message",actionType:"doNothing",message:"Message is clean - no action needed"}}],edges:[{id:"e1-2",source:"trigger-1",target:"event-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"event-1",target:"condition-1",type:"smoothstep",animated:!0},{id:"e3-4",source:"condition-1",target:"action-1",sourceHandle:"true",type:"smoothstep",animated:!0},{id:"e3-5",source:"condition-1",target:"action-2",sourceHandle:"false",type:"smoothstep",animated:!0}]},{id:"custom-embed",name:"Custom Embed Command",description:"Create beautiful embed messages",category:"Utility",thumbnail:"\uD83D\uDCCB",nodes:[{id:"trigger-1",type:"trigger",position:{x:100,y:100},data:{label:"Start"}},{id:"command-1",type:"command",position:{x:100,y:200},data:{label:"Embed Command",commandName:"embed",description:"Create a custom embed message"}},{id:"action-1",type:"action",position:{x:100,y:300},data:{label:"Send Embed",actionType:"sendEmbed",message:"This is a custom embed!\n\nYou can customize:\n• Title\n• Description\n• Color\n• Fields\n• And more!"}}],edges:[{id:"e1-2",source:"trigger-1",target:"command-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"command-1",target:"action-1",type:"smoothstep",animated:!0}]}];async function c(e,t){if("GET"!==e.method)return t.status(405).json({message:"Method not allowed"});try{let o=await (0,d.getServerSession)(e,t,r.authOptions);if(!o||o.user?.id!=="933023999770918932")return t.status(403).json({message:"Unauthorized"});let{category:a}=e.query,i=m;return a&&"string"==typeof a&&(i=m.filter(e=>e.category.toLowerCase()===a.toLowerCase())),t.status(200).json({templates:i,categories:Array.from(new Set(m.map(e=>e.category)))})}catch(e){return t.status(500).json({message:"Internal server error"})}}let p=(0,s.M)(a,"default"),l=(0,s.M)(a,"config"),g=new i.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/admin/experimental/addon-builder/templates",pathname:"/api/admin/experimental/addon-builder/templates",bundlePath:"",filename:""},userland:a})},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var o=e=>t(t.s=e),a=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>o(71331));module.exports=a})();