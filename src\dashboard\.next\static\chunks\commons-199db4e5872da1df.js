(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4223],{13279:(e,r,o)=>{"use strict";o.d(r,{$m:()=>b.$,$n:()=>i.$,EY:()=>y.E,In:()=>d.I,MJ:()=>l.MJ,Sc:()=>a.S,Tk:()=>v.T,aF:()=>h.aF,az:()=>t.a,cG:()=>n.c,cw:()=>x.c,dj:()=>w.d,jl:()=>g.j,lR:()=>s.l,mH:()=>f.m,pd:()=>p.p,rQ:()=>m.r,rS:()=>j.r,s_:()=>u.s,zt:()=>c.z});var t=o(79028),i=o(64349),a=o(42910),n=o(36468),l=o(31862),s=o(15975),c=o(1871),d=o(29484),p=o(59220),h=o(46949),x=o(81139),u=o(95066),b=o(53083),g=o(24490),m=o(31840),f=o(29607),j=o(30301),y=o(5130),v=o(3037),w=o(7836)},14470:(e,r,o)=>{"use strict";o.d(r,{A:()=>z});var t=o(94513),i=o(67116),a=o(1648),n=o(64349),l=o(31862),s=o(15975),c=o(1871),d=o(29484),p=o(59220),h=o(46949),x=o(81139),u=o(95066),b=o(53083),g=o(24490),m=o(31840),f=o(29607),j=o(35339),y=o(5130),v=o(52442),w=o(3037),S=o(7836),C=o(94285),k=o(99500),E=o(53424);function z(e){let{isOpen:r,onClose:o}=e,{data:z}=(0,E.useSession)(),A=(0,S.d)(),[F,_]=(0,C.useState)(!1),[I,W]=(0,C.useState)({motivation:"",experience:"",hoursPerWeek:"",feedback:"",contact:""}),D=(e,r)=>{W(o=>({...o,[e]:r}))},P=()=>I.motivation.length>10&&I.hoursPerWeek&&I.contact,N=async()=>{if(!P())return void A({title:"Form Incomplete",description:"Please fill in all required fields.",status:"error",duration:3e3});_(!0);try{var e,r;if(!(await fetch("/api/admin/applications",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"experimental",feature:"experimental-access",reason:JSON.stringify({motivation:I.motivation,experience:I.experience,hoursPerWeek:I.hoursPerWeek,feedback:I.feedback,contact:I.contact,username:(null==z||null==(e=z.user)?void 0:e.name)||"Unknown",userId:null==z||null==(r=z.user)?void 0:r.id,submittedAt:new Date().toISOString()})})})).ok)throw Error("Failed to submit application");A({title:"Application Submitted!",description:"Your application has been submitted and will be reviewed by OnedEyePete.",status:"success",duration:5e3}),o(),W({motivation:"",experience:"",hoursPerWeek:"",feedback:"",contact:""})}catch(e){A({title:"Submission Failed",description:"There was an error submitting your application. Please try again.",status:"error",duration:5e3})}finally{_(!1)}};return(0,t.jsxs)(h.aF,{isOpen:r,onClose:o,size:"xl",scrollBehavior:"inside",children:[(0,t.jsx)(f.m,{bg:"blackAlpha.700",backdropFilter:"blur(10px)"}),(0,t.jsxs)(b.$,{bg:"gray.800",border:"1px solid",borderColor:"whiteAlpha.200",children:[(0,t.jsx)(m.r,{borderBottom:"1px solid",borderColor:"whiteAlpha.200",children:(0,t.jsxs)(w.T,{align:"start",spacing:2,children:[(0,t.jsx)(y.E,{fontSize:"xl",fontWeight:"bold",children:"Experimental Features Application"}),(0,t.jsx)(y.E,{fontSize:"sm",color:"gray.400",children:"Apply to test cutting-edge features and help improve the bot"})]})}),(0,t.jsx)(u.s,{}),(0,t.jsx)(x.c,{p:6,children:(0,t.jsxs)(w.T,{spacing:6,align:"stretch",children:[(0,t.jsxs)(i.F,{status:"info",bg:"blue.900",border:"1px solid",borderColor:"blue.700",children:[(0,t.jsx)(a._,{}),(0,t.jsx)(y.E,{fontSize:"sm",children:"Your application will be submitted to OnedEyePete's dashboard for review. Response time can take up to one week. Only serious testers who can provide valuable feedback will be accepted."})]}),(0,t.jsxs)(l.MJ,{isRequired:!0,children:[(0,t.jsx)(s.l,{color:"white",children:"Why do you want to test experimental features? *"}),(0,t.jsx)(v.T,{placeholder:"Tell us about your motivation and what you hope to contribute...",value:I.motivation,onChange:e=>D("motivation",e.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300",rows:4,_focus:{borderColor:"yellow.400",boxShadow:"0 0 0 1px #F6E05E"}}),(0,t.jsxs)(y.E,{fontSize:"xs",color:"gray.400",mt:1,children:[I.motivation.length,"/500 characters (minimum 10)"]})]}),(0,t.jsxs)(l.MJ,{children:[(0,t.jsx)(s.l,{color:"white",children:"Previous testing or beta experience"}),(0,t.jsx)(v.T,{placeholder:"Describe any previous experience with beta testing, bug reporting, or feedback...",value:I.experience,onChange:e=>D("experience",e.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300",rows:3,_focus:{borderColor:"yellow.400",boxShadow:"0 0 0 1px #F6E05E"}})]}),(0,t.jsxs)(l.MJ,{isRequired:!0,children:[(0,t.jsx)(s.l,{color:"white",children:"How many hours per week can you dedicate to testing? *"}),(0,t.jsxs)(j.l,{placeholder:"Select hours per week",value:I.hoursPerWeek,onChange:e=>D("hoursPerWeek",e.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300",_focus:{borderColor:"yellow.400",boxShadow:"0 0 0 1px #F6E05E"},children:[(0,t.jsx)("option",{value:"1-2",children:"1-2 hours"}),(0,t.jsx)("option",{value:"3-5",children:"3-5 hours"}),(0,t.jsx)("option",{value:"6-10",children:"6-10 hours"}),(0,t.jsx)("option",{value:"10+",children:"10+ hours"})]})]}),(0,t.jsxs)(l.MJ,{children:[(0,t.jsx)(s.l,{color:"white",children:"What kind of feedback can you provide?"}),(0,t.jsx)(v.T,{placeholder:"Describe your ability to provide detailed bug reports, suggestions, or usability feedback...",value:I.feedback,onChange:e=>D("feedback",e.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300",rows:3,_focus:{borderColor:"yellow.400",boxShadow:"0 0 0 1px #F6E05E"}})]}),(0,t.jsxs)(l.MJ,{isRequired:!0,children:[(0,t.jsx)(s.l,{color:"white",children:"Best way to contact you for follow-up *"}),(0,t.jsx)(p.p,{placeholder:"Discord username, email, or other contact method",value:I.contact,onChange:e=>D("contact",e.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300",_focus:{borderColor:"yellow.400",boxShadow:"0 0 0 1px #F6E05E"}})]})]})}),(0,t.jsx)(g.j,{borderTop:"1px solid",borderColor:"whiteAlpha.200",children:(0,t.jsxs)(c.z,{spacing:4,width:"full",justify:"space-between",children:[(0,t.jsxs)(c.z,{spacing:4,children:[(0,t.jsx)(d.I,{as:k.y8Q,color:"yellow.300",boxSize:6}),(0,t.jsx)(y.E,{color:"gray.400",fontSize:"sm",children:"Submitted to OnedEyePete's dashboard • Response within 1 week"})]}),(0,t.jsxs)(c.z,{spacing:4,children:[(0,t.jsx)(n.$,{variant:"ghost",onClick:o,children:"Cancel"}),(0,t.jsx)(n.$,{colorScheme:"yellow",onClick:N,isLoading:F,loadingText:"Submitting...",leftIcon:(0,t.jsx)(d.I,{as:k.A7C}),isDisabled:!P(),children:"Submit Application"})]})]})})]})]})}},21489:(e,r,o)=>{"use strict";o.d(r,{XcJ:()=>t.XcJ,t69:()=>t.t69});var t=o(99500)},38262:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>a});var t=o(53424),i=o(94285);function a(){let{data:e}=(0,t.useSession)(),[r,o]=(0,i.useState)(!1),[a,n]=(0,i.useState)(""),[l,s]=(0,i.useState)(!0);return(0,i.useEffect)(()=>{!async function(){if(!(null==e?void 0:e.user)){o(!1),n("unauthenticated"),s(!1);return}try{let e=await fetch("/api/discord/user/experimental"),r=await e.json();o(r.hasAccess),n(r.reason)}catch(e){o(!1),n("error")}finally{s(!1)}}()},[e]),{hasAccess:r,reason:a,isLoading:l,isDeveloper:"developer"===a,isTester:"tester"===a}}},43557:(e,r,o)=>{"use strict";o.d(r,{cfS:()=>t.FiUsers,mEP:()=>t.FiMessageSquare,o77:()=>t.FiVolume2,pcC:()=>t.FiShield});var t=o(35044)},52826:(e,r,o)=>{"use strict";o.d(r,{A:()=>l});var t=o(8486),i=o(94285),a=o(53424);let n=async e=>{let r=await fetch(e);if(!r.ok){if(401===r.status)return{name:"404 Bot",botName:"404 Bot"};throw Error("Failed to fetch guild info")}return r.json()};function l(){let{data:e,status:r}=(0,a.useSession)(),o="authenticated"===r,{data:l,error:s}=(0,t.Ay)(o?"/api/discord/guild":null,n,{revalidateOnFocus:!1,revalidateOnReconnect:!1}),[c,d]=(0,i.useState)(()=>localStorage.getItem("dashboardDisplayNamePref")||"guild"),p=(0,i.useCallback)(e=>{d(e),localStorage.setItem("dashboardDisplayNamePref",e),window.dispatchEvent(new CustomEvent("displayNamePrefChanged",{detail:e}))},[]);(0,i.useEffect)(()=>{let e=e=>{(null==e?void 0:e.detail)&&d(e.detail)},r=e=>{"dashboardDisplayNamePref"===e.key&&d(e.newValue||"guild")};return window.addEventListener("displayNamePrefChanged",e),window.addEventListener("storage",r),()=>{window.removeEventListener("displayNamePrefChanged",e),window.removeEventListener("storage",r)}},[]);let h="404 Bot Dashboard",x=h;return l&&(x="bot"===c&&l.botName?l.botName:l.name||h),{guild:l,displayName:x,pref:c,updatePreference:p,isLoading:o&&!s&&!l,isError:!!s}}},75632:()=>{},82273:(e,r,o)=>{"use strict";o(29326)},84622:(e,r,o)=>{"use strict";o.d(r,{j:()=>t.j});var t=o(95845)},97119:(e,r,o)=>{"use strict";o.d(r,{A:()=>$});var t=o(94513),i=o(79028),a=o(7476),n=o(567),l=o(43700),s=o(64349),c=o(49892),d=o(1871),p=o(78813),h=o(29484),x=o(52156),u=o(75975),b=o(11593),g=o(78723),m=o(5130),f=o(53424),j=o(35044),y=o(99500),v=o(52826),w=o(38262),S=o(22184),C=o(23668),k=o(38047),E=o(63678),z=o(2996),A=o(72663),F=o(35440),_=o(3037),I=o(94285);function W(){let{data:e}=(0,f.useSession)(),[r]=(0,I.useState)([]);return(null==e?void 0:e.user)?(0,t.jsxs)(C.A,{placement:"bottom-end",children:[(0,t.jsx)(A.W,{children:(0,t.jsxs)(i.a,{position:"relative",children:[(0,t.jsx)(F.m,{label:"Notifications",placement:"bottom",children:(0,t.jsx)(S.K,{"aria-label":"Notifications",icon:(0,t.jsx)(j.FiBell,{}),variant:"ghost",size:"sm",color:"gray.300",_hover:{bg:"whiteAlpha.200",color:"white",transform:"scale(1.05)"},transition:"all 0.2s"})}),!1]})}),(0,t.jsxs)(E.h,{bg:"gray.800",borderColor:"whiteAlpha.200",boxShadow:"2xl",maxW:"400px",_focus:{boxShadow:"2xl"},children:[(0,t.jsx)(z.D,{borderBottomColor:"whiteAlpha.200",fontWeight:"semibold",fontSize:"lg",color:"white",children:"Notifications"}),(0,t.jsx)(k.e,{maxH:"400px",overflowY:"auto",children:(0,t.jsx)(_.T,{spacing:0,align:"stretch",children:0===r.length?(0,t.jsx)(i.a,{py:8,textAlign:"center",children:(0,t.jsx)(m.E,{color:"gray.400",fontSize:"sm",children:"No notifications yet"})}):r.map(e=>(0,t.jsxs)(i.a,{p:3,borderBottom:"1px",borderColor:"whiteAlpha.100",children:[(0,t.jsx)(m.E,{fontSize:"sm",color:"white",fontWeight:"medium",children:e.title}),(0,t.jsx)(m.E,{fontSize:"xs",color:"gray.400",mt:1,children:e.message})]},e.id))})})]})]}):null}function D(){var e,r;let{data:o}=(0,f.useSession)(),{displayName:a}=(0,v.A)(),{isLoading:S,isDeveloper:C,reason:k}=(0,w.default)();return(0,t.jsx)(i.a,{px:6,py:2,bg:"rgba(255,255,255,0.05)",backdropFilter:"blur(20px)",borderBottom:"1px solid",borderColor:"whiteAlpha.200",position:"sticky",top:0,zIndex:1e3,_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(159, 122, 234, 0.1))",zIndex:-1},children:(0,t.jsxs)(c.s,{h:16,alignItems:"center",justifyContent:"space-between",children:[(0,t.jsx)(i.a,{flex:"1",children:(0,t.jsx)(p.D,{as:"h1",fontSize:"xl",bgGradient:"linear(to-r, blue.400, purple.400)",bgClip:"text",_hover:{bgGradient:"linear(to-r, blue.300, purple.300)",transform:"scale(1.02)"},transition:"all 0.2s",children:a?"".concat(a," Dashboard"):"Bot Dashboard"})}),!S&&!C&&"open"===k&&(0,t.jsx)(i.a,{flex:"2",display:"flex",justifyContent:"center",children:(0,t.jsxs)(d.z,{spacing:2,bg:"rgba(236, 201, 75, 0.1)",border:"1px solid",borderColor:"yellow.400",borderRadius:"full",px:4,py:2,_hover:{bg:"rgba(236, 201, 75, 0.15)",transform:"scale(1.02)"},transition:"all 0.2s",children:[(0,t.jsx)(h.I,{as:y.XcJ,color:"yellow.300"}),(0,t.jsx)(m.E,{fontSize:"sm",fontWeight:"bold",bgGradient:"linear(to-r, yellow.200, orange.200)",bgClip:"text",children:"Experimental features are open to applicants!"}),(0,t.jsx)(l.E,{colorScheme:"yellow",variant:"solid",fontSize:"xs",children:"NEW"})]})}),(0,t.jsx)(i.a,{flex:"1",display:"flex",justifyContent:"flex-end",children:(0,t.jsx)(c.s,{alignItems:"center",gap:4,children:(null==o?void 0:o.user)?(0,t.jsxs)(d.z,{spacing:4,children:[(0,t.jsx)(W,{}),(0,t.jsxs)(x.W,{children:[(0,t.jsx)(u.I,{as:s.$,variant:"ghost",size:"sm",px:2,py:1,borderRadius:"full",_hover:{bg:"whiteAlpha.200"},children:(0,t.jsxs)(d.z,{spacing:2,children:[(0,t.jsx)(n.e,{size:"sm",name:null!=(e=o.user.name)?e:void 0,src:null!=(r=o.user.image)?r:void 0,borderWidth:2,borderColor:"blue.400",_hover:{borderColor:"purple.400",transform:"scale(1.05)"},transition:"all 0.2s"}),(0,t.jsx)(m.E,{color:"gray.300",display:{base:"none",md:"block"},children:o.user.name})]})}),(0,t.jsx)(g.c,{bg:"gray.800",borderColor:"whiteAlpha.200",boxShadow:"lg",_hover:{borderColor:"blue.400"},children:(0,t.jsx)(b.D,{icon:(0,t.jsx)(j.FiLogOut,{}),onClick:()=>(0,f.signOut)(),_hover:{bg:"whiteAlpha.200",color:"red.400"},children:"Sign out"})})]})]}):(0,t.jsx)(s.$,{onClick:()=>(0,f.signIn)("discord",{callbackUrl:"/overview"}),bgGradient:"linear(to-r, blue.500, purple.500)",color:"white",_hover:{bgGradient:"linear(to-r, blue.400, purple.400)",transform:"translateY(-1px)"},_active:{bgGradient:"linear(to-r, blue.600, purple.600)",transform:"translateY(1px)"},transition:"all 0.2s",children:"Login with Discord"})})})]})})}var P=o(23640),N=o(36468),T=o(23450),O=o(84622);o(75632),o(82273);var B=o(21489),J=o(24251),R=o.n(J),X=o(58686),G=o(14470),H=o(27263);let L="1.0.0";function M(){var e,r;let{data:o}=(0,f.useSession)(),a=(0,X.useRouter)(),n=null==o||null==(e=o.user)?void 0:e.isAdmin;null==o||null==(r=o.user)||r.id;let[l,c]=(0,I.useState)(!1),[d,p]=(0,I.useState)(!1),{displayName:x}=(0,v.A)(),{hasAccess:u,isLoading:b,isDeveloper:g,reason:y}=(0,w.default)(),{isOpen:S,onOpen:C,onClose:k}=(0,O.j)(),{currentScheme:E}=(0,H.DP)(),z=[{name:"Overview",icon:j.FiHome,href:"/overview"},{name:"Applications",icon:j.FiPackage,href:"/applications"},{name:"Tickets",icon:j.FiHelpCircle,href:"/tickets"},{name:"Game Servers",icon:j.FiMonitor,href:"/gameservers"}],A=[{name:"Server Management",href:"/admin/guilds",icon:j.FiSettings},{name:"Applications",href:"/admin/applications",icon:B.t69},{name:"Applications Builder",href:"/admin/applications-builder",icon:j.FiPackage},{name:"Addons",href:"/admin/addons",icon:j.FiBox},{name:"Commands",href:"/admin/commands",icon:j.FiCommand}],W=[{name:"Addon Builder",href:"/admin/experimental/addon-builder",icon:j.FiBox},{name:"Feature Flags",href:"/admin/experimental/feature-flags",icon:B.XcJ},{name:"Beta Testing",href:"/admin/experimental/beta-testing",icon:B.XcJ}],D=e=>"/overview"===e?a.pathname===e:a.pathname.startsWith(e);return(0,t.jsxs)(i.a,{as:"nav",h:"100%",bg:E.colors.surface,backdropFilter:"blur(20px)",borderRight:"1px solid",borderColor:E.colors.border,py:8,display:"flex",flexDirection:"column",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(180deg, ".concat(E.colors.primary,"15 0%, ").concat(E.colors.accent,"15 100%)"),zIndex:-1},children:[(0,t.jsxs)(_.T,{spacing:2,align:"stretch",flex:"1",children:[z.map(e=>{let r=D(e.href);return(0,t.jsx)(F.m,{label:e.name,placement:"right",hasArrow:!0,gutter:20,openDelay:500,display:{base:"block","2xl":"none"},children:(0,t.jsxs)(T.N,{as:R(),href:e.href,display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:r?E.colors.text:E.colors.textSecondary,bg:r?"".concat(E.colors.primary,"30"):"transparent",_hover:{bg:r?"".concat(E.colors.primary,"40"):E.colors.surface,color:E.colors.text,transform:"translateX(4px)"},_active:{bg:"".concat(E.colors.primary,"50")},borderRight:r?"2px solid":"none",borderColor:r?E.colors.primary:"transparent",transition:"all 0.2s",role:"group",children:[(0,t.jsx)(h.I,{as:e.icon,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,t.jsx)(m.E,{display:{base:"none",lg:"block"},bgGradient:r?"linear(to-r, ".concat(E.colors.primaryLight,", ").concat(E.colors.accent,")"):"none",bgClip:r?"text":"none",transition:"all 0.2s",children:e.name})]})},e.name)}),n&&(0,t.jsxs)(_.T,{spacing:0,align:"stretch",children:[(0,t.jsxs)(i.a,{display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:E.colors.textSecondary,bg:"transparent",_hover:{bg:E.colors.surface,color:E.colors.text,transform:"translateX(4px)"},transition:"all 0.2s",cursor:"pointer",role:"group",onClick:()=>c(!l),children:[(0,t.jsx)(h.I,{as:j.FiServer,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,t.jsx)(m.E,{display:{base:"none",lg:"block"},transition:"all 0.2s",flex:"1",children:"Admin"}),(0,t.jsx)(h.I,{as:j.FiChevronDown,w:4,h:4,ml:2,transition:"all 0.2s",transform:l?"rotate(180deg)":"rotate(0deg)",opacity:.6})]}),(0,t.jsx)(P.S,{in:l,animateOpacity:!0,children:(0,t.jsx)(_.T,{spacing:1,align:"stretch",pl:4,py:2,children:A.map(e=>(0,t.jsxs)(T.N,{as:R(),href:e.href,display:"flex",alignItems:"center",px:4,py:2,fontSize:"xs",fontWeight:"medium",color:D(e.href)?E.colors.text:E.colors.textSecondary,bg:D(e.href)?"".concat(E.colors.primary,"20"):"transparent",_hover:{bg:E.colors.surface,color:E.colors.text,transform:"translateX(2px)"},borderRadius:"md",transition:"all 0.2s",role:"group",children:[(0,t.jsx)(h.I,{as:e.icon,w:4,h:4,mr:2,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,t.jsx)(m.E,{display:{base:"none",lg:"block"},children:e.name})]},e.href))})})]}),g&&(0,t.jsxs)(_.T,{spacing:0,align:"stretch",children:[(0,t.jsxs)(i.a,{display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:E.colors.textSecondary,bg:"transparent",_hover:{bg:E.colors.surface,color:E.colors.text,transform:"translateX(4px)"},transition:"all 0.2s",cursor:"pointer",role:"group",onClick:()=>p(!d),children:[(0,t.jsx)(h.I,{as:B.XcJ,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,t.jsx)(m.E,{display:{base:"none",lg:"block"},transition:"all 0.2s",flex:"1",bgGradient:"linear(to-r, purple.400, pink.400)",bgClip:"text",children:"Manage Experimental"}),(0,t.jsx)(h.I,{as:j.FiChevronDown,w:4,h:4,ml:2,transition:"all 0.2s",transform:d?"rotate(180deg)":"rotate(0deg)",opacity:.6})]}),(0,t.jsx)(P.S,{in:d,animateOpacity:!0,children:(0,t.jsx)(_.T,{spacing:1,align:"stretch",pl:4,py:2,children:W.map(e=>(0,t.jsxs)(T.N,{as:R(),href:e.href,display:"flex",alignItems:"center",px:4,py:2,fontSize:"xs",fontWeight:"medium",color:D(e.href)?E.colors.text:E.colors.textSecondary,bg:D(e.href)?"".concat(E.colors.primary,"20"):"transparent",_hover:{bg:E.colors.surface,color:E.colors.text,transform:"translateX(2px)"},borderRadius:"md",transition:"all 0.2s",role:"group",children:[(0,t.jsx)(h.I,{as:e.icon,w:4,h:4,mr:2,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,t.jsx)(m.E,{display:{base:"none",lg:"block"},children:e.name})]},e.href))})})]})]}),!b&&!g&&(u||"open"===y)&&(0,t.jsxs)(i.a,{px:4,mt:"auto",children:[(0,t.jsx)(N.c,{borderColor:E.colors.border,mb:4}),u?(0,t.jsx)(s.$,{size:"sm",colorScheme:"yellow",variant:"ghost",leftIcon:(0,t.jsx)(h.I,{as:B.XcJ}),onClick:()=>a.push("/experimental"),w:"full",justifyContent:"flex-start",fontSize:"xs",_hover:{bg:E.colors.surface,transform:"translateX(2px)"},children:(0,t.jsx)(m.E,{display:{base:"none",lg:"block"},children:"Experimental Features"})}):"open"===y?(0,t.jsxs)(_.T,{spacing:2,align:"stretch",children:[(0,t.jsxs)(i.a,{bg:"rgba(236, 201, 75, 0.1)",border:"1px solid",borderColor:"yellow.400",borderRadius:"md",p:2,textAlign:"center",children:[(0,t.jsx)(m.E,{fontSize:"xs",color:"yellow.300",fontWeight:"bold",bgGradient:"linear(to-r, yellow.200, orange.200)",bgClip:"text",children:"\uD83E\uDDEA Experimental Features"}),(0,t.jsx)(m.E,{fontSize:"xs",color:"yellow.400",mt:1,children:"Apply Now • Response in ~1 week"})]}),(0,t.jsxs)(s.$,{size:"sm",colorScheme:"yellow",variant:"outline",onClick:C,w:"full",fontSize:"xs",_hover:{bg:"yellow.400",color:"black",transform:"translateY(-1px)"},children:[(0,t.jsx)(m.E,{display:{base:"none",lg:"block"},children:"Apply Now"}),(0,t.jsx)(m.E,{display:{base:"block",lg:"none"},children:"Apply"})]})]}):null]}),(0,t.jsxs)(i.a,{px:4,pt:4,...!b&&!g&&(u||"open"===y)?{}:{mt:"auto"},children:[(0,t.jsx)(N.c,{borderColor:E.colors.border,mb:4}),(0,t.jsx)(m.E,{fontSize:"xs",color:E.colors.textSecondary,textAlign:"center",bgGradient:"linear(to-r, ".concat(E.colors.primaryLight,", ").concat(E.colors.accent,")"),bgClip:"text",opacity:.7,_hover:{opacity:1,transform:"scale(1.05)"},transition:"all 0.2s",children:x?"".concat(x," v").concat(L):"Bot v".concat(L)})]}),(0,t.jsx)(G.A,{isOpen:S,onClose:k})]})}let $=e=>{let{children:r}=e,{currentScheme:o}=(0,H.DP)();return(0,t.jsx)(i.a,{minH:"100vh",bg:o.colors.background,position:"relative",overflow:"hidden",_before:{content:'""',position:"fixed",top:0,left:0,right:0,bottom:0,bgImage:"\n          radial-gradient(circle at 15% 50%, ".concat(o.colors.primary,"15 0%, transparent 25%),\n          radial-gradient(circle at 85% 30%, ").concat(o.colors.accent,"15 0%, transparent 25%)\n        "),zIndex:0},_after:{content:'""',position:"fixed",top:0,left:0,right:0,bottom:0,backdropFilter:"blur(100px)",zIndex:0},children:(0,t.jsxs)(i.a,{position:"relative",zIndex:1,display:"flex",flexDirection:"column",minH:"100vh",children:[(0,t.jsx)(i.a,{position:"fixed",top:0,left:0,right:0,zIndex:30,children:(0,t.jsx)(D,{})}),(0,t.jsxs)(i.a,{display:"flex",flex:"1",position:"relative",pt:"4rem",children:[(0,t.jsx)(i.a,{position:"fixed",top:"4rem",bottom:0,left:0,w:"64",zIndex:20,children:(0,t.jsx)(M,{})}),(0,t.jsx)(i.a,{flex:"1",ml:"64",p:{base:4,md:8},maxW:"100%",transition:"all 0.3s",position:"relative",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(135deg, rgba(255,255,255,0.03) 0%, transparent 100%)",pointerEvents:"none",zIndex:-1},children:(0,t.jsx)(a.m,{maxW:"container.xl",children:r})})]})]})})}}}]);