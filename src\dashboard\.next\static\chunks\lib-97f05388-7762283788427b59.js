"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8883],{28226:(e,t,r)=>{var n=r(30165);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.proxyLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,t=arguments.length>1?arguments[1]:void 0;try{if("undefined"==typeof window)return e;var r={},n=function(e){var n;r[e]=(n=(0,u.default)(a.default.mark(function r(n,u){var c,f;return a.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(l[e](n,u),"error"===e&&(u=i(u)),u.client=!0,c="".concat(t,"/_log"),f=new URLSearchParams(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){(0,o.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({level:e,code:n},u)),!navigator.sendBeacon){r.next=8;break}return r.abrupt("return",navigator.sendBeacon(c,f));case 8:return r.next=10,fetch(c,{method:"POST",body:f,keepalive:!0});case 10:return r.abrupt("return",r.sent);case 11:case"end":return r.stop()}},r)})),function(e,t){return n.apply(this,arguments)})};for(var c in e)n(c);return r}catch(e){return l}},t.setLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;t||(l.debug=function(){}),e.error&&(l.error=e.error),e.warn&&(l.warn=e.warn),e.debug&&(l.debug=e.debug)};var a=n(r(14487)),o=n(r(58630)),u=n(r(22)),c=r(45239);function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){var t,r;if(e instanceof Error&&!(e instanceof c.UnknownError))return{message:e.message,stack:e.stack,name:e.name};if(null!=(t=e)&&t.error){e.error=i(e.error),e.message=null!=(r=e.message)?r:e.error.message}return e}var l={error:function(e,t){t=i(t),console.error("[next-auth][error][".concat(e,"]"),"\nhttps://next-auth.js.org/errors#".concat(e.toLowerCase()),t.message,t)},warn:function(e){console.warn("[next-auth][warn][".concat(e,"]"),"\nhttps://next-auth.js.org/warnings#".concat(e.toLowerCase()))},debug:function(e,t){console.log("[next-auth][debug][".concat(e,"]"),t)}};t.default=l},45239:(e,t,r)=>{var n=r(30165);Object.defineProperty(t,"__esModule",{value:!0}),t.UnsupportedStrategy=t.UnknownError=t.OAuthCallbackError=t.MissingSecret=t.MissingAuthorize=t.MissingAdapterMethods=t.MissingAdapter=t.MissingAPIRoute=t.InvalidCallbackUrl=t.AccountNotLinkedError=void 0,t.adapterErrorHandler=function(e,t){if(e)return Object.keys(e).reduce(function(r,n){return r[n]=(0,o.default)(a.default.mark(function r(){var o,u,c,s,i,l=arguments;return a.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:for(r.prev=0,u=Array(o=l.length),c=0;c<o;c++)u[c]=l[c];return t.debug("adapter_".concat(n),{args:u}),s=e[n],r.next=6,s.apply(void 0,u);case 6:return r.abrupt("return",r.sent);case 9:throw r.prev=9,r.t0=r.catch(0),t.error("adapter_error_".concat(n),r.t0),(i=new v(r.t0)).name="".concat(g(n),"Error"),i;case 15:case"end":return r.stop()}},r,null,[[0,9]])})),r},{})},t.capitalize=g,t.eventsErrorHandler=function(e,t){return Object.keys(e).reduce(function(r,n){return r[n]=(0,o.default)(a.default.mark(function r(){var o,u=arguments;return a.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,o=e[n],r.next=4,o.apply(void 0,u);case 4:return r.abrupt("return",r.sent);case 7:r.prev=7,r.t0=r.catch(0),t.error("".concat(h(n),"_EVENT_ERROR"),r.t0);case 10:case"end":return r.stop()}},r,null,[[0,7]])})),r},{})},t.upperSnake=h;var a=n(r(14487)),o=n(r(22)),u=n(r(58630)),c=n(r(97348)),s=n(r(29588)),i=n(r(28359)),l=n(r(83419)),f=n(r(98812));function d(e,t,r){return t=(0,l.default)(t),(0,i.default)(e,p()?Reflect.construct(t,r||[],(0,l.default)(e).constructor):t.apply(e,r))}function p(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(p=function(){return!!e})()}var v=t.UnknownError=function(e){function t(e){var r,n;return(0,c.default)(this,t),(n=d(this,t,[null!=(r=null==e?void 0:e.message)?r:e])).name="UnknownError",n.code=e.code,e instanceof Error&&(n.stack=e.stack),n}return(0,f.default)(t,e),(0,s.default)(t,[{key:"toJSON",value:function(){return{name:this.name,message:this.message,stack:this.stack}}}])}((0,n(r(29454)).default)(Error));function h(e){return e.replace(/([A-Z])/g,"_$1").toUpperCase()}function g(e){return"".concat(e[0].toUpperCase()).concat(e.slice(1))}t.OAuthCallbackError=function(e){function t(){var e;(0,c.default)(this,t);for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return e=d(this,t,[].concat(n)),(0,u.default)(e,"name","OAuthCallbackError"),e}return(0,f.default)(t,e),(0,s.default)(t)}(v),t.AccountNotLinkedError=function(e){function t(){var e;(0,c.default)(this,t);for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return e=d(this,t,[].concat(n)),(0,u.default)(e,"name","AccountNotLinkedError"),e}return(0,f.default)(t,e),(0,s.default)(t)}(v),t.MissingAPIRoute=function(e){function t(){var e;(0,c.default)(this,t);for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return e=d(this,t,[].concat(n)),(0,u.default)(e,"name","MissingAPIRouteError"),(0,u.default)(e,"code","MISSING_NEXTAUTH_API_ROUTE_ERROR"),e}return(0,f.default)(t,e),(0,s.default)(t)}(v),t.MissingSecret=function(e){function t(){var e;(0,c.default)(this,t);for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return e=d(this,t,[].concat(n)),(0,u.default)(e,"name","MissingSecretError"),(0,u.default)(e,"code","NO_SECRET"),e}return(0,f.default)(t,e),(0,s.default)(t)}(v),t.MissingAuthorize=function(e){function t(){var e;(0,c.default)(this,t);for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return e=d(this,t,[].concat(n)),(0,u.default)(e,"name","MissingAuthorizeError"),(0,u.default)(e,"code","CALLBACK_CREDENTIALS_HANDLER_ERROR"),e}return(0,f.default)(t,e),(0,s.default)(t)}(v),t.MissingAdapter=function(e){function t(){var e;(0,c.default)(this,t);for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return e=d(this,t,[].concat(n)),(0,u.default)(e,"name","MissingAdapterError"),(0,u.default)(e,"code","EMAIL_REQUIRES_ADAPTER_ERROR"),e}return(0,f.default)(t,e),(0,s.default)(t)}(v),t.MissingAdapterMethods=function(e){function t(){var e;(0,c.default)(this,t);for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return e=d(this,t,[].concat(n)),(0,u.default)(e,"name","MissingAdapterMethodsError"),(0,u.default)(e,"code","MISSING_ADAPTER_METHODS_ERROR"),e}return(0,f.default)(t,e),(0,s.default)(t)}(v),t.UnsupportedStrategy=function(e){function t(){var e;(0,c.default)(this,t);for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return e=d(this,t,[].concat(n)),(0,u.default)(e,"name","UnsupportedStrategyError"),(0,u.default)(e,"code","CALLBACK_CREDENTIALS_JWT_ERROR"),e}return(0,f.default)(t,e),(0,s.default)(t)}(v),t.InvalidCallbackUrl=function(e){function t(){var e;(0,c.default)(this,t);for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return e=d(this,t,[].concat(n)),(0,u.default)(e,"name","InvalidCallbackUrl"),(0,u.default)(e,"code","INVALID_CALLBACK_URL_ERROR"),e}return(0,f.default)(t,e),(0,s.default)(t)}(v)},46070:(e,t,r)=>{var n=r(30165);Object.defineProperty(t,"__esModule",{value:!0}),t.BroadcastChannel=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"nextauth.message";return{receive:function(t){var r=function(r){if(r.key===e){var n,a=JSON.parse(null!=(n=r.newValue)?n:"{}");(null==a?void 0:a.event)==="session"&&null!=a&&a.data&&t(a)}};return window.addEventListener("storage",r),function(){return window.removeEventListener("storage",r)}},post:function(t){if("undefined"!=typeof window)try{localStorage.setItem(e,JSON.stringify(s(s({},t),{},{timestamp:f()})))}catch(e){}}}},t.apiBaseUrl=l,t.fetchData=function(e,t,r){return i.apply(this,arguments)},t.now=f;var a=n(r(14487)),o=n(r(58630)),u=n(r(22));function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){(0,o.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function i(){return(i=(0,u.default)(a.default.mark(function e(t,r,n){var o,u,c,i,f,d,p,v,h,g=arguments;return a.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return u=(o=g.length>3&&void 0!==g[3]?g[3]:{}).ctx,i=void 0===(c=o.req)?null==u?void 0:u.req:c,f="".concat(l(r),"/").concat(t),e.prev=2,p={headers:s({"Content-Type":"application/json"},null!=i&&null!=(d=i.headers)&&d.cookie?{cookie:i.headers.cookie}:{})},null!=i&&i.body&&(p.body=JSON.stringify(i.body),p.method="POST"),e.next=7,fetch(f,p);case 7:return v=e.sent,e.next=10,v.json();case 10:if(h=e.sent,v.ok){e.next=13;break}throw h;case 13:return e.abrupt("return",Object.keys(h).length>0?h:null);case 16:return e.prev=16,e.t0=e.catch(2),n.error("CLIENT_FETCH_ERROR",{error:e.t0,url:f}),e.abrupt("return",null);case 20:case"end":return e.stop()}},e,null,[[2,16]])}))).apply(this,arguments)}function l(e){return"undefined"==typeof window?"".concat(e.baseUrlServer).concat(e.basePathServer):e.basePath}function f(){return Math.floor(Date.now()/1e3)}},53424:(e,t,r)=>{var n,a,o,u,c=r(2209),s=r(30165),i=r(27617);Object.defineProperty(t,"__esModule",{value:!0});var l={SessionContext:!0,useSession:!0,getSession:!0,getCsrfToken:!0,getProviders:!0,signIn:!0,signOut:!0,SessionProvider:!0};t.SessionContext=void 0,t.SessionProvider=function(e){if(!R)throw Error("React Context is unavailable in Server Components");var t,r,n,a,o,u,c=e.children,s=e.basePath,i=e.refetchInterval,l=e.refetchWhenOffline;s&&(S.basePath=s);var d=void 0!==e.session;S._lastSync=d?(0,w.now)():0;var g=h.useState(function(){return d&&(S._session=e.session),e.session}),b=(0,v.default)(g,2),O=b[0],_=b[1],E=h.useState(!d),k=(0,v.default)(E,2),m=k[0],A=k[1];h.useEffect(function(){return S._getSession=(0,p.default)(f.default.mark(function e(){var t,r,n=arguments;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=(n.length>0&&void 0!==n[0]?n[0]:{}).event,e.prev=1,!((r="storage"===t)||void 0===S._session)){e.next=10;break}return S._lastSync=(0,w.now)(),e.next=7,j({broadcast:!r});case 7:return S._session=e.sent,_(S._session),e.abrupt("return");case 10:if(!(!t||null===S._session||(0,w.now)()<S._lastSync)){e.next=12;break}return e.abrupt("return");case 12:return S._lastSync=(0,w.now)(),e.next=15,j();case 15:S._session=e.sent,_(S._session),e.next=22;break;case 19:e.prev=19,e.t0=e.catch(1),x.error("CLIENT_SESSION_ERROR",e.t0);case 22:return e.prev=22,A(!1),e.finish(22);case 25:case"end":return e.stop()}},e,null,[[1,19,22,25]])})),S._getSession(),function(){S._lastSync=0,S._session=void 0,S._getSession=function(){}}},[]),h.useEffect(function(){var e=P.receive(function(){return S._getSession({event:"storage"})});return function(){return e()}},[]),h.useEffect(function(){var t=e.refetchOnWindowFocus,r=void 0===t||t,n=function(){r&&"visible"===document.visibilityState&&S._getSession({event:"visibilitychange"})};return document.addEventListener("visibilitychange",n,!1),function(){return document.removeEventListener("visibilitychange",n,!1)}},[e.refetchOnWindowFocus]);var U=(t=h.useState("undefined"!=typeof navigator&&navigator.onLine),n=(r=(0,v.default)(t,2))[0],a=r[1],o=function(){return a(!0)},u=function(){return a(!1)},h.useEffect(function(){return window.addEventListener("online",o),window.addEventListener("offline",u),function(){window.removeEventListener("online",o),window.removeEventListener("offline",u)}},[]),n),C=!1!==l||U;h.useEffect(function(){if(i&&C){var e=setInterval(function(){S._session&&S._getSession({event:"poll"})},1e3*i);return function(){return clearInterval(e)}}},[i,C]);var M=h.useMemo(function(){return{data:O,status:m?"loading":O?"authenticated":"unauthenticated",update:function(e){return(0,p.default)(f.default.mark(function t(){var r;return f.default.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!(m||!O)){t.next=2;break}return t.abrupt("return");case 2:return A(!0),t.t0=w.fetchData,t.t1=S,t.t2=x,t.next=8,L();case 8:return t.t3=t.sent,t.t4=e,t.t5={csrfToken:t.t3,data:t.t4},t.t6={body:t.t5},t.t7={req:t.t6},t.next=15,(0,t.t0)("session",t.t1,t.t2,t.t7);case 15:return r=t.sent,A(!1),r&&(_(r),P.post({event:"session",data:{trigger:"getSession"}})),t.abrupt("return",r);case 19:case"end":return t.stop()}},t)}))()}}},[O,m]);return(0,y.jsx)(R.Provider,{value:M,children:c})},t.getCsrfToken=L,t.getProviders=C,t.getSession=j,t.signIn=function(e,t,r){return T.apply(this,arguments)},t.signOut=function(e){return I.apply(this,arguments)},t.useSession=function(e){if(!R)throw Error("React Context is unavailable in Server Components");var t=h.useContext(R),r=null!=e?e:{},n=r.required,a=r.onUnauthenticated,o=n&&"unauthenticated"===t.status;return(h.useEffect(function(){if(o){var e="/api/auth/signin?".concat(new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href}));a?a():window.location.href=e}},[o,a]),o)?{data:t.data,update:t.update,status:"loading"}:t};var f=s(r(14487)),d=s(r(58630)),p=s(r(22)),v=s(r(30876)),h=E(r(94285)),g=E(r(28226)),b=s(r(72797)),w=r(46070),y=r(94513),O=r(70229);function _(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_=function(e){return e?r:t})(e)}function E(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var r=_(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var u=a?Object.getOwnPropertyDescriptor(e,o):null;u&&(u.get||u.set)?Object.defineProperty(n,o,u):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function k(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?k(Object(r),!0).forEach(function(t){(0,d.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Object.keys(O).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(l,e))&&(e in t&&t[e]===O[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return O[e]}}))});var S={baseUrl:(0,b.default)("http://***********:3000").origin,basePath:(0,b.default)("http://***********:3000").path,baseUrlServer:(0,b.default)(null!=(n=null!=(a=c.env.NEXTAUTH_URL_INTERNAL)?a:"http://***********:3000")?n:c.env.VERCEL_URL).origin,basePathServer:(0,b.default)(null!=(o=c.env.NEXTAUTH_URL_INTERNAL)?o:"http://***********:3000").path,_lastSync:0,_session:void 0,_getSession:function(){}},P=(0,w.BroadcastChannel)(),x=(0,g.proxyLogger)(g.default,S.basePath),R=t.SessionContext=null==(u=h.createContext)?void 0:u.call(h,void 0);function j(e){return A.apply(this,arguments)}function A(){return(A=(0,p.default)(f.default.mark(function e(t){var r,n;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,w.fetchData)("session",S,x,t);case 2:return n=e.sent,(null==(r=null==t?void 0:t.broadcast)||r)&&P.post({event:"session",data:{trigger:"getSession"}}),e.abrupt("return",n);case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function L(e){return U.apply(this,arguments)}function U(){return(U=(0,p.default)(f.default.mark(function e(t){var r;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,w.fetchData)("csrf",S,x,t);case 2:return r=e.sent,e.abrupt("return",null==r?void 0:r.csrfToken);case 4:case"end":return e.stop()}},e)}))).apply(this,arguments)}function C(){return M.apply(this,arguments)}function M(){return(M=(0,p.default)(f.default.mark(function e(){return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,w.fetchData)("providers",S,x);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}function T(){return(T=(0,p.default)(f.default.mark(function e(t,r,n){var a,o,u,c,s,i,l,d,p,v,h,g,b,y,O,_,E;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return u=void 0===(o=(a=null!=r?r:{}).callbackUrl)?window.location.href:o,s=void 0===(c=a.redirect)||c,i=(0,w.apiBaseUrl)(S),e.next=4,C();case 4:if(l=e.sent){e.next=8;break}return window.location.href="".concat(i,"/error"),e.abrupt("return");case 8:if(!(!t||!(t in l))){e.next=11;break}return window.location.href="".concat(i,"/signin?").concat(new URLSearchParams({callbackUrl:u})),e.abrupt("return");case 11:return d="credentials"===l[t].type,p="email"===l[t].type,v=d||p,h="".concat(i,"/").concat(d?"callback":"signin","/").concat(t),g="".concat(h).concat(n?"?".concat(new URLSearchParams(n)):""),e.t0=fetch,e.t1=g,e.t2={"Content-Type":"application/x-www-form-urlencoded"},e.t3=URLSearchParams,e.t4=m,e.t5=m({},r),e.t6={},e.next=25,L();case 25:return e.t7=e.sent,e.t8=u,e.t9={csrfToken:e.t7,callbackUrl:e.t8,json:!0},e.t10=(0,e.t4)(e.t5,e.t6,e.t9),e.t11=new e.t3(e.t10),e.t12={method:"post",headers:e.t2,body:e.t11},e.next=33,(0,e.t0)(e.t1,e.t12);case 33:return b=e.sent,e.next=36,b.json();case 36:if(y=e.sent,!(s||!v)){e.next=42;break}return _=null!=(O=y.url)?O:u,window.location.href=_,_.includes("#")&&window.location.reload(),e.abrupt("return");case 42:if(E=new URL(y.url).searchParams.get("error"),!b.ok){e.next=46;break}return e.next=46,S._getSession({event:"storage"});case 46:return e.abrupt("return",{error:E,status:b.status,ok:b.ok,url:E?null:y.url});case 47:case"end":return e.stop()}},e)}))).apply(this,arguments)}function I(){return(I=(0,p.default)(f.default.mark(function e(t){var r,n,a,o,u,c,s,i,l;return f.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a=void 0===(n=(null!=t?t:{}).callbackUrl)?window.location.href:n,o=(0,w.apiBaseUrl)(S),e.t0={"Content-Type":"application/x-www-form-urlencoded"},e.t1=URLSearchParams,e.next=6,L();case 6:return e.t2=e.sent,e.t3=a,e.t4={csrfToken:e.t2,callbackUrl:e.t3,json:!0},e.t5=new e.t1(e.t4),u={method:"post",headers:e.t0,body:e.t5},e.next=13,fetch("".concat(o,"/signout"),u);case 13:return c=e.sent,e.next=16,c.json();case 16:if(s=e.sent,P.post({event:"session",data:{trigger:"signout"}}),!(null==(r=null==t?void 0:t.redirect)||r)){e.next=23;break}return l=null!=(i=s.url)?i:a,window.location.href=l,l.includes("#")&&window.location.reload(),e.abrupt("return");case 23:return e.next=25,S._getSession({event:"storage"});case 25:return e.abrupt("return",s);case 26:case"end":return e.stop()}},e)}))).apply(this,arguments)}},70229:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},72797:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let r=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e=`https://${e}`);let n=new URL(null!=(t=e)?t:r),a=("/"===n.pathname?r.pathname:n.pathname).replace(/\/$/,""),o=`${n.origin}${a}`;return{origin:n.origin,host:n.host,path:a,base:o,toString:()=>o}}}}]);