"use strict";exports.id=2142,exports.ids=[2142],exports.modules={45261:(e,r,o)=>{o.d(r,{$7:()=>aw,UP:()=>av,w4:()=>az});var t=o(79464),a=o(30278);let{definePartsStyle:i,defineMultiStyleConfig:l}=(0,a.YU)(t.aH.keys),n=(0,a.H2)({borderTopWidth:"1px",borderColor:"inherit",_last:{borderBottomWidth:"1px"}}),s=(0,a.H2)({transitionProperty:"common",transitionDuration:"normal",fontSize:"md",_focusVisible:{boxShadow:"outline"},_hover:{bg:"blackAlpha.50"},_disabled:{opacity:.4,cursor:"not-allowed"},px:"4",py:"2"}),d=l({baseStyle:i({container:n,button:s,panel:(0,a.H2)({pt:"2",px:"4",pb:"5"}),icon:(0,a.H2)({fontSize:"1.25em"})})});var c=o(4487);let b=e=>0===Object.keys(e).length,g=(e,r,o)=>{let t=function(e,r,o,t,a){for(t=0,r=r.split?r.split("."):r;t<r.length;t++)e=e?e[r[t]]:void 0;return void 0===e?o:e}(e,`colors.${r}`,r);try{return(0,c.nj)(t),t}catch{return o??"#000000"}},p=e=>{let[r,o,t]=(0,c.Du)(e);return(299*r+587*o+114*t)/1e3},f=e=>r=>128>p(g(r,e))?"dark":"light",m=e=>r=>"dark"===f(e)(r),h=(e,r)=>o=>{let t=g(o,e);return(0,c.No)(t,1-r)};function u(e="1rem",r="rgba(255, 255, 255, 0.15)"){return{backgroundImage:`linear-gradient(
    45deg,
    ${r} 25%,
    transparent 25%,
    transparent 50%,
    ${r} 50%,
    ${r} 75%,
    transparent 75%,
    transparent
  )`,backgroundSize:`${e} ${e}`}}let x=()=>`#${Math.floor(0xffffff*Math.random()).toString(16).padEnd(6,"0")}`;function y(e,r){return o=>"dark"===o.colorMode?r:e}function v(e){let{orientation:r,vertical:o,horizontal:t}=e;return r?"vertical"===r?o:t:{}}var S=o(13910);function z(e){return(0,S.Gv)(e)&&e.reference?e.reference:String(e)}let w=(e,...r)=>r.map(z).join(` ${e} `).replace(/calc/g,""),k=(...e)=>`calc(${w("+",...e)})`,H=(...e)=>`calc(${w("-",...e)})`,_=(...e)=>`calc(${w("*",...e)})`,C=(...e)=>`calc(${w("/",...e)})`,$=e=>{let r=z(e);return null==r||Number.isNaN(parseFloat(r))?_(r,-1):String(r).startsWith("-")?String(r).slice(1):`-${r}`},A=Object.assign(e=>({add:(...r)=>A(k(e,...r)),subtract:(...r)=>A(H(e,...r)),multiply:(...r)=>A(_(e,...r)),divide:(...r)=>A(C(e,...r)),negate:()=>A($(e)),toString:()=>e.toString()}),{add:k,subtract:H,multiply:_,divide:C,negate:$});function F(e){let r=function(e,r="-"){return e.replace(/\s+/g,r)}(e.toString());return r.includes("\\.")?e:Number.isInteger(parseFloat(e.toString()))?e:r.replace(".","\\.")}function P(e,r){var o,t;let a=function(e,r=""){return`--${function(e,r=""){return[r,F(e)].filter(Boolean).join("-")}(e,r)}`}(e,r?.prefix);return{variable:a,reference:(o="string"==typeof(t=r?.fallback)?t:t?.reference,`var(${F(a)}${o?`, ${o}`:""})`)}}let{definePartsStyle:D,defineMultiStyleConfig:B}=(0,a.YU)(t.Ov.keys),V=(0,a.Vg)("alert-fg"),E=(0,a.Vg)("alert-bg"),W=D({container:{bg:E.reference,px:"4",py:"3"},title:{fontWeight:"bold",lineHeight:"6",marginEnd:"2"},description:{lineHeight:"6"},icon:{color:V.reference,flexShrink:0,marginEnd:"3",w:"5",h:"6"},spinner:{color:V.reference,flexShrink:0,marginEnd:"3",w:"5",h:"5"}});function R(e){let{theme:r,colorScheme:o}=e,t=h(`${o}.200`,.16)(r);return{light:`colors.${o}.100`,dark:t}}let M=D(e=>{let{colorScheme:r}=e,o=R(e);return{container:{[V.variable]:`colors.${r}.600`,[E.variable]:o.light,_dark:{[V.variable]:`colors.${r}.200`,[E.variable]:o.dark}}}}),I=D(e=>{let{colorScheme:r}=e,o=R(e);return{container:{[V.variable]:`colors.${r}.600`,[E.variable]:o.light,_dark:{[V.variable]:`colors.${r}.200`,[E.variable]:o.dark},paddingStart:"3",borderStartWidth:"4px",borderStartColor:V.reference}}}),U=D(e=>{let{colorScheme:r}=e,o=R(e);return{container:{[V.variable]:`colors.${r}.600`,[E.variable]:o.light,_dark:{[V.variable]:`colors.${r}.200`,[E.variable]:o.dark},pt:"2",borderTopWidth:"4px",borderTopColor:V.reference}}}),Y=B({baseStyle:W,variants:{subtle:M,"left-accent":I,"top-accent":U,solid:D(e=>{let{colorScheme:r}=e;return{container:{[V.variable]:"colors.white",[E.variable]:`colors.${r}.600`,_dark:{[V.variable]:"colors.gray.900",[E.variable]:`colors.${r}.200`},color:V.reference}}})},defaultProps:{variant:"subtle",colorScheme:"blue"}}),T={px:"1px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},j={...T,max:"max-content",min:"min-content",full:"100%","3xs":"14rem","2xs":"16rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem","8xl":"90rem",prose:"60ch",container:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px"}},L=e=>"function"==typeof e;function O(e,...r){return L(e)?e(...r):e}let{definePartsStyle:N,defineMultiStyleConfig:G}=(0,a.YU)(t.ZO.keys),Z=(0,a.Vg)("avatar-border-color"),q=(0,a.Vg)("avatar-bg"),K=(0,a.Vg)("avatar-font-size"),X=(0,a.Vg)("avatar-size"),J=(0,a.H2)({borderRadius:"full",border:"0.2em solid",borderColor:Z.reference,[Z.variable]:"white",_dark:{[Z.variable]:"colors.gray.800"}}),Q=(0,a.H2)({bg:q.reference,fontSize:K.reference,width:X.reference,height:X.reference,lineHeight:"1",[q.variable]:"colors.gray.200",_dark:{[q.variable]:"colors.whiteAlpha.400"}}),ee=(0,a.H2)(e=>{let{name:r,theme:o}=e,t=r?function(e){var r;let o=x();return!e||b(e)?o:e.string&&e.colors?function(e,r){let o=0;if(0===e.length)return r[0];for(let r=0;r<e.length;r+=1)o=e.charCodeAt(r)+((o<<5)-o),o&=o;return o=(o%r.length+r.length)%r.length,r[o]}(e.string,e.colors):e.string&&!e.colors?function(e){let r=0;if(0===e.length)return r.toString();for(let o=0;o<e.length;o+=1)r=e.charCodeAt(o)+((r<<5)-r),r&=r;let o="#";for(let e=0;e<3;e+=1){let t=r>>8*e&255;o+=`00${t.toString(16)}`.substr(-2)}return o}(e.string):e.colors&&!e.string?(r=e.colors)[Math.floor(Math.random()*r.length)]:o}({string:r}):"colors.gray.400",a=m(t)(o),i="white";return a||(i="gray.800"),{bg:q.reference,fontSize:K.reference,color:i,borderColor:Z.reference,verticalAlign:"top",width:X.reference,height:X.reference,"&:not([data-loaded])":{[q.variable]:t},[Z.variable]:"colors.white",_dark:{[Z.variable]:"colors.gray.800"}}}),er=(0,a.H2)({fontSize:K.reference,lineHeight:"1"});function eo(e){let r="100%"!==e?j[e]:void 0;return N({container:{[X.variable]:r??e,[K.variable]:`calc(${r??e} / 2.5)`},excessLabel:{[X.variable]:r??e,[K.variable]:`calc(${r??e} / 2.5)`}})}let et=G({baseStyle:N(e=>({badge:O(J,e),excessLabel:O(Q,e),container:O(ee,e),label:er})),sizes:{"2xs":eo(4),xs:eo(6),sm:eo(8),md:eo(12),lg:eo(16),xl:eo(24),"2xl":eo(32),full:eo("100%")},defaultProps:{size:"md"}}),ea=(0,a.lL)("badge",["bg","color","shadow"]),ei=(0,a.H2)({px:1,textTransform:"uppercase",fontSize:"xs",borderRadius:"sm",fontWeight:"bold",bg:ea.bg.reference,color:ea.color.reference,boxShadow:ea.shadow.reference}),el=(0,a.H2)(e=>{let{colorScheme:r,theme:o}=e,t=h(`${r}.500`,.6)(o);return{[ea.bg.variable]:`colors.${r}.500`,[ea.color.variable]:"colors.white",_dark:{[ea.bg.variable]:t,[ea.color.variable]:"colors.whiteAlpha.800"}}}),en=(0,a.H2)(e=>{let{colorScheme:r,theme:o}=e,t=h(`${r}.200`,.16)(o);return{[ea.bg.variable]:`colors.${r}.100`,[ea.color.variable]:`colors.${r}.800`,_dark:{[ea.bg.variable]:t,[ea.color.variable]:`colors.${r}.200`}}}),es=(0,a.H2)(e=>{let{colorScheme:r,theme:o}=e,t=h(`${r}.200`,.8)(o);return{[ea.color.variable]:`colors.${r}.500`,_dark:{[ea.color.variable]:t},[ea.shadow.variable]:`inset 0 0 0px 1px ${ea.color.reference}`}}),ed=(0,a.Dt)({baseStyle:ei,variants:{solid:el,subtle:en,outline:es},defaultProps:{variant:"subtle",colorScheme:"gray"}}),{defineMultiStyleConfig:ec,definePartsStyle:eb}=(0,a.YU)(t.RG.keys),eg=(0,a.Vg)("breadcrumb-link-decor"),ep=ec({baseStyle:eb({link:(0,a.H2)({transitionProperty:"common",transitionDuration:"fast",transitionTimingFunction:"ease-out",outline:"none",color:"inherit",textDecoration:eg.reference,[eg.variable]:"none","&:not([aria-current=page])":{cursor:"pointer",_hover:{[eg.variable]:"underline"},_focusVisible:{boxShadow:"outline"}}})})}),ef=(0,a.H2)({lineHeight:"1.2",borderRadius:"md",fontWeight:"semibold",transitionProperty:"common",transitionDuration:"normal",_focusVisible:{boxShadow:"outline"},_disabled:{opacity:.4,cursor:"not-allowed",boxShadow:"none"},_hover:{_disabled:{bg:"initial"}}}),em=(0,a.H2)(e=>{let{colorScheme:r,theme:o}=e;if("gray"===r)return{color:y("gray.800","whiteAlpha.900")(e),_hover:{bg:y("gray.100","whiteAlpha.200")(e)},_active:{bg:y("gray.200","whiteAlpha.300")(e)}};let t=h(`${r}.200`,.12)(o),a=h(`${r}.200`,.24)(o);return{color:y(`${r}.600`,`${r}.200`)(e),bg:"transparent",_hover:{bg:y(`${r}.50`,t)(e)},_active:{bg:y(`${r}.100`,a)(e)}}}),eh=(0,a.H2)(e=>{let{colorScheme:r}=e,o=y("gray.200","whiteAlpha.300")(e);return{border:"1px solid",borderColor:"gray"===r?o:"currentColor",".chakra-button__group[data-attached][data-orientation=horizontal] > &:not(:last-of-type)":{marginEnd:"-1px"},".chakra-button__group[data-attached][data-orientation=vertical] > &:not(:last-of-type)":{marginBottom:"-1px"},...O(em,e)}}),eu={yellow:{bg:"yellow.400",color:"black",hoverBg:"yellow.500",activeBg:"yellow.600"},cyan:{bg:"cyan.400",color:"black",hoverBg:"cyan.500",activeBg:"cyan.600"}},ex=(0,a.H2)(e=>{let{colorScheme:r}=e;if("gray"===r){let r=y("gray.100","whiteAlpha.200")(e);return{bg:r,color:y("gray.800","whiteAlpha.900")(e),_hover:{bg:y("gray.200","whiteAlpha.300")(e),_disabled:{bg:r}},_active:{bg:y("gray.300","whiteAlpha.400")(e)}}}let{bg:o=`${r}.500`,color:t="white",hoverBg:a=`${r}.600`,activeBg:i=`${r}.700`}=eu[r]??{},l=y(o,`${r}.200`)(e);return{bg:l,color:y(t,"gray.800")(e),_hover:{bg:y(a,`${r}.300`)(e),_disabled:{bg:l}},_active:{bg:y(i,`${r}.400`)(e)}}}),ey=(0,a.H2)(e=>{let{colorScheme:r}=e;return{padding:0,height:"auto",lineHeight:"normal",verticalAlign:"baseline",color:y(`${r}.500`,`${r}.200`)(e),_hover:{textDecoration:"underline",_disabled:{textDecoration:"none"}},_active:{color:y(`${r}.700`,`${r}.500`)(e)}}}),ev=(0,a.H2)({bg:"none",color:"inherit",display:"inline",lineHeight:"inherit",m:"0",p:"0"}),eS={lg:(0,a.H2)({h:"12",minW:"12",fontSize:"lg",px:"6"}),md:(0,a.H2)({h:"10",minW:"10",fontSize:"md",px:"4"}),sm:(0,a.H2)({h:"8",minW:"8",fontSize:"sm",px:"3"}),xs:(0,a.H2)({h:"6",minW:"6",fontSize:"xs",px:"2"})},ez=(0,a.Dt)({baseStyle:ef,variants:{ghost:em,outline:eh,solid:ex,link:ey,unstyled:ev},sizes:eS,defaultProps:{variant:"solid",size:"md",colorScheme:"gray"}}),{definePartsStyle:ew,defineMultiStyleConfig:ek}=(0,a.YU)(t.M9.keys),eH=(0,a.Vg)("card-bg"),e_=(0,a.Vg)("card-padding"),eC=(0,a.Vg)("card-shadow"),e$=(0,a.Vg)("card-radius"),eA=(0,a.Vg)("card-border-width","0"),eF=(0,a.Vg)("card-border-color"),eP=ew({container:{[eH.variable]:"colors.chakra-body-bg",backgroundColor:eH.reference,boxShadow:eC.reference,borderRadius:e$.reference,color:"chakra-body-text",borderWidth:eA.reference,borderColor:eF.reference},body:{padding:e_.reference,flex:"1 1 0%"},header:{padding:e_.reference},footer:{padding:e_.reference}}),eD={sm:ew({container:{[e$.variable]:"radii.base",[e_.variable]:"space.3"}}),md:ew({container:{[e$.variable]:"radii.md",[e_.variable]:"space.5"}}),lg:ew({container:{[e$.variable]:"radii.xl",[e_.variable]:"space.7"}})},eB=ek({baseStyle:eP,variants:{elevated:ew({container:{[eC.variable]:"shadows.base",_dark:{[eH.variable]:"colors.gray.700"}}}),outline:ew({container:{[eA.variable]:"1px",[eF.variable]:"colors.chakra-border-color"}}),filled:ew({container:{[eH.variable]:"colors.chakra-subtle-bg"}}),unstyled:{body:{[e_.variable]:0},header:{[e_.variable]:0},footer:{[e_.variable]:0}}},sizes:eD,defaultProps:{variant:"elevated",size:"md"}}),{definePartsStyle:eV,defineMultiStyleConfig:eE}=(0,a.YU)(t.fZ.keys),eW=(0,a.Vg)("checkbox-size"),eR=(0,a.H2)(e=>{let{colorScheme:r}=e;return{w:eW.reference,h:eW.reference,transitionProperty:"box-shadow",transitionDuration:"normal",border:"2px solid",borderRadius:"sm",borderColor:"inherit",color:"white",_checked:{bg:y(`${r}.500`,`${r}.200`)(e),borderColor:y(`${r}.500`,`${r}.200`)(e),color:y("white","gray.900")(e),_hover:{bg:y(`${r}.600`,`${r}.300`)(e),borderColor:y(`${r}.600`,`${r}.300`)(e)},_disabled:{borderColor:y("gray.200","transparent")(e),bg:y("gray.200","whiteAlpha.300")(e),color:y("gray.500","whiteAlpha.500")(e)}},_indeterminate:{bg:y(`${r}.500`,`${r}.200`)(e),borderColor:y(`${r}.500`,`${r}.200`)(e),color:y("white","gray.900")(e)},_disabled:{bg:y("gray.100","whiteAlpha.100")(e),borderColor:y("gray.100","transparent")(e)},_focusVisible:{boxShadow:"outline"},_invalid:{borderColor:y("red.500","red.300")(e)}}}),eM=(0,a.H2)({_disabled:{cursor:"not-allowed"}}),eI=(0,a.H2)({userSelect:"none",_disabled:{opacity:.4}}),eU=(0,a.H2)({transitionProperty:"transform",transitionDuration:"normal"}),eY=eE({baseStyle:eV(e=>({icon:eU,container:eM,control:O(eR,e),label:eI})),sizes:{sm:eV({control:{[eW.variable]:"sizes.3"},label:{fontSize:"sm"},icon:{fontSize:"3xs"}}),md:eV({control:{[eW.variable]:"sizes.4"},label:{fontSize:"md"},icon:{fontSize:"2xs"}}),lg:eV({control:{[eW.variable]:"sizes.5"},label:{fontSize:"lg"},icon:{fontSize:"2xs"}})},defaultProps:{size:"md",colorScheme:"blue"}}),eT=P("close-button-size"),ej=P("close-button-bg"),eL=(0,a.H2)({w:[eT.reference],h:[eT.reference],borderRadius:"md",transitionProperty:"common",transitionDuration:"normal",_disabled:{opacity:.4,cursor:"not-allowed",boxShadow:"none"},_hover:{[ej.variable]:"colors.blackAlpha.100",_dark:{[ej.variable]:"colors.whiteAlpha.100"}},_active:{[ej.variable]:"colors.blackAlpha.200",_dark:{[ej.variable]:"colors.whiteAlpha.200"}},_focusVisible:{boxShadow:"outline"},bg:ej.reference}),eO={lg:(0,a.H2)({[eT.variable]:"sizes.10",fontSize:"md"}),md:(0,a.H2)({[eT.variable]:"sizes.8",fontSize:"xs"}),sm:(0,a.H2)({[eT.variable]:"sizes.6",fontSize:"2xs"})},eN=(0,a.Dt)({baseStyle:eL,sizes:eO,defaultProps:{size:"md"}}),{variants:eG,defaultProps:eZ}=ed,eq=(0,a.H2)({fontFamily:"mono",fontSize:"sm",px:"0.2em",borderRadius:"sm",bg:ea.bg.reference,color:ea.color.reference,boxShadow:ea.shadow.reference}),eK=(0,a.Dt)({baseStyle:eq,variants:eG,defaultProps:eZ}),eX=(0,a.H2)({w:"100%",mx:"auto",maxW:"prose",px:"4"}),eJ=(0,a.Dt)({baseStyle:eX}),eQ=(0,a.H2)({opacity:.6,borderColor:"inherit"}),e0=(0,a.H2)({borderStyle:"solid"}),e2=(0,a.H2)({borderStyle:"dashed"}),e1=(0,a.Dt)({baseStyle:eQ,variants:{solid:e0,dashed:e2},defaultProps:{variant:"solid"}}),{definePartsStyle:e5,defineMultiStyleConfig:e4}=(0,a.YU)(t.Lx.keys),e6=(0,a.Vg)("drawer-bg"),e3=(0,a.Vg)("drawer-box-shadow");function e8(e){return"full"===e?e5({dialog:{maxW:"100vw",h:"100vh"}}):e5({dialog:{maxW:e}})}let e7=(0,a.H2)({bg:"blackAlpha.600",zIndex:"modal"}),e9=(0,a.H2)({display:"flex",zIndex:"modal",justifyContent:"center"}),re=(0,a.H2)(e=>{let{isFullHeight:r}=e;return{...r&&{height:"100vh"},zIndex:"modal",maxH:"100vh",color:"inherit",[e6.variable]:"colors.white",[e3.variable]:"shadows.lg",_dark:{[e6.variable]:"colors.gray.700",[e3.variable]:"shadows.dark-lg"},bg:e6.reference,boxShadow:e3.reference}}),rr=(0,a.H2)({px:"6",py:"4",fontSize:"xl",fontWeight:"semibold"}),ro=(0,a.H2)({position:"absolute",top:"2",insetEnd:"3"}),rt=(0,a.H2)({px:"6",py:"2",flex:"1",overflow:"auto"}),ra=(0,a.H2)({px:"6",py:"4"}),ri=e4({baseStyle:e5(e=>({overlay:e7,dialogContainer:e9,dialog:O(re,e),header:rr,closeButton:ro,body:rt,footer:ra})),sizes:{xs:e8("xs"),sm:e8("md"),md:e8("lg"),lg:e8("2xl"),xl:e8("4xl"),full:e8("full")},defaultProps:{size:"xs"}}),{definePartsStyle:rl,defineMultiStyleConfig:rn}=(0,a.YU)(t.Is.keys),rs=(0,a.H2)({borderRadius:"md",py:"1",transitionProperty:"common",transitionDuration:"normal"}),rd=rn({baseStyle:rl({preview:rs,input:(0,a.H2)({borderRadius:"md",py:"1",transitionProperty:"common",transitionDuration:"normal",width:"full",_focusVisible:{boxShadow:"outline"},_placeholder:{opacity:.6}}),textarea:(0,a.H2)({borderRadius:"md",py:"1",transitionProperty:"common",transitionDuration:"normal",width:"full",_focusVisible:{boxShadow:"outline"},_placeholder:{opacity:.6}})})}),{definePartsStyle:rc,defineMultiStyleConfig:rb}=(0,a.YU)(t.Ip.keys),rg=(0,a.Vg)("form-control-color"),rp=rb({baseStyle:rc({container:{width:"100%",position:"relative"},requiredIndicator:(0,a.H2)({marginStart:"1",[rg.variable]:"colors.red.500",_dark:{[rg.variable]:"colors.red.300"},color:rg.reference}),helperText:(0,a.H2)({mt:"2",[rg.variable]:"colors.gray.600",_dark:{[rg.variable]:"colors.whiteAlpha.600"},color:rg.reference,lineHeight:"normal",fontSize:"sm"})})}),{definePartsStyle:rf,defineMultiStyleConfig:rm}=(0,a.YU)(t._8.keys),rh=(0,a.Vg)("form-error-color"),ru=rm({baseStyle:rf({text:(0,a.H2)({[rh.variable]:"colors.red.500",_dark:{[rh.variable]:"colors.red.300"},color:rh.reference,mt:"2",fontSize:"sm",lineHeight:"normal"}),icon:(0,a.H2)({marginEnd:"0.5em",[rh.variable]:"colors.red.500",_dark:{[rh.variable]:"colors.red.300"},color:rh.reference})})}),rx=(0,a.H2)({fontSize:"md",marginEnd:"3",mb:"2",fontWeight:"medium",transitionProperty:"common",transitionDuration:"normal",opacity:1,_disabled:{opacity:.4}}),ry=(0,a.Dt)({baseStyle:rx}),rv=(0,a.H2)({fontFamily:"heading",fontWeight:"bold"}),rS={"4xl":(0,a.H2)({fontSize:["6xl",null,"7xl"],lineHeight:1}),"3xl":(0,a.H2)({fontSize:["5xl",null,"6xl"],lineHeight:1}),"2xl":(0,a.H2)({fontSize:["4xl",null,"5xl"],lineHeight:[1.2,null,1]}),xl:(0,a.H2)({fontSize:["3xl",null,"4xl"],lineHeight:[1.33,null,1.2]}),lg:(0,a.H2)({fontSize:["2xl",null,"3xl"],lineHeight:[1.33,null,1.2]}),md:(0,a.H2)({fontSize:"xl",lineHeight:1.2}),sm:(0,a.H2)({fontSize:"md",lineHeight:1.2}),xs:(0,a.H2)({fontSize:"sm",lineHeight:1.2})},rz=(0,a.Dt)({baseStyle:rv,sizes:rS,defaultProps:{size:"xl"}}),{definePartsStyle:rw,defineMultiStyleConfig:rk}=(0,a.YU)(t.Gq.keys),rH=(0,a.Vg)("input-height"),r_=(0,a.Vg)("input-font-size"),rC=(0,a.Vg)("input-padding"),r$=(0,a.Vg)("input-border-radius"),rA=rw({addon:{height:rH.reference,fontSize:r_.reference,px:rC.reference,borderRadius:r$.reference},field:{width:"100%",height:rH.reference,fontSize:r_.reference,px:rC.reference,borderRadius:r$.reference,minWidth:0,outline:0,position:"relative",appearance:"none",transitionProperty:"common",transitionDuration:"normal",_disabled:{opacity:.4,cursor:"not-allowed"}}}),rF={lg:(0,a.H2)({[r_.variable]:"fontSizes.lg",[rC.variable]:"space.4",[r$.variable]:"radii.md",[rH.variable]:"sizes.12"}),md:(0,a.H2)({[r_.variable]:"fontSizes.md",[rC.variable]:"space.4",[r$.variable]:"radii.md",[rH.variable]:"sizes.10"}),sm:(0,a.H2)({[r_.variable]:"fontSizes.sm",[rC.variable]:"space.3",[r$.variable]:"radii.sm",[rH.variable]:"sizes.8"}),xs:(0,a.H2)({[r_.variable]:"fontSizes.xs",[rC.variable]:"space.2",[r$.variable]:"radii.sm",[rH.variable]:"sizes.6"})},rP={lg:rw({field:rF.lg,group:rF.lg}),md:rw({field:rF.md,group:rF.md}),sm:rw({field:rF.sm,group:rF.sm}),xs:rw({field:rF.xs,group:rF.xs})};function rD(e){let{focusBorderColor:r,errorBorderColor:o}=e;return{focusBorderColor:r||y("blue.500","blue.300")(e),errorBorderColor:o||y("red.500","red.300")(e)}}let rB=rw(e=>{let{theme:r}=e,{focusBorderColor:o,errorBorderColor:t}=rD(e);return{field:{border:"1px solid",borderColor:"inherit",bg:"inherit",_hover:{borderColor:y("gray.300","whiteAlpha.400")(e)},_readOnly:{boxShadow:"none !important",userSelect:"all"},_invalid:{borderColor:g(r,t),boxShadow:`0 0 0 1px ${g(r,t)}`},_focusVisible:{zIndex:1,borderColor:g(r,o),boxShadow:`0 0 0 1px ${g(r,o)}`}},addon:{border:"1px solid",borderColor:y("inherit","whiteAlpha.50")(e),bg:y("gray.100","whiteAlpha.300")(e)}}}),rV=rw(e=>{let{theme:r}=e,{focusBorderColor:o,errorBorderColor:t}=rD(e);return{field:{border:"2px solid",borderColor:"transparent",bg:y("gray.100","whiteAlpha.50")(e),_hover:{bg:y("gray.200","whiteAlpha.100")(e)},_readOnly:{boxShadow:"none !important",userSelect:"all"},_invalid:{borderColor:g(r,t)},_focusVisible:{bg:"transparent",borderColor:g(r,o)}},addon:{border:"2px solid",borderColor:"transparent",bg:y("gray.100","whiteAlpha.50")(e)}}}),rE=rw(e=>{let{theme:r}=e,{focusBorderColor:o,errorBorderColor:t}=rD(e);return{field:{borderBottom:"1px solid",borderColor:"inherit",borderRadius:"0",px:"0",bg:"transparent",_readOnly:{boxShadow:"none !important",userSelect:"all"},_invalid:{borderColor:g(r,t),boxShadow:`0px 1px 0px 0px ${g(r,t)}`},_focusVisible:{borderColor:g(r,o),boxShadow:`0px 1px 0px 0px ${g(r,o)}`}},addon:{borderBottom:"2px solid",borderColor:"inherit",borderRadius:"0",px:"0",bg:"transparent"}}}),rW=rk({baseStyle:rA,sizes:rP,variants:{outline:rB,filled:rV,flushed:rE,unstyled:rw({field:{bg:"transparent",px:"0",height:"auto"},addon:{bg:"transparent",px:"0",height:"auto"}})},defaultProps:{size:"md",variant:"outline"}}),rR=(0,a.Vg)("kbd-bg"),rM=(0,a.H2)({[rR.variable]:"colors.gray.100",_dark:{[rR.variable]:"colors.whiteAlpha.100"},bg:rR.reference,borderRadius:"md",borderWidth:"1px",borderBottomWidth:"3px",fontSize:"0.8em",fontWeight:"bold",lineHeight:"normal",px:"0.4em",whiteSpace:"nowrap"}),rI=(0,a.Dt)({baseStyle:rM}),rU=(0,a.H2)({transitionProperty:"common",transitionDuration:"fast",transitionTimingFunction:"ease-out",cursor:"pointer",textDecoration:"none",outline:"none",color:"inherit",_hover:{textDecoration:"underline"},_focusVisible:{boxShadow:"outline"}}),rY=(0,a.Dt)({baseStyle:rU}),{defineMultiStyleConfig:rT,definePartsStyle:rj}=(0,a.YU)(t.yj.keys),rL=rT({baseStyle:rj({icon:(0,a.H2)({marginEnd:"2",display:"inline",verticalAlign:"text-bottom"})})}),{defineMultiStyleConfig:rO,definePartsStyle:rN}=(0,a.YU)(t.Pe.keys),rG=(0,a.Vg)("menu-bg"),rZ=(0,a.Vg)("menu-shadow"),rq=(0,a.H2)({[rG.variable]:"#fff",[rZ.variable]:"shadows.sm",_dark:{[rG.variable]:"colors.gray.700",[rZ.variable]:"shadows.dark-lg"},color:"inherit",minW:"3xs",py:"2",zIndex:"dropdown",borderRadius:"md",borderWidth:"1px",bg:rG.reference,boxShadow:rZ.reference}),rK=(0,a.H2)({py:"1.5",px:"3",transitionProperty:"background",transitionDuration:"ultra-fast",transitionTimingFunction:"ease-in",_focus:{[rG.variable]:"colors.gray.100",_dark:{[rG.variable]:"colors.whiteAlpha.100"}},_active:{[rG.variable]:"colors.gray.200",_dark:{[rG.variable]:"colors.whiteAlpha.200"}},_expanded:{[rG.variable]:"colors.gray.100",_dark:{[rG.variable]:"colors.whiteAlpha.100"}},_disabled:{opacity:.4,cursor:"not-allowed"},bg:rG.reference}),rX=(0,a.H2)({mx:4,my:2,fontWeight:"semibold",fontSize:"sm"}),rJ=(0,a.H2)({display:"inline-flex",alignItems:"center",justifyContent:"center",flexShrink:0}),rQ=(0,a.H2)({opacity:.6}),r0=(0,a.H2)({border:0,borderBottom:"1px solid",borderColor:"inherit",my:"2",opacity:.6}),r2=rO({baseStyle:rN({button:(0,a.H2)({transitionProperty:"common",transitionDuration:"normal"}),list:rq,item:rK,groupTitle:rX,icon:rJ,command:rQ,divider:r0})}),{defineMultiStyleConfig:r1,definePartsStyle:r5}=(0,a.YU)(t.Zt.keys),r4=(0,a.Vg)("modal-bg"),r6=(0,a.Vg)("modal-shadow"),r3=(0,a.H2)({bg:"blackAlpha.600",zIndex:"modal"}),r8=(0,a.H2)(e=>{let{isCentered:r,scrollBehavior:o}=e;return{display:"flex",zIndex:"modal",justifyContent:"center",alignItems:r?"center":"flex-start",overflow:"inside"===o?"hidden":"auto",overscrollBehaviorY:"none"}}),r7=(0,a.H2)(e=>{let{isCentered:r,scrollBehavior:o}=e;return{borderRadius:"md",color:"inherit",my:r?"auto":"16",mx:r?"auto":void 0,zIndex:"modal",maxH:"inside"===o?"calc(100% - 7.5rem)":void 0,[r4.variable]:"colors.white",[r6.variable]:"shadows.lg",_dark:{[r4.variable]:"colors.gray.700",[r6.variable]:"shadows.dark-lg"},bg:r4.reference,boxShadow:r6.reference}}),r9=(0,a.H2)({px:"6",py:"4",fontSize:"xl",fontWeight:"semibold"}),oe=(0,a.H2)({position:"absolute",top:"2",insetEnd:"3"}),or=(0,a.H2)(e=>{let{scrollBehavior:r}=e;return{px:"6",py:"2",flex:"1",overflow:"inside"===r?"auto":void 0}}),oo=(0,a.H2)({px:"6",py:"4"});function ot(e){return"full"===e?r5({dialog:{maxW:"100vw",minH:"$100vh",my:"0",borderRadius:"0"}}):r5({dialog:{maxW:e}})}let oa=r1({baseStyle:r5(e=>({overlay:r3,dialogContainer:O(r8,e),dialog:O(r7,e),header:r9,closeButton:oe,body:O(or,e),footer:oo})),sizes:{xs:ot("xs"),sm:ot("sm"),md:ot("md"),lg:ot("lg"),xl:ot("xl"),"2xl":ot("2xl"),"3xl":ot("3xl"),"4xl":ot("4xl"),"5xl":ot("5xl"),"6xl":ot("6xl"),full:ot("full")},defaultProps:{size:"md"}}),oi={letterSpacings:{tighter:"-0.05em",tight:"-0.025em",normal:"0",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeights:{normal:"normal",none:1,shorter:1.25,short:1.375,base:1.5,tall:1.625,taller:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},fontWeights:{hairline:100,thin:200,light:300,normal:400,medium:500,semibold:600,bold:700,extrabold:800,black:900},fonts:{heading:'-apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',body:'-apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',mono:'SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace'},fontSizes:{"3xs":"0.45rem","2xs":"0.625rem",xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem","7xl":"4.5rem","8xl":"6rem","9xl":"8rem"}},{defineMultiStyleConfig:ol,definePartsStyle:on}=(0,a.YU)(t.zV.keys),os=P("number-input-stepper-width"),od=P("number-input-input-padding"),oc=A(os).add("0.5rem").toString(),ob=P("number-input-bg"),og=P("number-input-color"),op=P("number-input-border-color"),of=(0,a.H2)({[os.variable]:"sizes.6",[od.variable]:oc}),om=(0,a.H2)(e=>O(rW.baseStyle,e)?.field??{}),oh=(0,a.H2)({width:os.reference}),ou=(0,a.H2)({borderStart:"1px solid",borderStartColor:op.reference,color:og.reference,bg:ob.reference,[og.variable]:"colors.chakra-body-text",[op.variable]:"colors.chakra-border-color",_dark:{[og.variable]:"colors.whiteAlpha.800",[op.variable]:"colors.whiteAlpha.300"},_active:{[ob.variable]:"colors.gray.200",_dark:{[ob.variable]:"colors.whiteAlpha.300"}},_disabled:{opacity:.4,cursor:"not-allowed"}});function ox(e){let r=rW.sizes?.[e],o={lg:"md",md:"md",sm:"sm",xs:"sm"},t=r.field?.fontSize??"md",a=oi.fontSizes[t];return on({field:{...r.field,paddingInlineEnd:od.reference,verticalAlign:"top"},stepper:{fontSize:A(a).multiply(.75).toString(),_first:{borderTopEndRadius:o[e]},_last:{borderBottomEndRadius:o[e],mt:"-1px",borderTopWidth:1}}})}let oy=ol({baseStyle:on(e=>({root:of,field:O(om,e)??{},stepperGroup:oh,stepper:ou})),sizes:{xs:ox("xs"),sm:ox("sm"),md:ox("md"),lg:ox("lg")},variants:rW.variants,defaultProps:rW.defaultProps}),ov=(0,a.H2)({...rW.baseStyle?.field,textAlign:"center"}),oS={lg:(0,a.H2)({fontSize:"lg",w:12,h:12,borderRadius:"md"}),md:(0,a.H2)({fontSize:"md",w:10,h:10,borderRadius:"md"}),sm:(0,a.H2)({fontSize:"sm",w:8,h:8,borderRadius:"sm"}),xs:(0,a.H2)({fontSize:"xs",w:6,h:6,borderRadius:"sm"})},oz={outline:(0,a.H2)(e=>O(rW.variants?.outline,e)?.field??{}),flushed:(0,a.H2)(e=>O(rW.variants?.flushed,e)?.field??{}),filled:(0,a.H2)(e=>O(rW.variants?.filled,e)?.field??{}),unstyled:rW.variants?.unstyled.field??{}},ow=(0,a.Dt)({baseStyle:ov,sizes:oS,variants:oz,defaultProps:rW.defaultProps}),{defineMultiStyleConfig:ok,definePartsStyle:oH}=(0,a.YU)(t.vI.keys),o_=P("popper-bg"),oC=P("popper-arrow-bg"),o$=P("popper-arrow-shadow-color"),oA=(0,a.H2)({zIndex:"popover"}),oF=(0,a.H2)({[o_.variable]:"colors.white",bg:o_.reference,[oC.variable]:o_.reference,[o$.variable]:"colors.gray.200",_dark:{[o_.variable]:"colors.gray.700",[o$.variable]:"colors.whiteAlpha.300"},width:"xs",border:"1px solid",borderColor:"inherit",borderRadius:"md",boxShadow:"sm",zIndex:"inherit",_focusVisible:{outline:0,boxShadow:"outline"}}),oP=(0,a.H2)({px:3,py:2,borderBottomWidth:"1px"}),oD=(0,a.H2)({px:3,py:2}),oB=ok({baseStyle:oH({popper:oA,content:oF,header:oP,body:oD,footer:(0,a.H2)({px:3,py:2,borderTopWidth:"1px"}),closeButton:(0,a.H2)({position:"absolute",borderRadius:"md",top:1,insetEnd:2,padding:2})})}),{defineMultiStyleConfig:oV,definePartsStyle:oE}=(0,a.YU)(t.j_.keys),oW=(0,a.H2)(e=>{let{colorScheme:r,theme:o,isIndeterminate:t,hasStripe:a}=e,i=y(u(),u("1rem","rgba(0,0,0,0.1)"))(e),l=y(`${r}.500`,`${r}.200`)(e),n=`linear-gradient(
    to right,
    transparent 0%,
    ${g(o,l)} 50%,
    transparent 100%
  )`;return{...!t&&a&&i,...t?{bgImage:n}:{bgColor:l}}}),oR=(0,a.H2)({lineHeight:"1",fontSize:"0.25em",fontWeight:"bold",color:"white"}),oM=(0,a.H2)(e=>({bg:y("gray.100","whiteAlpha.300")(e)})),oI=(0,a.H2)(e=>({transitionProperty:"common",transitionDuration:"slow",...oW(e)})),oU=oE(e=>({label:oR,filledTrack:oI(e),track:oM(e)})),oY=oV({sizes:{xs:oE({track:{h:"1"}}),sm:oE({track:{h:"2"}}),md:oE({track:{h:"3"}}),lg:oE({track:{h:"4"}})},baseStyle:oU,defaultProps:{size:"md",colorScheme:"blue"}}),{defineMultiStyleConfig:oT,definePartsStyle:oj}=(0,a.YU)(t.Vg.keys),oL=(0,a.H2)(e=>{let r=O(eY.baseStyle,e)?.control;return{...r,borderRadius:"full",_checked:{...r?._checked,_before:{content:'""',display:"inline-block",pos:"relative",w:"50%",h:"50%",borderRadius:"50%",bg:"currentColor"}}}}),oO=oT({baseStyle:oj(e=>({label:eY.baseStyle?.(e).label,container:eY.baseStyle?.(e).container,control:oL(e)})),sizes:{md:oj({control:{w:"4",h:"4"},label:{fontSize:"md"}}),lg:oj({control:{w:"5",h:"5"},label:{fontSize:"lg"}}),sm:oj({control:{width:"3",height:"3"},label:{fontSize:"sm"}})},defaultProps:{size:"md",colorScheme:"blue"}}),{defineMultiStyleConfig:oN,definePartsStyle:oG}=(0,a.YU)(t.oc.keys),oZ=(0,a.Vg)("select-bg"),oq=oG({field:(0,a.H2)({...rW.baseStyle?.field,appearance:"none",paddingBottom:"1px",lineHeight:"normal",bg:oZ.reference,[oZ.variable]:"colors.white",_dark:{[oZ.variable]:"colors.gray.700"},"> option, > optgroup":{bg:oZ.reference}}),icon:(0,a.H2)({width:"6",height:"100%",insetEnd:"2",position:"relative",color:"currentColor",fontSize:"xl",_disabled:{opacity:.5}})}),oK=(0,a.H2)({paddingInlineEnd:"8"}),oX=oN({baseStyle:oq,sizes:{lg:{...rW.sizes?.lg,field:{...rW.sizes?.lg.field,...oK}},md:{...rW.sizes?.md,field:{...rW.sizes?.md.field,...oK}},sm:{...rW.sizes?.sm,field:{...rW.sizes?.sm.field,...oK}},xs:{...rW.sizes?.xs,field:{...rW.sizes?.xs.field,...oK},icon:{insetEnd:"1"}}},variants:rW.variants,defaultProps:rW.defaultProps}),oJ=(0,a.Vg)("skeleton-start-color"),oQ=(0,a.Vg)("skeleton-end-color"),o0=(0,a.H2)({[oJ.variable]:"colors.gray.100",[oQ.variable]:"colors.gray.400",_dark:{[oJ.variable]:"colors.gray.800",[oQ.variable]:"colors.gray.600"},background:oJ.reference,borderColor:oQ.reference,opacity:.7,borderRadius:"sm"}),o2=(0,a.Dt)({baseStyle:o0}),o1=(0,a.Vg)("skip-link-bg"),o5=(0,a.H2)({borderRadius:"md",fontWeight:"semibold",_focusVisible:{boxShadow:"outline",padding:"4",position:"fixed",top:"6",insetStart:"6",[o1.variable]:"colors.white",_dark:{[o1.variable]:"colors.gray.700"},bg:o1.reference}}),o4=(0,a.Dt)({baseStyle:o5}),{defineMultiStyleConfig:o6,definePartsStyle:o3}=(0,a.YU)(t.tC.keys),o8=(0,a.Vg)("slider-thumb-size"),o7=(0,a.Vg)("slider-track-size"),o9=(0,a.Vg)("slider-bg"),te=(0,a.H2)(e=>{let{orientation:r}=e;return{display:"inline-block",position:"relative",cursor:"pointer",_disabled:{opacity:.6,cursor:"default",pointerEvents:"none"},...v({orientation:r,vertical:{h:"100%",px:(0,a._u)(o8.reference).divide(2).toString()},horizontal:{w:"100%",py:(0,a._u)(o8.reference).divide(2).toString()}})}}),tr=(0,a.H2)(e=>({...v({orientation:e.orientation,horizontal:{h:o7.reference},vertical:{w:o7.reference}}),overflow:"hidden",borderRadius:"sm",[o9.variable]:"colors.gray.200",_dark:{[o9.variable]:"colors.whiteAlpha.200"},_disabled:{[o9.variable]:"colors.gray.300",_dark:{[o9.variable]:"colors.whiteAlpha.300"}},bg:o9.reference})),to=(0,a.H2)(e=>{let{orientation:r}=e;return{...v({orientation:r,vertical:{left:"50%"},horizontal:{top:"50%"}}),w:o8.reference,h:o8.reference,display:"flex",alignItems:"center",justifyContent:"center",position:"absolute",outline:0,zIndex:1,borderRadius:"full",bg:"white",boxShadow:"base",border:"1px solid",borderColor:"transparent",transitionProperty:"transform",transitionDuration:"normal",_focusVisible:{boxShadow:"outline"},_active:{"--slider-thumb-scale":"1.15"},_disabled:{bg:"gray.300"}}}),tt=(0,a.H2)(e=>{let{colorScheme:r}=e;return{width:"inherit",height:"inherit",[o9.variable]:`colors.${r}.500`,_dark:{[o9.variable]:`colors.${r}.200`},bg:o9.reference}}),ta=o3(e=>({container:te(e),track:tr(e),thumb:to(e),filledTrack:tt(e)})),ti=o3({container:{[o8.variable]:"sizes.4",[o7.variable]:"sizes.1"}}),tl=o3({container:{[o8.variable]:"sizes.3.5",[o7.variable]:"sizes.1"}}),tn=o6({baseStyle:ta,sizes:{lg:ti,md:tl,sm:o3({container:{[o8.variable]:"sizes.2.5",[o7.variable]:"sizes.0.5"}})},defaultProps:{size:"md",colorScheme:"blue"}}),ts=P("spinner-size"),td=(0,a.H2)({width:[ts.reference],height:[ts.reference]}),tc={xs:(0,a.H2)({[ts.variable]:"sizes.3"}),sm:(0,a.H2)({[ts.variable]:"sizes.4"}),md:(0,a.H2)({[ts.variable]:"sizes.6"}),lg:(0,a.H2)({[ts.variable]:"sizes.8"}),xl:(0,a.H2)({[ts.variable]:"sizes.12"})},tb=(0,a.Dt)({baseStyle:td,sizes:tc,defaultProps:{size:"md"}}),{defineMultiStyleConfig:tg,definePartsStyle:tp}=(0,a.YU)(t.S4.keys),tf=(0,a.H2)({fontWeight:"medium"}),tm=(0,a.H2)({opacity:.8,marginBottom:"2"}),th=tg({baseStyle:tp({container:{},label:tf,helpText:tm,number:(0,a.H2)({verticalAlign:"baseline",fontWeight:"semibold"}),icon:(0,a.H2)({marginEnd:1,w:"3.5",h:"3.5",verticalAlign:"middle"})}),sizes:{md:tp({label:{fontSize:"sm"},helpText:{fontSize:"sm"},number:{fontSize:"2xl"}})},defaultProps:{size:"md"}}),{defineMultiStyleConfig:tu,definePartsStyle:tx}=(0,a.YU)(["stepper","step","title","description","indicator","separator","icon","number"]),ty=(0,a.Vg)("stepper-indicator-size"),tv=(0,a.Vg)("stepper-icon-size"),tS=(0,a.Vg)("stepper-title-font-size"),tz=(0,a.Vg)("stepper-description-font-size"),tw=(0,a.Vg)("stepper-accent-color"),tk=tu({baseStyle:tx(({colorScheme:e})=>({stepper:{display:"flex",justifyContent:"space-between",gap:"4","&[data-orientation=vertical]":{flexDirection:"column",alignItems:"flex-start"},"&[data-orientation=horizontal]":{flexDirection:"row",alignItems:"center"},[tw.variable]:`colors.${e}.500`,_dark:{[tw.variable]:`colors.${e}.200`}},title:{fontSize:tS.reference,fontWeight:"medium"},description:{fontSize:tz.reference,color:"chakra-subtle-text"},number:{fontSize:tS.reference},step:{flexShrink:0,position:"relative",display:"flex",gap:"2","&[data-orientation=horizontal]":{alignItems:"center"},flex:"1","&:last-of-type:not([data-stretch])":{flex:"initial"}},icon:{flexShrink:0,width:tv.reference,height:tv.reference},indicator:{flexShrink:0,borderRadius:"full",width:ty.reference,height:ty.reference,display:"flex",justifyContent:"center",alignItems:"center","&[data-status=active]":{borderWidth:"2px",borderColor:tw.reference},"&[data-status=complete]":{bg:tw.reference,color:"chakra-inverse-text"},"&[data-status=incomplete]":{borderWidth:"2px"}},separator:{bg:"chakra-border-color",flex:"1","&[data-status=complete]":{bg:tw.reference},"&[data-orientation=horizontal]":{width:"100%",height:"2px",marginStart:"2"},"&[data-orientation=vertical]":{width:"2px",position:"absolute",height:"100%",maxHeight:`calc(100% - ${ty.reference} - 8px)`,top:`calc(${ty.reference} + 4px)`,insetStart:`calc(${ty.reference} / 2 - 1px)`}}})),sizes:{xs:tx({stepper:{[ty.variable]:"sizes.4",[tv.variable]:"sizes.3",[tS.variable]:"fontSizes.xs",[tz.variable]:"fontSizes.xs"}}),sm:tx({stepper:{[ty.variable]:"sizes.6",[tv.variable]:"sizes.4",[tS.variable]:"fontSizes.sm",[tz.variable]:"fontSizes.xs"}}),md:tx({stepper:{[ty.variable]:"sizes.8",[tv.variable]:"sizes.5",[tS.variable]:"fontSizes.md",[tz.variable]:"fontSizes.sm"}}),lg:tx({stepper:{[ty.variable]:"sizes.10",[tv.variable]:"sizes.6",[tS.variable]:"fontSizes.lg",[tz.variable]:"fontSizes.md"}})},defaultProps:{size:"md",colorScheme:"blue"}}),{defineMultiStyleConfig:tH,definePartsStyle:t_}=(0,a.YU)(t.af.keys),tC=P("switch-track-width"),t$=P("switch-track-height"),tA=P("switch-track-diff"),tF=A.subtract(tC,t$),tP=P("switch-thumb-x"),tD=P("switch-bg"),tB=(0,a.H2)(e=>{let{colorScheme:r}=e;return{borderRadius:"full",p:"0.5",width:[tC.reference],height:[t$.reference],transitionProperty:"common",transitionDuration:"fast",[tD.variable]:"colors.gray.300",_dark:{[tD.variable]:"colors.whiteAlpha.400"},_focusVisible:{boxShadow:"outline"},_disabled:{opacity:.4,cursor:"not-allowed"},_checked:{[tD.variable]:`colors.${r}.500`,_dark:{[tD.variable]:`colors.${r}.200`}},bg:tD.reference}}),tV=(0,a.H2)({bg:"white",transitionProperty:"transform",transitionDuration:"normal",borderRadius:"inherit",width:[t$.reference],height:[t$.reference],_checked:{transform:`translateX(${tP.reference})`}}),tE=tH({baseStyle:t_(e=>({container:{[tA.variable]:tF,[tP.variable]:tA.reference,_rtl:{[tP.variable]:A(tA).negate().toString()}},track:tB(e),thumb:tV})),sizes:{sm:t_({container:{[tC.variable]:"1.375rem",[t$.variable]:"sizes.3"}}),md:t_({container:{[tC.variable]:"1.875rem",[t$.variable]:"sizes.4"}}),lg:t_({container:{[tC.variable]:"2.875rem",[t$.variable]:"sizes.6"}})},defaultProps:{size:"md",colorScheme:"blue"}}),{defineMultiStyleConfig:tW,definePartsStyle:tR}=(0,a.YU)(t.e.keys),tM=tR({table:{fontVariantNumeric:"lining-nums tabular-nums",borderCollapse:"collapse",width:"full"},th:{fontFamily:"heading",fontWeight:"bold",textTransform:"uppercase",letterSpacing:"wider",textAlign:"start"},td:{textAlign:"start"},caption:{mt:4,fontFamily:"heading",textAlign:"center",fontWeight:"medium"}}),tI=(0,a.H2)({"&[data-is-numeric=true]":{textAlign:"end"}}),tU=tW({baseStyle:tM,variants:{simple:tR(e=>{let{colorScheme:r}=e;return{th:{color:y("gray.600","gray.400")(e),borderBottom:"1px",borderColor:y(`${r}.100`,`${r}.700`)(e),...tI},td:{borderBottom:"1px",borderColor:y(`${r}.100`,`${r}.700`)(e),...tI},caption:{color:y("gray.600","gray.100")(e)},tfoot:{tr:{"&:last-of-type":{th:{borderBottomWidth:0}}}}}}),striped:tR(e=>{let{colorScheme:r}=e;return{th:{color:y("gray.600","gray.400")(e),borderBottom:"1px",borderColor:y(`${r}.100`,`${r}.700`)(e),...tI},td:{borderBottom:"1px",borderColor:y(`${r}.100`,`${r}.700`)(e),...tI},caption:{color:y("gray.600","gray.100")(e)},tbody:{tr:{"&:nth-of-type(odd)":{"th, td":{borderBottomWidth:"1px",borderColor:y(`${r}.100`,`${r}.700`)(e)},td:{background:y(`${r}.100`,`${r}.700`)(e)}}}},tfoot:{tr:{"&:last-of-type":{th:{borderBottomWidth:0}}}}}}),unstyled:(0,a.H2)({})},sizes:{sm:tR({th:{px:"4",py:"1",lineHeight:"4",fontSize:"xs"},td:{px:"4",py:"2",fontSize:"sm",lineHeight:"4"},caption:{px:"4",py:"2",fontSize:"xs"}}),md:tR({th:{px:"6",py:"3",lineHeight:"4",fontSize:"xs"},td:{px:"6",py:"4",lineHeight:"5"},caption:{px:"6",py:"2",fontSize:"sm"}}),lg:tR({th:{px:"8",py:"4",lineHeight:"5",fontSize:"sm"},td:{px:"8",py:"5",lineHeight:"6"},caption:{px:"6",py:"2",fontSize:"md"}})},defaultProps:{variant:"simple",size:"md",colorScheme:"gray"}}),tY=(0,a.Vg)("tabs-color"),tT=(0,a.Vg)("tabs-bg"),tj=(0,a.Vg)("tabs-border-color"),{defineMultiStyleConfig:tL,definePartsStyle:tO}=(0,a.YU)(t.Us.keys),tN=(0,a.H2)(e=>{let{orientation:r}=e;return{display:"vertical"===r?"flex":"block"}}),tG=(0,a.H2)(e=>{let{isFitted:r}=e;return{flex:r?1:void 0,transitionProperty:"common",transitionDuration:"normal",_focusVisible:{zIndex:1,boxShadow:"outline"},_disabled:{cursor:"not-allowed",opacity:.4}}}),tZ=(0,a.H2)(e=>{let{align:r="start",orientation:o}=e;return{justifyContent:({end:"flex-end",center:"center",start:"flex-start"})[r],flexDirection:"vertical"===o?"column":"row"}}),tq=(0,a.H2)({p:4}),tK=tO(e=>({root:tN(e),tab:tG(e),tablist:tZ(e),tabpanel:tq})),tX={sm:tO({tab:{py:1,px:4,fontSize:"sm"}}),md:tO({tab:{fontSize:"md",py:2,px:4}}),lg:tO({tab:{fontSize:"lg",py:3,px:4}})},tJ=tO(e=>{let{colorScheme:r,orientation:o}=e,t="vertical"===o,a=t?"borderStart":"borderBottom";return{tablist:{[a]:"2px solid",borderColor:"inherit"},tab:{[a]:"2px solid",borderColor:"transparent",[t?"marginStart":"marginBottom"]:"-2px",_selected:{[tY.variable]:`colors.${r}.600`,_dark:{[tY.variable]:`colors.${r}.300`},borderColor:"currentColor"},_active:{[tT.variable]:"colors.gray.200",_dark:{[tT.variable]:"colors.whiteAlpha.300"}},_disabled:{_active:{bg:"none"}},color:tY.reference,bg:tT.reference}}}),tQ=tO(e=>{let{colorScheme:r}=e;return{tab:{borderTopRadius:"md",border:"1px solid",borderColor:"transparent",mb:"-1px",[tj.variable]:"transparent",_selected:{[tY.variable]:`colors.${r}.600`,[tj.variable]:"colors.white",_dark:{[tY.variable]:`colors.${r}.300`,[tj.variable]:"colors.gray.800"},borderColor:"inherit",borderBottomColor:tj.reference},color:tY.reference},tablist:{mb:"-1px",borderBottom:"1px solid",borderColor:"inherit"}}}),t0=tO(e=>{let{colorScheme:r}=e;return{tab:{border:"1px solid",borderColor:"inherit",[tT.variable]:"colors.gray.50",_dark:{[tT.variable]:"colors.whiteAlpha.50"},mb:"-1px",_notLast:{marginEnd:"-1px"},_selected:{[tT.variable]:"colors.white",[tY.variable]:`colors.${r}.600`,_dark:{[tT.variable]:"colors.gray.800",[tY.variable]:`colors.${r}.300`},borderColor:"inherit",borderTopColor:"currentColor",borderBottomColor:"transparent"},color:tY.reference,bg:tT.reference},tablist:{mb:"-1px",borderBottom:"1px solid",borderColor:"inherit"}}}),t2=tO(e=>{let{colorScheme:r,theme:o}=e;return{tab:{borderRadius:"full",fontWeight:"semibold",color:"gray.600",_selected:{color:g(o,`${r}.700`),bg:g(o,`${r}.100`)}}}}),t1=tO(e=>{let{colorScheme:r}=e;return{tab:{borderRadius:"full",fontWeight:"semibold",[tY.variable]:"colors.gray.600",_dark:{[tY.variable]:"inherit"},_selected:{[tY.variable]:"colors.white",[tT.variable]:`colors.${r}.600`,_dark:{[tY.variable]:"colors.gray.800",[tT.variable]:`colors.${r}.300`}},color:tY.reference,bg:tT.reference}}}),t5=tL({baseStyle:tK,sizes:tX,variants:{line:tJ,enclosed:tQ,"enclosed-colored":t0,"soft-rounded":t2,"solid-rounded":t1,unstyled:tO({})},defaultProps:{size:"md",variant:"line",colorScheme:"blue"}}),{defineMultiStyleConfig:t4,definePartsStyle:t6}=(0,a.YU)(t.K_.keys),t3=(0,a.Vg)("tag-bg"),t8=(0,a.Vg)("tag-color"),t7=(0,a.Vg)("tag-shadow"),t9=(0,a.Vg)("tag-min-height"),ae=(0,a.Vg)("tag-min-width"),ar=(0,a.Vg)("tag-font-size"),ao=(0,a.Vg)("tag-padding-inline"),at=(0,a.H2)({fontWeight:"medium",lineHeight:1.2,outline:0,[t8.variable]:ea.color.reference,[t3.variable]:ea.bg.reference,[t7.variable]:ea.shadow.reference,color:t8.reference,bg:t3.reference,boxShadow:t7.reference,borderRadius:"md",minH:t9.reference,minW:ae.reference,fontSize:ar.reference,px:ao.reference,_focusVisible:{[t7.variable]:"shadows.outline"}}),aa=t6({container:at,label:(0,a.H2)({lineHeight:1.2,overflow:"visible"}),closeButton:(0,a.H2)({fontSize:"lg",w:"5",h:"5",transitionProperty:"common",transitionDuration:"normal",borderRadius:"full",marginStart:"1.5",marginEnd:"-1",opacity:.5,_disabled:{opacity:.4},_focusVisible:{boxShadow:"outline",bg:"rgba(0, 0, 0, 0.14)"},_hover:{opacity:.8},_active:{opacity:1}})}),ai={sm:t6({container:{[t9.variable]:"sizes.5",[ae.variable]:"sizes.5",[ar.variable]:"fontSizes.xs",[ao.variable]:"space.2"},closeButton:{marginEnd:"-2px",marginStart:"0.35rem"}}),md:t6({container:{[t9.variable]:"sizes.6",[ae.variable]:"sizes.6",[ar.variable]:"fontSizes.sm",[ao.variable]:"space.2"}}),lg:t6({container:{[t9.variable]:"sizes.8",[ae.variable]:"sizes.8",[ar.variable]:"fontSizes.md",[ao.variable]:"space.3"}})},al=t4({variants:{subtle:t6(e=>({container:ed.variants?.subtle(e)})),solid:t6(e=>({container:ed.variants?.solid(e)})),outline:t6(e=>({container:ed.variants?.outline(e)}))},baseStyle:aa,sizes:ai,defaultProps:{size:"md",variant:"subtle",colorScheme:"gray"}}),an=(0,a.H2)({...rW.baseStyle?.field,paddingY:"2",minHeight:"20",lineHeight:"short",verticalAlign:"top"}),as={outline:(0,a.H2)(e=>rW.variants?.outline(e).field??{}),flushed:(0,a.H2)(e=>rW.variants?.flushed(e).field??{}),filled:(0,a.H2)(e=>rW.variants?.filled(e).field??{}),unstyled:rW.variants?.unstyled.field??{}},ad={xs:rW.sizes?.xs.field??{},sm:rW.sizes?.sm.field??{},md:rW.sizes?.md.field??{},lg:rW.sizes?.lg.field??{}},ac=(0,a.Dt)({baseStyle:an,sizes:ad,variants:as,defaultProps:{size:"md",variant:"outline"}}),ab=P("tooltip-bg"),ag=P("tooltip-fg"),ap=P("popper-arrow-bg"),af=(0,a.H2)({bg:ab.reference,color:ag.reference,[ab.variable]:"colors.gray.700",[ag.variable]:"colors.whiteAlpha.900",_dark:{[ab.variable]:"colors.gray.300",[ag.variable]:"colors.gray.900"},[ap.variable]:ab.reference,px:"2",py:"0.5",borderRadius:"sm",fontWeight:"medium",fontSize:"sm",boxShadow:"md",maxW:"xs",zIndex:"tooltip"}),am=(0,a.Dt)({baseStyle:af}),ah={breakpoints:{base:"0em",sm:"30em",md:"48em",lg:"62em",xl:"80em","2xl":"96em"},zIndices:{hide:-1,auto:"auto",base:0,docked:10,dropdown:1e3,sticky:1100,banner:1200,overlay:1300,modal:1400,popover:1500,skipLink:1600,toast:1700,tooltip:1800},radii:{none:"0",sm:"0.125rem",base:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},blur:{none:0,sm:"4px",base:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},colors:{transparent:"transparent",current:"currentColor",black:"#000000",white:"#FFFFFF",whiteAlpha:{50:"rgba(255, 255, 255, 0.04)",100:"rgba(255, 255, 255, 0.06)",200:"rgba(255, 255, 255, 0.08)",300:"rgba(255, 255, 255, 0.16)",400:"rgba(255, 255, 255, 0.24)",500:"rgba(255, 255, 255, 0.36)",600:"rgba(255, 255, 255, 0.48)",700:"rgba(255, 255, 255, 0.64)",800:"rgba(255, 255, 255, 0.80)",900:"rgba(255, 255, 255, 0.92)"},blackAlpha:{50:"rgba(0, 0, 0, 0.04)",100:"rgba(0, 0, 0, 0.06)",200:"rgba(0, 0, 0, 0.08)",300:"rgba(0, 0, 0, 0.16)",400:"rgba(0, 0, 0, 0.24)",500:"rgba(0, 0, 0, 0.36)",600:"rgba(0, 0, 0, 0.48)",700:"rgba(0, 0, 0, 0.64)",800:"rgba(0, 0, 0, 0.80)",900:"rgba(0, 0, 0, 0.92)"},gray:{50:"#F7FAFC",100:"#EDF2F7",200:"#E2E8F0",300:"#CBD5E0",400:"#A0AEC0",500:"#718096",600:"#4A5568",700:"#2D3748",800:"#1A202C",900:"#171923"},red:{50:"#FFF5F5",100:"#FED7D7",200:"#FEB2B2",300:"#FC8181",400:"#F56565",500:"#E53E3E",600:"#C53030",700:"#9B2C2C",800:"#822727",900:"#63171B"},orange:{50:"#FFFAF0",100:"#FEEBC8",200:"#FBD38D",300:"#F6AD55",400:"#ED8936",500:"#DD6B20",600:"#C05621",700:"#9C4221",800:"#7B341E",900:"#652B19"},yellow:{50:"#FFFFF0",100:"#FEFCBF",200:"#FAF089",300:"#F6E05E",400:"#ECC94B",500:"#D69E2E",600:"#B7791F",700:"#975A16",800:"#744210",900:"#5F370E"},green:{50:"#F0FFF4",100:"#C6F6D5",200:"#9AE6B4",300:"#68D391",400:"#48BB78",500:"#38A169",600:"#2F855A",700:"#276749",800:"#22543D",900:"#1C4532"},teal:{50:"#E6FFFA",100:"#B2F5EA",200:"#81E6D9",300:"#4FD1C5",400:"#38B2AC",500:"#319795",600:"#2C7A7B",700:"#285E61",800:"#234E52",900:"#1D4044"},blue:{50:"#ebf8ff",100:"#bee3f8",200:"#90cdf4",300:"#63b3ed",400:"#4299e1",500:"#3182ce",600:"#2b6cb0",700:"#2c5282",800:"#2a4365",900:"#1A365D"},cyan:{50:"#EDFDFD",100:"#C4F1F9",200:"#9DECF9",300:"#76E4F7",400:"#0BC5EA",500:"#00B5D8",600:"#00A3C4",700:"#0987A0",800:"#086F83",900:"#065666"},purple:{50:"#FAF5FF",100:"#E9D8FD",200:"#D6BCFA",300:"#B794F4",400:"#9F7AEA",500:"#805AD5",600:"#6B46C1",700:"#553C9A",800:"#44337A",900:"#322659"},pink:{50:"#FFF5F7",100:"#FED7E2",200:"#FBB6CE",300:"#F687B3",400:"#ED64A6",500:"#D53F8C",600:"#B83280",700:"#97266D",800:"#702459",900:"#521B41"}},...oi,sizes:j,shadows:{xs:"0 0 0 1px rgba(0, 0, 0, 0.05)",sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",base:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",xl:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)","2xl":"0 25px 50px -12px rgba(0, 0, 0, 0.25)",outline:"0 0 0 3px rgba(66, 153, 225, 0.6)",inner:"inset 0 2px 4px 0 rgba(0,0,0,0.06)",none:"none","dark-lg":"rgba(0, 0, 0, 0.1) 0px 0px 0px 1px, rgba(0, 0, 0, 0.2) 0px 5px 10px, rgba(0, 0, 0, 0.4) 0px 15px 40px"},space:T,borders:{none:0,"1px":"1px solid","2px":"2px solid","4px":"4px solid","8px":"8px solid"},transition:{property:{common:"background-color, border-color, color, fill, stroke, opacity, box-shadow, transform",colors:"background-color, border-color, color, fill, stroke",dimensions:"width, height",position:"left, right, top, bottom",background:"background-color, background-image, background-position"},easing:{"ease-in":"cubic-bezier(0.4, 0, 1, 1)","ease-out":"cubic-bezier(0, 0, 0.2, 1)","ease-in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},duration:{"ultra-fast":"50ms",faster:"100ms",fast:"150ms",normal:"200ms",slow:"300ms",slower:"400ms","ultra-slow":"500ms"}}},au={colors:{"chakra-body-text":{_light:"gray.800",_dark:"whiteAlpha.900"},"chakra-body-bg":{_light:"white",_dark:"gray.800"},"chakra-border-color":{_light:"gray.200",_dark:"whiteAlpha.300"},"chakra-inverse-text":{_light:"white",_dark:"gray.800"},"chakra-subtle-bg":{_light:"gray.100",_dark:"gray.700"},"chakra-subtle-text":{_light:"gray.600",_dark:"gray.400"},"chakra-placeholder-color":{_light:"gray.500",_dark:"whiteAlpha.400"}}},ax={global:{body:{fontFamily:"body",color:"chakra-body-text",bg:"chakra-body-bg",transitionProperty:"background-color",transitionDuration:"normal",lineHeight:"base"},"*::placeholder":{color:"chakra-placeholder-color"},"*, *::before, &::after":{borderColor:"chakra-border-color"}}},ay=["borders","breakpoints","colors","components","config","direction","fonts","fontSizes","fontWeights","letterSpacings","lineHeights","radii","shadows","sizes","space","styles","transition","zIndices"];function av(e){return!!(0,S.Gv)(e)&&ay.every(r=>Object.prototype.hasOwnProperty.call(e,r))}let aS={useSystemColorMode:!1,initialColorMode:"light",cssVarPrefix:"chakra"},az={semanticTokens:au,direction:"ltr",...ah,components:{Accordion:d,Alert:Y,Avatar:et,Badge:ed,Breadcrumb:ep,Button:ez,Checkbox:eY,CloseButton:eN,Code:eK,Container:eJ,Divider:e1,Drawer:ri,Editable:rd,Form:rp,FormError:ru,FormLabel:ry,Heading:rz,Input:rW,Kbd:rI,Link:rY,List:rL,Menu:r2,Modal:oa,NumberInput:oy,PinInput:ow,Popover:oB,Progress:oY,Radio:oO,Select:oX,Skeleton:o2,SkipLink:o4,Slider:tn,Spinner:tb,Stat:th,Switch:tE,Table:tU,Tabs:t5,Tag:al,Textarea:ac,Tooltip:am,Card:eB,Stepper:tk},styles:ax,config:aS},aw={semanticTokens:au,direction:"ltr",components:{},...ah,styles:ax,config:aS}}};