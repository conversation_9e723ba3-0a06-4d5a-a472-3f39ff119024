"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-f15ba28f"],{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js":
/*!*****************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js ***!
  \*****************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    EDITOR_LINK_STYLES: function() {\n        return EDITOR_LINK_STYLES;\n    },\n    EditorLink: function() {\n        return EditorLink;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _useopenineditor = __webpack_require__(/*! ../../utils/use-open-in-editor */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js\");\nfunction EditorLink(param) {\n    let { file, location } = param;\n    var _location_line, _location_column;\n    const open = (0, _useopenineditor.useOpenInEditor)({\n        file,\n        lineNumber: (_location_line = location == null ? void 0 : location.line) != null ? _location_line : 1,\n        column: (_location_column = location == null ? void 0 : location.column) != null ? _location_column : 0\n    });\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-with-open-in-editor-link\": true,\n        \"data-with-open-in-editor-link-import-trace\": true,\n        tabIndex: 10,\n        role: 'link',\n        onClick: open,\n        title: 'Click to open in your editor',\n        children: [\n            file,\n            location ? \":\" + location.line + \":\" + location.column : null,\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"polyline\", {\n                        points: \"15 3 21 3 21 9\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"line\", {\n                        x1: \"10\",\n                        y1: \"14\",\n                        x2: \"21\",\n                        y2: \"3\"\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c = EditorLink;\nconst EDITOR_LINK_STYLES = \"\\n  [data-with-open-in-editor-link] svg {\\n    width: auto;\\n    height: var(--size-14);\\n    margin-left: 8px;\\n  }\\n  [data-with-open-in-editor-link] {\\n    cursor: pointer;\\n  }\\n  [data-with-open-in-editor-link]:hover {\\n    text-decoration: underline dotted;\\n  }\\n  [data-with-open-in-editor-link-import-trace] {\\n    margin-left: 16px;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=editor-link.js.map\nvar _c;\n$RefreshReg$(_c, \"EditorLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/index.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/index.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Terminal\", ({\n    enumerable: true,\n    get: function() {\n        return _terminal.Terminal;\n    }\n}));\nconst _terminal = __webpack_require__(/*! ./terminal */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjVfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L3VpL2NvbXBvbmVudHMvdGVybWluYWwvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs0Q0FBU0E7OztlQUFBQSxVQUFBQSxRQUFROzs7c0NBQVEiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHVpXFxjb21wb25lbnRzXFx0ZXJtaW5hbFxcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IFRlcm1pbmFsIH0gZnJvbSAnLi90ZXJtaW5hbCdcbiJdLCJuYW1lcyI6WyJUZXJtaW5hbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    TERMINAL_STYLES: function() {\n        return TERMINAL_STYLES;\n    },\n    Terminal: function() {\n        return Terminal;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _anser = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/anser */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/anser/index.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\"));\nconst _hotlinkedtext = __webpack_require__(/*! ../hot-linked-text */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js\");\nconst _editorlink = __webpack_require__(/*! ./editor-link */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/editor-link.js\");\nconst _external = __webpack_require__(/*! ../../icons/external */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/external.js\");\nconst _stackframe = __webpack_require__(/*! ../../../utils/stack-frame */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/utils/stack-frame.js\");\nconst _useopenineditor = __webpack_require__(/*! ../../utils/use-open-in-editor */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/use-open-in-editor.js\");\nconst _file = __webpack_require__(/*! ../../icons/file */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/file.js\");\nfunction getFile(lines) {\n    const contentFileName = lines.shift();\n    if (!contentFileName) return null;\n    const [fileName, line, column] = contentFileName.split(':', 3);\n    const parsedLine = Number(line);\n    const parsedColumn = Number(column);\n    const hasLocation = !Number.isNaN(parsedLine) && !Number.isNaN(parsedColumn);\n    return {\n        fileName: hasLocation ? fileName : contentFileName,\n        location: hasLocation ? {\n            line: parsedLine,\n            column: parsedColumn\n        } : undefined\n    };\n}\nfunction getImportTraceFiles(lines) {\n    if (lines.some((line)=>/ReactServerComponentsError:/.test(line)) || lines.some((line)=>/Import trace for requested module:/.test(line))) {\n        // Grab the lines at the end containing the files\n        const files = [];\n        while(/.+\\..+/.test(lines[lines.length - 1]) && !lines[lines.length - 1].includes(':')){\n            const file = lines.pop().trim();\n            files.unshift(file);\n        }\n        return files;\n    }\n    return [];\n}\nfunction getEditorLinks(content) {\n    const lines = content.split('\\n');\n    const file = getFile(lines);\n    const importTraceFiles = getImportTraceFiles(lines);\n    return {\n        file,\n        source: lines.join('\\n'),\n        importTraceFiles\n    };\n}\nconst Terminal = function Terminal(param) {\n    _s();\n    let { content } = param;\n    var _file_location, _file_location1, _file_location2, _file_location3, _stackFrame_file;\n    const { file, source, importTraceFiles } = _react.useMemo({\n        \"Terminal.useMemo\": ()=>getEditorLinks(content)\n    }[\"Terminal.useMemo\"], [\n        content\n    ]);\n    const decoded = _react.useMemo({\n        \"Terminal.useMemo[decoded]\": ()=>{\n            return _anser.default.ansiToJson(source, {\n                json: true,\n                use_classes: true,\n                remove_empty: true\n            });\n        }\n    }[\"Terminal.useMemo[decoded]\"], [\n        source\n    ]);\n    var _file_location_line, _file_location_column;\n    const open = (0, _useopenineditor.useOpenInEditor)({\n        file: file == null ? void 0 : file.fileName,\n        lineNumber: (_file_location_line = file == null ? void 0 : (_file_location = file.location) == null ? void 0 : _file_location.line) != null ? _file_location_line : 1,\n        column: (_file_location_column = file == null ? void 0 : (_file_location1 = file.location) == null ? void 0 : _file_location1.column) != null ? _file_location_column : 0\n    });\n    var _file_fileName, _file_location_line1, _file_location_column1;\n    const stackFrame = {\n        file: (_file_fileName = file == null ? void 0 : file.fileName) != null ? _file_fileName : null,\n        methodName: '',\n        arguments: [],\n        lineNumber: (_file_location_line1 = file == null ? void 0 : (_file_location2 = file.location) == null ? void 0 : _file_location2.line) != null ? _file_location_line1 : null,\n        column: (_file_location_column1 = file == null ? void 0 : (_file_location3 = file.location) == null ? void 0 : _file_location3.column) != null ? _file_location_column1 : null\n    };\n    const fileExtension = stackFrame == null ? void 0 : (_stackFrame_file = stackFrame.file) == null ? void 0 : _stackFrame_file.split('.').pop();\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-codeframe\": true,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                className: \"code-frame-header\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"code-frame-link\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            className: \"code-frame-icon\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_file.FileIcon, {\n                                lang: fileExtension\n                            })\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-text\": true,\n                            children: (0, _stackframe.getFrameSource)(stackFrame)\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                            \"aria-label\": \"Open in editor\",\n                            \"data-with-open-in-editor-link-source-file\": true,\n                            onClick: open,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                className: \"code-frame-icon\",\n                                \"data-icon\": \"right\",\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_external.ExternalIcon, {\n                                    width: 16,\n                                    height: 16\n                                })\n                            })\n                        })\n                    ]\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"pre\", {\n                className: \"code-frame-pre\",\n                children: [\n                    decoded.map((entry, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            style: {\n                                color: entry.fg ? \"var(--color-\" + entry.fg + \")\" : undefined,\n                                ...entry.decoration === 'bold' ? // above 600, hence a temporary fix is to use 500 for bold.\n                                {\n                                    fontWeight: 500\n                                } : entry.decoration === 'italic' ? {\n                                    fontStyle: 'italic'\n                                } : undefined\n                            },\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                                text: entry.content\n                            })\n                        }, \"terminal-entry-\" + index)),\n                    importTraceFiles.map((importTraceFile)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_editorlink.EditorLink, {\n                            isSourceFile: false,\n                            file: importTraceFile\n                        }, importTraceFile))\n                ]\n            })\n        ]\n    });\n};\n_s(Terminal, \"nkmao/TIox3Jie/+6JvWO3hTKPQ=\");\n_c = Terminal;\nconst TERMINAL_STYLES = \"\\n  [data-nextjs-terminal]::selection,\\n  [data-nextjs-terminal] *::selection {\\n    background-color: var(--color-ansi-selection);\\n  }\\n\\n  [data-nextjs-terminal] * {\\n    color: inherit;\\n    background-color: transparent;\\n    font-family: var(--font-stack-monospace);\\n  }\\n\\n  [data-nextjs-terminal] > div > p {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    cursor: pointer;\\n    margin: 0;\\n  }\\n  [data-nextjs-terminal] > div > p:hover {\\n    text-decoration: underline dotted;\\n  }\\n  [data-nextjs-terminal] div > pre {\\n    overflow: hidden;\\n    display: inline-block;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=terminal.js.map\nvar _c;\n$RefreshReg$(_c, \"Terminal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/terminal.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js":
/*!********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js ***!
  \********************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Toast: function() {\n        return _toast.Toast;\n    },\n    styles: function() {\n        return _styles.styles;\n    }\n});\nconst _styles = __webpack_require__(/*! ./styles */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/styles.js\");\nconst _toast = __webpack_require__(/*! ./toast */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/toast.js\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjVfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L3VpL2NvbXBvbmVudHMvdG9hc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBQ1NBLEtBQUs7ZUFBTEEsT0FBQUEsS0FBSzs7SUFETEMsTUFBTTtlQUFOQSxRQUFBQSxNQUFNOzs7b0NBQVE7bUNBQ0QiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHVpXFxjb21wb25lbnRzXFx0b2FzdFxcaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IHN0eWxlcyB9IGZyb20gJy4vc3R5bGVzJ1xuZXhwb3J0IHsgVG9hc3QgfSBmcm9tICcuL3RvYXN0J1xuIl0sIm5hbWVzIjpbIlRvYXN0Iiwic3R5bGVzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/styles.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/styles.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst styles = \"\\n  .nextjs-toast {\\n    position: fixed;\\n    bottom: 16px;\\n    left: 16px;\\n    max-width: 420px;\\n    z-index: 9000;\\n    box-shadow: 0px 16px 32px\\n      rgba(0, 0, 0, 0.25);\\n  }\\n\\n  @media (max-width: 440px) {\\n    .nextjs-toast {\\n      max-width: 90vw;\\n      left: 5vw;\\n    }\\n  }\\n\\n  .nextjs-toast-errors-parent {\\n    padding: 16px;\\n    border-radius: var(--rounded-4xl);\\n    font-weight: 500;\\n    color: var(--color-ansi-bright-white);\\n    background-color: var(--color-ansi-red);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjVfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L3VpL2NvbXBvbmVudHMvdG9hc3Qvc3R5bGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7MENBMkJTQTs7O2VBQUFBOzs7QUEzQlQsTUFBTUEsU0FBVSIsInNvdXJjZXMiOlsiRDpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXHRvYXN0XFxzdHlsZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc3R5bGVzID0gYFxuICAubmV4dGpzLXRvYXN0IHtcbiAgICBwb3NpdGlvbjogZml4ZWQ7XG4gICAgYm90dG9tOiAxNnB4O1xuICAgIGxlZnQ6IDE2cHg7XG4gICAgbWF4LXdpZHRoOiA0MjBweDtcbiAgICB6LWluZGV4OiA5MDAwO1xuICAgIGJveC1zaGFkb3c6IDBweCAxNnB4IDMycHhcbiAgICAgIHJnYmEoMCwgMCwgMCwgMC4yNSk7XG4gIH1cblxuICBAbWVkaWEgKG1heC13aWR0aDogNDQwcHgpIHtcbiAgICAubmV4dGpzLXRvYXN0IHtcbiAgICAgIG1heC13aWR0aDogOTB2dztcbiAgICAgIGxlZnQ6IDV2dztcbiAgICB9XG4gIH1cblxuICAubmV4dGpzLXRvYXN0LWVycm9ycy1wYXJlbnQge1xuICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgYm9yZGVyLXJhZGl1czogdmFyKC0tcm91bmRlZC00eGwpO1xuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgY29sb3I6IHZhcigtLWNvbG9yLWFuc2ktYnJpZ2h0LXdoaXRlKTtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1jb2xvci1hbnNpLXJlZCk7XG4gIH1cbmBcblxuZXhwb3J0IHsgc3R5bGVzIH1cbiJdLCJuYW1lcyI6WyJzdHlsZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/styles.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/toast.js":
/*!********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/toast.js ***!
  \********************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Toast\", ({\n    enumerable: true,\n    get: function() {\n        return Toast;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\"));\nconst _cx = __webpack_require__(/*! ../../utils/cx */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js\");\nconst Toast = function Toast(param) {\n    let { onClick, children, className, ...props } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        ...props,\n        onClick: (e)=>{\n            if (!e.target.closest('a')) {\n                e.preventDefault();\n            }\n            return onClick == null ? void 0 : onClick();\n        },\n        className: (0, _cx.cx)('nextjs-toast', className),\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n            \"data-nextjs-toast-wrapper\": true,\n            children: children\n        })\n    });\n};\n_c = Toast;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=toast.js.map\nvar _c;\n$RefreshReg$(_c, \"Toast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjVfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L3VpL2NvbXBvbmVudHMvdG9hc3QvdG9hc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozt5Q0FRYUE7OztlQUFBQTs7Ozs7NkVBUlU7Z0NBQ0o7QUFPWixjQUFvQyxTQUFTQSxNQUFNLEtBS3pEO0lBTHlELE1BQ3hEQyxPQUFPLEVBQ1BDLFFBQVEsRUFDUkMsU0FBUyxFQUNULEdBQUdDLE9BQ0osR0FMeUQ7SUFNeEQscUJBQ0UscUJBQUNDLE9BQUFBO1FBQ0UsR0FBR0QsS0FBSztRQUNUSCxTQUFTLENBQUNLO1lBQ1IsSUFBSSxDQUFFQSxFQUFFQyxNQUFNLENBQWlCQyxPQUFPLENBQUMsTUFBTTtnQkFDM0NGLEVBQUVHLGNBQWM7WUFDbEI7WUFDQSxPQUFPUixXQUFBQSxPQUFBQSxLQUFBQSxJQUFBQTtRQUNUO1FBQ0FFLFdBQVdPLENBQUFBLEdBQUFBLElBQUFBLEVBQUFBLEVBQUcsZ0JBQWdCUDtrQkFFOUIsbUNBQUNFLE9BQUFBO1lBQUlNLDJCQUF5QjtzQkFBRVQ7OztBQUd0QztLQXBCYUYiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHVpXFxjb21wb25lbnRzXFx0b2FzdFxcdG9hc3QudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgY3ggfSBmcm9tICcuLi8uLi91dGlscy9jeCdcbmV4cG9ydCB0eXBlIFRvYXN0UHJvcHMgPSBSZWFjdC5IVE1MUHJvcHM8SFRNTERpdkVsZW1lbnQ+ICYge1xuICBjaGlsZHJlbj86IFJlYWN0LlJlYWN0Tm9kZVxuICBvbkNsaWNrPzogKCkgPT4gdm9pZFxuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGNvbnN0IFRvYXN0OiBSZWFjdC5GQzxUb2FzdFByb3BzPiA9IGZ1bmN0aW9uIFRvYXN0KHtcbiAgb25DbGljayxcbiAgY2hpbGRyZW4sXG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICB7Li4ucHJvcHN9XG4gICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICBpZiAoIShlLnRhcmdldCBhcyBIVE1MRWxlbWVudCkuY2xvc2VzdCgnYScpKSB7XG4gICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG9uQ2xpY2s/LigpXG4gICAgICB9fVxuICAgICAgY2xhc3NOYW1lPXtjeCgnbmV4dGpzLXRvYXN0JywgY2xhc3NOYW1lKX1cbiAgICA+XG4gICAgICA8ZGl2IGRhdGEtbmV4dGpzLXRvYXN0LXdyYXBwZXI+e2NoaWxkcmVufTwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiVG9hc3QiLCJvbkNsaWNrIiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJwcm9wcyIsImRpdiIsImUiLCJ0YXJnZXQiLCJjbG9zZXN0IiwicHJldmVudERlZmF1bHQiLCJjeCIsImRhdGEtbmV4dGpzLXRvYXN0LXdyYXBwZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/toast.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js":
/*!******************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js ***!
  \******************************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    VersionStalenessInfo: function() {\n        return VersionStalenessInfo;\n    },\n    getStaleness: function() {\n        return getStaleness;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _cx = __webpack_require__(/*! ../../utils/cx */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/cx.js\");\nfunction VersionStalenessInfo(param) {\n    let { versionInfo, bundlerName } = param;\n    const { staleness } = versionInfo;\n    let { text, indicatorClass, title } = getStaleness(versionInfo);\n    const isTurbopack = bundlerName === 'Turbopack';\n    const shouldBeLink = staleness.startsWith('stale');\n    if (shouldBeLink) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"a\", {\n            className: \"nextjs-container-build-error-version-status dialog-exclude-closing-from-outside-click\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            href: \"https://nextjs.org/docs/messages/version-staleness\",\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Eclipse, {\n                    className: (0, _cx.cx)('version-staleness-indicator', indicatorClass)\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                    \"data-nextjs-version-checker\": true,\n                    title: title,\n                    children: text\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                    className: (0, _cx.cx)(isTurbopack && 'turbopack-text'),\n                    children: bundlerName\n                })\n            ]\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n        className: \"nextjs-container-build-error-version-status dialog-exclude-closing-from-outside-click\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Eclipse, {\n                className: (0, _cx.cx)('version-staleness-indicator', indicatorClass)\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                \"data-nextjs-version-checker\": true,\n                title: title,\n                children: text\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: (0, _cx.cx)(isTurbopack && 'turbopack-text'),\n                children: bundlerName\n            })\n        ]\n    });\n}\n_c = VersionStalenessInfo;\nfunction getStaleness(param) {\n    let { installed, staleness, expected } = param;\n    let text = '';\n    let title = '';\n    let indicatorClass = '';\n    const versionLabel = \"Next.js \" + installed;\n    switch(staleness){\n        case 'newer-than-npm':\n        case 'fresh':\n            text = versionLabel;\n            title = \"Latest available version is detected (\" + installed + \").\";\n            indicatorClass = 'fresh';\n            break;\n        case 'stale-patch':\n        case 'stale-minor':\n            text = \"\" + versionLabel + \" (stale)\";\n            title = \"There is a newer version (\" + expected + \") available, upgrade recommended! \";\n            indicatorClass = 'stale';\n            break;\n        case 'stale-major':\n            {\n                text = \"\" + versionLabel + \" (outdated)\";\n                title = \"An outdated version detected (latest is \" + expected + \"), upgrade is highly recommended!\";\n                indicatorClass = 'outdated';\n                break;\n            }\n        case 'stale-prerelease':\n            {\n                text = \"\" + versionLabel + \" (stale)\";\n                title = \"There is a newer canary version (\" + expected + \") available, please upgrade! \";\n                indicatorClass = 'stale';\n                break;\n            }\n        case 'unknown':\n            text = \"\" + versionLabel + \" (unknown)\";\n            title = 'No Next.js version data was found.';\n            indicatorClass = 'unknown';\n            break;\n        default:\n            break;\n    }\n    return {\n        text,\n        indicatorClass,\n        title\n    };\n}\nconst styles = \"\\n  .nextjs-container-build-error-version-status {\\n    -webkit-font-smoothing: antialiased;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    gap: 4px;\\n\\n    height: var(--size-26);\\n    padding: 6px 8px 6px 6px;\\n    background: var(--color-background-100);\\n    background-clip: padding-box;\\n    border: 1px solid var(--color-gray-alpha-400);\\n    box-shadow: var(--shadow-small);\\n    border-radius: var(--rounded-full);\\n\\n    color: var(--color-gray-900);\\n    font-size: var(--size-12);\\n    font-weight: 500;\\n    line-height: var(--size-16);\\n  }\\n\\n  a.nextjs-container-build-error-version-status {\\n    text-decoration: none;\\n    color: var(--color-gray-900);\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n\\n    &:focus {\\n      outline: var(--focus-ring);\\n    }\\n  }\\n\\n  .version-staleness-indicator.fresh {\\n    fill: var(--color-green-800);\\n    stroke: var(--color-green-300);\\n  }\\n  .version-staleness-indicator.stale {\\n    fill: var(--color-amber-800);\\n    stroke: var(--color-amber-300);\\n  }\\n  .version-staleness-indicator.outdated {\\n    fill: var(--color-red-800);\\n    stroke: var(--color-red-300);\\n  }\\n  .version-staleness-indicator.unknown {\\n    fill: var(--color-gray-800);\\n    stroke: var(--color-gray-300);\\n  }\\n\\n  .nextjs-container-build-error-version-status > .turbopack-text {\\n    background: linear-gradient(\\n      to right,\\n      var(--color-turbopack-text-red) 0%,\\n      var(--color-turbopack-text-blue) 100%\\n    );\\n    background-clip: text;\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n  }\\n\";\nfunction Eclipse(param) {\n    let { className } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"14\",\n        height: \"14\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"circle\", {\n            className: className,\n            cx: \"7\",\n            cy: \"7\",\n            r: \"5.5\",\n            strokeWidth: \"3\"\n        })\n    });\n}\n_c1 = Eclipse;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=version-staleness-info.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"VersionStalenessInfo\");\n$RefreshReg$(_c1, \"Eclipse\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.js\n"));

/***/ })

}]);