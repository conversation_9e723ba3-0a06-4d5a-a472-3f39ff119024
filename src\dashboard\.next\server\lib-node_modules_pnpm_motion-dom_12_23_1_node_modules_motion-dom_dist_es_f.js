"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f";
exports.ids = ["lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/batcher.mjs":
/*!*********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/batcher.mjs ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderBatcher: () => (/* binding */ createRenderBatcher)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _order_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./order.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/order.mjs\");\n/* harmony import */ var _render_step_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./render-step.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/render-step.mjs\");\n\n\n\n\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    const flagRunNextFrame = () => (runNextFrame = true);\n    const steps = _order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder.reduce((acc, key) => {\n        acc[key] = (0,_render_step_mjs__WEBPACK_IMPORTED_MODULE_1__.createRenderStep)(flagRunNextFrame, allowKeepAlive ? key : undefined);\n        return acc;\n    }, {});\n    const { setup, read, resolveKeyframes, preUpdate, update, preRender, render, postRender, } = steps;\n    const processBatch = () => {\n        const timestamp = motion_utils__WEBPACK_IMPORTED_MODULE_2__.MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        if (!motion_utils__WEBPACK_IMPORTED_MODULE_2__.MotionGlobalConfig.useManualTiming) {\n            state.delta = useDefaultElapsed\n                ? 1000 / 60\n                : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        }\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        // Unrolled render loop for better per-frame performance\n        setup.process(state);\n        read.process(state);\n        resolveKeyframes.process(state);\n        preUpdate.process(state);\n        update.process(state);\n        preRender.process(state);\n        render.process(state);\n        postRender.process(state);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = _order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => {\n        for (let i = 0; i < _order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder.length; i++) {\n            steps[_order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder[i]].cancel(process);\n        }\n    };\n    return { schedule, cancel, state, steps };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/batcher.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/frame.mjs":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/frame.mjs ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelFrame: () => (/* binding */ cancelFrame),\n/* harmony export */   frame: () => (/* binding */ frame),\n/* harmony export */   frameData: () => (/* binding */ frameData),\n/* harmony export */   frameSteps: () => (/* binding */ frameSteps)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _batcher_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./batcher.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/batcher.mjs\");\n\n\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps: frameSteps, } = /* @__PURE__ */ (0,_batcher_mjs__WEBPACK_IMPORTED_MODULE_0__.createRenderBatcher)(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop, true);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbW90aW9uLWRvbUAxMi4yMy4xL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZnJhbWVsb29wL2ZyYW1lLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBb0M7QUFDZ0I7O0FBRXBELFFBQVEsNkVBQTZFLGtCQUFrQixpRUFBbUIsd0VBQXdFLDhDQUFJOztBQUVqSiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbW90aW9uLWRvbUAxMi4yMy4xXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxmcmFtZWxvb3BcXGZyYW1lLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBub29wIH0gZnJvbSAnbW90aW9uLXV0aWxzJztcbmltcG9ydCB7IGNyZWF0ZVJlbmRlckJhdGNoZXIgfSBmcm9tICcuL2JhdGNoZXIubWpzJztcblxuY29uc3QgeyBzY2hlZHVsZTogZnJhbWUsIGNhbmNlbDogY2FuY2VsRnJhbWUsIHN0YXRlOiBmcmFtZURhdGEsIHN0ZXBzOiBmcmFtZVN0ZXBzLCB9ID0gLyogQF9fUFVSRV9fICovIGNyZWF0ZVJlbmRlckJhdGNoZXIodHlwZW9mIHJlcXVlc3RBbmltYXRpb25GcmFtZSAhPT0gXCJ1bmRlZmluZWRcIiA/IHJlcXVlc3RBbmltYXRpb25GcmFtZSA6IG5vb3AsIHRydWUpO1xuXG5leHBvcnQgeyBjYW5jZWxGcmFtZSwgZnJhbWUsIGZyYW1lRGF0YSwgZnJhbWVTdGVwcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/frame.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/index-legacy.mjs":
/*!**************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/index-legacy.mjs ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelSync: () => (/* binding */ cancelSync),\n/* harmony export */   sync: () => (/* binding */ sync)\n/* harmony export */ });\n/* harmony import */ var _order_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./order.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/order.mjs\");\n/* harmony import */ var _frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./frame.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\n\n/**\n * @deprecated\n *\n * Import as `frame` instead.\n */\nconst sync = _frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame;\n/**\n * @deprecated\n *\n * Use cancelFrame(callback) instead.\n */\nconst cancelSync = _order_mjs__WEBPACK_IMPORTED_MODULE_1__.stepsOrder.reduce((acc, key) => {\n    acc[key] = (process) => (0,_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(process);\n    return acc;\n}, {});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbW90aW9uLWRvbUAxMi4yMy4xL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZnJhbWVsb29wL2luZGV4LWxlZ2FjeS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF5QztBQUNROztBQUVqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSw2Q0FBSztBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGtEQUFVO0FBQzdCLDRCQUE0Qix1REFBVztBQUN2QztBQUNBLENBQUMsSUFBSTs7QUFFdUIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG1vdGlvbi1kb21AMTIuMjMuMVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZnJhbWVsb29wXFxpbmRleC1sZWdhY3kubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN0ZXBzT3JkZXIgfSBmcm9tICcuL29yZGVyLm1qcyc7XG5pbXBvcnQgeyBmcmFtZSwgY2FuY2VsRnJhbWUgfSBmcm9tICcuL2ZyYW1lLm1qcyc7XG5cbi8qKlxuICogQGRlcHJlY2F0ZWRcbiAqXG4gKiBJbXBvcnQgYXMgYGZyYW1lYCBpbnN0ZWFkLlxuICovXG5jb25zdCBzeW5jID0gZnJhbWU7XG4vKipcbiAqIEBkZXByZWNhdGVkXG4gKlxuICogVXNlIGNhbmNlbEZyYW1lKGNhbGxiYWNrKSBpbnN0ZWFkLlxuICovXG5jb25zdCBjYW5jZWxTeW5jID0gc3RlcHNPcmRlci5yZWR1Y2UoKGFjYywga2V5KSA9PiB7XG4gICAgYWNjW2tleV0gPSAocHJvY2VzcykgPT4gY2FuY2VsRnJhbWUocHJvY2Vzcyk7XG4gICAgcmV0dXJuIGFjYztcbn0sIHt9KTtcblxuZXhwb3J0IHsgY2FuY2VsU3luYywgc3luYyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/index-legacy.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/microtask.mjs":
/*!***********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/microtask.mjs ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelMicrotask: () => (/* binding */ cancelMicrotask),\n/* harmony export */   microtask: () => (/* binding */ microtask)\n/* harmony export */ });\n/* harmony import */ var _batcher_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./batcher.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/batcher.mjs\");\n\n\nconst { schedule: microtask, cancel: cancelMicrotask } = \n/* @__PURE__ */ (0,_batcher_mjs__WEBPACK_IMPORTED_MODULE_0__.createRenderBatcher)(queueMicrotask, false);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbW90aW9uLWRvbUAxMi4yMy4xL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZnJhbWVsb29wL21pY3JvdGFzay5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9EOztBQUVwRCxRQUFRLCtDQUErQztBQUN2RCxnQkFBZ0IsaUVBQW1COztBQUVHIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxtb3Rpb24tZG9tQDEyLjIzLjFcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGZyYW1lbG9vcFxcbWljcm90YXNrLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVSZW5kZXJCYXRjaGVyIH0gZnJvbSAnLi9iYXRjaGVyLm1qcyc7XG5cbmNvbnN0IHsgc2NoZWR1bGU6IG1pY3JvdGFzaywgY2FuY2VsOiBjYW5jZWxNaWNyb3Rhc2sgfSA9IFxuLyogQF9fUFVSRV9fICovIGNyZWF0ZVJlbmRlckJhdGNoZXIocXVldWVNaWNyb3Rhc2ssIGZhbHNlKTtcblxuZXhwb3J0IHsgY2FuY2VsTWljcm90YXNrLCBtaWNyb3Rhc2sgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/microtask.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/order.mjs":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/order.mjs ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stepsOrder: () => (/* binding */ stepsOrder)\n/* harmony export */ });\nconst stepsOrder = [\n    \"setup\", // Compute\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"preUpdate\", // Compute\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbW90aW9uLWRvbUAxMi4yMy4xL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZnJhbWVsb29wL29yZGVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxtb3Rpb24tZG9tQDEyLjIzLjFcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGZyYW1lbG9vcFxcb3JkZXIubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHN0ZXBzT3JkZXIgPSBbXG4gICAgXCJzZXR1cFwiLCAvLyBDb21wdXRlXG4gICAgXCJyZWFkXCIsIC8vIFJlYWRcbiAgICBcInJlc29sdmVLZXlmcmFtZXNcIiwgLy8gV3JpdGUvUmVhZC9Xcml0ZS9SZWFkXG4gICAgXCJwcmVVcGRhdGVcIiwgLy8gQ29tcHV0ZVxuICAgIFwidXBkYXRlXCIsIC8vIENvbXB1dGVcbiAgICBcInByZVJlbmRlclwiLCAvLyBDb21wdXRlXG4gICAgXCJyZW5kZXJcIiwgLy8gV3JpdGVcbiAgICBcInBvc3RSZW5kZXJcIiwgLy8gQ29tcHV0ZVxuXTtcblxuZXhwb3J0IHsgc3RlcHNPcmRlciB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/order.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/render-step.mjs":
/*!*************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/render-step.mjs ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderStep: () => (/* binding */ createRenderStep)\n/* harmony export */ });\n/* harmony import */ var _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stats/buffer.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/stats/buffer.mjs\");\n\n\nfunction createRenderStep(runNextFrame, stepName) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Set();\n    let nextFrame = new Set();\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    let latestFrameData = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    let numCalls = 0;\n    function triggerCallback(callback) {\n        if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n        }\n        numCalls++;\n        callback(latestFrameData);\n    }\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (!queue.has(callback))\n                queue.add(callback);\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.delete(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            latestFrameData = frameData;\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Execute this frame\n            thisFrame.forEach(triggerCallback);\n            /**\n             * If we're recording stats then\n             */\n            if (stepName && _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__.statsBuffer.value) {\n                _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__.statsBuffer.value.frameloop[stepName].push(numCalls);\n            }\n            numCalls = 0;\n            // Clear the frame so no callbacks remain. This is to avoid\n            // memory leaks should this render step not run for a while.\n            thisFrame.clear();\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/render-step.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs":
/*!***********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   time: () => (/* binding */ time)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./frame.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\n\nlet now;\nfunction clearTime() {\n    now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n    now: () => {\n        if (now === undefined) {\n            time.set(_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.isProcessing || motion_utils__WEBPACK_IMPORTED_MODULE_1__.MotionGlobalConfig.useManualTiming\n                ? _frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.timestamp\n                : performance.now());\n        }\n        return now;\n    },\n    set: (newTime) => {\n        now = newTime;\n        queueMicrotask(clearTime);\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDragActive: () => (/* binding */ isDragActive),\n/* harmony export */   isDragging: () => (/* binding */ isDragging)\n/* harmony export */ });\nconst isDragging = {\n    x: false,\n    y: false,\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbW90aW9uLWRvbUAxMi4yMy4xL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvZHJhZy9zdGF0ZS9pcy1hY3RpdmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW9DIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxtb3Rpb24tZG9tQDEyLjIzLjFcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFxkcmFnXFxzdGF0ZVxcaXMtYWN0aXZlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc0RyYWdnaW5nID0ge1xuICAgIHg6IGZhbHNlLFxuICAgIHk6IGZhbHNlLFxufTtcbmZ1bmN0aW9uIGlzRHJhZ0FjdGl2ZSgpIHtcbiAgICByZXR1cm4gaXNEcmFnZ2luZy54IHx8IGlzRHJhZ2dpbmcueTtcbn1cblxuZXhwb3J0IHsgaXNEcmFnQWN0aXZlLCBpc0RyYWdnaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setDragLock: () => (/* binding */ setDragLock)\n/* harmony export */ });\n/* harmony import */ var _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-active.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis]) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = false;\n            };\n        }\n    }\n    else {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x || _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = false;\n            };\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbW90aW9uLWRvbUAxMi4yMy4xL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvZHJhZy9zdGF0ZS9zZXQtYWN0aXZlLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2Qzs7QUFFN0M7QUFDQTtBQUNBLFlBQVksc0RBQVU7QUFDdEI7QUFDQTtBQUNBO0FBQ0EsWUFBWSxzREFBVTtBQUN0QjtBQUNBLGdCQUFnQixzREFBVTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksc0RBQVUsTUFBTSxzREFBVTtBQUN0QztBQUNBO0FBQ0E7QUFDQSxZQUFZLHNEQUFVLEtBQUssc0RBQVU7QUFDckM7QUFDQSxnQkFBZ0Isc0RBQVUsS0FBSyxzREFBVTtBQUN6QztBQUNBO0FBQ0E7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG1vdGlvbi1kb21AMTIuMjMuMVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZ2VzdHVyZXNcXGRyYWdcXHN0YXRlXFxzZXQtYWN0aXZlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0RyYWdnaW5nIH0gZnJvbSAnLi9pcy1hY3RpdmUubWpzJztcblxuZnVuY3Rpb24gc2V0RHJhZ0xvY2soYXhpcykge1xuICAgIGlmIChheGlzID09PSBcInhcIiB8fCBheGlzID09PSBcInlcIikge1xuICAgICAgICBpZiAoaXNEcmFnZ2luZ1theGlzXSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpc0RyYWdnaW5nW2F4aXNdID0gdHJ1ZTtcbiAgICAgICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgaXNEcmFnZ2luZ1theGlzXSA9IGZhbHNlO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgaWYgKGlzRHJhZ2dpbmcueCB8fCBpc0RyYWdnaW5nLnkpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaXNEcmFnZ2luZy54ID0gaXNEcmFnZ2luZy55ID0gdHJ1ZTtcbiAgICAgICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgaXNEcmFnZ2luZy54ID0gaXNEcmFnZ2luZy55ID0gZmFsc2U7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfVxufVxuXG5leHBvcnQgeyBzZXREcmFnTG9jayB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/hover.mjs":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/hover.mjs ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hover: () => (/* binding */ hover)\n/* harmony export */ });\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./drag/state/is-active.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/setup.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n\n\n\nfunction isValidHover(event) {\n    return !(event.pointerType === \"touch\" || (0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragActive)());\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n    const [elements, eventOptions, cancel] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__.setupGesture)(elementOrSelector, options);\n    const onPointerEnter = (enterEvent) => {\n        if (!isValidHover(enterEvent))\n            return;\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(target, enterEvent);\n        if (typeof onHoverEnd !== \"function\" || !target)\n            return;\n        const onPointerLeave = (leaveEvent) => {\n            if (!isValidHover(leaveEvent))\n                return;\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        };\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    };\n    elements.forEach((element) => {\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return cancel;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/hover.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/index.mjs":
/*!************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/index.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   press: () => (/* binding */ press)\n/* harmony export */ });\n/* harmony import */ var _utils_is_html_element_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/is-html-element.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/utils/is-html-element.mjs\");\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../drag/state/is-active.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/is-node-or-child.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\");\n/* harmony import */ var _utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/is-primary-pointer.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/setup.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n/* harmony import */ var _utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/is-keyboard-accessible.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\");\n/* harmony import */ var _utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/keyboard.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\");\n/* harmony import */ var _utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/state.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n\n\n\n\n\n\n\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return (0,_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_0__.isPrimaryPointer)(event) && !(0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_1__.isDragActive)();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n    const [targets, eventOptions, cancelEvents] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_2__.setupGesture)(targetOrSelector, options);\n    const startPress = (startEvent) => {\n        const target = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent))\n            return;\n        _utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__.isPressing.add(target);\n        const onPressEnd = onPressStart(target, startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (_utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__.isPressing.has(target)) {\n                _utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__.isPressing.delete(target);\n            }\n            if (!isValidPressEvent(endEvent)) {\n                return;\n            }\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, target === window ||\n                target === document ||\n                options.useGlobalTarget ||\n                (0,_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_4__.isNodeOrChild)(target, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    targets.forEach((target) => {\n        const pointerDownTarget = options.useGlobalTarget ? window : target;\n        pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n        if ((0,_utils_is_html_element_mjs__WEBPACK_IMPORTED_MODULE_5__.isHTMLElement)(target)) {\n            target.addEventListener(\"focus\", (event) => (0,_utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_6__.enableKeyboardPress)(event, eventOptions));\n            if (!(0,_utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_7__.isElementKeyboardAccessible)(target) &&\n                !target.hasAttribute(\"tabindex\")) {\n                target.tabIndex = 0;\n            }\n        }\n    });\n    return cancelEvents;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs":
/*!***********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isElementKeyboardAccessible: () => (/* binding */ isElementKeyboardAccessible)\n/* harmony export */ });\nconst focusableElements = new Set([\n    \"BUTTON\",\n    \"INPUT\",\n    \"SELECT\",\n    \"TEXTAREA\",\n    \"A\",\n]);\nfunction isElementKeyboardAccessible(element) {\n    return (focusableElements.has(element.tagName) ||\n        element.tabIndex !== -1);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbW90aW9uLWRvbUAxMi4yMy4xL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvcHJlc3MvdXRpbHMvaXMta2V5Ym9hcmQtYWNjZXNzaWJsZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXVDIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxtb3Rpb24tZG9tQDEyLjIzLjFcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFxwcmVzc1xcdXRpbHNcXGlzLWtleWJvYXJkLWFjY2Vzc2libGUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGZvY3VzYWJsZUVsZW1lbnRzID0gbmV3IFNldChbXG4gICAgXCJCVVRUT05cIixcbiAgICBcIklOUFVUXCIsXG4gICAgXCJTRUxFQ1RcIixcbiAgICBcIlRFWFRBUkVBXCIsXG4gICAgXCJBXCIsXG5dKTtcbmZ1bmN0aW9uIGlzRWxlbWVudEtleWJvYXJkQWNjZXNzaWJsZShlbGVtZW50KSB7XG4gICAgcmV0dXJuIChmb2N1c2FibGVFbGVtZW50cy5oYXMoZWxlbWVudC50YWdOYW1lKSB8fFxuICAgICAgICBlbGVtZW50LnRhYkluZGV4ICE9PSAtMSk7XG59XG5cbmV4cG9ydCB7IGlzRWxlbWVudEtleWJvYXJkQWNjZXNzaWJsZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enableKeyboardPress: () => (/* binding */ enableKeyboardPress)\n/* harmony export */ });\n/* harmony import */ var _state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.key !== \"Enter\")\n            return;\n        callback(event);\n    };\n}\nfunction firePointerEvent(target, type) {\n    target.dispatchEvent(new PointerEvent(\"pointer\" + type, { isPrimary: true, bubbles: true }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions) => {\n    const element = focusEvent.currentTarget;\n    if (!element)\n        return;\n    const handleKeydown = filterEvents(() => {\n        if (_state_mjs__WEBPACK_IMPORTED_MODULE_0__.isPressing.has(element))\n            return;\n        firePointerEvent(element, \"down\");\n        const handleKeyup = filterEvents(() => {\n            firePointerEvent(element, \"up\");\n        });\n        const handleBlur = () => firePointerEvent(element, \"cancel\");\n        element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n        element.addEventListener(\"blur\", handleBlur, eventOptions);\n    });\n    element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n    /**\n     * Add an event listener that fires on blur to remove the keydown events.\n     */\n    element.addEventListener(\"blur\", () => element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs":
/*!******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPressing: () => (/* binding */ isPressing)\n/* harmony export */ });\nconst isPressing = new WeakSet();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbW90aW9uLWRvbUAxMi4yMy4xL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvcHJlc3MvdXRpbHMvc3RhdGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFc0IiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG1vdGlvbi1kb21AMTIuMjMuMVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZ2VzdHVyZXNcXHByZXNzXFx1dGlsc1xcc3RhdGUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzUHJlc3NpbmcgPSBuZXcgV2Vha1NldCgpO1xuXG5leHBvcnQgeyBpc1ByZXNzaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs":
/*!***********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNodeOrChild: () => (/* binding */ isNodeOrChild)\n/* harmony export */ });\n/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n    if (!child) {\n        return false;\n    }\n    else if (parent === child) {\n        return true;\n    }\n    else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbW90aW9uLWRvbUAxMi4yMy4xL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvdXRpbHMvaXMtbm9kZS1vci1jaGlsZC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFeUIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG1vdGlvbi1kb21AMTIuMjMuMVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZ2VzdHVyZXNcXHV0aWxzXFxpcy1ub2RlLW9yLWNoaWxkLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJlY3Vyc2l2ZWx5IHRyYXZlcnNlIHVwIHRoZSB0cmVlIHRvIGNoZWNrIHdoZXRoZXIgdGhlIHByb3ZpZGVkIGNoaWxkIG5vZGVcbiAqIGlzIHRoZSBwYXJlbnQgb3IgYSBkZXNjZW5kYW50IG9mIGl0LlxuICpcbiAqIEBwYXJhbSBwYXJlbnQgLSBFbGVtZW50IHRvIGZpbmRcbiAqIEBwYXJhbSBjaGlsZCAtIEVsZW1lbnQgdG8gdGVzdCBhZ2FpbnN0IHBhcmVudFxuICovXG5jb25zdCBpc05vZGVPckNoaWxkID0gKHBhcmVudCwgY2hpbGQpID0+IHtcbiAgICBpZiAoIWNoaWxkKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgZWxzZSBpZiAocGFyZW50ID09PSBjaGlsZCkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiBpc05vZGVPckNoaWxkKHBhcmVudCwgY2hpbGQucGFyZW50RWxlbWVudCk7XG4gICAgfVxufTtcblxuZXhwb3J0IHsgaXNOb2RlT3JDaGlsZCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPrimaryPointer: () => (/* binding */ isPrimaryPointer)\n/* harmony export */ });\nconst isPrimaryPointer = (event) => {\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    }\n    else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */\n        return event.isPrimary !== false;\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbW90aW9uLWRvbUAxMi4yMy4xL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvdXRpbHMvaXMtcHJpbWFyeS1wb2ludGVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxtb3Rpb24tZG9tQDEyLjIzLjFcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFx1dGlsc1xcaXMtcHJpbWFyeS1wb2ludGVyLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc1ByaW1hcnlQb2ludGVyID0gKGV2ZW50KSA9PiB7XG4gICAgaWYgKGV2ZW50LnBvaW50ZXJUeXBlID09PSBcIm1vdXNlXCIpIHtcbiAgICAgICAgcmV0dXJuIHR5cGVvZiBldmVudC5idXR0b24gIT09IFwibnVtYmVyXCIgfHwgZXZlbnQuYnV0dG9uIDw9IDA7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICAvKipcbiAgICAgICAgICogaXNQcmltYXJ5IGlzIHRydWUgZm9yIGFsbCBtaWNlIGJ1dHRvbnMsIHdoZXJlYXMgZXZlcnkgdG91Y2ggcG9pbnRcbiAgICAgICAgICogaXMgcmVnYXJkZWQgYXMgaXRzIG93biBpbnB1dC4gU28gc3Vic2VxdWVudCBjb25jdXJyZW50IHRvdWNoIHBvaW50c1xuICAgICAgICAgKiB3aWxsIGJlIGZhbHNlLlxuICAgICAgICAgKlxuICAgICAgICAgKiBTcGVjaWZpY2FsbHkgbWF0Y2ggYWdhaW5zdCBmYWxzZSBoZXJlIGFzIGluY29tcGxldGUgdmVyc2lvbnMgb2ZcbiAgICAgICAgICogUG9pbnRlckV2ZW50cyBpbiB2ZXJ5IG9sZCBicm93c2VyIG1pZ2h0IGhhdmUgaXQgc2V0IGFzIHVuZGVmaW5lZC5cbiAgICAgICAgICovXG4gICAgICAgIHJldHVybiBldmVudC5pc1ByaW1hcnkgIT09IGZhbHNlO1xuICAgIH1cbn07XG5cbmV4cG9ydCB7IGlzUHJpbWFyeVBvaW50ZXIgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs":
/*!************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setupGesture: () => (/* binding */ setupGesture)\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/resolve-elements.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = (0,_utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const cancel = () => gestureAbortController.abort();\n    return [elements, eventOptions, cancel];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbW90aW9uLWRvbUAxMi4yMy4xL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvdXRpbHMvc2V0dXAubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1FOztBQUVuRTtBQUNBLHFCQUFxQiw0RUFBZTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXdCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxtb3Rpb24tZG9tQDEyLjIzLjFcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFx1dGlsc1xcc2V0dXAubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlc29sdmVFbGVtZW50cyB9IGZyb20gJy4uLy4uL3V0aWxzL3Jlc29sdmUtZWxlbWVudHMubWpzJztcblxuZnVuY3Rpb24gc2V0dXBHZXN0dXJlKGVsZW1lbnRPclNlbGVjdG9yLCBvcHRpb25zKSB7XG4gICAgY29uc3QgZWxlbWVudHMgPSByZXNvbHZlRWxlbWVudHMoZWxlbWVudE9yU2VsZWN0b3IpO1xuICAgIGNvbnN0IGdlc3R1cmVBYm9ydENvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgY29uc3QgZXZlbnRPcHRpb25zID0ge1xuICAgICAgICBwYXNzaXZlOiB0cnVlLFxuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICBzaWduYWw6IGdlc3R1cmVBYm9ydENvbnRyb2xsZXIuc2lnbmFsLFxuICAgIH07XG4gICAgY29uc3QgY2FuY2VsID0gKCkgPT4gZ2VzdHVyZUFib3J0Q29udHJvbGxlci5hYm9ydCgpO1xuICAgIHJldHVybiBbZWxlbWVudHMsIGV2ZW50T3B0aW9ucywgY2FuY2VsXTtcbn1cblxuZXhwb3J0IHsgc2V0dXBHZXN0dXJlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/batcher.mjs":
/*!*********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/batcher.mjs ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderBatcher: () => (/* binding */ createRenderBatcher)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-utils */ \"../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _order_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./order.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/order.mjs\");\n/* harmony import */ var _render_step_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./render-step.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/render-step.mjs\");\n\n\n\n\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    const flagRunNextFrame = () => (runNextFrame = true);\n    const steps = _order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder.reduce((acc, key) => {\n        acc[key] = (0,_render_step_mjs__WEBPACK_IMPORTED_MODULE_1__.createRenderStep)(flagRunNextFrame, allowKeepAlive ? key : undefined);\n        return acc;\n    }, {});\n    const { setup, read, resolveKeyframes, preUpdate, update, preRender, render, postRender, } = steps;\n    const processBatch = () => {\n        const timestamp = motion_utils__WEBPACK_IMPORTED_MODULE_2__.MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        if (!motion_utils__WEBPACK_IMPORTED_MODULE_2__.MotionGlobalConfig.useManualTiming) {\n            state.delta = useDefaultElapsed\n                ? 1000 / 60\n                : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        }\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        // Unrolled render loop for better per-frame performance\n        setup.process(state);\n        read.process(state);\n        resolveKeyframes.process(state);\n        preUpdate.process(state);\n        update.process(state);\n        preRender.process(state);\n        render.process(state);\n        postRender.process(state);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = _order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => {\n        for (let i = 0; i < _order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder.length; i++) {\n            steps[_order_mjs__WEBPACK_IMPORTED_MODULE_0__.stepsOrder[i]].cancel(process);\n        }\n    };\n    return { schedule, cancel, state, steps };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/batcher.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/frame.mjs":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/frame.mjs ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelFrame: () => (/* binding */ cancelFrame),\n/* harmony export */   frame: () => (/* binding */ frame),\n/* harmony export */   frameData: () => (/* binding */ frameData),\n/* harmony export */   frameSteps: () => (/* binding */ frameSteps)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _batcher_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./batcher.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/batcher.mjs\");\n\n\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps: frameSteps, } = /* @__PURE__ */ (0,_batcher_mjs__WEBPACK_IMPORTED_MODULE_0__.createRenderBatcher)(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop, true);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9mcmFtZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW9DO0FBQ2dCOztBQUVwRCxRQUFRLDZFQUE2RSxrQkFBa0IsaUVBQW1CLHdFQUF3RSw4Q0FBSTs7QUFFakoiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG1vdGlvbi1kb21AMTIuMjMuMVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZnJhbWVsb29wXFxmcmFtZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbm9vcCB9IGZyb20gJ21vdGlvbi11dGlscyc7XG5pbXBvcnQgeyBjcmVhdGVSZW5kZXJCYXRjaGVyIH0gZnJvbSAnLi9iYXRjaGVyLm1qcyc7XG5cbmNvbnN0IHsgc2NoZWR1bGU6IGZyYW1lLCBjYW5jZWw6IGNhbmNlbEZyYW1lLCBzdGF0ZTogZnJhbWVEYXRhLCBzdGVwczogZnJhbWVTdGVwcywgfSA9IC8qIEBfX1BVUkVfXyAqLyBjcmVhdGVSZW5kZXJCYXRjaGVyKHR5cGVvZiByZXF1ZXN0QW5pbWF0aW9uRnJhbWUgIT09IFwidW5kZWZpbmVkXCIgPyByZXF1ZXN0QW5pbWF0aW9uRnJhbWUgOiBub29wLCB0cnVlKTtcblxuZXhwb3J0IHsgY2FuY2VsRnJhbWUsIGZyYW1lLCBmcmFtZURhdGEsIGZyYW1lU3RlcHMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/frame.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/index-legacy.mjs":
/*!**************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/index-legacy.mjs ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelSync: () => (/* binding */ cancelSync),\n/* harmony export */   sync: () => (/* binding */ sync)\n/* harmony export */ });\n/* harmony import */ var _order_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./order.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/order.mjs\");\n/* harmony import */ var _frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./frame.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\n\n/**\n * @deprecated\n *\n * Import as `frame` instead.\n */\nconst sync = _frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame;\n/**\n * @deprecated\n *\n * Use cancelFrame(callback) instead.\n */\nconst cancelSync = _order_mjs__WEBPACK_IMPORTED_MODULE_1__.stepsOrder.reduce((acc, key) => {\n    acc[key] = (process) => (0,_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(process);\n    return acc;\n}, {});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9pbmRleC1sZWdhY3kubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBeUM7QUFDUTs7QUFFakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsNkNBQUs7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixrREFBVTtBQUM3Qiw0QkFBNEIsdURBQVc7QUFDdkM7QUFDQSxDQUFDLElBQUk7O0FBRXVCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxtb3Rpb24tZG9tQDEyLjIzLjFcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGZyYW1lbG9vcFxcaW5kZXgtbGVnYWN5Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdGVwc09yZGVyIH0gZnJvbSAnLi9vcmRlci5tanMnO1xuaW1wb3J0IHsgZnJhbWUsIGNhbmNlbEZyYW1lIH0gZnJvbSAnLi9mcmFtZS5tanMnO1xuXG4vKipcbiAqIEBkZXByZWNhdGVkXG4gKlxuICogSW1wb3J0IGFzIGBmcmFtZWAgaW5zdGVhZC5cbiAqL1xuY29uc3Qgc3luYyA9IGZyYW1lO1xuLyoqXG4gKiBAZGVwcmVjYXRlZFxuICpcbiAqIFVzZSBjYW5jZWxGcmFtZShjYWxsYmFjaykgaW5zdGVhZC5cbiAqL1xuY29uc3QgY2FuY2VsU3luYyA9IHN0ZXBzT3JkZXIucmVkdWNlKChhY2MsIGtleSkgPT4ge1xuICAgIGFjY1trZXldID0gKHByb2Nlc3MpID0+IGNhbmNlbEZyYW1lKHByb2Nlc3MpO1xuICAgIHJldHVybiBhY2M7XG59LCB7fSk7XG5cbmV4cG9ydCB7IGNhbmNlbFN5bmMsIHN5bmMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/index-legacy.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/microtask.mjs":
/*!***********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/microtask.mjs ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelMicrotask: () => (/* binding */ cancelMicrotask),\n/* harmony export */   microtask: () => (/* binding */ microtask)\n/* harmony export */ });\n/* harmony import */ var _batcher_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./batcher.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/batcher.mjs\");\n\n\nconst { schedule: microtask, cancel: cancelMicrotask } = \n/* @__PURE__ */ (0,_batcher_mjs__WEBPACK_IMPORTED_MODULE_0__.createRenderBatcher)(queueMicrotask, false);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9taWNyb3Rhc2subWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDs7QUFFcEQsUUFBUSwrQ0FBK0M7QUFDdkQsZ0JBQWdCLGlFQUFtQjs7QUFFRyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbW90aW9uLWRvbUAxMi4yMy4xXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxmcmFtZWxvb3BcXG1pY3JvdGFzay5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlUmVuZGVyQmF0Y2hlciB9IGZyb20gJy4vYmF0Y2hlci5tanMnO1xuXG5jb25zdCB7IHNjaGVkdWxlOiBtaWNyb3Rhc2ssIGNhbmNlbDogY2FuY2VsTWljcm90YXNrIH0gPSBcbi8qIEBfX1BVUkVfXyAqLyBjcmVhdGVSZW5kZXJCYXRjaGVyKHF1ZXVlTWljcm90YXNrLCBmYWxzZSk7XG5cbmV4cG9ydCB7IGNhbmNlbE1pY3JvdGFzaywgbWljcm90YXNrIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/microtask.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/order.mjs":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/order.mjs ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stepsOrder: () => (/* binding */ stepsOrder)\n/* harmony export */ });\nconst stepsOrder = [\n    \"setup\", // Compute\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"preUpdate\", // Compute\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9vcmRlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzQiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbW90aW9uLWRvbUAxMi4yMy4xXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxmcmFtZWxvb3BcXG9yZGVyLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzdGVwc09yZGVyID0gW1xuICAgIFwic2V0dXBcIiwgLy8gQ29tcHV0ZVxuICAgIFwicmVhZFwiLCAvLyBSZWFkXG4gICAgXCJyZXNvbHZlS2V5ZnJhbWVzXCIsIC8vIFdyaXRlL1JlYWQvV3JpdGUvUmVhZFxuICAgIFwicHJlVXBkYXRlXCIsIC8vIENvbXB1dGVcbiAgICBcInVwZGF0ZVwiLCAvLyBDb21wdXRlXG4gICAgXCJwcmVSZW5kZXJcIiwgLy8gQ29tcHV0ZVxuICAgIFwicmVuZGVyXCIsIC8vIFdyaXRlXG4gICAgXCJwb3N0UmVuZGVyXCIsIC8vIENvbXB1dGVcbl07XG5cbmV4cG9ydCB7IHN0ZXBzT3JkZXIgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/order.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/render-step.mjs":
/*!*************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/render-step.mjs ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderStep: () => (/* binding */ createRenderStep)\n/* harmony export */ });\n/* harmony import */ var _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stats/buffer.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/stats/buffer.mjs\");\n\n\nfunction createRenderStep(runNextFrame, stepName) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Set();\n    let nextFrame = new Set();\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    let latestFrameData = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    let numCalls = 0;\n    function triggerCallback(callback) {\n        if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n        }\n        numCalls++;\n        callback(latestFrameData);\n    }\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (!queue.has(callback))\n                queue.add(callback);\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.delete(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            latestFrameData = frameData;\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Execute this frame\n            thisFrame.forEach(triggerCallback);\n            /**\n             * If we're recording stats then\n             */\n            if (stepName && _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__.statsBuffer.value) {\n                _stats_buffer_mjs__WEBPACK_IMPORTED_MODULE_0__.statsBuffer.value.frameloop[stepName].push(numCalls);\n            }\n            numCalls = 0;\n            // Clear the frame so no callbacks remain. This is to avoid\n            // memory leaks should this render step not run for a while.\n            thisFrame.clear();\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9yZW5kZXItc3RlcC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0Q7O0FBRWxEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsMERBQVc7QUFDdkMsZ0JBQWdCLDBEQUFXO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTs7QUFFNEIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG1vdGlvbi1kb21AMTIuMjMuMVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZnJhbWVsb29wXFxyZW5kZXItc3RlcC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3RhdHNCdWZmZXIgfSBmcm9tICcuLi9zdGF0cy9idWZmZXIubWpzJztcblxuZnVuY3Rpb24gY3JlYXRlUmVuZGVyU3RlcChydW5OZXh0RnJhbWUsIHN0ZXBOYW1lKSB7XG4gICAgLyoqXG4gICAgICogV2UgY3JlYXRlIGFuZCByZXVzZSB0d28gcXVldWVzLCBvbmUgdG8gcXVldWUgam9icyBmb3IgdGhlIGN1cnJlbnQgZnJhbWVcbiAgICAgKiBhbmQgb25lIGZvciB0aGUgbmV4dC4gV2UgcmV1c2UgdG8gYXZvaWQgdHJpZ2dlcmluZyBHQyBhZnRlciB4IGZyYW1lcy5cbiAgICAgKi9cbiAgICBsZXQgdGhpc0ZyYW1lID0gbmV3IFNldCgpO1xuICAgIGxldCBuZXh0RnJhbWUgPSBuZXcgU2V0KCk7XG4gICAgLyoqXG4gICAgICogVHJhY2sgd2hldGhlciB3ZSdyZSBjdXJyZW50bHkgcHJvY2Vzc2luZyBqb2JzIGluIHRoaXMgc3RlcC4gVGhpcyB3YXlcbiAgICAgKiB3ZSBjYW4gZGVjaWRlIHdoZXRoZXIgdG8gc2NoZWR1bGUgbmV3IGpvYnMgZm9yIHRoaXMgZnJhbWUgb3IgbmV4dC5cbiAgICAgKi9cbiAgICBsZXQgaXNQcm9jZXNzaW5nID0gZmFsc2U7XG4gICAgbGV0IGZsdXNoTmV4dEZyYW1lID0gZmFsc2U7XG4gICAgLyoqXG4gICAgICogQSBzZXQgb2YgcHJvY2Vzc2VzIHdoaWNoIHdlcmUgbWFya2VkIGtlZXBBbGl2ZSB3aGVuIHNjaGVkdWxlZC5cbiAgICAgKi9cbiAgICBjb25zdCB0b0tlZXBBbGl2ZSA9IG5ldyBXZWFrU2V0KCk7XG4gICAgbGV0IGxhdGVzdEZyYW1lRGF0YSA9IHtcbiAgICAgICAgZGVsdGE6IDAuMCxcbiAgICAgICAgdGltZXN0YW1wOiAwLjAsXG4gICAgICAgIGlzUHJvY2Vzc2luZzogZmFsc2UsXG4gICAgfTtcbiAgICBsZXQgbnVtQ2FsbHMgPSAwO1xuICAgIGZ1bmN0aW9uIHRyaWdnZXJDYWxsYmFjayhjYWxsYmFjaykge1xuICAgICAgICBpZiAodG9LZWVwQWxpdmUuaGFzKGNhbGxiYWNrKSkge1xuICAgICAgICAgICAgc3RlcC5zY2hlZHVsZShjYWxsYmFjayk7XG4gICAgICAgICAgICBydW5OZXh0RnJhbWUoKTtcbiAgICAgICAgfVxuICAgICAgICBudW1DYWxscysrO1xuICAgICAgICBjYWxsYmFjayhsYXRlc3RGcmFtZURhdGEpO1xuICAgIH1cbiAgICBjb25zdCBzdGVwID0ge1xuICAgICAgICAvKipcbiAgICAgICAgICogU2NoZWR1bGUgYSBwcm9jZXNzIHRvIHJ1biBvbiB0aGUgbmV4dCBmcmFtZS5cbiAgICAgICAgICovXG4gICAgICAgIHNjaGVkdWxlOiAoY2FsbGJhY2ssIGtlZXBBbGl2ZSA9IGZhbHNlLCBpbW1lZGlhdGUgPSBmYWxzZSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgYWRkVG9DdXJyZW50RnJhbWUgPSBpbW1lZGlhdGUgJiYgaXNQcm9jZXNzaW5nO1xuICAgICAgICAgICAgY29uc3QgcXVldWUgPSBhZGRUb0N1cnJlbnRGcmFtZSA/IHRoaXNGcmFtZSA6IG5leHRGcmFtZTtcbiAgICAgICAgICAgIGlmIChrZWVwQWxpdmUpXG4gICAgICAgICAgICAgICAgdG9LZWVwQWxpdmUuYWRkKGNhbGxiYWNrKTtcbiAgICAgICAgICAgIGlmICghcXVldWUuaGFzKGNhbGxiYWNrKSlcbiAgICAgICAgICAgICAgICBxdWV1ZS5hZGQoY2FsbGJhY2spO1xuICAgICAgICAgICAgcmV0dXJuIGNhbGxiYWNrO1xuICAgICAgICB9LFxuICAgICAgICAvKipcbiAgICAgICAgICogQ2FuY2VsIHRoZSBwcm92aWRlZCBjYWxsYmFjayBmcm9tIHJ1bm5pbmcgb24gdGhlIG5leHQgZnJhbWUuXG4gICAgICAgICAqL1xuICAgICAgICBjYW5jZWw6IChjYWxsYmFjaykgPT4ge1xuICAgICAgICAgICAgbmV4dEZyYW1lLmRlbGV0ZShjYWxsYmFjayk7XG4gICAgICAgICAgICB0b0tlZXBBbGl2ZS5kZWxldGUoY2FsbGJhY2spO1xuICAgICAgICB9LFxuICAgICAgICAvKipcbiAgICAgICAgICogRXhlY3V0ZSBhbGwgc2NoZWR1bGUgY2FsbGJhY2tzLlxuICAgICAgICAgKi9cbiAgICAgICAgcHJvY2VzczogKGZyYW1lRGF0YSkgPT4ge1xuICAgICAgICAgICAgbGF0ZXN0RnJhbWVEYXRhID0gZnJhbWVEYXRhO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBJZiB3ZSdyZSBhbHJlYWR5IHByb2Nlc3Npbmcgd2UndmUgcHJvYmFibHkgYmVlbiB0cmlnZ2VyZWQgYnkgYSBmbHVzaFN5bmNcbiAgICAgICAgICAgICAqIGluc2lkZSBhbiBleGlzdGluZyBwcm9jZXNzLiBJbnN0ZWFkIG9mIGV4ZWN1dGluZywgbWFyayBmbHVzaE5leHRGcmFtZVxuICAgICAgICAgICAgICogYXMgdHJ1ZSBhbmQgZW5zdXJlIHdlIGZsdXNoIHRoZSBmb2xsb3dpbmcgZnJhbWUgYXQgdGhlIGVuZCBvZiB0aGlzIG9uZS5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKGlzUHJvY2Vzc2luZykge1xuICAgICAgICAgICAgICAgIGZsdXNoTmV4dEZyYW1lID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpc1Byb2Nlc3NpbmcgPSB0cnVlO1xuICAgICAgICAgICAgW3RoaXNGcmFtZSwgbmV4dEZyYW1lXSA9IFtuZXh0RnJhbWUsIHRoaXNGcmFtZV07XG4gICAgICAgICAgICAvLyBFeGVjdXRlIHRoaXMgZnJhbWVcbiAgICAgICAgICAgIHRoaXNGcmFtZS5mb3JFYWNoKHRyaWdnZXJDYWxsYmFjayk7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIElmIHdlJ3JlIHJlY29yZGluZyBzdGF0cyB0aGVuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGlmIChzdGVwTmFtZSAmJiBzdGF0c0J1ZmZlci52YWx1ZSkge1xuICAgICAgICAgICAgICAgIHN0YXRzQnVmZmVyLnZhbHVlLmZyYW1lbG9vcFtzdGVwTmFtZV0ucHVzaChudW1DYWxscyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBudW1DYWxscyA9IDA7XG4gICAgICAgICAgICAvLyBDbGVhciB0aGUgZnJhbWUgc28gbm8gY2FsbGJhY2tzIHJlbWFpbi4gVGhpcyBpcyB0byBhdm9pZFxuICAgICAgICAgICAgLy8gbWVtb3J5IGxlYWtzIHNob3VsZCB0aGlzIHJlbmRlciBzdGVwIG5vdCBydW4gZm9yIGEgd2hpbGUuXG4gICAgICAgICAgICB0aGlzRnJhbWUuY2xlYXIoKTtcbiAgICAgICAgICAgIGlzUHJvY2Vzc2luZyA9IGZhbHNlO1xuICAgICAgICAgICAgaWYgKGZsdXNoTmV4dEZyYW1lKSB7XG4gICAgICAgICAgICAgICAgZmx1c2hOZXh0RnJhbWUgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICBzdGVwLnByb2Nlc3MoZnJhbWVEYXRhKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICB9O1xuICAgIHJldHVybiBzdGVwO1xufVxuXG5leHBvcnQgeyBjcmVhdGVSZW5kZXJTdGVwIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/render-step.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs":
/*!***********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   time: () => (/* binding */ time)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./frame.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\n\nlet now;\nfunction clearTime() {\n    now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n    now: () => {\n        if (now === undefined) {\n            time.set(_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.isProcessing || motion_utils__WEBPACK_IMPORTED_MODULE_1__.MotionGlobalConfig.useManualTiming\n                ? _frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.timestamp\n                : performance.now());\n        }\n        return now;\n    },\n    set: (newTime) => {\n        now = newTime;\n        queueMicrotask(clearTime);\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2ZyYW1lbG9vcC9zeW5jLXRpbWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNWOztBQUV4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsaURBQVMsaUJBQWlCLDREQUFrQjtBQUNqRSxrQkFBa0IsaURBQVM7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFZ0IiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG1vdGlvbi1kb21AMTIuMjMuMVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZnJhbWVsb29wXFxzeW5jLXRpbWUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE1vdGlvbkdsb2JhbENvbmZpZyB9IGZyb20gJ21vdGlvbi11dGlscyc7XG5pbXBvcnQgeyBmcmFtZURhdGEgfSBmcm9tICcuL2ZyYW1lLm1qcyc7XG5cbmxldCBub3c7XG5mdW5jdGlvbiBjbGVhclRpbWUoKSB7XG4gICAgbm93ID0gdW5kZWZpbmVkO1xufVxuLyoqXG4gKiBBbiBldmVudGxvb3Atc3luY2hyb25vdXMgYWx0ZXJuYXRpdmUgdG8gcGVyZm9ybWFuY2Uubm93KCkuXG4gKlxuICogRW5zdXJlcyB0aGF0IHRpbWUgbWVhc3VyZW1lbnRzIHJlbWFpbiBjb25zaXN0ZW50IHdpdGhpbiBhIHN5bmNocm9ub3VzIGNvbnRleHQuXG4gKiBVc3VhbGx5IGNhbGxpbmcgcGVyZm9ybWFuY2Uubm93KCkgdHdpY2Ugd2l0aGluIHRoZSBzYW1lIHN5bmNocm9ub3VzIGNvbnRleHRcbiAqIHdpbGwgcmV0dXJuIGRpZmZlcmVudCB2YWx1ZXMgd2hpY2ggaXNuJ3QgdXNlZnVsIGZvciBhbmltYXRpb25zIHdoZW4gd2UncmUgdXN1YWxseVxuICogdHJ5aW5nIHRvIHN5bmMgYW5pbWF0aW9ucyB0byB0aGUgc2FtZSBmcmFtZS5cbiAqL1xuY29uc3QgdGltZSA9IHtcbiAgICBub3c6ICgpID0+IHtcbiAgICAgICAgaWYgKG5vdyA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aW1lLnNldChmcmFtZURhdGEuaXNQcm9jZXNzaW5nIHx8IE1vdGlvbkdsb2JhbENvbmZpZy51c2VNYW51YWxUaW1pbmdcbiAgICAgICAgICAgICAgICA/IGZyYW1lRGF0YS50aW1lc3RhbXBcbiAgICAgICAgICAgICAgICA6IHBlcmZvcm1hbmNlLm5vdygpKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbm93O1xuICAgIH0sXG4gICAgc2V0OiAobmV3VGltZSkgPT4ge1xuICAgICAgICBub3cgPSBuZXdUaW1lO1xuICAgICAgICBxdWV1ZU1pY3JvdGFzayhjbGVhclRpbWUpO1xuICAgIH0sXG59O1xuXG5leHBvcnQgeyB0aW1lIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDragActive: () => (/* binding */ isDragActive),\n/* harmony export */   isDragging: () => (/* binding */ isDragging)\n/* harmony export */ });\nconst isDragging = {\n    x: false,\n    y: false,\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvc3RhdGUvaXMtYWN0aXZlLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVvQyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbW90aW9uLWRvbUAxMi4yMy4xXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxnZXN0dXJlc1xcZHJhZ1xcc3RhdGVcXGlzLWFjdGl2ZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNEcmFnZ2luZyA9IHtcbiAgICB4OiBmYWxzZSxcbiAgICB5OiBmYWxzZSxcbn07XG5mdW5jdGlvbiBpc0RyYWdBY3RpdmUoKSB7XG4gICAgcmV0dXJuIGlzRHJhZ2dpbmcueCB8fCBpc0RyYWdnaW5nLnk7XG59XG5cbmV4cG9ydCB7IGlzRHJhZ0FjdGl2ZSwgaXNEcmFnZ2luZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setDragLock: () => (/* binding */ setDragLock)\n/* harmony export */ });\n/* harmony import */ var _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-active.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis]) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = false;\n            };\n        }\n    }\n    else {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x || _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = false;\n            };\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvc3RhdGUvc2V0LWFjdGl2ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkM7O0FBRTdDO0FBQ0E7QUFDQSxZQUFZLHNEQUFVO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLFlBQVksc0RBQVU7QUFDdEI7QUFDQSxnQkFBZ0Isc0RBQVU7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHNEQUFVLE1BQU0sc0RBQVU7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsWUFBWSxzREFBVSxLQUFLLHNEQUFVO0FBQ3JDO0FBQ0EsZ0JBQWdCLHNEQUFVLEtBQUssc0RBQVU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7O0FBRXVCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxtb3Rpb24tZG9tQDEyLjIzLjFcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFxkcmFnXFxzdGF0ZVxcc2V0LWFjdGl2ZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNEcmFnZ2luZyB9IGZyb20gJy4vaXMtYWN0aXZlLm1qcyc7XG5cbmZ1bmN0aW9uIHNldERyYWdMb2NrKGF4aXMpIHtcbiAgICBpZiAoYXhpcyA9PT0gXCJ4XCIgfHwgYXhpcyA9PT0gXCJ5XCIpIHtcbiAgICAgICAgaWYgKGlzRHJhZ2dpbmdbYXhpc10pIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaXNEcmFnZ2luZ1theGlzXSA9IHRydWU7XG4gICAgICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlzRHJhZ2dpbmdbYXhpc10gPSBmYWxzZTtcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGlmIChpc0RyYWdnaW5nLnggfHwgaXNEcmFnZ2luZy55KSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGlzRHJhZ2dpbmcueCA9IGlzRHJhZ2dpbmcueSA9IHRydWU7XG4gICAgICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlzRHJhZ2dpbmcueCA9IGlzRHJhZ2dpbmcueSA9IGZhbHNlO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH1cbn1cblxuZXhwb3J0IHsgc2V0RHJhZ0xvY2sgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/hover.mjs":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/hover.mjs ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hover: () => (/* binding */ hover)\n/* harmony export */ });\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./drag/state/is-active.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/setup.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n\n\n\nfunction isValidHover(event) {\n    return !(event.pointerType === \"touch\" || (0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragActive)());\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n    const [elements, eventOptions, cancel] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__.setupGesture)(elementOrSelector, options);\n    const onPointerEnter = (enterEvent) => {\n        if (!isValidHover(enterEvent))\n            return;\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(target, enterEvent);\n        if (typeof onHoverEnd !== \"function\" || !target)\n            return;\n        const onPointerLeave = (leaveEvent) => {\n            if (!isValidHover(leaveEvent))\n                return;\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        };\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    };\n    elements.forEach((element) => {\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return cancel;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2hvdmVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEQ7QUFDVDs7QUFFakQ7QUFDQSw4Q0FBOEMsdUVBQVk7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDREQUE0RDtBQUM1RCw2Q0FBNkMsOERBQVk7QUFDekQ7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFNBQVM7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFaUIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG1vdGlvbi1kb21AMTIuMjMuMVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcZ2VzdHVyZXNcXGhvdmVyLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0RyYWdBY3RpdmUgfSBmcm9tICcuL2RyYWcvc3RhdGUvaXMtYWN0aXZlLm1qcyc7XG5pbXBvcnQgeyBzZXR1cEdlc3R1cmUgfSBmcm9tICcuL3V0aWxzL3NldHVwLm1qcyc7XG5cbmZ1bmN0aW9uIGlzVmFsaWRIb3ZlcihldmVudCkge1xuICAgIHJldHVybiAhKGV2ZW50LnBvaW50ZXJUeXBlID09PSBcInRvdWNoXCIgfHwgaXNEcmFnQWN0aXZlKCkpO1xufVxuLyoqXG4gKiBDcmVhdGUgYSBob3ZlciBnZXN0dXJlLiBob3ZlcigpIGlzIGRpZmZlcmVudCB0byAuYWRkRXZlbnRMaXN0ZW5lcihcInBvaW50ZXJlbnRlclwiKVxuICogaW4gdGhhdCBpdCBoYXMgYW4gZWFzaWVyIHN5bnRheCwgZmlsdGVycyBvdXQgcG9seWZpbGxlZCB0b3VjaCBldmVudHMsIGludGVyb3BlcmF0ZXNcbiAqIHdpdGggZHJhZyBnZXN0dXJlcywgYW5kIGF1dG9tYXRpY2FsbHkgcmVtb3ZlcyB0aGUgXCJwb2ludGVyZW5uZFwiIGV2ZW50IGxpc3RlbmVyIHdoZW4gdGhlIGhvdmVyIGVuZHMuXG4gKlxuICogQHB1YmxpY1xuICovXG5mdW5jdGlvbiBob3ZlcihlbGVtZW50T3JTZWxlY3Rvciwgb25Ib3ZlclN0YXJ0LCBvcHRpb25zID0ge30pIHtcbiAgICBjb25zdCBbZWxlbWVudHMsIGV2ZW50T3B0aW9ucywgY2FuY2VsXSA9IHNldHVwR2VzdHVyZShlbGVtZW50T3JTZWxlY3Rvciwgb3B0aW9ucyk7XG4gICAgY29uc3Qgb25Qb2ludGVyRW50ZXIgPSAoZW50ZXJFdmVudCkgPT4ge1xuICAgICAgICBpZiAoIWlzVmFsaWRIb3ZlcihlbnRlckV2ZW50KSlcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgY29uc3QgeyB0YXJnZXQgfSA9IGVudGVyRXZlbnQ7XG4gICAgICAgIGNvbnN0IG9uSG92ZXJFbmQgPSBvbkhvdmVyU3RhcnQodGFyZ2V0LCBlbnRlckV2ZW50KTtcbiAgICAgICAgaWYgKHR5cGVvZiBvbkhvdmVyRW5kICE9PSBcImZ1bmN0aW9uXCIgfHwgIXRhcmdldClcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgY29uc3Qgb25Qb2ludGVyTGVhdmUgPSAobGVhdmVFdmVudCkgPT4ge1xuICAgICAgICAgICAgaWYgKCFpc1ZhbGlkSG92ZXIobGVhdmVFdmVudCkpXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgb25Ib3ZlckVuZChsZWF2ZUV2ZW50KTtcbiAgICAgICAgICAgIHRhcmdldC5yZW1vdmVFdmVudExpc3RlbmVyKFwicG9pbnRlcmxlYXZlXCIsIG9uUG9pbnRlckxlYXZlKTtcbiAgICAgICAgfTtcbiAgICAgICAgdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIoXCJwb2ludGVybGVhdmVcIiwgb25Qb2ludGVyTGVhdmUsIGV2ZW50T3B0aW9ucyk7XG4gICAgfTtcbiAgICBlbGVtZW50cy5mb3JFYWNoKChlbGVtZW50KSA9PiB7XG4gICAgICAgIGVsZW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihcInBvaW50ZXJlbnRlclwiLCBvblBvaW50ZXJFbnRlciwgZXZlbnRPcHRpb25zKTtcbiAgICB9KTtcbiAgICByZXR1cm4gY2FuY2VsO1xufVxuXG5leHBvcnQgeyBob3ZlciB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/hover.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/index.mjs":
/*!************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/index.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   press: () => (/* binding */ press)\n/* harmony export */ });\n/* harmony import */ var _utils_is_html_element_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/is-html-element.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/utils/is-html-element.mjs\");\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../drag/state/is-active.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/is-node-or-child.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\");\n/* harmony import */ var _utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/is-primary-pointer.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/setup.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n/* harmony import */ var _utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/is-keyboard-accessible.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\");\n/* harmony import */ var _utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/keyboard.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\");\n/* harmony import */ var _utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/state.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n\n\n\n\n\n\n\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return (0,_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_0__.isPrimaryPointer)(event) && !(0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_1__.isDragActive)();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n    const [targets, eventOptions, cancelEvents] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_2__.setupGesture)(targetOrSelector, options);\n    const startPress = (startEvent) => {\n        const target = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent))\n            return;\n        _utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__.isPressing.add(target);\n        const onPressEnd = onPressStart(target, startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (_utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__.isPressing.has(target)) {\n                _utils_state_mjs__WEBPACK_IMPORTED_MODULE_3__.isPressing.delete(target);\n            }\n            if (!isValidPressEvent(endEvent)) {\n                return;\n            }\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, target === window ||\n                target === document ||\n                options.useGlobalTarget ||\n                (0,_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_4__.isNodeOrChild)(target, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    targets.forEach((target) => {\n        const pointerDownTarget = options.useGlobalTarget ? window : target;\n        pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n        if ((0,_utils_is_html_element_mjs__WEBPACK_IMPORTED_MODULE_5__.isHTMLElement)(target)) {\n            target.addEventListener(\"focus\", (event) => (0,_utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_6__.enableKeyboardPress)(event, eventOptions));\n            if (!(0,_utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_7__.isElementKeyboardAccessible)(target) &&\n                !target.hasAttribute(\"tabindex\")) {\n                target.tabIndex = 0;\n            }\n        }\n    });\n    return cancelEvents;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/index.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs":
/*!***********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isElementKeyboardAccessible: () => (/* binding */ isElementKeyboardAccessible)\n/* harmony export */ });\nconst focusableElements = new Set([\n    \"BUTTON\",\n    \"INPUT\",\n    \"SELECT\",\n    \"TEXTAREA\",\n    \"A\",\n]);\nfunction isElementKeyboardAccessible(element) {\n    return (focusableElements.has(element.tagName) ||\n        element.tabIndex !== -1);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3ByZXNzL3V0aWxzL2lzLWtleWJvYXJkLWFjY2Vzc2libGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbW90aW9uLWRvbUAxMi4yMy4xXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxnZXN0dXJlc1xccHJlc3NcXHV0aWxzXFxpcy1rZXlib2FyZC1hY2Nlc3NpYmxlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBmb2N1c2FibGVFbGVtZW50cyA9IG5ldyBTZXQoW1xuICAgIFwiQlVUVE9OXCIsXG4gICAgXCJJTlBVVFwiLFxuICAgIFwiU0VMRUNUXCIsXG4gICAgXCJURVhUQVJFQVwiLFxuICAgIFwiQVwiLFxuXSk7XG5mdW5jdGlvbiBpc0VsZW1lbnRLZXlib2FyZEFjY2Vzc2libGUoZWxlbWVudCkge1xuICAgIHJldHVybiAoZm9jdXNhYmxlRWxlbWVudHMuaGFzKGVsZW1lbnQudGFnTmFtZSkgfHxcbiAgICAgICAgZWxlbWVudC50YWJJbmRleCAhPT0gLTEpO1xufVxuXG5leHBvcnQgeyBpc0VsZW1lbnRLZXlib2FyZEFjY2Vzc2libGUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enableKeyboardPress: () => (/* binding */ enableKeyboardPress)\n/* harmony export */ });\n/* harmony import */ var _state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.key !== \"Enter\")\n            return;\n        callback(event);\n    };\n}\nfunction firePointerEvent(target, type) {\n    target.dispatchEvent(new PointerEvent(\"pointer\" + type, { isPrimary: true, bubbles: true }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions) => {\n    const element = focusEvent.currentTarget;\n    if (!element)\n        return;\n    const handleKeydown = filterEvents(() => {\n        if (_state_mjs__WEBPACK_IMPORTED_MODULE_0__.isPressing.has(element))\n            return;\n        firePointerEvent(element, \"down\");\n        const handleKeyup = filterEvents(() => {\n            firePointerEvent(element, \"up\");\n        });\n        const handleBlur = () => firePointerEvent(element, \"cancel\");\n        element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n        element.addEventListener(\"blur\", handleBlur, eventOptions);\n    });\n    element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n    /**\n     * Add an event listener that fires on blur to remove the keydown events.\n     */\n    element.addEventListener(\"blur\", () => element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs":
/*!******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPressing: () => (/* binding */ isPressing)\n/* harmony export */ });\nconst isPressing = new WeakSet();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3ByZXNzL3V0aWxzL3N0YXRlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRXNCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxtb3Rpb24tZG9tQDEyLjIzLjFcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFxwcmVzc1xcdXRpbHNcXHN0YXRlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc1ByZXNzaW5nID0gbmV3IFdlYWtTZXQoKTtcblxuZXhwb3J0IHsgaXNQcmVzc2luZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs":
/*!***********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNodeOrChild: () => (/* binding */ isNodeOrChild)\n/* harmony export */ });\n/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n    if (!child) {\n        return false;\n    }\n    else if (parent === child) {\n        return true;\n    }\n    else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL2lzLW5vZGUtb3ItY2hpbGQubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXlCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxtb3Rpb24tZG9tQDEyLjIzLjFcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXGdlc3R1cmVzXFx1dGlsc1xcaXMtbm9kZS1vci1jaGlsZC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZWN1cnNpdmVseSB0cmF2ZXJzZSB1cCB0aGUgdHJlZSB0byBjaGVjayB3aGV0aGVyIHRoZSBwcm92aWRlZCBjaGlsZCBub2RlXG4gKiBpcyB0aGUgcGFyZW50IG9yIGEgZGVzY2VuZGFudCBvZiBpdC5cbiAqXG4gKiBAcGFyYW0gcGFyZW50IC0gRWxlbWVudCB0byBmaW5kXG4gKiBAcGFyYW0gY2hpbGQgLSBFbGVtZW50IHRvIHRlc3QgYWdhaW5zdCBwYXJlbnRcbiAqL1xuY29uc3QgaXNOb2RlT3JDaGlsZCA9IChwYXJlbnQsIGNoaWxkKSA9PiB7XG4gICAgaWYgKCFjaGlsZCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGVsc2UgaWYgKHBhcmVudCA9PT0gY2hpbGQpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4gaXNOb2RlT3JDaGlsZChwYXJlbnQsIGNoaWxkLnBhcmVudEVsZW1lbnQpO1xuICAgIH1cbn07XG5cbmV4cG9ydCB7IGlzTm9kZU9yQ2hpbGQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPrimaryPointer: () => (/* binding */ isPrimaryPointer)\n/* harmony export */ });\nconst isPrimaryPointer = (event) => {\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    }\n    else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */\n        return event.isPrimary !== false;\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL2lzLXByaW1hcnktcG9pbnRlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU0QiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbW90aW9uLWRvbUAxMi4yMy4xXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxnZXN0dXJlc1xcdXRpbHNcXGlzLXByaW1hcnktcG9pbnRlci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNQcmltYXJ5UG9pbnRlciA9IChldmVudCkgPT4ge1xuICAgIGlmIChldmVudC5wb2ludGVyVHlwZSA9PT0gXCJtb3VzZVwiKSB7XG4gICAgICAgIHJldHVybiB0eXBlb2YgZXZlbnQuYnV0dG9uICE9PSBcIm51bWJlclwiIHx8IGV2ZW50LmJ1dHRvbiA8PSAwO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIGlzUHJpbWFyeSBpcyB0cnVlIGZvciBhbGwgbWljZSBidXR0b25zLCB3aGVyZWFzIGV2ZXJ5IHRvdWNoIHBvaW50XG4gICAgICAgICAqIGlzIHJlZ2FyZGVkIGFzIGl0cyBvd24gaW5wdXQuIFNvIHN1YnNlcXVlbnQgY29uY3VycmVudCB0b3VjaCBwb2ludHNcbiAgICAgICAgICogd2lsbCBiZSBmYWxzZS5cbiAgICAgICAgICpcbiAgICAgICAgICogU3BlY2lmaWNhbGx5IG1hdGNoIGFnYWluc3QgZmFsc2UgaGVyZSBhcyBpbmNvbXBsZXRlIHZlcnNpb25zIG9mXG4gICAgICAgICAqIFBvaW50ZXJFdmVudHMgaW4gdmVyeSBvbGQgYnJvd3NlciBtaWdodCBoYXZlIGl0IHNldCBhcyB1bmRlZmluZWQuXG4gICAgICAgICAqL1xuICAgICAgICByZXR1cm4gZXZlbnQuaXNQcmltYXJ5ICE9PSBmYWxzZTtcbiAgICB9XG59O1xuXG5leHBvcnQgeyBpc1ByaW1hcnlQb2ludGVyIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs":
/*!************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setupGesture: () => (/* binding */ setupGesture)\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/resolve-elements.mjs */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = (0,_utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const cancel = () => gestureAbortController.abort();\n    return [elements, eventOptions, cancel];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL21vdGlvbi1kb21AMTIuMjMuMS9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL3NldHVwLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRTs7QUFFbkU7QUFDQSxxQkFBcUIsNEVBQWU7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcbW90aW9uLWRvbUAxMi4yMy4xXFxub2RlX21vZHVsZXNcXG1vdGlvbi1kb21cXGRpc3RcXGVzXFxnZXN0dXJlc1xcdXRpbHNcXHNldHVwLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZXNvbHZlRWxlbWVudHMgfSBmcm9tICcuLi8uLi91dGlscy9yZXNvbHZlLWVsZW1lbnRzLm1qcyc7XG5cbmZ1bmN0aW9uIHNldHVwR2VzdHVyZShlbGVtZW50T3JTZWxlY3Rvciwgb3B0aW9ucykge1xuICAgIGNvbnN0IGVsZW1lbnRzID0gcmVzb2x2ZUVsZW1lbnRzKGVsZW1lbnRPclNlbGVjdG9yKTtcbiAgICBjb25zdCBnZXN0dXJlQWJvcnRDb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xuICAgIGNvbnN0IGV2ZW50T3B0aW9ucyA9IHtcbiAgICAgICAgcGFzc2l2ZTogdHJ1ZSxcbiAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgc2lnbmFsOiBnZXN0dXJlQWJvcnRDb250cm9sbGVyLnNpZ25hbCxcbiAgICB9O1xuICAgIGNvbnN0IGNhbmNlbCA9ICgpID0+IGdlc3R1cmVBYm9ydENvbnRyb2xsZXIuYWJvcnQoKTtcbiAgICByZXR1cm4gW2VsZW1lbnRzLCBldmVudE9wdGlvbnMsIGNhbmNlbF07XG59XG5cbmV4cG9ydCB7IHNldHVwR2VzdHVyZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\n");

/***/ })

};
;