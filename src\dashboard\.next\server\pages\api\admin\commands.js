"use strict";(()=>{var e={};e.id=7053,e.ids=[7053],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},53248:(e,t,o)=>{o.r(t),o.d(t,{config:()=>h,default:()=>g,routeModule:()=>y});var s={};o.r(s),o.d(s,{default:()=>w});var a=o(93433),n=o(20264),r=o(20584),i=o(15806),d=o(94506),c=o(98580),l=o(12518),u=o(29021),p=o.n(u),m=o(33873),f=o.n(m);async function w(e,t){let o=await (0,i.getServerSession)(e,t,d.authOptions);if(!o)return t.status(401).json({error:"Unauthorized"});if(!o.user.isAdmin)return t.status(403).json({error:"Forbidden"});let{bot:s,database:a}=c.dashboardConfig;if(!s?.token||!s?.clientId)return t.status(500).json({error:"Bot credentials missing"});let n={Authorization:`Bot ${s.token}`,"Content-Type":"application/json"};try{let r=(await l.MongoClient.connect(a.url)).db(a.name).collection("command_states");if("GET"===e.method){let e=await fetch(`https://discord.com/api/v10/applications/${s.clientId}/commands`,{headers:n}),o=e.ok?await e.json():[],a=[];if(s.guildId){let e=await fetch(`https://discord.com/api/v10/applications/${s.clientId}/guilds/${s.guildId}/commands`,{headers:n});a=e.ok?await e.json():[]}let i=await r.find({}).toArray(),d=new Map(i.map(e=>[e.commandId,e.enabled])),c=new Map;try{for(let e of[f().resolve(process.cwd(),"../addons"),f().resolve(process.cwd(),"src/addons"),f().resolve(process.cwd(),"../../addons"),f().resolve(process.cwd(),"../dist/addons"),f().resolve(process.cwd(),"dist/addons")].filter(e=>p().existsSync(e)))for(let t of p().readdirSync(e,{withFileTypes:!0}).filter(e=>e.isDirectory()).map(e=>e.name)){let o=f().join(e,t,"commands");if(p().existsSync(o))for(let e of p().readdirSync(o).filter(e=>e.endsWith(".ts")||e.endsWith(".js")).map(e=>f().basename(e,f().extname(e))))c.set(e,{addon:t,category:t.charAt(0).toUpperCase()+t.slice(1)})}}catch(e){}let l=[...o.map(e=>({scope:"GLOBAL",...e,enabled:d.get(e.id)??!0,addon:c.get(e.name)?.addon||"Unknown",category:c.get(e.name)?.category||"Unknown"})),...a.map(e=>({scope:"GUILD",...e,enabled:d.get(e.id)??!0,addon:c.get(e.name)?.addon||"Unknown",category:c.get(e.name)?.category||"Unknown"}))];return t.status(200).json(l)}if("PUT"===e.method){let{commandId:s,enabled:a}=e.body;if("string"!=typeof s||"boolean"!=typeof a)return t.status(400).json({error:"Invalid request body"});await r.updateOne({commandId:s},{$set:{commandId:s,enabled:a}},{upsert:!0});try{let e=process.cwd().includes(f().join("src","dashboard"))?f().resolve(process.cwd(),"..",".."):process.cwd(),t=f().join(e,"command-state.signal");p().writeFileSync(t,JSON.stringify({commandId:s,enabled:a,requestedBy:o.user?.email||"unknown",timestamp:Date.now()}))}catch(e){}return t.status(200).json({commandId:s,enabled:a})}if("DELETE"===e.method){let{commandId:o,scope:a}=e.query;if(!o)return t.status(400).json({error:"Command ID required"});let i="GUILD"===a&&s.guildId?`https://discord.com/api/v10/applications/${s.clientId}/guilds/${s.guildId}/commands/${o}`:`https://discord.com/api/v10/applications/${s.clientId}/commands/${o}`,d=await fetch(i,{method:"DELETE",headers:n});if(!d.ok){let e=await d.json().catch(()=>({message:"Unknown error"}));return t.status(d.status).json(e)}return await r.deleteOne({commandId:o}),t.status(204).end()}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Failed to handle command operation",details:e.message})}}let g=(0,r.M)(s,"default"),h=(0,r.M)(s,"config"),y=new a.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/admin/commands",pathname:"/api/admin/commands",bundlePath:"",filename:""},userland:s})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=e=>t(t.s=e),s=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>o(53248));module.exports=s})();