"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/commands";
exports.ids = ["pages/api/admin/commands"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fcommands&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Ccommands.ts&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fcommands&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Ccommands.ts&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_commands_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\commands.ts */ \"(api-node)/./pages/api/admin/commands.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_commands_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_commands_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/commands\",\n        pathname: \"/api/admin/commands\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_admin_commands_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fcommands&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Ccommands.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/admin/commands.ts":
/*!*************************************!*\
  !*** ./pages/api/admin/commands.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nasync function handler(req, res) {\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    if (!session) return res.status(401).json({\n        error: 'Unauthorized'\n    });\n    if (!session.user.isAdmin) return res.status(403).json({\n        error: 'Forbidden'\n    });\n    const { bot, database } = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig;\n    if (!bot?.token || !bot?.clientId) return res.status(500).json({\n        error: 'Bot credentials missing'\n    });\n    const headers = {\n        Authorization: `Bot ${bot.token}`,\n        'Content-Type': 'application/json'\n    };\n    try {\n        // Connect to MongoDB\n        const client = await mongodb__WEBPACK_IMPORTED_MODULE_3__.MongoClient.connect(database.url);\n        const db = client.db(database.name);\n        const commandStates = db.collection('command_states');\n        if (req.method === 'GET') {\n            // Fetch global commands\n            const globalRes = await fetch(`https://discord.com/api/v10/applications/${bot.clientId}/commands`, {\n                headers\n            });\n            const globalCmds = globalRes.ok ? await globalRes.json() : [];\n            // Fetch guild commands if guildId present\n            let guildCmds = [];\n            if (bot.guildId) {\n                const gRes = await fetch(`https://discord.com/api/v10/applications/${bot.clientId}/guilds/${bot.guildId}/commands`, {\n                    headers\n                });\n                guildCmds = gRes.ok ? await gRes.json() : [];\n            }\n            // Fetch command states from database\n            const states = await commandStates.find({}).toArray();\n            const stateMap = new Map(states.map((s)=>[\n                    s.commandId,\n                    s.enabled\n                ]));\n            // Read addon command metadata from filesystem\n            const addonCommandMap = new Map();\n            try {\n                const possibleDirs = [\n                    path__WEBPACK_IMPORTED_MODULE_5___default().resolve(process.cwd(), '../addons'),\n                    path__WEBPACK_IMPORTED_MODULE_5___default().resolve(process.cwd(), 'src/addons'),\n                    path__WEBPACK_IMPORTED_MODULE_5___default().resolve(process.cwd(), '../../addons'),\n                    path__WEBPACK_IMPORTED_MODULE_5___default().resolve(process.cwd(), '../dist/addons'),\n                    path__WEBPACK_IMPORTED_MODULE_5___default().resolve(process.cwd(), 'dist/addons')\n                ].filter((p)=>fs__WEBPACK_IMPORTED_MODULE_4___default().existsSync(p));\n                for (const addonsDir of possibleDirs){\n                    const addonDirs = fs__WEBPACK_IMPORTED_MODULE_4___default().readdirSync(addonsDir, {\n                        withFileTypes: true\n                    }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n                    for (const addonName of addonDirs){\n                        const commandsDir = path__WEBPACK_IMPORTED_MODULE_5___default().join(addonsDir, addonName, 'commands');\n                        if (fs__WEBPACK_IMPORTED_MODULE_4___default().existsSync(commandsDir)) {\n                            const commandFiles = fs__WEBPACK_IMPORTED_MODULE_4___default().readdirSync(commandsDir).filter((file)=>file.endsWith('.ts') || file.endsWith('.js')).map((file)=>path__WEBPACK_IMPORTED_MODULE_5___default().basename(file, path__WEBPACK_IMPORTED_MODULE_5___default().extname(file)));\n                            for (const cmdName of commandFiles){\n                                addonCommandMap.set(cmdName, {\n                                    addon: addonName,\n                                    category: addonName.charAt(0).toUpperCase() + addonName.slice(1)\n                                });\n                            }\n                        }\n                    }\n                }\n            } catch (err) {\n                console.warn('Failed to read addon command metadata:', err);\n            }\n            const mapped = [\n                ...globalCmds.map((c)=>({\n                        scope: 'GLOBAL',\n                        ...c,\n                        enabled: stateMap.get(c.id) ?? true,\n                        addon: addonCommandMap.get(c.name)?.addon || 'Unknown',\n                        category: addonCommandMap.get(c.name)?.category || 'Unknown'\n                    })),\n                ...guildCmds.map((c)=>({\n                        scope: 'GUILD',\n                        ...c,\n                        enabled: stateMap.get(c.id) ?? true,\n                        addon: addonCommandMap.get(c.name)?.addon || 'Unknown',\n                        category: addonCommandMap.get(c.name)?.category || 'Unknown'\n                    }))\n            ];\n            return res.status(200).json(mapped);\n        }\n        if (req.method === 'PUT') {\n            const { commandId, enabled } = req.body;\n            if (typeof commandId !== 'string' || typeof enabled !== 'boolean') {\n                return res.status(400).json({\n                    error: 'Invalid request body'\n                });\n            }\n            // Update command state in database\n            await commandStates.updateOne({\n                commandId\n            }, {\n                $set: {\n                    commandId,\n                    enabled\n                }\n            }, {\n                upsert: true\n            });\n            // Clear cache to ensure immediate reflection of changes\n            // This could be expanded to use a proper cache invalidation mechanism\n            // For now, we'll rely on the CommandHandler's TTL cache\n            try {\n                const baseDir = process.cwd().includes(path__WEBPACK_IMPORTED_MODULE_5___default().join('src', 'dashboard')) ? path__WEBPACK_IMPORTED_MODULE_5___default().resolve(process.cwd(), '..', '..') : process.cwd();\n                const signalPath = path__WEBPACK_IMPORTED_MODULE_5___default().join(baseDir, 'command-state.signal');\n                fs__WEBPACK_IMPORTED_MODULE_4___default().writeFileSync(signalPath, JSON.stringify({\n                    commandId,\n                    enabled,\n                    requestedBy: session.user?.email || 'unknown',\n                    timestamp: Date.now()\n                }));\n            } catch (signalErr) {\n                console.warn('Failed to write command-state signal file:', signalErr);\n            }\n            return res.status(200).json({\n                commandId,\n                enabled\n            });\n        }\n        if (req.method === 'DELETE') {\n            const { commandId, scope } = req.query;\n            if (!commandId) return res.status(400).json({\n                error: 'Command ID required'\n            });\n            const endpoint = scope === 'GUILD' && bot.guildId ? `https://discord.com/api/v10/applications/${bot.clientId}/guilds/${bot.guildId}/commands/${commandId}` : `https://discord.com/api/v10/applications/${bot.clientId}/commands/${commandId}`;\n            const deleteRes = await fetch(endpoint, {\n                method: 'DELETE',\n                headers\n            });\n            if (!deleteRes.ok) {\n                const error = await deleteRes.json().catch(()=>({\n                        message: 'Unknown error'\n                    }));\n                return res.status(deleteRes.status).json(error);\n            }\n            // Remove command state from database\n            await commandStates.deleteOne({\n                commandId\n            });\n            return res.status(204).end();\n        }\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    } catch (err) {\n        console.error('Failed to handle command operation:', err);\n        return res.status(500).json({\n            error: 'Failed to handle command operation',\n            details: err.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/admin/commands.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fcommands&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Ccommands.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();