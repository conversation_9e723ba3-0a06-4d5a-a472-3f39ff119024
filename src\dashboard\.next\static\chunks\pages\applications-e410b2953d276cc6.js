(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3925],{66507:(e,i,t)=>{"use strict";t.r(i),t.d(i,{__N_SSP:()=>Z,default:()=>X});var o=t(94513),r=t(67116),s=t(1648),a=t(43700),n=t(79028),l=t(64349),c=t(5142),d=t(28365),p=t(7476),h=t(31862),u=t(15975),m=t(1871),x=t(78813),g=t(29484),b=t(46949),j=t(81139),y=t(95066),f=t(53083),w=t(24490),A=t(31840),v=t(29607),S=t(52545),z=t(30301),k=t(84748),C=t(93493),E=t(91140),T=t(57688),I=t(21181),q=t(56858),W=t(5130),R=t(52442),_=t(3037),D=t(7836),F=t(84622);t(75632),t(82273);var P=t(97119),O=t(94285),H=t(53424),M=t(35044),N=t(99500),J=t(58686);let G=[{id:"moderator",title:"Moderator Application",description:"Join our moderation team to help maintain a safe and friendly community environment.",icon:"FiShield",color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"},isOpen:!0,requiresApproval:!0,questions:[{id:"motivation",text:"Why do you want to be a moderator?",type:"text",required:!0},{id:"experience",text:"What experience do you have with community moderation?",type:"text",required:!0},{id:"scenario1",text:"How would you handle a difficult situation with a user?",type:"radio",required:!0,options:["Immediately ban them without warning","Give them a warning and explain why their behavior is inappropriate","Ignore it and let other moderators handle it","Timeout the user and delete the messages"],correctAnswer:1}],requirements:{minAge:18,minAccountAge:30,timezone:!0,availability:!0},metadata:{totalApplications:0,acceptanceRate:0,averageResponseTime:"48 hours"},quiz:[{question:"A user is spamming offensive content in multiple channels. What's your first action?",options:["Immediately ban the user","Warn them and explain why their behavior is inappropriate","Temporarily mute them and remove the offensive content","Ignore it and let other moderators handle it"],correctAnswer:2},{question:"Two users are having a heated argument that's disrupting chat. How do you handle it?",options:["Mute both users immediately","Take the discussion to a private channel and mediate","Tell them to stop or face consequences","Move their conversation to an appropriate channel and remind them of the rules"],correctAnswer:3},{question:"You notice a user sharing what might be personal information. What do you do?",options:["Delete the message and send them a warning","Temporarily mute them without explanation","Remove the message and privately explain why sharing personal info is dangerous","Publicly call them out to set an example"],correctAnswer:2},{question:"A user reports a bug in the bot. What's your response?",options:["Tell them to check if it's already reported","Document the issue and escalate to the development team","Ignore it since you're not a developer","Tell them to fix it themselves"],correctAnswer:1},{question:"You discover another moderator abusing their powers. What do you do?",options:["Confront them publicly","Remove their permissions immediately","Document the abuse and report it to senior staff privately","Ignore it to avoid conflict"],correctAnswer:2},{question:"A user is repeatedly asking for moderator roles. How do you respond?",options:["Ban them for being annoying","Direct them to the application process and explain the requirements","Give them a trial moderator role","Tell them to stop asking"],correctAnswer:1},{question:"You notice suspicious bot activity in the server. What's your first step?",options:["Shut down all bots immediately","Alert senior staff and monitor the situation","Ignore it since it's probably nothing","Delete all bot messages"],correctAnswer:1},{question:"A user claims they were wrongly banned. How do you proceed?",options:["Unban them immediately","Review the ban logs and discuss with the mod team","Tell them bans are final","Direct them to create a new account"],correctAnswer:1},{question:"Multiple users are using excessive caps and emojis. What do you do?",options:["Mute everyone involved","Delete all their messages","Send a friendly reminder about chat etiquette","Add a slowmode to the channel temporarily"],correctAnswer:2},{question:"You notice a potential raid beginning. What's your immediate action?",options:["Ban all new accounts immediately","Enable server lockdown and alert other moderators","Warn users in chat about the raid","Try to talk to the raiders"],correctAnswer:1}]},{id:"support",title:"Support Team Application",description:"Help other members with technical issues and general support inquiries.",icon:"FiHelpCircle",color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"},isOpen:!0,requiresApproval:!0,questions:[{id:"tech_experience",text:"What is your experience with technical support?",type:"text",required:!0},{id:"availability",text:"How many hours per week can you dedicate to support?",type:"select",required:!0,options:["5-10 hours","10-20 hours","20+ hours"]}],requirements:{minAge:16,timezone:!0,availability:!0},metadata:{totalApplications:0,acceptanceRate:0,averageResponseTime:"24 hours"}},{id:"developer",title:"Developer Team Application",description:"Join our development team to help improve and maintain our bot and systems.",icon:"FiCode",color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},isOpen:!1,requiresApproval:!0,questions:[{id:"programming_exp",text:"What programming languages are you proficient in?",type:"checkbox",required:!0,options:["JavaScript/TypeScript","Python","Java","C++","Other"]},{id:"github",text:"Please provide your GitHub profile URL",type:"text",required:!0}],requirements:{minAccountAge:60,availability:!0},metadata:{totalApplications:0,acceptanceRate:0,averageResponseTime:"72 hours",nextReviewDate:"2024-04-01"}}],$={log:(e,i,t)=>{},error:(e,i,t)=>{}};var Y=t(77072),B=t.n(Y);let K=B()(()=>Promise.all([t.e(2457),t.e(9784),t.e(6021),t.e(3786),t.e(1430),t.e(9498),t.e(2142),t.e(1283),t.e(4594)]).then(t.bind(t,84594)).then(e=>({default:e.ApplicationCard})),{loadableGenerated:{webpack:()=>[84594]},loading:()=>(0,o.jsx)(k.y,{size:"lg"}),ssr:!1}),L=B()(()=>t.e(8024).then(t.bind(t,38024)),{loadableGenerated:{webpack:()=>[38024]},loading:()=>(0,o.jsx)(k.y,{size:"md"}),ssr:!1}),Q=B()(()=>t.e(4364).then(t.bind(t,14364)),{loadableGenerated:{webpack:()=>[14364]},loading:()=>(0,o.jsx)(k.y,{size:"md"}),ssr:!1}),U=["Join our team and make a difference!","Great moderators are the backbone of great communities.","Help us keep the community safe and friendly.","Be the change you want to see in the community.","Together we can build something amazing!"];var Z=!0;function X(e){var i,t,Y;let{ownerIds:B}=e,{data:Z,status:X}=(0,H.useSession)(),V=(0,J.useRouter)(),ee=(0,D.d)(),ei=null==Z||null==(i=Z.user)?void 0:i.isAdmin,[et,eo]=(0,O.useState)({age:"",hoursPerWeek:"",timezone:"",motivation:"",scenarioResponses:{}}),[er,es]=(0,O.useState)([]),[ea,en]=(0,O.useState)([]),[el,ec]=(0,O.useState)(!1),[ed,ep]=(0,O.useState)(!0),[eh,eu]=(0,O.useState)(!1),{isOpen:em,onOpen:ex,onClose:eg}=(0,F.j)(),[eb,ej]=(0,O.useState)(0),[ey,ef]=(0,O.useState)(!1),[ew,eA]=(0,O.useState)(!1),[ev,eS]=(0,O.useState)(!1),[ez,ek]=(0,O.useState)(null),[eC,eE]=(0,O.useState)({types:G,userSubmissions:[],loading:!0,error:null}),[eT,eI]=(0,O.useState)(null),eq=B.includes((null==Z||null==(t=Z.user)?void 0:t.id)||""),eW=e=>{eI(e),ej(0),ex()},eR=e=>eC.userSubmissions.some(i=>i.applicationTypeId===e),e_=(0,O.useMemo)(()=>""!==et.age.trim()&&""!==et.hoursPerWeek.trim()&&""!==et.timezone.trim()&&""!==et.motivation.trim(),[et]),eD=(0,O.useMemo)(()=>Object.keys(et.scenarioResponses||{}).length>=15,[et.scenarioResponses]),eF=U[new Date().getDate()%U.length];(0,O.useEffect)(()=>{try{let e=new Date().getTimezoneOffset(),i=Math.abs(Math.floor(e/60)),t=Math.abs(e%60),o="GMT".concat(e>0?"-":"+").concat(i.toString().padStart(2,"0"),":").concat(t.toString().padStart(2,"0"));eo(e=>({...e,timezone:o}))}catch(e){eo(e=>({...e,timezone:"GMT+00:00"}))}},[]),(0,O.useEffect)(()=>{fetch("/api/applications/config").then(e=>e.json()).then(e=>{if($.log("Applications","Received config",e),!e)throw Error("No config received");ek(e);let i=Array.isArray(e.quiz)?e.quiz:[];$.log("Applications","Setting quiz data",i),es(i),eo(e=>({...e,scenarioResponses:{}})),ep(e.open)}).catch(e=>{ee({title:"Error",description:"Failed to load application configuration. Please try again.",status:"error",duration:5e3,isClosable:!0})}),ei&&fetch("/api/applications/moderation").then(e=>e.json()).then(e=>{en(Array.isArray(e)?e:[])}).catch(e=>{$.error("Applications","Failed to fetch submissions",e),en([])}),(async()=>{try{let e=await fetch("/api/applications/config"),i=await e.json();eA(i.isOpen)}catch(e){$.error("Applications","Failed to check application status",e),ee({title:"Error",description:"Failed to check if applications are open",status:"error",duration:5e3})}})()},[ei]),(0,O.useEffect)(()=>{$.log("Applications","Quiz state updated",er)},[er]),(0,O.useCallback)(e=>{eo(i=>({...i,...e}))},[]);let eP=(0,O.useCallback)(e=>{eo(i=>({...i,...e}))},[]),eO=(0,O.useCallback)(e=>{eo(i=>({...i,scenarioResponses:e}))},[]),eH=async()=>{var e;if(!(null==Z||null==(e=Z.user)?void 0:e.id))return void ee({title:"Error",description:"You must be logged in to apply.",status:"error",duration:5e3});if(!et.age||!et.hoursPerWeek||!et.timezone||!et.motivation)return void ee({title:"Missing Information",description:"Please fill in all required fields before submitting.",status:"error",duration:5e3});eS(!0);try{let e=await fetch("/api/applications/moderation",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:Z.user.id,age:parseInt(et.age),hoursPerWeek:parseInt(et.hoursPerWeek),timezone:et.timezone,motivation:et.motivation,scenarioResponses:et.scenarioResponses})});if(!e.ok){let i=await e.json();throw Error(i.error||"Failed to submit application")}ee({title:"Application submitted",description:"Your application has been submitted successfully.",status:"success",duration:5e3}),V.push("/")}catch(e){ee({title:"Error",description:e instanceof Error?e.message:"Failed to submit application",status:"error",duration:5e3})}finally{eS(!1)}},eM=async(e,i)=>{try{if(!(await fetch("/api/applications/moderation",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({applicationId:e,status:i})})).ok)throw Error("Failed to update application status");let t=await fetch("/api/applications/moderation"),o=await t.json();en(o),ee({title:"Status updated",description:"Application has been ".concat(i),status:"success",duration:3e3})}catch(e){ee({title:"Failed to update status",description:e.message,status:"error",duration:5e3})}},[eN,eJ]=(0,O.useState)({score:0,total:0,completed:!1});return"loading"!==X&&ez?Z?ez.open?(0,o.jsx)(P.A,{children:(0,o.jsxs)(p.m,{maxW:"container.xl",py:8,children:[(0,o.jsxs)(n.a,{maxW:"4xl",mx:"auto",mb:8,mt:8,bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"purple.400",boxShadow:"0 0 15px rgba(159, 122, 234, 0.4)",textAlign:"center",children:[(0,o.jsx)(x.D,{size:"2xl",bgGradient:"linear(to-r, purple.300, blue.400)",bgClip:"text",mb:4,children:"Join Our Team"}),(0,o.jsx)(W.E,{color:"gray.300",fontSize:"lg",mb:6,children:"Explore available positions and become part of our community staff"}),(0,o.jsxs)(n.a,{position:"relative",bg:"gray.900",p:6,rounded:"lg",border:"1px",borderColor:"whiteAlpha.200",children:[(0,o.jsx)(g.I,{as:N.JwJ,color:"purple.300",boxSize:6,position:"absolute",top:-3,left:-3}),(0,o.jsx)(g.I,{as:N.K9h,color:"purple.300",boxSize:6,position:"absolute",bottom:-3,right:-3}),(0,o.jsxs)(m.z,{spacing:3,justify:"center",children:[(0,o.jsx)(g.I,{as:N.y8Q,color:"purple.300",boxSize:6}),(0,o.jsx)(W.E,{fontStyle:"italic",color:"purple.200",fontSize:"md",children:eF})]})]})]}),ei&&(0,o.jsxs)(n.a,{maxW:"4xl",mx:"auto",mb:8,children:[(0,o.jsxs)(z.r,{columns:{base:1,md:3},spacing:6,children:[(0,o.jsx)(c.Z,{bg:"rgba(255,255,255,0.08)",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",children:(0,o.jsx)(d.b,{children:(0,o.jsxs)(_.T,{spacing:4,align:"stretch",children:[(0,o.jsxs)(m.z,{justify:"space-between",children:[(0,o.jsx)(x.D,{size:"md",children:"Pending"}),(0,o.jsx)(a.E,{colorScheme:"yellow",fontSize:"md",children:Array.isArray(ea)?ea.filter(e=>"pending"===e.status).length:0})]}),(0,o.jsx)(g.I,{as:N.NPF,boxSize:8,color:"yellow.400"})]})})}),(0,o.jsx)(c.Z,{bg:"rgba(255,255,255,0.08)",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",children:(0,o.jsx)(d.b,{children:(0,o.jsxs)(_.T,{spacing:4,align:"stretch",children:[(0,o.jsxs)(m.z,{justify:"space-between",children:[(0,o.jsx)(x.D,{size:"md",children:"Approved"}),(0,o.jsx)(a.E,{colorScheme:"green",fontSize:"md",children:Array.isArray(ea)?ea.filter(e=>"approved"===e.status).length:0})]}),(0,o.jsx)(g.I,{as:N.A7C,boxSize:8,color:"green.400"})]})})}),(0,o.jsx)(c.Z,{bg:"rgba(255,255,255,0.08)",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",children:(0,o.jsx)(d.b,{children:(0,o.jsxs)(_.T,{spacing:4,align:"stretch",children:[(0,o.jsxs)(m.z,{justify:"space-between",children:[(0,o.jsx)(x.D,{size:"md",children:"Rejected"}),(0,o.jsx)(a.E,{colorScheme:"red",fontSize:"md",children:Array.isArray(ea)?ea.filter(e=>"rejected"===e.status).length:0})]}),(0,o.jsx)(g.I,{as:N._Hm,boxSize:8,color:"red.400"})]})})})]}),(0,o.jsx)(l.$,{mt:6,size:"lg",w:"full",colorScheme:eh?"red":"blue",onClick:()=>eu(!eh),leftIcon:(0,o.jsx)(g.I,{as:N.kkc}),children:eh?"Hide Submissions":"View All Submissions"})]}),!ei&&(0,o.jsxs)(n.a,{maxW:"6xl",mx:"auto",mb:12,children:[(0,o.jsx)(x.D,{size:"lg",mb:6,textAlign:"center",color:"white",children:"Available Applications"}),(0,o.jsx)(z.r,{columns:{base:1,md:2,lg:3},spacing:6,children:eC.types.filter(e=>"support"!==e.id&&("developer"!==e.id||!!eq)).map(e=>(0,o.jsx)(O.Suspense,{fallback:(0,o.jsx)(k.y,{}),children:(0,o.jsx)(K,{application:e,onApply:eW,hasApplied:eR(e.id)},e.id)},e.id))})]}),eT&&(0,o.jsxs)(b.aF,{isOpen:em,onClose:()=>{eg(),eI(null)},size:"6xl",scrollBehavior:"inside",isCentered:!0,children:[(0,o.jsx)(v.m,{bg:"blackAlpha.700",backdropFilter:"blur(10px)"}),(0,o.jsxs)(f.$,{bg:"gray.800",minH:"80vh",maxH:"90vh",borderRadius:"xl",border:"1px solid",borderColor:"whiteAlpha.200",boxShadow:"2xl",children:[(0,o.jsx)(A.r,{borderBottom:"1px solid",borderColor:"whiteAlpha.200",py:6,px:8,children:(0,o.jsxs)(m.z,{justify:"space-between",align:"center",children:[(0,o.jsxs)(_.T,{align:"start",spacing:2,children:[(0,o.jsx)(x.D,{size:"lg",children:eT.title}),(0,o.jsx)(W.E,{color:"gray.400",fontSize:"md",children:eT.description})]}),(0,o.jsxs)(m.z,{spacing:4,children:[(0,o.jsxs)(a.E,{colorScheme:eT.color,fontSize:"md",px:3,py:1,borderRadius:"full",children:["Step ",eb+1," of 3"]}),(0,o.jsx)(y.s,{position:"static",size:"lg",_hover:{bg:"whiteAlpha.200"}})]})]})}),(0,o.jsx)(j.c,{p:0,children:(0,o.jsxs)(n.a,{position:"relative",children:[(0,o.jsx)(S.k,{value:(eb+1)*(100/("experimental-features"===eT.id?2:3)),size:"xs",colorScheme:eT.color,bg:"whiteAlpha.100",sx:{"& > div":{transition:"all 0.3s ease-in-out"}}}),(0,o.jsx)(n.a,{p:8,children:(0,o.jsxs)(q.t,{index:eb,onChange:e=>{(1!==e||e_)&&(2!==e||eD)&&ej(e)},variant:"unstyled",isLazy:!0,children:[(0,o.jsx)(E.w,{mb:6,children:(0,o.jsxs)(m.z,{spacing:4,width:"full",children:[(0,o.jsx)(C.o,{flex:1,py:3,_selected:{color:"white",bg:"".concat(eT.color,".500"),boxShadow:"lg"},bg:"whiteAlpha.50",borderRadius:"lg",fontWeight:"semibold",transition:"all 0.2s",_hover:{bg:0===eb?"".concat(eT.color,".500"):"whiteAlpha.100"},children:(0,o.jsxs)(m.z,{children:[(0,o.jsx)(g.I,{as:N.kkc}),(0,o.jsx)(W.E,{children:"Personal Information"})]})}),(0,o.jsx)(C.o,{flex:1,py:3,isDisabled:!e_,_selected:{color:"white",bg:"".concat(eT.color,".500"),boxShadow:"lg"},bg:"whiteAlpha.50",borderRadius:"lg",fontWeight:"semibold",transition:"all 0.2s",_hover:{bg:1===eb?"".concat(eT.color,".500"):"whiteAlpha.100"},children:(0,o.jsxs)(m.z,{children:[(0,o.jsx)(g.I,{as:N.NPF}),(0,o.jsx)(W.E,{children:"moderator"===eT.id?"Moderation Scenarios":"Application Questions"})]})}),(0,o.jsx)(C.o,{flex:1,py:3,isDisabled:!eD,_selected:{color:"white",bg:"".concat(eT.color,".500"),boxShadow:"lg"},bg:"whiteAlpha.50",borderRadius:"lg",fontWeight:"semibold",transition:"all 0.2s",_hover:{bg:2===eb?"".concat(eT.color,".500"):"whiteAlpha.100"},children:(0,o.jsxs)(m.z,{children:[(0,o.jsx)(g.I,{as:M.FiInfo}),(0,o.jsx)(W.E,{children:"Additional Information"})]})})]})}),(0,o.jsxs)(I.T,{children:[(0,o.jsx)(T.K,{p:0,children:(0,o.jsx)(O.Suspense,{fallback:(0,o.jsx)(k.y,{}),children:(0,o.jsx)(Q,{session:Z,onFormChange:eP,initialData:et})})}),(0,o.jsx)(T.K,{p:0,children:"moderator"===eT.id?(0,o.jsx)(O.Suspense,{fallback:(0,o.jsx)(k.y,{}),children:(0,o.jsx)(L,{onFormChange:eO,initialData:et.scenarioResponses})}):(0,o.jsx)(_.T,{spacing:4,align:"stretch",children:null==(Y=eT.questions)?void 0:Y.map(e=>(0,o.jsxs)(h.MJ,{isRequired:e.required,children:[(0,o.jsx)(u.l,{color:"white",children:e.text}),"text"===e.type&&(0,o.jsx)(R.T,{placeholder:"Your answer...",value:et[e.id]||"",onChange:i=>eP(e.id,i.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300"})]},e.id))})}),(0,o.jsx)(T.K,{p:0,children:(0,o.jsx)(W.E,{children:"Additional information will be shown here."})})]})]})})]})}),(0,o.jsx)(w.j,{borderTop:"1px solid",borderColor:"whiteAlpha.200",py:6,px:8,children:(0,o.jsxs)(m.z,{spacing:4,width:"full",justify:"space-between",children:[(0,o.jsxs)(m.z,{spacing:4,children:[(0,o.jsx)(g.I,{as:N.y8Q,color:"".concat(eT.color,".300"),boxSize:6}),(0,o.jsx)(W.E,{color:"gray.400",fontSize:"sm",children:"Your responses will be reviewed by our moderation team"})]}),(0,o.jsxs)(m.z,{spacing:4,children:[eb>0&&(0,o.jsx)(l.$,{onClick:()=>ej(eb-1),leftIcon:(0,o.jsx)(g.I,{as:N.kkc}),variant:"ghost",size:"lg",_hover:{bg:"whiteAlpha.100"},children:"Previous"}),0===eb&&(0,o.jsx)(l.$,{onClick:()=>ej(1),rightIcon:(0,o.jsx)(g.I,{as:N.kkc}),colorScheme:"".concat(eT.color,".500"),size:"lg",isDisabled:!e_,children:"Continue"}),1===eb&&(0,o.jsx)(l.$,{onClick:()=>ej(2),rightIcon:(0,o.jsx)(g.I,{as:N.kkc}),colorScheme:"".concat(eT.color,".500"),size:"lg",isDisabled:!eD,children:"Continue"}),2===eb&&(0,o.jsx)(l.$,{onClick:eH,leftIcon:(0,o.jsx)(g.I,{as:N.A7C}),colorScheme:"green",size:"lg",isLoading:ey,loadingText:"Submitting...",children:"Submit Application"})]})]})})]})]}),eh&&(0,o.jsxs)(b.aF,{isOpen:eh,onClose:()=>eu(!1),size:"4xl",scrollBehavior:"inside",children:[(0,o.jsx)(v.m,{backdropFilter:"blur(10px)"}),(0,o.jsxs)(f.$,{bg:"gray.800",border:"1px solid",borderColor:"whiteAlpha.300",children:[(0,o.jsx)(A.r,{children:(0,o.jsxs)(m.z,{justify:"space-between",align:"center",children:[(0,o.jsx)(x.D,{size:"lg",children:"Application Submissions"}),(0,o.jsxs)(m.z,{spacing:4,children:[(0,o.jsxs)(a.E,{colorScheme:"yellow",fontSize:"md",p:2,children:[Array.isArray(ea)?ea.filter(e=>"pending"===e.status).length:0," Pending"]}),(0,o.jsxs)(a.E,{colorScheme:"green",fontSize:"md",p:2,children:[Array.isArray(ea)?ea.filter(e=>"approved"===e.status).length:0," Approved"]}),(0,o.jsxs)(a.E,{colorScheme:"red",fontSize:"md",p:2,children:[Array.isArray(ea)?ea.filter(e=>"rejected"===e.status).length:0," Rejected"]})]})]})}),(0,o.jsx)(y.s,{}),(0,o.jsx)(j.c,{children:(0,o.jsx)(_.T,{spacing:4,align:"stretch",children:Array.isArray(ea)?ea.map(e=>(0,o.jsx)(c.Z,{bg:"rgba(255,255,255,0.08)",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"approved"===e.status?"green.400":"rejected"===e.status?"red.400":"yellow.400",rounded:"lg",overflow:"hidden",children:(0,o.jsx)(d.b,{children:(0,o.jsxs)(_.T,{spacing:4,align:"stretch",children:[(0,o.jsxs)(m.z,{justify:"space-between",children:[(0,o.jsxs)(_.T,{align:"start",spacing:1,children:[(0,o.jsx)(x.D,{size:"md",children:e.username||"Anonymous"}),(0,o.jsxs)(W.E,{fontSize:"sm",color:"gray.400",children:["Applied ",new Date(e.date).toLocaleDateString()]})]}),(0,o.jsx)(a.E,{colorScheme:"approved"===e.status?"green":"rejected"===e.status?"red":"yellow",fontSize:"md",p:2,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,o.jsxs)(n.a,{bg:"gray.900",p:4,rounded:"md",children:[(0,o.jsx)(W.E,{fontWeight:"bold",mb:2,children:"Why do you want to be a moderator?"}),(0,o.jsx)(W.E,{color:"gray.300",children:e.motivation||"No response"})]}),(0,o.jsxs)(z.r,{columns:{base:1,md:2},spacing:4,children:[(0,o.jsxs)(n.a,{children:[(0,o.jsx)(W.E,{fontWeight:"bold",mb:2,children:"Age"}),(0,o.jsxs)(W.E,{color:"gray.300",children:[e.age," years old"]})]}),(0,o.jsxs)(n.a,{children:[(0,o.jsx)(W.E,{fontWeight:"bold",mb:2,children:"Hours per Week"}),(0,o.jsxs)(W.E,{color:"gray.300",children:[e.hoursPerWeek," hours"]})]}),(0,o.jsxs)(n.a,{children:[(0,o.jsx)(W.E,{fontWeight:"bold",mb:2,children:"Timezone"}),(0,o.jsx)(W.E,{color:"gray.300",children:e.timezone})]})]}),e.scenarioResponses&&(0,o.jsxs)(n.a,{bg:"gray.900",p:4,rounded:"md",children:[(0,o.jsx)(W.E,{fontWeight:"bold",mb:2,children:"Scenario Responses"}),(0,o.jsx)(W.E,{color:"gray.300",children:JSON.stringify(e.scenarioResponses)})]}),"pending"===e.status&&(0,o.jsxs)(m.z,{spacing:4,justify:"flex-end",children:[(0,o.jsx)(l.$,{colorScheme:"green",onClick:()=>eM(e._id,"approved"),leftIcon:(0,o.jsx)(g.I,{as:N.A7C}),children:"Approve"}),(0,o.jsx)(l.$,{colorScheme:"red",onClick:()=>eM(e._id,"rejected"),leftIcon:(0,o.jsx)(g.I,{as:N._Hm}),children:"Reject"})]})]})})},e._id)):[]})})]})]})]})}):(0,o.jsx)(P.A,{children:(0,o.jsx)(n.a,{w:"full",p:4,children:(0,o.jsxs)(n.a,{maxW:"4xl",mx:"auto",mb:8,mt:8,bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"red.400",boxShadow:"0 0 15px rgba(255, 0, 0, 0.2)",textAlign:"center",children:[(0,o.jsx)(x.D,{size:"2xl",bgGradient:"linear(to-r, red.300, orange.400)",bgClip:"text",mb:4,children:"Applications Closed"}),(0,o.jsx)(W.E,{color:"gray.300",fontSize:"lg",children:"We are not accepting new applications at this time. Please check back later!"})]})})}):(0,o.jsx)(P.A,{children:(0,o.jsx)(p.m,{maxW:"container.md",py:8,children:(0,o.jsxs)(r.F,{status:"warning",children:[(0,o.jsx)(s._,{}),"Please sign in to submit an application."]})})}):(0,o.jsx)(P.A,{children:(0,o.jsx)(n.a,{w:"full",p:4,children:(0,o.jsx)(p.m,{maxW:"4xl",centerContent:!0,children:(0,o.jsx)(S.k,{size:"xs",isIndeterminate:!0,w:"full",colorScheme:"blue"})})})})}},93587:(e,i,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/applications",function(){return t(66507)}])}},e=>{var i=i=>e(e.s=i);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,636,7398,1203,8792],()=>i(93587)),_N_E=e.O()}]);