(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5449],{49169:(e,r,o)=>{"use strict";o.r(r),o.d(r,{__N_SSP:()=>q,default:()=>K});var t=o(86739),i=o(94513),n=o(79028),l=o(5142),a=o(28365),s=o(1871),c=o(78813),d=o(29484),h=o(30301),p=o(24941),x=o(42398),u=o(67866),b=o(11067),g=o(30994),m=o(5130),f=o(3037),j=o(75138),v=o(7836),y=o(35044),w=o(97119);let S=[{id:"overview",title:"Overview",description:"View server statistics and general information.",icon:y.FiActivity,href:"/overview",color:"blue",gradient:{from:"rgba(49, 130, 206, 0.4)",to:"rgba(49, 130, 206, 0.1)"},accentColor:"#63B3ED"},{id:"gameservers",title:"Game Servers",description:"Manage and monitor your game servers. View status, add or edit server configurations.",icon:y.FiMonitor,href:"/gameservers",color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accentColor:"#68D391"},{id:"applications",title:"Applications",description:"Review and manage guild applications. Process new members and handle requests.",icon:y.FiPackage,href:"/applications",color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"},accentColor:"#B794F4"},{id:"tickets",title:"Support Tickets",description:"Track and manage support tickets. Respond to user inquiries and resolve issues.",icon:y.FiHelpCircle,href:"/tickets",color:"orange",gradient:{from:"rgba(237, 137, 54, 0.4)",to:"rgba(237, 137, 54, 0.1)"},accentColor:"#F6AD55"},{id:"moderation",title:"Moderation",description:"Tools and features for server moderators.",icon:y.FiLock,href:"/moderation",color:"teal",gradient:{from:"rgba(49, 151, 149, 0.4)",to:"rgba(49, 151, 149, 0.1)"},accentColor:"#4FD1C5",requiredRole:"moderator"},{id:"experimental",title:"Experimental Features",description:"Try out new features that are still in development. These may not work as expected.",icon:y.FiSettings,href:"#",color:"yellow",gradient:{from:"rgba(236, 201, 75, 0.4)",to:"rgba(236, 201, 75, 0.1)"},accentColor:"#F6E05E",experimental:!0,disabled:!0}];var k=o(83541),C=o(94285),A=o(2965),F=o(8219),E=o(39034),T=o(83415),z=o(23740),D=o(76282),_=o(99384),M=o(76079),R=o(39529),I=o(33438),L=o(53424),N=o(24251),O=o.n(N);let B=e=>{let{title:r,description:o,icon:t,href:l,color:a,gradient:h,accentColor:p,disabled:x=!1,experimental:u=!1}=e,b=l&&"#"!==l&&!x,g=(0,i.jsx)(n.a,{px:10,py:5,bg:h?"linear-gradient(135deg, ".concat(h.from,", ").concat(h.to,")"):"gray.800",borderRadius:"lg",border:"1px solid",borderColor:x?"whiteAlpha.100":"whiteAlpha.200",transition:"all 0.3s",h:"140px",minW:"360px",w:"full",overflow:"hidden",display:"flex",flexDirection:"column",cursor:b?"pointer":x?"not-allowed":"default",position:"relative",opacity:x?.6:1,_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:u?"repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0,0,0,0.1) 10px, rgba(0,0,0,0.1) 20px)":"none",opacity:.5,pointerEvents:"none"},_hover:b?{transform:"translateY(-3px)",boxShadow:"0 6px 14px ".concat(p||"var(--chakra-colors-".concat(a,"-900)"),"40"),borderColor:"".concat(a,".400"),_before:{opacity:.7}}:{},children:(0,i.jsxs)(f.T,{spacing:4,align:"start",flex:"1",justify:"flex-start",h:"full",position:"relative",zIndex:1,children:[(0,i.jsxs)(s.z,{spacing:3,children:[(0,i.jsx)(d.I,{as:t,boxSize:6,color:p||"".concat(a,".300"),filter:u?"drop-shadow(0 0 2px currentColor)":"none"}),(0,i.jsx)(c.D,{size:"md",color:"white",noOfLines:1,whiteSpace:"nowrap",children:r})]}),(0,i.jsx)(m.E,{color:x?"gray.500":"gray.300",fontSize:"sm",lineHeight:"1.4",noOfLines:3,overflow:"hidden",textOverflow:"ellipsis",flex:"1",children:o})]})});return b?(0,i.jsx)(O(),{href:l,passHref:!0,children:g}):g};var P=o(38262),Z=o(23450);function W(){let e=(0,t._)(["\n  0% { opacity: 0.3; }\n  50% { opacity: 0.7; }\n  100% { opacity: 0.3; }\n"]);return W=function(){return e},e}(0,k.i7)(W());var q=!0;function K(){let{data:e}=(0,L.useSession)(),[r,o]=(0,C.useState)(null),[t,k]=(0,C.useState)(!0),N=(0,v.d)(),{hasAccess:W,isDeveloper:q,isLoading:K}=(0,P.default)();(0,C.useEffect)(()=>{(async()=>{try{let[e,r]=await Promise.all([fetch("/api/analytics/server"),fetch("/api/analytics/bot")]);if(!e.ok||!r.ok)throw Error("Failed to fetch analytics");let[t,i]=await Promise.all([e.json(),r.json()]);o({serverStats:t.serverStats,botStats:i.botStats})}catch(e){N({title:"Error",description:"Failed to load analytics data",status:"error",duration:5e3}),o({serverStats:{totalMembers:0,onlineMembers:0,totalChannels:0,totalRoles:0},botStats:{commandsToday:0,uptime:"Unknown",responseTime:"0ms",activeAddons:0,inactiveAddons:0}})}finally{k(!1)}})()},[N]);let U=['"Talk is cheap. Show me the code." – Linus Torvalds','"Programs must be written for people to read, and only incidentally for machines to execute." – Harold Abelson','"Any fool can write code that a computer can understand. Good programmers write code that humans can understand." – Martin Fowler','"First, solve the problem. Then, write the code." – John Johnson','"404 Chill Not Found? Keep calm and debug on." – Unknown',"It's not a bug – it's an undocumented feature.",'"The best error message is the one that never shows up." – Thomas Fuchs',"Code is like humor. When you have to explain it, it's bad.",'"Experience is the name everyone gives to their mistakes." – Oscar Wilde','"In order to be irreplaceable, one must always be different." – Coco Chanel'];U[new Date().getDate()%U.length];let G=S.filter(r=>{var o,t;return"admin"===r.requiredRole?null==e||null==(o=e.user)?void 0:o.isAdmin:"moderator"===r.requiredRole?null==e||null==(t=e.user)?void 0:t.isModerator:"overview"!==r.id&&"experimental"!==r.id}),H=r?[{name:"Text",value:r.serverStats.textChannels||0,color:"#4299E1"},{name:"Voice",value:r.serverStats.voiceChannels||0,color:"#48BB78"},{name:"Categories",value:r.serverStats.categories||0,color:"#9F7AEA"}]:[],J=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],V=J.map(e=>({day:e,commands:0,joins:0,leaves:0})),X=r?J.map(e=>{var o,t,i,n;let l=(null==(t=r.botStats)||null==(o=t.weeklyActivity)?void 0:o.find(r=>r.day===e))||{},a=(null==(n=r.serverStats)||null==(i=n.weeklyMembers)?void 0:i.find(r=>r.day===e))||{};return{day:e,commands:l.commands||0,joins:a.joins||0,leaves:a.leaves||0}}):V;return(0,i.jsx)(w.A,{children:(0,i.jsxs)(n.a,{p:8,position:"relative",_before:{content:'""',position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"100%",height:"100%",background:"radial-gradient(circle at center, rgba(66, 153, 225, 0.1) 0%, transparent 70%)",pointerEvents:"none"},children:[(0,i.jsx)(n.a,{position:"absolute",top:0,left:0,width:"100%",height:"100%",pointerEvents:"none",opacity:.05,sx:{"@keyframes glow":{"0%":{opacity:.03},"50%":{opacity:.07},"100%":{opacity:.03}},animation:"glow 4s infinite"},fontSize:"3xl",fontFamily:"monospace",color:"blue.200",textAlign:"center",pt:20,children:"ORACLE"}),(0,i.jsxs)(f.T,{spacing:8,mb:8,children:[(0,i.jsxs)(c.D,{size:"lg",textAlign:"center",display:"flex",alignItems:"center",justifyContent:"center",children:[(0,i.jsx)("span",{style:{marginRight:"0.5rem"},role:"img","aria-label":"chart",children:"\uD83D\uDCCA"}),(0,i.jsx)(n.a,{as:"span",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",children:"Server Analytics"})]}),(0,i.jsx)(h.r,{columns:{base:1,md:2,lg:4},spacing:6,w:"full",children:t?Array.from({length:4}).map((e,r)=>(0,i.jsx)(l.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.b,{children:(0,i.jsx)(p.E,{height:"80px"})})},r)):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(l.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.b,{children:(0,i.jsxs)(x.r,{children:[(0,i.jsxs)(s.z,{children:[(0,i.jsx)(d.I,{as:y.FiUsers,color:"blue.400",boxSize:6}),(0,i.jsx)(b.v,{color:"gray.300",children:"Total Members"})]}),(0,i.jsx)(g.k,{color:"white",fontSize:"2xl",children:(null==r?void 0:r.serverStats.totalMembers.toLocaleString())||"0"}),(0,i.jsxs)(u.h,{color:"green.400",children:[(0,i.jsx)(d.I,{as:y.FiTrendingUp,mr:1}),(null==r?void 0:r.serverStats.onlineMembers)||"0"," online"]}),(0,i.jsxs)(u.h,{color:"green.300",children:["+",(null==r?void 0:r.serverStats.newMembersToday)||0," joined"]}),(0,i.jsxs)(u.h,{color:"red.400",children:["-",(null==r?void 0:r.serverStats.leftMembersToday)||0," left"]})]})})}),(0,i.jsx)(l.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.b,{children:(0,i.jsxs)(x.r,{children:[(0,i.jsxs)(s.z,{children:[(0,i.jsx)(d.I,{as:y.FiMessageSquare,color:"green.400",boxSize:6}),(0,i.jsx)(b.v,{color:"gray.300",children:"Channels"})]}),(0,i.jsx)(g.k,{color:"white",fontSize:"2xl",children:(null==r?void 0:r.serverStats.totalChannels)||"0"}),(0,i.jsxs)(u.h,{color:"gray.400",children:[(null==r?void 0:r.serverStats.totalRoles)||"0"," roles"]})]})})}),(0,i.jsx)(l.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.b,{children:(0,i.jsxs)(x.r,{children:[(0,i.jsxs)(s.z,{children:[(0,i.jsx)(d.I,{as:y.FiActivity,color:"purple.400",boxSize:6}),(0,i.jsx)(b.v,{color:"gray.300",children:"Commands Today"})]}),(0,i.jsx)(g.k,{color:"white",fontSize:"2xl",children:(null==r?void 0:r.botStats.commandsToday)||"0"}),(0,i.jsxs)(u.h,{color:"gray.400",children:[(null==r?void 0:r.botStats.responseTime)||"0ms"," avg response"]})]})})}),(0,i.jsx)(l.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.b,{children:(0,i.jsxs)(x.r,{children:[(0,i.jsxs)(s.z,{children:[(0,i.jsx)(d.I,{as:y.FiServer,color:"orange.400",boxSize:6}),(0,i.jsx)(b.v,{color:"gray.300",children:"Bot Uptime"})]}),(0,i.jsx)(g.k,{color:"white",fontSize:"xl",children:(null==r?void 0:r.botStats.uptime)||"Unknown"}),(0,i.jsxs)(u.h,{color:"green.400",children:[(null==r?void 0:r.botStats.activeAddons)||"0"," addons active"]}),(0,i.jsxs)(u.h,{color:"red.400",children:[(null==r?void 0:r.botStats.inactiveAddons)||"0"," addons inactive"]})]})})}),(null==r?void 0:r.botStats.errorsToday)>0&&(0,i.jsx)(Z.N,{as:O(),href:"/admin/errors",_hover:{textDecoration:"none"},w:"full",children:(0,i.jsx)(l.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",borderColor:"red.400",borderWidth:"1px",cursor:"pointer",_hover:{transform:"translateY(-4px)",boxShadow:"0 4px 12px rgba(0,0,0,0.2)",borderColor:"red.500"},children:(0,i.jsx)(a.b,{children:(0,i.jsxs)(x.r,{children:[(0,i.jsxs)(s.z,{children:[(0,i.jsx)(d.I,{as:y.FiAlertCircle,color:"red.400",boxSize:6}),(0,i.jsx)(b.v,{color:"gray.300",children:"Errors Today"})]}),(0,i.jsx)(g.k,{color:"red.400",fontSize:"2xl",children:r.botStats.errorsToday}),(0,i.jsx)(u.h,{color:"red.300",children:"Needs attention"})]})})})})]})})]}),!t&&r&&(0,i.jsxs)(f.T,{spacing:8,mb:8,children:[(0,i.jsxs)(c.D,{size:"lg",textAlign:"center",display:"flex",alignItems:"center",justifyContent:"center",children:[(0,i.jsx)("span",{style:{marginRight:"0.5rem"},role:"img","aria-label":"graph",children:"\uD83D\uDCC8"}),(0,i.jsx)(n.a,{as:"span",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",children:"Activity Overview"})]}),(0,i.jsxs)(h.r,{columns:{base:1,lg:2},spacing:8,w:"full",children:[(0,i.jsx)(l.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.b,{children:(0,i.jsxs)(f.T,{spacing:4,children:[(0,i.jsx)(c.D,{size:"md",color:"white",children:"Channel Distribution"}),(0,i.jsx)(n.a,{h:"200px",w:"full",children:(0,i.jsx)(_.u,{width:"100%",height:"100%",children:(0,i.jsxs)(D.r,{children:[(0,i.jsx)(z.F,{data:H,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:5,dataKey:"value",children:H.map((e,r)=>(0,i.jsx)(T.f,{fill:e.color},"cell-".concat(r)))}),(0,i.jsx)(M.m,{wrapperStyle:{backgroundColor:"transparent"},contentStyle:{backgroundColor:"rgba(26, 32, 44, 0.9)",border:"1px solid rgba(255,255,255,0.2)",borderRadius:"8px",color:"#fff"},itemStyle:{color:"#fff"},labelStyle:{color:"#fff"}})]})})}),(0,i.jsx)(s.z,{spacing:4,justify:"center",children:H.map((e,r)=>(0,i.jsxs)(s.z,{spacing:2,children:[(0,i.jsx)(n.a,{w:"3",h:"3",bg:e.color,rounded:"full"}),(0,i.jsxs)(m.E,{fontSize:"sm",color:"gray.300",children:[e.name,": ",e.value]})]},r))})]})})}),(0,i.jsx)(l.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.b,{children:(0,i.jsxs)(f.T,{spacing:4,children:[(0,i.jsx)(c.D,{size:"md",color:"white",children:"Weekly Activity"}),(0,i.jsx)(n.a,{h:"200px",w:"full",children:(0,i.jsx)(_.u,{width:"100%",height:"100%",children:(0,i.jsxs)(F.E,{data:X,children:[(0,i.jsx)(E.d,{strokeDasharray:"3 3",stroke:"rgba(255,255,255,0.1)"}),(0,i.jsx)(R.W,{dataKey:"day",axisLine:!1,tickLine:!1,tick:{fill:"#A0AEC0",fontSize:12}}),(0,i.jsx)(I.h,{axisLine:!1,tickLine:!1,tick:{fill:"#A0AEC0",fontSize:12}}),(0,i.jsx)(M.m,{wrapperStyle:{backgroundColor:"transparent"},contentStyle:{backgroundColor:"rgba(26, 32, 44, 0.9)",border:"1px solid rgba(255,255,255,0.2)",borderRadius:"8px",color:"#fff"},itemStyle:{color:"#fff"},labelStyle:{color:"#fff"},cursor:{fill:"rgba(255,255,255,0.08)"}}),(0,i.jsx)(A.y,{dataKey:"commands",fill:"#4299E1",name:"Commands"}),(0,i.jsx)(A.y,{dataKey:"joins",fill:"#48BB78",name:"Joins"}),(0,i.jsx)(A.y,{dataKey:"leaves",fill:"#E53E3E",name:"Leaves"})]})})})]})})})]})]}),(0,i.jsx)(j.B,{spacing:"24px",justify:"start",children:G.map(e=>(0,i.jsx)(j.Q,{flex:"1 0 260px",children:(0,i.jsx)(n.a,{onClick:()=>window.dispatchEvent(new CustomEvent("colorClick",{detail:e.color})),children:(0,i.jsx)(B,{...e})})},e.id))})]})})}},54067:(e,r,o)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/overview",function(){return o(49169)}])}},e=>{var r=r=>e(e.s=r);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,636,7398,1203,8792],()=>r(54067)),_N_E=e.O()}]);