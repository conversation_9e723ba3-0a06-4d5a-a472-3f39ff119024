"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6021],{7476:(e,t,n)=>{n.d(t,{m:()=>l});var i=n(94513),r=n(56005),o=n(79435),s=n(84756),a=n(8475),d=n(21533);let l=(0,s.R)(function(e,t){let{className:n,centerContent:s,...l}=(0,r.MN)(e),u=(0,a.V)("Container",e);return(0,i.jsx)(d.B.div,{ref:t,className:(0,o.cx)("chakra-container",n),...l,__css:{...u,...s&&{display:"flex",flexDirection:"column",alignItems:"center"}}})});l.displayName="Container"},13596:(e,t,n)=>{n.d(t,{v:()=>s});var i=n(94513);n(56084);var r=n(94285);let o=(0,r.createContext)({getDocument:()=>document,getWindow:()=>window});function s(e){let{children:t,environment:n,disabled:s}=e,a=(0,r.useRef)(null),d=(0,r.useMemo)(()=>n||{getDocument:()=>a.current?.ownerDocument??document,getWindow:()=>a.current?.ownerDocument.defaultView??window},[n]),l=!s||!n;return(0,i.jsxs)(o.Provider,{value:d,children:[t,l&&(0,i.jsx)("span",{id:"__chakra_env",hidden:!0,ref:a})]})}o.displayName="EnvironmentContext",s.displayName="EnvironmentProvider"},16128:(e,t,n)=>{n.d(t,{R:()=>a,r:()=>d});var i=n(94513),r=n(83541);let o=String.raw,s=o`
  :root,
  :host {
    --chakra-vh: 100vh;
  }

  @supports (height: -webkit-fill-available) {
    :root,
    :host {
      --chakra-vh: -webkit-fill-available;
    }
  }

  @supports (height: -moz-fill-available) {
    :root,
    :host {
      --chakra-vh: -moz-fill-available;
    }
  }

  @supports (height: 100dvh) {
    :root,
    :host {
      --chakra-vh: 100dvh;
    }
  }
`,a=()=>(0,i.jsx)(r.mL,{styles:s}),d=({scope:e=""})=>(0,i.jsx)(r.mL,{styles:o`
      html {
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        font-family: system-ui, sans-serif;
        -webkit-font-smoothing: antialiased;
        text-rendering: optimizeLegibility;
        -moz-osx-font-smoothing: grayscale;
        touch-action: manipulation;
      }

      body {
        position: relative;
        min-height: 100%;
        margin: 0;
        font-feature-settings: "kern";
      }

      ${e} :where(*, *::before, *::after) {
        border-width: 0;
        border-style: solid;
        box-sizing: border-box;
        word-wrap: break-word;
      }

      main {
        display: block;
      }

      ${e} hr {
        border-top-width: 1px;
        box-sizing: content-box;
        height: 0;
        overflow: visible;
      }

      ${e} :where(pre, code, kbd,samp) {
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, monospace;
        font-size: 1em;
      }

      ${e} a {
        background-color: transparent;
        color: inherit;
        text-decoration: inherit;
      }

      ${e} abbr[title] {
        border-bottom: none;
        text-decoration: underline;
        -webkit-text-decoration: underline dotted;
        text-decoration: underline dotted;
      }

      ${e} :where(b, strong) {
        font-weight: bold;
      }

      ${e} small {
        font-size: 80%;
      }

      ${e} :where(sub,sup) {
        font-size: 75%;
        line-height: 0;
        position: relative;
        vertical-align: baseline;
      }

      ${e} sub {
        bottom: -0.25em;
      }

      ${e} sup {
        top: -0.5em;
      }

      ${e} img {
        border-style: none;
      }

      ${e} :where(button, input, optgroup, select, textarea) {
        font-family: inherit;
        font-size: 100%;
        line-height: 1.15;
        margin: 0;
      }

      ${e} :where(button, input) {
        overflow: visible;
      }

      ${e} :where(button, select) {
        text-transform: none;
      }

      ${e} :where(
          button::-moz-focus-inner,
          [type="button"]::-moz-focus-inner,
          [type="reset"]::-moz-focus-inner,
          [type="submit"]::-moz-focus-inner
        ) {
        border-style: none;
        padding: 0;
      }

      ${e} fieldset {
        padding: 0.35em 0.75em 0.625em;
      }

      ${e} legend {
        box-sizing: border-box;
        color: inherit;
        display: table;
        max-width: 100%;
        padding: 0;
        white-space: normal;
      }

      ${e} progress {
        vertical-align: baseline;
      }

      ${e} textarea {
        overflow: auto;
      }

      ${e} :where([type="checkbox"], [type="radio"]) {
        box-sizing: border-box;
        padding: 0;
      }

      ${e} input[type="number"]::-webkit-inner-spin-button,
      ${e} input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none !important;
      }

      ${e} input[type="number"] {
        -moz-appearance: textfield;
      }

      ${e} input[type="search"] {
        -webkit-appearance: textfield;
        outline-offset: -2px;
      }

      ${e} input[type="search"]::-webkit-search-decoration {
        -webkit-appearance: none !important;
      }

      ${e} ::-webkit-file-upload-button {
        -webkit-appearance: button;
        font: inherit;
      }

      ${e} details {
        display: block;
      }

      ${e} summary {
        display: list-item;
      }

      template {
        display: none;
      }

      [hidden] {
        display: none !important;
      }

      ${e} :where(
          blockquote,
          dl,
          dd,
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          hr,
          figure,
          p,
          pre
        ) {
        margin: 0;
      }

      ${e} button {
        background: transparent;
        padding: 0;
      }

      ${e} fieldset {
        margin: 0;
        padding: 0;
      }

      ${e} :where(ol, ul) {
        margin: 0;
        padding: 0;
      }

      ${e} textarea {
        resize: vertical;
      }

      ${e} :where(button, [role="button"]) {
        cursor: pointer;
      }

      ${e} button::-moz-focus-inner {
        border: 0 !important;
      }

      ${e} table {
        border-collapse: collapse;
      }

      ${e} :where(h1, h2, h3, h4, h5, h6) {
        font-size: inherit;
        font-weight: inherit;
      }

      ${e} :where(button, input, optgroup, select, textarea) {
        padding: 0;
        line-height: inherit;
        color: inherit;
      }

      ${e} :where(img, svg, video, canvas, audio, iframe, embed, object) {
        display: block;
      }

      ${e} :where(img, video) {
        max-width: 100%;
        height: auto;
      }

      [data-js-focus-visible]
        :focus:not([data-focus-visible-added]):not(
          [data-focus-visible-disabled]
        ) {
        outline: none;
        box-shadow: none;
      }

      ${e} select::-ms-expand {
        display: none;
      }

      ${s}
    `})},21966:(e,t,n)=>{n.d(t,{an:()=>f});var i,r,o=n(94513),s=n(79435),a=n(83541),d=n(94285),l=n(88142);let u={light:"chakra-ui-light",dark:"chakra-ui-dark"},c="chakra-ui-color-mode",h=function(e){return{ssr:!1,type:"localStorage",get(t){let n;if(!globalThis?.document)return t;try{n=localStorage.getItem(e)||t}catch(e){}return n||t},set(t){try{localStorage.setItem(e,t)}catch(e){}}}}(c);i=0;let m=()=>{},b=(0,s.Bd)()?d.useLayoutEffect:d.useEffect;function p(e,t){return"cookie"===e.type&&e.ssr?e.get(t):t}let f=function(e){let{value:t,children:n,options:{useSystemColorMode:i,initialColorMode:r,disableTransitionOnChange:s}={},colorModeManager:c=h}=e,f=(0,a.Iz)(),g="dark"===r?"dark":"light",[v,y]=(0,d.useState)(()=>p(c,g)),[x,w]=(0,d.useState)(()=>p(c)),{getSystemTheme:k,setClassName:N,setDataset:C,addListener:$}=(0,d.useMemo)(()=>(function(e={}){let{preventTransition:t=!0,nonce:n}=e,i={setDataset:e=>{let n=t?i.preventTransition():void 0;document.documentElement.dataset.theme=e,document.documentElement.style.colorScheme=e,n?.()},setClassName(e){document.body.classList.add(e?u.dark:u.light),document.body.classList.remove(e?u.light:u.dark)},query:()=>window.matchMedia("(prefers-color-scheme: dark)"),getSystemTheme:e=>i.query().matches??"dark"===e?"dark":"light",addListener(e){let t=i.query(),n=t=>{e(t.matches?"dark":"light")};return"function"==typeof t.addListener?t.addListener(n):t.addEventListener("change",n),()=>{"function"==typeof t.removeListener?t.removeListener(n):t.removeEventListener("change",n)}},preventTransition(){let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),void 0!==n&&(e.nonce=n),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),requestAnimationFrame(()=>{requestAnimationFrame(()=>{document.head.removeChild(e)})})}}};return i})({preventTransition:s,nonce:f?.nonce}),[s,f?.nonce]),I="system"!==r||v?v:x,E=(0,d.useCallback)(e=>{let t="system"===e?k():e;y(t),N("dark"===t),C(t),c.set(t)},[c,k,N,C]);b(()=>{"system"===r&&w(k())},[]),(0,d.useEffect)(()=>{let e=c.get();return e?void E(e):"system"===r?void E("system"):void E(g)},[c,g,r,E]);let O=(0,d.useCallback)(()=>{E("dark"===I?"light":"dark")},[I,E]);(0,d.useEffect)(()=>{if(i)return $(E)},[i,$,E]);let M=(0,d.useMemo)(()=>({colorMode:t??I,toggleColorMode:t?m:O,setColorMode:t?m:E,forced:void 0!==t}),[I,O,E,t]);return(0,o.jsx)(l.Ig.Provider,{value:M,children:n})};f.displayName="ColorModeProvider"},26725:(e,t,n)=>{n.d(t,{Q:()=>r});var i=n(94285);function r(e){let t=i.version;return"string"!=typeof t||t.startsWith("18.")?e?.ref:e?.props?.ref}},36468:(e,t,n)=>{n.d(t,{c:()=>l});var i=n(94513),r=n(56005),o=n(79435),s=n(84756),a=n(8475),d=n(21533);let l=(0,s.R)(function(e,t){let{borderLeftWidth:n,borderBottomWidth:s,borderTopWidth:l,borderRightWidth:u,borderWidth:c,borderStyle:h,borderColor:m,...b}=(0,a.V)("Divider",e),{className:p,orientation:f="horizontal",__css:g,...v}=(0,r.MN)(e);return(0,i.jsx)(d.B.hr,{ref:t,"aria-orientation":f,...v,__css:{...b,border:"0",borderColor:m,borderStyle:h,...{vertical:{borderLeftWidth:n||u||c||"1px",height:"100%"},horizontal:{borderBottomWidth:s||l||c||"1px",width:"100%"}}[f],...g},className:(0,o.cx)("chakra-divider",p)})});l.displayName="Divider"},52080:(e,t,n)=>{n.d(t,{C:()=>l});var i=n(94513),r=n(56005),o=n(79435),s=n(84756),a=n(8475),d=n(21533);let l=(0,s.R)(function(e,t){let n=(0,a.V)("Code",e),{className:s,...l}=(0,r.MN)(e);return(0,i.jsx)(d.B.code,{ref:t,className:(0,o.cx)("chakra-code",e.className),...l,__css:{display:"inline-block",...n}})});l.displayName="Code"},55588:(e,t,n)=>{n.d(t,{D:()=>f});var i=n(56084),r=n(79435),o=n(94285);function s(e){return e.sort((e,t)=>{let n=e.compareDocumentPosition(t);if(n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY)return -1;if(n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS)return 1;if(!(n&Node.DOCUMENT_POSITION_DISCONNECTED)&&!(n&Node.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC))return 0;throw Error("Cannot sort the given nodes.")})}let a=e=>"object"==typeof e&&"nodeType"in e&&e.nodeType===Node.ELEMENT_NODE;function d(e,t,n){let i=e+1;return n&&i>=t&&(i=0),i}function l(e,t,n){let i=e-1;return n&&i<0&&(i=t),i}let u="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,c=e=>e;var h=Object.defineProperty,m=(e,t,n)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,b=(e,t,n)=>(m(e,"symbol"!=typeof t?t+"":t,n),n);class p{constructor(){b(this,"descendants",new Map),b(this,"register",e=>{if(null!=e)return a(e)?this.registerNode(e):t=>{this.registerNode(t,e)}}),b(this,"unregister",e=>{this.descendants.delete(e);let t=s(Array.from(this.descendants.keys()));this.assignIndex(t)}),b(this,"destroy",()=>{this.descendants.clear()}),b(this,"assignIndex",e=>{this.descendants.forEach(t=>{let n=e.indexOf(t.node);t.index=n,t.node.dataset.index=t.index.toString()})}),b(this,"count",()=>this.descendants.size),b(this,"enabledCount",()=>this.enabledValues().length),b(this,"values",()=>Array.from(this.descendants.values()).sort((e,t)=>e.index-t.index)),b(this,"enabledValues",()=>this.values().filter(e=>!e.disabled)),b(this,"item",e=>{if(0!==this.count())return this.values()[e]}),b(this,"enabledItem",e=>{if(0!==this.enabledCount())return this.enabledValues()[e]}),b(this,"first",()=>this.item(0)),b(this,"firstEnabled",()=>this.enabledItem(0)),b(this,"last",()=>this.item(this.descendants.size-1)),b(this,"lastEnabled",()=>{let e=this.enabledValues().length-1;return this.enabledItem(e)}),b(this,"indexOf",e=>e?this.descendants.get(e)?.index??-1:-1),b(this,"enabledIndexOf",e=>null==e?-1:this.enabledValues().findIndex(t=>t.node.isSameNode(e))),b(this,"next",(e,t=!0)=>{let n=d(e,this.count(),t);return this.item(n)}),b(this,"nextEnabled",(e,t=!0)=>{let n=this.item(e);if(!n)return;let i=d(this.enabledIndexOf(n.node),this.enabledCount(),t);return this.enabledItem(i)}),b(this,"prev",(e,t=!0)=>{let n=l(e,this.count()-1,t);return this.item(n)}),b(this,"prevEnabled",(e,t=!0)=>{let n=this.item(e);if(!n)return;let i=l(this.enabledIndexOf(n.node),this.enabledCount()-1,t);return this.enabledItem(i)}),b(this,"registerNode",(e,t)=>{if(!e||this.descendants.has(e))return;let n=s(Array.from(this.descendants.keys()).concat(e));t?.disabled&&(t.disabled=!!t.disabled);let i={node:e,index:-1,...t};this.descendants.set(e,i),this.assignIndex(n)})}}function f(){let[e,t]=(0,r.q6)({name:"DescendantsProvider",errorMessage:"useDescendantsContext must be used within DescendantsProvider"});return[e,t,()=>{let e=(0,o.useRef)(new p);return u(()=>()=>e.current.destroy()),e.current},e=>{let n=t(),[r,s]=(0,o.useState)(-1),a=(0,o.useRef)(null);u(()=>()=>{a.current&&n.unregister(a.current)},[]),u(()=>{if(!a.current)return;let e=Number(a.current.dataset.index);r==e||Number.isNaN(e)||s(e)});let d=e?c(n.register(e)):c(n.register);return{descendants:n,index:r,enabledIndex:n.enabledIndexOf(a.current),register:(0,i.Px)(d,a)}}]}},88142:(e,t,n)=>{n.d(t,{G6:()=>o,Ig:()=>r,dU:()=>s});var i=n(94285);let r=(0,i.createContext)({});function o(){let e=(0,i.useContext)(r);if(void 0===e)throw Error("useColorMode must be used within a ColorModeProvider");return e}function s(e,t){let{colorMode:n}=o();return"dark"===n?t:e}r.displayName="ColorModeContext"},94218:(e,t,n)=>{n.d(t,{oY:()=>a});var i=n(29326),r=n(79435);function o(e){return"function"==typeof e}let s=e=>function(...t){let n=[...t],s=t[t.length-1];return(0,i.UP)(s)&&n.length>1?n=n.slice(0,n.length-1):s=e,(function(...e){return t=>e.reduce((e,t)=>t(e),t)})(...n.map(e=>t=>o(e)?e(t):function(...e){return(0,r.XQ)({},...e,d)}(t,e)))(s)},a=s(i.w4);function d(e,t,n,i){return(o(e)||o(t))&&Object.prototype.hasOwnProperty.call(i,n)?(...n)=>{let i=o(e)?e(...n):e,s=o(t)?t(...n):t;return(0,r.XQ)({},i,s,d)}:(0,r.Gv)(e)&&(0,r.cy)(t)||(0,r.cy)(e)&&(0,r.Gv)(t)?t:void 0}s(i.$7)}}]);