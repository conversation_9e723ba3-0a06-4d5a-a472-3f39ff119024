"use strict";exports.id=6099,exports.ids=[6099],exports.modules={4235:(e,o,n)=>{n.a(e,async(e,s)=>{try{n.d(o,{A:()=>u});var r=n(8732),i=n(82015),t=n(66909),a=n(15994),c=n(3052),l=n(3001),d=e([t,a,l]);[t,a,l]=d.then?(await d)():d;let h={command:[{name:"{command.name}",description:"Command name that was executed",icon:"⚡"},{name:"{command.user}",description:"User who executed the command",icon:"\uD83D\uDC64"},{name:"{command.channel}",description:"Channel where command was executed",icon:"\uD83D\uDCFA"},{name:"{command.server}",description:"Server where command was executed",icon:"\uD83C\uDFE0"},{name:"{command.timestamp}",description:"When the command was executed",icon:"⏰"}],options:[{name:"{option.name}",description:"Value of a specific option",icon:"\uD83D\uDD27"},{name:"{option.user}",description:"User option value",icon:"\uD83D\uDC64"},{name:"{option.channel}",description:"Channel option value",icon:"\uD83D\uDCFA"},{name:"{option.role}",description:"Role option value",icon:"\uD83C\uDFAD"},{name:"{option.string}",description:"String option value",icon:"\uD83D\uDCAC"},{name:"{option.number}",description:"Number option value",icon:"\uD83D\uDD22"},{name:"{option.boolean}",description:"Boolean option value",icon:"✅"}],user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.tag}",description:"User Tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{user.mention}",description:"User Mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{user.avatar}",description:"Avatar URL",icon:"\uD83D\uDDBC️"},{name:"{user.roles}",description:"User Roles",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions",icon:"\uD83D\uDD10"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel Mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel Topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server Icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{server.owner}",description:"Server Owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server Boost Level",icon:"\uD83D\uDE80"}]},x=["ADMINISTRATOR","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","KICK_MEMBERS","BAN_MEMBERS","MANAGE_MESSAGES","EMBED_LINKS","ATTACH_FILES","READ_MESSAGE_HISTORY","MENTION_EVERYONE","USE_EXTERNAL_EMOJIS","CONNECT","SPEAK","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS","USE_VAD","CHANGE_NICKNAME","MANAGE_NICKNAMES","MANAGE_WEBHOOKS","MANAGE_EMOJIS","MODERATE_MEMBERS","VIEW_AUDIT_LOG","MANAGE_EVENTS","MANAGE_THREADS","CREATE_PUBLIC_THREADS","CREATE_PRIVATE_THREADS","USE_EXTERNAL_STICKERS","SEND_MESSAGES_IN_THREADS","START_EMBEDDED_ACTIVITIES"],m=[{value:"string",label:"\uD83D\uDCDD String - Text input"},{value:"integer",label:"\uD83D\uDD22 Integer - Whole number"},{value:"number",label:"\uD83D\uDD22 Number - Decimal number"},{value:"boolean",label:"✅ Boolean - True/False"},{value:"user",label:"\uD83D\uDC64 User - Discord user"},{value:"channel",label:"\uD83D\uDCFA Channel - Discord channel"},{value:"role",label:"\uD83C\uDFAD Role - Discord role"},{value:"mentionable",label:"\uD83D\uDCE2 Mentionable - User or role"},{value:"attachment",label:"\uD83D\uDCCE Attachment - File upload"}],p=(0,i.memo)(({data:e,selected:o,id:n,updateNodeData:s})=>{let{currentScheme:d}=(0,l.DP)(),{isOpen:p,onOpen:u,onClose:g}=(0,a.useDisclosure)(),[b,j]=(0,i.useState)(()=>({guildOnly:!1,adminOnly:!1,allowDMs:!1,cooldown:0,options:[],category:"general",examples:[],permissions:[],ephemeral:!1,deferReply:!1,...e})),[S,v]=(0,i.useState)(!1),f=e=>{j(o=>({...o,...e}))},C=(e,o)=>{let n=[...b.options||[]];n[e]={...n[e],...o},f({options:n})},y=e=>{let o=(b.options||[]).filter((o,n)=>n!==e);f({options:o})},T=e=>{let o=[...b.options||[]];o[e].choices||(o[e].choices=[]),o[e].choices.push({name:"",value:""}),f({options:o})},k=(e,o,n,s)=>{let r=[...b.options||[]];r[e].choices&&(r[e].choices[o][n]=s,f({options:r}))},A=(e,o)=>{let n=[...b.options||[]];n[e].choices&&(n[e].choices=n[e].choices.filter((e,n)=>n!==o),f({options:n}))},w=e=>{navigator.clipboard.writeText(e)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(a.Box,{bg:d.colors.surface,border:`2px solid ${o?"#3b82f6":d.colors.border}`,borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,r.jsx)(t.Handle,{type:"target",position:t.Position.Top,style:{background:"#3b82f6",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,r.jsxs)(a.VStack,{spacing:1,align:"stretch",children:[(0,r.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,r.jsxs)(a.HStack,{spacing:1,children:[(0,r.jsx)(a.Box,{bg:"blue.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,r.jsx)(c.FrA,{})}),(0,r.jsx)(a.Text,{fontSize:"xs",fontWeight:"bold",color:d.colors.text,children:"Command"})]}),(0,r.jsx)(a.IconButton,{icon:(0,r.jsx)(c.VSk,{}),size:"xs",variant:"ghost",onClick:u,"aria-label":"Configure command"})]}),(0,r.jsx)(a.Box,{children:(0,r.jsxs)(a.Text,{fontSize:"xs",color:d.colors.text,noOfLines:1,children:["/",b.commandName||"unnamed"]})}),b.description&&(0,r.jsx)(a.Box,{children:(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,noOfLines:1,children:b.description.length>25?b.description.substring(0,25)+"...":b.description})}),(0,r.jsxs)(a.HStack,{spacing:1,flexWrap:"wrap",children:[(b.options?.length??0)>0&&(0,r.jsxs)(a.Badge,{size:"xs",colorScheme:"blue",children:[b.options?.length," option",(b.options?.length??0)!==1?"s":""]}),b.adminOnly&&(0,r.jsx)(a.Badge,{size:"xs",colorScheme:"red",children:"Admin"}),b.cooldown&&b.cooldown>0&&(0,r.jsxs)(a.Badge,{size:"xs",colorScheme:"orange",children:[b.cooldown,"s"]})]})]}),(0,r.jsx)(t.Handle,{type:"source",position:t.Position.Bottom,style:{background:"#3b82f6",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,r.jsxs)(a.Modal,{isOpen:p,onClose:()=>{s&&n&&s(n,b),g()},size:"4xl",children:[(0,r.jsx)(a.ModalOverlay,{bg:"blackAlpha.600"}),(0,r.jsxs)(a.ModalContent,{bg:d.colors.background,border:"2px solid",borderColor:"blue.400",maxW:"1200px",children:[(0,r.jsx)(a.ModalHeader,{color:d.colors.text,children:"⚡ Configure Command"}),(0,r.jsx)(a.ModalCloseButton,{}),(0,r.jsx)(a.ModalBody,{pb:6,children:(0,r.jsxs)(a.VStack,{spacing:6,align:"stretch",children:[(0,r.jsxs)(a.Box,{children:[(0,r.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Available Variables"}),(0,r.jsxs)(a.Button,{size:"sm",variant:"ghost",leftIcon:S?(0,r.jsx)(c._NO,{}):(0,r.jsx)(c.Vap,{}),onClick:()=>v(!S),children:[S?"Hide":"Show"," Variables"]})]}),(0,r.jsxs)(a.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,r.jsx)(a.AlertIcon,{}),(0,r.jsx)(a.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your command responses! Click any variable below to copy it. Variables are replaced with actual values when your command runs."})]}),(0,r.jsx)(a.Collapse,{in:S,animateOpacity:!0,children:(0,r.jsx)(a.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,r.jsx)(a.Accordion,{allowMultiple:!0,children:Object.entries(h).map(([e,o])=>(0,r.jsxs)(a.AccordionItem,{border:"none",children:[(0,r.jsxs)(a.AccordionButton,{px:0,py:2,children:[(0,r.jsx)(a.Box,{flex:"1",textAlign:"left",children:(0,r.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,textTransform:"capitalize",children:[e," Variables"]})}),(0,r.jsx)(a.AccordionIcon,{})]}),(0,r.jsx)(a.AccordionPanel,{px:0,py:2,children:(0,r.jsx)(a.VStack,{spacing:2,align:"stretch",children:o.map(e=>(0,r.jsxs)(a.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>w(e.name),children:[(0,r.jsx)(a.Text,{fontSize:"sm",children:e.icon}),(0,r.jsx)(a.Code,{fontSize:"xs",colorScheme:"blue",children:e.name}),(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,flex:"1",children:e.description}),(0,r.jsx)(a.IconButton,{icon:(0,r.jsx)(c.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),w(e.name)}})]},e.name))})})]},e))})})})]}),(0,r.jsx)(a.Divider,{}),(0,r.jsxs)(a.Tabs,{variant:"enclosed",colorScheme:"blue",children:[(0,r.jsxs)(a.TabList,{children:[(0,r.jsx)(a.Tab,{children:"Basic Info"}),(0,r.jsx)(a.Tab,{children:"Options"}),(0,r.jsx)(a.Tab,{children:"Permissions"}),(0,r.jsx)(a.Tab,{children:"Advanced"})]}),(0,r.jsxs)(a.TabPanels,{children:[(0,r.jsx)(a.TabPanel,{children:(0,r.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,r.jsxs)(a.FormControl,{isRequired:!0,children:[(0,r.jsx)(a.FormLabel,{color:d.colors.text,children:"Command Name"}),(0,r.jsxs)(a.InputGroup,{children:[(0,r.jsx)(a.InputLeftAddon,{bg:d.colors.surface,color:d.colors.text,children:"/"}),(0,r.jsx)(a.Input,{value:b.commandName||"",onChange:e=>f({commandName:e.target.value}),placeholder:"ping",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),(0,r.jsxs)(a.FormControl,{children:[(0,r.jsx)(a.FormLabel,{color:d.colors.text,children:"Category"}),(0,r.jsxs)(a.Select,{value:b.category||"general",onChange:e=>f({category:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:[(0,r.jsx)("option",{value:"general",children:"General"}),(0,r.jsx)("option",{value:"moderation",children:"Moderation"}),(0,r.jsx)("option",{value:"fun",children:"Fun"}),(0,r.jsx)("option",{value:"utility",children:"Utility"}),(0,r.jsx)("option",{value:"admin",children:"Admin"}),(0,r.jsx)("option",{value:"info",children:"Info"}),(0,r.jsx)("option",{value:"music",children:"Music"}),(0,r.jsx)("option",{value:"games",children:"Games"})]})]})]}),(0,r.jsxs)(a.FormControl,{isRequired:!0,children:[(0,r.jsx)(a.FormLabel,{color:d.colors.text,children:"Description"}),(0,r.jsx)(a.Textarea,{value:b.description||"",onChange:e=>f({description:e.target.value}),placeholder:"What does this command do?",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"})]}),(0,r.jsxs)(a.FormControl,{children:[(0,r.jsx)(a.FormLabel,{color:d.colors.text,children:"Usage Examples"}),(0,r.jsx)(a.Textarea,{value:b.examples?.join("\n")||"",onChange:e=>f({examples:e.target.value.split("\n").filter(e=>e.trim())}),placeholder:`/ping
/ping server
/ping {user.mention}`,bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"}),(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"One example per line"})]})]})}),(0,r.jsx)(a.TabPanel,{children:(0,r.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,r.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Command Options"}),(0,r.jsx)(a.Button,{leftIcon:(0,r.jsx)(c.GGD,{}),onClick:()=>{f({options:[...b.options||[],{name:"",description:"",type:"string",required:!1,choices:[]}]})},colorScheme:"blue",size:"sm",children:"Add Option"})]}),(0,r.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,r.jsx)(a.AlertIcon,{}),(0,r.jsx)(a.AlertDescription,{fontSize:"sm",children:"Options are parameters users can provide with your command. They appear as autocomplete fields in Discord."})]}),(0,r.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[b.options?.map((e,o)=>(0,r.jsxs)(a.Box,{p:4,bg:d.colors.surface,borderRadius:"md",border:"1px solid",borderColor:d.colors.border,children:[(0,r.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:3,children:[(0,r.jsxs)(a.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,children:["Option ",o+1]}),(0,r.jsx)(a.IconButton,{icon:(0,r.jsx)(c.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>y(o),"aria-label":"Remove option"})]}),(0,r.jsxs)(a.VStack,{spacing:3,align:"stretch",children:[(0,r.jsxs)(a.SimpleGrid,{columns:2,spacing:3,children:[(0,r.jsxs)(a.FormControl,{isRequired:!0,children:[(0,r.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Option Name"}),(0,r.jsx)(a.Input,{value:e.name,onChange:e=>C(o,{name:e.target.value}),placeholder:"user",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]}),(0,r.jsxs)(a.FormControl,{isRequired:!0,children:[(0,r.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Option Type"}),(0,r.jsx)(a.Select,{value:e.type,onChange:e=>C(o,{type:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm",children:m.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]})]}),(0,r.jsxs)(a.FormControl,{children:[(0,r.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Description"}),(0,r.jsx)(a.Input,{value:e.description,onChange:e=>C(o,{description:e.target.value}),placeholder:"The user to ping",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]}),(0,r.jsxs)(a.HStack,{children:[(0,r.jsx)(a.Switch,{isChecked:e.required,onChange:e=>C(o,{required:e.target.checked}),colorScheme:"blue"}),(0,r.jsx)(a.Text,{fontSize:"sm",color:d.colors.text,children:"Required option"})]}),("string"===e.type||"integer"===e.type||"number"===e.type)&&(0,r.jsxs)(a.Box,{children:[(0,r.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Predefined Choices (Optional)"}),(0,r.jsx)(a.Button,{size:"xs",leftIcon:(0,r.jsx)(c.GGD,{}),onClick:()=>T(o),colorScheme:"blue",variant:"ghost",children:"Add Choice"})]}),(0,r.jsx)(a.VStack,{spacing:2,align:"stretch",children:e.choices?.map((e,n)=>(0,r.jsxs)(a.HStack,{spacing:2,children:[(0,r.jsx)(a.Input,{value:e.name,onChange:e=>k(o,n,"name",e.target.value),placeholder:"Choice name",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"}),(0,r.jsx)(a.Input,{value:e.value,onChange:e=>k(o,n,"value",e.target.value),placeholder:"Choice value",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"}),(0,r.jsx)(a.IconButton,{icon:(0,r.jsx)(c.QLg,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>A(o,n),"aria-label":"Remove choice"})]},n))})]})]})]},o)),(!b.options||0===b.options.length)&&(0,r.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,r.jsx)(a.AlertIcon,{}),(0,r.jsx)(a.AlertDescription,{children:"No options configured. Your command will work without any parameters."})]})]})]})}),(0,r.jsx)(a.TabPanel,{children:(0,r.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,r.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Command Permissions"}),(0,r.jsxs)(a.Alert,{status:"warning",borderRadius:"md",children:[(0,r.jsx)(a.AlertIcon,{}),(0,r.jsx)(a.AlertDescription,{fontSize:"sm",children:"Be careful with permissions! Overly restrictive permissions can prevent legitimate users from using your command."})]}),(0,r.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(a.HStack,{spacing:4,children:[(0,r.jsx)(a.Switch,{isChecked:b.adminOnly,onChange:e=>f({adminOnly:e.target.checked}),colorScheme:"red"}),(0,r.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Admin Only"}),(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Only server administrators can use this command"})]})]}),(0,r.jsxs)(a.HStack,{spacing:4,children:[(0,r.jsx)(a.Switch,{isChecked:b.guildOnly,onChange:e=>f({guildOnly:e.target.checked}),colorScheme:"blue"}),(0,r.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Server Only"}),(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Command can only be used in servers, not DMs"})]})]}),(0,r.jsxs)(a.HStack,{spacing:4,children:[(0,r.jsx)(a.Switch,{isChecked:b.allowDMs,onChange:e=>f({allowDMs:e.target.checked}),colorScheme:"green"}),(0,r.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Allow DMs"}),(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Command can be used in direct messages"})]})]})]}),(0,r.jsx)(a.Divider,{}),(0,r.jsxs)(a.Box,{children:[(0,r.jsx)(a.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,mb:3,children:"Required Permissions"}),(0,r.jsx)(a.Text,{fontSize:"sm",color:d.colors.textSecondary,mb:3,children:"Select the Discord permissions users need to use this command"}),(0,r.jsx)(a.CheckboxGroup,{value:b.permissions||[],onChange:e=>f({permissions:e}),children:(0,r.jsx)(a.SimpleGrid,{columns:3,spacing:2,children:x.map(e=>(0,r.jsx)(a.Checkbox,{value:e,colorScheme:"blue",size:"sm",children:(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.text,children:e.replace(/_/g," ").toLowerCase().replace(/\b\w/g,e=>e.toUpperCase())})},e))})})]})]})}),(0,r.jsx)(a.TabPanel,{children:(0,r.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,r.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Advanced Settings"}),(0,r.jsxs)(a.FormControl,{children:[(0,r.jsx)(a.FormLabel,{color:d.colors.text,children:"Cooldown (seconds)"}),(0,r.jsxs)(a.NumberInput,{value:b.cooldown||0,onChange:e=>f({cooldown:parseInt(e)||0}),min:0,max:3600,children:[(0,r.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,r.jsxs)(a.NumberInputStepper,{children:[(0,r.jsx)(a.NumberIncrementStepper,{}),(0,r.jsx)(a.NumberDecrementStepper,{})]})]}),(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"How long users must wait between uses (0 = no cooldown)"})]}),(0,r.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(a.HStack,{spacing:4,children:[(0,r.jsx)(a.Switch,{isChecked:b.ephemeral,onChange:e=>f({ephemeral:e.target.checked}),colorScheme:"blue"}),(0,r.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Ephemeral Response"}),(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Command response is only visible to the user who ran it"})]})]}),(0,r.jsxs)(a.HStack,{spacing:4,children:[(0,r.jsx)(a.Switch,{isChecked:b.deferReply,onChange:e=>f({deferReply:e.target.checked}),colorScheme:"orange"}),(0,r.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Defer Reply"}),(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:'Show "thinking..." message while processing (for slow commands)'})]})]})]})]})})]})]}),(0,r.jsx)(a.Button,{colorScheme:"blue",onClick:()=>{e.commandName=b.commandName,e.description=b.description,e.options=b.options,e.permissions=b.permissions,e.cooldown=b.cooldown,e.guildOnly=b.guildOnly,e.adminOnly=b.adminOnly,e.allowDMs=b.allowDMs,e.category=b.category,e.examples=b.examples,e.ephemeral=b.ephemeral,e.deferReply=b.deferReply,e.label=b.commandName?`/${b.commandName}`:"Command",g()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});p.displayName="CommandNode";let u=p;s()}catch(e){s(e)}})},30303:(e,o,n)=>{n.a(e,async(e,s)=>{try{n.d(o,{A:()=>u});var r=n(8732),i=n(82015),t=n(66909),a=n(98645),c=n(82055),l=n(3001),d=e([t,a,l]);[t,a,l]=d.then?(await d)():d;let h={user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.roles}",description:"User Roles (array)",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions (array)",icon:"\uD83D\uDD10"},{name:"{user.isBot}",description:"Is Bot (true/false)",icon:"\uD83E\uDD16"},{name:"{user.createdAt}",description:"Account Creation Date",icon:"\uD83D\uDCC5"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.nsfw}",description:"Is NSFW (true/false)",icon:"\uD83D\uDD1E"},{name:"{channel.memberCount}",description:"Member Count (number)",icon:"\uD83D\uDC65"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.memberCount}",description:"Total Members (number)",icon:"\uD83D\uDC65"},{name:"{server.boostLevel}",description:"Boost Level (number)",icon:"\uD83D\uDE80"},{name:"{server.owner}",description:"Server Owner ID",icon:"\uD83D\uDC51"}],message:[{name:"{message.content}",description:"Message Content",icon:"\uD83D\uDCAC"},{name:"{message.length}",description:"Message Length (number)",icon:"\uD83D\uDCCF"},{name:"{message.mentions}",description:"Message Mentions (array)",icon:"\uD83D\uDCE2"},{name:"{message.attachments}",description:"Attachments Count (number)",icon:"\uD83D\uDCCE"},{name:"{message.embeds}",description:"Embeds Count (number)",icon:"\uD83D\uDCCB"}],api:[{name:"{response.status}",description:"HTTP Status Code (number)",icon:"\uD83D\uDD22"},{name:"{response.data}",description:"Response Data",icon:"\uD83D\uDCCA"},{name:"{response.error}",description:"Error Message",icon:"❌"},{name:"{response.length}",description:"Response Array Length",icon:"\uD83D\uDCCF"}],time:[{name:"{time.hour}",description:"Current Hour (0-23)",icon:"\uD83D\uDD50"},{name:"{time.day}",description:"Day of Week (0-6)",icon:"\uD83D\uDCC5"},{name:"{time.date}",description:"Current Date",icon:"\uD83D\uDCC6"},{name:"{time.timestamp}",description:"Unix Timestamp",icon:"⏰"}],random:[{name:"{random.number}",description:"Random Number (1-100)",icon:"\uD83C\uDFB2"},{name:"{random.boolean}",description:"Random True/False",icon:"\uD83C\uDFAF"}]},x=[{value:"userHasRole",label:"\uD83C\uDFAD User Has Role",category:"User",description:"Check if user has a specific role"},{value:"userIsAdmin",label:"\uD83D\uDC51 User Is Admin",category:"User",description:"Check if user is server admin"},{value:"userHasPermission",label:"\uD83D\uDD10 User Has Permission",category:"User",description:"Check if user has specific permission"},{value:"userIsBot",label:"\uD83E\uDD16 User Is Bot",category:"User",description:"Check if user is a bot"},{value:"userJoinedRecently",label:"\uD83D\uDEAA User Joined Recently",category:"User",description:"Check if user joined within timeframe"},{value:"messageContains",label:"\uD83D\uDCAC Message Contains",category:"Message",description:"Check if message contains text"},{value:"messageLength",label:"\uD83D\uDCCF Message Length",category:"Message",description:"Check message character count"},{value:"messageHasMentions",label:"\uD83D\uDCE2 Message Has Mentions",category:"Message",description:"Check if message mentions users/roles"},{value:"messageHasAttachments",label:"\uD83D\uDCCE Message Has Attachments",category:"Message",description:"Check if message has files"},{value:"messageHasEmbeds",label:"\uD83D\uDCCB Message Has Embeds",category:"Message",description:"Check if message has embeds"},{value:"channelType",label:"\uD83D\uDCFA Channel Type",category:"Channel",description:"Check channel type (text, voice, etc.)"},{value:"channelIsNSFW",label:"\uD83D\uDD1E Channel Is NSFW",category:"Channel",description:"Check if channel is NSFW"},{value:"channelMemberCount",label:"\uD83D\uDC65 Channel Member Count",category:"Channel",description:"Check voice channel member count"},{value:"serverMemberCount",label:"\uD83D\uDC65 Server Member Count",category:"Server",description:"Check total server members"},{value:"serverBoostLevel",label:"\uD83D\uDE80 Server Boost Level",category:"Server",description:"Check server boost level"},{value:"serverName",label:"\uD83C\uDFE0 Server Name",category:"Server",description:"Check server name"},{value:"timeOfDay",label:"\uD83D\uDD50 Time of Day",category:"Time",description:"Check current hour of day"},{value:"dayOfWeek",label:"\uD83D\uDCC5 Day of Week",category:"Time",description:"Check day of the week"},{value:"apiResponseStatus",label:"\uD83D\uDD22 API Response Status",category:"API",description:"Check HTTP status code"},{value:"apiResponseData",label:"\uD83D\uDCCA API Response Data",category:"API",description:"Check API response content"},{value:"customVariable",label:"⚙️ Custom Variable",category:"Custom",description:"Check custom variable value"},{value:"randomChance",label:"\uD83C\uDFB2 Random Chance",category:"Custom",description:"Random percentage chance"}],m=[{value:"equals",label:"= Equals",description:"Exact match"},{value:"notEquals",label:"≠ Not Equals",description:"Does not match"},{value:"contains",label:"\uD83D\uDD0D Contains",description:"Contains substring"},{value:"notContains",label:"\uD83D\uDEAB Not Contains",description:"Does not contain substring"},{value:"startsWith",label:"▶️ Starts With",description:"Begins with text"},{value:"endsWith",label:"◀️ Ends With",description:"Ends with text"},{value:"greaterThan",label:"> Greater Than",description:"Numeric greater than"},{value:"lessThan",label:"< Less Than",description:"Numeric less than"},{value:"greaterEqual",label:"≥ Greater or Equal",description:"Numeric greater than or equal"},{value:"lessEqual",label:"≤ Less or Equal",description:"Numeric less than or equal"},{value:"regex",label:"\uD83D\uDD0D Regex Match",description:"Regular expression pattern"},{value:"inArray",label:"\uD83D\uDCCB In Array",description:"Value exists in array"},{value:"hasLength",label:"\uD83D\uDCCF Has Length",description:"Array/string has specific length"}],p=(0,i.memo)(({data:e,selected:o,id:n,updateNodeData:s})=>{let{currentScheme:d}=(0,l.DP)(),{isOpen:p,onOpen:u,onClose:g}=(0,a.useDisclosure)(),[b,j]=(0,i.useState)(()=>({operator:"equals",logicalOperator:"AND",caseSensitive:!1,conditions:[],...e})),[S,v]=(0,i.useState)(!1),f=e=>{j(o=>({...o,...e}))},C=e=>{let o=x.find(o=>o.value===e);return o?o.label.split(" ").slice(1).join(" "):e},y=e=>{navigator.clipboard.writeText(e)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(a.Box,{bg:d.colors.surface,border:`2px solid ${o?"#f59e0b":d.colors.border}`,borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,r.jsx)(t.Handle,{type:"target",position:t.Position.Top,style:{background:"#f59e0b",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,r.jsxs)(a.VStack,{spacing:1,align:"stretch",children:[(0,r.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,r.jsxs)(a.HStack,{spacing:1,children:[(0,r.jsx)(a.Box,{bg:"orange.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,r.jsx)(c.lrG,{})}),(0,r.jsx)(a.Text,{fontSize:"xs",fontWeight:"bold",color:d.colors.text,children:"Condition"})]}),(0,r.jsx)(a.IconButton,{icon:(0,r.jsx)(c.VSk,{}),size:"xs",variant:"ghost",onClick:u,"aria-label":"Configure condition"})]}),(0,r.jsx)(a.Box,{children:(0,r.jsxs)(a.HStack,{spacing:1,children:[b.conditionType&&(0,r.jsx)(a.Text,{fontSize:"xs",children:(e=>{let o=x.find(o=>o.value===e);return o?o.label.split(" ")[0]:"❓"})(b.conditionType)}),(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.text,noOfLines:1,children:b.conditionType?C(b.conditionType):"Select Condition"})]})}),b.operator&&b.value&&(0,r.jsx)(a.Box,{children:(0,r.jsxs)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,noOfLines:1,children:[(e=>m.find(o=>o.value===e)?.label||e)(b.operator).split(" ").slice(1).join(" "),' "',b.value.length>15?b.value.substring(0,15)+"...":b.value,'"']})}),(0,r.jsxs)(a.HStack,{spacing:1,justify:"space-between",children:[(0,r.jsx)(a.Badge,{size:"xs",colorScheme:"green",children:"TRUE"}),(0,r.jsx)(a.Badge,{size:"xs",colorScheme:"red",children:"FALSE"})]})]}),(0,r.jsx)(t.Handle,{type:"source",position:t.Position.Bottom,id:"true",style:{background:"#38a169",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",bottom:"-6px",left:"25%",transform:"translateX(-50%)"}}),(0,r.jsx)(t.Handle,{type:"source",position:t.Position.Bottom,id:"false",style:{background:"#e53e3e",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",bottom:"-6px",left:"75%",transform:"translateX(-50%)"}})]}),(0,r.jsxs)(a.Modal,{isOpen:p,onClose:()=>{s&&n&&s(n,b),g()},size:"4xl",children:[(0,r.jsx)(a.ModalOverlay,{bg:"blackAlpha.600"}),(0,r.jsxs)(a.ModalContent,{bg:d.colors.background,border:"2px solid",borderColor:"orange.400",maxW:"1200px",children:[(0,r.jsx)(a.ModalHeader,{color:d.colors.text,children:"❓ Configure Condition"}),(0,r.jsx)(a.ModalCloseButton,{}),(0,r.jsx)(a.ModalBody,{pb:6,children:(0,r.jsxs)(a.VStack,{spacing:6,align:"stretch",children:[(0,r.jsxs)(a.Box,{children:[(0,r.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Available Variables"}),(0,r.jsxs)(a.Button,{size:"sm",variant:"ghost",leftIcon:S?(0,r.jsx)(c._NO,{}):(0,r.jsx)(c.Vap,{}),onClick:()=>v(!S),children:[S?"Hide":"Show"," Variables"]})]}),(0,r.jsxs)(a.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,r.jsx)(a.AlertIcon,{}),(0,r.jsx)(a.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your conditions! Click any variable below to copy it. Conditions determine which path (TRUE or FALSE) the flow takes."})]}),(0,r.jsx)(a.Collapse,{in:S,animateOpacity:!0,children:(0,r.jsx)(a.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,r.jsx)(a.Accordion,{allowMultiple:!0,children:Object.entries(h).map(([e,o])=>(0,r.jsxs)(a.AccordionItem,{border:"none",children:[(0,r.jsxs)(a.AccordionButton,{px:0,py:2,children:[(0,r.jsx)(a.Box,{flex:"1",textAlign:"left",children:(0,r.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,textTransform:"capitalize",children:[e," Variables"]})}),(0,r.jsx)(a.AccordionIcon,{})]}),(0,r.jsx)(a.AccordionPanel,{px:0,py:2,children:(0,r.jsx)(a.VStack,{spacing:2,align:"stretch",children:o.map(e=>(0,r.jsxs)(a.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>y(e.name),children:[(0,r.jsx)(a.Text,{fontSize:"sm",children:e.icon}),(0,r.jsx)(a.Code,{fontSize:"xs",colorScheme:"orange",children:e.name}),(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,flex:"1",children:e.description}),(0,r.jsx)(a.IconButton,{icon:(0,r.jsx)(c.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),y(e.name)}})]},e.name))})})]},e))})})})]}),(0,r.jsx)(a.Divider,{}),(0,r.jsxs)(a.Tabs,{variant:"enclosed",colorScheme:"orange",children:[(0,r.jsxs)(a.TabList,{children:[(0,r.jsx)(a.Tab,{children:"Basic Condition"}),(0,r.jsx)(a.Tab,{children:"Advanced"})]}),(0,r.jsxs)(a.TabPanels,{children:[(0,r.jsx)(a.TabPanel,{children:(0,r.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(a.FormControl,{isRequired:!0,children:[(0,r.jsx)(a.FormLabel,{color:d.colors.text,children:"Condition Type"}),(0,r.jsx)(a.Select,{value:b.conditionType||"",onChange:e=>f({conditionType:e.target.value}),placeholder:"Select a condition type",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:Object.entries(x.reduce((e,o)=>(e[o.category]||(e[o.category]=[]),e[o.category].push(o),e),{})).map(([e,o])=>(0,r.jsx)("optgroup",{label:e,children:o.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))},e))})]}),b.conditionType&&(0,r.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,r.jsx)(a.AlertIcon,{}),(0,r.jsxs)(a.Box,{children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:x.find(e=>e.value===b.conditionType)?.label}),(0,r.jsx)(a.Text,{fontSize:"sm",children:x.find(e=>e.value===b.conditionType)?.description})]})]}),(0,r.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,r.jsxs)(a.FormControl,{children:[(0,r.jsx)(a.FormLabel,{color:d.colors.text,children:"Operator"}),(0,r.jsx)(a.Select,{value:b.operator||"equals",onChange:e=>f({operator:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:m.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)(a.FormControl,{children:[(0,r.jsx)(a.FormLabel,{color:d.colors.text,children:"Compare Value"}),(0,r.jsx)(a.Input,{value:b.value||"",onChange:e=>f({value:e.target.value}),placeholder:"userHasRole"===b.conditionType?"Member or {user.roles}":"messageContains"===b.conditionType?"hello or {message.content}":"serverMemberCount"===b.conditionType?"100 or {server.memberCount}":"timeOfDay"===b.conditionType?"14 (for 2 PM)":"Value to compare against",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),(0,r.jsxs)(a.FormControl,{children:[(0,r.jsx)(a.FormLabel,{color:d.colors.text,children:"Description"}),(0,r.jsx)(a.Textarea,{value:b.description||"",onChange:e=>f({description:e.target.value}),placeholder:"Describe what this condition checks for",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"})]}),(0,r.jsxs)(a.Alert,{status:"warning",borderRadius:"md",children:[(0,r.jsx)(a.AlertIcon,{}),(0,r.jsxs)(a.Box,{children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:"Understanding TRUE/FALSE Paths"}),(0,r.jsx)(a.Text,{fontSize:"sm",children:"• **TRUE (Left)**: Actions that run when the condition passes • **FALSE (Right)**: Actions that run when the condition fails • You can connect different actions to each path"})]})]})]})}),(0,r.jsx)(a.TabPanel,{children:(0,r.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,r.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Advanced Settings"}),(0,r.jsx)(a.VStack,{spacing:4,align:"stretch",children:(0,r.jsxs)(a.HStack,{spacing:4,children:[(0,r.jsx)(a.Switch,{isChecked:b.caseSensitive,onChange:e=>f({caseSensitive:e.target.checked}),colorScheme:"orange"}),(0,r.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Case Sensitive"}),(0,r.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Match exact capitalization for text comparisons"})]})]})}),(0,r.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,r.jsx)(a.AlertIcon,{}),(0,r.jsxs)(a.Box,{children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:2,children:"\uD83D\uDCA1 Condition Examples:"}),(0,r.jsxs)(a.VStack,{align:"start",spacing:1,fontSize:"sm",children:[(0,r.jsx)(a.Text,{children:'• Check if user has "Admin" role: **User Has Role** equals "Admin"'}),(0,r.jsx)(a.Text,{children:'• Check if message contains swear word: **Message Contains** contains "badword"'}),(0,r.jsx)(a.Text,{children:"• Check if server has many members: **Server Member Count** greater than 1000"}),(0,r.jsx)(a.Text,{children:"• Check if it's nighttime: **Time of Day** greater than 22"}),(0,r.jsx)(a.Text,{children:"• Check API status: **API Response Status** equals 200"})]})]})]}),(0,r.jsxs)(a.Alert,{status:"warning",borderRadius:"md",children:[(0,r.jsx)(a.AlertIcon,{}),(0,r.jsxs)(a.Box,{children:[(0,r.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:2,children:"⚠️ Important Notes:"}),(0,r.jsxs)(a.VStack,{align:"start",spacing:1,fontSize:"sm",children:[(0,r.jsxs)(a.Text,{children:["• Use variables like ","{user.roles}"," to check dynamic values"]}),(0,r.jsx)(a.Text,{children:"• Number comparisons work with Greater/Less Than operators"}),(0,r.jsx)(a.Text,{children:"• Text comparisons work with Contains, Starts With, etc."}),(0,r.jsx)(a.Text,{children:"• Always connect both TRUE and FALSE paths for complete logic"})]})]})]})]})})]})]}),(0,r.jsx)(a.Button,{colorScheme:"orange",onClick:()=>{e.conditionType=b.conditionType,e.operator=b.operator,e.value=b.value,e.caseSensitive=b.caseSensitive,e.description=b.description,e.logicalOperator=b.logicalOperator,e.label=b.conditionType?C(b.conditionType):"Condition",g()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});p.displayName="ConditionNode";let u=p;s()}catch(e){s(e)}})}};