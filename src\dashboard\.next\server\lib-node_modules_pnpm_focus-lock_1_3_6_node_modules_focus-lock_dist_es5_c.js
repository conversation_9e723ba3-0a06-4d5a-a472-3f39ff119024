"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c";
exports.ids = ["lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/commands.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/commands.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.focusOn = void 0;\nvar focusOn = function (target, focusOptions) {\n    if (!target) {\n        // not clear how, but is possible https://github.com/theKashey/focus-lock/issues/53\n        return;\n    }\n    if ('focus' in target) {\n        target.focus(focusOptions);\n    }\n    if ('contentWindow' in target && target.contentWindow) {\n        target.contentWindow.focus();\n    }\n};\nexports.focusOn = focusOn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzNS9jb21tYW5kcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZm9jdXMtbG9ja0AxLjMuNlxcbm9kZV9tb2R1bGVzXFxmb2N1cy1sb2NrXFxkaXN0XFxlczVcXGNvbW1hbmRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5mb2N1c09uID0gdm9pZCAwO1xudmFyIGZvY3VzT24gPSBmdW5jdGlvbiAodGFyZ2V0LCBmb2N1c09wdGlvbnMpIHtcbiAgICBpZiAoIXRhcmdldCkge1xuICAgICAgICAvLyBub3QgY2xlYXIgaG93LCBidXQgaXMgcG9zc2libGUgaHR0cHM6Ly9naXRodWIuY29tL3RoZUthc2hleS9mb2N1cy1sb2NrL2lzc3Vlcy81M1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIGlmICgnZm9jdXMnIGluIHRhcmdldCkge1xuICAgICAgICB0YXJnZXQuZm9jdXMoZm9jdXNPcHRpb25zKTtcbiAgICB9XG4gICAgaWYgKCdjb250ZW50V2luZG93JyBpbiB0YXJnZXQgJiYgdGFyZ2V0LmNvbnRlbnRXaW5kb3cpIHtcbiAgICAgICAgdGFyZ2V0LmNvbnRlbnRXaW5kb3cuZm9jdXMoKTtcbiAgICB9XG59O1xuZXhwb3J0cy5mb2N1c09uID0gZm9jdXNPbjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/commands.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/constants.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/constants.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FOCUS_NO_AUTOFOCUS = exports.FOCUS_AUTO = exports.FOCUS_ALLOW = exports.FOCUS_DISABLED = exports.FOCUS_GROUP = void 0;\n/**\n * defines a focus group\n */\nexports.FOCUS_GROUP = 'data-focus-lock';\n/**\n * disables element discovery inside a group marked by key\n */\nexports.FOCUS_DISABLED = 'data-focus-lock-disabled';\n/**\n * allows uncontrolled focus within the marked area, effectively disabling focus lock for it's content\n */\nexports.FOCUS_ALLOW = 'data-no-focus-lock';\n/**\n * instructs autofocus engine to pick default autofocus inside a given node\n * can be set on the element or container\n */\nexports.FOCUS_AUTO = 'data-autofocus-inside';\n/**\n * instructs autofocus to ignore elements within a given node\n * can be set on the element or container\n */\nexports.FOCUS_NO_AUTOFOCUS = 'data-no-autofocus';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/constants.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusInside.js":
/*!*************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusInside.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.focusInside = void 0;\nvar DOMutils_1 = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js\");\nvar all_affected_1 = __webpack_require__(/*! ./utils/all-affected */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/all-affected.js\");\nvar array_1 = __webpack_require__(/*! ./utils/array */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js\");\nvar getActiveElement_1 = __webpack_require__(/*! ./utils/getActiveElement */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/getActiveElement.js\");\nvar focusInFrame = function (frame, activeElement) { return frame === activeElement; };\nvar focusInsideIframe = function (topNode, activeElement) {\n    return Boolean((0, array_1.toArray)(topNode.querySelectorAll('iframe')).some(function (node) { return focusInFrame(node, activeElement); }));\n};\n/**\n * @returns {Boolean} true, if the current focus is inside given node or nodes.\n * Supports nodes hidden inside shadowDom\n */\nvar focusInside = function (topNode, activeElement) {\n    // const activeElement = document && getActiveElement();\n    if (activeElement === void 0) { activeElement = (0, getActiveElement_1.getActiveElement)((0, array_1.getFirst)(topNode).ownerDocument); }\n    if (!activeElement || (activeElement.dataset && activeElement.dataset.focusGuard)) {\n        return false;\n    }\n    return (0, all_affected_1.getAllAffectedNodes)(topNode).some(function (node) {\n        return (0, DOMutils_1.contains)(node, activeElement) || focusInsideIframe(node, activeElement);\n    });\n};\nexports.focusInside = focusInside;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusInside.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusIsHidden.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusIsHidden.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.focusIsHidden = void 0;\nvar constants_1 = __webpack_require__(/*! ./constants */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/constants.js\");\nvar DOMutils_1 = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js\");\nvar array_1 = __webpack_require__(/*! ./utils/array */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js\");\nvar getActiveElement_1 = __webpack_require__(/*! ./utils/getActiveElement */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/getActiveElement.js\");\n/**\n * checks if focus is hidden FROM the focus-lock\n * ie contained inside a node focus-lock shall ignore\n *\n * This is a utility function coupled with {@link FOCUS_ALLOW} constant\n *\n * @returns {boolean} focus is currently is in \"allow\" area\n */\nvar focusIsHidden = function (inDocument) {\n    if (inDocument === void 0) { inDocument = document; }\n    var activeElement = (0, getActiveElement_1.getActiveElement)(inDocument);\n    if (!activeElement) {\n        return false;\n    }\n    // this does not support setting FOCUS_ALLOW within shadow dom\n    return (0, array_1.toArray)(inDocument.querySelectorAll(\"[\".concat(constants_1.FOCUS_ALLOW, \"]\"))).some(function (node) { return (0, DOMutils_1.contains)(node, activeElement); });\n};\nexports.focusIsHidden = focusIsHidden;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusIsHidden.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusSolver.js":
/*!*************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusSolver.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.focusSolver = void 0;\nvar solver_1 = __webpack_require__(/*! ./solver */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/solver.js\");\nvar DOMutils_1 = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js\");\nvar all_affected_1 = __webpack_require__(/*! ./utils/all-affected */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/all-affected.js\");\nvar array_1 = __webpack_require__(/*! ./utils/array */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js\");\nvar auto_focus_1 = __webpack_require__(/*! ./utils/auto-focus */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/auto-focus.js\");\nvar getActiveElement_1 = __webpack_require__(/*! ./utils/getActiveElement */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/getActiveElement.js\");\nvar is_1 = __webpack_require__(/*! ./utils/is */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/is.js\");\nvar parenting_1 = __webpack_require__(/*! ./utils/parenting */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/parenting.js\");\nvar reorderNodes = function (srcNodes, dstNodes) {\n    var remap = new Map();\n    // no Set(dstNodes) for IE11 :(\n    dstNodes.forEach(function (entity) { return remap.set(entity.node, entity); });\n    // remap to dstNodes\n    return srcNodes.map(function (node) { return remap.get(node); }).filter(is_1.isDefined);\n};\n/**\n * contains the main logic of the `focus-lock` package.\n *\n * ! you probably dont need this function !\n *\n * given top node(s) and the last active element returns the element to be focused next\n * @returns element which should be focused to move focus inside\n * @param topNode\n * @param lastNode\n */\nvar focusSolver = function (topNode, lastNode) {\n    var activeElement = (0, getActiveElement_1.getActiveElement)((0, array_1.asArray)(topNode).length > 0 ? document : (0, array_1.getFirst)(topNode).ownerDocument);\n    var entries = (0, all_affected_1.getAllAffectedNodes)(topNode).filter(is_1.isNotAGuard);\n    var commonParent = (0, parenting_1.getTopCommonParent)(activeElement || topNode, topNode, entries);\n    var visibilityCache = new Map();\n    var anyFocusable = (0, DOMutils_1.getFocusableNodes)(entries, visibilityCache);\n    var innerElements = anyFocusable.filter(function (_a) {\n        var node = _a.node;\n        return (0, is_1.isNotAGuard)(node);\n    });\n    if (!innerElements[0]) {\n        return undefined;\n    }\n    var outerNodes = (0, DOMutils_1.getFocusableNodes)([commonParent], visibilityCache).map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var orderedInnerElements = reorderNodes(outerNodes, innerElements);\n    // collect inner focusable and separately tabbables\n    var innerFocusables = orderedInnerElements.map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var innerTabbable = orderedInnerElements.filter(function (_a) {\n        var tabIndex = _a.tabIndex;\n        return tabIndex >= 0;\n    }).map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var newId = (0, solver_1.newFocus)(innerFocusables, innerTabbable, outerNodes, activeElement, lastNode);\n    if (newId === solver_1.NEW_FOCUS) {\n        var focusNode = \n        // first try only tabbable, and the fallback to all focusable, as long as at least one element should be picked for focus\n        (0, auto_focus_1.pickAutofocus)(anyFocusable, innerTabbable, (0, parenting_1.allParentAutofocusables)(entries, visibilityCache)) ||\n            (0, auto_focus_1.pickAutofocus)(anyFocusable, innerFocusables, (0, parenting_1.allParentAutofocusables)(entries, visibilityCache));\n        if (focusNode) {\n            return { node: focusNode };\n        }\n        else {\n            console.warn('focus-lock: cannot find any node to move focus into');\n            return undefined;\n        }\n    }\n    if (newId === undefined) {\n        return newId;\n    }\n    return orderedInnerElements[newId];\n};\nexports.focusSolver = focusSolver;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusSolver.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusables.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusables.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.expandFocusableNodes = void 0;\nvar all_affected_1 = __webpack_require__(/*! ./utils/all-affected */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/all-affected.js\");\nvar is_1 = __webpack_require__(/*! ./utils/is */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/is.js\");\nvar parenting_1 = __webpack_require__(/*! ./utils/parenting */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/parenting.js\");\nvar tabOrder_1 = __webpack_require__(/*! ./utils/tabOrder */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabOrder.js\");\nvar tabUtils_1 = __webpack_require__(/*! ./utils/tabUtils */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabUtils.js\");\n/**\n * traverses all related nodes (including groups) returning a list of all nodes(outer and internal) with meta information\n * This is low-level API!\n * @returns list of focusable elements inside a given top(!) node.\n * @see {@link getFocusableNodes} providing a simpler API\n */\nvar expandFocusableNodes = function (topNode) {\n    var entries = (0, all_affected_1.getAllAffectedNodes)(topNode).filter(is_1.isNotAGuard);\n    var commonParent = (0, parenting_1.getTopCommonParent)(topNode, topNode, entries);\n    var outerNodes = (0, tabOrder_1.orderByTabIndex)((0, tabUtils_1.getFocusables)([commonParent], true), true, true);\n    var innerElements = (0, tabUtils_1.getFocusables)(entries, false);\n    return outerNodes.map(function (_a) {\n        var node = _a.node, index = _a.index;\n        return ({\n            node: node,\n            index: index,\n            lockItem: innerElements.indexOf(node) >= 0,\n            guard: (0, is_1.isGuard)(node),\n        });\n    });\n};\nexports.expandFocusableNodes = expandFocusableNodes;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusables.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/index.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/index.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.captureFocusRestore = exports.getRelativeFocusable = exports.focusLastElement = exports.focusFirstElement = exports.focusPrevElement = exports.focusNextElement = exports.getTabbableNodes = exports.getFocusableNodes = exports.expandFocusableNodes = exports.focusSolver = exports.moveFocusInside = exports.focusIsHidden = exports.focusInside = exports.constants = void 0;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(pages-dir-node)/../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\nvar allConstants = (0, tslib_1.__importStar)(__webpack_require__(/*! ./constants */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/constants.js\"));\nvar focusInside_1 = __webpack_require__(/*! ./focusInside */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusInside.js\");\nObject.defineProperty(exports, \"focusInside\", ({ enumerable: true, get: function () { return focusInside_1.focusInside; } }));\nvar focusIsHidden_1 = __webpack_require__(/*! ./focusIsHidden */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusIsHidden.js\");\nObject.defineProperty(exports, \"focusIsHidden\", ({ enumerable: true, get: function () { return focusIsHidden_1.focusIsHidden; } }));\nvar focusSolver_1 = __webpack_require__(/*! ./focusSolver */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusSolver.js\");\nObject.defineProperty(exports, \"focusSolver\", ({ enumerable: true, get: function () { return focusSolver_1.focusSolver; } }));\nvar focusables_1 = __webpack_require__(/*! ./focusables */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusables.js\");\nObject.defineProperty(exports, \"expandFocusableNodes\", ({ enumerable: true, get: function () { return focusables_1.expandFocusableNodes; } }));\nvar moveFocusInside_1 = __webpack_require__(/*! ./moveFocusInside */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/moveFocusInside.js\");\nObject.defineProperty(exports, \"moveFocusInside\", ({ enumerable: true, get: function () { return moveFocusInside_1.moveFocusInside; } }));\nvar return_focus_1 = __webpack_require__(/*! ./return-focus */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/return-focus.js\");\nObject.defineProperty(exports, \"captureFocusRestore\", ({ enumerable: true, get: function () { return return_focus_1.captureFocusRestore; } }));\nvar sibling_1 = __webpack_require__(/*! ./sibling */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/sibling.js\");\nObject.defineProperty(exports, \"focusNextElement\", ({ enumerable: true, get: function () { return sibling_1.focusNextElement; } }));\nObject.defineProperty(exports, \"focusPrevElement\", ({ enumerable: true, get: function () { return sibling_1.focusPrevElement; } }));\nObject.defineProperty(exports, \"getRelativeFocusable\", ({ enumerable: true, get: function () { return sibling_1.getRelativeFocusable; } }));\nObject.defineProperty(exports, \"focusFirstElement\", ({ enumerable: true, get: function () { return sibling_1.focusFirstElement; } }));\nObject.defineProperty(exports, \"focusLastElement\", ({ enumerable: true, get: function () { return sibling_1.focusLastElement; } }));\nvar DOMutils_1 = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js\");\nObject.defineProperty(exports, \"getFocusableNodes\", ({ enumerable: true, get: function () { return DOMutils_1.getFocusableNodes; } }));\nObject.defineProperty(exports, \"getTabbableNodes\", ({ enumerable: true, get: function () { return DOMutils_1.getTabbableNodes; } }));\n/**\n * magic symbols to control focus behavior from DOM\n * see description of every particular one\n */\nvar constants = allConstants;\nexports.constants = constants;\n/**\n * @deprecated - please use {@link moveFocusInside} named export\n */\nvar deprecated_default_moveFocusInside = moveFocusInside_1.moveFocusInside;\nexports[\"default\"] = deprecated_default_moveFocusInside;\n//\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/index.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/moveFocusInside.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/moveFocusInside.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.moveFocusInside = void 0;\nvar commands_1 = __webpack_require__(/*! ./commands */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/commands.js\");\nvar focusSolver_1 = __webpack_require__(/*! ./focusSolver */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/focusSolver.js\");\nvar guardCount = 0;\nvar lockDisabled = false;\n/**\n * The main functionality of the focus-lock package\n *\n * Contains focus at a given node.\n * The last focused element will help to determine which element(first or last) should be focused.\n * The found element will be focused.\n *\n * This is one time action (move), not a persistent focus-lock\n *\n * HTML markers (see {@link import('./constants').FOCUS_AUTO} constants) can control autofocus\n * @see {@link focusSolver} for the same functionality without autofocus\n */\nvar moveFocusInside = function (topNode, lastNode, options) {\n    if (options === void 0) { options = {}; }\n    var focusable = (0, focusSolver_1.focusSolver)(topNode, lastNode);\n    // global local side effect to countain recursive lock activation and resolve focus-fighting\n    if (lockDisabled) {\n        return;\n    }\n    if (focusable) {\n        /** +FOCUS-FIGHTING prevention **/\n        if (guardCount > 2) {\n            // we have recursive entered back the lock activation\n            console.error('FocusLock: focus-fighting detected. Only one focus management system could be active. ' +\n                'See https://github.com/theKashey/focus-lock/#focus-fighting');\n            lockDisabled = true;\n            setTimeout(function () {\n                lockDisabled = false;\n            }, 1);\n            return;\n        }\n        guardCount++;\n        (0, commands_1.focusOn)(focusable.node, options.focusOptions);\n        guardCount--;\n    }\n};\nexports.moveFocusInside = moveFocusInside;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/moveFocusInside.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/return-focus.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/return-focus.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.captureFocusRestore = exports.recordElementLocation = void 0;\nvar DOMutils_1 = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js\");\nfunction weakRef(value) {\n    if (!value)\n        return null;\n    // #68 Safari 14.1 dont have it yet\n    // FIXME: remove in 2025\n    if (typeof WeakRef === 'undefined') {\n        return function () { return value || null; };\n    }\n    var w = value ? new WeakRef(value) : null;\n    return function () { return (w === null || w === void 0 ? void 0 : w.deref()) || null; };\n}\nvar recordElementLocation = function (element) {\n    if (!element) {\n        return null;\n    }\n    var stack = [];\n    var currentElement = element;\n    while (currentElement && currentElement !== document.body) {\n        stack.push({\n            current: weakRef(currentElement),\n            parent: weakRef(currentElement.parentElement),\n            left: weakRef(currentElement.previousElementSibling),\n            right: weakRef(currentElement.nextElementSibling),\n        });\n        currentElement = currentElement.parentElement;\n    }\n    return {\n        element: weakRef(element),\n        stack: stack,\n        ownerDocument: element.ownerDocument,\n    };\n};\nexports.recordElementLocation = recordElementLocation;\nvar restoreFocusTo = function (location) {\n    var _a, _b, _c, _d, _e;\n    if (!location) {\n        return undefined;\n    }\n    var stack = location.stack, ownerDocument = location.ownerDocument;\n    var visibilityCache = new Map();\n    for (var _i = 0, stack_1 = stack; _i < stack_1.length; _i++) {\n        var line = stack_1[_i];\n        var parent_1 = (_a = line.parent) === null || _a === void 0 ? void 0 : _a.call(line);\n        // is it still here?\n        if (parent_1 && ownerDocument.contains(parent_1)) {\n            var left = (_b = line.left) === null || _b === void 0 ? void 0 : _b.call(line);\n            var savedCurrent = line.current();\n            var current = parent_1.contains(savedCurrent) ? savedCurrent : undefined;\n            var right = (_c = line.right) === null || _c === void 0 ? void 0 : _c.call(line);\n            var focusables = (0, DOMutils_1.getTabbableNodes)([parent_1], visibilityCache);\n            var aim = \n            // that is element itself\n            (_e = (_d = current !== null && current !== void 0 ? current : \n            // or something in it's place\n            left === null || left === void 0 ? void 0 : left.nextElementSibling) !== null && _d !== void 0 ? _d : \n            // or somebody to the right, still close enough\n            right) !== null && _e !== void 0 ? _e : \n            // or somebody to the left, something?\n            left;\n            while (aim) {\n                for (var _f = 0, focusables_1 = focusables; _f < focusables_1.length; _f++) {\n                    var focusable = focusables_1[_f];\n                    if (aim === null || aim === void 0 ? void 0 : aim.contains(focusable.node)) {\n                        return focusable.node;\n                    }\n                }\n                aim = aim.nextElementSibling;\n            }\n            if (focusables.length) {\n                // if parent contains a focusable - move there\n                return focusables[0].node;\n            }\n        }\n    }\n    // nothing matched\n    return undefined;\n};\n/**\n * Captures the current focused element to restore focus as close as possible in the future\n * Handles situations where the focused element is removed from the DOM or no longer focusable\n * moving focus to the closest focusable element\n * @param targetElement - element where focus should be restored\n * @returns a function returning a new element to focus\n */\nvar captureFocusRestore = function (targetElement) {\n    var location = (0, exports.recordElementLocation)(targetElement);\n    return function () {\n        return restoreFocusTo(location);\n    };\n};\nexports.captureFocusRestore = captureFocusRestore;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/return-focus.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/sibling.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/sibling.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.focusLastElement = exports.focusFirstElement = exports.focusPrevElement = exports.focusNextElement = exports.getRelativeFocusable = void 0;\nvar commands_1 = __webpack_require__(/*! ./commands */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/commands.js\");\nvar DOMutils_1 = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js\");\nvar array_1 = __webpack_require__(/*! ./utils/array */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js\");\n/**\n * for a given `element` in a given `scope` returns focusable siblings\n * @param element - base element\n * @param scope - common parent. Can be document, but better to narrow it down for performance reasons\n * @returns {prev,next} - references to a focusable element before and after\n * @returns undefined - if operation is not applicable\n */\nvar getRelativeFocusable = function (element, scope, useTabbables) {\n    if (!element || !scope) {\n        console.error('no element or scope given');\n        return {};\n    }\n    var shards = (0, array_1.asArray)(scope);\n    if (shards.every(function (shard) { return !(0, DOMutils_1.contains)(shard, element); })) {\n        console.error('Active element is not contained in the scope');\n        return {};\n    }\n    var focusables = useTabbables\n        ? (0, DOMutils_1.getTabbableNodes)(shards, new Map())\n        : (0, DOMutils_1.getFocusableNodes)(shards, new Map());\n    var current = focusables.findIndex(function (_a) {\n        var node = _a.node;\n        return node === element;\n    });\n    if (current === -1) {\n        // an edge case, when anchor element is not found\n        return undefined;\n    }\n    return {\n        prev: focusables[current - 1],\n        next: focusables[current + 1],\n        first: focusables[0],\n        last: focusables[focusables.length - 1],\n    };\n};\nexports.getRelativeFocusable = getRelativeFocusable;\nvar getBoundary = function (shards, useTabbables) {\n    var set = useTabbables\n        ? (0, DOMutils_1.getTabbableNodes)((0, array_1.asArray)(shards), new Map())\n        : (0, DOMutils_1.getFocusableNodes)((0, array_1.asArray)(shards), new Map());\n    return {\n        first: set[0],\n        last: set[set.length - 1],\n    };\n};\nvar defaultOptions = function (options) {\n    return Object.assign({\n        scope: document.body,\n        cycle: true,\n        onlyTabbable: true,\n    }, options);\n};\nvar moveFocus = function (fromElement, options, cb) {\n    if (options === void 0) { options = {}; }\n    var newOptions = defaultOptions(options);\n    var solution = (0, exports.getRelativeFocusable)(fromElement, newOptions.scope, newOptions.onlyTabbable);\n    if (!solution) {\n        return;\n    }\n    var target = cb(solution, newOptions.cycle);\n    if (target) {\n        (0, commands_1.focusOn)(target.node, newOptions.focusOptions);\n    }\n};\n/**\n * focuses next element in the tab-order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nvar focusNextElement = function (fromElement, options) {\n    if (options === void 0) { options = {}; }\n    moveFocus(fromElement, options, function (_a, cycle) {\n        var next = _a.next, first = _a.first;\n        return next || (cycle && first);\n    });\n};\nexports.focusNextElement = focusNextElement;\n/**\n * focuses prev element in the tab order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nvar focusPrevElement = function (fromElement, options) {\n    if (options === void 0) { options = {}; }\n    moveFocus(fromElement, options, function (_a, cycle) {\n        var prev = _a.prev, last = _a.last;\n        return prev || (cycle && last);\n    });\n};\nexports.focusPrevElement = focusPrevElement;\nvar pickBoundary = function (scope, options, what) {\n    var _a;\n    var boundary = getBoundary(scope, (_a = options.onlyTabbable) !== null && _a !== void 0 ? _a : true);\n    var node = boundary[what];\n    if (node) {\n        (0, commands_1.focusOn)(node.node, options.focusOptions);\n    }\n};\n/**\n * focuses first element in the tab-order\n * @param {FocusNextOptions} options - focus options\n */\nvar focusFirstElement = function (scope, options) {\n    if (options === void 0) { options = {}; }\n    pickBoundary(scope, options, 'first');\n};\nexports.focusFirstElement = focusFirstElement;\n/**\n * focuses last element in the tab order\n * @param {FocusNextOptions} options - focus options\n */\nvar focusLastElement = function (scope, options) {\n    if (options === void 0) { options = {}; }\n    pickBoundary(scope, options, 'last');\n};\nexports.focusLastElement = focusLastElement;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/sibling.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/solver.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/solver.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.newFocus = exports.NEW_FOCUS = void 0;\nvar correctFocus_1 = __webpack_require__(/*! ./utils/correctFocus */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/correctFocus.js\");\nvar firstFocus_1 = __webpack_require__(/*! ./utils/firstFocus */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/firstFocus.js\");\nvar is_1 = __webpack_require__(/*! ./utils/is */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/is.js\");\nexports.NEW_FOCUS = 'NEW_FOCUS';\n/**\n * Main solver for the \"find next focus\" question\n * @param innerNodes - used to control \"return focus\"\n * @param innerTabbables - used to control \"autofocus\"\n * @param outerNodes\n * @param activeElement\n * @param lastNode\n * @returns {number|string|undefined|*}\n */\nvar newFocus = function (innerNodes, innerTabbables, outerNodes, activeElement, lastNode) {\n    var cnt = innerNodes.length;\n    var firstFocus = innerNodes[0];\n    var lastFocus = innerNodes[cnt - 1];\n    var isOnGuard = (0, is_1.isGuard)(activeElement);\n    // focus is inside\n    if (activeElement && innerNodes.indexOf(activeElement) >= 0) {\n        return undefined;\n    }\n    var activeIndex = activeElement !== undefined ? outerNodes.indexOf(activeElement) : -1;\n    var lastIndex = lastNode ? outerNodes.indexOf(lastNode) : activeIndex;\n    var lastNodeInside = lastNode ? innerNodes.indexOf(lastNode) : -1;\n    // no active focus (or focus is on the body)\n    if (activeIndex === -1) {\n        // known fallback\n        if (lastNodeInside !== -1) {\n            return lastNodeInside;\n        }\n        return exports.NEW_FOCUS;\n    }\n    // new focus, nothing to calculate\n    if (lastNodeInside === -1) {\n        return exports.NEW_FOCUS;\n    }\n    var indexDiff = activeIndex - lastIndex;\n    var firstNodeIndex = outerNodes.indexOf(firstFocus);\n    var lastNodeIndex = outerNodes.indexOf(lastFocus);\n    var correctedNodes = (0, correctFocus_1.correctNodes)(outerNodes);\n    var currentFocusableIndex = activeElement !== undefined ? correctedNodes.indexOf(activeElement) : -1;\n    var previousFocusableIndex = lastNode ? correctedNodes.indexOf(lastNode) : currentFocusableIndex;\n    var tabbableNodes = correctedNodes.filter(function (node) { return node.tabIndex >= 0; });\n    var currentTabbableIndex = activeElement !== undefined ? tabbableNodes.indexOf(activeElement) : -1;\n    var previousTabbableIndex = lastNode ? tabbableNodes.indexOf(lastNode) : currentTabbableIndex;\n    var focusIndexDiff = currentTabbableIndex >= 0 && previousTabbableIndex >= 0\n        ? // old/new are tabbables, measure distance in tabbable space\n            previousTabbableIndex - currentTabbableIndex\n        : // or else measure in focusable space\n            previousFocusableIndex - currentFocusableIndex;\n    // old focus\n    if (!indexDiff && lastNodeInside >= 0) {\n        return lastNodeInside;\n    }\n    // no tabbable elements, autofocus is not possible\n    if (innerTabbables.length === 0) {\n        // an edge case with no tabbable elements\n        // return the last focusable one\n        // with some probability this will prevent focus from cycling across the lock, but there is no tabbale elements to cycle to\n        return lastNodeInside;\n    }\n    var returnFirstNode = (0, firstFocus_1.pickFocusable)(innerNodes, innerTabbables[0]);\n    var returnLastNode = (0, firstFocus_1.pickFocusable)(innerNodes, innerTabbables[innerTabbables.length - 1]);\n    // first element\n    if (activeIndex <= firstNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n        return returnLastNode;\n    }\n    // last element\n    if (activeIndex >= lastNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n        return returnFirstNode;\n    }\n    // jump out, but not on the guard\n    if (indexDiff && Math.abs(focusIndexDiff) > 1) {\n        return lastNodeInside;\n    }\n    // focus above lock\n    if (activeIndex <= firstNodeIndex) {\n        return returnLastNode;\n    }\n    // focus below lock\n    if (activeIndex > lastNodeIndex) {\n        return returnFirstNode;\n    }\n    // index is inside tab order, but outside Lock\n    if (indexDiff) {\n        if (Math.abs(indexDiff) > 1) {\n            return lastNodeInside;\n        }\n        return (cnt + lastNodeInside + indexDiff) % cnt;\n    }\n    // do nothing\n    return undefined;\n};\nexports.newFocus = newFocus;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/solver.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.contains = exports.parentAutofocusables = exports.getFocusableNodes = exports.getTabbableNodes = exports.filterAutoFocusable = exports.filterFocusable = void 0;\nvar array_1 = __webpack_require__(/*! ./array */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js\");\nvar is_1 = __webpack_require__(/*! ./is */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/is.js\");\nvar tabOrder_1 = __webpack_require__(/*! ./tabOrder */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabOrder.js\");\nvar tabUtils_1 = __webpack_require__(/*! ./tabUtils */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabUtils.js\");\n/**\n * given list of focusable elements keeps the ones user can interact with\n * @param nodes\n * @param visibilityCache\n */\nvar filterFocusable = function (nodes, visibilityCache) {\n    return (0, array_1.toArray)(nodes)\n        .filter(function (node) { return (0, is_1.isVisibleCached)(visibilityCache, node); })\n        .filter(function (node) { return (0, is_1.notHiddenInput)(node); });\n};\nexports.filterFocusable = filterFocusable;\nvar filterAutoFocusable = function (nodes, cache) {\n    if (cache === void 0) { cache = new Map(); }\n    return (0, array_1.toArray)(nodes).filter(function (node) { return (0, is_1.isAutoFocusAllowedCached)(cache, node); });\n};\nexports.filterAutoFocusable = filterAutoFocusable;\n/**\n * !__WARNING__! Low level API.\n * @returns all tabbable nodes\n *\n * @see {@link getFocusableNodes} to get any focusable element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nvar getTabbableNodes = function (topNodes, visibilityCache, withGuards) {\n    return (0, tabOrder_1.orderByTabIndex)((0, exports.filterFocusable)((0, tabUtils_1.getFocusables)(topNodes, withGuards), visibilityCache), true, withGuards);\n};\nexports.getTabbableNodes = getTabbableNodes;\n/**\n * !__WARNING__! Low level API.\n *\n * @returns anything \"focusable\", not only tabbable. The difference is in `tabIndex=-1`\n * (without guards, as long as they are not expected to be ever focused)\n *\n * @see {@link getTabbableNodes} to get only tabble nodes element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nvar getFocusableNodes = function (topNodes, visibilityCache) {\n    return (0, tabOrder_1.orderByTabIndex)((0, exports.filterFocusable)((0, tabUtils_1.getFocusables)(topNodes), visibilityCache), false);\n};\nexports.getFocusableNodes = getFocusableNodes;\n/**\n * return list of nodes which are expected to be auto-focused\n * @param topNode\n * @param visibilityCache\n */\nvar parentAutofocusables = function (topNode, visibilityCache) {\n    return (0, exports.filterFocusable)((0, tabUtils_1.getParentAutofocusables)(topNode), visibilityCache);\n};\nexports.parentAutofocusables = parentAutofocusables;\n/*\n * Determines if element is contained in scope, including nested shadow DOMs\n */\nvar contains = function (scope, element) {\n    if (scope.shadowRoot) {\n        return (0, exports.contains)(scope.shadowRoot, element);\n    }\n    else {\n        if (Object.getPrototypeOf(scope).contains !== undefined &&\n            Object.getPrototypeOf(scope).contains.call(scope, element)) {\n            return true;\n        }\n        return (0, array_1.toArray)(scope.children).some(function (child) {\n            var _a;\n            if (child instanceof HTMLIFrameElement) {\n                var iframeBody = (_a = child.contentDocument) === null || _a === void 0 ? void 0 : _a.body;\n                if (iframeBody) {\n                    return (0, exports.contains)(iframeBody, element);\n                }\n                return false;\n            }\n            return (0, exports.contains)(child, element);\n        });\n    }\n};\nexports.contains = contains;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/all-affected.js":
/*!********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/all-affected.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getAllAffectedNodes = void 0;\nvar constants_1 = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/constants.js\");\nvar array_1 = __webpack_require__(/*! ./array */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js\");\n/**\n * in case of multiple nodes nested inside each other\n * keeps only top ones\n * this is O(nlogn)\n * @param nodes\n * @returns {*}\n */\nvar filterNested = function (nodes) {\n    var contained = new Set();\n    var l = nodes.length;\n    for (var i = 0; i < l; i += 1) {\n        for (var j = i + 1; j < l; j += 1) {\n            var position = nodes[i].compareDocumentPosition(nodes[j]);\n            /* eslint-disable no-bitwise */\n            if ((position & Node.DOCUMENT_POSITION_CONTAINED_BY) > 0) {\n                contained.add(j);\n            }\n            if ((position & Node.DOCUMENT_POSITION_CONTAINS) > 0) {\n                contained.add(i);\n            }\n            /* eslint-enable */\n        }\n    }\n    return nodes.filter(function (_, index) { return !contained.has(index); });\n};\n/**\n * finds top most parent for a node\n * @param node\n * @returns {*}\n */\nvar getTopParent = function (node) {\n    return node.parentNode ? getTopParent(node.parentNode) : node;\n};\n/**\n * returns all \"focus containers\" inside a given node\n * @param node - node or nodes to look inside\n * @returns Element[]\n */\nvar getAllAffectedNodes = function (node) {\n    var nodes = (0, array_1.asArray)(node);\n    return nodes.filter(Boolean).reduce(function (acc, currentNode) {\n        var group = currentNode.getAttribute(constants_1.FOCUS_GROUP);\n        acc.push.apply(acc, (group\n            ? filterNested((0, array_1.toArray)(getTopParent(currentNode).querySelectorAll(\"[\".concat(constants_1.FOCUS_GROUP, \"=\\\"\").concat(group, \"\\\"]:not([\").concat(constants_1.FOCUS_DISABLED, \"=\\\"disabled\\\"])\"))))\n            : [currentNode]));\n        return acc;\n    }, []);\n};\nexports.getAllAffectedNodes = getAllAffectedNodes;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/all-affected.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js":
/*!*************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\nIE11 support\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getFirst = exports.asArray = exports.toArray = void 0;\nvar toArray = function (a) {\n    var ret = Array(a.length);\n    for (var i = 0; i < a.length; ++i) {\n        ret[i] = a[i];\n    }\n    return ret;\n};\nexports.toArray = toArray;\nvar asArray = function (a) { return (Array.isArray(a) ? a : [a]); };\nexports.asArray = asArray;\nvar getFirst = function (a) { return (Array.isArray(a) ? a[0] : a); };\nexports.getFirst = getFirst;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzNS91dGlscy9hcnJheS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQkFBZ0IsR0FBRyxlQUFlLEdBQUcsZUFBZTtBQUNwRDtBQUNBO0FBQ0Esb0JBQW9CLGNBQWM7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2YsNkJBQTZCO0FBQzdCLGVBQWU7QUFDZiw4QkFBOEI7QUFDOUIsZ0JBQWdCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmb2N1cy1sb2NrQDEuMy42XFxub2RlX21vZHVsZXNcXGZvY3VzLWxvY2tcXGRpc3RcXGVzNVxcdXRpbHNcXGFycmF5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbklFMTEgc3VwcG9ydFxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmdldEZpcnN0ID0gZXhwb3J0cy5hc0FycmF5ID0gZXhwb3J0cy50b0FycmF5ID0gdm9pZCAwO1xudmFyIHRvQXJyYXkgPSBmdW5jdGlvbiAoYSkge1xuICAgIHZhciByZXQgPSBBcnJheShhLmxlbmd0aCk7XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBhLmxlbmd0aDsgKytpKSB7XG4gICAgICAgIHJldFtpXSA9IGFbaV07XG4gICAgfVxuICAgIHJldHVybiByZXQ7XG59O1xuZXhwb3J0cy50b0FycmF5ID0gdG9BcnJheTtcbnZhciBhc0FycmF5ID0gZnVuY3Rpb24gKGEpIHsgcmV0dXJuIChBcnJheS5pc0FycmF5KGEpID8gYSA6IFthXSk7IH07XG5leHBvcnRzLmFzQXJyYXkgPSBhc0FycmF5O1xudmFyIGdldEZpcnN0ID0gZnVuY3Rpb24gKGEpIHsgcmV0dXJuIChBcnJheS5pc0FycmF5KGEpID8gYVswXSA6IGEpOyB9O1xuZXhwb3J0cy5nZXRGaXJzdCA9IGdldEZpcnN0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/auto-focus.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/auto-focus.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.pickAutofocus = void 0;\nvar DOMutils_1 = __webpack_require__(/*! ./DOMutils */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js\");\nvar firstFocus_1 = __webpack_require__(/*! ./firstFocus */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/firstFocus.js\");\nvar is_1 = __webpack_require__(/*! ./is */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/is.js\");\nvar findAutoFocused = function (autoFocusables) {\n    return function (node) {\n        var _a;\n        var autofocus = (_a = (0, is_1.getDataset)(node)) === null || _a === void 0 ? void 0 : _a.autofocus;\n        return (\n        // @ts-expect-error\n        node.autofocus ||\n            //\n            (autofocus !== undefined && autofocus !== 'false') ||\n            //\n            autoFocusables.indexOf(node) >= 0);\n    };\n};\nvar pickAutofocus = function (nodesIndexes, orderedNodes, groups) {\n    var nodes = nodesIndexes.map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var autoFocusable = (0, DOMutils_1.filterAutoFocusable)(nodes.filter(findAutoFocused(groups)));\n    if (autoFocusable && autoFocusable.length) {\n        return (0, firstFocus_1.pickFirstFocus)(autoFocusable);\n    }\n    return (0, firstFocus_1.pickFirstFocus)((0, DOMutils_1.filterAutoFocusable)(orderedNodes));\n};\nexports.pickAutofocus = pickAutofocus;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/auto-focus.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/correctFocus.js":
/*!********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/correctFocus.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.correctNodes = exports.correctNode = void 0;\nvar is_1 = __webpack_require__(/*! ./is */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/is.js\");\nvar findSelectedRadio = function (node, nodes) {\n    return nodes\n        .filter(is_1.isRadioElement)\n        .filter(function (el) { return el.name === node.name; })\n        .filter(function (el) { return el.checked; })[0] || node;\n};\nvar correctNode = function (node, nodes) {\n    if ((0, is_1.isRadioElement)(node) && node.name) {\n        return findSelectedRadio(node, nodes);\n    }\n    return node;\n};\nexports.correctNode = correctNode;\n/**\n * giving a set of radio inputs keeps only selected (tabbable) ones\n * @param nodes\n */\nvar correctNodes = function (nodes) {\n    // IE11 has no Set(array) constructor\n    var resultSet = new Set();\n    nodes.forEach(function (node) { return resultSet.add((0, exports.correctNode)(node, nodes)); });\n    // using filter to support IE11\n    return nodes.filter(function (node) { return resultSet.has(node); });\n};\nexports.correctNodes = correctNodes;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/correctFocus.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/firstFocus.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/firstFocus.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.pickFocusable = exports.pickFirstFocus = void 0;\nvar correctFocus_1 = __webpack_require__(/*! ./correctFocus */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/correctFocus.js\");\nvar pickFirstFocus = function (nodes) {\n    if (nodes[0] && nodes.length > 1) {\n        return (0, correctFocus_1.correctNode)(nodes[0], nodes);\n    }\n    return nodes[0];\n};\nexports.pickFirstFocus = pickFirstFocus;\nvar pickFocusable = function (nodes, node) {\n    return nodes.indexOf((0, correctFocus_1.correctNode)(node, nodes));\n};\nexports.pickFocusable = pickFocusable;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzNS91dGlscy9maXJzdEZvY3VzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHFCQUFxQixHQUFHLHNCQUFzQjtBQUM5QyxxQkFBcUIsbUJBQU8sQ0FBQyx5SUFBZ0I7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZm9jdXMtbG9ja0AxLjMuNlxcbm9kZV9tb2R1bGVzXFxmb2N1cy1sb2NrXFxkaXN0XFxlczVcXHV0aWxzXFxmaXJzdEZvY3VzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5waWNrRm9jdXNhYmxlID0gZXhwb3J0cy5waWNrRmlyc3RGb2N1cyA9IHZvaWQgMDtcbnZhciBjb3JyZWN0Rm9jdXNfMSA9IHJlcXVpcmUoXCIuL2NvcnJlY3RGb2N1c1wiKTtcbnZhciBwaWNrRmlyc3RGb2N1cyA9IGZ1bmN0aW9uIChub2Rlcykge1xuICAgIGlmIChub2Rlc1swXSAmJiBub2Rlcy5sZW5ndGggPiAxKSB7XG4gICAgICAgIHJldHVybiAoMCwgY29ycmVjdEZvY3VzXzEuY29ycmVjdE5vZGUpKG5vZGVzWzBdLCBub2Rlcyk7XG4gICAgfVxuICAgIHJldHVybiBub2Rlc1swXTtcbn07XG5leHBvcnRzLnBpY2tGaXJzdEZvY3VzID0gcGlja0ZpcnN0Rm9jdXM7XG52YXIgcGlja0ZvY3VzYWJsZSA9IGZ1bmN0aW9uIChub2Rlcywgbm9kZSkge1xuICAgIHJldHVybiBub2Rlcy5pbmRleE9mKCgwLCBjb3JyZWN0Rm9jdXNfMS5jb3JyZWN0Tm9kZSkobm9kZSwgbm9kZXMpKTtcbn07XG5leHBvcnRzLnBpY2tGb2N1c2FibGUgPSBwaWNrRm9jdXNhYmxlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/firstFocus.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/getActiveElement.js":
/*!************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/getActiveElement.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getActiveElement = void 0;\n/**\n * returns active element from document or from nested shadowdoms\n */\nvar safe_1 = __webpack_require__(/*! ./safe */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/safe.js\");\n/**\n * returns current active element. If the active element is a \"container\" itself(shadowRoot or iframe) returns active element inside it\n * @param [inDocument]\n */\nvar getActiveElement = function (inDocument) {\n    if (inDocument === void 0) { inDocument = document; }\n    if (!inDocument || !inDocument.activeElement) {\n        return undefined;\n    }\n    var activeElement = inDocument.activeElement;\n    return (activeElement.shadowRoot\n        ? (0, exports.getActiveElement)(activeElement.shadowRoot)\n        : activeElement instanceof HTMLIFrameElement && (0, safe_1.safeProbe)(function () { return activeElement.contentWindow.document; })\n            ? (0, exports.getActiveElement)(activeElement.contentWindow.document)\n            : activeElement);\n};\nexports.getActiveElement = getActiveElement;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/getActiveElement.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/is.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/is.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isDefined = exports.isNotAGuard = exports.isGuard = exports.isAutoFocusAllowed = exports.notHiddenInput = exports.isRadioElement = exports.isHTMLInputElement = exports.isHTMLButtonElement = exports.getDataset = exports.isAutoFocusAllowedCached = exports.isVisibleCached = void 0;\nvar constants_1 = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/constants.js\");\nvar isElementHidden = function (node) {\n    // we can measure only \"elements\"\n    // consider others as \"visible\"\n    if (node.nodeType !== Node.ELEMENT_NODE) {\n        return false;\n    }\n    var computedStyle = window.getComputedStyle(node, null);\n    if (!computedStyle || !computedStyle.getPropertyValue) {\n        return false;\n    }\n    return (computedStyle.getPropertyValue('display') === 'none' || computedStyle.getPropertyValue('visibility') === 'hidden');\n};\nvar getParentNode = function (node) {\n    // DOCUMENT_FRAGMENT_NODE can also point on ShadowRoot. In this case .host will point on the next node\n    return node.parentNode && node.parentNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE\n        ? // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            node.parentNode.host\n        : node.parentNode;\n};\nvar isTopNode = function (node) {\n    // @ts-ignore\n    return node === document || (node && node.nodeType === Node.DOCUMENT_NODE);\n};\nvar isInert = function (node) { return node.hasAttribute('inert'); };\n/**\n * @see https://github.com/testing-library/jest-dom/blob/main/src/to-be-visible.js\n */\nvar isVisibleUncached = function (node, checkParent) {\n    return !node || isTopNode(node) || (!isElementHidden(node) && !isInert(node) && checkParent(getParentNode(node)));\n};\nvar isVisibleCached = function (visibilityCache, node) {\n    var cached = visibilityCache.get(node);\n    if (cached !== undefined) {\n        return cached;\n    }\n    var result = isVisibleUncached(node, exports.isVisibleCached.bind(undefined, visibilityCache));\n    visibilityCache.set(node, result);\n    return result;\n};\nexports.isVisibleCached = isVisibleCached;\nvar isAutoFocusAllowedUncached = function (node, checkParent) {\n    return node && !isTopNode(node) ? ((0, exports.isAutoFocusAllowed)(node) ? checkParent(getParentNode(node)) : false) : true;\n};\nvar isAutoFocusAllowedCached = function (cache, node) {\n    var cached = cache.get(node);\n    if (cached !== undefined) {\n        return cached;\n    }\n    var result = isAutoFocusAllowedUncached(node, exports.isAutoFocusAllowedCached.bind(undefined, cache));\n    cache.set(node, result);\n    return result;\n};\nexports.isAutoFocusAllowedCached = isAutoFocusAllowedCached;\nvar getDataset = function (node) {\n    // @ts-ignore\n    return node.dataset;\n};\nexports.getDataset = getDataset;\nvar isHTMLButtonElement = function (node) { return node.tagName === 'BUTTON'; };\nexports.isHTMLButtonElement = isHTMLButtonElement;\nvar isHTMLInputElement = function (node) { return node.tagName === 'INPUT'; };\nexports.isHTMLInputElement = isHTMLInputElement;\nvar isRadioElement = function (node) {\n    return (0, exports.isHTMLInputElement)(node) && node.type === 'radio';\n};\nexports.isRadioElement = isRadioElement;\nvar notHiddenInput = function (node) {\n    return !(((0, exports.isHTMLInputElement)(node) || (0, exports.isHTMLButtonElement)(node)) && (node.type === 'hidden' || node.disabled));\n};\nexports.notHiddenInput = notHiddenInput;\nvar isAutoFocusAllowed = function (node) {\n    var attribute = node.getAttribute(constants_1.FOCUS_NO_AUTOFOCUS);\n    return ![true, 'true', ''].includes(attribute);\n};\nexports.isAutoFocusAllowed = isAutoFocusAllowed;\nvar isGuard = function (node) { var _a; return Boolean(node && ((_a = (0, exports.getDataset)(node)) === null || _a === void 0 ? void 0 : _a.focusGuard)); };\nexports.isGuard = isGuard;\nvar isNotAGuard = function (node) { return !(0, exports.isGuard)(node); };\nexports.isNotAGuard = isNotAGuard;\nvar isDefined = function (x) { return Boolean(x); };\nexports.isDefined = isDefined;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/is.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/parenting.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/parenting.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.allParentAutofocusables = exports.getTopCommonParent = exports.getCommonParent = void 0;\nvar DOMutils_1 = __webpack_require__(/*! ./DOMutils */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js\");\nvar DOMutils_2 = __webpack_require__(/*! ./DOMutils */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/DOMutils.js\");\nvar array_1 = __webpack_require__(/*! ./array */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js\");\nvar getParents = function (node, parents) {\n    if (parents === void 0) { parents = []; }\n    parents.push(node);\n    if (node.parentNode) {\n        getParents(node.parentNode.host || node.parentNode, parents);\n    }\n    return parents;\n};\n/**\n * finds a parent for both nodeA and nodeB\n * @param nodeA\n * @param nodeB\n * @returns {boolean|*}\n */\nvar getCommonParent = function (nodeA, nodeB) {\n    var parentsA = getParents(nodeA);\n    var parentsB = getParents(nodeB);\n    // tslint:disable-next-line:prefer-for-of\n    for (var i = 0; i < parentsA.length; i += 1) {\n        var currentParent = parentsA[i];\n        if (parentsB.indexOf(currentParent) >= 0) {\n            return currentParent;\n        }\n    }\n    return false;\n};\nexports.getCommonParent = getCommonParent;\nvar getTopCommonParent = function (baseActiveElement, leftEntry, rightEntries) {\n    var activeElements = (0, array_1.asArray)(baseActiveElement);\n    var leftEntries = (0, array_1.asArray)(leftEntry);\n    var activeElement = activeElements[0];\n    var topCommon = false;\n    leftEntries.filter(Boolean).forEach(function (entry) {\n        topCommon = (0, exports.getCommonParent)(topCommon || entry, entry) || topCommon;\n        rightEntries.filter(Boolean).forEach(function (subEntry) {\n            var common = (0, exports.getCommonParent)(activeElement, subEntry);\n            if (common) {\n                if (!topCommon || (0, DOMutils_2.contains)(common, topCommon)) {\n                    topCommon = common;\n                }\n                else {\n                    topCommon = (0, exports.getCommonParent)(common, topCommon);\n                }\n            }\n        });\n    });\n    // TODO: add assert here?\n    return topCommon;\n};\nexports.getTopCommonParent = getTopCommonParent;\n/**\n * return list of nodes which are expected to be autofocused inside a given top nodes\n * @param entries\n * @param visibilityCache\n */\nvar allParentAutofocusables = function (entries, visibilityCache) {\n    return entries.reduce(function (acc, node) { return acc.concat((0, DOMutils_1.parentAutofocusables)(node, visibilityCache)); }, []);\n};\nexports.allParentAutofocusables = allParentAutofocusables;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/parenting.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/safe.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/safe.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.safeProbe = void 0;\nvar safeProbe = function (cb) {\n    try {\n        return cb();\n    }\n    catch (e) {\n        return undefined;\n    }\n};\nexports.safeProbe = safeProbe;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzNS91dGlscy9zYWZlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmb2N1cy1sb2NrQDEuMy42XFxub2RlX21vZHVsZXNcXGZvY3VzLWxvY2tcXGRpc3RcXGVzNVxcdXRpbHNcXHNhZmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnNhZmVQcm9iZSA9IHZvaWQgMDtcbnZhciBzYWZlUHJvYmUgPSBmdW5jdGlvbiAoY2IpIHtcbiAgICB0cnkge1xuICAgICAgICByZXR1cm4gY2IoKTtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG59O1xuZXhwb3J0cy5zYWZlUHJvYmUgPSBzYWZlUHJvYmU7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/safe.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabOrder.js":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabOrder.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.orderByTabIndex = exports.tabSort = void 0;\nvar array_1 = __webpack_require__(/*! ./array */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js\");\nvar tabSort = function (a, b) {\n    var aTab = Math.max(0, a.tabIndex);\n    var bTab = Math.max(0, b.tabIndex);\n    var tabDiff = aTab - bTab;\n    var indexDiff = a.index - b.index;\n    if (tabDiff) {\n        if (!aTab) {\n            return 1;\n        }\n        if (!bTab) {\n            return -1;\n        }\n    }\n    return tabDiff || indexDiff;\n};\nexports.tabSort = tabSort;\nvar getTabIndex = function (node) {\n    if (node.tabIndex < 0) {\n        // all \"focusable\" elements are already preselected\n        // but some might have implicit negative tabIndex\n        // return 0 for <audio without tabIndex attribute - it is \"tabbable\"\n        if (!node.hasAttribute('tabindex')) {\n            return 0;\n        }\n    }\n    return node.tabIndex;\n};\nvar orderByTabIndex = function (nodes, filterNegative, keepGuards) {\n    return (0, array_1.toArray)(nodes)\n        .map(function (node, index) {\n        var tabIndex = getTabIndex(node);\n        return {\n            node: node,\n            index: index,\n            tabIndex: keepGuards && tabIndex === -1 ? ((node.dataset || {}).focusGuard ? 0 : -1) : tabIndex,\n        };\n    })\n        .filter(function (data) { return !filterNegative || data.tabIndex >= 0; })\n        .sort(exports.tabSort);\n};\nexports.orderByTabIndex = orderByTabIndex;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabOrder.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabUtils.js":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabUtils.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getParentAutofocusables = exports.getFocusables = void 0;\nvar constants_1 = __webpack_require__(/*! ../constants */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/constants.js\");\nvar array_1 = __webpack_require__(/*! ./array */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/array.js\");\nvar tabbables_1 = __webpack_require__(/*! ./tabbables */ \"(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabbables.js\");\nvar queryTabbables = tabbables_1.tabbables.join(',');\nvar queryGuardTabbables = \"\".concat(queryTabbables, \", [data-focus-guard]\");\nvar getFocusablesWithShadowDom = function (parent, withGuards) {\n    return (0, array_1.toArray)((parent.shadowRoot || parent).children).reduce(function (acc, child) {\n        return acc.concat(child.matches(withGuards ? queryGuardTabbables : queryTabbables) ? [child] : [], getFocusablesWithShadowDom(child));\n    }, []);\n};\nvar getFocusablesWithIFrame = function (parent, withGuards) {\n    var _a;\n    // contentDocument of iframe will be null if current origin cannot access it\n    if (parent instanceof HTMLIFrameElement && ((_a = parent.contentDocument) === null || _a === void 0 ? void 0 : _a.body)) {\n        return (0, exports.getFocusables)([parent.contentDocument.body], withGuards);\n    }\n    return [parent];\n};\nvar getFocusables = function (parents, withGuards) {\n    return parents.reduce(function (acc, parent) {\n        var _a;\n        var focusableWithShadowDom = getFocusablesWithShadowDom(parent, withGuards);\n        var focusableWithIframes = (_a = []).concat.apply(_a, focusableWithShadowDom.map(function (node) { return getFocusablesWithIFrame(node, withGuards); }));\n        return acc.concat(\n        // add all tabbables inside and within shadow DOMs in DOM order\n        focusableWithIframes, \n        // add if node is tabbable itself\n        parent.parentNode\n            ? (0, array_1.toArray)(parent.parentNode.querySelectorAll(queryTabbables)).filter(function (node) { return node === parent; })\n            : []);\n    }, []);\n};\nexports.getFocusables = getFocusables;\n/**\n * return a list of focusable nodes within an area marked as \"auto-focusable\"\n * @param parent\n */\nvar getParentAutofocusables = function (parent) {\n    var parentFocus = parent.querySelectorAll(\"[\".concat(constants_1.FOCUS_AUTO, \"]\"));\n    return (0, array_1.toArray)(parentFocus)\n        .map(function (node) { return (0, exports.getFocusables)([node]); })\n        .reduce(function (acc, nodes) { return acc.concat(nodes); }, []);\n};\nexports.getParentAutofocusables = getParentAutofocusables;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabUtils.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabbables.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabbables.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.tabbables = void 0;\n/**\n * list of the object to be considered as focusable\n */\nexports.tabbables = [\n    'button:enabled',\n    'select:enabled',\n    'textarea:enabled',\n    'input:enabled',\n    // elements with explicit roles will also use explicit tabindex\n    // '[role=\"button\"]',\n    'a[href]',\n    'area[href]',\n    'summary',\n    'iframe',\n    'object',\n    'embed',\n    'audio[controls]',\n    'video[controls]',\n    '[tabindex]',\n    '[contenteditable]',\n    '[autofocus]',\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzNS91dGlscy90YWJiYWJsZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZvY3VzLWxvY2tAMS4zLjZcXG5vZGVfbW9kdWxlc1xcZm9jdXMtbG9ja1xcZGlzdFxcZXM1XFx1dGlsc1xcdGFiYmFibGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy50YWJiYWJsZXMgPSB2b2lkIDA7XG4vKipcbiAqIGxpc3Qgb2YgdGhlIG9iamVjdCB0byBiZSBjb25zaWRlcmVkIGFzIGZvY3VzYWJsZVxuICovXG5leHBvcnRzLnRhYmJhYmxlcyA9IFtcbiAgICAnYnV0dG9uOmVuYWJsZWQnLFxuICAgICdzZWxlY3Q6ZW5hYmxlZCcsXG4gICAgJ3RleHRhcmVhOmVuYWJsZWQnLFxuICAgICdpbnB1dDplbmFibGVkJyxcbiAgICAvLyBlbGVtZW50cyB3aXRoIGV4cGxpY2l0IHJvbGVzIHdpbGwgYWxzbyB1c2UgZXhwbGljaXQgdGFiaW5kZXhcbiAgICAvLyAnW3JvbGU9XCJidXR0b25cIl0nLFxuICAgICdhW2hyZWZdJyxcbiAgICAnYXJlYVtocmVmXScsXG4gICAgJ3N1bW1hcnknLFxuICAgICdpZnJhbWUnLFxuICAgICdvYmplY3QnLFxuICAgICdlbWJlZCcsXG4gICAgJ2F1ZGlvW2NvbnRyb2xzXScsXG4gICAgJ3ZpZGVvW2NvbnRyb2xzXScsXG4gICAgJ1t0YWJpbmRleF0nLFxuICAgICdbY29udGVudGVkaXRhYmxlXScsXG4gICAgJ1thdXRvZm9jdXNdJyxcbl07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es5/utils/tabbables.js\n");

/***/ })

};
;