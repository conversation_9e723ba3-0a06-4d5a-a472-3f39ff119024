(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3640],{8486:(e,t,n)=>{"use strict";let r;n.d(t,{Ay:()=>eu});var a=n(94285),i=n(45617),u=n(10241);let o=new WeakMap,l=()=>{},c=l(),s=Object,f=e=>e===c,d=e=>"function"==typeof e,p=(e,t)=>({...e,...t}),h=e=>d(e.then),y={},b={},v="undefined",g=typeof window!=v,w=typeof document!=v,m=g&&"Deno"in window,O=()=>g&&typeof window.requestAnimationFrame!=v,k=(e,t)=>{let n=o.get(e);return[()=>!f(t)&&e.get(t)||y,r=>{if(!f(t)){let a=e.get(t);t in b||(b[t]=a),n[5](t,p(a,r),a||y)}},n[6],()=>!f(t)&&t in b?b[t]:!f(t)&&e.get(t)||y]},_=!0,[j,T]=g&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[l,l],R={initFocus:e=>(w&&document.addEventListener("visibilitychange",e),j("focus",e),()=>{w&&document.removeEventListener("visibilitychange",e),T("focus",e)}),initReconnect:e=>{let t=()=>{_=!0,e()},n=()=>{_=!1};return j("online",t),j("offline",n),()=>{T("online",t),T("offline",n)}}},E=!a.useId,P=!g||m,C=e=>O()?window.requestAnimationFrame(e):setTimeout(e,1),S=P?a.useEffect:a.useLayoutEffect,x="undefined"!=typeof navigator&&navigator.connection,A=!P&&x&&(["slow-2g","2g"].includes(x.effectiveType)||x.saveData),D=new WeakMap,L=e=>s.prototype.toString.call(e),I=(e,t)=>e===`[object ${t}]`,M=0,V=e=>{let t,n,r=typeof e,a=L(e),i=I(a,"Date"),u=I(a,"RegExp"),o=I(a,"Object");if(s(e)!==e||i||u)t=i?e.toJSON():"symbol"==r?e.toString():"string"==r?JSON.stringify(e):""+e;else{if(t=D.get(e))return t;if(t=++M+"~",D.set(e,t),Array.isArray(e)){for(n=0,t="@";n<e.length;n++)t+=V(e[n])+",";D.set(e,t)}if(o){t="#";let r=s.keys(e).sort();for(;!f(n=r.pop());)f(e[n])||(t+=n+":"+V(e[n])+",");D.set(e,t)}}return t},W=e=>{if(d(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?V(e):"",t]},F=0,$=()=>++F;async function q(...e){let[t,n,r,a]=e,i=p({populateCache:!0,throwOnError:!0},"boolean"==typeof a?{revalidate:a}:a||{}),u=i.populateCache,l=i.rollbackOnError,s=i.optimisticData,y=e=>"function"==typeof l?l(e):!1!==l,b=i.throwOnError;if(d(n)){let e=[];for(let r of t.keys())!/^\$(inf|sub)\$/.test(r)&&n(t.get(r)._k)&&e.push(r);return Promise.all(e.map(v))}return v(n);async function v(n){let a,[l]=W(n);if(!l)return;let[p,v]=k(t,l),[g,w,m,O]=o.get(t),_=()=>{let e=g[l];return(d(i.revalidate)?i.revalidate(p().data,n):!1!==i.revalidate)&&(delete m[l],delete O[l],e&&e[0])?e[0](2).then(()=>p().data):p().data};if(e.length<3)return _();let j=r,T=!1,R=$();w[l]=[R,0];let E=!f(s),P=p(),C=P.data,S=P._c,x=f(S)?C:S;if(E&&v({data:s=d(s)?s(x,C):s,_c:x}),d(j))try{j=j(x)}catch(e){a=e,T=!0}if(j&&h(j)){if(j=await j.catch(e=>{a=e,T=!0}),R!==w[l][0]){if(T)throw a;return j}T&&E&&y(a)&&(u=!0,v({data:x,_c:c}))}if(u&&!T&&(d(u)?v({data:u(j,x),error:c,_c:c}):v({data:j,error:c,_c:c})),w[l][1]=$(),Promise.resolve(_()).then(()=>{v({_c:c})}),T){if(b)throw a;return}return j}}let N=(e,t)=>{for(let n in e)e[n][0]&&e[n][0](t)},K=(e,t)=>{if(!o.has(e)){let n=p(R,t),r=Object.create(null),a=q.bind(c,e),i=l,u=Object.create(null),s=(e,t)=>{let n=u[e]||[];return u[e]=n,n.push(t),()=>n.splice(n.indexOf(t),1)},f=(t,n,r)=>{e.set(t,n);let a=u[t];if(a)for(let e of a)e(n,r)},d=()=>{if(!o.has(e)&&(o.set(e,[r,Object.create(null),Object.create(null),Object.create(null),a,f,s]),!P)){let t=n.initFocus(setTimeout.bind(c,N.bind(c,r,0))),a=n.initReconnect(setTimeout.bind(c,N.bind(c,r,1)));i=()=>{t&&t(),a&&a(),o.delete(e)}}};return d(),[e,a,d,i]}return[e,o.get(e)[4]]},z=u.j,[U,Y]=K(new Map),H=p({onLoadingSlow:l,onSuccess:l,onError:l,onErrorRetry:(e,t,n,r,a)=>{let i=n.errorRetryCount,u=a.retryCount,o=~~((Math.random()+.5)*(1<<(u<8?u:8)))*n.errorRetryInterval;(f(i)||!(u>i))&&setTimeout(r,o,a)},onDiscarded:l,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:A?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:A?5e3:3e3,compare:z,isPaused:()=>!1,cache:U,mutate:Y,fallback:{}},{isOnline:()=>_,isVisible:()=>{let e=w&&document.visibilityState;return f(e)||"hidden"!==e}}),J=(e,t)=>{let n=p(e,t);if(t){let{use:r,fallback:a}=e,{use:i,fallback:u}=t;r&&i&&(n.use=r.concat(i)),a&&u&&(n.fallback=p(a,u))}return n},X=(0,a.createContext)({}),B=g&&window.__SWR_DEVTOOLS_USE__,G=B?window.__SWR_DEVTOOLS_USE__:[],Z=e=>d(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],Q=()=>p(H,(0,a.useContext)(X)),ee=G.concat(e=>(t,n,r)=>{let a=n&&((...e)=>{let[r]=W(t),[,,,a]=o.get(U);if(r.startsWith("$inf$"))return n(...e);let i=a[r];return f(i)?n(...e):(delete a[r],i)});return e(t,a,r)}),et=(e,t,n)=>{let r=t[e]||(t[e]=[]);return r.push(n),()=>{let e=r.indexOf(n);e>=0&&(r[e]=r[r.length-1],r.pop())}};B&&(window.__SWR_DEVTOOLS_REACT__=a);let en=()=>{},er=en();new WeakMap;let ea=a.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),ei={dedupe:!0};s.defineProperty(e=>{let{value:t}=e,n=(0,a.useContext)(X),r=d(t),i=(0,a.useMemo)(()=>r?t(n):t,[r,n,t]),u=(0,a.useMemo)(()=>r?i:J(n,i),[r,n,i]),o=i&&i.provider,l=(0,a.useRef)(c);o&&!l.current&&(l.current=K(o(u.cache||U),i));let s=l.current;return s&&(u.cache=s[0],u.mutate=s[1]),S(()=>{if(s)return s[2]&&s[2](),s[3]},[]),(0,a.createElement)(X.Provider,p(e,{value:u}))},"defaultValue",{value:H});let eu=(r=(e,t,n)=>{let{cache:r,compare:u,suspense:l,fallbackData:s,revalidateOnMount:y,revalidateIfStale:b,refreshInterval:v,refreshWhenHidden:g,refreshWhenOffline:w,keepPreviousData:m}=n,[O,_,j,T]=o.get(r),[R,x]=W(e),A=(0,a.useRef)(!1),D=(0,a.useRef)(!1),L=(0,a.useRef)(R),I=(0,a.useRef)(t),M=(0,a.useRef)(n),V=()=>M.current,F=()=>V().isVisible()&&V().isOnline(),[N,K,z,U]=k(r,R),Y=(0,a.useRef)({}).current,H=f(s)?f(n.fallback)?c:n.fallback[R]:s,J=(e,t)=>{for(let n in Y)if("data"===n){if(!u(e[n],t[n])&&(!f(e[n])||!u(eu,t[n])))return!1}else if(t[n]!==e[n])return!1;return!0},X=(0,a.useMemo)(()=>{let e=!!R&&!!t&&(f(y)?!V().isPaused()&&!l&&!1!==b:y),n=t=>{let n=p(t);return(delete n._k,e)?{isValidating:!0,isLoading:!0,...n}:n},r=N(),a=U(),i=n(r),u=r===a?i:n(a),o=i;return[()=>{let e=n(N());return J(e,o)?(o.data=e.data,o.isLoading=e.isLoading,o.isValidating=e.isValidating,o.error=e.error,o):(o=e,e)},()=>u]},[r,R]),B=(0,i.useSyncExternalStore)((0,a.useCallback)(e=>z(R,(t,n)=>{J(n,t)||e()}),[r,R]),X[0],X[1]),G=!A.current,Z=O[R]&&O[R].length>0,Q=B.data,ee=f(Q)?H&&h(H)?ea(H):H:Q,en=B.error,er=(0,a.useRef)(ee),eu=m?f(Q)?f(er.current)?ee:er.current:Q:ee,eo=(!Z||!!f(en))&&(G&&!f(y)?y:!V().isPaused()&&(l?!f(ee)&&b:f(ee)||b)),el=!!(R&&t&&G&&eo),ec=f(B.isValidating)?el:B.isValidating,es=f(B.isLoading)?el:B.isLoading,ef=(0,a.useCallback)(async e=>{let t,r,a=I.current;if(!R||!a||D.current||V().isPaused())return!1;let i=!0,o=e||{},l=!j[R]||!o.dedupe,s=()=>E?!D.current&&R===L.current&&A.current:R===L.current,p={isValidating:!1,isLoading:!1},h=()=>{K(p)},y=()=>{let e=j[R];e&&e[1]===r&&delete j[R]},b={isValidating:!0};f(N().data)&&(b.isLoading=!0);try{if(l&&(K(b),n.loadingTimeout&&f(N().data)&&setTimeout(()=>{i&&s()&&V().onLoadingSlow(R,n)},n.loadingTimeout),j[R]=[a(x),$()]),[t,r]=j[R],t=await t,l&&setTimeout(y,n.dedupingInterval),!j[R]||j[R][1]!==r)return l&&s()&&V().onDiscarded(R),!1;p.error=c;let e=_[R];if(!f(e)&&(r<=e[0]||r<=e[1]||0===e[1]))return h(),l&&s()&&V().onDiscarded(R),!1;let o=N().data;p.data=u(o,t)?o:t,l&&s()&&V().onSuccess(t,R,n)}catch(n){y();let e=V(),{shouldRetryOnError:t}=e;!e.isPaused()&&(p.error=n,l&&s()&&(e.onError(n,R,e),(!0===t||d(t)&&t(n))&&(!V().revalidateOnFocus||!V().revalidateOnReconnect||F())&&e.onErrorRetry(n,R,e,e=>{let t=O[R];t&&t[0]&&t[0](3,e)},{retryCount:(o.retryCount||0)+1,dedupe:!0})))}return i=!1,h(),!0},[R,r]),ed=(0,a.useCallback)((...e)=>q(r,L.current,...e),[]);if(S(()=>{I.current=t,M.current=n,f(Q)||(er.current=Q)}),S(()=>{if(!R)return;let e=ef.bind(c,ei),t=0;V().revalidateOnFocus&&(t=Date.now()+V().focusThrottleInterval);let n=et(R,O,(n,r={})=>{if(0==n){let n=Date.now();V().revalidateOnFocus&&n>t&&F()&&(t=n+V().focusThrottleInterval,e())}else if(1==n)V().revalidateOnReconnect&&F()&&e();else if(2==n)return ef();else if(3==n)return ef(r)});return D.current=!1,L.current=R,A.current=!0,K({_k:x}),eo&&!j[R]&&(f(ee)||P?e():C(e)),()=>{D.current=!0,n()}},[R]),S(()=>{let e;function t(){let t=d(v)?v(N().data):v;t&&-1!==e&&(e=setTimeout(n,t))}function n(){!N().error&&(g||V().isVisible())&&(w||V().isOnline())?ef(ei).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[v,g,w,R]),(0,a.useDebugValue)(eu),l&&f(ee)&&R){if(!E&&P)throw Error("Fallback data is required when using Suspense in SSR.");I.current=t,M.current=n,D.current=!1;let e=T[R];if(f(e)||ea(ed(e)),f(en)){let e=ef(ei);f(eu)||(e.status="fulfilled",e.value=!0),ea(e)}else throw en}return{mutate:ed,get data(){return Y.data=!0,eu},get error(){return Y.error=!0,en},get isValidating(){return Y.isValidating=!0,ec},get isLoading(){return Y.isLoading=!0,es}}},function(...e){let t=Q(),[n,a,i]=Z(e),u=J(t,i),o=r,{use:l}=u,c=(l||[]).concat(ee);for(let e=c.length;e--;)o=c[e](o);return o(n,a||u.fetcher||null,u)})},13341:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],r=0;r<e.rangeCount;r++)n.push(e.getRangeAt(r));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},24088:(e,t,n)=>{"use strict";n.d(t,{LU:()=>l,Sv:()=>c,vd:()=>a,MS:()=>r,XZ:()=>o,j:()=>i,c4:()=>L,wN:()=>v,kg:()=>O,wE:()=>V,C:()=>C,VF:()=>I,Tb:()=>M,HT:()=>f,tW:()=>p,K5:()=>b,YW:()=>h,r1:()=>N,K2:()=>S,se:()=>x,G1:()=>T,HC:()=>y,MY:()=>K,lK:()=>$,di:()=>A,As:()=>q,b2:()=>w,Sh:()=>D});var r="-ms-",a="-moz-",i="-webkit-",u="comm",o="rule",l="decl",c="@keyframes",s=Math.abs,f=String.fromCharCode,d=Object.assign;function p(e,t){return 45^v(e,0)?(((t<<2^v(e,0))<<2^v(e,1))<<2^v(e,2))<<2^v(e,3):0}function h(e,t){return(e=t.exec(e))?e[0]:e}function y(e,t,n){return e.replace(t,n)}function b(e,t){return e.indexOf(t)}function v(e,t){return 0|e.charCodeAt(t)}function g(e,t,n){return e.slice(t,n)}function w(e){return e.length}function m(e,t){return t.push(e),e}function O(e,t){return e.map(t).join("")}var k=1,_=1,j=0,T=0,R=0,E="";function P(e,t,n,r,a,i,u){return{value:e,root:t,parent:n,type:r,props:a,children:i,line:k,column:_,length:u,return:""}}function C(e,t){return d(P("",null,null,"",null,null,0),e,{length:-e.length},t)}function S(){return R=T<j?v(E,T++):0,_++,10===R&&(_=1,k++),R}function x(){return v(E,T)}function A(e,t){return g(E,e,t)}function D(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function L(e){return k=_=1,j=w(E=e),T=0,[]}function I(e){return E="",e}function M(e){return A(T-1,function e(t){for(;S();)switch(R){case t:return T;case 34:case 39:34!==t&&39!==t&&e(R);break;case 40:41===t&&e(t);break;case 92:S()}return T}(91===e?e+2:40===e?e+1:e)).trim()}function V(e){return I(function e(t,n,r,a,i,o,l,c,s){for(var d,p=0,h=0,O=l,j=0,C=0,L=0,I=1,V=1,$=1,q=0,N="",K=i,z=o,U=a,Y=N;V;)switch(L=q,q=S()){case 40:if(108!=L&&58==v(Y,O-1)){-1!=b(Y+=y(M(q),"&","&\f"),"&\f")&&($=-1);break}case 34:case 39:case 91:Y+=M(q);break;case 9:case 10:case 13:case 32:Y+=function(e){for(;R=x();)if(R<33)S();else break;return D(e)>2||D(R)>3?"":" "}(L);break;case 92:Y+=function(e,t){for(;--t&&S()&&!(R<48)&&!(R>102)&&(!(R>57)||!(R<65))&&(!(R>70)||!(R<97)););return A(e,T+(t<6&&32==x()&&32==S()))}(T-1,7);continue;case 47:switch(x()){case 42:case 47:m((d=function(e,t){for(;S();)if(e+R===57)break;else if(e+R===84&&47===x())break;return"/*"+A(t,T-1)+"*"+f(47===e?e:S())}(S(),T),P(d,n,r,u,f(R),g(d,2,-2),0)),s);break;default:Y+="/"}break;case 123*I:c[p++]=w(Y)*$;case 125*I:case 59:case 0:switch(q){case 0:case 125:V=0;case 59+h:-1==$&&(Y=y(Y,/\f/g,"")),C>0&&w(Y)-O&&m(C>32?F(Y+";",a,r,O-1):F(y(Y," ","")+";",a,r,O-2),s);break;case 59:Y+=";";default:if(m(U=W(Y,n,r,p,h,i,c,N,K=[],z=[],O),o),123===q)if(0===h)e(Y,n,U,U,K,o,O,c,z);else switch(99===j&&110===v(Y,3)?100:j){case 100:case 108:case 109:case 115:e(t,U,U,a&&m(W(t,U,U,0,0,i,c,N,i,K=[],O),z),i,z,O,c,a?K:z);break;default:e(Y,U,U,U,[""],z,0,c,z)}}p=h=C=0,I=$=1,N=Y="",O=l;break;case 58:O=1+w(Y),C=L;default:if(I<1){if(123==q)--I;else if(125==q&&0==I++&&125==(R=T>0?v(E,--T):0,_--,10===R&&(_=1,k--),R))continue}switch(Y+=f(q),q*I){case 38:$=h>0?1:(Y+="\f",-1);break;case 44:c[p++]=(w(Y)-1)*$,$=1;break;case 64:45===x()&&(Y+=M(S())),j=x(),h=O=w(N=Y+=function(e){for(;!D(x());)S();return A(e,T)}(T)),q++;break;case 45:45===L&&2==w(Y)&&(I=0)}}return o}("",null,null,null,[""],e=L(e),0,[0],e))}function W(e,t,n,r,a,i,u,l,c,f,d){for(var p=a-1,h=0===a?i:[""],b=h.length,v=0,w=0,m=0;v<r;++v)for(var O=0,k=g(e,p+1,p=s(w=u[v])),_=e;O<b;++O)(_=(w>0?h[O]+" "+k:y(k,/&\f/g,h[O])).trim())&&(c[m++]=_);return P(e,t,n,0===a?o:l,c,f,d)}function F(e,t,n,r){return P(e,t,n,l,g(e,0,r),g(e,r+1,-1),r)}function $(e,t){for(var n="",r=e.length,a=0;a<r;a++)n+=t(e[a],a,e,t)||"";return n}function q(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case l:return e.return=e.return||e.value;case u:return"";case c:return e.return=e.value+"{"+$(e.children,r)+"}";case o:e.value=e.props.join(",")}return w(n=$(e.children,r))?e.return=e.value+"{"+n+"}":""}function N(e){var t=e.length;return function(n,r,a,i){for(var u="",o=0;o<t;o++)u+=e[o](n,r,a,i)||"";return u}}function K(e){return function(t){!t.root&&(t=t.return)&&e(t)}}},26908:(e,t,n)=>{"use strict";function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}function a(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var a={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&Object.prototype.hasOwnProperty.call(e,u)){var o=i?Object.getOwnPropertyDescriptor(e,u):null;o&&(o.get||o.set)?Object.defineProperty(a,u,o):a[u]=e[u]}return a.default=e,n&&n.set(e,a),a}n.r(t),n.d(t,{_:()=>a})},28163:(e,t)=>{"use strict";function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,a=e[r];if(0<i(a,t))e[r]=t,e[n]=a,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,a=e.length,u=a>>>1;r<u;){var o=2*(r+1)-1,l=e[o],c=o+1,s=e[c];if(0>i(l,n))c<a&&0>i(s,l)?(e[r]=s,e[c]=n,r=c):(e[r]=l,e[o]=n,r=o);else if(c<a&&0>i(s,n))e[r]=s,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var u,o=performance;t.unstable_now=function(){return o.now()}}else{var l=Date,c=l.now();t.unstable_now=function(){return l.now()-c}}var s=[],f=[],d=1,p=null,h=3,y=!1,b=!1,v=!1,g=!1,w="function"==typeof setTimeout?setTimeout:null,m="function"==typeof clearTimeout?clearTimeout:null,O="undefined"!=typeof setImmediate?setImmediate:null;function k(e){for(var t=r(f);null!==t;){if(null===t.callback)a(f);else if(t.startTime<=e)a(f),t.sortIndex=t.expirationTime,n(s,t);else break;t=r(f)}}function _(e){if(v=!1,k(e),!b)if(null!==r(s))b=!0,j||(j=!0,u());else{var t=r(f);null!==t&&A(_,t.startTime-e)}}var j=!1,T=-1,R=5,E=-1;function P(){return!!g||!(t.unstable_now()-E<R)}function C(){if(g=!1,j){var e=t.unstable_now();E=e;var n=!0;try{e:{b=!1,v&&(v=!1,m(T),T=-1),y=!0;var i=h;try{t:{for(k(e),p=r(s);null!==p&&!(p.expirationTime>e&&P());){var o=p.callback;if("function"==typeof o){p.callback=null,h=p.priorityLevel;var l=o(p.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof l){p.callback=l,k(e),n=!0;break t}p===r(s)&&a(s),k(e)}else a(s);p=r(s)}if(null!==p)n=!0;else{var c=r(f);null!==c&&A(_,c.startTime-e),n=!1}}break e}finally{p=null,h=i,y=!1}}}finally{n?u():j=!1}}}if("function"==typeof O)u=function(){O(C)};else if("undefined"!=typeof MessageChannel){var S=new MessageChannel,x=S.port2;S.port1.onmessage=C,u=function(){x.postMessage(null)}}else u=function(){w(C,0)};function A(e,n){T=w(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_requestPaint=function(){g=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,a,i){var o=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?o+i:o,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=0x3fffffff;break;case 4:l=1e4;break;default:l=5e3}return l=i+l,e={id:d++,callback:a,priorityLevel:e,startTime:i,expirationTime:l,sortIndex:-1},i>o?(e.sortIndex=i,n(f,e),null===r(s)&&e===r(f)&&(v?(m(T),T=-1):v=!0,A(_,i-o))):(e.sortIndex=l,n(s,e),b||y||(b=!0,j||(j=!0,u()))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},31848:(e,t,n)=>{"use strict";function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach(function(t){var r;r=n[t],t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function i(e){return function t(){for(var n=this,r=arguments.length,a=Array(r),i=0;i<r;i++)a[i]=arguments[i];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t.apply(n,[].concat(a,r))}}}function u(e){return({}).toString.call(e).includes("Object")}function o(e){return"function"==typeof e}n.d(t,{A:()=>p});var l=i(function(e,t){throw Error(e[t]||e.default)})({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),c={changes:function(e,t){return u(t)||l("changeType"),Object.keys(t).some(function(t){return!Object.prototype.hasOwnProperty.call(e,t)})&&l("changeField"),t},selector:function(e){o(e)||l("selectorType")},handler:function(e){o(e)||u(e)||l("handlerType"),u(e)&&Object.values(e).some(function(e){return!o(e)})&&l("handlersType")},initial:function(e){e||l("initialIsRequired"),u(e)||l("initialType"),Object.keys(e).length||l("initialContent")}};function s(e,t){return o(t)?t(e.current):t}function f(e,t){return e.current=a(a({},e.current),t),t}function d(e,t,n){return o(t)?t(e.current):Object.keys(n).forEach(function(n){var r;return null==(r=t[n])?void 0:r.call(t,e.current[n])}),n}let p={create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};c.initial(e),c.handler(t);var n={current:e},r=i(d)(n,t),a=i(f)(n),u=i(c.changes)(e),o=i(s)(n);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return c.selector(e),e(n.current)},function(e){(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}})(r,a,u,o)(e)}]}}},34007:(e,t,n)=>{"use strict";function r(e){return e&&e.__esModule?e:{default:e}}n.r(t),n.d(t,{_:()=>r})},58255:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});function r(e,t){if(!e)throw Error("Invariant failed")}},59950:(e,t,n)=>{"use strict";e.exports=n(28163)},73474:(e,t,n)=>{"use strict";n.d(t,{Mz:()=>O});var r=e=>Array.isArray(e)?e:[e],a=0,i=null,u=class{revision=a;_value;_lastValue;_isEqual=o;constructor(e,t=o){this._value=this._lastValue=e,this._isEqual=t}get value(){return i?.add(this),this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++a)}};function o(e,t){return e===t}function l(e){return e instanceof u||console.warn("Not a valid cell! ",e),e.value}var c=(e,t)=>!1;function s(){return function(e,t=o){return new u(null,t)}(0,c)}var f=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=s()),l(t)};Symbol();var d=0,p=Object.getPrototypeOf({}),h=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,y);tag=s();tags={};children={};collectionTag=null;id=d++},y={get:(e,t)=>(function(){let{value:n}=e,r=Reflect.get(n,t);if("symbol"==typeof t||t in p)return r;if("object"==typeof r&&null!==r){let n=e.children[t];return void 0===n&&(n=e.children[t]=function(e){return Array.isArray(e)?new b(e):new h(e)}(r)),n.tag&&l(n.tag),n.proxy}{let n=e.tags[t];return void 0===n&&((n=e.tags[t]=s()).value=r),l(n),r}})(),ownKeys:e=>(f(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},b=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],v);tag=s();tags={};children={};collectionTag=null;id=d++},v={get:([e],t)=>("length"===t&&f(e),y.get(e,t)),ownKeys:([e])=>y.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>y.getOwnPropertyDescriptor(e,t),has:([e],t)=>y.has(e,t)},g="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function w(){return{s:0,v:void 0,o:null,p:null}}function m(e,t={}){let n,r=w(),{resultEqualityCheck:a}=t,i=0;function u(){let t,u=r,{length:o}=arguments;for(let e=0;e<o;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=u.o;null===e&&(u.o=e=new WeakMap);let n=e.get(t);void 0===n?(u=w(),e.set(t,u)):u=n}else{let e=u.p;null===e&&(u.p=e=new Map);let n=e.get(t);void 0===n?(u=w(),e.set(t,u)):u=n}}let l=u;if(1===u.s)t=u.v;else if(t=e.apply(null,arguments),i++,a){let e=n?.deref?.()??n;null!=e&&a(e,t)&&(t=e,0!==i&&i--),n="object"==typeof t&&null!==t||"function"==typeof t?new g(t):t}return l.s=1,l.v=t,t}return u.clearCache=()=>{r=w(),u.resetResultsCount()},u.resultsCount=()=>i,u.resetResultsCount=()=>{i=0},u}var O=function(e,...t){let n="function"==typeof e?{memoize:e,memoizeOptions:t}:e,a=(...e)=>{let t,a=0,i=0,u={},o=e.pop();"object"==typeof o&&(u=o,o=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(o,`createSelector expects an output function after the inputs, but received: [${typeof o}]`);let{memoize:l,memoizeOptions:c=[],argsMemoize:s=m,argsMemoizeOptions:f=[],devModeChecks:d={}}={...n,...u},p=r(c),h=r(f),y=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let n=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${n}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),b=l(function(){return a++,o.apply(null,arguments)},...p);return Object.assign(s(function(){i++;let e=function(e,t){let n=[],{length:r}=e;for(let a=0;a<r;a++)n.push(e[a].apply(null,t));return n}(y,arguments);return t=b.apply(null,e)},...h),{resultFunc:o,memoizedResultFunc:b,dependencies:y,dependencyRecomputations:()=>i,resetDependencyRecomputations:()=>{i=0},lastResult:()=>t,recomputations:()=>a,resetRecomputations:()=>{a=0},memoize:l,argsMemoize:s})};return Object.assign(a,{withTypes:()=>a}),a}(m),k=Object.assign((e,t=O)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let n=Object.keys(e);return t(n.map(t=>e[t]),(...e)=>e.reduce((e,t,r)=>(e[n[r]]=t,e),{}))},{withTypes:()=>k})},86739:(e,t,n)=>{"use strict";function r(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}n.d(t,{_:()=>r})}}]);