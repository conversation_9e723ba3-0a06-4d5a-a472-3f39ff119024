"use strict";(()=>{var e={};e.id=7920,e.ids=[7920],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},59868:(e,r,t)=>{t.r(r),t.d(r,{config:()=>f,default:()=>p,routeModule:()=>x});var a={};t.r(a),t.d(a,{default:()=>l});var s=t(93433),i=t(20264),o=t(20584),n=t(15806),u=t(94506),d=t(72290);async function l(e,r){let t=await (0,n.getServerSession)(e,r,u.authOptions);if(!t)return r.status(401).json({error:"Unauthorized"});let a=(await (0,d.L)()).collection("experimental_flags"),{feature:s}=e.query;if("string"!=typeof s)return r.status(400).json({error:"Invalid feature"});if("PATCH"===e.method){if(t.user?.id!=="933023999770918932")return r.status(403).json({error:"Forbidden"});let{enabled:i}=e.body;if("boolean"!=typeof i)return r.status(400).json({error:"enabled boolean required"});await a.updateOne({feature:s},{$set:{feature:s,enabled:i,updatedBy:t.user.id,updatedAt:new Date}},{upsert:!0});let o=await a.findOne({feature:s});return r.status(200).json({flag:o})}return r.status(405).json({error:"Method not allowed"})}let p=(0,o.M)(a,"default"),f=(0,o.M)(a,"config"),x=new s.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/experimental/flags/[feature]",pathname:"/api/experimental/flags/[feature]",bundlePath:"",filename:""},userland:a})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(59868));module.exports=a})();