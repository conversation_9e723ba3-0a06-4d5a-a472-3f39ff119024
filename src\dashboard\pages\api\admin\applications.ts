import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { MongoClient } from 'mongodb';
import { dashboardConfig } from '../../../core/config';

// Hardcoded developer ID
const DEVELOPER_ID = '933023999770918932';

// Reuse connection pattern
let cachedClient: MongoClient | null = null;
const mongoUrl = dashboardConfig.database?.url || 'mongodb://localhost:27017';
const dbName = dashboardConfig.database?.name || 'discord_bot';

async function getDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl, {
      ...(dashboardConfig.database?.options || {}),
    });
  }
  return cachedClient.db(dbName);
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user is admin
    const isAdmin = (session.user as any)?.isAdmin;
    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    const db = await getDb();

    if (req.method === 'GET') {
      // Handle different query parameters
      const { type } = req.query;
      
      if (type === 'experimental-settings') {
        // Only allow the developer to access experimental settings
        if (session.user.id !== DEVELOPER_ID) {
          return res.status(403).json({ error: 'Forbidden' });
        }
        
        const collection = db.collection('experimental_settings');
        const settings = await collection.findOne({ key: 'applications_enabled' }) || { enabled: false };
        return res.status(200).json({ enabled: settings.enabled });
      }
      
      // Get regular applications
      const applicationsCollection = db.collection('applications');
      const applications = await applicationsCollection.find({}).toArray();
      
      // Calculate stats
      const stats = {
        total: applications.length,
        pending: applications.filter(app => app.status === 'pending').length,
        approved: applications.filter(app => app.status === 'approved').length,
        rejected: applications.filter(app => app.status === 'rejected').length,
        recentIncrease: 0 // TODO: Calculate based on date comparison
      };
      
      return res.status(200).json({ applications, stats });
    }

    if (req.method === 'POST') {
      const { type, ...data } = req.body;
      
      if (type === 'experimental-settings') {
        // Only allow the developer to modify experimental settings
        if (session.user.id !== DEVELOPER_ID) {
          return res.status(403).json({ error: 'Forbidden' });
        }
        
        const { enabled } = data;
        if (typeof enabled !== 'boolean') {
          return res.status(400).json({ error: 'Invalid request body' });
        }

        const collection = db.collection('experimental_settings');
        await collection.updateOne(
          { key: 'applications_enabled' },
          { $set: { enabled, updatedAt: new Date(), updatedBy: session.user.id } },
          { upsert: true }
        );

        return res.status(200).json({ enabled });
      }
      
      // Handle regular application creation
      const applicationsCollection = db.collection('applications');
      const newApplication = {
        ...data,
        createdAt: new Date(),
        createdBy: session.user.id,
        status: 'pending'
      };
      
      const result = await applicationsCollection.insertOne(newApplication);
      return res.status(201).json({ id: result.insertedId, ...newApplication });
    }

    if (req.method === 'PATCH') {
      // Handle application status updates
      const { applicationId, action } = req.body;
      
      if (!applicationId || !action) {
        return res.status(400).json({ error: 'Missing applicationId or action' });
      }
      
      const applicationsCollection = db.collection('applications');
      const updateData = {
        status: action === 'approve' ? 'approved' : 'rejected',
        updatedAt: new Date(),
        updatedBy: session.user.id
      };
      
      await applicationsCollection.updateOne(
        { _id: applicationId },
        { $set: updateData }
      );
      
      return res.status(200).json({ success: true });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error in applications API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 