(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2365],{1620:(e,n,o)=>{"use strict";o.r(n),o.d(n,{default:()=>$});var s=o(94513),r=o(94285),a=o(43700),t=o(79028),i=o(64349),d=o(5142),l=o(28365),c=o(7476),h=o(36468),u=o(1871),g=o(78813),m=o(29484),f=o(46949),x=o(81139),j=o(95066),p=o(53083),b=o(24490),y=o(31840),C=o(29607),v=o(30301),S=o(55206),E=o(5130),w=o(3037),A=o(7836),k=o(84622);o(75632),o(82273);var z=o(97119),F=o(35044),T=o(86225),I=o(77072);let D=o.n(I)()(()=>Promise.all([o.e(4301),o.e(9114),o.e(7170),o.e(2432),o.e(1281),o.e(3920),o.e(3119),o.e(9176),o.e(1307),o.e(727),o.e(3976),o.e(2774),o.e(879),o.e(9984),o.e(2048),o.e(8883),o.e(5652),o.e(4754),o.e(523),o.e(7889),o.e(8360),o.e(8063),o.e(9284),o.e(5300),o.e(1349),o.e(7102),o.e(6835),o.e(246),o.e(8255),o.e(393),o.e(9450),o.e(3704),o.e(7897),o.e(4599),o.e(3640),o.e(4914),o.e(8637),o.e(4020),o.e(9),o.e(5388),o.e(6733)]).then(o.bind(o,36733)),{loadableGenerated:{webpack:()=>[36733]},ssr:!1}),O={"voice-mistress":{color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"}},tickets:{color:"orange",gradient:{from:"rgba(237, 137, 54, 0.4)",to:"rgba(237, 137, 54, 0.1)"}},"welcome-goodbye":{color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"}},example:{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"}},default:{color:"teal",gradient:{from:"rgba(49, 151, 149, 0.4)",to:"rgba(49, 151, 149, 0.1)"}}},_=e=>{var n,o,t,c,v,k,z;let{addon:I,onSave:_,onToggle:$,onEdit:P,onDelete:N}=e,[L,M]=(0,r.useState)(!1),[R,W]=(0,r.useState)(I.config||""),[Y,B]=(0,r.useState)(null),[J,U]=(0,r.useState)(!1),q=(0,A.d)();(0,r.useEffect)(()=>{W(I.config||"")},[I.config]);let G=async()=>{try{T.qg(R||""),B(null);let e=await fetch("/api/admin/addons/".concat(I.name),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({config:R})});if(!e.ok){let n=await e.json();throw Error(n.message||"Failed to save configuration")}_(R),M(!1),q({title:"Success",description:"Configuration saved successfully",status:"success",duration:3e3})}catch(e){B(e.message||"Invalid YAML format"),q({title:"Error",description:e.message||"Invalid YAML format",status:"error",duration:5e3})}};return(0,s.jsx)(d.Z,{bg:(null==(n=O[I.name])?void 0:n.color)?"linear-gradient(135deg, ".concat(O[I.name].gradient.from,", ").concat(O[I.name].gradient.to,")"):"gray.900",backdropFilter:"blur(10px)",borderWidth:2,borderColor:(null==(o=O[I.name])?void 0:o.color)?"".concat(O[I.name].color,".400"):"gray.600",rounded:"xl",overflow:"hidden",transition:"all 0.2s",_hover:{transform:"translateY(-2px)",boxShadow:(null==(t=O[I.name])?void 0:t.color)?"0 4px 20px ".concat(O[I.name].gradient.from):"none"},children:(0,s.jsx)(l.b,{children:L?(0,s.jsxs)(w.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(D,{value:R,onChange:e=>{W(e||""),B(null)}}),Y&&(0,s.jsx)(E.E,{color:"red.400",fontSize:"sm",children:Y}),(0,s.jsxs)(u.z,{justify:"flex-end",spacing:2,children:[(0,s.jsx)(i.$,{variant:"ghost",onClick:()=>{M(!1),W(I.config||""),B(null)},children:"Cancel"}),(0,s.jsx)(i.$,{colorScheme:"purple",onClick:G,isDisabled:!!Y,children:"Save Changes"})]})]}):(0,s.jsxs)(w.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(u.z,{justify:"space-between",children:[(0,s.jsxs)(u.z,{children:[(0,s.jsx)(m.I,{as:F.FiPackage,color:"".concat((null==(c=O[I.name])?void 0:c.color)||"gray",".400"),boxSize:5}),(0,s.jsx)(g.D,{size:"md",color:"white",children:I.name})]}),(0,s.jsx)(a.E,{colorScheme:I.enabled&&(null==(v=O[I.name])?void 0:v.color)||"gray",children:I.enabled?"Active":"Inactive"})]}),(0,s.jsx)(E.E,{color:"gray.300",fontSize:"sm",noOfLines:2,children:I.description}),(0,s.jsxs)(u.z,{children:[(0,s.jsxs)(E.E,{color:"gray.400",fontSize:"xs",children:["v",I.version]}),(0,s.jsxs)(E.E,{color:"gray.400",fontSize:"xs",children:["by ",I.author]})]}),(0,s.jsx)(h.c,{borderColor:"whiteAlpha.200"}),(0,s.jsxs)(u.z,{justify:"space-between",children:[(0,s.jsx)(S.d,{isChecked:I.enabled,onChange:()=>$(I),colorScheme:(null==(k=O[I.name])?void 0:k.color)||"gray"}),(0,s.jsxs)(u.z,{spacing:2,children:[(0,s.jsx)(i.$,{size:"sm",leftIcon:(0,s.jsx)(m.I,{as:F.FiCode}),variant:"ghost",colorScheme:(null==(z=O[I.name])?void 0:z.color)||"gray",onClick:()=>P(I),children:"Edit Config"}),I.isCustomAddon&&N&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.$,{size:"sm",leftIcon:(0,s.jsx)(m.I,{as:F.FiTrash2}),variant:"ghost",colorScheme:"red",onClick:()=>U(!0),children:"Delete"}),(0,s.jsxs)(f.aF,{isOpen:J,onClose:()=>U(!1),children:[(0,s.jsx)(C.m,{}),(0,s.jsxs)(p.$,{children:[(0,s.jsx)(y.r,{children:"Delete Custom Addon"}),(0,s.jsx)(j.s,{}),(0,s.jsx)(x.c,{children:(0,s.jsxs)(E.E,{children:["Are you sure you want to delete ",(0,s.jsx)("strong",{children:I.name}),"? This action cannot be undone and will permanently remove all files for this custom addon."]})}),(0,s.jsxs)(b.j,{children:[(0,s.jsx)(i.$,{variant:"ghost",mr:3,onClick:()=>U(!1),children:"Cancel"}),(0,s.jsx)(i.$,{colorScheme:"red",onClick:()=>{N(I),U(!1)},children:"Delete"})]})]})]})]})]})]})]})})},I.name)};function $(){var e,n;let[o,a]=(0,r.useState)([]),[d,l]=(0,r.useState)([]),[h,S]=(0,r.useState)(null),[I,$]=(0,r.useState)(""),{isOpen:P,onOpen:N,onClose:L}=(0,k.j)(),M=(0,A.d)();(0,r.useEffect)(()=>{P||(S(null),$(""))},[P]);let R=async()=>{try{let o=await fetch("/api/admin/addons",{credentials:"include",headers:{"Content-Type":"application/json"}});if(!o.ok){let e=await o.json();if(401===o.status||403===o.status){window.location.href="/signin";return}throw Error(e.message||"Failed to fetch addons")}let s=await o.json();if(s.builtInAddons&&s.customAddons)a(s.builtInAddons),l(s.customAddons);else{var e,n;let o=(null==(e=s.addons)?void 0:e.filter(e=>!e.isCustomAddon))||[],r=(null==(n=s.addons)?void 0:n.filter(e=>e.isCustomAddon))||[];a(o),l(r)}}catch(e){M({title:"Error",description:e instanceof Error?e.message:"Failed to fetch addons",status:"error",duration:5e3})}};(0,r.useEffect)(()=>{R()},[]);let W=async e=>{try{if(!(await fetch("/api/admin/addons/".concat(e.name),{method:"PATCH",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({enabled:!e.enabled})})).ok)throw Error("Failed to toggle addon");try{await fetch("/api/admin/addons/reload",{method:"POST",credentials:"include"})}catch(e){}let n=n=>n.map(n=>n.name===e.name?{...n,enabled:!n.enabled}:n);e.isCustomAddon?l(n):a(n),M({title:"Success",description:"Addon ".concat(e.enabled?"disabled":"enabled"," successfully"),status:"success",duration:3e3})}catch(e){M({title:"Error",description:"Failed to toggle addon",status:"error",duration:5e3})}},Y=(e,n)=>{let o=o=>o.map(o=>o.name===e.name?{...o,config:n}:o);e.isCustomAddon?l(o):a(o)},B=async e=>{try{let n=await fetch("/api/admin/addons/".concat(e.name,"/config"),{credentials:"include"});if(!n.ok){let e=await n.json();throw Error(e.error||"Failed to fetch addon config: ".concat(n.status))}let o=await n.json();S(e),setTimeout(()=>{$(o.config||""),N()},100)}catch(e){M({title:"Error Fetching Config",description:e.message,status:"error",duration:5e3,isClosable:!0})}},J=async()=>{if(h)try{if(T.qg(I),!(await fetch("/api/admin/addons/".concat(h.name,"/config"),{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({config:I})})).ok)throw Error("Failed to save configuration");M({title:"Success",description:"Configuration saved successfully",status:"success",duration:3e3}),L(),R()}catch(e){M({title:"Error",description:e.message||"Failed to save configuration",status:"error",duration:5e3})}},U=async()=>{try{await fetch("/api/admin/addons/reload",{method:"POST",credentials:"include"}),M({title:"Success",description:"Addons reloaded successfully",status:"success",duration:3e3}),R()}catch(e){M({title:"Error",description:"Failed to reload addons",status:"error",duration:5e3})}},q=async()=>{try{await fetch("/api/admin/commands/refresh",{method:"POST",credentials:"include"}),M({title:"Success",description:"Discord commands refreshed successfully",status:"success",duration:3e3})}catch(e){M({title:"Error",description:"Failed to refresh commands",status:"error",duration:5e3})}},G=async e=>{try{let n=await fetch("/api/admin/addons/".concat(e.name),{method:"DELETE",credentials:"include"});if(!n.ok){let e=await n.json();throw Error(e.details||e.error||"Failed to delete addon")}try{await fetch("/api/admin/addons/reload",{method:"POST",credentials:"include"})}catch(e){}l(n=>n.filter(n=>n.name!==e.name)),M({title:"Success",description:"Custom addon deleted successfully",status:"success",duration:3e3})}catch(e){M({title:"Error",description:e instanceof Error?e.message:"Failed to delete addon",status:"error",duration:5e3})}};return(0,s.jsxs)(z.A,{children:[(0,s.jsx)(c.m,{maxW:"container.xl",py:8,children:(0,s.jsxs)(w.T,{spacing:8,align:"stretch",children:[(0,s.jsx)(t.a,{bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"purple.400",boxShadow:"0 0 15px rgba(159, 122, 234, 0.4)",children:(0,s.jsxs)(u.z,{justify:"space-between",align:"center",children:[(0,s.jsxs)(w.T,{align:"start",spacing:2,children:[(0,s.jsx)(g.D,{size:"xl",bgGradient:"linear(to-r, purple.300, pink.400)",bgClip:"text",children:"Bot Addons Management"}),(0,s.jsx)(E.E,{color:"gray.300",children:"Manage built-in addons and custom addons created with the addon builder"}),(0,s.jsx)(E.E,{color:"gray.500",fontSize:"sm",children:'Use "Reload Addons" to refresh the bot\'s addon system. Use "Refresh Commands" to update Discord\'s command list.'})]}),(0,s.jsxs)(u.z,{spacing:2,children:[(0,s.jsx)(i.$,{leftIcon:(0,s.jsx)(m.I,{as:F.FiRefreshCw}),colorScheme:"purple",variant:"outline",onClick:U,children:"Reload Addons"}),(0,s.jsx)(i.$,{leftIcon:(0,s.jsx)(m.I,{as:F.FiZap}),colorScheme:"orange",variant:"outline",onClick:q,children:"Refresh Commands"})]})]})}),(0,s.jsxs)(t.a,{children:[(0,s.jsxs)(g.D,{size:"lg",mb:4,color:"blue.300",children:["\uD83D\uDEE0️ Built-in Addons (",o.length,")"]}),(0,s.jsx)(E.E,{color:"gray.400",mb:6,fontSize:"sm",children:"Core bot functionality - these addons cannot be deleted"}),(0,s.jsx)(v.r,{columns:{base:1,md:2,lg:3},spacing:6,children:o.map(e=>(0,s.jsx)(_,{addon:e,onSave:n=>Y(e,n),onToggle:W,onEdit:B},e.name))}),0===o.length&&(0,s.jsx)(t.a,{bg:"gray.800",p:6,rounded:"lg",textAlign:"center",borderColor:"gray.600",borderWidth:1,children:(0,s.jsx)(E.E,{color:"gray.400",children:"No built-in addons found"})})]}),(0,s.jsxs)(t.a,{children:[(0,s.jsxs)(g.D,{size:"lg",mb:4,color:"green.300",children:["⚗️ Custom Addons (",d.length,")"]}),(0,s.jsx)(E.E,{color:"gray.400",mb:6,fontSize:"sm",children:"Addons created with the addon builder - these can be edited or deleted"}),(0,s.jsx)(v.r,{columns:{base:1,md:2,lg:3},spacing:6,children:d.map(e=>(0,s.jsx)(_,{addon:e,onSave:n=>Y(e,n),onToggle:W,onEdit:B,onDelete:G},e.name))}),0===d.length&&(0,s.jsxs)(t.a,{bg:"gray.800",p:6,rounded:"lg",textAlign:"center",borderColor:"gray.600",borderWidth:1,children:[(0,s.jsx)(E.E,{color:"gray.400",children:"No custom addons created yet"}),(0,s.jsx)(E.E,{color:"gray.500",fontSize:"sm",mt:2,children:"Use the Addon Builder to create your first custom addon!"})]})]})]})}),(0,s.jsxs)(f.aF,{isOpen:P,onClose:L,size:"6xl",children:[(0,s.jsx)(C.m,{backdropFilter:"blur(10px)"}),(0,s.jsxs)(p.$,{bg:"gray.800",border:"1px solid",borderColor:"".concat((null==(e=O[null==h?void 0:h.name])?void 0:e.color)||"gray",".500"),maxW:"1200px",children:[(0,s.jsx)(y.r,{children:(0,s.jsxs)(u.z,{children:[(0,s.jsx)(m.I,{as:F.FiSettings}),(0,s.jsxs)(E.E,{children:["Configure ",null==h?void 0:h.name]})]})}),(0,s.jsx)(j.s,{}),(0,s.jsx)(x.c,{maxH:"70vh",overflowY:"auto",children:(0,s.jsxs)(w.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(E.E,{color:"gray.400",fontSize:"sm",children:"Edit the configuration in YAML format. Changes will be saved to config.yml"}),(0,s.jsx)(D,{value:I,onChange:e=>$(e||""),height:"60vh"})]})}),(0,s.jsxs)(b.j,{children:[(0,s.jsx)(i.$,{variant:"ghost",mr:3,onClick:L,children:"Cancel"}),(0,s.jsx)(i.$,{colorScheme:(null==(n=O[null==h?void 0:h.name])?void 0:n.color)||"gray",leftIcon:(0,s.jsx)(m.I,{as:F.FiSave}),onClick:J,children:"Save Changes"})]})]})]})]})}},40295:(e,n,o)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/addons",function(){return o(1620)}])}},e=>{var n=n=>e(e.s=n);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,636,7398,1203,8792],()=>n(40295)),_N_E=e.O()}]);