"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-c1f28c14"],{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/fader/index.js":
/*!********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/fader/index.js ***!
  \********************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    FADER_STYLES: function() {\n        return FADER_STYLES;\n    },\n    Fader: function() {\n        return Fader;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nconst Fader = /*#__PURE__*/ (0, _react.forwardRef)(function Fader(param, ref) {\n    let { stop, blur, side, style, height } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        ref: ref,\n        \"aria-hidden\": true,\n        \"data-nextjs-scroll-fader\": true,\n        className: \"nextjs-scroll-fader\",\n        \"data-side\": side,\n        style: {\n            '--stop': stop,\n            '--blur': blur,\n            '--height': \"\" + height + \"px\",\n            ...style\n        }\n    });\n});\nconst FADER_STYLES = '\\n  .nextjs-scroll-fader {\\n    --blur: 1px;\\n    --stop: 25%;\\n    --height: 150px;\\n    --color-bg: var(--color-background-100);\\n    position: absolute;\\n    pointer-events: none;\\n    user-select: none;\\n    width: 100%;\\n    height: var(--height);\\n    left: 0;\\n    backdrop-filter: blur(var(--blur));\\n\\n    &[data-side=\"top\"] {\\n      top: 0;\\n      background: linear-gradient(to top, transparent, var(--color-bg));\\n      mask-image: linear-gradient(to bottom, var(--color-bg) var(--stop), transparent);\\n    }\\n  }\\n\\n';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/fader/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HotlinkedText\", ({\n    enumerable: true,\n    get: function() {\n        return HotlinkedText;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\"));\nconst _magicidentifier = __webpack_require__(/*! ../../../../../../shared/lib/magic-identifier */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/magic-identifier.js\");\nconst linkRegex = /https?:\\/\\/[^\\s/$.?#].[^\\s)'\"]*/i;\nconst splitRegexp = new RegExp(\"(\" + _magicidentifier.MAGIC_IDENTIFIER_REGEX.source + \"|\\\\s+)\");\nconst HotlinkedText = function HotlinkedText(props) {\n    const { text, matcher } = props;\n    const wordsAndWhitespaces = text.split(splitRegexp);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: wordsAndWhitespaces.map((word, index)=>{\n            if (linkRegex.test(word)) {\n                const link = linkRegex.exec(word);\n                const href = link[0];\n                // If link matcher is present but the link doesn't match, don't turn it into a link\n                if (typeof matcher === 'function' && !matcher(href)) {\n                    return word;\n                }\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.default.Fragment, {\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noreferrer noopener\",\n                        children: word\n                    })\n                }, \"link-\" + index);\n            }\n            try {\n                const decodedWord = (0, _magicidentifier.decodeMagicIdentifier)(word);\n                if (decodedWord !== word) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"i\", {\n                        children: [\n                            '{',\n                            decodedWord,\n                            '}'\n                        ]\n                    }, \"ident-\" + index);\n                }\n            } catch (e) {\n                return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"i\", {\n                    children: [\n                        '{',\n                        word,\n                        \" (decoding failed: \",\n                        '' + e,\n                        \")\",\n                        '}'\n                    ]\n                }, \"ident-\" + index);\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.default.Fragment, {\n                children: word\n            }, \"text-\" + index);\n        })\n    });\n};\n_c = HotlinkedText;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c;\n$RefreshReg$(_c, \"HotlinkedText\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hydration-diff/diff-view.js":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hydration-diff/diff-view.js ***!
  \*********************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PseudoHtmlDiff\", ({\n    enumerable: true,\n    get: function() {\n        return PseudoHtmlDiff;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nconst _collapseicon = __webpack_require__(/*! ../../icons/collapse-icon */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/collapse-icon.js\");\nfunction PseudoHtmlDiff(param) {\n    let { firstContent, secondContent, hydrationMismatchType, reactOutputComponentDiff, ...props } = param;\n    const [isDiffCollapsed, toggleCollapseHtml] = (0, _react.useState)(true);\n    const htmlComponents = (0, _react.useMemo)(()=>{\n        const componentStacks = [];\n        const reactComponentDiffLines = reactOutputComponentDiff.split('\\n');\n        reactComponentDiffLines.forEach((line, index)=>{\n            const isDiffLine = line[0] === '+' || line[0] === '-';\n            const isHighlightedLine = line[0] === '>';\n            const hasSign = isDiffLine || isHighlightedLine;\n            const sign = hasSign ? line[0] : '';\n            const signIndex = hasSign ? line.indexOf(sign) : -1;\n            const [prefix, suffix] = hasSign ? [\n                line.slice(0, signIndex),\n                line.slice(signIndex + 1)\n            ] : [\n                line,\n                ''\n            ];\n            if (isDiffLine) {\n                componentStacks.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                    \"data-nextjs-container-errors-pseudo-html-line\": true,\n                    \"data-nextjs-container-errors-pseudo-html--diff\": sign === '+' ? 'add' : 'remove',\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                        children: [\n                            prefix,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                \"data-nextjs-container-errors-pseudo-html-line-sign\": true,\n                                children: sign\n                            }),\n                            suffix,\n                            '\\n'\n                        ]\n                    })\n                }, 'comp-diff' + index));\n            } else {\n                // In general, if it's not collapsed, show the whole diff\n                componentStacks.push(/*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n                    \"data-nextjs-container-errors-pseudo-html-line\": true,\n                    ...isHighlightedLine ? {\n                        'data-nextjs-container-errors-pseudo-html--diff': 'error'\n                    } : undefined,\n                    children: [\n                        prefix,\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                            \"data-nextjs-container-errors-pseudo-html-line-sign\": true,\n                            children: sign\n                        }),\n                        suffix,\n                        '\\n'\n                    ]\n                }, 'comp-diff' + index));\n            }\n        });\n        return componentStacks;\n    }, [\n        reactOutputComponentDiff\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-container-errors-pseudo-html\": true,\n        \"data-nextjs-container-errors-pseudo-html-collapse\": isDiffCollapsed,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                tabIndex: 10,\n                \"data-nextjs-container-errors-pseudo-html-collapse-button\": true,\n                onClick: ()=>toggleCollapseHtml(!isDiffCollapsed),\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_collapseicon.CollapseIcon, {\n                    collapsed: isDiffCollapsed\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"pre\", {\n                ...props,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                    children: htmlComponents\n                })\n            })\n        ]\n    });\n}\n_c = PseudoHtmlDiff;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=diff-view.js.map\nvar _c;\n$RefreshReg$(_c, \"PseudoHtmlDiff\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hydration-diff/diff-view.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    lock: function() {\n        return lock;\n    },\n    unlock: function() {\n        return unlock;\n    }\n});\nlet previousBodyPaddingRight;\nlet previousBodyOverflowSetting;\nlet activeLocks = 0;\nfunction lock() {\n    setTimeout(()=>{\n        if (activeLocks++ > 0) {\n            return;\n        }\n        const scrollBarGap = window.innerWidth - document.documentElement.clientWidth;\n        if (scrollBarGap > 0) {\n            previousBodyPaddingRight = document.body.style.paddingRight;\n            document.body.style.paddingRight = \"\" + scrollBarGap + \"px\";\n        }\n        previousBodyOverflowSetting = document.body.style.overflow;\n        document.body.style.overflow = 'hidden';\n    });\n}\nfunction unlock() {\n    setTimeout(()=>{\n        if (activeLocks === 0 || --activeLocks !== 0) {\n            return;\n        }\n        if (previousBodyPaddingRight !== undefined) {\n            document.body.style.paddingRight = previousBodyPaddingRight;\n            previousBodyPaddingRight = undefined;\n        }\n        if (previousBodyOverflowSetting !== undefined) {\n            document.body.style.overflow = previousBodyOverflowSetting;\n            previousBodyOverflowSetting = undefined;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=body-locker.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Overlay\", ({\n    enumerable: true,\n    get: function() {\n        return _overlay.Overlay;\n    }\n}));\nconst _overlay = __webpack_require__(/*! ./overlay */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjVfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L3VpL2NvbXBvbmVudHMvb3ZlcmxheS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OzJDQUFTQTs7O2VBQUFBLFNBQUFBLE9BQU87OztxQ0FBUSIsInNvdXJjZXMiOlsiRDpcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxyZWFjdC1kZXYtb3ZlcmxheVxcdWlcXGNvbXBvbmVudHNcXG92ZXJsYXlcXGluZGV4LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBPdmVybGF5IH0gZnJvbSAnLi9vdmVybGF5J1xuIl0sIm5hbWVzIjpbIk92ZXJsYXkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js":
/*!************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js ***!
  \************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Overlay\", ({\n    enumerable: true,\n    get: function() {\n        return Overlay;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\"));\nconst _bodylocker = __webpack_require__(/*! ./body-locker */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/body-locker.js\");\nconst Overlay = function Overlay(param) {\n    _s();\n    let { className, children, fixed, ...props } = param;\n    _react.useEffect({\n        \"Overlay.useEffect\": ()=>{\n            (0, _bodylocker.lock)();\n            return ({\n                \"Overlay.useEffect\": ()=>{\n                    (0, _bodylocker.unlock)();\n                }\n            })[\"Overlay.useEffect\"];\n        }\n    }[\"Overlay.useEffect\"], []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-nextjs-dialog-overlay\": true,\n        className: className,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-nextjs-dialog-backdrop\": true,\n                \"data-nextjs-dialog-backdrop-fixed\": fixed ? true : undefined\n            }),\n            children\n        ]\n    });\n};\n_s(Overlay, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Overlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=overlay.js.map\nvar _c;\n$RefreshReg$(_c, \"Overlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjVfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L3VpL2NvbXBvbmVudHMvb3ZlcmxheS9vdmVybGF5LmpzIiwibWFwcGluZ3MiOiI7Ozs7OzsyQ0FpQ1NBOzs7ZUFBQUE7Ozs7OzZFQWpDYzt3Q0FDTTtBQVE3QixnQkFBd0MsU0FBU0EsUUFBUSxLQUt4RDs7SUFMd0QsTUFDdkRDLFNBQVMsRUFDVEMsUUFBUSxFQUNSQyxLQUFLLEVBQ0wsR0FBR0MsT0FDSixHQUx3RDtJQU12REMsT0FBTUMsU0FBUzs2QkFBQztZQUNkQyxDQUFBQSxHQUFBQSxZQUFBQSxJQUFBQTtZQUNBO3FDQUFPO29CQUNMQyxDQUFBQSxHQUFBQSxZQUFBQSxNQUFBQTtnQkFDRjs7UUFDRjs0QkFBRyxFQUFFO0lBRUwscUJBQ0Usc0JBQUNDLE9BQUFBO1FBQUlDLDRCQUEwQjtRQUFDVCxXQUFXQTtRQUFZLEdBQUdHLEtBQUs7OzBCQUM3RCxxQkFBQ0ssT0FBQUE7Z0JBQ0NFLDZCQUEyQjtnQkFDM0JDLHFDQUFtQ1QsUUFBUSxPQUFPVTs7WUFFbkRYOzs7QUFHUDs7S0F0Qk1GIiwic291cmNlcyI6WyJEOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcb3ZlcmxheVxcb3ZlcmxheS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBsb2NrLCB1bmxvY2sgfSBmcm9tICcuL2JvZHktbG9ja2VyJ1xuXG5leHBvcnQgdHlwZSBPdmVybGF5UHJvcHMgPSB7XG4gIGNoaWxkcmVuPzogUmVhY3QuUmVhY3ROb2RlXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xuICBmaXhlZD86IGJvb2xlYW5cbn1cblxuY29uc3QgT3ZlcmxheTogUmVhY3QuRkM8T3ZlcmxheVByb3BzPiA9IGZ1bmN0aW9uIE92ZXJsYXkoe1xuICBjbGFzc05hbWUsXG4gIGNoaWxkcmVuLFxuICBmaXhlZCxcbiAgLi4ucHJvcHNcbn0pIHtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBsb2NrKClcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgdW5sb2NrKClcbiAgICB9XG4gIH0sIFtdKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBkYXRhLW5leHRqcy1kaWFsb2ctb3ZlcmxheSBjbGFzc05hbWU9e2NsYXNzTmFtZX0gey4uLnByb3BzfT5cbiAgICAgIDxkaXZcbiAgICAgICAgZGF0YS1uZXh0anMtZGlhbG9nLWJhY2tkcm9wXG4gICAgICAgIGRhdGEtbmV4dGpzLWRpYWxvZy1iYWNrZHJvcC1maXhlZD17Zml4ZWQgPyB0cnVlIDogdW5kZWZpbmVkfVxuICAgICAgLz5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgeyBPdmVybGF5IH1cbiJdLCJuYW1lcyI6WyJPdmVybGF5IiwiY2xhc3NOYW1lIiwiY2hpbGRyZW4iLCJmaXhlZCIsInByb3BzIiwiUmVhY3QiLCJ1c2VFZmZlY3QiLCJsb2NrIiwidW5sb2NrIiwiZGl2IiwiZGF0YS1uZXh0anMtZGlhbG9nLW92ZXJsYXkiLCJkYXRhLW5leHRqcy1kaWFsb2ctYmFja2Ryb3AiLCJkYXRhLW5leHRqcy1kaWFsb2ctYmFja2Ryb3AtZml4ZWQiLCJ1bmRlZmluZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/overlay.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"styles\", ({\n    enumerable: true,\n    get: function() {\n        return styles;\n    }\n}));\nconst styles = \"\\n  [data-nextjs-dialog-overlay] {\\n    position: fixed;\\n    top: 0;\\n    right: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 9000;\\n\\n    display: flex;\\n    align-content: center;\\n    align-items: center;\\n    flex-direction: column;\\n    padding: 10vh 15px 0;\\n  }\\n\\n  @media (max-height: 812px) {\\n    [data-nextjs-dialog-overlay] {\\n      padding: 15px 15px 0;\\n    }\\n  }\\n\\n  [data-nextjs-dialog-backdrop] {\\n    position: fixed;\\n    top: 0;\\n    right: 0;\\n    bottom: 0;\\n    left: 0;\\n    background-color: var(--color-backdrop);\\n    backdrop-filter: blur(10px);\\n    pointer-events: all;\\n    z-index: -1;\\n  }\\n\\n  [data-nextjs-dialog-backdrop-fixed] {\\n    cursor: not-allowed;\\n    -webkit-backdrop-filter: blur(8px);\\n    backdrop-filter: blur(8px);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=styles.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjVfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L3VpL2NvbXBvbmVudHMvb3ZlcmxheS9zdHlsZXMuanMiLCJtYXBwaW5ncyI6Ijs7OzswQ0F5Q1NBOzs7ZUFBQUE7OztBQXpDVCxNQUFNQSxTQUFVIiwic291cmNlcyI6WyJEOlxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXHJlYWN0LWRldi1vdmVybGF5XFx1aVxcY29tcG9uZW50c1xcb3ZlcmxheVxcc3R5bGVzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzdHlsZXMgPSBgXG4gIFtkYXRhLW5leHRqcy1kaWFsb2ctb3ZlcmxheV0ge1xuICAgIHBvc2l0aW9uOiBmaXhlZDtcbiAgICB0b3A6IDA7XG4gICAgcmlnaHQ6IDA7XG4gICAgYm90dG9tOiAwO1xuICAgIGxlZnQ6IDA7XG4gICAgei1pbmRleDogOTAwMDtcblxuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24tY29udGVudDogY2VudGVyO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBwYWRkaW5nOiAxMHZoIDE1cHggMDtcbiAgfVxuXG4gIEBtZWRpYSAobWF4LWhlaWdodDogODEycHgpIHtcbiAgICBbZGF0YS1uZXh0anMtZGlhbG9nLW92ZXJsYXldIHtcbiAgICAgIHBhZGRpbmc6IDE1cHggMTVweCAwO1xuICAgIH1cbiAgfVxuXG4gIFtkYXRhLW5leHRqcy1kaWFsb2ctYmFja2Ryb3BdIHtcbiAgICBwb3NpdGlvbjogZml4ZWQ7XG4gICAgdG9wOiAwO1xuICAgIHJpZ2h0OiAwO1xuICAgIGJvdHRvbTogMDtcbiAgICBsZWZ0OiAwO1xuICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWNvbG9yLWJhY2tkcm9wKTtcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XG4gICAgcG9pbnRlci1ldmVudHM6IGFsbDtcbiAgICB6LWluZGV4OiAtMTtcbiAgfVxuXG4gIFtkYXRhLW5leHRqcy1kaWFsb2ctYmFja2Ryb3AtZml4ZWRdIHtcbiAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xuICAgIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDhweCk7XG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDhweCk7XG4gIH1cbmBcblxuZXhwb3J0IHsgc3R5bGVzIH1cbiJdLCJuYW1lcyI6WyJzdHlsZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/styles.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ShadowPortal\", ({\n    enumerable: true,\n    get: function() {\n        return ShadowPortal;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\"));\nconst _reactdom = __webpack_require__(/*! react-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-dom@19.1.0_react@19.1.0/node_modules/react-dom/index.js\");\nconst _shared = __webpack_require__(/*! ../../shared */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nfunction ShadowPortal(param) {\n    _s();\n    let { children } = param;\n    let portalNode = _react.useRef(null);\n    let shadowNode = _react.useRef(null);\n    let [, forceUpdate] = _react.useState();\n    // Don't use useLayoutEffect here, as it will cause warnings during SSR in React 18.\n    // Don't use useSyncExternalStore as an SSR gate unless you verified it doesn't\n    // downgrade a Transition of the initial root render to a sync render or\n    // we can assure the root render is not a Transition.\n    _react.useEffect({\n        \"ShadowPortal.useEffect\": ()=>{\n            const ownerDocument = document;\n            portalNode.current = ownerDocument.createElement('nextjs-portal');\n            // load default color preference from localstorage\n            if (typeof localStorage !== 'undefined') {\n                const theme = localStorage.getItem(_shared.STORAGE_KEY_THEME);\n                if (theme === 'dark') {\n                    portalNode.current.classList.add('dark');\n                    portalNode.current.classList.remove('light');\n                } else if (theme === 'light') {\n                    portalNode.current.classList.remove('dark');\n                    portalNode.current.classList.add('light');\n                }\n            }\n            shadowNode.current = portalNode.current.attachShadow({\n                mode: 'open'\n            });\n            ownerDocument.body.appendChild(portalNode.current);\n            forceUpdate({});\n            return ({\n                \"ShadowPortal.useEffect\": ()=>{\n                    if (portalNode.current && portalNode.current.ownerDocument) {\n                        portalNode.current.ownerDocument.body.removeChild(portalNode.current);\n                    }\n                }\n            })[\"ShadowPortal.useEffect\"];\n        }\n    }[\"ShadowPortal.useEffect\"], []);\n    return shadowNode.current ? /*#__PURE__*/ (0, _reactdom.createPortal)(children, shadowNode.current) : null;\n}\n_s(ShadowPortal, \"EFyP5ycIwJoPRLuyI1FUKGtPXWU=\");\n_c = ShadowPortal;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=shadow-portal.js.map\nvar _c;\n$RefreshReg$(_c, \"ShadowPortal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/shadow-portal.js\n"));

/***/ })

}]);