"use strict";(()=>{var e={};e.id=5641,e.ids=[5641],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},62886:(e,t,r)=>{r.r(t),r.d(t,{config:()=>p,default:()=>f,routeModule:()=>m});var s={};r.r(s),r.d(s,{default:()=>h});var a=r(93433),i=r(20264),n=r(20584),o=r(15806),d=r(94506),u=r(98580);async function l(e,t,r=0){try{let s=await fetch(`https://discord.com/api/v10/channels/${t}`,{method:"DELETE",headers:{Authorization:`Bot ${e}`}});if(s.ok)return!0;if(429===s.status){let a=await s.json(),i=1e3*(a.retry_after||1);if(await new Promise(e=>setTimeout(e,i)),r<2)return l(e,t,r+1)}return!1}catch(s){if(r<2)return await new Promise(e=>setTimeout(e,1e3)),l(e,t,r+1);return!1}}async function c(e,t){return(await Promise.all(t.map(async t=>{let r=await l(e,t);return{channelId:t,success:r}}))).reduce((e,{channelId:t,success:r})=>(r?e.succeeded.push(t):e.failed.push(t),e),{succeeded:[],failed:[]})}async function h(e,t){try{let r=await (0,o.getServerSession)(e,t,d.authOptions);if(!r?.user)return t.status(401).json({error:"Unauthorized"});if(!r.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});let{guildId:s,token:a}=u.dashboardConfig.bot;if(!a||!s)return t.status(500).json({error:"Bot configuration missing"});if("POST"===e.method)try{let{channelIds:r}=e.body;if(!Array.isArray(r)||0===r.length)return t.status(400).json({error:"Channel IDs array is required"});let i=await fetch(`https://discord.com/api/v10/guilds/${s}/channels`,{headers:{Authorization:`Bot ${a}`}});if(!i.ok)throw Error("Failed to fetch channels for validation");let n=await i.json();r.filter(e=>{let t=n.find(t=>t.id===e);return t&&2===t.type}).length;let o={succeeded:[],failed:[]};for(let e=0;e<r.length;e+=5){let t=r.slice(e,e+5),s=await c(a,t);o.succeeded.push(...s.succeeded),o.failed.push(...s.failed),e+5<r.length&&await new Promise(e=>setTimeout(e,1e3))}return t.status(200).json({message:`Successfully deleted ${o.succeeded.length} channels${o.failed.length>0?`, failed to delete ${o.failed.length} channels`:""}`,succeeded:o.succeeded,failed:o.failed})}catch(e){return t.status(500).json({error:"Failed to delete channels"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let f=(0,n.M)(s,"default"),p=(0,n.M)(s,"config"),m=new a.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/discord/channels/bulk-delete",pathname:"/api/discord/channels/bulk-delete",bundlePath:"",filename:""},userland:s})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(62886));module.exports=s})();