"use strict";(()=>{var e={};e.id=5241,e.ids=[5241],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},79646:e=>{e.exports=require("child_process")},98543:(e,t,n)=>{n.r(t),n.d(t,{config:()=>y,default:()=>h,routeModule:()=>b});var a={};n.r(a),n.d(a,{default:()=>p});var o=n(93433),r=n(20264),i=n(20584),s=n(15806),d=n(94506),c=n(29021),l=n.n(c),m=n(33873),u=n.n(m);async function p(e,t){if("POST"!==e.method)return t.status(405).json({message:"Method not allowed"});try{let a=await (0,s.getServerSession)(e,t,d.authOptions);if(!a||a.user?.id!=="933023999770918932")return t.status(403).json({message:"Unauthorized"});let{nodes:o,edges:r,name:i,description:c,author:m,version:p}=e.body,h=function(e,t){let n=[],a=e.filter(e=>"command"===e.type);a.forEach(e=>{e.data.commandName&&""!==e.data.commandName.trim()||n.push(`Command node "${e.data.label||e.id}" is missing a command name`),e.data.description&&""!==e.data.description.trim()||n.push(`Command node "${e.data.label||e.id}" is missing a description`)}),e.filter(e=>"apiRequest"===e.type).forEach(e=>{if(e.data.url&&""!==e.data.url.trim()&&"https://api.example.com"!==e.data.url||n.push(`API Request node "${e.data.label||e.id}" needs a valid URL. Please configure the API endpoint.`),e.data.url&&""!==e.data.url.trim())try{new URL(e.data.url)}catch(t){n.push(`API Request node "${e.data.label||e.id}" has an invalid URL: ${e.data.url}`)}}),e.filter(e=>"action"===e.type).forEach(e=>{e.data.actionType&&""!==e.data.actionType.trim()||n.push(`Action node "${e.data.label||e.id}" is missing an action type`),"sendMessage"!==e.data.actionType&&"sendEmbed"!==e.data.actionType||e.data.message&&""!==e.data.message.trim()||n.push(`Action node "${e.data.label||e.id}" needs a message to send`)}),0===a.length&&n.push("Your addon must have at least one command node");let o=new Set;t.forEach(e=>{o.add(e.source),o.add(e.target)});let r=e.filter(e=>"trigger"!==e.type&&!o.has(e.id));return r.length>0&&n.push(`Found ${r.length} disconnected nodes. All nodes (except triggers) must be connected to the flow.`),{isValid:0===n.length,errors:n}}(o,r);if(!h.isValid)return t.status(400).json({message:"Flow validation failed",errors:h.errors,details:"Please fix the configuration issues before building"});let y=function(e,t,n,a,o,r){var i,s,d,c,l,m,u,p,h,y,b,x;let $=[],w=[],v=e.filter(e=>"command"===e.type),j=e.filter(e=>"event"===e.type);return v.forEach(n=>{let a=n.data.commandName||`command${n.id}`,o=n.data.description||"Generated command",r=n.data.cooldown||0,i=function(e,t,n,a){let o=a.filter(e=>"apiRequest"===e.type),r=a.filter(e=>"apiRequest"!==e.type),i=[o.map(e=>g(e)).join("\n\n"),r.map(e=>{switch(e.data.actionType){case"sendMessage":return`    // Action: Send Message
    await interaction.reply(interpolateVariables('${e.data.message||"Hello!"}', context));`;case"sendEmbed":return`    // Action: Send Embed
    try {
      const response = (context as any).response;
      
      if (response && response.results && response.results.length > 0) {
        const item = response.results[0];
        
        const embed = new EmbedBuilder()
          .setTitle('🤗 ${e.data.title||"Hug!"}')
          .setDescription(\`\${interaction.user.username} sends a warm hug! 💕\`)
          .setColor('#FF69B4');
        
        // Add image if available
        if (item.url) {
          embed.setImage(item.url);
        }
        
        // Add footer with source info
        if (item.anime_name) {
          embed.setFooter({ text: \`From: \${item.anime_name}\` });
        }
        
        await interaction.reply({ embeds: [embed] });
      } else {
        // Fallback if no API data available
        const fallbackMessage = interpolateVariables('${e.data.message||"\uD83E\uDD17 *sends a virtual hug* \uD83D\uDC95"}', context);
        
        const embed = new EmbedBuilder()
          .setTitle('${e.data.title||"Message"}')
          .setDescription(fallbackMessage)
          .setColor(0x00ff00);
        
        await interaction.reply({ embeds: [embed] });
      }
    } catch (error) {
      console.error('Error creating embed:', error);
      await interaction.reply({ content: '🤗 *sends a virtual hug* 💕', ephemeral: true });
    }`;default:return`    // Action: ${e.data.actionType}
    await interaction.reply('Action: ${e.data.actionType}');`}}).join("\n\n")].filter(e=>e.trim()).join("\n\n");return`import { SlashCommandBuilder, EmbedBuilder, ChatInputCommandInteraction } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

// Helper function to interpolate variables in strings
function interpolateVariables(text: string, context: any): string {
  return text.replace(/\\{([^}]+)\\}/g, (match, variable) => {
    try {
      const value = variable.split('.').reduce((obj: any, key: string) => obj?.[key], context);
      return value !== undefined ? String(value) : match;
    } catch {
      return match;
    }
  });
}

export const data = new SlashCommandBuilder()
  .setName('${e}')
  .setDescription('${t}');

export async function execute(interaction: ChatInputCommandInteraction, bot: BotInstance) {
  try {
    // Create context with Discord data and API responses
    const context = {
      user: {
        id: interaction.user.id,
        username: interaction.user.username,
        displayName: interaction.user.displayName,
        tag: interaction.user.tag,
      },
      channel: {
        id: interaction.channel?.id,
        name: interaction.channel?.type === 0 ? interaction.channel.name : 'dm',
      },
      guild: {
        id: interaction.guild?.id,
        name: interaction.guild?.name,
      },
      // API response data will be added here by API request blocks
    };

${i||'    await interaction.reply("Command executed successfully!");'}
  } catch (error) {
    console.error('Error executing ${e} command:', error);
    await interaction.reply({ content: 'An error occurred while executing this command.', ephemeral: true });
  }
}

export const cooldown = ${1e3*n};`}(a,o,r,f(n.id,e,t));$.push({name:a,code:i})}),j.forEach(n=>{let a=n.data.eventType||"messageCreate",o=`${a}Handler`,r=function(e,t){let n=t.map(e=>"apiRequest"===e.type?g(e):"sendMessage"===e.data.actionType?`    // Send message action
    const channel = bot.client.channels.cache.get(interpolateVariables('${e.data.channel||"CHANNEL_ID"}', context));
    if (channel && channel.isTextBased()) {
      await channel.send(interpolateVariables('${e.data.message||"Hello!"}', context));
    }`:`    // Action: ${e.data.actionType}
    console.log('Executing action:', '${e.data.actionType}');`).join("\n");return`import type { BotInstance } from '../../../types/index.js';

// Helper function to interpolate variables in strings
function interpolateVariables(text: string, context: any): string {
  return text.replace(/\\{([^}]+)\\}/g, (match, variable) => {
    try {
      const value = variable.split('.').reduce((obj: any, key: string) => obj?.[key], context);
      return value !== undefined ? String(value) : match;
    } catch {
      return match;
    }
  });
}

export const eventName = '${e}';

export async function execute(bot: BotInstance, ...args: any[]) {
  try {
    // Create context with event data and API responses
    const context = {
      event: {
        type: '${e}',
        args: args,
      },
      // API response data will be added here by API request blocks
    };

${n||'    console.log("Event triggered:", eventName);'}
  } catch (error) {
    console.error('Error in event handler:', error);
  }
}`}(a,f(n.id,e,t));w.push({name:o,code:r})}),{index:(i=n,s=a,d=o,c=r,l=0,m=0,`import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import YAML from 'yaml';
import type { Addon, BotInstance } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load addon configuration
const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

// Dynamically load commands from commands folder
async function loadCommands(): Promise<any[]> {
  const commands: any[] = [];
  const commandsPath = path.join(__dirname, 'commands');
  
  if (!fs.existsSync(commandsPath)) {
    return commands;
  }

  const commandFiles = fs.readdirSync(commandsPath).filter(file => 
    file.endsWith('.js') || file.endsWith('.ts')
  );

  for (const file of commandFiles) {
    try {
      const commandPath = path.join(commandsPath, file);
      const commandUrl = pathToFileURL(commandPath).href;
      const commandModule = await import(commandUrl);
      
      if (commandModule.data && commandModule.execute) {
        commands.push({
          data: commandModule.data,
          execute: commandModule.execute,
          cooldown: commandModule.cooldown || 3000
        });
      }
    } catch (error) {
      console.error(\`Failed to load command \${file}:\`, error);
    }
  }

  return commands;
}

const addon: Addon = {
  info: {
    name: '${i}',
    version: '${c||"1.0.0"}',
    description: '${s||"Generated addon from visual builder"}',
    author: '${d||"Addon Builder"}'
  },

  commands: await loadCommands(),

  events: [],

  onLoad: async (bot: BotInstance) => {
    const logger = Logger.createAddonLogger('${i}');
    logger.info(\`Loading ${i} with \${addon.commands?.length || 0} commands...\`);
  },

  onUnload: async (_bot: BotInstance) => {
    const logger = Logger.createAddonLogger('${i}');
    logger.info(\`Unloading ${i}...\`);
  }
};

export default addon;`),config:(u=n,p=a,h=o,y=r,b=$,x=w,`name: "${u}"
version: "${y||"1.0.0"}"
description: "${p||"Generated addon from visual builder"}"
author: "${h||"Addon Builder"}"
main: "index.js"
dependencies: []
commands:
${b.map(e=>`  - ${e.name}`).join("\n")}
events:
${x.map(e=>`  - ${e.name}`).join("\n")}`),commands:$,events:w}}(o,r,i,c,m,p),b=i.toLowerCase().replace(/[^a-z0-9]/g,"-"),x=process.cwd().includes("dashboard")?u().resolve(process.cwd(),"..",".."):process.cwd(),$=u().join(x,"src","addons",b);l().existsSync($)||l().mkdirSync($,{recursive:!0});let w=u().join($,"commands");l().existsSync(w)||l().mkdirSync(w,{recursive:!0}),l().writeFileSync(u().join($,"index.ts"),y.index),l().writeFileSync(u().join($,"config.yml"),y.config);let v={name:i,description:c||"Generated addon from visual builder",author:m||a.user?.name||"Addon Builder",version:p||"1.0.0",nodes:o,edges:r,metadata:{createdAt:new Date().toISOString(),builderVersion:"1.0.0",originalFlow:!0,createdBy:a.user?.email||"unknown"}};if(l().writeFileSync(u().join($,"flow.json"),JSON.stringify(v,null,2)),y.commands.forEach(e=>{l().writeFileSync(u().join(w,`${e.name}.ts`),e.code)}),y.events.length>0){let e=u().join($,"events");l().existsSync(e)||l().mkdirSync(e,{recursive:!0}),y.events.forEach(t=>{l().writeFileSync(u().join(e,`${t.name}.ts`),t.code)})}let j=u().join(x,"src","dashboard","addon-reload.signal");l().writeFileSync(j,JSON.stringify({requestedBy:a.user?.email||"addon-builder",timestamp:Date.now(),action:"addon-built",addonName:b}));try{let{exec:e}=n(79646),t=n(28354).promisify(e),a=(process.platform,`cd "${x}" && npx tsc --project tsconfig.json --outDir dist --rootDir src`);await t(a);let o=u().join(x,"dist","addons",b);if(l().existsSync(o)){let e=u().join($,"config.yml"),t=u().join(o,"config.yml");l().existsSync(e)&&l().copyFileSync(e,t);let n=u().join($,"flow.json"),a=u().join(o,"flow.json");l().existsSync(n)&&l().copyFileSync(n,a)}}catch(e){}let A=u().join(x,"addon-reload.signal"),P={timestamp:Date.now(),requestedBy:"addon-builder",action:"addon-built",addonName:b};return l().writeFileSync(A,JSON.stringify(P,null,2)),t.status(200).json({message:"Addon built and compiled successfully",addonName:b,files:{index:y.index,config:y.config,commands:y.commands,events:y.events}})}catch(e){return t.status(500).json({message:"Internal server error"})}}function f(e,t,n){let a=new Set,o=[];return!function e(r){a.has(r)||(a.add(r),n.filter(e=>e.source===r).forEach(n=>{let a=t.find(e=>e.id===n.target);a&&(("action"===a.type||"apiRequest"===a.type)&&o.push(a),e(a.id))}))}(e),o}function g(e){let t=e.data,n=t.method||"GET",a=t.url,o=t.timeout||5e3,r=t.headers||[],i=t.body||"",s=t.bodyType||"json",d=t.saveToVariable||"response",c=t.errorHandling||"log";if(!a||""===a.trim()||"https://api.example.com"===a)return`    // API Request: ${n} (URL not configured)
    console.warn('API Request node is not properly configured. Please set a valid URL in the visual builder.');
    
    // Placeholder response for unconfigured API request
    const ${d}Data = {
      error: 'API endpoint not configured',
      message: 'Please configure the API request node in the visual builder',
      configured: false
    };
    
    // Store placeholder response in context
    (context as any).${d} = ${d}Data;`;let l=r.length>0?`      headers: {
${r.map(e=>`        '${e.key}': interpolateVariables('${e.value}', context),`).join("\n")}
      },`:"",m="";return i&&("POST"===n||"PUT"===n||"PATCH"===n)&&(m="json"===s?`      body: JSON.stringify(JSON.parse(interpolateVariables('${i}', context))),`:"form"===s?`      body: new URLSearchParams(interpolateVariables('${i}', context)),`:`      body: interpolateVariables('${i}', context),`),`    // API Request: ${n} ${a}
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), ${o});
      
      const ${d}Response = await fetch(interpolateVariables('${a}', context), {
        method: '${n}',
${l}
${m}
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!${d}Response.ok) {
        throw new Error(\`HTTP error! status: \${${d}Response.status}\`);
      }
      
      const ${d}Data = await ${d}Response.json();
      console.log('API Response:', ${d}Data);
      
      // Store response in context for use in other blocks
      (context as any).${d} = ${d}Data;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('API request timed out after ${o}ms');
      } else {
        ${"ignore"===c?"// Errors ignored":"log"===c?"console.error('API request failed:', error);":"throw error;"}
      }
    }`}let h=(0,i.M)(a,"default"),y=(0,i.M)(a,"config"),b=new o.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/admin/experimental/addon-builder/build",pathname:"/api/admin/experimental/addon-builder/build",bundlePath:"",filename:""},userland:a})}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var n=e=>t(t.s=e),a=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>n(98543));module.exports=a})();