"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-528cafbd",{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/index.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/index.js ***!
  \*******************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* global location */ // imports polyfill from `@next/polyfill-module` after build.\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    emitter: function() {\n        return emitter;\n    },\n    hydrate: function() {\n        return hydrate;\n    },\n    initialize: function() {\n        return initialize;\n    },\n    router: function() {\n        return router;\n    },\n    version: function() {\n        return version;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\n__webpack_require__(/*! ../build/polyfills/polyfill-module */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/polyfill-module.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\"));\nconst _client = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom/client */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-dom@19.1.0_react@19.1.0/node_modules/react-dom/client.js\"));\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/mitt */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/mitt.js\"));\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../shared/lib/router/utils/handle-smooth-scroll */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _isdynamic = __webpack_require__(/*! ../shared/lib/router/utils/is-dynamic */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _querystring = __webpack_require__(/*! ../shared/lib/router/utils/querystring */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/router/utils/querystring.js\");\nconst _runtimeconfigexternal = __webpack_require__(/*! ../shared/lib/runtime-config.external */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/runtime-config.external.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/utils.js\");\nconst _portal = __webpack_require__(/*! ./portal */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/portal/index.js\");\nconst _headmanager = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./head-manager */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/head-manager.js\"));\nconst _pageloader = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./page-loader */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/page-loader.js\"));\nconst _routeannouncer = __webpack_require__(/*! ./route-announcer */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/route-announcer.js\");\nconst _router = __webpack_require__(/*! ./router */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/router.js\");\nconst _iserror = __webpack_require__(/*! ../lib/is-error */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/is-error.js\");\nconst _imageconfigcontextsharedruntime = __webpack_require__(/*! ../shared/lib/image-config-context.shared-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js\");\nconst _removebasepath = __webpack_require__(/*! ./remove-base-path */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ./has-base-path */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/has-base-path.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _adapters = __webpack_require__(/*! ../shared/lib/router/adapters */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/router/adapters.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../shared/lib/hooks-client-context.shared-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _onrecoverableerror = __webpack_require__(/*! ./react-client-callbacks/on-recoverable-error */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\");\nconst _tracer = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./tracing/tracer */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/tracing/tracer.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ./components/is-next-router-error */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/is-next-router-error.js\");\nconst version = \"15.3.5\";\nlet router;\nconst emitter = (0, _mitt.default)();\nconst looseToArray = (input)=>[].slice.call(input);\nlet initialData;\nlet defaultLocale = undefined;\nlet asPath;\nlet pageLoader;\nlet appElement;\nlet headManager;\nlet initialMatchesMiddleware = false;\nlet lastAppProps;\nlet lastRenderReject;\nlet devClient;\nlet CachedApp, onPerfEntry;\nlet CachedComponent;\nclass Container extends _react.default.Component {\n    componentDidCatch(componentErr, info) {\n        this.props.fn(componentErr, info);\n    }\n    componentDidMount() {\n        this.scrollToHash();\n        // We need to replace the router state if:\n        // - the page was (auto) exported and has a query string or search (hash)\n        // - it was auto exported and is a dynamic route (to provide params)\n        // - if it is a client-side skeleton (fallback render)\n        // - if middleware matches the current page (may have rewrite params)\n        // - if rewrites in next.config.js match (may have rewrite params)\n        if (router.isSsr && (initialData.isFallback || initialData.nextExport && ((0, _isdynamic.isDynamicRoute)(router.pathname) || location.search || false || initialMatchesMiddleware) || initialData.props && initialData.props.__N_SSG && (location.search || false || initialMatchesMiddleware))) {\n            // update query on mount for exported pages\n            router.replace(router.pathname + '?' + String((0, _querystring.assign)((0, _querystring.urlQueryToSearchParams)(router.query), new URLSearchParams(location.search))), asPath, {\n                // @ts-ignore\n                // WARNING: `_h` is an internal option for handing Next.js\n                // client-side hydration. Your app should _never_ use this property.\n                // It may change at any time without notice.\n                _h: 1,\n                // Fallback pages must trigger the data fetch, so the transition is\n                // not shallow.\n                // Other pages (strictly updating query) happens shallowly, as data\n                // requirements would already be present.\n                shallow: !initialData.isFallback && !initialMatchesMiddleware\n            }).catch((err)=>{\n                if (!err.cancelled) throw err;\n            });\n        }\n    }\n    componentDidUpdate() {\n        this.scrollToHash();\n    }\n    scrollToHash() {\n        let { hash } = location;\n        hash = hash && hash.substring(1);\n        if (!hash) return;\n        const el = document.getElementById(hash);\n        if (!el) return;\n        // If we call scrollIntoView() in here without a setTimeout\n        // it won't scroll properly.\n        setTimeout(()=>el.scrollIntoView(), 0);\n    }\n    render() {\n        if (false) {} else {\n            const { PagesDevOverlay } = __webpack_require__(/*! ./components/react-dev-overlay/pages/pages-dev-overlay */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/pages/pages-dev-overlay.js\");\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(PagesDevOverlay, {\n                children: this.props.children\n            });\n        }\n    }\n}\nasync function initialize(opts) {\n    if (opts === void 0) opts = {};\n    // This makes sure this specific lines are removed in production\n    if (true) {\n        _tracer.default.onSpanEnd((__webpack_require__(/*! ./tracing/report-to-socket */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/tracing/report-to-socket.js\")[\"default\"]));\n        devClient = opts.devClient;\n    }\n    initialData = JSON.parse(document.getElementById('__NEXT_DATA__').textContent);\n    window.__NEXT_DATA__ = initialData;\n    defaultLocale = initialData.defaultLocale;\n    const prefix = initialData.assetPrefix || '';\n    self.__next_set_public_path__(\"\" + prefix + \"/_next/\") //eslint-disable-line\n    ;\n    // Initialize next/config with the environment configuration\n    (0, _runtimeconfigexternal.setConfig)({\n        serverRuntimeConfig: {},\n        publicRuntimeConfig: initialData.runtimeConfig || {}\n    });\n    asPath = (0, _utils.getURL)();\n    // make sure not to attempt stripping basePath for 404s\n    if ((0, _hasbasepath.hasBasePath)(asPath)) {\n        asPath = (0, _removebasepath.removeBasePath)(asPath);\n    }\n    if (false) {}\n    if (initialData.scriptLoader) {\n        const { initScriptLoader } = __webpack_require__(/*! ./script */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/script.js\");\n        initScriptLoader(initialData.scriptLoader);\n    }\n    pageLoader = new _pageloader.default(initialData.buildId, prefix);\n    const register = (param)=>{\n        let [r, f] = param;\n        return pageLoader.routeLoader.onEntrypoint(r, f);\n    };\n    if (window.__NEXT_P) {\n        // Defer page registration for another tick. This will increase the overall\n        // latency in hydrating the page, but reduce the total blocking time.\n        window.__NEXT_P.map((p)=>setTimeout(()=>register(p), 0));\n    }\n    window.__NEXT_P = [];\n    window.__NEXT_P.push = register;\n    headManager = (0, _headmanager.default)();\n    headManager.getIsSsr = ()=>{\n        return router.isSsr;\n    };\n    appElement = document.getElementById('__next');\n    return {\n        assetPrefix: prefix\n    };\n}\nfunction renderApp(App, appProps) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(App, {\n        ...appProps\n    });\n}\nfunction AppContainer(param) {\n    _s();\n    let { children } = param;\n    // Create a memoized value for next/navigation router context.\n    const adaptedForAppRouter = _react.default.useMemo({\n        \"AppContainer.useMemo[adaptedForAppRouter]\": ()=>{\n            return (0, _adapters.adaptForAppRouterInstance)(router);\n        }\n    }[\"AppContainer.useMemo[adaptedForAppRouter]\"], []);\n    var _self___NEXT_DATA___autoExport;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(Container, {\n        fn: (error)=>// eslint-disable-next-line @typescript-eslint/no-use-before-define\n            renderError({\n                App: CachedApp,\n                err: error\n            }).catch((err)=>console.error('Error rendering page: ', err)),\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n            value: adaptedForAppRouter,\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                value: (0, _adapters.adaptForSearchParams)(router),\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_adapters.PathnameContextProviderAdapter, {\n                    router: router,\n                    isAutoExport: (_self___NEXT_DATA___autoExport = self.__NEXT_DATA__.autoExport) != null ? _self___NEXT_DATA___autoExport : false,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                        value: (0, _adapters.adaptForPathParams)(router),\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_routercontextsharedruntime.RouterContext.Provider, {\n                            value: (0, _router.makePublicRouterInstance)(router),\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_headmanagercontextsharedruntime.HeadManagerContext.Provider, {\n                                value: headManager,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_imageconfigcontextsharedruntime.ImageConfigContext.Provider, {\n                                    value: {\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"dangerouslyAllowSVG\":false,\"unoptimized\":false,\"domains\":[],\"remotePatterns\":[]},\n                                    children: children\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        })\n    });\n}\n_s(AppContainer, \"F6BSfrFQNeqenuPnUMVY/6gI8uE=\");\n_c = AppContainer;\nconst wrapApp = (App)=>(wrappedAppProps)=>{\n        const appProps = {\n            ...wrappedAppProps,\n            Component: CachedComponent,\n            err: initialData.err,\n            router\n        };\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(AppContainer, {\n            children: renderApp(App, appProps)\n        });\n    };\n// This method handles all runtime and debug errors.\n// 404 and 500 errors are special kind of errors\n// and they are still handle via the main render method.\nfunction renderError(renderErrorProps) {\n    let { App, err } = renderErrorProps;\n    // In development runtime errors are caught by our overlay\n    // In production we catch runtime errors using componentDidCatch which will trigger renderError\n    if (true) {\n        // A Next.js rendering runtime error is always unrecoverable\n        // FIXME: let's make this recoverable (error in GIP client-transition)\n        devClient.onUnrecoverableError();\n        // We need to render an empty <App> so that the `<ReactDevOverlay>` can\n        // render itself.\n        // TODO: Fix disabled eslint rule\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        return doRender({\n            App: ()=>null,\n            props: {},\n            Component: ()=>null,\n            styleSheets: []\n        });\n    }\n    // Make sure we log the error to the console, otherwise users can't track down issues.\n    console.error(err);\n    console.error(\"A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred\");\n    return pageLoader.loadPage('/_error').then((param)=>{\n        let { page: ErrorComponent, styleSheets } = param;\n        return (lastAppProps == null ? void 0 : lastAppProps.Component) === ErrorComponent ? Promise.all(/*! import() */[__webpack_require__.e(\"lib-node_modules_pnpm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es2015_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-516eb045\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171\"), __webpack_require__.e(\"lib-node_modules_pnpm_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_h\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next-auth_4_24_11_next_15_3_2aafdeb2717e2285cf78486a4e62992f_node_modules_next-e83df605\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-65360dba\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-041c6906\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-5bb80607\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-b08bee20\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-3132e3e8\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-1dd8b864\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-c1f28c14\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-f15ba28f\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-7974549f\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-15c79965\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-b81043bf\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-34be4b23\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-72590865\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-c2581ded\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-619c5d36\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-04138191\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-8b1a74d9\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-f459d541\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-77f57188\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-7eb5f54d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-387463bd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0ac67067\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_po\"), __webpack_require__.e(\"lib-node_modules_pnpm_r\"), __webpack_require__.e(\"lib-node_modules_pnpm_s\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! ../pages/_error */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_error.js\", 23)).then((errorModule)=>{\n            return Promise.all(/*! import() */[__webpack_require__.e(\"lib-node_modules_pnpm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es2015_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-516eb045\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171\"), __webpack_require__.e(\"lib-node_modules_pnpm_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_h\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next-auth_4_24_11_next_15_3_2aafdeb2717e2285cf78486a4e62992f_node_modules_next-e83df605\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-65360dba\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-041c6906\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-5bb80607\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-b08bee20\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-3132e3e8\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-1dd8b864\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-c1f28c14\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-f15ba28f\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-7974549f\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-15c79965\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-b81043bf\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-34be4b23\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-72590865\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-c2581ded\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-619c5d36\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-04138191\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-8b1a74d9\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-f459d541\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-77f57188\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-7eb5f54d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-387463bd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0ac67067\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_po\"), __webpack_require__.e(\"lib-node_modules_pnpm_r\"), __webpack_require__.e(\"lib-node_modules_pnpm_s\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! ../pages/_app */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_app.js\", 23)).then((appModule)=>{\n                App = appModule.default;\n                renderErrorProps.App = App;\n                return errorModule;\n            });\n        }).then((m)=>({\n                ErrorComponent: m.default,\n                styleSheets: []\n            })) : {\n            ErrorComponent,\n            styleSheets\n        };\n    }).then((param)=>{\n        let { ErrorComponent, styleSheets } = param;\n        var _renderErrorProps_props;\n        // In production we do a normal render with the `ErrorComponent` as component.\n        // If we've gotten here upon initial render, we can use the props from the server.\n        // Otherwise, we need to call `getInitialProps` on `App` before mounting.\n        const AppTree = wrapApp(App);\n        const appCtx = {\n            Component: ErrorComponent,\n            AppTree,\n            router,\n            ctx: {\n                err,\n                pathname: initialData.page,\n                query: initialData.query,\n                asPath,\n                AppTree\n            }\n        };\n        return Promise.resolve(((_renderErrorProps_props = renderErrorProps.props) == null ? void 0 : _renderErrorProps_props.err) ? renderErrorProps.props : (0, _utils.loadGetInitialProps)(App, appCtx)).then((initProps)=>// eslint-disable-next-line @typescript-eslint/no-use-before-define\n            doRender({\n                ...renderErrorProps,\n                err,\n                Component: ErrorComponent,\n                styleSheets,\n                props: initProps\n            }));\n    });\n}\n// Dummy component that we render as a child of Root so that we can\n// toggle the correct styles before the page is rendered.\nfunction Head(param) {\n    _s1();\n    let { callback } = param;\n    // We use `useLayoutEffect` to guarantee the callback is executed\n    // as soon as React flushes the update.\n    _react.default.useLayoutEffect({\n        \"Head.useLayoutEffect\": ()=>callback()\n    }[\"Head.useLayoutEffect\"], [\n        callback\n    ]);\n    return null;\n}\n_s1(Head, \"n7/vCynhJvM+pLkyL2DMQUF0odM=\");\n_c1 = Head;\nconst performanceMarks = {\n    navigationStart: 'navigationStart',\n    beforeRender: 'beforeRender',\n    afterRender: 'afterRender',\n    afterHydrate: 'afterHydrate',\n    routeChange: 'routeChange'\n};\nconst performanceMeasures = {\n    hydration: 'Next.js-hydration',\n    beforeHydration: 'Next.js-before-hydration',\n    routeChangeToRender: 'Next.js-route-change-to-render',\n    render: 'Next.js-render'\n};\nlet reactRoot = null;\n// On initial render a hydrate should always happen\nlet shouldHydrate = true;\nfunction clearMarks() {\n    ;\n    [\n        performanceMarks.beforeRender,\n        performanceMarks.afterHydrate,\n        performanceMarks.afterRender,\n        performanceMarks.routeChange\n    ].forEach((mark)=>performance.clearMarks(mark));\n}\nfunction markHydrateComplete() {\n    if (!_utils.ST) return;\n    performance.mark(performanceMarks.afterHydrate) // mark end of hydration\n    ;\n    const hasBeforeRenderMark = performance.getEntriesByName(performanceMarks.beforeRender, 'mark').length;\n    if (hasBeforeRenderMark) {\n        const beforeHydrationMeasure = performance.measure(performanceMeasures.beforeHydration, performanceMarks.navigationStart, performanceMarks.beforeRender);\n        const hydrationMeasure = performance.measure(performanceMeasures.hydration, performanceMarks.beforeRender, performanceMarks.afterHydrate);\n        if ( true && // Old versions of Safari don't return `PerformanceMeasure`s from `performance.measure()`\n        beforeHydrationMeasure && hydrationMeasure) {\n            _tracer.default.startSpan('navigation-to-hydration', {\n                startTime: performance.timeOrigin + beforeHydrationMeasure.startTime,\n                attributes: {\n                    pathname: location.pathname,\n                    query: location.search\n                }\n            }).end(performance.timeOrigin + hydrationMeasure.startTime + hydrationMeasure.duration);\n        }\n    }\n    if (onPerfEntry) {\n        performance.getEntriesByName(performanceMeasures.hydration).forEach(onPerfEntry);\n    }\n    clearMarks();\n}\nfunction markRenderComplete() {\n    if (!_utils.ST) return;\n    performance.mark(performanceMarks.afterRender) // mark end of render\n    ;\n    const navStartEntries = performance.getEntriesByName(performanceMarks.routeChange, 'mark');\n    if (!navStartEntries.length) return;\n    const hasBeforeRenderMark = performance.getEntriesByName(performanceMarks.beforeRender, 'mark').length;\n    if (hasBeforeRenderMark) {\n        performance.measure(performanceMeasures.routeChangeToRender, navStartEntries[0].name, performanceMarks.beforeRender);\n        performance.measure(performanceMeasures.render, performanceMarks.beforeRender, performanceMarks.afterRender);\n        if (onPerfEntry) {\n            performance.getEntriesByName(performanceMeasures.render).forEach(onPerfEntry);\n            performance.getEntriesByName(performanceMeasures.routeChangeToRender).forEach(onPerfEntry);\n        }\n    }\n    clearMarks();\n    [\n        performanceMeasures.routeChangeToRender,\n        performanceMeasures.render\n    ].forEach((measure)=>performance.clearMeasures(measure));\n}\nfunction renderReactElement(domEl, fn) {\n    // mark start of hydrate/render\n    if (_utils.ST) {\n        performance.mark(performanceMarks.beforeRender);\n    }\n    const reactEl = fn(shouldHydrate ? markHydrateComplete : markRenderComplete);\n    if (!reactRoot) {\n        // Unlike with createRoot, you don't need a separate root.render() call here\n        reactRoot = _client.default.hydrateRoot(domEl, reactEl, {\n            onRecoverableError: _onrecoverableerror.onRecoverableError\n        });\n        // TODO: Remove shouldHydrate variable when React 18 is stable as it can depend on `reactRoot` existing\n        shouldHydrate = false;\n    } else {\n        const startTransition = _react.default.startTransition;\n        startTransition(()=>{\n            reactRoot.render(reactEl);\n        });\n    }\n}\nfunction Root(param) {\n    _s2();\n    let { callbacks, children } = param;\n    // We use `useLayoutEffect` to guarantee the callbacks are executed\n    // as soon as React flushes the update\n    _react.default.useLayoutEffect({\n        \"Root.useLayoutEffect\": ()=>callbacks.forEach({\n                \"Root.useLayoutEffect\": (callback)=>callback()\n            }[\"Root.useLayoutEffect\"])\n    }[\"Root.useLayoutEffect\"], [\n        callbacks\n    ]);\n    if (false) {}\n    return children;\n}\n_s2(Root, \"n7/vCynhJvM+pLkyL2DMQUF0odM=\");\n_c2 = Root;\nfunction doRender(input) {\n    let { App, Component, props, err } = input;\n    let styleSheets = 'initial' in input ? undefined : input.styleSheets;\n    Component = Component || lastAppProps.Component;\n    props = props || lastAppProps.props;\n    const appProps = {\n        ...props,\n        Component,\n        err,\n        router\n    };\n    // lastAppProps has to be set before ReactDom.render to account for ReactDom throwing an error.\n    lastAppProps = appProps;\n    let canceled = false;\n    let resolvePromise;\n    const renderPromise = new Promise((resolve, reject)=>{\n        if (lastRenderReject) {\n            lastRenderReject();\n        }\n        resolvePromise = ()=>{\n            lastRenderReject = null;\n            resolve();\n        };\n        lastRenderReject = ()=>{\n            canceled = true;\n            lastRenderReject = null;\n            const error = Object.defineProperty(new Error('Cancel rendering route'), \"__NEXT_ERROR_CODE\", {\n                value: \"E503\",\n                enumerable: false,\n                configurable: true\n            });\n            error.cancelled = true;\n            reject(error);\n        };\n    });\n    // This function has a return type to ensure it doesn't start returning a\n    // Promise. It should remain synchronous.\n    function onStart() {\n        if (!styleSheets || // We use `style-loader` in development, so we don't need to do anything\n        // unless we're in production:\n        \"development\" !== 'production') {\n            return false;\n        }\n        const currentStyleTags = looseToArray(document.querySelectorAll('style[data-n-href]'));\n        const currentHrefs = new Set(currentStyleTags.map((tag)=>tag.getAttribute('data-n-href')));\n        const noscript = document.querySelector('noscript[data-n-css]');\n        const nonce = noscript == null ? void 0 : noscript.getAttribute('data-n-css');\n        styleSheets.forEach((param)=>{\n            let { href, text } = param;\n            if (!currentHrefs.has(href)) {\n                const styleTag = document.createElement('style');\n                styleTag.setAttribute('data-n-href', href);\n                styleTag.setAttribute('media', 'x');\n                if (nonce) {\n                    styleTag.setAttribute('nonce', nonce);\n                }\n                document.head.appendChild(styleTag);\n                styleTag.appendChild(document.createTextNode(text));\n            }\n        });\n        return true;\n    }\n    function onHeadCommit() {\n        if (false) {}\n        if (input.scroll) {\n            const { x, y } = input.scroll;\n            (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                window.scrollTo(x, y);\n            });\n        }\n    }\n    function onRootCommit() {\n        resolvePromise();\n    }\n    onStart();\n    const elem = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n                callback: onHeadCommit\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(AppContainer, {\n                children: [\n                    renderApp(App, appProps),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_portal.Portal, {\n                        type: \"next-route-announcer\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_routeannouncer.RouteAnnouncer, {})\n                    })\n                ]\n            })\n        ]\n    });\n    // We catch runtime errors using componentDidCatch which will trigger renderError\n    renderReactElement(appElement, (callback)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(Root, {\n            callbacks: [\n                callback,\n                onRootCommit\n            ],\n            children:  true ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.default.StrictMode, {\n                children: elem\n            }) : 0\n        }));\n    return renderPromise;\n}\nasync function render(renderingProps) {\n    // if an error occurs in a server-side page (e.g. in getInitialProps),\n    // skip re-rendering the error page client-side as data-fetching operations\n    // will already have been done on the server and NEXT_DATA contains the correct\n    // data for straight-forward hydration of the error page\n    if (renderingProps.err && // renderingProps.Component might be undefined if there is a top/module-level error\n    (typeof renderingProps.Component === 'undefined' || !renderingProps.isHydratePass)) {\n        await renderError(renderingProps);\n        return;\n    }\n    try {\n        await doRender(renderingProps);\n    } catch (err) {\n        const renderErr = (0, _iserror.getProperError)(err);\n        // bubble up cancelation errors\n        if (renderErr.cancelled) {\n            throw renderErr;\n        }\n        if (true) {\n            // Ensure this error is displayed in the overlay in development\n            setTimeout(()=>{\n                throw renderErr;\n            });\n        }\n        await renderError({\n            ...renderingProps,\n            err: renderErr\n        });\n    }\n}\nasync function hydrate(opts) {\n    let initialErr = initialData.err;\n    try {\n        const appEntrypoint = await pageLoader.routeLoader.whenEntrypoint('/_app');\n        if ('error' in appEntrypoint) {\n            throw appEntrypoint.error;\n        }\n        const { component: app, exports: mod } = appEntrypoint;\n        CachedApp = app;\n        if (mod && mod.reportWebVitals) {\n            onPerfEntry = (param)=>{\n                let { id, name, startTime, value, duration, entryType, entries, attribution } = param;\n                // Combines timestamp with random number for unique ID\n                const uniqueID = Date.now() + \"-\" + (Math.floor(Math.random() * (9e12 - 1)) + 1e12);\n                let perfStartEntry;\n                if (entries && entries.length) {\n                    perfStartEntry = entries[0].startTime;\n                }\n                const webVitals = {\n                    id: id || uniqueID,\n                    name,\n                    startTime: startTime || perfStartEntry,\n                    value: value == null ? duration : value,\n                    label: entryType === 'mark' || entryType === 'measure' ? 'custom' : 'web-vital'\n                };\n                if (attribution) {\n                    webVitals.attribution = attribution;\n                }\n                mod.reportWebVitals(webVitals);\n            };\n        }\n        const pageEntrypoint = // error, so we need to skip waiting for the entrypoint.\n         true && initialData.err ? {\n            error: initialData.err\n        } : await pageLoader.routeLoader.whenEntrypoint(initialData.page);\n        if ('error' in pageEntrypoint) {\n            throw pageEntrypoint.error;\n        }\n        CachedComponent = pageEntrypoint.component;\n        if (true) {\n            const { isValidElementType } = __webpack_require__(/*! next/dist/compiled/react-is */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react-is/index.js\");\n            if (!isValidElementType(CachedComponent)) {\n                throw Object.defineProperty(new Error('The default export is not a React Component in page: \"' + initialData.page + '\"'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E286\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    } catch (error) {\n        // This catches errors like throwing in the top level of a module\n        initialErr = (0, _iserror.getProperError)(error);\n    }\n    if (true) {\n        const getServerError = (__webpack_require__(/*! ./components/react-dev-overlay/pages/client */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/pages/client.js\").getServerError);\n        // Server-side runtime errors need to be re-thrown on the client-side so\n        // that the overlay is rendered.\n        if (initialErr) {\n            if (initialErr === initialData.err) {\n                setTimeout(()=>{\n                    let error;\n                    try {\n                        // Generate a new error object. We `throw` it because some browsers\n                        // will set the `stack` when thrown, and we want to ensure ours is\n                        // not overridden when we re-throw it below.\n                        throw Object.defineProperty(new Error(initialErr.message), \"__NEXT_ERROR_CODE\", {\n                            value: \"E394\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    } catch (e) {\n                        error = e;\n                    }\n                    error.name = initialErr.name;\n                    error.stack = initialErr.stack;\n                    const errSource = initialErr.source;\n                    // In development, error the navigation API usage in runtime,\n                    // since it's not allowed to be used in pages router as it doesn't contain error boundary like app router.\n                    if ((0, _isnextroutererror.isNextRouterError)(initialErr)) {\n                        error.message = 'Next.js navigation API is not allowed to be used in Pages Router.';\n                    }\n                    throw getServerError(error, errSource);\n                });\n            } else {\n                setTimeout(()=>{\n                    throw initialErr;\n                });\n            }\n        }\n    }\n    if (window.__NEXT_PRELOADREADY) {\n        await window.__NEXT_PRELOADREADY(initialData.dynamicIds);\n    }\n    router = (0, _router.createRouter)(initialData.page, initialData.query, asPath, {\n        initialProps: initialData.props,\n        pageLoader,\n        App: CachedApp,\n        Component: CachedComponent,\n        wrapApp,\n        err: initialErr,\n        isFallback: Boolean(initialData.isFallback),\n        subscription: (info, App, scroll)=>render(Object.assign({}, info, {\n                App,\n                scroll\n            })),\n        locale: initialData.locale,\n        locales: initialData.locales,\n        defaultLocale,\n        domainLocales: initialData.domainLocales,\n        isPreview: initialData.isPreview\n    });\n    initialMatchesMiddleware = await router._initialMatchesMiddlewarePromise;\n    const renderCtx = {\n        App: CachedApp,\n        initial: true,\n        Component: CachedComponent,\n        props: initialData.props,\n        err: initialErr,\n        isHydratePass: true\n    };\n    if (opts == null ? void 0 : opts.beforeRender) {\n        await opts.beforeRender();\n    }\n    render(renderCtx);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Root\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/index.js\n"));

/***/ })

});