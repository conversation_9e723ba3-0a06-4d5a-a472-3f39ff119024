"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_pages-dir-browser_components_CreateChannelDialog_tsx",{

/***/ "(pages-dir-browser)/./components/CreateChannelDialog.tsx":
/*!********************************************!*\
  !*** ./components/CreateChannelDialog.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateChannelDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button,FormControl,FormHelperText,FormLabel,Input,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,NumberDecrementStepper,NumberIncrementStepper,NumberInput,NumberInputField,NumberInputStepper,Select,Stack,Switch,useToast!=!@chakra-ui/react */ \"(pages-dir-browser)/__barrel_optimize__?names=Button,FormControl,FormHelperText,FormLabel,Input,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,NumberDecrementStepper,NumberIncrementStepper,NumberInput,NumberInputField,NumberInputStepper,Select,Stack,Switch,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\n\n// Update the channel type mapping\nconst CHANNEL_TYPES = {\n    GUILD_TEXT: 0,\n    GUILD_VOICE: 2,\n    GUILD_CATEGORY: 4\n};\nfunction CreateChannelDialog(param) {\n    let { isOpen, onClose, onSuccess } = param;\n    _s();\n    const toast = (0,_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [channelData, setChannelData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: 'GUILD_TEXT',\n        parent: '',\n        topic: '',\n        nsfw: false,\n        rateLimitPerUser: 0,\n        position: 0,\n        bitrate: 64000,\n        userLimit: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateChannelDialog.useEffect\": ()=>{\n            // Fetch categories when the modal opens\n            if (isOpen) {\n                fetchCategories();\n            }\n        }\n    }[\"CreateChannelDialog.useEffect\"], [\n        isOpen\n    ]);\n    const fetchCategories = async ()=>{\n        try {\n            const response = await fetch('/api/discord/channels');\n            if (!response.ok) {\n                throw new Error('Failed to fetch channels');\n            }\n            const channels = await response.json();\n            // Filter out categories (type 4 or 'category')\n            const categoryChannels = channels.filter((channel)=>{\n                // Detect categories regardless of format returned by API\n                if (typeof channel.raw_type === 'number') {\n                    return channel.raw_type === CHANNEL_TYPES.GUILD_CATEGORY;\n                }\n                // Fallback to string comparison\n                return channel.type === 'GUILD_CATEGORY' || channel.type === 'category';\n            });\n            setCategories(categoryChannels);\n        } catch (error) {\n            console.error('Failed to fetch categories:', error);\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch categories',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setChannelData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSubmit = async ()=>{\n        try {\n            setIsLoading(true);\n            // Validate channel name\n            if (!channelData.name.trim()) {\n                toast({\n                    title: 'Error',\n                    description: 'Channel name is required',\n                    status: 'error',\n                    duration: 3000\n                });\n                return;\n            }\n            // Format channel name (lowercase, no spaces)\n            const formattedName = channelData.name.toLowerCase().replace(/\\s+/g, '-');\n            // Convert channel type to numeric value\n            const numericType = CHANNEL_TYPES[channelData.type];\n            console.log('Sending channel data:', {\n                ...channelData,\n                name: formattedName,\n                type: numericType\n            });\n            const response = await fetch('/api/discord/channels', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...channelData,\n                    name: formattedName,\n                    type: numericType\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Failed to create channel. Response:', errorText);\n                let errorMessage = 'Failed to create channel';\n                try {\n                    const errorJson = JSON.parse(errorText);\n                    errorMessage = errorJson.message || errorJson.error || errorMessage;\n                } catch (e) {\n                    // If response isn't JSON, use the raw text\n                    errorMessage = errorText;\n                }\n                throw new Error(errorMessage);\n            }\n            toast({\n                title: 'Success',\n                description: 'Channel created successfully',\n                status: 'success',\n                duration: 3000\n            });\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n            onClose();\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to create channel',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset form when modal closes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateChannelDialog.useEffect\": ()=>{\n            if (!isOpen) {\n                setChannelData({\n                    name: '',\n                    type: 'GUILD_TEXT',\n                    parent: '',\n                    topic: '',\n                    nsfw: false,\n                    rateLimitPerUser: 0,\n                    position: 0,\n                    bitrate: 64000,\n                    userLimit: 0\n                });\n            }\n        }\n    }[\"CreateChannelDialog.useEffect\"], [\n        isOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalOverlay, {\n                backdropFilter: \"blur(10px)\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalContent, {\n                bg: \"gray.800\",\n                border: \"1px\",\n                borderColor: \"blue.500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalHeader, {\n                        children: \"Create Channel\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalCloseButton, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalBody, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                            spacing: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormControl, {\n                                    isRequired: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormLabel, {\n                                            children: \"Channel Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            placeholder: \"Enter channel name\",\n                                            value: channelData.name,\n                                            onChange: (e)=>handleInputChange('name', e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormHelperText, {\n                                            children: \"Channel name will be automatically formatted (lowercase, hyphens instead of spaces)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormLabel, {\n                                            children: \"Channel Type\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                            value: channelData.type,\n                                            onChange: (e)=>handleInputChange('type', e.target.value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"GUILD_TEXT\",\n                                                    children: \"Text Channel\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"GUILD_VOICE\",\n                                                    children: \"Voice Channel\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"GUILD_CATEGORY\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                channelData.type !== 'GUILD_CATEGORY' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormLabel, {\n                                            children: \"Parent Category\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                            placeholder: \"Select category\",\n                                            value: channelData.parent,\n                                            onChange: (e)=>handleInputChange('parent', e.target.value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"None\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this),\n                                                (categories || []).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category.id,\n                                                        children: category.name\n                                                    }, category.id, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this),\n                                channelData.type === 'GUILD_TEXT' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormControl, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormLabel, {\n                                                    children: \"Channel Topic\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                    placeholder: \"Enter channel topic\",\n                                                    value: channelData.topic,\n                                                    onChange: (e)=>handleInputChange('topic', e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormControl, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormLabel, {\n                                                    children: \"Slowmode (seconds)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInput, {\n                                                    min: 0,\n                                                    max: 21600,\n                                                    value: channelData.rateLimitPerUser,\n                                                    onChange: (value)=>handleInputChange('rateLimitPerUser', parseInt(value)),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInputField, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInputStepper, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberIncrementStepper, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberDecrementStepper, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormHelperText, {\n                                                    children: \"Set how long users must wait between sending messages (0 to disable)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormControl, {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormLabel, {\n                                                    mb: \"0\",\n                                                    children: \"Age-Restricted (NSFW)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Switch, {\n                                                    isChecked: channelData.nsfw,\n                                                    onChange: (e)=>handleInputChange('nsfw', e.target.checked)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                channelData.type === 'GUILD_VOICE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormControl, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormLabel, {\n                                                    children: \"Bitrate (kbps)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInput, {\n                                                    min: 8,\n                                                    max: 96,\n                                                    value: channelData.bitrate / 1000,\n                                                    onChange: (value)=>handleInputChange('bitrate', parseInt(value) * 1000),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInputField, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInputStepper, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberIncrementStepper, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberDecrementStepper, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormControl, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormLabel, {\n                                                    children: \"User Limit\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInput, {\n                                                    min: 0,\n                                                    max: 99,\n                                                    value: channelData.userLimit,\n                                                    onChange: (value)=>handleInputChange('userLimit', parseInt(value)),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInputField, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInputStepper, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberIncrementStepper, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberDecrementStepper, {}, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormHelperText, {\n                                                    children: \"Set to 0 for unlimited users\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormLabel, {\n                                            children: \"Position\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInput, {\n                                            min: 0,\n                                            value: channelData.position,\n                                            onChange: (value)=>handleInputChange('position', parseInt(value)),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInputField, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberInputStepper, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberIncrementStepper, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.NumberDecrementStepper, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.FormHelperText, {\n                                            children: \"Channel position in the list (0 = top)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalFooter, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                mr: 3,\n                                onClick: onClose,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                colorScheme: \"blue\",\n                                onClick: handleSubmit,\n                                isLoading: isLoading,\n                                loadingText: \"Creating...\",\n                                children: \"Create Channel\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateChannelDialog.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateChannelDialog, \"b6+y6hDGrOq/xgcKbR2G052fFjE=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_FormControl_FormHelperText_FormLabel_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_Stack_Switch_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = CreateChannelDialog;\nvar _c;\n$RefreshReg$(_c, \"CreateChannelDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/CreateChannelDialog.tsx\n"));

/***/ })

});