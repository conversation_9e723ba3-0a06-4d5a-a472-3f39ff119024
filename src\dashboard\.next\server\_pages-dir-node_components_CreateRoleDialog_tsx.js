"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_CreateRoleDialog_tsx";
exports.ids = ["_pages-dir-node_components_CreateRoleDialog_tsx"];
exports.modules = {

/***/ "(pages-dir-node)/./components/CreateRoleDialog.tsx":
/*!*****************************************!*\
  !*** ./components/CreateRoleDialog.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateRoleDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Checkbox,Divider,FormControl,FormLabel,HStack,Icon,Input,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,SimpleGrid,Text,VStack,useToast!=!@chakra-ui/react */ \"(pages-dir-node)/__barrel_optimize__?names=Box,Button,Checkbox,Divider,FormControl,FormLabel,HStack,Icon,Input,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,SimpleGrid,Text,VStack,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__]);\n_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// @ts-nocheck\n\n\n\n\nconst PERMISSION_GROUPS = {\n    General: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiShield,\n        permissions: [\n            'ADMINISTRATOR',\n            'VIEW_AUDIT_LOG',\n            'MANAGE_GUILD',\n            'MANAGE_ROLES',\n            'MANAGE_CHANNELS',\n            'MANAGE_EMOJIS_AND_STICKERS',\n            'MANAGE_WEBHOOKS',\n            'VIEW_CHANNEL'\n        ]\n    },\n    Text: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiMessageSquare,\n        permissions: [\n            'SEND_MESSAGES',\n            'EMBED_LINKS',\n            'ATTACH_FILES',\n            'ADD_REACTIONS',\n            'USE_EXTERNAL_EMOJIS',\n            'MENTION_EVERYONE',\n            'MANAGE_MESSAGES',\n            'READ_MESSAGE_HISTORY'\n        ]\n    },\n    Voice: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiVolume2,\n        permissions: [\n            'CONNECT',\n            'SPEAK',\n            'STREAM',\n            'USE_VAD',\n            'PRIORITY_SPEAKER',\n            'MUTE_MEMBERS',\n            'DEAFEN_MEMBERS',\n            'MOVE_MEMBERS'\n        ]\n    },\n    Members: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiUsers,\n        permissions: [\n            'KICK_MEMBERS',\n            'BAN_MEMBERS',\n            'CHANGE_NICKNAME',\n            'MANAGE_NICKNAMES',\n            'CREATE_INSTANT_INVITE'\n        ]\n    }\n};\nfunction CreateRoleDialog({ isOpen, onClose, onSuccess }) {\n    const toast = (0,_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        color: '#99AAB5',\n        permissions: [],\n        hoist: false,\n        mentionable: false\n    });\n    const handleSubmit = async ()=>{\n        if (!formData.name.trim()) {\n            toast({\n                title: 'Error',\n                description: 'Role name is required',\n                status: 'error',\n                duration: 3000\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/discord/roles', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.message || 'Failed to create role');\n            }\n            toast({\n                title: 'Success',\n                description: 'Role created successfully',\n                status: 'success',\n                duration: 3000\n            });\n            // Reset form\n            setFormData({\n                name: '',\n                color: '#99AAB5',\n                permissions: [],\n                hoist: false,\n                mentionable: false\n            });\n            onSuccess();\n            onClose();\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to create role',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePermissionChange = (permission)=>{\n        setFormData((prev)=>({\n                ...prev,\n                permissions: prev.permissions.includes(permission) ? prev.permissions.filter((p)=>p !== permission) : [\n                    ...prev.permissions,\n                    permission\n                ]\n            }));\n    };\n    const handleChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"xl\",\n        scrollBehavior: \"inside\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalOverlay, {\n                backdropFilter: \"blur(10px)\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalContent, {\n                bg: \"gray.800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalHeader, {\n                        children: \"Create New Role\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalCloseButton, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalBody, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                            spacing: 6,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                    isRequired: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                            children: \"Role Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            placeholder: \"Enter role name\",\n                                            value: formData.name,\n                                            onChange: (e)=>handleChange('name', e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                            children: \"Role Color\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            type: \"color\",\n                                            value: formData.color,\n                                            onChange: (e)=>handleChange('color', e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                        spacing: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                isChecked: formData.hoist,\n                                                onChange: (e)=>handleChange('hoist', e.target.checked),\n                                                children: \"Display role separately\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                isChecked: formData.mentionable,\n                                                onChange: (e)=>handleChange('mentionable', e.target.checked),\n                                                children: \"Allow anyone to @mention\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Divider, {}, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    fontSize: \"lg\",\n                                    fontWeight: \"bold\",\n                                    alignSelf: \"flex-start\",\n                                    children: \"Permissions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                Object.entries(PERMISSION_GROUPS).map(([groupName, group])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                        w: \"full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                mb: 2,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                                        as: group.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                        fontWeight: \"semibold\",\n                                                        children: groupName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.SimpleGrid, {\n                                                columns: 2,\n                                                spacing: 2,\n                                                children: group.permissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                        isChecked: formData.permissions.includes(permission),\n                                                        onChange: ()=>handlePermissionChange(permission),\n                                                        children: permission.split('_').map((word)=>word.charAt(0) + word.slice(1).toLowerCase()).join(' ')\n                                                    }, permission, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, groupName, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalFooter, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                mr: 3,\n                                onClick: onClose,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                colorScheme: \"blue\",\n                                onClick: handleSubmit,\n                                isLoading: isLoading,\n                                children: \"Create Role\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/CreateRoleDialog.tsx\n");

/***/ })

};
;