"use strict";(()=>{var e={};e.id=2529,e.ids=[2529],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},55728:(e,r,s)=>{s.r(r),s.d(r,{config:()=>h,default:()=>f,routeModule:()=>x});var t={};s.r(t),s.d(t,{default:()=>p});var a=s(93433),n=s(20264),o=s(20584),i=s(15806),d=s(94506),u=s(29021),m=s.n(u),c=s(33873),l=s.n(c);async function p(e,r){if("POST"!==e.method)return r.status(405).json({error:"Method not allowed"});try{let s=await (0,i.getServerSession)(e,r,d.authOptions);if(!s)return r.status(401).json({error:"Unauthorized"});if(!s.user.isAdmin)return r.status(403).json({error:"Admin access required"});let t=process.cwd().includes("dashboard")?l().resolve(process.cwd(),"..",".."):process.cwd(),a=l().join(t,"addon-reload.signal");m().writeFileSync(a,JSON.stringify({requestedBy:s.user?.email||"dashboard",timestamp:Date.now(),action:"commands-refresh",source:"manual-refresh"})),r.status(200).json({message:"Command refresh triggered successfully",timestamp:Date.now()})}catch(e){r.status(500).json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"})}}let f=(0,o.M)(t,"default"),h=(0,o.M)(t,"config"),x=new a.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/admin/commands/refresh",pathname:"/api/admin/commands/refresh",bundlePath:"",filename:""},userland:t})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>s(55728));module.exports=t})();