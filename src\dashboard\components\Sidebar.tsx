import { Box, VStack, Link, Icon, useColorModeValue, Text, Tooltip, Divider, <PERSON>lapse, Button, useDisclosure } from '@chakra-ui/react';
import { FiServer, FiSettings, FiUsers, FiPackage, FiHelpCircle, FiMonitor, FiHome, FiDatabase, FiActivity, FiBox, FiCommand, FiChevronDown, FiAlertCircle } from 'react-icons/fi';
import { FaFileAlt, FaFlask } from 'react-icons/fa';
import NextLink from 'next/link';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useState } from 'react';
import useGuildInfo from '../hooks/useGuildInfo';
import useExperimentalFeatures from '../hooks/useExperimentalFeatures';
import ExperimentalApplicationForm from './ExperimentalApplicationForm';
import { useTheme } from '../contexts/ThemeContext';

// Import package.json version
const BOT_VERSION = '1.0.0'; // You can update this manually or import from package.json

const DEVELOPER_ID = '933023999770918932';

export default function Sidebar() {
  const { data: session } = useSession();
  const router = useRouter();
  const isAdmin = (session?.user as any)?.isAdmin;
  const userId = (session?.user as any)?.id;
  const [isAdminExpanded, setIsAdminExpanded] = useState(false);
  const [isExperimentalExpanded, setIsExperimentalExpanded] = useState(false);
  const { displayName } = useGuildInfo();
  const { hasAccess, isLoading: isExperimentalLoading, isDeveloper, reason } = useExperimentalFeatures();
  const { isOpen: isApplicationFormOpen, onOpen: onApplicationFormOpen, onClose: onApplicationFormClose } = useDisclosure();
  const { currentScheme } = useTheme();

  const menuItems = [
    { name: 'Overview', icon: FiHome, href: '/overview' },
    { name: 'Applications', icon: FiPackage, href: '/applications' },
    { name: 'Tickets', icon: FiHelpCircle, href: '/tickets' },
    { name: 'Game Servers', icon: FiMonitor, href: '/gameservers' },
  ];

  // Admin functionality is now handled through the expandable admin section below

  const adminQuickLinks = [
    { name: 'Server Management', href: '/admin/guilds', icon: FiSettings },
    { name: 'Applications', href: '/admin/applications', icon: FaFileAlt },
    { name: 'Applications Builder', href: '/admin/applications-builder', icon: FiPackage },
    { name: 'Addons', href: '/admin/addons', icon: FiBox },
    { name: 'Commands', href: '/admin/commands', icon: FiCommand },
  ];

  // All experimental features are now consolidated into the main Applications page

  // Experimental admin links for developers
  const experimentalAdminLinks = [
    { name: 'Addon Builder', href: '/admin/experimental/addon-builder', icon: FiBox },
    { name: 'Feature Flags', href: '/admin/experimental/feature-flags', icon: FaFlask },
    { name: 'Beta Testing', href: '/admin/experimental/beta-testing', icon: FaFlask },
  ];

  const isActive = (href: string) => {
    if (href === '/overview') {
      return router.pathname === href;
    }
    return router.pathname.startsWith(href);
  };

  return (
    <Box
      as="nav"
      h="100%"
      bg={currentScheme.colors.surface}
      backdropFilter="blur(20px)"
      borderRight="1px solid"
      borderColor={currentScheme.colors.border}
      py={8}
      display="flex"
      flexDirection="column"
      _before={{
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bg: `linear-gradient(180deg, ${currentScheme.colors.primary}15 0%, ${currentScheme.colors.accent}15 100%)`,
        zIndex: -1,
      }}
    >
      <VStack spacing={2} align="stretch" flex="1">
        {menuItems.map((item) => {
          const active = isActive(item.href);
          
          return (
            <Tooltip
              key={item.name}
              label={item.name}
              placement="right"
              hasArrow
              gutter={20}
              openDelay={500}
              display={{ base: 'block', '2xl': 'none' }}
            >
              <Link
                as={NextLink}
                href={item.href}
                display="flex"
                alignItems="center"
                px={4}
                py={3}
                fontSize="sm"
                fontWeight="medium"
                color={active ? currentScheme.colors.text : currentScheme.colors.textSecondary}
                bg={active ? `${currentScheme.colors.primary}30` : 'transparent'}
                _hover={{
                  bg: active ? `${currentScheme.colors.primary}40` : currentScheme.colors.surface,
                  color: currentScheme.colors.text,
                  transform: 'translateX(4px)',
                }}
                _active={{
                  bg: `${currentScheme.colors.primary}50`,
                }}
                borderRight={active ? '2px solid' : 'none'}
                borderColor={active ? currentScheme.colors.primary : 'transparent'}
                transition="all 0.2s"
                role="group"
              >
                <Icon
                  as={item.icon}
                  w={5}
                  h={5}
                  mr={3}
                  transition="all 0.2s"
                  _groupHover={{
                    transform: 'scale(1.1)',
                  }}
                />
                <Text
                  display={{ base: 'none', lg: 'block' }}
                  bgGradient={active ? `linear(to-r, ${currentScheme.colors.primaryLight}, ${currentScheme.colors.accent})` : 'none'}
                  bgClip={active ? 'text' : 'none'}
                  transition="all 0.2s"
                >
                  {item.name}
                </Text>
              </Link>
            </Tooltip>
          );
        })}
        
        {/* Admin Expandable Section */}
        {isAdmin && (
          <VStack spacing={0} align="stretch">
            <Box
              display="flex"
              alignItems="center"
              px={4}
              py={3}
              fontSize="sm"
              fontWeight="medium"
              color={currentScheme.colors.textSecondary}
              bg="transparent"
              _hover={{
                bg: currentScheme.colors.surface,
                color: currentScheme.colors.text,
                transform: 'translateX(4px)',
              }}
              transition="all 0.2s"
              cursor="pointer"
              role="group"
              onClick={() => setIsAdminExpanded(!isAdminExpanded)}
            >
              <Icon
                as={FiServer}
                w={5}
                h={5}
                mr={3}
                transition="all 0.2s"
                _groupHover={{
                  transform: 'scale(1.1)',
                }}
              />
              <Text
                display={{ base: 'none', lg: 'block' }}
                transition="all 0.2s"
                flex="1"
              >
                Admin
              </Text>
              <Icon
                as={FiChevronDown}
                w={4}
                h={4}
                ml={2}
                transition="all 0.2s"
                transform={isAdminExpanded ? 'rotate(180deg)' : 'rotate(0deg)'}
                opacity={0.6}
              />
            </Box>
            
            <Collapse in={isAdminExpanded} animateOpacity>
              <VStack spacing={1} align="stretch" pl={4} py={2}>
                {adminQuickLinks.map((link) => (
                  <Link
                    key={link.href}
                    as={NextLink}
                    href={link.href}
                    display="flex"
                    alignItems="center"
                    px={4}
                    py={2}
                    fontSize="xs"
                    fontWeight="medium"
                    color={isActive(link.href) ? currentScheme.colors.text : currentScheme.colors.textSecondary}
                    bg={isActive(link.href) ? `${currentScheme.colors.primary}20` : 'transparent'}
                    _hover={{
                      bg: currentScheme.colors.surface,
                      color: currentScheme.colors.text,
                      transform: 'translateX(2px)',
                    }}
                    borderRadius="md"
                    transition="all 0.2s"
                    role="group"
                  >
                    <Icon
                      as={link.icon}
                      w={4}
                      h={4}
                      mr={2}
                      transition="all 0.2s"
                      _groupHover={{
                        transform: 'scale(1.1)',
                      }}
                    />
                    <Text display={{ base: 'none', lg: 'block' }}>
                      {link.name}
                    </Text>
                  </Link>
                ))}
              </VStack>
            </Collapse>
          </VStack>
        )}

        {/* Experimental Admin Section (Developer Only) */}
        {isDeveloper && (
          <VStack spacing={0} align="stretch">
            <Box
              display="flex"
              alignItems="center"
              px={4}
              py={3}
              fontSize="sm"
              fontWeight="medium"
              color={currentScheme.colors.textSecondary}
              bg="transparent"
              _hover={{
                bg: currentScheme.colors.surface,
                color: currentScheme.colors.text,
                transform: 'translateX(4px)',
              }}
              transition="all 0.2s"
              cursor="pointer"
              role="group"
              onClick={() => setIsExperimentalExpanded(!isExperimentalExpanded)}
            >
              <Icon
                as={FaFlask}
                w={5}
                h={5}
                mr={3}
                transition="all 0.2s"
                _groupHover={{
                  transform: 'scale(1.1)',
                }}
              />
              <Text
                display={{ base: 'none', lg: 'block' }}
                transition="all 0.2s"
                flex="1"
                bgGradient="linear(to-r, purple.400, pink.400)"
                bgClip="text"
              >
                Manage Experimental
              </Text>
              <Icon
                as={FiChevronDown}
                w={4}
                h={4}
                ml={2}
                transition="all 0.2s"
                transform={isExperimentalExpanded ? 'rotate(180deg)' : 'rotate(0deg)'}
                opacity={0.6}
              />
            </Box>
            
            <Collapse in={isExperimentalExpanded} animateOpacity>
              <VStack spacing={1} align="stretch" pl={4} py={2}>
                {experimentalAdminLinks.map((link) => (
                  <Link
                    key={link.href}
                    as={NextLink}
                    href={link.href}
                    display="flex"
                    alignItems="center"
                    px={4}
                    py={2}
                    fontSize="xs"
                    fontWeight="medium"
                    color={isActive(link.href) ? currentScheme.colors.text : currentScheme.colors.textSecondary}
                    bg={isActive(link.href) ? `${currentScheme.colors.primary}20` : 'transparent'}
                    _hover={{
                      bg: currentScheme.colors.surface,
                      color: currentScheme.colors.text,
                      transform: 'translateX(2px)',
                    }}
                    borderRadius="md"
                    transition="all 0.2s"
                    role="group"
                  >
                    <Icon
                      as={link.icon}
                      w={4}
                      h={4}
                      mr={2}
                      transition="all 0.2s"
                      _groupHover={{
                        transform: 'scale(1.1)',
                      }}
                    />
                    <Text display={{ base: 'none', lg: 'block' }}>
                      {link.name}
                    </Text>
                  </Link>
                ))}
              </VStack>
            </Collapse>
          </VStack>
        )}
      </VStack>

      {/* Experimental Features Status (Non-Developer Users) */}
      {!isExperimentalLoading && !isDeveloper && (hasAccess || reason === 'open') && (
        <Box px={4} mt="auto">
          <Divider borderColor={currentScheme.colors.border} mb={4} />
          {hasAccess ? (
            <Button
              size="sm"
              colorScheme="yellow"
              variant="ghost"
              leftIcon={<Icon as={FaFlask} />}
              onClick={() => router.push('/experimental')}
              w="full"
              justifyContent="flex-start"
              fontSize="xs"
              _hover={{
                bg: currentScheme.colors.surface,
                transform: 'translateX(2px)',
              }}
            >
              <Text display={{ base: 'none', lg: 'block' }}>
                Experimental Features
              </Text>
            </Button>
          ) : reason === 'open' ? (
            <VStack spacing={2} align="stretch">
              <Box
                bg="rgba(236, 201, 75, 0.1)"
                border="1px solid"
                borderColor="yellow.400"
                borderRadius="md"
                p={2}
                textAlign="center"
              >
                <Text
                  fontSize="xs"
                  color="yellow.300"
                  fontWeight="bold"
                  bgGradient="linear(to-r, yellow.200, orange.200)"
                  bgClip="text"
                >
                  🧪 Experimental Features
                </Text>
                                 <Text fontSize="xs" color="yellow.400" mt={1}>
                   Apply Now • Response in ~1 week
                 </Text>
              </Box>
              <Button
                size="sm"
                colorScheme="yellow"
                variant="outline"
                onClick={onApplicationFormOpen}
                w="full"
                fontSize="xs"
                _hover={{
                  bg: 'yellow.400',
                  color: 'black',
                  transform: 'translateY(-1px)',
                }}
              >
                <Text display={{ base: 'none', lg: 'block' }}>
                  Apply Now
                </Text>
                <Text display={{ base: 'block', lg: 'none' }}>
                  Apply
                </Text>
              </Button>
            </VStack>
          ) : null}
        </Box>
      )}

      {/* Version display at bottom */}
      <Box px={4} pt={4} {...(!isExperimentalLoading && !isDeveloper && (hasAccess || reason === 'open') ? {} : { mt: "auto" })}>
        <Divider borderColor={currentScheme.colors.border} mb={4} />
        <Text
          fontSize="xs"
          color={currentScheme.colors.textSecondary}
          textAlign="center"
          bgGradient={`linear(to-r, ${currentScheme.colors.primaryLight}, ${currentScheme.colors.accent})`}
          bgClip="text"
          opacity={0.7}
          _hover={{
            opacity: 1,
            transform: "scale(1.05)"
          }}
          transition="all 0.2s"
        >
          {displayName ? `${displayName} v${BOT_VERSION}` : `Bot v${BOT_VERSION}`}
        </Text>
      </Box>

      {/* Experimental Application Form Modal */}
      <ExperimentalApplicationForm 
        isOpen={isApplicationFormOpen} 
        onClose={onApplicationFormClose} 
      />
    </Box>
  );
} 