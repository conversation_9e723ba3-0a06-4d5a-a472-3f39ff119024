"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3976],{74201:(e,t,r)=>{r.d(t,{Qx:()=>l,a6:()=>u,h4:()=>X,jM:()=>Q,ss:()=>I});var n,o=Symbol.for("immer-nothing"),i=Symbol.for("immer-draftable"),a=Symbol.for("immer-state");function c(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var s=Object.getPrototypeOf;function l(e){return!!e&&!!e[a]}function u(e){return!!e&&(p(e)||Array.isArray(e)||!!e[i]||!!e.constructor?.[i]||b(e)||m(e))}var f=Object.prototype.constructor.toString();function p(e){if(!e||"object"!=typeof e)return!1;let t=s(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function _(e,t){0===y(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function y(e){let t=e[a];return t?t.type_:Array.isArray(e)?1:b(e)?2:3*!!m(e)}function d(e,t){return 2===y(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function h(e,t,r){let n=y(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function b(e){return e instanceof Map}function m(e){return e instanceof Set}function v(e){return e.copy_||e.base_}function g(e,t){if(b(e))return new Map(e);if(m(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=p(e);if(!0!==t&&("class_only"!==t||r)){let t=s(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[a];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let o=r[n],i=t[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[o]})}return Object.create(s(e),t)}}function P(e,t=!1){return O(e)||l(e)||!u(e)||(y(e)>1&&(e.set=e.add=e.clear=e.delete=w),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>P(t,!0))),e}function w(){c(2)}function O(e){return Object.isFrozen(e)}var S={};function z(e){let t=S[e];return t||c(0,e),t}function j(e,t){t&&(z("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function F(e){D(e),e.drafts_.forEach(C),e.drafts_=null}function D(e){e===n&&(n=e.parent_)}function A(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function C(e){let t=e[a];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function N(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[a].modified_&&(F(t),c(4)),u(e)&&(e=M(t,e),t.parent_||k(t,e)),t.patches_&&z("Patches").generateReplacementPatches_(r[a].base_,e,t.patches_,t.inversePatches_)):e=M(t,r,[]),F(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==o?e:void 0}function M(e,t,r){if(O(t))return t;let n=t[a];if(!n)return _(t,(o,i)=>E(e,n,t,o,i,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return k(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,o=t,i=!1;3===n.type_&&(o=new Set(t),t.clear(),i=!0),_(o,(o,a)=>E(e,n,t,o,a,r,i)),k(e,t,!1),r&&e.patches_&&z("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function E(e,t,r,n,o,i,a){if(l(o)){let a=M(e,o,i&&t&&3!==t.type_&&!d(t.assigned_,n)?i.concat(n):void 0);if(h(r,n,a),!l(a))return;e.canAutoFreeze_=!1}else a&&r.add(o);if(u(o)&&!O(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;M(e,o),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&k(e,o)}}function k(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&P(t,r)}var x={get(e,t){if(t===a)return e;let r=v(e);if(!d(r,t)){var n=e,o=r,i=t;let a=T(o,i);return a?"value"in a?a.value:a.get?.call(n.draft_):void 0}let c=r[t];return e.finalized_||!u(c)?c:c===$(e.base_,t)?(W(e),e.copy_[t]=U(c,e)):c},has:(e,t)=>t in v(e),ownKeys:e=>Reflect.ownKeys(v(e)),set(e,t,r){let n=T(v(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=$(v(e),t),o=n?.[a];if(o&&o.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||d(e.base_,t)))return!0;W(e),K(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==$(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,W(e),K(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=v(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){c(11)},getPrototypeOf:e=>s(e.base_),setPrototypeOf(){c(12)}},R={};function $(e,t){let r=e[a];return(r?v(r):e)[t]}function T(e,t){if(!(t in e))return;let r=s(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=s(r)}}function K(e){!e.modified_&&(e.modified_=!0,e.parent_&&K(e.parent_))}function W(e){e.copy_||(e.copy_=g(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function U(e,t){let r=b(e)?z("MapSet").proxyMap_(e,t):m(e)?z("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),o={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},i=o,a=x;r&&(i=[o],a=R);let{revoke:c,proxy:s}=Proxy.revocable(i,a);return o.draft_=s,o.revoke_=c,s}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function I(e){return l(e)||c(10,e),function e(t){let r;if(!u(t)||O(t))return t;let n=t[a];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=g(t,n.scope_.immer_.useStrictShallowCopy_)}else r=g(t,!0);return _(r,(t,n)=>{h(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}_(x,(e,t)=>{R[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),R.deleteProperty=function(e,t){return R.set.call(this,e,t,void 0)},R.set=function(e,t,r){return x.set.call(this,e[0],t,r,e[0])};var L=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...o){return n.produce(e,e=>t.call(this,e,...o))}}if("function"!=typeof t&&c(6),void 0!==r&&"function"!=typeof r&&c(7),u(e)){let o=A(this),i=U(e,void 0),a=!0;try{n=t(i),a=!1}finally{a?F(o):D(o)}return j(o,r),N(n,o)}if(e&&"object"==typeof e)c(1,e);else{if(void 0===(n=t(e))&&(n=e),n===o&&(n=void 0),this.autoFreeze_&&P(n,!0),r){let t=[],o=[];z("Patches").generateReplacementPatches_(e,n,t,o),r(t,o)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){u(e)||c(8),l(e)&&(e=I(e));let t=A(this),r=U(e,void 0);return r[a].isManual_=!0,D(t),r}finishDraft(e,t){let r=e&&e[a];r&&r.isManual_||c(9);let{scope_:n}=r;return j(n,t),N(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=z("Patches").applyPatches_;return l(e)?n(e,t):this.produce(e,e=>n(e,t))}},Q=L.produce;function X(e){return e}L.produceWithPatches.bind(L),L.setAutoFreeze.bind(L),L.setUseStrictShallowCopy.bind(L),L.applyPatches.bind(L),L.createDraft.bind(L),L.finishDraft.bind(L)},78910:(e,t,r)=>{var n=r(18306),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},c={};function s(e){return n.isMemo(e)?a:c[e.$$typeof]||o}c[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},c[n.Memo]=a;var l=Object.defineProperty,u=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,_=Object.getPrototypeOf,y=Object.prototype;e.exports=function e(t,r,n){if("string"!=typeof r){if(y){var o=_(r);o&&o!==y&&e(t,o,n)}var a=u(r);f&&(a=a.concat(f(r)));for(var c=s(t),d=s(r),h=0;h<a.length;++h){var b=a[h];if(!i[b]&&!(n&&n[b])&&!(d&&d[b])&&!(c&&c[b])){var m=p(r,b);try{l(t,b,m)}catch(e){}}}}return t}}}]);