"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/analytics/server";
exports.ids = ["pages/api/analytics/server"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fanalytics%2Fserver&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Canalytics%5Cserver.ts&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fanalytics%2Fserver&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Canalytics%5Cserver.ts&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_analytics_server_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\analytics\\server.ts */ \"(api-node)/./pages/api/analytics/server.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_analytics_server_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_analytics_server_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_analytics_server_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_analytics_server_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/analytics/server\",\n        pathname: \"/api/analytics/server\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_analytics_server_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjVfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmFuYWx5dGljcyUyRnNlcnZlciZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTVDYXBpJTVDYW5hbHl0aWNzJTVDc2VydmVyLnRzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNFO0FBQzFEO0FBQytEO0FBQy9EO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQywyREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMsMkRBQVE7QUFDcEM7QUFDTyx3QkFBd0IseUdBQW1CO0FBQ2xEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVELHFDIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlc1xcXFxhcGlcXFxcYW5hbHl0aWNzXFxcXHNlcnZlci50c1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCAnZGVmYXVsdCcpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgJ2NvbmZpZycpO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgUGFnZXNBUElSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuUEFHRVNfQVBJLFxuICAgICAgICBwYWdlOiBcIi9hcGkvYW5hbHl0aWNzL3NlcnZlclwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2FuYWx5dGljcy9zZXJ2ZXJcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fanalytics%2Fserver&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Canalytics%5Cserver.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/analytics/server.ts":
/*!***************************************!*\
  !*** ./pages/api/analytics/server.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n/* harmony import */ var _discordjs_rest__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @discordjs/rest */ \"@discordjs/rest\");\n/* harmony import */ var discord_api_types_v10__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! discord-api-types/v10 */ \"discord-api-types/v10\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_discordjs_rest__WEBPACK_IMPORTED_MODULE_3__, discord_api_types_v10__WEBPACK_IMPORTED_MODULE_4__]);\n([_discordjs_rest__WEBPACK_IMPORTED_MODULE_3__, discord_api_types_v10__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session) {\n            return res.status(401).json({\n                error: 'Unauthorized'\n            });\n        }\n        // Initialize Discord REST client\n        const rest = new _discordjs_rest__WEBPACK_IMPORTED_MODULE_3__.REST({\n            version: '10'\n        }).setToken(_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token);\n        const guildId = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId;\n        // Fetch guild data\n        const [guild, channels, roles, members] = await Promise.all([\n            rest.get(discord_api_types_v10__WEBPACK_IMPORTED_MODULE_4__.Routes.guild(guildId)),\n            rest.get(discord_api_types_v10__WEBPACK_IMPORTED_MODULE_4__.Routes.guildChannels(guildId)),\n            rest.get(discord_api_types_v10__WEBPACK_IMPORTED_MODULE_4__.Routes.guildRoles(guildId)),\n            rest.get(discord_api_types_v10__WEBPACK_IMPORTED_MODULE_4__.Routes.guildMembers(guildId), {\n                query: new URLSearchParams({\n                    limit: '1000'\n                })\n            })\n        ]);\n        // Calculate online members (check presence if available, else use a better approximation)\n        let onlineMembers = 0;\n        if (members.some((m)=>m.presence)) {\n            // If presence data is available (requires presence intent)\n            onlineMembers = members.filter((member)=>member.user && !member.user.bot && member.presence?.status !== 'offline').length;\n        } else {\n            // Fallback: Assume 50% of non-bot members are online (better than 0.3)\n            onlineMembers = Math.ceil(members.filter((member)=>member.user && !member.user.bot).length * 0.5);\n        }\n        // Filter channels by type\n        const textChannels = channels.filter((c)=>c.type === 0).length; // GUILD_TEXT\n        const voiceChannels = channels.filter((c)=>c.type === 2).length; // GUILD_VOICE\n        const categories = channels.filter((c)=>c.type === 4).length; // GUILD_CATEGORY\n        // Filter roles (exclude @everyone and managed roles)\n        const customRoles = roles.filter((role)=>!role.managed && role.name !== '@everyone').length;\n        const serverStats = {\n            totalMembers: guild.member_count || members.length,\n            onlineMembers: onlineMembers,\n            totalChannels: channels.length,\n            textChannels,\n            voiceChannels,\n            categories,\n            totalRoles: customRoles,\n            serverBoosts: guild.premium_subscription_count || 0,\n            boostLevel: guild.premium_tier || 0\n        };\n        // Additional metrics: new members joined and left today\n        const db = await getDb();\n        const startOfDay = new Date();\n        startOfDay.setHours(0, 0, 0, 0);\n        const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);\n        const newMembersToday = await db.collection('member_logs').countDocuments({\n            action: 'join',\n            timestamp: {\n                $gte: startOfDay,\n                $lt: endOfDay\n            }\n        }).catch(()=>0);\n        const leftMembersToday = await db.collection('member_logs').countDocuments({\n            action: 'leave',\n            timestamp: {\n                $gte: startOfDay,\n                $lt: endOfDay\n            }\n        }).catch(()=>0);\n        // Weekly joins/leaves\n        const sevenDaysAgo = new Date();\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6);\n        const logsWeek = await db.collection('member_logs').find({\n            timestamp: {\n                $gte: sevenDaysAgo\n            }\n        }).toArray().catch(()=>[]);\n        const dayLabels = [\n            'Sun',\n            'Mon',\n            'Tue',\n            'Wed',\n            'Thu',\n            'Fri',\n            'Sat'\n        ];\n        const daysMap = {\n            Mon: {\n                joins: 0,\n                leaves: 0\n            },\n            Tue: {\n                joins: 0,\n                leaves: 0\n            },\n            Wed: {\n                joins: 0,\n                leaves: 0\n            },\n            Thu: {\n                joins: 0,\n                leaves: 0\n            },\n            Fri: {\n                joins: 0,\n                leaves: 0\n            },\n            Sat: {\n                joins: 0,\n                leaves: 0\n            },\n            Sun: {\n                joins: 0,\n                leaves: 0\n            }\n        };\n        for (const log of logsWeek){\n            const d = new Date(log.timestamp);\n            const label = dayLabels[d.getDay()];\n            if (!daysMap[label]) continue;\n            if (log.action === 'join') daysMap[label].joins += 1;\n            else if (log.action === 'leave') daysMap[label].leaves += 1;\n        }\n        const orderedDays = [\n            'Mon',\n            'Tue',\n            'Wed',\n            'Thu',\n            'Fri',\n            'Sat',\n            'Sun'\n        ];\n        const weeklyMembers = orderedDays.map((day)=>({\n                day,\n                joins: daysMap[day].joins,\n                leaves: daysMap[day].leaves\n            }));\n        serverStats.newMembersToday = newMembersToday;\n        serverStats.leftMembersToday = leftMembersToday;\n        serverStats.weeklyMembers = weeklyMembers;\n        res.status(200).json({\n            serverStats\n        });\n    } catch (error) {\n        console.error('Error fetching server analytics:', error);\n        res.status(500).json({\n            error: 'Failed to fetch server analytics',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        });\n    }\n}\n// Reuse connection pattern like other analytics\nlet cachedClient = null;\nconst mongoUrl = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.database?.url || 'mongodb://localhost:27017';\nconst dbName = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.database?.name || 'discord_bot';\nasync function getDb() {\n    if (!cachedClient) {\n        cachedClient = await mongodb__WEBPACK_IMPORTED_MODULE_5__.MongoClient.connect(mongoUrl, {\n            ..._core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.database?.options || {}\n        });\n    }\n    return cachedClient.db(dbName);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/analytics/server.ts\n");

/***/ }),

/***/ "@discordjs/rest":
/*!**********************************!*\
  !*** external "@discordjs/rest" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("@discordjs/rest");;

/***/ }),

/***/ "discord-api-types/v10":
/*!****************************************!*\
  !*** external "discord-api-types/v10" ***!
  \****************************************/
/***/ ((module) => {

module.exports = import("discord-api-types/v10");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_d3-sc","lib-node_modules_pnpm_d3-time-","lib-node_modules_pnpm_dec","lib-node_modules_pnpm_e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_immer_10_1_1_node_modules_immer_dist_immer_mjs-806fdd73","lib-node_modules_pnpm_i","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_react-c","lib-node_modules_pnpm_react-red","lib-node_modules_pnpm_rea","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-5c215c87","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-bf697780","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-334e6784","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-d89a0eeb","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-4c6b09db","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-d29869f5","lib-node_modules_pnpm_rec","lib-node_modules_pnpm_redux_","lib-node_modules_pnpm_r","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fanalytics%2Fserver&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Canalytics%5Cserver.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();