"use strict";(()=>{var e={};e.id=709,e.ids=[709],e.modules={224:e=>{e.exports=import("@discordjs/rest")},5808:(e,t,a)=>{a.a(e,async(e,o)=>{try{a.r(t),a.d(t,{default:()=>c});var s=a(15806),r=a(94506),n=a(98580),i=a(224),l=a(33915),u=a(12518),d=e([i,l]);async function c(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{if(!await (0,s.getServerSession)(e,t,r.authOptions))return t.status(401).json({error:"Unauthorized"});let a=new i.REST({version:"10"}).setToken(n.dashboardConfig.bot.token),o=n.dashboardConfig.bot.guildId,[u,d,c,g]=await Promise.all([a.get(l.Routes.guild(o)),a.get(l.Routes.guildChannels(o)),a.get(l.Routes.guildRoles(o)),a.get(l.Routes.guildMembers(o),{query:new URLSearchParams({limit:"1000"})})]),p=0;p=g.some(e=>e.presence)?g.filter(e=>e.user&&!e.user.bot&&e.presence?.status!=="offline").length:Math.ceil(.5*g.filter(e=>e.user&&!e.user.bot).length);let h=d.filter(e=>0===e.type).length,f=d.filter(e=>2===e.type).length,b=d.filter(e=>4===e.type).length,v=c.filter(e=>!e.managed&&"@everyone"!==e.name).length,y={totalMembers:u.member_count||g.length,onlineMembers:p,totalChannels:d.length,textChannels:h,voiceChannels:f,categories:b,totalRoles:v,serverBoosts:u.premium_subscription_count||0,boostLevel:u.premium_tier||0},w=await m(),j=new Date;j.setHours(0,0,0,0);let x=new Date(j.getTime()+864e5),M=await w.collection("member_logs").countDocuments({action:"join",timestamp:{$gte:j,$lt:x}}).catch(()=>0),T=await w.collection("member_logs").countDocuments({action:"leave",timestamp:{$gte:j,$lt:x}}).catch(()=>0),S=new Date;S.setDate(S.getDate()-6);let q=await w.collection("member_logs").find({timestamp:{$gte:S}}).toArray().catch(()=>[]),C=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],D={Mon:{joins:0,leaves:0},Tue:{joins:0,leaves:0},Wed:{joins:0,leaves:0},Thu:{joins:0,leaves:0},Fri:{joins:0,leaves:0},Sat:{joins:0,leaves:0},Sun:{joins:0,leaves:0}};for(let e of q){let t=C[new Date(e.timestamp).getDay()];D[t]&&("join"===e.action?D[t].joins+=1:"leave"===e.action&&(D[t].leaves+=1))}let R=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"].map(e=>({day:e,joins:D[e].joins,leaves:D[e].leaves}));y.newMembersToday=M,y.leftMembersToday=T,y.weeklyMembers=R,t.status(200).json({serverStats:y})}catch(e){t.status(500).json({error:"Failed to fetch server analytics",details:e instanceof Error?e.message:"Unknown error"})}}[i,l]=d.then?(await d)():d;let g=null,p=n.dashboardConfig.database?.url||"mongodb://localhost:27017",h=n.dashboardConfig.database?.name||"discord_bot";async function m(){return g||(g=await u.MongoClient.connect(p,{...n.dashboardConfig.database?.options||{}})),g.db(h)}o()}catch(e){o(e)}})},12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},33915:e=>{e.exports=import("discord-api-types/v10")},39259:(e,t,a)=>{a.a(e,async(e,o)=>{try{a.r(t),a.d(t,{config:()=>d,default:()=>u,routeModule:()=>c});var s=a(93433),r=a(20264),n=a(20584),i=a(5808),l=e([i]);i=(l.then?(await l)():l)[0];let u=(0,n.M)(i,"default"),d=(0,n.M)(i,"config"),c=new s.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/analytics/server",pathname:"/api/analytics/server",bundlePath:"",filename:""},userland:i});o()}catch(e){o(e)}})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>a(39259));module.exports=o})();