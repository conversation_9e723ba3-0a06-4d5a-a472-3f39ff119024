"use strict";(()=>{var e={};e.id=4822,e.ids=[4822],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},39524:e=>{e.exports=import("gamedig")},43253:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{config:()=>d,default:()=>u,routeModule:()=>l});var s=t(93433),o=t(20264),n=t(20584),i=t(51937),p=e([i]);i=(p.then?(await p)():p)[0];let u=(0,n.M)(i,"default"),d=(0,n.M)(i,"config"),l=new s.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/gameservers/query",pathname:"/api/gameservers/query",bundlePath:"",filename:""},userland:i});a()}catch(e){a(e)}})},51937:(e,r,t)=>{t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>l});var s=t(15806),o=t(94506),n=t(39524),i=t(12518),p=t(98580),u=e([n]);n=(u.then?(await u)():u)[0];let{url:m,name:c}=p.dashboardConfig.database,y=null;async function d(){if(y)return y;let e=await i.MongoClient.connect(m);return y=e,e}let h=new n.GameDig;async function l(e,r){if(!await (0,s.getServerSession)(e,r,o.authOptions))return r.status(401).json({error:"Unauthorized"});if("GET"!==e.method)return r.status(405).json({error:"Method not allowed"});try{let e=(await d()).db(c),t=(await e.collection("gameservers").find({}).toArray()).map(e=>({_id:e._id?.toString(),type:e.type,host:e.host,port:e.port,name:e.name,description:e.description,hasPassword:e.hasPassword,password:e.password})),a=await Promise.all(t.map(async e=>{try{let r=await h.query({type:e.type,host:e.host,port:e.port,maxAttempts:1,socketTimeout:2e3,..."sdtd"===e.type?{port:e.port,queryPort:e.port+1}:{}});return{...e,online:!0,players:r.players,maxPlayers:r.maxplayers,map:r.map,name:r.name||e.name,ping:r.ping,lastUpdated:new Date().toISOString()}}catch(t){let r=t instanceof Error?t.message:"Unknown error";return{...e,online:!1,error:r,lastUpdated:new Date().toISOString()}}}));return r.status(200).json(a)}catch(e){return r.status(500).json({error:"Internal server error"})}}a()}catch(e){a(e)}})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(43253));module.exports=a})();