"use strict";(()=>{var e={};e.id=7453,e.ids=[636,3220,7453],e.modules={3814:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>h,getServerSideProps:()=>p});var a=r(8732),i=r(81752),n=r(81011),l=r(82015),c=r(15806),d=r(92546),o=r(95814),x=e([i,n]);function h(){let[e,t]=(0,l.useState)([]),[r,s]=(0,l.useState)([]),[c,d]=(0,l.useState)(!0),[x,h]=(0,l.useState)({total:0,pending:0,approved:0,rejected:0,recentIncrease:0}),p=(0,i.useToast)(),{isOpen:j,onOpen:u,onClose:m}=(0,i.useDisclosure)(),[S,g]=(0,l.useState)(null),T=async()=>{try{let e=await fetch("/api/admin/applications");if(e.ok){let r=await e.json();t(r.applications||[]),h(r.stats||x)}}catch(e){p({title:"Error",description:"Failed to load applications",status:"error",duration:3e3})}finally{d(!1)}},b=async()=>{try{let e=await fetch("/api/admin/applications?type=experimental");if(e.ok){let t=await e.json();s((t.applications||[]).filter(e=>"experimental"===e.type))}}catch(e){}},f=async(e,t,r)=>{try{(await fetch(`/api/admin/applications/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:t})})).ok&&("regular"===r?T():b(),p({title:"Success",description:`Application ${t}d successfully`,status:"success",duration:3e3}),m())}catch(e){p({title:"Error",description:`Failed to ${t} application`,status:"error",duration:3e3})}},y=e=>{g(e),u()};return c?(0,a.jsx)(n.A,{children:(0,a.jsx)(i.Box,{p:8,display:"flex",justifyContent:"center",alignItems:"center",minH:"400px",children:(0,a.jsx)(i.Spinner,{size:"xl"})})}):(0,a.jsxs)(n.A,{children:[(0,a.jsx)(i.Box,{p:8,children:(0,a.jsxs)(i.VStack,{align:"stretch",spacing:6,children:[(0,a.jsxs)(i.HStack,{children:[(0,a.jsx)(i.Icon,{as:o.t69,boxSize:6,color:"blue.500"}),(0,a.jsx)(i.Heading,{size:"lg",children:"Applications Management"})]}),(0,a.jsx)(i.Text,{color:"gray.600",_dark:{color:"gray.300"},children:"Manage and review all user applications including role applications and experimental feature requests."}),(0,a.jsxs)(i.SimpleGrid,{columns:{base:1,md:4},spacing:6,children:[(0,a.jsx)(i.Card,{children:(0,a.jsx)(i.CardBody,{children:(0,a.jsxs)(i.Stat,{children:[(0,a.jsx)(i.StatLabel,{children:"Total Applications"}),(0,a.jsx)(i.StatNumber,{children:x.total}),(0,a.jsxs)(i.StatHelpText,{children:[(0,a.jsx)(i.StatArrow,{type:x.recentIncrease>=0?"increase":"decrease"}),Math.abs(x.recentIncrease),"% this month"]})]})})}),(0,a.jsx)(i.Card,{children:(0,a.jsx)(i.CardBody,{children:(0,a.jsxs)(i.Stat,{children:[(0,a.jsx)(i.StatLabel,{children:"Pending Review"}),(0,a.jsx)(i.StatNumber,{color:"yellow.500",children:x.pending}),(0,a.jsx)(i.StatHelpText,{children:"Requires attention"})]})})}),(0,a.jsx)(i.Card,{children:(0,a.jsx)(i.CardBody,{children:(0,a.jsxs)(i.Stat,{children:[(0,a.jsx)(i.StatLabel,{children:"Approved"}),(0,a.jsx)(i.StatNumber,{color:"green.500",children:x.approved}),(0,a.jsx)(i.StatHelpText,{children:"Accepted applications"})]})})}),(0,a.jsx)(i.Card,{children:(0,a.jsx)(i.CardBody,{children:(0,a.jsxs)(i.Stat,{children:[(0,a.jsx)(i.StatLabel,{children:"Rejected"}),(0,a.jsx)(i.StatNumber,{color:"red.500",children:x.rejected}),(0,a.jsx)(i.StatHelpText,{children:"Declined applications"})]})})})]}),(0,a.jsxs)(i.Tabs,{colorScheme:"blue",isLazy:!0,children:[(0,a.jsxs)(i.TabList,{children:[(0,a.jsx)(i.Tab,{children:(0,a.jsxs)(i.HStack,{children:[(0,a.jsx)(i.Icon,{as:o.t69}),(0,a.jsx)(i.Text,{children:"Role Applications"}),e.filter(e=>"pending"===e.status).length>0&&(0,a.jsx)(i.Badge,{colorScheme:"red",children:e.filter(e=>"pending"===e.status).length})]})}),(0,a.jsx)(i.Tab,{children:(0,a.jsxs)(i.HStack,{children:[(0,a.jsx)(i.Icon,{as:o.XcJ}),(0,a.jsx)(i.Text,{children:"Experimental Requests"}),r.filter(e=>"pending"===e.status).length>0&&(0,a.jsx)(i.Badge,{colorScheme:"yellow",children:r.filter(e=>"pending"===e.status).length})]})})]}),(0,a.jsxs)(i.TabPanels,{children:[(0,a.jsx)(i.TabPanel,{children:0===e.length?(0,a.jsxs)(i.Alert,{status:"info",children:[(0,a.jsx)(i.AlertIcon,{}),(0,a.jsx)(i.AlertDescription,{children:"No role applications found. Applications will appear here when users apply for moderator or other roles."})]}):(0,a.jsxs)(i.Table,{variant:"simple",children:[(0,a.jsx)(i.Thead,{children:(0,a.jsxs)(i.Tr,{children:[(0,a.jsx)(i.Th,{children:"User"}),(0,a.jsx)(i.Th,{children:"Application Type"}),(0,a.jsx)(i.Th,{children:"Submitted"}),(0,a.jsx)(i.Th,{children:"Status"}),(0,a.jsx)(i.Th,{children:"Actions"})]})}),(0,a.jsx)(i.Tbody,{children:e.map(e=>(0,a.jsxs)(i.Tr,{children:[(0,a.jsx)(i.Td,{children:(0,a.jsxs)(i.HStack,{children:[(0,a.jsx)(i.Avatar,{size:"sm",name:e.username||e.userId}),(0,a.jsx)(i.Text,{children:e.username||e.userId})]})}),(0,a.jsx)(i.Td,{children:"Moderator"}),(0,a.jsx)(i.Td,{children:new Date(e.date).toLocaleDateString()}),(0,a.jsx)(i.Td,{children:(0,a.jsx)(i.Badge,{colorScheme:"approved"===e.status?"green":"rejected"===e.status?"red":"yellow",children:e.status})}),(0,a.jsx)(i.Td,{children:(0,a.jsxs)(i.HStack,{spacing:2,children:[(0,a.jsx)(i.Button,{size:"sm",colorScheme:"blue",onClick:()=>y(e),children:"View"}),"pending"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.Button,{size:"sm",colorScheme:"green",onClick:()=>f(e._id,"approve","regular"),children:"Accept"}),(0,a.jsx)(i.Button,{size:"sm",colorScheme:"red",onClick:()=>f(e._id,"reject","regular"),children:"Reject"})]})]})})]},e._id))})]})}),(0,a.jsx)(i.TabPanel,{children:0===r.length?(0,a.jsxs)(i.Alert,{status:"info",children:[(0,a.jsx)(i.AlertIcon,{}),(0,a.jsx)(i.AlertDescription,{children:"No experimental feature requests found. Requests will appear here when users apply for experimental features access."})]}):(0,a.jsxs)(i.Table,{variant:"simple",children:[(0,a.jsx)(i.Thead,{children:(0,a.jsxs)(i.Tr,{children:[(0,a.jsx)(i.Th,{children:"User"}),(0,a.jsx)(i.Th,{children:"Feature"}),(0,a.jsx)(i.Th,{children:"Submitted"}),(0,a.jsx)(i.Th,{children:"Status"}),(0,a.jsx)(i.Th,{children:"Actions"})]})}),(0,a.jsx)(i.Tbody,{children:r.map(e=>(0,a.jsxs)(i.Tr,{children:[(0,a.jsx)(i.Td,{children:(0,a.jsxs)(i.HStack,{children:[(0,a.jsx)(i.Avatar,{size:"sm",name:e.username||e.userId}),(0,a.jsx)(i.Text,{children:e.username||e.userId})]})}),(0,a.jsx)(i.Td,{children:(0,a.jsx)(i.Badge,{colorScheme:"purple",children:e.feature})}),(0,a.jsx)(i.Td,{children:new Date(e.timestamp).toLocaleDateString()}),(0,a.jsx)(i.Td,{children:(0,a.jsx)(i.Badge,{colorScheme:"approved"===e.status?"green":"rejected"===e.status?"red":"yellow",children:e.status})}),(0,a.jsx)(i.Td,{children:(0,a.jsxs)(i.HStack,{spacing:2,children:[(0,a.jsx)(i.Button,{size:"sm",colorScheme:"blue",onClick:()=>y(e),children:"View"}),"pending"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.Button,{size:"sm",colorScheme:"green",onClick:()=>f(e._id,"approve","experimental"),children:"Accept"}),(0,a.jsx)(i.Button,{size:"sm",colorScheme:"red",onClick:()=>f(e._id,"reject","experimental"),children:"Reject"})]})]})})]},e._id))})]})})]})]})]})}),(0,a.jsxs)(i.Modal,{isOpen:j,onClose:m,size:"xl",children:[(0,a.jsx)(i.ModalOverlay,{}),(0,a.jsxs)(i.ModalContent,{children:[(0,a.jsx)(i.ModalHeader,{children:(0,a.jsxs)(i.HStack,{children:[(0,a.jsx)(i.Icon,{as:S&&"feature"in S?o.XcJ:o.t69}),(0,a.jsx)(i.Text,{children:"Application Details"})]})}),(0,a.jsx)(i.ModalCloseButton,{}),(0,a.jsx)(i.ModalBody,{pb:6,children:S&&(0,a.jsxs)(i.VStack,{align:"stretch",spacing:4,children:[(0,a.jsxs)(i.HStack,{children:[(0,a.jsx)(i.Avatar,{name:S.username||S.userId}),(0,a.jsxs)(i.VStack,{align:"start",spacing:0,children:[(0,a.jsx)(i.Text,{fontWeight:"bold",children:S.username||S.userId}),(0,a.jsx)(i.Text,{fontSize:"sm",color:"gray.500",children:"feature"in S?"Experimental Request":"Role Application"})]})]}),(0,a.jsx)(i.Divider,{}),"feature"in S?(0,a.jsxs)(i.VStack,{align:"stretch",spacing:3,children:[(0,a.jsxs)(i.Box,{children:[(0,a.jsx)(i.Text,{fontWeight:"bold",children:"Feature Requested:"}),(0,a.jsx)(i.Badge,{colorScheme:"purple",children:S.feature})]}),(0,a.jsxs)(i.Box,{children:[(0,a.jsx)(i.Text,{fontWeight:"bold",children:"Reason:"}),(0,a.jsx)(i.Text,{children:S.reason})]}),(0,a.jsxs)(i.Box,{children:[(0,a.jsx)(i.Text,{fontWeight:"bold",children:"Submitted:"}),(0,a.jsx)(i.Text,{children:new Date(S.timestamp).toLocaleString()})]})]}):(0,a.jsxs)(i.VStack,{align:"stretch",spacing:3,children:[(0,a.jsxs)(i.Box,{children:[(0,a.jsx)(i.Text,{fontWeight:"bold",children:"Age:"}),(0,a.jsxs)(i.Text,{children:[S.age," years old"]})]}),(0,a.jsxs)(i.Box,{children:[(0,a.jsx)(i.Text,{fontWeight:"bold",children:"Timezone:"}),(0,a.jsx)(i.Text,{children:S.timezone})]}),(0,a.jsxs)(i.Box,{children:[(0,a.jsx)(i.Text,{fontWeight:"bold",children:"Hours per week:"}),(0,a.jsxs)(i.Text,{children:[S.hoursPerWeek," hours"]})]}),(0,a.jsxs)(i.Box,{children:[(0,a.jsx)(i.Text,{fontWeight:"bold",children:"Motivation:"}),(0,a.jsx)(i.Text,{children:S.answers?.statement})]}),S.extraInfo&&(0,a.jsxs)(i.Box,{children:[(0,a.jsx)(i.Text,{fontWeight:"bold",children:"Additional Information:"}),(0,a.jsx)(i.Text,{children:S.extraInfo})]})]})]})})]})]})]})}[i,n]=x.then?(await x)():x;let p=async e=>{let t=await (0,c.getServerSession)(e.req,e.res,d.N);return t?{props:{session:t}}:{redirect:{destination:"/api/auth/signin?callbackUrl=%2Fadmin%2Fapplications",permanent:!1}}};s()}catch(e){s(e)}})},4722:e=>{e.exports=require("next-auth/react")},8732:e=>{e.exports=require("react/jsx-runtime")},14078:e=>{e.exports=import("swr")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},22326:e=>{e.exports=require("react-dom")},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},30067:e=>{e.exports=import("@emotion/styled")},33873:e=>{e.exports=require("path")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},74075:e=>{e.exports=require("zlib")},74934:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>u,default:()=>x,getServerSideProps:()=>j,getStaticPaths:()=>p,getStaticProps:()=>h,reportWebVitals:()=>m,routeModule:()=>y,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>T,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>S});var a=r(1292),i=r(58834),n=r(40786),l=r(83567),c=r(8077),d=r(3814),o=e([c,d]);[c,d]=o.then?(await o)():o;let x=(0,n.M)(d,"default"),h=(0,n.M)(d,"getStaticProps"),p=(0,n.M)(d,"getStaticPaths"),j=(0,n.M)(d,"getServerSideProps"),u=(0,n.M)(d,"config"),m=(0,n.M)(d,"reportWebVitals"),S=(0,n.M)(d,"unstable_getStaticProps"),g=(0,n.M)(d,"unstable_getStaticPaths"),T=(0,n.M)(d,"unstable_getStaticParams"),b=(0,n.M)(d,"unstable_getServerProps"),f=(0,n.M)(d,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/admin/applications",pathname:"/admin/applications",bundlePath:"",filename:""},components:{App:c.default,Document:l.default},userland:d});s()}catch(e){s(e)}})},81752:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{Alert:()=>a.F,AlertDescription:()=>i.T,AlertIcon:()=>n._,Avatar:()=>l.e,Badge:()=>c.E,Box:()=>d.a,Button:()=>o.$,Card:()=>x.Z,CardBody:()=>h.b,Divider:()=>p.c,HStack:()=>j.z,Heading:()=>u.D,Icon:()=>m.I,Modal:()=>S.aF,ModalBody:()=>g.c,ModalCloseButton:()=>T.s,ModalContent:()=>b.$,ModalHeader:()=>f.r,ModalOverlay:()=>y.m,SimpleGrid:()=>v.r,Spinner:()=>A.y,Stat:()=>B.r,StatArrow:()=>k.Ip,StatHelpText:()=>w.h,StatLabel:()=>M.v,StatNumber:()=>P.k,Tab:()=>C.o,TabList:()=>I.w,TabPanel:()=>q.K,TabPanels:()=>H.T,Table:()=>z.X,Tabs:()=>_.t,Tbody:()=>D.N,Td:()=>R.Td,Text:()=>W.E,Th:()=>L.Th,Thead:()=>F.d,Tr:()=>N.Tr,VStack:()=>V.T,useDisclosure:()=>$.j,useToast:()=>E.d});var a=r(5128),i=r(76331),n=r(31772),l=r(36308),c=r(25392),d=r(45200),o=r(77502),x=r(90846),h=r(60615),p=r(464),j=r(55197),u=r(30519),m=r(50792),S=r(75460),g=r(42929),T=r(7394),b=r(89164),f=r(95148),y=r(12725),v=r(67981),A=r(90088),B=r(33593),k=r(57186),w=r(58018),M=r(71577),P=r(42650),C=r(8399),I=r(81248),q=r(46596),H=r(92279),z=r(88468),_=r(64450),D=r(46196),R=r(54474),W=r(87378),L=r(29838),F=r(50938),N=r(82548),V=r(17335),E=r(5978),$=r(66646);r(9436),r(25035);var O=e([a,i,n,l,c,d,o,x,h,p,j,u,m,S,g,T,b,f,y,v,A,B,k,w,M,P,C,I,q,H,z,_,D,R,W,L,F,N,V,E]);[a,i,n,l,c,d,o,x,h,p,j,u,m,S,g,T,b,f,y,v,A,B,k,w,M,P,C,I,q,H,z,_,D,R,W,L,F,N,V,E]=O.then?(await O)():O,s()}catch(e){s(e)}})},82015:e=>{e.exports=require("react")},88455:e=>{e.exports=import("@emotion/react")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2457,9784,6021,3786,8740,9498,2142,1283,589,6185,4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(74934));module.exports=s})();