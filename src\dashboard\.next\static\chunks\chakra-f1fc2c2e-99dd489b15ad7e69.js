"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3786],{1871:(e,t,r)=>{r.d(t,{z:()=>l});var n=r(94513),a=r(71569);let l=(0,r(84756).R)((e,t)=>(0,n.jsx)(a.B,{align:"center",...e,direction:"row",ref:t}));l.displayName="HStack"},2996:(e,t,r)=>{r.d(t,{D:()=>i});var n=r(94513),a=r(79435),l=r(38822),o=r(84756),s=r(21533);let i=(0,o.R)(function(e,t){let{getHeaderProps:r}=(0,l.C_)(),o=(0,l.jm)();return(0,n.jsx)(s.B.header,{...r(e,t),className:(0,a.cx)("chakra-popover__header",e.className),__css:o.header})});i.displayName="PopoverHeader"},3037:(e,t,r)=>{r.d(t,{T:()=>l});var n=r(94513),a=r(71569);let l=(0,r(84756).R)((e,t)=>(0,n.jsx)(a.B,{align:"center",...e,direction:"column",ref:t}));l.displayName="VStack"},8475:(e,t,r)=>{r.d(t,{V:()=>u,o:()=>c});var n=r(56005),a=r(79435),l=r(94285),o=r(64757),s=r(15327);function i(e,t={}){let{styleConfig:r,...u}=t,{theme:c,colorMode:d}=(0,s.UQ)(),p=e?(0,a.rY)(c,`components.${e}`):void 0,m=r||p,f=(0,a.XQ)({theme:c,colorMode:d},m?.defaultProps??{},Object.fromEntries(Object.entries(u).filter(([e,t])=>void 0!==t&&"children"!==e&&!(0,l.isValidElement)(t))),(e,t)=>e?void 0:t),h=(0,l.useRef)({});if(m){let e=(0,n.uB)(m)(f);o(h.current,e)||(h.current=e)}return h.current}function u(e,t={}){return i(e,t)}function c(e,t={}){return i(e,t)}},9982:(e,t,r)=>{r.d(t,{D:()=>l});var n=r(83541),a=r(94285);function l(){let e=(0,a.useContext)(n.Dx);if(!e)throw Error("useTheme: `theme` is undefined. Seems you forgot to wrap your app in `<ChakraProvider />` or `<ThemeProvider />`");return e}},10246:(e,t,r)=>{r.d(t,{Th:()=>s});var n=r(94513),a=r(82824),l=r(84756),o=r(21533);let s=(0,l.R)(({isNumeric:e,...t},r)=>{let l=(0,a.k)();return(0,n.jsx)(o.B.th,{...t,ref:r,__css:l.th,"data-is-numeric":e})})},11067:(e,t,r)=>{r.d(t,{v:()=>i});var n=r(94513),a=r(79435),l=r(42398),o=r(84756),s=r(21533);let i=(0,o.R)(function(e,t){let r=(0,l.E)();return(0,n.jsx)(s.B.dt,{ref:t,...e,className:(0,a.cx)("chakra-stat__label",e.className),__css:r.label})});i.displayName="StatLabel"},11593:(e,t,r)=>{r.d(t,{D:()=>m});var n=r(94513),a=r(79435),l=r(52156),o=r(84756),s=r(21533);let i=(0,o.R)((e,t)=>{let r=(0,l.$)();return(0,n.jsx)(s.B.span,{ref:t,...e,__css:r.command,className:"chakra-menu__command"})});i.displayName="MenuCommand";var u=r(94285);let c=e=>{let{className:t,children:r,...o}=e,i=(0,l.$)(),c=u.Children.only(r),d=(0,u.isValidElement)(c)?(0,u.cloneElement)(c,{focusable:"false","aria-hidden":!0,className:(0,a.cx)("chakra-menu__icon",c.props.className)}):null,p=(0,a.cx)("chakra-menu__icon-wrapper",t);return(0,n.jsx)(s.B.span,{className:p,...o,__css:i.icon,children:d})};c.displayName="MenuIcon";let d=(0,o.R)((e,t)=>{let{type:r,...a}=e,o=(0,l.$)(),i=a.as||r?r??void 0:"button",c=(0,u.useMemo)(()=>({textDecoration:"none",color:"inherit",userSelect:"none",display:"flex",width:"100%",alignItems:"center",textAlign:"start",flex:"0 0 auto",outline:0,...o.item}),[o.item]);return(0,n.jsx)(s.B.button,{ref:t,type:i,...a,__css:c})});var p=r(45905);let m=(0,o.R)((e,t)=>{let{icon:r,iconSpacing:l="0.75rem",command:o,commandSpacing:s="0.75rem",children:u,...m}=e,f=(0,p.Os)(m,t),h=r||o?(0,n.jsx)("span",{style:{pointerEvents:"none",flex:1},children:u}):u;return(0,n.jsxs)(d,{...f,className:(0,a.cx)("chakra-menu__menuitem",f.className),children:[r&&(0,n.jsx)(c,{fontSize:"0.8em",marginEnd:l,children:r}),h,o&&(0,n.jsx)(i,{marginStart:s,children:o})]})});m.displayName="MenuItem"},15327:(e,t,r)=>{r.d(t,{UQ:()=>l,gf:()=>s,rd:()=>o});var n=r(9982),a=r(88142);function l(){let e=(0,a.G6)(),t=(0,n.D)();return{...e,theme:t}}function o(e,t,r){let a=(0,n.D)();return s(e,t,r)(a)}function s(e,t,r){let n=Array.isArray(t)?t:[t],a=Array.isArray(r)?r:[r];return r=>{let l=a.filter(Boolean),o=n.map((t,n)=>{if("breakpoints"===e){var a=l[n]??t;if(null==t)return t;let e=e=>r.__breakpoints?.asArray?.[e];return e(t)??e(a)??a}var o=`${e}.${t}`,s=l[n]??t;if(null==o)return o;let i=e=>r.__cssMap?.[e]?.value;return i(o)??i(s)??s});return Array.isArray(t)?o:o[0]}}},15975:(e,t,r)=>{r.d(t,{l:()=>c});var n=r(94513),a=r(56005),l=r(79435),o=r(31862),s=r(84756),i=r(8475),u=r(21533);let c=(0,s.R)(function(e,t){let r=(0,i.V)("FormLabel",e),s=(0,a.MN)(e),{className:c,children:p,requiredIndicator:m=(0,n.jsx)(d,{}),optionalIndicator:f=null,...h}=s,x=(0,o.Uc)(),v=x?.getLabelProps(h,t)??{ref:t,...h};return(0,n.jsxs)(u.B.label,{...v,className:(0,l.cx)("chakra-form__label",s.className),__css:{display:"block",textAlign:"start",...r},children:[p,x?.isRequired?m:f]})});c.displayName="FormLabel";let d=(0,s.R)(function(e,t){let r=(0,o.Uc)(),a=(0,o.TP)();if(!r?.isRequired)return null;let s=(0,l.cx)("chakra-form__required-indicator",e.className);return(0,n.jsx)(u.B.span,{...r?.getRequiredIndicatorProps(e,t),__css:a.requiredIndicator,className:s})});d.displayName="RequiredIndicator"},21181:(e,t,r)=>{r.d(t,{T:()=>u});var n=r(94513),a=r(79435),l=r(56858),o=r(87096),s=r(84756),i=r(21533);let u=(0,s.R)(function(e,t){let r=(0,o.uo)(e),s=(0,l.e)();return(0,n.jsx)(i.B.div,{...r,width:"100%",ref:t,className:(0,a.cx)("chakra-tabs__tab-panels",e.className),__css:s.tabpanels})});u.displayName="TabPanels"},21533:(e,t,r)=>{r.d(t,{B:()=>f});var n=r(56005),a=r(79435),l=r(5891),o=r(94285);let s=new Set([...n.q8,"textStyle","layerStyle","apply","noOfLines","focusBorderColor","errorBorderColor","as","__css","css","sx"]),i=new Set(["htmlWidth","htmlHeight","htmlSize","htmlTranslate"]);function u(e){return(i.has(e)||!s.has(e))&&"_"!==e[0]}var c=r(88142);let d=(0,a.TE)(l.A),p=({baseStyle:e})=>t=>{let{theme:r,css:l,__css:o,sx:s,...i}=t,[u]=(0,a.rg)(i,n.HU),c=(0,a.Jg)(e,t),d=(0,a.mH)({},o,c,(0,a.oE)(u),s),p=(0,n.AH)(d)(t.theme);return l?[p,l]:p};function m(e,t){let{baseStyle:r,...n}=t??{};n.shouldForwardProp||(n.shouldForwardProp=u);let a=p({baseStyle:r}),l=d(e,n)(a);return(0,o.forwardRef)(function(e,t){let{children:r,...n}=e,{colorMode:a,forced:s}=(0,c.G6)();return(0,o.createElement)(l,{ref:t,"data-theme":s?a:void 0,...n},r)})}let f=function(){let e=new Map;return new Proxy(m,{apply:(e,t,r)=>m(...r),get:(t,r)=>(e.has(r)||e.set(r,m(r)),e.get(r))})}()},21954:(e,t,r)=>{r.d(t,{Z:()=>h});var n=r(94513),a=r(56084),l=r(79435),o=r(94285),s=r(24518),i=r(45604);let[u,c]=(0,l.q6)({strict:!1,name:"PortalContext"}),d="chakra-portal",p=e=>(0,n.jsx)("div",{className:"chakra-portal-zIndex",style:{position:"absolute",zIndex:e.zIndex,top:0,left:0,right:0},children:e.children}),m=e=>{let{appendToParentPortal:t,children:r}=e,[l,m]=(0,o.useState)(null),f=(0,o.useRef)(null),[,h]=(0,o.useState)({});(0,o.useEffect)(()=>h({}),[]);let x=c(),v=(0,i.T)();(0,a.UQ)(()=>{if(!l)return;let e=l.ownerDocument,r=t?x??e.body:e.body;if(!r)return;f.current=e.createElement("div"),f.current.className=d,r.appendChild(f.current),h({});let n=f.current;return()=>{r.contains(n)&&r.removeChild(n)}},[l]);let b=v?.zIndex?(0,n.jsx)(p,{zIndex:v?.zIndex,children:r}):r;return f.current?(0,s.createPortal)((0,n.jsx)(u,{value:f.current,children:b}),f.current):(0,n.jsx)("span",{ref:e=>{e&&m(e)}})},f=e=>{let{children:t,containerRef:r,appendToParentPortal:l}=e,i=r.current,c=i??("undefined"!=typeof window?document.body:void 0),p=(0,o.useMemo)(()=>{let e=i?.ownerDocument.createElement("div");return e&&(e.className=d),e},[i]),[,m]=(0,o.useState)({});return((0,a.UQ)(()=>m({}),[]),(0,a.UQ)(()=>{if(p&&c)return c.appendChild(p),()=>{c.removeChild(p)}},[p,c]),c&&p)?(0,s.createPortal)((0,n.jsx)(u,{value:l?p:null,children:t}),p):null};function h(e){let{containerRef:t,...r}={appendToParentPortal:!0,...e};return t?(0,n.jsx)(f,{containerRef:t,...r}):(0,n.jsx)(m,{...r})}h.className=d,h.selector=".chakra-portal",h.displayName="Portal"},23450:(e,t,r)=>{r.d(t,{N:()=>u});var n=r(94513),a=r(56005),l=r(79435),o=r(84756),s=r(8475),i=r(21533);let u=(0,o.R)(function(e,t){let r=(0,s.V)("Link",e),{className:o,isExternal:u,...c}=(0,a.MN)(e);return(0,n.jsx)(i.B.a,{target:u?"_blank":void 0,rel:u?"noopener":void 0,ref:t,className:(0,l.cx)("chakra-link",o),...c,__css:r})});u.displayName="Link"},23668:(e,t,r)=>{r.d(t,{A:()=>x});var n=r(94513),a=r(56005),l=r(79435),o=r(38822),s=r(56084),i=r(94285),u=r(70646),c=r(27225);let d={click:"click",hover:"hover"};function p(e,t){return e===t||e?.contains(t)}function m(e){let t=e.currentTarget.ownerDocument.activeElement;return e.relatedTarget??t}var f=r(9982),h=r(8475);function x(e){let t=(0,h.o)("Popover",e),{children:r,...x}=(0,a.MN)(e),v=(0,f.D)(),b=function(e={}){let{closeOnBlur:t=!0,closeOnEsc:r=!0,initialFocusRef:n,id:a,returnFocusOnClose:o=!0,autoFocus:f=!0,arrowSize:h,arrowShadowColor:x,trigger:v=d.click,openDelay:b=200,closeDelay:y=200,isLazy:g,lazyBehavior:_="unmount",computePositionOnMount:k,...w}=e,{isOpen:j,onClose:N,onOpen:C,onToggle:S}=(0,s.j1)(e),[R,E]=(0,i.useState)(o);(0,i.useEffect)(()=>E(o),[o]);let I=(0,i.useRef)(null),M=(0,i.useRef)(null),B=(0,i.useRef)(null),P=(0,i.useRef)(!1),T=(0,i.useRef)(!1);j&&(T.current=!0);let[O,A]=(0,i.useState)(!1),[D,F]=(0,i.useState)(!1),H=(0,i.useId)(),L=a??H,[q,z,$,W]=["popover-trigger","popover-content","popover-header","popover-body"].map(e=>`${e}-${L}`),{referenceRef:U,getArrowProps:K,getPopperProps:V,getArrowInnerProps:G,forceUpdate:J}=(0,u.E)({...w,enabled:j||!!k}),Q=(0,s.vG)({isOpen:j,ref:B});(0,s.Sp)({enabled:j,ref:M}),(0,s.Xb)(B,{focusRef:M,visible:j,shouldFocus:R&&v===d.click}),(0,s.wf)(B,{focusRef:n,visible:j,shouldFocus:f&&v===d.click}),(0,s.jz)({enabled:j&&t,ref:B,handler(e){let t=e.composedPath?.()[0]??[e.target];p(M.current,t)||(t&&E(!(0,l.tp)(t)),N())}});let Y=(0,l.qJ)({wasSelected:T.current,enabled:g,mode:_,isSelected:Q.present}),Z=(0,i.useCallback)((e={},n=null)=>{let a={...e,style:{...e.style,transformOrigin:c.O3.transformOrigin.varRef,[c.O3.arrowSize.var]:h?`${h}px`:void 0,[c.O3.arrowShadowColor.var]:x},ref:(0,s.Px)(B,n),children:Y?e.children:null,id:z,tabIndex:-1,role:"dialog",onKeyDown:(0,l.Hj)(e.onKeyDown,e=>{!e.nativeEvent.isComposing&&r&&"Escape"===e.key&&(e.preventDefault(),e.stopPropagation(),N())}),onBlur:(0,l.Hj)(e.onBlur,e=>{let r=m(e),n=p(B.current,r),a=p(M.current,r);r&&E(!(0,l.tp)(r)),j&&t&&!n&&!a&&N()}),"aria-labelledby":O?$:void 0,"aria-describedby":D?W:void 0};return v===d.hover&&(a.role="tooltip",a.onMouseEnter=(0,l.Hj)(e.onMouseEnter,()=>{P.current=!0}),a.onMouseLeave=(0,l.Hj)(e.onMouseLeave,e=>{null!==e.nativeEvent.relatedTarget&&(P.current=!1,setTimeout(()=>N(),y))})),a},[Y,z,O,$,D,W,v,r,N,j,t,y,x,h]),X=(0,i.useCallback)((e={},t=null)=>V({...e,style:{visibility:j?"visible":"hidden",...e.style}},t),[j,V]),ee=(0,i.useCallback)((e,t=null)=>({...e,ref:(0,s.Px)(t,I,U)}),[I,U]),et=(0,i.useRef)(void 0),er=(0,i.useRef)(void 0),en=(0,i.useCallback)(e=>{null==I.current&&U(e)},[U]),ea=(0,i.useCallback)((e={},r=null)=>{let n={...e,ref:(0,s.Px)(M,r,en),id:q,"aria-haspopup":"dialog","aria-expanded":j,"aria-controls":z};return v===d.click&&(n.onClick=(0,l.Hj)(e.onClick,S)),v===d.hover&&(n.onFocus=(0,l.Hj)(e.onFocus,()=>{void 0===et.current&&C()}),n.onBlur=(0,l.Hj)(e.onBlur,e=>{let r=m(e),n=!p(B.current,r);j&&t&&n&&N()}),n.onKeyDown=(0,l.Hj)(e.onKeyDown,e=>{"Escape"===e.key&&N()}),n.onMouseEnter=(0,l.Hj)(e.onMouseEnter,()=>{P.current=!0,et.current=window.setTimeout(()=>C(),b)}),n.onMouseLeave=(0,l.Hj)(e.onMouseLeave,()=>{P.current=!1,et.current&&(clearTimeout(et.current),et.current=void 0),er.current=window.setTimeout(()=>{!1===P.current&&N()},y)})),n},[q,j,z,v,en,S,C,t,N,b,y]);(0,i.useEffect)(()=>()=>{et.current&&clearTimeout(et.current),er.current&&clearTimeout(er.current)},[]);let el=(0,i.useCallback)((e={},t=null)=>({...e,id:$,ref:(0,s.Px)(t,e=>{A(!!e)})}),[$]),eo=(0,i.useCallback)((e={},t=null)=>({...e,id:W,ref:(0,s.Px)(t,e=>{F(!!e)})}),[W]);return{forceUpdate:J,isOpen:j,onAnimationComplete:Q.onComplete,onClose:N,getAnchorProps:ee,getArrowProps:K,getArrowInnerProps:G,getPopoverPositionerProps:X,getPopoverProps:Z,getTriggerProps:ea,getHeaderProps:el,getBodyProps:eo}}({...x,direction:v.direction});return(0,n.jsx)(o.pb,{value:b,children:(0,n.jsx)(o.hA,{value:t,children:(0,l.Jg)(r,{isOpen:b.isOpen,onClose:b.onClose,forceUpdate:b.forceUpdate})})})}x.displayName="Popover"},24490:(e,t,r)=>{r.d(t,{j:()=>u});var n=r(94513),a=r(56005),l=r(79435),o=r(46949),s=r(84756),i=r(21533);let u=(0,s.R)((e,t)=>{let{className:r,...s}=e,u=(0,l.cx)("chakra-modal__footer",r),c=(0,o.x5)(),d=(0,a.H2)({display:"flex",alignItems:"center",justifyContent:"flex-end",...c.footer});return(0,n.jsx)(i.B.footer,{ref:t,...s,__css:d,className:u})});u.displayName="ModalFooter"},24941:(e,t,r)=>{r.d(t,{E:()=>b});var n=r(94513),a=r(56084),l=r(56005),o=r(79435),s=r(83541),i=r(94285),u=r(15327),c=r(21533),d=r(84756),p=r(8475);let m=(0,c.B)("div",{baseStyle:{boxShadow:"none",backgroundClip:"padding-box",cursor:"default",color:"transparent",pointerEvents:"none",userSelect:"none","&::before, &::after, *":{visibility:"hidden"}}}),f=(0,l.Vg)("skeleton-start-color"),h=(0,l.Vg)("skeleton-end-color"),x=(0,s.i7)({from:{opacity:0},to:{opacity:1}}),v=(0,s.i7)({from:{borderColor:f.reference,background:f.reference},to:{borderColor:h.reference,background:h.reference}}),b=(0,d.R)((e,t)=>{let r={...e,fadeDuration:"number"==typeof e.fadeDuration?e.fadeDuration:.4,speed:"number"==typeof e.speed?e.speed:.8},s=(0,p.V)("Skeleton",r),d=function(){let e=(0,i.useRef)(!0);return(0,i.useEffect)(()=>{e.current=!1},[]),e.current}(),{startColor:b="",endColor:y="",isLoaded:g,fadeDuration:_,speed:k,className:w,fitContent:j,animation:N,...C}=(0,l.MN)(r),[S,R]=(0,u.rd)("colors",[b,y]),E=(0,a.ZC)(g),I=(0,o.cx)("chakra-skeleton",w),M={...S&&{[f.variable]:S},...R&&{[h.variable]:R}};if(g){let e=d||E?"none":`${x} ${_}s`;return(0,n.jsx)(c.B.div,{ref:t,className:I,__css:{animation:e},...C})}return(0,n.jsx)(m,{ref:t,className:I,...C,__css:{width:j?"fit-content":void 0,...s,...M,_dark:{...s._dark,...M},animation:N||`${k}s linear infinite alternate ${v}`}})});b.displayName="Skeleton"},27225:(e,t,r)=>{r.d(t,{Fs:()=>u,Ky:()=>l,O3:()=>a,ed:()=>s});let n=(e,t)=>({var:e,varRef:t?`var(${e}, ${t})`:`var(${e})`}),a={arrowShadowColor:n("--popper-arrow-shadow-color"),arrowSize:n("--popper-arrow-size","8px"),arrowSizeHalf:n("--popper-arrow-size-half"),arrowBg:n("--popper-arrow-bg"),transformOrigin:n("--popper-transform-origin"),arrowOffset:n("--popper-arrow-offset")};function l(e){return e.includes("top")?"1px 1px 0px 0 var(--popper-arrow-shadow-color)":e.includes("bottom")?"-1px -1px 0px 0 var(--popper-arrow-shadow-color)":e.includes("right")?"-1px 1px 0px 0 var(--popper-arrow-shadow-color)":e.includes("left")?"1px -1px 0px 0 var(--popper-arrow-shadow-color)":void 0}let o={top:"bottom center","top-start":"bottom left","top-end":"bottom right",bottom:"top center","bottom-start":"top left","bottom-end":"top right",left:"right center","left-start":"right top","left-end":"right bottom",right:"left center","right-start":"left top","right-end":"left bottom"},s=e=>o[e],i={scroll:!0,resize:!0};function u(e){let t;return"object"==typeof e?{enabled:!0,options:{...i,...e}}:{enabled:e,options:i}}},29484:(e,t,r)=>{r.d(t,{I:()=>u});var n=r(94513),a=r(79435),l=r(84756),o=r(8475),s=r(21533);let i={path:(0,n.jsxs)("g",{stroke:"currentColor",strokeWidth:"1.5",children:[(0,n.jsx)("path",{strokeLinecap:"round",fill:"none",d:"M9,9a3,3,0,1,1,4,2.829,1.5,1.5,0,0,0-1,1.415V14.25"}),(0,n.jsx)("path",{fill:"currentColor",strokeLinecap:"round",d:"M12,17.25a.375.375,0,1,0,.375.375A.375.375,0,0,0,12,17.25h0"}),(0,n.jsx)("circle",{fill:"none",strokeMiterlimit:"10",cx:"12",cy:"12",r:"11.25"})]}),viewBox:"0 0 24 24"},u=(0,l.R)((e,t)=>{let{as:r,viewBox:l,color:u="currentColor",focusable:c=!1,children:d,className:p,__css:m,...f}=e,h=(0,a.cx)("chakra-icon",p),x=(0,o.V)("Icon",e),v={ref:t,focusable:c,className:h,__css:{w:"1em",h:"1em",display:"inline-block",lineHeight:"1em",flexShrink:0,color:u,...m,...x}},b=l??i.viewBox;if(r&&"string"!=typeof r)return(0,n.jsx)(s.B.svg,{as:r,...v,...f});let y=d??i.path;return(0,n.jsx)(s.B.svg,{verticalAlign:"middle",viewBox:b,...v,...f,children:y})});u.displayName="Icon"},29607:(e,t,r)=>{r.d(t,{m:()=>d});var n=r(94513),a=r(79435),l=r(5649),o=r(46949),s=r(48851),i=r(21533),u=r(84756);let c=(0,i.B)(l.PY1.div),d=(0,u.R)((e,t)=>{let{className:r,transition:l,motionProps:i,...u}=e,d=(0,a.cx)("chakra-modal__overlay",r),p={pos:"fixed",left:"0",top:"0",w:"100vw",h:"100vh",...(0,o.x5)().overlay},{motionPreset:m}=(0,o.k3)(),f="none"===m?{}:s.l;return(0,n.jsx)(c,{...i||f,__css:p,ref:t,className:d,...u})});d.displayName="ModalOverlay"},30301:(e,t,r)=>{r.d(t,{r:()=>c});var n=r(94513),a=r(79435),l=r(84756),o=r(21533);let s=(0,l.R)(function(e,t){let{templateAreas:r,gap:a,rowGap:l,columnGap:s,column:i,row:u,autoFlow:c,autoRows:d,templateRows:p,autoColumns:m,templateColumns:f,...h}=e;return(0,n.jsx)(o.B.div,{ref:t,__css:{display:"grid",gridTemplateAreas:r,gridGap:a,gridRowGap:l,gridColumnGap:s,gridAutoColumns:m,gridColumn:i,gridRow:u,gridAutoFlow:c,gridAutoRows:d,gridTemplateRows:p,gridTemplateColumns:f},...h})});s.displayName="Grid";var i=r(9982),u=r(15327);let c=(0,l.R)(function(e,t){var r,l,o;let{columns:c,spacingX:d,spacingY:p,spacing:m,minChildWidth:f,...h}=e,x=(0,i.D)(),v=f?(r=f,l=x,(0,a.bk)(r,e=>{let t=(0,u.gf)("sizes",e,"number"==typeof e?`${e}px`:e)(l);return null===e?null:`repeat(auto-fit, minmax(${t}, 1fr))`})):(o=c,(0,a.bk)(o,e=>null===e?null:`repeat(${e}, minmax(0, 1fr))`));return(0,n.jsx)(s,{ref:t,gap:m,columnGap:d,rowGap:p,templateColumns:v,...h})});c.displayName="SimpleGrid"},30994:(e,t,r)=>{r.d(t,{k:()=>i});var n=r(94513),a=r(79435),l=r(42398),o=r(84756),s=r(21533);let i=(0,o.R)(function(e,t){let r=(0,l.E)();return(0,n.jsx)(s.B.dd,{ref:t,...e,className:(0,a.cx)("chakra-stat__number",e.className),__css:{...r.number,fontFeatureSettings:"pnum",fontVariantNumeric:"proportional-nums"}})});i.displayName="StatNumber"},31840:(e,t,r)=>{r.d(t,{r:()=>c});var n=r(94513),a=r(56005),l=r(79435),o=r(94285),s=r(46949),i=r(84756),u=r(21533);let c=(0,i.R)((e,t)=>{let{className:r,...i}=e,{headerId:c,setHeaderMounted:d}=(0,s.k3)();(0,o.useEffect)(()=>(d(!0),()=>d(!1)),[d]);let p=(0,l.cx)("chakra-modal__header",r),m=(0,s.x5)(),f=(0,a.H2)({flex:0,...m.header});return(0,n.jsx)(u.B.header,{ref:t,className:p,id:c,...i,__css:f})});c.displayName="ModalHeader"},31862:(e,t,r)=>{r.d(t,{MJ:()=>h,TP:()=>p,Uc:()=>f,eK:()=>x});var n=r(94513),a=r(56084),l=r(56005),o=r(79435),s=r(94285),i=r(84756),u=r(8475),c=r(21533);let[d,p]=(0,o.q6)({name:"FormControlStylesContext",errorMessage:"useFormControlStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<FormControl />\" "}),[m,f]=(0,o.q6)({strict:!1,name:"FormControlContext"}),h=(0,i.R)(function(e,t){let r=(0,u.o)("Form",e),{getRootProps:i,htmlProps:p,...f}=function(e){let{id:t,isRequired:r,isInvalid:n,isDisabled:l,isReadOnly:i,...u}=e,c=(0,s.useId)(),d=t||`field-${c}`,p=`${d}-label`,m=`${d}-feedback`,f=`${d}-helptext`,[h,x]=(0,s.useState)(!1),[v,b]=(0,s.useState)(!1),[y,g]=(0,s.useState)(!1),_=(0,s.useCallback)((e={},t=null)=>({id:f,...e,ref:(0,a.Px)(t,e=>{e&&b(!0)})}),[f]),k=(0,s.useCallback)((e={},t=null)=>({...e,ref:t,"data-focus":(0,o.sE)(y),"data-disabled":(0,o.sE)(l),"data-invalid":(0,o.sE)(n),"data-readonly":(0,o.sE)(i),id:void 0!==e.id?e.id:p,htmlFor:void 0!==e.htmlFor?e.htmlFor:d}),[d,l,y,n,i,p]),w=(0,s.useCallback)((e={},t=null)=>({id:m,...e,ref:(0,a.Px)(t,e=>{e&&x(!0)}),"aria-live":"polite"}),[m]),j=(0,s.useCallback)((e={},t=null)=>({...e,...u,ref:t,role:"group","data-focus":(0,o.sE)(y),"data-disabled":(0,o.sE)(l),"data-invalid":(0,o.sE)(n),"data-readonly":(0,o.sE)(i)}),[u,l,y,n,i]);return{isRequired:!!r,isInvalid:!!n,isReadOnly:!!i,isDisabled:!!l,isFocused:!!y,onFocus:()=>g(!0),onBlur:()=>g(!1),hasFeedbackText:h,setHasFeedbackText:x,hasHelpText:v,setHasHelpText:b,id:d,labelId:p,feedbackId:m,helpTextId:f,htmlProps:u,getHelpTextProps:_,getErrorMessageProps:w,getRootProps:j,getLabelProps:k,getRequiredIndicatorProps:(0,s.useCallback)((e={},t=null)=>({...e,ref:t,role:"presentation","aria-hidden":!0,children:e.children||"*"}),[])}}((0,l.MN)(e)),h=(0,o.cx)("chakra-form-control",e.className);return(0,n.jsx)(m,{value:f,children:(0,n.jsx)(d,{value:r,children:(0,n.jsx)(c.B.div,{...i({},t),className:h,__css:r.container})})})});h.displayName="FormControl";let x=(0,i.R)(function(e,t){let r=f(),a=p(),l=(0,o.cx)("chakra-form__helper-text",e.className);return(0,n.jsx)(c.B.div,{...r?.getHelpTextProps(e,t),__css:a.helperText,className:l})});x.displayName="FormHelperText"},32338:(e,t,r)=>{r.d(t,{d:()=>s});var n=r(94513),a=r(82824),l=r(84756),o=r(21533);let s=(0,l.R)((e,t)=>{let r=(0,a.k)();return(0,n.jsx)(o.B.thead,{...e,ref:t,__css:r.thead})})},35339:(e,t,r)=>{r.d(t,{l:()=>p});var n=r(94513),a=r(56005),l=r(79435),o=r(94285),s=r(84756),i=r(21533);let u=(0,s.R)(function(e,t){let{children:r,placeholder:a,className:o,...s}=e;return(0,n.jsxs)(i.B.select,{...s,ref:t,className:(0,l.cx)("chakra-select",o),children:[a&&(0,n.jsx)("option",{value:"",children:a}),r]})});u.displayName="SelectField";var c=r(97680),d=r(8475);let p=(0,s.R)((e,t)=>{let r=(0,d.o)("Select",e),{rootProps:o,placeholder:s,icon:p,color:m,height:f,h:x,minH:v,minHeight:b,iconColor:y,iconSize:g,..._}=(0,a.MN)(e),[k,w]=(0,l.lD)(_,a.GF),j=(0,c.t)(w),N={paddingEnd:"2rem",...r.field,_focus:{zIndex:"unset",...r.field?._focus}};return(0,n.jsxs)(i.B.div,{className:"chakra-select__wrapper",__css:{width:"100%",height:"fit-content",position:"relative",color:m},...k,...o,children:[(0,n.jsx)(u,{ref:t,height:x??f,minH:v??b,placeholder:s,...j,__css:N,children:e.children}),(0,n.jsx)(h,{"data-disabled":(0,l.sE)(j.disabled),...(y||m)&&{color:y||m},__css:r.icon,...g&&{fontSize:g},children:p})]})});p.displayName="Select";let m=e=>(0,n.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})}),f=(0,i.B)("div",{baseStyle:{position:"absolute",display:"inline-flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",top:"50%",transform:"translateY(-50%)"}}),h=e=>{let{children:t=(0,n.jsx)(m,{}),...r}=e,a=(0,o.cloneElement)(t,{role:"presentation",className:"chakra-select__icon",focusable:!1,"aria-hidden":!0,style:{width:"1em",height:"1em",color:"currentColor"}});return(0,n.jsx)(f,{...r,className:"chakra-select__icon-wrapper",children:(0,o.isValidElement)(t)?a:null})};h.displayName="SelectIcon"},35624:(e,t,r)=>{r.d(t,{Sh:()=>C,Q0:()=>S,Q7:()=>k,OO:()=>j,lw:()=>w});var n=r(94513),a=r(56005),l=r(79435),o=r(94285),s=r(29484);let i=e=>(0,n.jsx)(s.I,{viewBox:"0 0 24 24",...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M21,5H3C2.621,5,2.275,5.214,2.105,5.553C1.937,5.892,1.973,6.297,2.2,6.6l9,12 c0.188,0.252,0.485,0.4,0.8,0.4s0.611-0.148,0.8-0.4l9-12c0.228-0.303,0.264-0.708,0.095-1.047C21.725,5.214,21.379,5,21,5z"})}),u=e=>(0,n.jsx)(s.I,{viewBox:"0 0 24 24",...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M12.8,5.4c-0.377-0.504-1.223-0.504-1.6,0l-9,12c-0.228,0.303-0.264,0.708-0.095,1.047 C2.275,18.786,2.621,19,3,19h18c0.379,0,0.725-0.214,0.895-0.553c0.169-0.339,0.133-0.744-0.095-1.047L12.8,5.4z"})});var c=r(56084);function d(e,t,r,n){(0,o.useEffect)(()=>{if(!e.current||!n)return;let a=e.current.ownerDocument.defaultView??window,l=Array.isArray(t)?t:[t],o=new a.MutationObserver(e=>{for(let t of e)"attributes"===t.type&&t.attributeName&&l.includes(t.attributeName)&&r(t)});return o.observe(e.current,{attributes:!0,attributeFilter:l}),()=>o.disconnect()})}let p=/^[Ee0-9+\-.]$/;function m(e){return p.test(e)}var f=r(97680),h=r(84756),x=r(21533),v=r(8475);let[b,y]=(0,l.q6)({name:"NumberInputStylesContext",errorMessage:"useNumberInputStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<NumberInput />\" "}),[g,_]=(0,l.q6)({name:"NumberInputContext",errorMessage:"useNumberInputContext: `context` is undefined. Seems you forgot to wrap number-input's components within <NumberInput />"}),k=(0,h.R)(function(e,t){let r=(0,v.o)("NumberInput",e),s=(0,a.MN)(e),{htmlProps:i,...u}=function(e={}){let{focusInputOnChange:t=!0,clampValueOnBlur:r=!0,keepWithinRange:n=!0,min:a=Number.MIN_SAFE_INTEGER,max:s=Number.MAX_SAFE_INTEGER,step:i=1,isReadOnly:u,isDisabled:p,isRequired:f,isInvalid:h,pattern:x="[0-9]*(.[0-9]+)?",inputMode:v="decimal",allowMouseWheel:b,id:y,onChange:g,precision:_,name:k,"aria-describedby":w,"aria-label":j,"aria-labelledby":N,onFocus:C,onBlur:S,onInvalid:R,getAriaValueText:E,isValidCharacter:I,format:M,parse:B,...P}=e,T=(0,c.c9)(C),O=(0,c.c9)(S),A=(0,c.c9)(R),D=(0,c.c9)(I??m),F=(0,c.c9)(E),H=(0,c.I5)(e),{update:L,increment:q,decrement:z}=H,[$,W]=(0,o.useState)(!1),U=!(u||p),K=(0,o.useRef)(null),V=(0,o.useRef)(null),G=(0,o.useRef)(null),J=(0,o.useRef)(null),Q=(0,o.useCallback)(e=>e.split("").filter(D).join(""),[D]),Y=(0,o.useCallback)(e=>B?.(e)??e,[B]),Z=(0,o.useCallback)(e=>(M?.(e)??e).toString(),[M]);(0,c.w5)(()=>{H.valueAsNumber>s?A?.("rangeOverflow",Z(H.value),H.valueAsNumber):H.valueAsNumber<a&&A?.("rangeOverflow",Z(H.value),H.valueAsNumber)},[H.valueAsNumber,H.value,Z,A]),(0,c.UQ)(()=>{if(K.current&&K.current.value!=H.value){let e=Y(K.current.value);H.setValue(Q(e))}},[Y,Q]);let X=(0,o.useCallback)((e=i)=>{U&&q(e)},[q,U,i]),ee=(0,o.useCallback)((e=i)=>{U&&z(e)},[z,U,i]),et=function(e,t){let[r,n]=(0,o.useState)(!1),[a,l]=(0,o.useState)(null),[s,i]=(0,o.useState)(!0),u=(0,o.useRef)(null),d=()=>clearTimeout(u.current);(0,c.$$)(()=>{"increment"===a&&e(),"decrement"===a&&t()},r?50:null);let p=(0,o.useCallback)(()=>{s&&e(),u.current=setTimeout(()=>{i(!1),n(!0),l("increment")},300)},[e,s]),m=(0,o.useCallback)(()=>{s&&t(),u.current=setTimeout(()=>{i(!1),n(!0),l("decrement")},300)},[t,s]),f=(0,o.useCallback)(()=>{i(!0),n(!1),d()},[]);return(0,o.useEffect)(()=>()=>d(),[]),{up:p,down:m,stop:f,isSpinning:r}}(X,ee);d(G,"disabled",et.stop,et.isSpinning),d(J,"disabled",et.stop,et.isSpinning);let er=(0,o.useCallback)(e=>{e.nativeEvent.isComposing||(L(Q(Y(e.currentTarget.value))),V.current={start:e.currentTarget.selectionStart,end:e.currentTarget.selectionEnd})},[L,Q,Y]),en=(0,o.useCallback)(e=>{T?.(e),V.current&&(e.currentTarget.selectionStart=V.current.start??e.currentTarget.value?.length,e.currentTarget.selectionEnd=V.current.end??e.currentTarget.selectionStart)},[T]),ea=(0,o.useCallback)(e=>{if(e.nativeEvent.isComposing)return;!function(e,t){if(null==e.key)return!0;let r=e.ctrlKey||e.altKey||e.metaKey;return 1!==e.key.length||!!r||t(e.key)}(e,D)&&e.preventDefault();let t=el(e)*i,r={ArrowUp:()=>X(t),ArrowDown:()=>ee(t),Home:()=>L(a),End:()=>L(s)}[e.key];r&&(e.preventDefault(),r(e))},[D,i,X,ee,L,a,s]),el=e=>{let t=1;return(e.metaKey||e.ctrlKey)&&(t=.1),e.shiftKey&&(t=10),t},eo=(0,o.useMemo)(()=>{let e=F?.(H.value);return null!=e?e:H.value.toString()||void 0},[H.value,F]),es=(0,o.useCallback)(()=>{let e=H.value;""!==H.value&&(/^[eE]/.test(H.value.toString())?H.setValue(""):(H.valueAsNumber<a&&(e=a),H.valueAsNumber>s&&(e=s),H.cast(e)))},[H,s,a]),ei=(0,o.useCallback)(()=>{W(!1),r&&es()},[r,W,es]),eu=(0,o.useCallback)(()=>{t&&requestAnimationFrame(()=>{K.current?.focus()})},[t]),ec=(0,o.useCallback)(e=>{e.preventDefault(),et.up(),eu()},[eu,et]),ed=(0,o.useCallback)(e=>{e.preventDefault(),et.down(),eu()},[eu,et]);(0,c.ML)(()=>K.current,"wheel",e=>{let t=(K.current?.ownerDocument??document).activeElement===K.current;if(!b||!t)return;e.preventDefault();let r=el(e)*i,n=Math.sign(e.deltaY);-1===n?X(r):1===n&&ee(r)},{passive:!1});let ep=(0,o.useCallback)((e={},t=null)=>{let r=p||n&&H.isAtMax;return{...e,ref:(0,c.Px)(t,G),role:"button",tabIndex:-1,onPointerDown:(0,l.Hj)(e.onPointerDown,e=>{0!==e.button||r||ec(e)}),onPointerLeave:(0,l.Hj)(e.onPointerLeave,et.stop),onPointerUp:(0,l.Hj)(e.onPointerUp,et.stop),disabled:r,"aria-disabled":(0,l.rq)(r)}},[H.isAtMax,n,ec,et.stop,p]),em=(0,o.useCallback)((e={},t=null)=>{let r=p||n&&H.isAtMin;return{...e,ref:(0,c.Px)(t,J),role:"button",tabIndex:-1,onPointerDown:(0,l.Hj)(e.onPointerDown,e=>{0!==e.button||r||ed(e)}),onPointerLeave:(0,l.Hj)(e.onPointerLeave,et.stop),onPointerUp:(0,l.Hj)(e.onPointerUp,et.stop),disabled:r,"aria-disabled":(0,l.rq)(r)}},[H.isAtMin,n,ed,et.stop,p]),ef=(0,o.useCallback)((e={},t=null)=>({name:k,inputMode:v,type:"text",pattern:x,"aria-labelledby":N,"aria-label":j,"aria-describedby":w,id:y,disabled:p,role:"spinbutton",...e,readOnly:e.readOnly??u,"aria-readonly":e.readOnly??u,"aria-required":e.required??f,required:e.required??f,ref:(0,c.Px)(K,t),value:Z(H.value),"aria-valuemin":a,"aria-valuemax":s,"aria-valuenow":Number.isNaN(H.valueAsNumber)?void 0:H.valueAsNumber,"aria-invalid":(0,l.rq)(h??H.isOutOfRange),"aria-valuetext":eo,autoComplete:"off",autoCorrect:"off",onChange:(0,l.Hj)(e.onChange,er),onKeyDown:(0,l.Hj)(e.onKeyDown,ea),onFocus:(0,l.Hj)(e.onFocus,en,()=>W(!0)),onBlur:(0,l.Hj)(e.onBlur,O,ei)}),[k,v,x,N,j,Z,w,y,p,f,u,h,H.value,H.valueAsNumber,H.isOutOfRange,a,s,eo,er,ea,en,O,ei]);return{value:Z(H.value),valueAsNumber:H.valueAsNumber,isFocused:$,isDisabled:p,isReadOnly:u,getIncrementButtonProps:ep,getDecrementButtonProps:em,getInputProps:ef,htmlProps:P}}((0,f.v)(s)),p=(0,o.useMemo)(()=>u,[u]);return(0,n.jsx)(g,{value:p,children:(0,n.jsx)(b,{value:r,children:(0,n.jsx)(x.B.div,{...i,ref:t,className:(0,l.cx)("chakra-numberinput",e.className),__css:{position:"relative",zIndex:0,...r.root}})})})});k.displayName="NumberInput";let w=(0,h.R)(function(e,t){let r=y();return(0,n.jsx)(x.B.div,{"aria-hidden":!0,ref:t,...e,__css:{display:"flex",flexDirection:"column",position:"absolute",top:"0",insetEnd:"0px",margin:"1px",height:"calc(100% - 2px)",zIndex:1,...r.stepperGroup}})});w.displayName="NumberInputStepper";let j=(0,h.R)(function(e,t){let{getInputProps:r}=_(),a=r(e,t),o=y();return(0,n.jsx)(x.B.input,{...a,className:(0,l.cx)("chakra-numberinput__field",e.className),__css:{width:"100%",...o.field}})});j.displayName="NumberInputField";let N=(0,x.B)("div",{baseStyle:{display:"flex",justifyContent:"center",alignItems:"center",flex:1,transitionProperty:"common",transitionDuration:"normal",userSelect:"none",cursor:"pointer",lineHeight:"normal"}}),C=(0,h.R)(function(e,t){let r=y(),{getDecrementButtonProps:a}=_(),l=a(e,t);return(0,n.jsx)(N,{...l,__css:r.stepper,children:e.children??(0,n.jsx)(i,{})})});C.displayName="NumberDecrementStepper";let S=(0,h.R)(function(e,t){let{getIncrementButtonProps:r}=_(),a=r(e,t),l=y();return(0,n.jsx)(N,{...a,__css:l.stepper,children:e.children??(0,n.jsx)(u,{})})});S.displayName="NumberIncrementStepper"},37846:(e,t,r)=>{r.d(t,{M:()=>p,Z:()=>d});var n=r(94513),a=r(56005),l=r(79435),o=r(94285),s=r(84756),i=r(8475),u=r(21533);let[c,d]=(0,l.q6)({name:"InputGroupStylesContext",errorMessage:"useInputGroupStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<InputGroup />\" "}),p=(0,s.R)(function(e,t){let r=(0,i.o)("Input",e),{children:s,className:d,...p}=(0,a.MN)(e),m=(0,l.cx)("chakra-input__group",d),f={},h=(0,l.ag)(s),x=r.field;h.forEach(e=>{r&&(x&&"InputLeftElement"===e.type.id&&(f.paddingStart=x.height??x.h),x&&"InputRightElement"===e.type.id&&(f.paddingEnd=x.height??x.h),"InputRightAddon"===e.type.id&&(f.borderEndRadius=0),"InputLeftAddon"===e.type.id&&(f.borderStartRadius=0))});let v=h.map(t=>{let r=(0,l.oE)({size:t.props?.size||e.size,variant:t.props?.variant||e.variant});return"Input"!==t.type.id?(0,o.cloneElement)(t,r):(0,o.cloneElement)(t,Object.assign(r,f,t.props))});return(0,n.jsx)(u.B.div,{className:m,ref:t,__css:{width:"100%",display:"flex",position:"relative",isolation:"isolate",...r.group},"data-group":!0,...p,children:(0,n.jsx)(c,{value:r,children:v})})});p.displayName="InputGroup"},38047:(e,t,r)=>{r.d(t,{e:()=>i});var n=r(94513),a=r(79435),l=r(38822),o=r(84756),s=r(21533);let i=(0,o.R)(function(e,t){let{getBodyProps:r}=(0,l.C_)(),o=(0,l.jm)();return(0,n.jsx)(s.B.div,{...r(e,t),className:(0,a.cx)("chakra-popover__body",e.className),__css:o.body})});i.displayName="PopoverBody"},38822:(e,t,r)=>{r.d(t,{C_:()=>l,hA:()=>o,jm:()=>s,pb:()=>a});var n=r(79435);let[a,l]=(0,n.q6)({name:"PopoverContext",errorMessage:"usePopoverContext: `context` is undefined. Seems you forgot to wrap all popover components within `<Popover />`"}),[o,s]=(0,n.q6)({name:"PopoverStylesContext",errorMessage:"usePopoverStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Popover />\" "})},42398:(e,t,r)=>{r.d(t,{E:()=>c,r:()=>d});var n=r(94513),a=r(56005),l=r(79435),o=r(84756),s=r(8475),i=r(21533);let[u,c]=(0,l.q6)({name:"StatStylesContext",errorMessage:"useStatStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Stat />\" "}),d=(0,o.R)(function(e,t){let r=(0,s.o)("Stat",e),o={position:"relative",flex:"1 1 0%",...r.container},{className:c,children:d,...p}=(0,a.MN)(e);return(0,n.jsx)(u,{value:r,children:(0,n.jsx)(i.B.div,{ref:t,...p,className:(0,l.cx)("chakra-stat",c),__css:o,children:(0,n.jsx)("dl",{children:d})})})});d.displayName="Stat"},45604:(e,t,r)=>{r.d(t,{T:()=>l,w:()=>o});var n=r(94513);let[a,l]=(0,r(79435).q6)({strict:!1,name:"PortalManagerContext"});function o(e){let{children:t,zIndex:r}=e;return(0,n.jsx)(a,{value:{zIndex:r},children:t})}o.displayName="PortalManager"},45905:(e,t,r)=>{r.d(t,{Xu:()=>u,BV:()=>m,b:()=>x,db:()=>v,KZ:()=>f,Os:()=>_,jy:()=>y,F9:()=>g});var n=r(56084),a=r(79435),l=r(94285),o=r(55588),s=r(70646),i=r(73336);let[u,c,d,p]=(0,o.D)(),[m,f]=(0,a.q6)({strict:!1,name:"MenuContext"});function h(e){return e?.ownerDocument??document}function x(e={}){let{id:t,closeOnSelect:r=!0,closeOnBlur:a=!0,initialFocusRef:o,autoSelect:i=!0,isLazy:u,isOpen:c,defaultIsOpen:p,onClose:m,onOpen:f,placement:v="bottom-start",lazyBehavior:b="unmount",direction:y,computePositionOnMount:g=!1,..._}=e,k=(0,l.useRef)(null),w=(0,l.useRef)(null),j=(0,l.useRef)(!0),N=d(),C=(0,l.useCallback)(()=>{requestAnimationFrame(()=>{k.current?.focus({preventScroll:!1})})},[]),S=(0,l.useCallback)(()=>{let e=setTimeout(()=>{if(o)o.current?.focus();else if(N.count()){let e=N.firstEnabled();e&&A(e.index)}else k.current?.focus({preventScroll:!1})});q.current.add(e)},[N,o]),R=(0,l.useCallback)(()=>{let e=setTimeout(()=>{if(N.count()){let e=N.lastEnabled();e&&A(e.index)}else k.current?.focus({preventScroll:!1})});q.current.add(e)},[N]),E=(0,l.useCallback)(()=>{f?.(),i?S():C()},[i,S,C,f]),{isOpen:I,onOpen:M,onClose:B,onToggle:P}=(0,n.j1)({isOpen:c,defaultIsOpen:p,onClose:m,onOpen:E});(0,n.jz)({enabled:I&&a,ref:k,handler:e=>{let t=e.composedPath?.()?.[0]??e.target;w.current?.contains(t)||B()}});let T=(0,s.E)({..._,enabled:I||g,placement:v,direction:y}),[O,A]=(0,l.useState)(-1);(0,n.Xb)(k,{focusRef:w,visible:I,shouldFocus:!0});let D=(0,n.vG)({isOpen:I,ref:k}),[F,H]=(0,n.cC)(t,"menu-button","menu-list"),L=(0,l.useCallback)(()=>{M(),C()},[M,C]),q=(0,l.useRef)(new Set([]));(0,l.useEffect)(()=>{let e=q.current;return()=>{e.forEach(e=>clearTimeout(e)),e.clear()}},[]),(0,n.w5)(()=>{I||(A(-1),k.current?.scrollTo(0,0))},[I]),(0,n.w5)(()=>{I&&-1===O&&C()},[O,I]),(0,l.useEffect)(()=>{if(!I)return;let e=N.item(O);e?.node?.focus({preventScroll:!j.current})},[N,O,I]);let z=(0,l.useCallback)(()=>{M(),S()},[S,M]);return{openAndFocusMenu:L,openAndFocusFirstItem:z,openAndFocusLastItem:(0,l.useCallback)(()=>{j.current=!0,M(),R()},[M,R]),onTransitionEnd:(0,l.useCallback)(()=>{let e=h(k.current),t=k.current?.contains(e.activeElement);if(!(I&&!t))return;let r=N.item(O)?.node;r?.focus({preventScroll:!j.current})},[I,O,N]),unstable__animationState:D,descendants:N,popper:T,buttonId:F,menuId:H,forceUpdate:T.forceUpdate,orientation:"vertical",isOpen:I,onToggle:P,onOpen:M,onClose:B,menuRef:k,buttonRef:w,focusedIndex:O,closeOnSelect:r,closeOnBlur:a,autoSelect:i,setFocusedIndex:A,isLazy:u,lazyBehavior:b,initialFocusRef:o,scrollIntoViewRef:j}}function v(e={},t=null){let r=f(),{onToggle:o,popper:s,openAndFocusFirstItem:i,openAndFocusLastItem:u,scrollIntoViewRef:c}=r,d=(0,l.useCallback)(e=>{let t={Enter:i,ArrowDown:i,ArrowUp:u}[e.key];t&&(c.current=!0,e.preventDefault(),e.stopPropagation(),t(e))},[i,u,c]);return{...e,ref:(0,n.Px)(r.buttonRef,t,s.referenceRef),id:r.buttonId,"data-active":(0,a.sE)(r.isOpen),"aria-expanded":r.isOpen,"aria-haspopup":"menu","aria-controls":r.menuId,onClick:(0,a.Hj)(e.onClick,o),onKeyDown:(0,a.Hj)(e.onKeyDown,d)}}function b(e){return function(e){var t;if(!(null!=(t=e)&&"object"==typeof t&&"nodeType"in t&&t.nodeType===Node.ELEMENT_NODE))return!1;let r=e.ownerDocument.defaultView??window;return e instanceof r.HTMLElement}(e)&&!!e?.getAttribute("role")?.startsWith("menuitem")}function y(e={},t=null){let r=f();if(!r)throw Error("useMenuContext: context is undefined. Seems you forgot to wrap component within <Menu>");let{focusedIndex:o,setFocusedIndex:s,menuRef:i,isOpen:u,onClose:d,menuId:p,isLazy:m,lazyBehavior:h,scrollIntoViewRef:x,unstable__animationState:v}=r,g=c(),_=function(e={}){let{timeout:t=300,preventDefault:r=()=>!0}=e,[n,a]=(0,l.useState)([]),o=(0,l.useRef)(void 0),s=()=>{o.current&&(clearTimeout(o.current),o.current=null)},i=()=>{s(),o.current=setTimeout(()=>{a([]),o.current=null},t)};return(0,l.useEffect)(()=>s,[]),function(e){return t=>{if("Backspace"===t.key){let e=[...n];e.pop(),a(e);return}if(function(e){let{key:t}=e;return 1===t.length||t.length>1&&/[^a-zA-Z0-9]/.test(t)}(t)){let l=n.concat(t.key);r(t)&&(t.preventDefault(),t.stopPropagation()),a(l),e(l.join("")),i()}}}}({preventDefault:e=>" "!==e.key&&b(e.target)}),k=(0,l.useCallback)(e=>{if(!e.currentTarget.contains(e.target))return;let t={Tab:e=>e.preventDefault(),Escape:e=>{e.stopPropagation(),d()},ArrowDown:()=>{x.current=!0;let e=g.nextEnabled(o)??g.firstEnabled();e&&s(e.index)},ArrowUp:()=>{x.current=!0;let e=g.prevEnabled(o)??g.firstEnabled();e&&s(e.index)}}[e.key];if(t){e.preventDefault(),t(e);return}let r=_(e=>{let t=function(e,t,r,n){if(null==t)return n;if(!n)return e.find(e=>r(e).toLowerCase().startsWith(t.toLowerCase()));let a=e.filter(e=>r(e).toLowerCase().startsWith(t.toLowerCase()));if(a.length>0){let t;return a.includes(n)?((t=a.indexOf(n)+1)===a.length&&(t=0),a[t]):(t=e.indexOf(a[0]),e[t])}return n}(g.values(),e,e=>e?.node?.textContent??"",g.item(o));t&&s(g.indexOf(t.node))});b(e.target)&&r(e)},[g,o,_,d,s,x]),w=(0,l.useRef)(!1);u&&(w.current=!0);let j=(0,a.qJ)({wasSelected:w.current,enabled:m,mode:h,isSelected:v.present});return{...e,ref:(0,n.Px)(i,t),children:j?e.children:null,tabIndex:-1,role:"menu",id:p,style:{...e.style,transformOrigin:"var(--popper-transform-origin)"},"aria-orientation":"vertical",onKeyDown:(0,a.Hj)(e.onKeyDown,k)}}function g(e={}){let{popper:t,isOpen:r}=f();return t.getPopperProps({...e,style:{visibility:r?"visible":"hidden",...e.style}})}function _(e={},t=null){let{onMouseEnter:r,onMouseMove:a,onMouseLeave:o,onClick:s,onFocus:u,isDisabled:c,isFocusable:d,closeOnSelect:m,type:x,...v}=e,{setFocusedIndex:y,focusedIndex:g,closeOnSelect:k,onClose:w,menuId:j,scrollIntoViewRef:N}=f(),C=(0,l.useRef)(null),S=`${j}-menuitem-${(0,l.useId)()}`,{index:R,register:E}=p({disabled:c&&!d}),I=(0,l.useCallback)(e=>{r?.(e),c||(N.current=!1,y(R))},[y,R,c,r,N]),M=(0,l.useCallback)(e=>{var t;a?.(e),C.current&&h(t=C.current).activeElement!==t&&I(e)},[I,a]),B=(0,l.useCallback)(e=>{o?.(e),c||y(-1)},[y,c,o]),P=(0,l.useCallback)(e=>{s?.(e),b(e.currentTarget)&&(m??k)&&w()},[w,s,k,m]),T=(0,l.useCallback)(e=>{u?.(e),y(R)},[y,u,R]),O=R===g,A=(0,i.I)({onClick:P,onFocus:T,onMouseEnter:I,onMouseMove:M,onMouseLeave:B,ref:(0,n.Px)(E,C,t),isDisabled:c,isFocusable:d});return{...v,...A,type:x??A.type,id:S,role:"menuitem",tabIndex:O?0:-1}}},46312:(e,t,r)=>{r.d(t,{N:()=>s});var n=r(94513),a=r(82824),l=r(84756),o=r(21533);let s=(0,l.R)((e,t)=>{let r=(0,a.k)();return(0,n.jsx)(o.B.tbody,{...e,ref:t,__css:r.tbody})})},46949:(e,t,r)=>{r.d(t,{aF:()=>x,k3:()=>h,x5:()=>m});var n=r(94513),a=r(79435),l=r(5649),o=r(56084),s=r(15207),i=r(94285),u=r(93334),c=r(21954),d=r(8475);let[p,m]=(0,a.q6)({name:"ModalStylesContext",errorMessage:"useModalStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Modal />\" "}),[f,h]=(0,a.q6)({strict:!0,name:"ModalContext",errorMessage:"useModalContext: `context` is undefined. Seems you forgot to wrap modal components in `<Modal />`"}),x=e=>{let t={scrollBehavior:"outside",autoFocus:!0,trapFocus:!0,returnFocusOnClose:!0,blockScrollOnMount:!0,allowPinchZoom:!1,preserveScrollBarGap:!0,motionPreset:"scale",...e,lockFocusAcrossFrames:e.lockFocusAcrossFrames??!0},{portalProps:r,children:m,autoFocus:h,trapFocus:x,initialFocusRef:v,finalFocusRef:b,returnFocusOnClose:y,blockScrollOnMount:g,allowPinchZoom:_,preserveScrollBarGap:k,motionPreset:w,lockFocusAcrossFrames:j,animatePresenceProps:N,onCloseComplete:C}=t,S=(0,d.o)("Modal",t),R={...function(e){let{isOpen:t,onClose:r,id:n,closeOnOverlayClick:l=!0,closeOnEsc:c=!0,useInert:d=!0,onOverlayClick:p,onEsc:m}=e,f=(0,i.useRef)(null),h=(0,i.useRef)(null),[x,v,b]=function(e,...t){let r=(0,i.useId)(),n=e||r;return(0,i.useMemo)(()=>t.map(e=>`${e}-${n}`),[n,t])}(n,"chakra-modal","chakra-modal--header","chakra-modal--body");var y=f,g=t&&d;let _=y.current;(0,i.useEffect)(()=>{if(y.current&&g)return(0,s.Eq)(y.current)},[g,y,_]);let k=(0,u.y)(f,t),w=(0,i.useRef)(null),j=(0,i.useCallback)(e=>{w.current=e.target},[]),N=(0,i.useCallback)(e=>{"Escape"===e.key&&(e.stopPropagation(),c&&r?.(),m?.())},[c,r,m]),[C,S]=(0,i.useState)(!1),[R,E]=(0,i.useState)(!1),I=(0,i.useCallback)((e={},t=null)=>({role:"dialog",...e,ref:(0,o.Px)(t,f),id:x,tabIndex:-1,"aria-modal":!0,"aria-labelledby":C?v:void 0,"aria-describedby":R?b:void 0,onClick:(0,a.Hj)(e.onClick,e=>e.stopPropagation())}),[b,R,x,v,C]),M=(0,i.useCallback)(e=>{e.stopPropagation(),w.current===e.target&&u.J.isTopModal(f.current)&&(l&&r?.(),p?.())},[r,l,p]),B=(0,i.useCallback)((e={},t=null)=>({...e,ref:(0,o.Px)(t,h),onClick:(0,a.Hj)(e.onClick,M),onKeyDown:(0,a.Hj)(e.onKeyDown,N),onMouseDown:(0,a.Hj)(e.onMouseDown,j)}),[N,j,M]);return{isOpen:t,onClose:r,headerId:v,bodyId:b,setBodyMounted:E,setHeaderMounted:S,dialogRef:f,overlayRef:h,getDialogProps:I,getDialogContainerProps:B,index:k}}(t),autoFocus:h,trapFocus:x,initialFocusRef:v,finalFocusRef:b,returnFocusOnClose:y,blockScrollOnMount:g,allowPinchZoom:_,preserveScrollBarGap:k,motionPreset:w,lockFocusAcrossFrames:j};return(0,n.jsx)(f,{value:R,children:(0,n.jsx)(p,{value:S,children:(0,n.jsx)(l.Nyo,{...N,onExitComplete:C,children:R.isOpen&&(0,n.jsx)(c.Z,{...r,children:m})})})})};x.displayName="Modal"},49892:(e,t,r)=>{r.d(t,{s:()=>o});var n=r(94513),a=r(84756),l=r(21533);let o=(0,a.R)(function(e,t){let{direction:r,align:a,justify:o,wrap:s,basis:i,grow:u,shrink:c,...d}=e;return(0,n.jsx)(l.B.div,{ref:t,__css:{display:"flex",flexDirection:r,alignItems:a,justifyContent:o,flexWrap:s,flexBasis:i,flexGrow:u,flexShrink:c},...d})});o.displayName="Flex"},52156:(e,t,r)=>{r.d(t,{$:()=>d,W:()=>p});var n=r(94513),a=r(56005),l=r(79435),o=r(94285),s=r(45905),i=r(9982),u=r(8475);let[c,d]=(0,l.q6)({name:"MenuStylesContext",errorMessage:"useMenuStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Menu />\" "}),p=e=>{let{children:t}=e,r=(0,u.o)("Menu",e),d=(0,a.MN)(e),{direction:p}=(0,i.D)(),{descendants:m,...f}=(0,s.b)({...d,direction:p}),h=(0,o.useMemo)(()=>f,[f]),{isOpen:x,onClose:v,forceUpdate:b}=h;return(0,n.jsx)(s.Xu,{value:m,children:(0,n.jsx)(s.BV,{value:h,children:(0,n.jsx)(c,{value:r,children:(0,l.Jg)(t,{isOpen:x,onClose:v,forceUpdate:b})})})})};p.displayName="Menu"},52545:(e,t,r)=>{r.d(t,{k:()=>h});var n=r(94513),a=r(56005),l=r(79435),o=r(83541);(0,o.i7)({"0%":{strokeDasharray:"1, 400",strokeDashoffset:"0"},"50%":{strokeDasharray:"400, 400",strokeDashoffset:"-100"},"100%":{strokeDasharray:"400, 400",strokeDashoffset:"-260"}}),(0,o.i7)({"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}});let s=(0,o.i7)({"0%":{left:"-40%"},"100%":{left:"100%"}}),i=(0,o.i7)({from:{backgroundPosition:"1rem 0"},to:{backgroundPosition:"0 0"}});var u=r(84756),c=r(21533),d=r(8475);let[p,m]=(0,l.q6)({name:"ProgressStylesContext",errorMessage:"useProgressStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Progress />\" "}),f=(0,u.R)((e,t)=>{let{min:r,max:a,value:l,isIndeterminate:o,role:s,...i}=e,u=function(e){let{value:t=0,min:r,max:n,valueText:a,getValueText:l,isIndeterminate:o,role:s="progressbar"}=e,i=(t-r)*100/(n-r);return{bind:{"data-indeterminate":o?"":void 0,"aria-valuemax":n,"aria-valuemin":r,"aria-valuenow":o?void 0:t,"aria-valuetext":(()=>{if(null!=t)return"function"==typeof l?l(t,i):a})(),role:s},percent:i,value:t}}({value:l,min:r,max:a,isIndeterminate:o,role:s}),d={height:"100%",...m().filledTrack};return(0,n.jsx)(c.B.div,{ref:t,style:{width:`${u.percent}%`,...i.style},...u.bind,...i,__css:d})}),h=(0,u.R)((e,t)=>{let{value:r,min:l=0,max:o=100,hasStripe:u,isAnimated:m,children:h,borderRadius:x,isIndeterminate:v,"aria-label":b,"aria-labelledby":y,"aria-valuetext":g,title:_,role:k,...w}=(0,a.MN)(e),j=(0,d.o)("Progress",e),N=x??j.track?.borderRadius,C={animation:`${i} 1s linear infinite`},S={...!v&&u&&m&&C,...v&&{position:"absolute",willChange:"left",minWidth:"50%",animation:`${s} 1s ease infinite normal none running`}},R={overflow:"hidden",position:"relative",...j.track};return(0,n.jsx)(c.B.div,{ref:t,borderRadius:N,__css:R,...w,children:(0,n.jsxs)(p,{value:j,children:[(0,n.jsx)(f,{"aria-label":b,"aria-labelledby":y,"aria-valuetext":g,min:l,max:o,value:r,isIndeterminate:v,css:S,borderRadius:N,title:_,role:k}),h]})})});h.displayName="Progress"},53083:(e,t,r)=>{r.d(t,{$:()=>k});var n=r(94513),a=r(56005),l=r(79435),o=r(46949),s=r(5649),i=r(94285),u=r(20282),c=r(93334),d=r(48981);let p=d.Ay.default??d.Ay,m=e=>{let{initialFocusRef:t,finalFocusRef:r,contentRef:a,restoreFocus:o,children:s,isDisabled:u,autoFocus:c,persistentFocus:d,lockFocusAcrossFrames:m}=e,f=(0,i.useCallback)(()=>{t?.current?t.current.focus():a?.current&&0===(0,l.ep)(a.current).length&&requestAnimationFrame(()=>{a.current?.focus()})},[t,a]),h=(0,i.useCallback)(()=>{r?.current?.focus()},[r]),x=o&&!r;return(0,n.jsx)(p,{crossFrame:m,persistentFocus:d,autoFocus:c,disabled:u,onActivation:f,onDeactivation:h,returnFocus:x,children:s})};function f(e){let{autoFocus:t,trapFocus:r,dialogRef:a,initialFocusRef:l,blockScrollOnMount:d,allowPinchZoom:p,finalFocusRef:f,returnFocusOnClose:h,preserveScrollBarGap:x,lockFocusAcrossFrames:v,isOpen:b}=(0,o.k3)(),[y,g]=(0,s.xQ_)();(0,i.useEffect)(()=>{!y&&g&&setTimeout(g)},[y,g]);let _=(0,c.y)(a,b);return(0,n.jsx)(m,{autoFocus:t,isDisabled:!r,initialFocusRef:l,finalFocusRef:f,restoreFocus:h,contentRef:a,lockFocusAcrossFrames:v,children:(0,n.jsx)(u.G,{removeScrollBar:!x,allowPinchZoom:p,enabled:1===_&&d,forwardProps:!0,children:e.children})})}m.displayName="FocusLock";var h=r(37911),x=r(67252),v=r(21533);let b={slideInBottom:{...h.w,custom:{offsetY:16,reverse:!0}},slideInRight:{...h.w,custom:{offsetX:16,reverse:!0}},slideInTop:{...h.w,custom:{offsetY:-16,reverse:!0}},slideInLeft:{...h.w,custom:{offsetX:-16,reverse:!0}},scale:{...x.T,custom:{initialScale:.95,reverse:!0}},none:{}},y=(0,v.B)(s.PY1.section),g=e=>b[e||"none"],_=(0,i.forwardRef)((e,t)=>{let{preset:r,motionProps:a=g(r),...l}=e;return(0,n.jsx)(y,{ref:t,...a,...l})});_.displayName="ModalTransition";let k=(0,r(84756).R)((e,t)=>{let{className:r,children:s,containerProps:i,motionProps:u,...c}=e,{getDialogProps:d,getDialogContainerProps:p}=(0,o.k3)(),m=d(c,t),h=p(i),x=(0,l.cx)("chakra-modal__content",r),b=(0,o.x5)(),y=(0,a.H2)({display:"flex",flexDirection:"column",position:"relative",width:"100%",outline:0,...b.dialog}),g=(0,a.H2)({display:"flex",width:"100vw",height:"$100vh",position:"fixed",left:0,top:0,...b.dialogContainer}),{motionPreset:k}=(0,o.k3)();return(0,n.jsx)(f,{children:(0,n.jsx)(v.B.div,{...h,className:"chakra-modal__content-container",tabIndex:-1,__css:g,children:(0,n.jsx)(_,{preset:k,motionProps:u,className:x,...m,__css:y,children:s})})})});k.displayName="ModalContent"},55206:(e,t,r)=>{r.d(t,{d:()=>d});var n=r(94513),a=r(56005),l=r(79435),o=r(94285),s=r(63006),i=r(84756),u=r(8475),c=r(21533);let d=(0,i.R)(function(e,t){let r=(0,u.o)("Switch",e),{spacing:i="0.5rem",children:d,...p}=(0,a.MN)(e),{getIndicatorProps:m,getInputProps:f,getCheckboxProps:h,getRootProps:x,getLabelProps:v}=(0,s.v)(p),b=(0,o.useMemo)(()=>({display:"inline-block",position:"relative",verticalAlign:"middle",lineHeight:0,...r.container}),[r.container]),y=(0,o.useMemo)(()=>({display:"inline-flex",flexShrink:0,justifyContent:"flex-start",boxSizing:"content-box",cursor:"pointer",...r.track}),[r.track]),g=(0,o.useMemo)(()=>({userSelect:"none",marginStart:i,...r.label}),[i,r.label]);return(0,n.jsxs)(c.B.label,{...x(),className:(0,l.cx)("chakra-switch",e.className),__css:b,children:[(0,n.jsx)("input",{className:"chakra-switch__input",...f({},t)}),(0,n.jsx)(c.B.span,{...h(),className:"chakra-switch__track",__css:y,children:(0,n.jsx)(c.B.span,{__css:r.thumb,className:"chakra-switch__thumb",...m()})}),d&&(0,n.jsx)(c.B.span,{className:"chakra-switch__label",...v(),__css:g,children:d})]})});d.displayName="Switch"},56340:(e,t,r)=>{r.d(t,{Q:()=>d});var n=r(94513),a=r(21966),l=r(16128),o=r(93681),s=r(45604),i=r(13596);let u=e=>{let{children:t,colorModeManager:r,portalZIndex:u,resetScope:c,resetCSS:d=!0,theme:p={},environment:m,cssVarsRoot:f,disableEnvironment:h,disableGlobalStyle:x}=e,v=(0,n.jsx)(i.v,{environment:m,disabled:h,children:t});return(0,n.jsx)(o.NP,{theme:p,cssVarsRoot:f,children:(0,n.jsxs)(a.an,{colorModeManager:r,options:p.config,children:[d?(0,n.jsx)(l.r,{scope:c}):(0,n.jsx)(l.R,{}),!x&&(0,n.jsx)(o.zy,{}),u?(0,n.jsx)(s.w,{zIndex:u,children:v}):v]})})};var c=r(65151);let d=e=>function({children:t,theme:r=e,toastOptions:a,...l}){return(0,n.jsxs)(u,{theme:r,...l,children:[(0,n.jsx)(c.ym,{value:a?.defaultOptions,children:t}),(0,n.jsx)(c.tE,{...a})]})}},56858:(e,t,r)=>{r.d(t,{e:()=>p,t:()=>m});var n=r(94513),a=r(56005),l=r(79435),o=r(94285),s=r(87096),i=r(84756),u=r(8475),c=r(21533);let[d,p]=(0,l.q6)({name:"TabsStylesContext",errorMessage:"useTabsStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Tabs />\" "}),m=(0,i.R)(function(e,t){let r=(0,u.o)("Tabs",e),{children:i,className:p,...m}=(0,a.MN)(e),{htmlProps:f,descendants:h,...x}=(0,s.uc)(m),v=(0,o.useMemo)(()=>x,[x]),{isFitted:b,...y}=f,g={position:"relative",...r.root};return(0,n.jsx)(s.at,{value:h,children:(0,n.jsx)(s.O_,{value:v,children:(0,n.jsx)(d,{value:r,children:(0,n.jsx)(c.B.div,{className:(0,l.cx)("chakra-tabs",p),ref:t,...y,__css:g,children:i})})})})});m.displayName="Tabs"},57688:(e,t,r)=>{r.d(t,{K:()=>u});var n=r(94513),a=r(79435),l=r(56858),o=r(87096),s=r(84756),i=r(21533);let u=(0,s.R)(function(e,t){let r=(0,o.Jn)({...e,ref:t}),s=(0,l.e)();return(0,n.jsx)(i.B.div,{outline:"0",...r,className:(0,a.cx)("chakra-tabs__tab-panel",e.className),__css:s.tabpanel})});u.displayName="TabPanel"},59220:(e,t,r)=>{r.d(t,{p:()=>c});var n=r(94513),a=r(56005),l=r(79435),o=r(97680),s=r(84756),i=r(8475),u=r(21533);let c=(0,s.R)(function(e,t){let{htmlSize:r,...s}=e,c=(0,i.o)("Input",s),d=(0,a.MN)(s),p=(0,o.t)(d),m=(0,l.cx)("chakra-input",e.className);return(0,n.jsx)(u.B.input,{size:r,...p,__css:c.field,ref:t,className:m})});c.displayName="Input",c.id="Input"},63678:(e,t,r)=>{r.d(t,{h:()=>m});var n=r(94513),a=r(56005),l=r(79435),o=r(38822),s=r(5649),i=r(21533),u=r(84756);let c={exit:{opacity:0,scale:.95,transition:{duration:.1,ease:[.4,0,1,1]}},enter:{scale:1,opacity:1,transition:{duration:.15,ease:[0,0,.2,1]}}},d=(0,i.B)(s.PY1.section),p=(0,u.R)(function(e,t){let{variants:r=c,...a}=e,{isOpen:l}=(0,o.C_)();return(0,n.jsx)(d,{ref:t,variants:function(e){if(e)return{enter:{...e.enter,visibility:"visible"},exit:{...e.exit,transitionEnd:{visibility:"hidden"}}}}(r),initial:!1,animate:l?"enter":"exit",...a})});p.displayName="PopoverTransition";let m=(0,u.R)(function(e,t){let{rootProps:r,motionProps:s,...u}=e,{getPopoverProps:c,getPopoverPositionerProps:d,onAnimationComplete:m}=(0,o.C_)(),f=(0,o.jm)(),h=(0,a.H2)({position:"relative",display:"flex",flexDirection:"column",...f.content});return(0,n.jsx)(i.B.div,{...d(r),__css:f.popper,className:"chakra-popover__popper",children:(0,n.jsx)(p,{...s,...c(u,t),onAnimationComplete:(0,l.OK)(m,u.onAnimationComplete),className:(0,l.cx)("chakra-popover__content",e.className),__css:h})})});m.displayName="PopoverContent"},63957:(e,t,r)=>{r.d(t,{G6:()=>d});var n=r(94513),a=r(79435),l=r(37846),o=r(21533),s=r(84756);let i={left:{marginEnd:"-1px",borderEndRadius:0,borderEndColor:"transparent"},right:{marginStart:"-1px",borderStartRadius:0,borderStartColor:"transparent"}},u=(0,o.B)("div",{baseStyle:{flex:"0 0 auto",width:"auto",display:"flex",alignItems:"center",whiteSpace:"nowrap"}}),c=(0,s.R)(function(e,t){let{placement:r="left",...a}=e,o=i[r]??{},s=(0,l.Z)();return(0,n.jsx)(u,{ref:t,...a,__css:{...s.addon,...o}})});c.displayName="InputAddon";let d=(0,s.R)(function(e,t){return(0,n.jsx)(c,{ref:t,placement:"left",...e,className:(0,a.cx)("chakra-input__left-addon",e.className)})});d.displayName="InputLeftAddon",d.id="InputLeftAddon";let p=(0,s.R)(function(e,t){return(0,n.jsx)(c,{ref:t,placement:"right",...e,className:(0,a.cx)("chakra-input__right-addon",e.className)})});p.displayName="InputRightAddon",p.id="InputRightAddon"},67866:(e,t,r)=>{r.d(t,{h:()=>i});var n=r(94513),a=r(79435),l=r(42398),o=r(84756),s=r(21533);let i=(0,o.R)(function(e,t){let r=(0,l.E)();return(0,n.jsx)(s.B.dd,{ref:t,...e,className:(0,a.cx)("chakra-stat__help-text",e.className),__css:r.helpText})});i.displayName="StatHelpText"},70646:(e,t,r)=>{r.d(t,{E:()=>v});var n=r(56084),a=r(68688),l=r(94285),o=r(27225);let s={name:"matchWidth",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:({state:e})=>{e.styles.popper.width=`${e.rects.reference.width}px`},effect:({state:e})=>()=>{let t=e.elements.reference;e.elements.popper.style.width=`${t.offsetWidth}px`}},i={name:"transformOrigin",enabled:!0,phase:"write",fn:({state:e})=>{u(e)},effect:({state:e})=>()=>{u(e)}},u=e=>{e.elements.popper.style.setProperty(o.O3.transformOrigin.var,(0,o.ed)(e.placement))},c={name:"positionArrow",enabled:!0,phase:"afterWrite",fn:({state:e})=>{d(e)}},d=e=>{if(!e.placement)return;let t=p(e.placement);if(e.elements?.arrow&&t){Object.assign(e.elements.arrow.style,{[t.property]:t.value,width:o.O3.arrowSize.varRef,height:o.O3.arrowSize.varRef,zIndex:-1});let r={[o.O3.arrowSizeHalf.var]:`calc(${o.O3.arrowSize.varRef} / 2 - 1px)`,[o.O3.arrowOffset.var]:`calc(${o.O3.arrowSizeHalf.varRef} * -1)`};for(let t in r)e.elements.arrow.style.setProperty(t,r[t])}},p=e=>e.startsWith("top")?{property:"bottom",value:o.O3.arrowOffset.varRef}:e.startsWith("bottom")?{property:"top",value:o.O3.arrowOffset.varRef}:e.startsWith("left")?{property:"right",value:o.O3.arrowOffset.varRef}:e.startsWith("right")?{property:"left",value:o.O3.arrowOffset.varRef}:void 0,m={name:"innerArrow",enabled:!0,phase:"main",requires:["arrow"],fn:({state:e})=>{f(e)},effect:({state:e})=>()=>{f(e)}},f=e=>{if(!e.elements.arrow)return;let t=e.elements.arrow.querySelector("[data-popper-arrow-inner]");if(!t)return;let r=(0,o.Ky)(e.placement);r&&t.style.setProperty("--popper-arrow-default-shadow",r),Object.assign(t.style,{transform:"rotate(45deg)",background:o.O3.arrowBg.varRef,top:0,left:0,width:"100%",height:"100%",position:"absolute",zIndex:"inherit",boxShadow:"var(--popper-arrow-shadow, var(--popper-arrow-default-shadow))"})},h={"start-start":{ltr:"left-start",rtl:"right-start"},"start-end":{ltr:"left-end",rtl:"right-end"},"end-start":{ltr:"right-start",rtl:"left-start"},"end-end":{ltr:"right-end",rtl:"left-end"},start:{ltr:"left",rtl:"right"},end:{ltr:"right",rtl:"left"}},x={"auto-start":"auto-end","auto-end":"auto-start","top-start":"top-end","top-end":"top-start","bottom-start":"bottom-end","bottom-end":"bottom-start"};function v(e={}){let{enabled:t=!0,modifiers:r,placement:u="bottom",strategy:d="absolute",arrowPadding:p=8,eventListeners:f=!0,offset:b,gutter:y=8,flip:g=!0,boundary:_="clippingParents",preventOverflow:k=!0,matchWidth:w,direction:j="ltr"}=e,N=(0,l.useRef)(null),C=(0,l.useRef)(null),S=(0,l.useRef)(null),R=function(e,t="ltr"){let r=h[e]?.[t]||e;return"ltr"===t?r:x[e]??r}(u,j),E=(0,l.useRef)(()=>{}),I=(0,l.useCallback)(()=>{t&&N.current&&C.current&&(E.current?.(),S.current=(0,a.n4)(N.current,C.current,{placement:R,modifiers:[m,c,i,{...s,enabled:!!w},{name:"eventListeners",...(0,o.Fs)(f)},{name:"arrow",options:{padding:p}},{name:"offset",options:{offset:b??[0,y]}},{name:"flip",enabled:!!g,options:{padding:8}},{name:"preventOverflow",enabled:!!k,options:{boundary:_}},...r??[]],strategy:d}),S.current.forceUpdate(),E.current=S.current.destroy)},[R,t,r,w,f,p,b,y,g,k,_,d]);(0,l.useEffect)(()=>()=>{N.current||C.current||(S.current?.destroy(),S.current=null)},[]);let M=(0,l.useCallback)(e=>{N.current=e,I()},[I]),B=(0,l.useCallback)((e={},t=null)=>({...e,ref:(0,n.Px)(M,t)}),[M]),P=(0,l.useCallback)(e=>{C.current=e,I()},[I]),T=(0,l.useCallback)((e={},t=null)=>({...e,ref:(0,n.Px)(P,t),style:{...e.style,position:d,minWidth:w?void 0:"max-content",inset:"0 auto auto 0"}}),[d,P,w]),O=(0,l.useCallback)((e={},t=null)=>{let{size:r,shadowColor:n,bg:a,style:l,...o}=e;return{...o,ref:t,"data-popper-arrow":"",style:function(e){let{size:t,shadowColor:r,bg:n,style:a}=e,l={...a,position:"absolute"};return t&&(l["--popper-arrow-size"]=t),r&&(l["--popper-arrow-shadow-color"]=r),n&&(l["--popper-arrow-bg"]=n),l}(e)}},[]),A=(0,l.useCallback)((e={},t=null)=>({...e,ref:t,"data-popper-arrow-inner":""}),[]);return{update(){S.current?.update()},forceUpdate(){S.current?.forceUpdate()},transformOrigin:o.O3.transformOrigin.varRef,referenceRef:M,popperRef:P,getPopperProps:T,getArrowProps:O,getArrowInnerProps:A,getReferenceProps:B}}},71569:(e,t,r)=>{r.d(t,{B:()=>i});var n=r(94513),a=r(79435),l=r(94285),o=r(21533);let s=e=>(0,n.jsx)(o.B.div,{className:"chakra-stack__item",...e,__css:{display:"inline-block",flex:"0 0 auto",minWidth:0,...e.__css}});s.displayName="StackItem";let i=(0,r(84756).R)((e,t)=>{let{isInline:r,direction:i,align:u,justify:c,spacing:d="0.5rem",wrap:p,children:m,divider:f,className:h,shouldWrapChildren:x,...v}=e,b=r?"row":i??"column",y=(0,l.useMemo)(()=>(function(e){let{spacing:t,direction:r}=e,n={column:{my:t,mx:0,borderLeftWidth:0,borderBottomWidth:"1px"},"column-reverse":{my:t,mx:0,borderLeftWidth:0,borderBottomWidth:"1px"},row:{mx:t,my:0,borderLeftWidth:"1px",borderBottomWidth:0},"row-reverse":{mx:t,my:0,borderLeftWidth:"1px",borderBottomWidth:0}};return{"&":(0,a.bk)(r,e=>n[e])}})({spacing:d,direction:b}),[d,b]),g=!!f,_=!x&&!g,k=(0,l.useMemo)(()=>{let e=(0,a.ag)(m);return _?e:e.map((t,r)=>{let a=void 0!==t.key?t.key:r,o=r+1===e.length,i=(0,n.jsx)(s,{children:t},a),u=x?i:t;if(!g)return u;let c=(0,l.cloneElement)(f,{__css:y});return(0,n.jsxs)(l.Fragment,{children:[u,o?null:c]},a)})},[f,y,g,_,x,m]),w=(0,a.cx)("chakra-stack",h);return(0,n.jsx)(o.B.div,{ref:t,display:"flex",alignItems:u,justifyContent:c,flexDirection:b,flexWrap:p,gap:g?void 0:d,className:w,...v,children:k})});i.displayName="Stack"},72468:(e,t,r)=>{r.d(t,{EO:()=>s,Lt:()=>o});var n=r(94513),a=r(46949),l=r(53083);function o(e){let{leastDestructiveRef:t,...r}=e;return(0,n.jsx)(a.aF,{...r,initialFocusRef:t})}r(81139),r(95066),r(24490),r(31840),r(29607);let s=(0,r(84756).R)((e,t)=>(0,n.jsx)(l.$,{ref:t,role:"alertdialog",...e}))},72663:(e,t,r)=>{r.d(t,{W:()=>s});var n=r(94513),a=r(94285),l=r(38822),o=r(26725);function s(e){let t=a.Children.only(e.children),{getTriggerProps:r}=(0,l.C_)();return(0,n.jsx)(n.Fragment,{children:(0,a.cloneElement)(t,r(t.props,(0,o.Q)(t)))})}s.displayName="PopoverTrigger"},75975:(e,t,r)=>{r.d(t,{I:()=>c});var n=r(94513),a=r(79435),l=r(52156),o=r(45905),s=r(84756),i=r(21533);let u=(0,s.R)((e,t)=>{let r=(0,l.$)();return(0,n.jsx)(i.B.button,{ref:t,...e,__css:{display:"inline-flex",appearance:"none",alignItems:"center",outline:0,...r.button}})}),c=(0,s.R)((e,t)=>{let{children:r,as:l,...s}=e,c=(0,o.db)(s,t);return(0,n.jsx)(l||u,{...c,className:(0,a.cx)("chakra-menu__menu-button",e.className),children:(0,n.jsx)(i.B.span,{__css:{pointerEvents:"none",flex:"1 1 auto",minW:0},children:e.children})})});c.displayName="MenuButton"},78723:(e,t,r)=>{r.d(t,{c:()=>p});var n=r(94513),a=r(79435),l=r(5649),o=r(52156),s=r(45905),i=r(21533),u=r(84756);let c={enter:{visibility:"visible",opacity:1,scale:1,transition:{duration:.2,ease:[.4,0,.2,1]}},exit:{transitionEnd:{visibility:"hidden"},opacity:0,scale:.8,transition:{duration:.1,easings:"easeOut"}}},d=(0,i.B)(l.PY1.div),p=(0,u.R)(function(e,t){let{rootProps:r,motionProps:l,...u}=e,{isOpen:p,onTransitionEnd:m,unstable__animationState:f}=(0,s.KZ)(),h=(0,s.jy)(u,t),x=(0,s.F9)(r),v=(0,o.$)();return(0,n.jsx)(i.B.div,{...x,__css:{zIndex:e.zIndex??v.list?.zIndex},children:(0,n.jsx)(d,{variants:c,initial:!1,animate:p?"enter":"exit",__css:{outline:0,...v.list},...l,...h,className:(0,a.cx)("chakra-menu__menu-list",h.className),onUpdate:m,onAnimationComplete:(0,a.OK)(f.onComplete,h.onAnimationComplete)})})});p.displayName="MenuList"},78846:(e,t,r)=>{r.d(t,{l:()=>l});var n=r(56084),a=r(94285);function l(e){let{loading:t,src:r,srcSet:l,onLoad:o,onError:s,crossOrigin:i,sizes:u,ignoreFallback:c}=e,[d,p]=(0,a.useState)("pending");(0,a.useEffect)(()=>{p(r?"loading":"pending")},[r]);let m=(0,a.useRef)(null),f=(0,a.useCallback)(()=>{if(!r)return;h();let e=new Image;e.src=r,i&&(e.crossOrigin=i),l&&(e.srcset=l),u&&(e.sizes=u),t&&(e.loading=t),e.onload=e=>{h(),p("loaded"),o?.(e)},e.onerror=e=>{h(),p("failed"),s?.(e)},m.current=e},[r,i,l,u,o,s,t]),h=()=>{m.current&&(m.current.onload=null,m.current.onerror=null,m.current=null)};return(0,n.UQ)(()=>{if(!c)return"loading"===d&&f(),()=>{h()}},[d,f,c]),c?"loaded":d}},80456:(e,t,r)=>{r.d(t,{Tr:()=>s});var n=r(94513),a=r(82824),l=r(84756),o=r(21533);let s=(0,l.R)((e,t)=>{let r=(0,a.k)();return(0,n.jsx)(o.B.tr,{...e,ref:t,__css:r.tr})})},81139:(e,t,r)=>{r.d(t,{c:()=>u});var n=r(94513),a=r(79435),l=r(94285),o=r(46949),s=r(84756),i=r(21533);let u=(0,s.R)((e,t)=>{let{className:r,...s}=e,{bodyId:u,setBodyMounted:c}=(0,o.k3)();(0,l.useEffect)(()=>(c(!0),()=>c(!1)),[c]);let d=(0,a.cx)("chakra-modal__body",r),p=(0,o.x5)();return(0,n.jsx)(i.B.div,{ref:t,className:d,id:u,...s,__css:p.body})});u.displayName="ModalBody"},82824:(e,t,r)=>{r.d(t,{X:()=>d,k:()=>c});var n=r(94513),a=r(56005),l=r(79435),o=r(84756),s=r(8475),i=r(21533);let[u,c]=(0,l.q6)({name:"TableStylesContext",errorMessage:"useTableStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Table />\" "}),d=(0,o.R)((e,t)=>{let r=(0,s.o)("Table",e),{className:o,layout:c,...d}=(0,a.MN)(e);return(0,n.jsx)(u,{value:r,children:(0,n.jsx)(i.B.table,{ref:t,__css:{tableLayout:c,...r.table},className:(0,l.cx)("chakra-table",o),...d})})});d.displayName="Table"},82939:(e,t,r)=>{r.d(t,{W:()=>c});var n=r(94513),a=r(79435),l=r(37846),o=r(21533),s=r(84756);let i=(0,o.B)("div",{baseStyle:{display:"flex",alignItems:"center",justifyContent:"center",position:"absolute",top:"0",zIndex:2}}),u=(0,s.R)(function(e,t){let{placement:r="left",...a}=e,o=(0,l.Z)(),s=o.field,u={["left"===r?"insetStart":"insetEnd"]:"0",width:s?.height??s?.h,height:s?.height??s?.h,fontSize:s?.fontSize,...o.element};return(0,n.jsx)(i,{ref:t,__css:u,...a})});u.id="InputElement",u.displayName="InputElement";let c=(0,s.R)(function(e,t){let{className:r,...l}=e,o=(0,a.cx)("chakra-input__left-element",r);return(0,n.jsx)(u,{ref:t,placement:"left",className:o,...l})});c.id="InputLeftElement",c.displayName="InputLeftElement";let d=(0,s.R)(function(e,t){let{className:r,...l}=e,o=(0,a.cx)("chakra-input__right-element",r);return(0,n.jsx)(u,{ref:t,placement:"right",className:o,...l})});d.id="InputRightElement",d.displayName="InputRightElement"},84482:(e,t,r)=>{r.d(t,{Td:()=>s});var n=r(94513),a=r(82824),l=r(84756),o=r(21533);let s=(0,l.R)(({isNumeric:e,...t},r)=>{let l=(0,a.k)();return(0,n.jsx)(o.B.td,{...t,ref:r,__css:l.td,"data-is-numeric":e})})},84748:(e,t,r)=>{r.d(t,{y:()=>d});var n=r(94513),a=r(56005),l=r(79435),o=r(83541),s=r(84756),i=r(8475),u=r(21533);let c=(0,o.i7)({"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}}),d=(0,s.R)((e,t)=>{let r=(0,i.V)("Spinner",e),{label:o="Loading...",thickness:s="2px",speed:d="0.45s",emptyColor:p="transparent",className:m,...f}=(0,a.MN)(e),h=(0,l.cx)("chakra-spinner",m),x={display:"inline-block",borderColor:"currentColor",borderStyle:"solid",borderRadius:"99999px",borderWidth:s,borderBottomColor:p,borderLeftColor:p,animation:`${c} ${d} linear infinite`,...r};return(0,n.jsx)(u.B.div,{ref:t,__css:x,className:h,...f,children:o&&(0,n.jsx)(u.B.span,{srOnly:!0,children:o})})});d.displayName="Spinner"},84756:(e,t,r)=>{r.d(t,{R:()=>a});var n=r(94285);function a(e){return(0,n.forwardRef)(e)}},87096:(e,t,r)=>{r.d(t,{$c:()=>h,Jn:()=>g,O_:()=>m,Vh:()=>x,at:()=>i,uc:()=>p,uo:()=>y});var n=r(56084),a=r(79435),l=r(94285),o=r(55588),s=r(73336);let[i,u,c,d]=(0,o.D)();function p(e){let{defaultIndex:t,onChange:r,index:a,isManual:o,isLazy:s,lazyBehavior:i="unmount",orientation:u="horizontal",direction:d="ltr",...p}=e,[m,f]=(0,l.useState)(t??0),[h,x]=(0,n.ic)({defaultValue:t??0,value:a,onChange:r});(0,l.useEffect)(()=>{null!=a&&f(a)},[a]);let v=c(),b=(0,l.useId)(),y=e.id??b;return{id:`tabs-${y}`,selectedIndex:h,focusedIndex:m,setSelectedIndex:x,setFocusedIndex:f,isManual:o,isLazy:s,lazyBehavior:i,orientation:u,descendants:v,direction:d,htmlProps:p}}let[m,f]=(0,a.q6)({name:"TabsContext",errorMessage:"useTabsContext: `context` is undefined. Seems you forgot to wrap all tabs components within <Tabs />"});function h(e){let{focusedIndex:t,orientation:r,direction:n}=f(),o=u(),s=(0,l.useCallback)(e=>{let a=()=>{let e=o.nextEnabled(t);e&&e.node?.focus()},l=()=>{let e=o.prevEnabled(t);e&&e.node?.focus()},s="horizontal"===r,i="vertical"===r,u=e.key,c={["ltr"===n?"ArrowLeft":"ArrowRight"]:()=>s&&l(),["ltr"===n?"ArrowRight":"ArrowLeft"]:()=>s&&a(),ArrowDown:()=>i&&a(),ArrowUp:()=>i&&l(),Home:()=>{let e=o.firstEnabled();e&&e.node?.focus()},End:()=>{let e=o.lastEnabled();e&&e.node?.focus()}}[u];c&&(e.preventDefault(),c(e))},[o,t,r,n]);return{...e,role:"tablist","aria-orientation":r,onKeyDown:(0,a.Hj)(e.onKeyDown,s)}}function x(e){let{isDisabled:t=!1,isFocusable:r=!1,...l}=e,{setSelectedIndex:o,isManual:i,id:u,setFocusedIndex:c,selectedIndex:p}=f(),{index:m,register:h}=d({disabled:t&&!r}),x=m===p;return{...(0,s.I)({...l,ref:(0,n.Px)(h,e.ref),isDisabled:t,isFocusable:r,onClick:(0,a.Hj)(e.onClick,()=>{o(m)})}),id:_(u,m),role:"tab",tabIndex:x?0:-1,type:"button","aria-selected":x,"aria-controls":k(u,m),onFocus:t?void 0:(0,a.Hj)(e.onFocus,()=>{c(m);let e=t&&r;i||e||o(m)})}}let[v,b]=(0,a.q6)({});function y(e){let{id:t,selectedIndex:r}=f(),n=(0,a.ag)(e.children).map((e,n)=>(0,l.createElement)(v,{key:e.key??n,value:{isSelected:n===r,id:k(t,n),tabId:_(t,n),selectedIndex:r}},e));return{...e,children:n}}function g(e){let{children:t,...r}=e,{isLazy:n,lazyBehavior:o}=f(),{isSelected:s,id:i,tabId:u}=b(),c=(0,l.useRef)(!1);s&&(c.current=!0);let d=(0,a.qJ)({wasSelected:c.current,isSelected:s,enabled:n,mode:o});return{tabIndex:0,...r,children:d?t:null,role:"tabpanel","aria-labelledby":u,hidden:!s,id:i}}function _(e,t){return`${e}--tab-${t}`}function k(e,t){return`${e}--tabpanel-${t}`}},90066:(e,t,r)=>{r.d(t,{B8:()=>p,ck:()=>m,kp:()=>f});var n=r(94513),a=r(56005),l=r(79435),o=r(29484),s=r(84756),i=r(8475),u=r(21533);let[c,d]=(0,l.q6)({name:"ListStylesContext",errorMessage:"useListStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<List />\" "}),p=(0,s.R)(function(e,t){let r=(0,i.o)("List",e),{children:o,styleType:s="none",stylePosition:d,spacing:p,...m}=(0,a.MN)(e),f=(0,l.ag)(o);return(0,n.jsx)(c,{value:r,children:(0,n.jsx)(u.B.ul,{ref:t,listStyleType:s,listStylePosition:d,role:"list",__css:{...r.container,...p?{"& > *:not(style) ~ *:not(style)":{mt:p}}:{}},...m,children:f})})});p.displayName="List",(0,s.R)((e,t)=>{let{as:r,...a}=e;return(0,n.jsx)(p,{ref:t,as:"ol",styleType:"decimal",marginStart:"1em",...a})}).displayName="OrderedList",(0,s.R)(function(e,t){let{as:r,...a}=e;return(0,n.jsx)(p,{ref:t,as:"ul",styleType:"initial",marginStart:"1em",...a})}).displayName="UnorderedList";let m=(0,s.R)(function(e,t){let r=d();return(0,n.jsx)(u.B.li,{ref:t,...e,__css:r.item})});m.displayName="ListItem";let f=(0,s.R)(function(e,t){let r=d();return(0,n.jsx)(o.I,{ref:t,role:"presentation",...e,__css:r.icon})});f.displayName="ListIcon"},91140:(e,t,r)=>{r.d(t,{w:()=>c});var n=r(94513),a=r(56005),l=r(79435),o=r(56858),s=r(87096),i=r(84756),u=r(21533);let c=(0,i.R)(function(e,t){let r=(0,s.$c)({...e,ref:t}),i=(0,o.e)(),c=(0,a.H2)({display:"flex",...i.tablist});return(0,n.jsx)(u.B.div,{...r,className:(0,l.cx)("chakra-tabs__tablist",e.className),__css:c})});c.displayName="TabList"},93334:(e,t,r)=>{r.d(t,{J:()=>i,y:()=>u});var n=r(94285),a=Object.defineProperty,l=(e,t,r)=>t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,o=(e,t,r)=>(l(e,"symbol"!=typeof t?t+"":t,r),r);class s{constructor(){o(this,"modals"),this.modals=new Set}add(e){return this.modals.add(e),this.modals.size}remove(e){this.modals.delete(e)}isTopModal(e){return!!e&&e===Array.from(this.modals)[this.modals.size-1]}}let i=new s;function u(e,t){let[r,a]=(0,n.useState)(0);return(0,n.useEffect)(()=>{let r=e.current;if(r)return t&&a(i.add(r)),()=>{i.remove(r),a(0)}},[t,e]),r}},93493:(e,t,r)=>{r.d(t,{o:()=>c});var n=r(94513),a=r(56005),l=r(79435),o=r(56858),s=r(87096),i=r(84756),u=r(21533);let c=(0,i.R)(function(e,t){let r=(0,o.e)(),i=(0,s.Vh)({...e,ref:t}),c=(0,a.H2)({outline:"0",display:"flex",alignItems:"center",justifyContent:"center",...r.tab});return(0,n.jsx)(u.B.button,{...i,className:(0,l.cx)("chakra-tabs__tab",e.className),__css:c})});c.displayName="Tab"},93681:(e,t,r)=>{r.d(t,{NP:()=>u,Wh:()=>m,zy:()=>f});var n=r(94513),a=r(56005),l=r(79435),o=r(83541),s=r(94285),i=r(88142);function u(e){let{cssVarsRoot:t,theme:r,children:l}=e,i=(0,s.useMemo)(()=>(0,a.gd)(r),[r]);return(0,n.jsxs)(o.NP,{theme:i,children:[(0,n.jsx)(c,{root:t}),l]})}function c({root:e=":host, :root"}){let t=[e,"[data-theme]"].join(",");return(0,n.jsx)(o.mL,{styles:e=>({[t]:e.__cssVars})})}let[d,p]=(0,l.q6)({name:"StylesContext",errorMessage:"useStyles: `styles` is undefined. Seems you forgot to wrap the components in `<StylesProvider />` "});function m(e){return(0,l.q6)({name:`${e}StylesContext`,errorMessage:`useStyles: "styles" is undefined. Seems you forgot to wrap the components in "<${e} />" `})}function f(){let{colorMode:e}=(0,i.G6)();return(0,n.jsx)(o.mL,{styles:t=>{let r=(0,l.rY)(t,"styles.global"),n=(0,l.Jg)(r,{theme:t,colorMode:e});if(n)return(0,a.AH)(n)(t)}})}},94042:(e,t,r)=>{r.d(t,{Ip:()=>u});var n=r(94513),a=r(42398),l=r(29484),o=r(21533);let s=e=>(0,n.jsx)(l.I,{color:"red.400",...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M21,5H3C2.621,5,2.275,5.214,2.105,5.553C1.937,5.892,1.973,6.297,2.2,6.6l9,12 c0.188,0.252,0.485,0.4,0.8,0.4s0.611-0.148,0.8-0.4l9-12c0.228-0.303,0.264-0.708,0.095-1.047C21.725,5.214,21.379,5,21,5z"})});function i(e){return(0,n.jsx)(l.I,{color:"green.400",...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M12.8,5.4c-0.377-0.504-1.223-0.504-1.6,0l-9,12c-0.228,0.303-0.264,0.708-0.095,1.047 C2.275,18.786,2.621,19,3,19h18c0.379,0,0.725-0.214,0.895-0.553c0.169-0.339,0.133-0.744-0.095-1.047L12.8,5.4z"})})}function u(e){let{type:t,"aria-label":r,...l}=e,u=(0,a.E)(),c="increase"===t?i:s;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.B.span,{srOnly:!0,children:r||("increase"===t?"increased by":"decreased by")}),(0,n.jsx)(c,{"aria-hidden":!0,...l,__css:u.icon})]})}s.displayName="StatDownArrow",i.displayName="StatUpArrow",u.displayName="StatArrow"},95066:(e,t,r)=>{r.d(t,{s:()=>s});var n=r(94513),a=r(79435),l=r(46949),o=r(65012);let s=(0,r(84756).R)((e,t)=>{let{onClick:r,className:s,...i}=e,{onClose:u}=(0,l.k3)(),c=(0,a.cx)("chakra-modal__close-btn",s),d=(0,l.x5)();return(0,n.jsx)(o.J,{ref:t,__css:d.closeButton,className:c,onClick:(0,a.Hj)(r,e=>{e.stopPropagation(),u()}),...i})});s.displayName="ModalCloseButton"},97680:(e,t,r)=>{r.d(t,{t:()=>l,v:()=>o});var n=r(79435),a=r(31862);function l(e){let{isDisabled:t,isInvalid:r,isReadOnly:a,isRequired:l,...s}=o(e);return{...s,disabled:t,readOnly:a,required:l,"aria-invalid":(0,n.rq)(r),"aria-required":(0,n.rq)(l),"aria-readonly":(0,n.rq)(a)}}function o(e){let t=(0,a.Uc)(),{id:r,disabled:l,readOnly:o,required:s,isRequired:i,isInvalid:u,isReadOnly:c,isDisabled:d,onFocus:p,onBlur:m,...f}=e,h=e["aria-describedby"]?[e["aria-describedby"]]:[];return t?.hasFeedbackText&&t?.isInvalid&&h.push(t.feedbackId),t?.hasHelpText&&h.push(t.helpTextId),{...f,"aria-describedby":h.join(" ")||void 0,id:r??t?.id,isDisabled:l??d??t?.isDisabled,isReadOnly:o??c??t?.isReadOnly,isRequired:s??i??t?.isRequired,isInvalid:u??t?.isInvalid,onFocus:(0,n.Hj)(t?.onFocus,p),onBlur:(0,n.Hj)(t?.onBlur,m)}}}}]);