exports.id=4959,exports.ids=[4959],exports.modules={13594:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.styleHookSingleton=t.stylesheetSingleton=t.styleSingleton=void 0;var n=r(77687);Object.defineProperty(t,"styleSingleton",{enumerable:!0,get:function(){return n.styleSingleton}});var i=r(20573);Object.defineProperty(t,"stylesheetSingleton",{enumerable:!0,get:function(){return i.stylesheetSingleton}});var a=r(36967);Object.defineProperty(t,"styleHookSingleton",{enumerable:!0,get:function(){return a.styleHookSingleton}})},16742:(e,t,r)=>{"use strict";r.d(t,{h:()=>O});var n=r(82015),i=r(79486),a=r(59009),o=r(42714),l=r(34797),c=r(68181),s=r(78813),u=r(81265),f=r(50765),d=r(11333),p=["dangerouslySetInnerHTML","ticks"];function y(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function h(e){var t=(0,l.j)();return(0,n.useEffect)(()=>(t((0,o.cU)(e)),()=>{t((0,o.fR)(e))}),[e,t]),null}var m=e=>{var t,{yAxisId:r,className:y,width:h,label:m}=e,b=(0,n.useRef)(null),g=(0,n.useRef)(null),O=(0,l.G)(s.c2),w=(0,u.r)(),P=(0,l.j)(),x="yAxis",E=(0,l.G)(e=>(0,c.iV)(e,x,r,w)),j=(0,l.G)(e=>(0,c.wP)(e,r)),k=(0,l.G)(e=>(0,c.KR)(e,r)),S=(0,l.G)(e=>(0,c.Zi)(e,x,r,w));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==h||!j||(0,d.Z)(m)||(0,n.isValidElement)(m))){var e,t=b.current,i=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:a,tickMargin:l}=t.props,c=(0,f.z)({ticks:i,label:g.current,labelGapWithTick:5,tickSize:a,tickMargin:l});Math.round(j.width)!==Math.round(c)&&P((0,o.QG)({id:r,width:c}))}},[b,null==b||null==(t=b.current)||null==(t=t.tickRefs)?void 0:t.current,null==j?void 0:j.width,j,P,m,r,h]),null==j||null==k)return null;var{dangerouslySetInnerHTML:C,ticks:A}=e,D=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,p);return n.createElement(a.u,v({},D,{ref:b,labelRef:g,scale:E,x:k.x,y:k.y,width:j.width,height:j.height,className:(0,i.$)("recharts-".concat(x," ").concat(x),y),viewBox:O,ticks:S}))},b=e=>{var t,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(h,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter}),n.createElement(m,e))},g={allowDataOverflow:c.cd.allowDataOverflow,allowDecimals:c.cd.allowDecimals,allowDuplicatedCategory:c.cd.allowDuplicatedCategory,hide:!1,mirror:c.cd.mirror,orientation:c.cd.orientation,padding:c.cd.padding,reversed:c.cd.reversed,scale:c.cd.scale,tickCount:c.cd.tickCount,type:c.cd.type,width:c.cd.width,yAxisId:0};class O extends n.Component{render(){return n.createElement(b,this.props)}}y(O,"displayName","YAxis"),y(O,"defaultProps",g)},19236:()=>{},20573:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.stylesheetSingleton=void 0;var n=r(6337);t.stylesheetSingleton=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=(0,n.getNonce)();return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}}},23170:(e,t,r)=>{"use strict";r.d(t,{i:()=>T});var n=r(82015),i=r(76271),a=r.n(i),o=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],l=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),c=(e,t)=>r=>l(o(e,t),r),s=(e,t)=>r=>l([...o(e,t).map((e,t)=>e*t).slice(1),0],r),u=function(){for(var e,t,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var u=c(e,t),f=c(r,n),d=s(e,t),p=e=>e>1?1:e<0?0:e,y=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=u(r)-t,a=d(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=p(r-i/a)}return f(r)};return y.isStepper=!1,y},f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},d=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return u(e);case"spring":return f();default:if("cubic-bezier"===e.split("(")[0])return u(e)}return"function"==typeof e?e:null};function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var v=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),h=(e,t,r)=>e.map(e=>"".concat(v(e)," ").concat(t,"ms ").concat(r)).join(","),m=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),b=(e,t)=>Object.keys(t).reduce((r,n)=>y(y({},r),{},{[n]:e(n,t[n])}),{});function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function O(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var w=(e,t,r)=>e+(t-e)*r,P=e=>{var{from:t,to:r}=e;return t!==r},x=(e,t,r)=>{var n=b((t,r)=>{if(P(r)){var[n,i]=e(r.from,r.to,r.velocity);return O(O({},r),{},{from:n,velocity:i})}return r},t);return r<1?b((e,t)=>P(t)?O(O({},t),{},{velocity:w(t.velocity,n[e].velocity,r),from:w(t.from,n[e].from,r)}):t,t):x(e,n,r-1)};let E=(e,t,r,n,i,a)=>{var o=m(e,t);return!0===r.isStepper?function(e,t,r,n,i,a){var o,l=n.reduce((r,n)=>O(O({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),c=()=>b((e,t)=>t.from,l),s=()=>!Object.values(l).filter(P).length,u=null,f=n=>{o||(o=n);var d=(n-o)/r.dt;l=x(r,l,d),i(O(O(O({},e),t),c())),o=n,s()||(u=a.setTimeout(f))};return()=>(u=a.setTimeout(f),()=>{u()})}(e,t,r,o,i,a):function(e,t,r,n,i,a,o){var l,c=null,s=i.reduce((r,n)=>O(O({},r),{},{[n]:[e[n],t[n]]}),{}),u=i=>{l||(l=i);var f=(i-l)/n,d=b((e,t)=>w(...t,r(f)),s);if(a(O(O(O({},e),t),d)),f<1)c=o.setTimeout(u);else{var p=b((e,t)=>w(...t,r(1)),s);a(O(O(O({},e),t),p))}};return()=>(c=o.setTimeout(u),()=>{c()})}(e,t,r,n,o,i,a)};class j{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var k=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function S(){return(S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function C(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?C(Object(r),!0).forEach(function(t){D(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function D(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class M extends n.PureComponent{constructor(e,t){super(e,t),D(this,"mounted",!1),D(this,"manager",null),D(this,"stopJSAnimation",null),D(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:l}=this.props,{style:c}=this.state;if(r){if(!t){this.state&&c&&(n&&c[n]!==o||!n&&c!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!a()(e.to,o)||!e.canBegin||!e.isActive){var s=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var u=s||i?l:e.to;this.state&&c&&(n&&c[n]!==u||!n&&c!==u)&&this.setState({style:n?{[n]:u}:u}),this.runAnimation(A(A({},this.props),{},{from:u,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,c=E(t,r,d(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=c()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:c}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof c||"spring"===a)return void this.runJSAnimation(e);var s=n?{[n]:i}:i,u=h(Object.keys(s),r,a);this.manager.start([o,t,A(A({},s),{},{transition:u}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:a,easing:o,isActive:l,from:c,to:s,canBegin:u,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:p,animationManager:y}=e,v=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,k),h=n.Children.count(t),m=this.state.style;if("function"==typeof t)return t(m);if(!l||0===h||i<=0)return t;var b=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,A(A({},v),{},{style:A(A({},t),m),className:r}))};return 1===h?b(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>b(e)))}}D(M,"displayName","Animate"),D(M,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var N=(0,n.createContext)(null);function T(e){var t,r,i,a,o,l,c,s,u=(0,n.useContext)(N);return n.createElement(M,S({},e,{animationManager:null!=(c=null!=(s=e.animationManager)?s:u)?c:(t=new j,i=()=>null,a=!1,o=null,l=e=>{if(!a){if(Array.isArray(e)){if(!e.length)return;var[r,...n]=e;if("number"==typeof r){o=t.setTimeout(l.bind(null,n),r);return}l(r),o=t.setTimeout(l.bind(null,n));return}"object"==typeof e&&i(e),"function"==typeof e&&e()}},{stop:()=>{a=!0},start:e=>{a=!1,o&&(o(),o=null),l(e)},subscribe:e=>(i=e,()=>{i=()=>null}),getTimeoutController:()=>t})}))}},28162:(e,t,r)=>{"use strict";r.d(t,{E:()=>m});var n=r(82015),i=r(58971),a=r(42189),o=r(98812),l=r(65244),c=r(60115),s=r(61262),u=r(42127),f=r(4236),d=["width","height"];function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var y={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},v=(0,n.forwardRef)(function(e,t){var r,i=(0,u.e)(e.categoricalChartProps,y),{width:v,height:h}=i,m=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,d);if(!(0,f.F)(v)||!(0,f.F)(h))return null;var{chartName:b,defaultTooltipEventType:g,validateTooltipEventTypes:O,tooltipPayloadSearcher:w,categoricalChartProps:P}=e;return n.createElement(a.J,{preloadedState:{options:{chartName:b,defaultTooltipEventType:g,validateTooltipEventTypes:O,tooltipPayloadSearcher:w,eventEmitter:void 0}},reduxStoreName:null!=(r=P.id)?r:b},n.createElement(o.TK,{chartData:P.data}),n.createElement(l.s,{width:v,height:h,layout:i.layout,margin:i.margin}),n.createElement(c.p,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(s.L,p({},m,{width:v,height:h,ref:t})))}),h=["axis","item"],m=(0,n.forwardRef)((e,t)=>n.createElement(v,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:h,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))},36967:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.styleHookSingleton=void 0;var n=r(5818).__importStar(r(82015)),i=r(20573);t.styleHookSingleton=function(){var e=(0,i.stylesheetSingleton)();return function(t,r){n.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}}},39202:(e,t,r)=>{"use strict";r.d(t,{d:()=>N});var n=r(82015),i=r(54621),a=r(77331),o=r(80020),l=r(29655),c=r(76037),s=r(59009),u=r(28158),f=r(68181),d=r(34797),p=r(81265),y=r(42127),v=["x1","y1","x2","y2","key"],h=["offset"],m=["xAxisId","yAxisId"],b=["xAxisId","yAxisId"];function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function O(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function P(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var x=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:i,y:a,width:o,height:l,ry:c}=e;return n.createElement("rect",{x:i,y:a,ry:c,width:o,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function E(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:i,y1:a,x2:l,y2:c,key:s}=t,u=P(t,v),f=(0,o.J9)(u,!1),{offset:d}=f,p=P(f,h);r=n.createElement("line",w({},p,{x1:i,y1:a,x2:l,y2:c,fill:"none",key:s}))}return r}function j(e){var{x:t,width:r,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=P(e,m),s=a.map((e,n)=>E(i,O(O({},c),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},s)}function k(e){var{y:t,height:r,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=P(e,b),s=a.map((e,n)=>E(i,O(O({},c),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},s)}function S(e){var{horizontalFill:t,fillOpacity:r,x:i,y:a,width:o,height:l,horizontalPoints:c,horizontal:s=!0}=e;if(!s||!t||!t.length)return null;var u=c.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==u[0]&&u.unshift(0);var f=u.map((e,c)=>{var s=u[c+1]?u[c+1]-e:a+l-e;if(s<=0)return null;var f=c%t.length;return n.createElement("rect",{key:"react-".concat(c),y:e,x:i,height:s,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function C(e){var{vertical:t=!0,verticalFill:r,fillOpacity:i,x:a,y:o,width:l,height:c,verticalPoints:s}=e;if(!t||!r||!r.length)return null;var u=s.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==u[0]&&u.unshift(0);var f=u.map((e,t)=>{var s=u[t+1]?u[t+1]-e:a+l-e;if(s<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:s,height:c,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var A=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,c.f)(O(O(O({},s.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},D=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,c.f)(O(O(O({},s.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},M={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function N(e){var t=(0,u.yi)(),r=(0,u.rY)(),o=(0,u.W7)(),l=O(O({},(0,y.e)(e,M)),{},{x:(0,a.Et)(e.x)?e.x:o.left,y:(0,a.Et)(e.y)?e.y:o.top,width:(0,a.Et)(e.width)?e.width:o.width,height:(0,a.Et)(e.height)?e.height:o.height}),{xAxisId:c,yAxisId:s,x:v,y:h,width:m,height:b,syncWithTicks:g,horizontalValues:P,verticalValues:E}=l,N=(0,p.r)(),T=(0,d.G)(e=>(0,f.ZB)(e,"xAxis",c,N)),z=(0,d.G)(e=>(0,f.ZB)(e,"yAxis",s,N));if(!(0,a.Et)(m)||m<=0||!(0,a.Et)(b)||b<=0||!(0,a.Et)(v)||v!==+v||!(0,a.Et)(h)||h!==+h)return null;var I=l.verticalCoordinatesGenerator||A,G=l.horizontalCoordinatesGenerator||D,{horizontalPoints:B,verticalPoints:R}=l;if((!B||!B.length)&&"function"==typeof G){var L=P&&P.length,F=G({yAxis:z?O(O({},z),{},{ticks:L?P:z.ticks}):void 0,width:t,height:r,offset:o},!!L||g);(0,i.R)(Array.isArray(F),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof F,"]")),Array.isArray(F)&&(B=F)}if((!R||!R.length)&&"function"==typeof I){var U=E&&E.length,J=I({xAxis:T?O(O({},T),{},{ticks:U?E:T.ticks}):void 0,width:t,height:r,offset:o},!!U||g);(0,i.R)(Array.isArray(J),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof J,"]")),Array.isArray(J)&&(R=J)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(x,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(S,w({},l,{horizontalPoints:B})),n.createElement(C,w({},l,{verticalPoints:R})),n.createElement(j,w({},l,{offset:o,horizontalPoints:B,xAxis:T,yAxis:z})),n.createElement(k,w({},l,{offset:o,verticalPoints:R,xAxis:T,yAxis:z})))}N.displayName="CartesianGrid"},59009:(e,t,r)=>{"use strict";r.d(t,{u:()=>P});var n=r(82015),i=r(67063),a=r.n(i),o=r(79486),l=r(89192),c=r(65132),s=r(68812),u=r(11333),f=r(77331),d=r(50991),p=r(80020),y=r(76037),v=["viewBox"],h=["viewBox"];function m(){return(m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function w(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class P extends n.Component{constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(e,t){var{viewBox:r}=e,n=O(e,v),i=this.props,{viewBox:a}=i,o=O(i,h);return!(0,l.b)(r,a)||!(0,l.b)(n,o)||!(0,l.b)(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:c,width:s,height:u,orientation:d,tickSize:p,mirror:y,tickMargin:v}=this.props,h=y?-1:1,m=e.tickSize||p,b=(0,f.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(d){case"top":t=r=e.coordinate,o=(n=(i=c+!y*u)-h*m)-h*v,a=b;break;case"left":n=i=e.coordinate,a=(t=(r=l+!y*s)-h*m)-h*v,o=b;break;case"right":n=i=e.coordinate,a=(t=(r=l+y*s)+h*m)+h*v,o=b;break;default:t=r=e.coordinate,o=(n=(i=c+y*u)+h*m)+h*v,a=b}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:i,orientation:l,mirror:c,axisLine:s}=this.props,u=g(g(g({},(0,p.J9)(this.props,!1)),(0,p.J9)(s,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var f=+("top"===l&&!c||"bottom"===l&&c);u=g(g({},u),{},{x1:e,y1:t+f*i,x2:e+r,y2:t+f*i})}else{var d=+("left"===l&&!c||"right"===l&&c);u=g(g({},u),{},{x1:e+d*r,y1:t,x2:e+d*r,y2:t+i})}return n.createElement("line",m({},u,{className:(0,o.$)("recharts-cartesian-axis-line",a()(s,"className"))}))}static renderTickItem(e,t,r){var i,a=(0,o.$)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))i=n.cloneElement(e,g(g({},t),{},{className:a}));else if("function"==typeof e)i=e(g(g({},t),{},{className:a}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=(0,o.$)(l,e.className)),i=n.createElement(s.E,m({},t,{className:l}),r)}return i}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:l,tick:s,tickFormatter:u,unit:f}=this.props,v=(0,y.f)(g(g({},this.props),{},{ticks:r}),e,t),h=this.getTickTextAnchor(),b=this.getTickVerticalAnchor(),O=(0,p.J9)(this.props,!1),w=(0,p.J9)(s,!1),x=g(g({},O),{},{fill:"none"},(0,p.J9)(i,!1)),E=v.map((e,t)=>{var{line:r,tick:p}=this.getTickLineCoord(e),y=g(g(g(g({textAnchor:h,verticalAnchor:b},O),{},{stroke:"none",fill:l},w),p),{},{index:t,payload:e,visibleTicksCount:v.length,tickFormatter:u});return n.createElement(c.W,m({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,d.XC)(this.props,e,t)),i&&n.createElement("line",m({},x,r,{className:(0,o.$)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),s&&P.renderTickItem(s,y,"".concat("function"==typeof u?u(e.value,t):e.value).concat(f||"")))});return E.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},E):null}render(){var{axisLine:e,width:t,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:l}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(c.W,{className:(0,o.$)("recharts-cartesian-axis",i),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),u.J.renderCallByParent(this.props))}}w(P,"displayName","CartesianAxis"),w(P,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},61262:(e,t,r)=>{"use strict";r.d(t,{L:()=>w});var n=r(82015),i=r(80020),a=r(6177),o=r(79486),l=r(95322),c=r(34797),s=r(61055),u=r(91055),f=r(28995),d=r(82927),p=r(99225),y=r(38987),v=r(36549),h=r(92753);function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var b=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,height:a,onClick:b,onContextMenu:g,onDoubleClick:O,onMouseDown:w,onMouseEnter:P,onMouseLeave:x,onMouseMove:E,onMouseUp:j,onTouchEnd:k,onTouchMove:S,onTouchStart:C,style:A,width:D}=e,M=(0,c.j)(),[N,T]=(0,n.useState)(null),[z,I]=(0,n.useState)(null);(0,u.l3)();var G=(0,d.C)(),B=(0,n.useCallback)(e=>{G(e),"function"==typeof t&&t(e),T(e),I(e)},[G,t,T,I]),R=(0,n.useCallback)(e=>{M((0,s.ky)(e)),M((0,p.y)({handler:b,reactEvent:e}))},[M,b]),L=(0,n.useCallback)(e=>{M((0,s.dj)(e)),M((0,p.y)({handler:P,reactEvent:e}))},[M,P]),F=(0,n.useCallback)(e=>{M((0,l.xS)()),M((0,p.y)({handler:x,reactEvent:e}))},[M,x]),U=(0,n.useCallback)(e=>{M((0,s.dj)(e)),M((0,p.y)({handler:E,reactEvent:e}))},[M,E]),J=(0,n.useCallback)(()=>{M((0,f.Ru)())},[M]),K=(0,n.useCallback)(e=>{M((0,f.uZ)(e.key))},[M]),W=(0,n.useCallback)(e=>{M((0,p.y)({handler:g,reactEvent:e}))},[M,g]),H=(0,n.useCallback)(e=>{M((0,p.y)({handler:O,reactEvent:e}))},[M,O]),$=(0,n.useCallback)(e=>{M((0,p.y)({handler:w,reactEvent:e}))},[M,w]),_=(0,n.useCallback)(e=>{M((0,p.y)({handler:j,reactEvent:e}))},[M,j]),V=(0,n.useCallback)(e=>{M((0,p.y)({handler:C,reactEvent:e}))},[M,C]),Z=(0,n.useCallback)(e=>{M((0,y.e)(e)),M((0,p.y)({handler:S,reactEvent:e}))},[M,S]),X=(0,n.useCallback)(e=>{M((0,p.y)({handler:k,reactEvent:e}))},[M,k]);return n.createElement(v.$.Provider,{value:N},n.createElement(h.t.Provider,{value:z},n.createElement("div",{className:(0,o.$)("recharts-wrapper",i),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:D,height:a},A),onClick:R,onContextMenu:W,onDoubleClick:H,onFocus:J,onKeyDown:K,onMouseDown:$,onMouseEnter:L,onMouseLeave:F,onMouseMove:U,onMouseUp:_,onTouchEnd:X,onTouchMove:Z,onTouchStart:V,ref:B},r)))}),g=r(57001),O=["children","className","width","height","style","compact","title","desc"],w=(0,n.forwardRef)((e,t)=>{var{children:r,className:o,width:l,height:c,style:s,compact:u,title:f,desc:d}=e,p=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,O),y=(0,i.J9)(p,!1);return u?n.createElement(a.a,{otherAttributes:y,title:f,desc:d},r):n.createElement(b,{className:o,style:s,width:l,height:c,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(a.a,{otherAttributes:y,title:f,desc:d,ref:t},n.createElement(g.f,null,r)))})},62157:(e,t,r)=>{"use strict";r.d(t,{r:()=>g});var n=r(82015),i=r(58971),a=r(42189),o=r(98812),l=r(65244),c=r(60115),s=r(69367),u=r(61262),f=r(42127),d=r(4236),p=["width","height","layout"];function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},h=(0,n.forwardRef)(function(e,t){var r,i=(0,f.e)(e.categoricalChartProps,v),{width:h,height:m,layout:b}=i,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,p);if(!(0,d.F)(h)||!(0,d.F)(m))return null;var{chartName:O,defaultTooltipEventType:w,validateTooltipEventTypes:P,tooltipPayloadSearcher:x}=e;return n.createElement(a.J,{preloadedState:{options:{chartName:O,defaultTooltipEventType:w,validateTooltipEventTypes:P,tooltipPayloadSearcher:x,eventEmitter:void 0}},reduxStoreName:null!=(r=i.id)?r:O},n.createElement(o.TK,{chartData:i.data}),n.createElement(l.s,{width:h,height:m,layout:b,margin:i.margin}),n.createElement(c.p,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(s.P,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),n.createElement(u.L,y({width:h,height:m},g,{ref:t})))}),m=["item"],b={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},g=(0,n.forwardRef)((e,t)=>{var r=(0,f.e)(e,b);return n.createElement(h,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:m,tooltipPayloadSearcher:i.uN,categoricalChartProps:r,ref:t})})},73262:(e,t,r)=>{"use strict";r.d(t,{y:()=>ea,L:()=>ei});var n=r(82015),i=r(79486),a=r(65132),o=r(80020),l=r(27978),c=r(95167),s=r(42127),u=r(23170),f=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function y(e){var{direction:t,width:r,dataKey:i,isAnimationActive:s,animationBegin:d,animationDuration:y,animationEasing:v}=e,h=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,f),m=(0,o.J9)(h,!1),{data:b,dataPointFormatter:g,xAxisId:O,yAxisId:w,errorBarOffset:P}=(0,l.G9)(),x=(0,c.ZI)(O),E=(0,c.gi)(w);if((null==x?void 0:x.scale)==null||(null==E?void 0:E.scale)==null||null==b||"x"===t&&"number"!==x.type)return null;var j=b.map(e=>{var o,l,{x:c,y:f,value:h,errorVal:b}=g(e,i,t);if(!b)return null;var O=[];if(Array.isArray(b)?[o,l]=b:o=l=b,"x"===t){var{scale:w}=x,j=f+P,k=j+r,S=j-r,C=w(h-o),A=w(h+l);O.push({x1:A,y1:k,x2:A,y2:S}),O.push({x1:C,y1:j,x2:A,y2:j}),O.push({x1:C,y1:k,x2:C,y2:S})}else if("y"===t){var{scale:D}=E,M=c+P,N=M-r,T=M+r,z=D(h-o),I=D(h+l);O.push({x1:N,y1:I,x2:T,y2:I}),O.push({x1:M,y1:z,x2:M,y2:I}),O.push({x1:N,y1:z,x2:T,y2:z})}var G="".concat(c+P,"px ").concat(f+P,"px");return n.createElement(a.W,p({className:"recharts-errorBar",key:"bar-".concat(O.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},m),O.map(e=>{var t=s?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return n.createElement(u.i,{from:{transform:"scaleY(0)",transformOrigin:G},to:{transform:"scaleY(1)",transformOrigin:G},begin:d,easing:v,isActive:s,duration:y,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:G}},n.createElement("line",p({},e,{style:t})))}))});return n.createElement(a.W,{className:"recharts-errorBars"},j)}var v=(0,n.createContext)(void 0);function h(e){var{direction:t,children:r}=e;return n.createElement(v.Provider,{value:t},r)}var m={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function b(e){var t,r,i=(t=e.direction,r=(0,n.useContext)(v),null!=t?t:null!=r?r:"x"),{width:a,isAnimationActive:o,animationBegin:c,animationDuration:u,animationEasing:f}=(0,s.e)(e,m);return n.createElement(n.Fragment,null,n.createElement(l.pU,{dataKey:e.dataKey,direction:i}),n.createElement(y,p({},e,{direction:i,width:a,isAnimationActive:o,animationBegin:c,animationDuration:u,animationEasing:f})))}class g extends n.Component{render(){return n.createElement(b,this.props)}}d(g,"defaultProps",m),d(g,"displayName","ErrorBar");var O=r(44581),w=r(60897),P=r(77331),x=r(13141),E=r(29655),j=r(50991),k=r(7262),S=r(9843),C=r(63461),A=r(60456),D=r(34797),M=r(68181);function N(e,t){var r,n,i=(0,D.G)(t=>(0,M.Rl)(t,e)),a=(0,D.G)(e=>(0,M.sf)(e,t)),o=null!=(r=null==i?void 0:i.allowDataOverflow)?r:M.PU.allowDataOverflow,l=null!=(n=null==a?void 0:a.allowDataOverflow)?n:M.cd.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function T(e){var{xAxisId:t,yAxisId:r,clipPathId:i}=e,a=(0,c.oM)(),{needClipX:o,needClipY:l,needClip:s}=N(t,r);if(!s)return null;var{x:u,y:f,width:d,height:p}=a;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:o?u:u-d/2,y:l?f:f-p/2,width:o?d:2*d,height:l?p:2*p}))}var z=r(28158),I=r(93849),G=r(81265),B=r(77741),R=r(49484),L=r(25826),F=["onMouseEnter","onMouseLeave","onClick"],U=["value","background","tooltipPosition"],J=["onMouseEnter","onClick","onMouseLeave"];function K(){return(K=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function W(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?W(Object(r),!0).forEach(function(t){$(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):W(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function $(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var V=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,E.uM)(r,t),payload:e}]};function Z(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:(0,E.uM)(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function X(e){var t=(0,D.G)(B.A2),{data:r,dataKey:i,background:a,allOtherBarProps:l}=e,{onMouseEnter:c,onMouseLeave:s,onClick:u}=l,f=_(l,F),d=(0,S.Cj)(c,i),p=(0,S.Pg)(s),y=(0,S.Ub)(u,i);if(!a||null==r)return null;var v=(0,o.J9)(a,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:o,background:l,tooltipPosition:c}=e,s=_(e,U);if(!l)return null;var u=d(e,r),h=p(e,r),m=y(e,r),b=H(H(H(H(H({option:a,isActive:String(r)===t},s),{},{fill:"#eee"},l),v),(0,j.XC)(f,e,r)),{},{onMouseEnter:u,onMouseLeave:h,onClick:m,dataKey:i,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(k.z,K({key:"background-bar-".concat(r)},b))}))}function q(e){var{data:t,props:r,showLabels:i}=e,l=(0,o.J9)(r,!1),{shape:c,dataKey:s,activeBar:u}=r,f=(0,D.G)(B.A2),d=(0,D.G)(B.Xb),{onMouseEnter:p,onClick:y,onMouseLeave:v}=r,h=_(r,J),m=(0,S.Cj)(p,s),b=(0,S.Pg)(v),g=(0,S.Ub)(y,s);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=u&&String(t)===f&&(null==d||s===d),i=H(H(H({},l),e),{},{isActive:r,option:r?u:c,index:t,dataKey:s});return n.createElement(a.W,K({className:"recharts-bar-rectangle"},(0,j.XC)(h,e,t),{onMouseEnter:m(e,t),onMouseLeave:b(e,t),onClick:g(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),n.createElement(k.z,i))}),i&&w.Z.renderCallByParent(r,t)):null}function Y(e){var{props:t,previousRectanglesRef:r}=e,{data:i,layout:o,isAnimationActive:l,animationBegin:c,animationDuration:s,animationEasing:f,onAnimationEnd:d,onAnimationStart:p}=t,y=r.current,v=(0,L.n)(t,"recharts-bar-"),[h,m]=(0,n.useState)(!1),b=(0,n.useCallback)(()=>{"function"==typeof d&&d(),m(!1)},[d]),g=(0,n.useCallback)(()=>{"function"==typeof p&&p(),m(!0)},[p]);return n.createElement(u.i,{begin:c,duration:s,isActive:l,easing:f,from:{t:0},to:{t:1},onAnimationEnd:b,onAnimationStart:g,key:v},e=>{var{t:l}=e,c=1===l?i:i.map((e,t)=>{var r=y&&y[t];if(r){var n=(0,P.Dj)(r.x,e.x),i=(0,P.Dj)(r.y,e.y),a=(0,P.Dj)(r.width,e.width),c=(0,P.Dj)(r.height,e.height);return H(H({},e),{},{x:n(l),y:i(l),width:a(l),height:c(l)})}if("horizontal"===o){var s=(0,P.Dj)(0,e.height)(l);return H(H({},e),{},{y:e.y+e.height-s,height:s})}var u=(0,P.Dj)(0,e.width)(l);return H(H({},e),{},{width:u})});return l>0&&(r.current=c),n.createElement(a.W,null,n.createElement(q,{props:t,data:c,showLabels:!h}))})}function Q(e){var{data:t,isAnimationActive:r}=e,i=(0,n.useRef)(null);return r&&t&&t.length&&(null==i.current||i.current!==t)?n.createElement(Y,{previousRectanglesRef:i,props:e}):n.createElement(q,{props:e,data:t,showLabels:!0})}var ee=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,E.kr)(e,t)}};class et extends n.PureComponent{constructor(){super(...arguments),$(this,"id",(0,P.NF)("recharts-bar-"))}render(){var{hide:e,data:t,dataKey:r,className:o,xAxisId:l,yAxisId:c,needClip:s,background:u,id:f,layout:d}=this.props;if(e)return null;var p=(0,i.$)("recharts-bar",o),y=(0,P.uy)(f)?this.id:f;return n.createElement(a.W,{className:p},s&&n.createElement("defs",null,n.createElement(T,{clipPathId:y,xAxisId:l,yAxisId:c})),n.createElement(a.W,{className:"recharts-bar-rectangles",clipPath:s?"url(#clipPath-".concat(y,")"):null},n.createElement(X,{data:t,dataKey:r,background:u,allOtherBarProps:this.props}),n.createElement(Q,this.props)),n.createElement(h,{direction:"horizontal"===d?"y":"x"},this.props.children))}}var er={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!x.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function en(e){var t,{xAxisId:r,yAxisId:i,hide:a,legendType:c,minPointSize:u,activeBar:f,animationBegin:d,animationDuration:p,animationEasing:y,isAnimationActive:v}=(0,s.e)(e,er),{needClip:h}=N(r,i),m=(0,z.WX)(),b=(0,G.r)(),g=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:u,stackId:(0,E.$8)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,u,e.stackId]),w=(0,o.aS)(e.children,O.f),P=(0,D.G)(e=>(0,I.OS)(e,r,i,b,g,w));if("vertical"!==m&&"horizontal"!==m)return null;var x=null==P?void 0:P[0];return t=null==x||null==x.height||null==x.width?0:"vertical"===m?x.height/2:x.width/2,n.createElement(l.zk,{xAxisId:r,yAxisId:i,data:P,dataPointFormatter:ee,errorBarOffset:t},n.createElement(et,K({},e,{layout:m,needClip:h,data:P,xAxisId:r,yAxisId:i,hide:a,legendType:c,minPointSize:u,activeBar:f,animationBegin:d,animationDuration:p,animationEasing:y,isAnimationActive:v})))}function ei(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:c,yAxisTicks:s,stackedData:u,displayedData:f,offset:d,cells:p}=e,y="horizontal"===t?l:o,v=u?y.scale.domain():null,h=(0,E.DW)({numericAxis:y});return f.map((e,f)=>{u?m=(0,E._f)(u[f],v):Array.isArray(m=(0,E.kr)(e,r))||(m=[h,m]);var y=(0,k.l)(n,0)(m[1],f);if("horizontal"===t){var m,b,g,O,w,x,j,[S,C]=[l.scale(m[0]),l.scale(m[1])];b=(0,E.y2)({axis:o,ticks:c,bandSize:a,offset:i.offset,entry:e,index:f}),g=null!=(j=null!=C?C:S)?j:void 0,O=i.size;var A=S-C;if(w=(0,P.M8)(A)?0:A,x={x:b,y:d.top,width:O,height:d.height},Math.abs(y)>0&&Math.abs(w)<Math.abs(y)){var D=(0,P.sA)(w||y)*(Math.abs(y)-Math.abs(w));g-=D,w+=D}}else{var[M,N]=[o.scale(m[0]),o.scale(m[1])];if(b=M,g=(0,E.y2)({axis:l,ticks:s,bandSize:a,offset:i.offset,entry:e,index:f}),O=N-M,w=i.size,x={x:d.left,y:g,width:d.width,height:w},Math.abs(y)>0&&Math.abs(O)<Math.abs(y)){var T=(0,P.sA)(O||y)*(Math.abs(y)-Math.abs(O));O+=T}}return H(H({},e),{},{x:b,y:g,width:O,height:w,value:u?m:m[1],payload:e,background:x,tooltipPosition:{x:b+O/2,y:g+w/2}},p&&p[f]&&p[f].props)})}class ea extends n.PureComponent{render(){return n.createElement(l._S,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(A.y,null),n.createElement(R.A,{legendPayload:V(this.props)}),n.createElement(C.r,{fn:Z,args:this.props}),n.createElement(en,this.props))}}$(ea,"displayName","Bar"),$(ea,"defaultProps",er)},76037:(e,t,r)=>{"use strict";r.d(t,{f:()=>u});var n=r(77331),i=r(12591),a=r(13141),o=r(12738),l=r(1240);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e,t,r){var c,{tick:u,ticks:f,viewBox:d,minTickGap:p,orientation:y,interval:v,tickFormatter:h,unit:m,angle:b}=e;if(!f||!f.length||!u)return[];if((0,n.Et)(v)||a.m.isSsr)return null!=(c=(0,o.pB)(f,(0,n.Et)(v)?v:0))?c:[];var g=[],O="top"===y||"bottom"===y?"width":"height",w=m&&"width"===O?(0,i.P)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},P=(e,n)=>{var a="function"==typeof h?h(e.value,n):e.value;return"width"===O?(0,o.HX)((0,i.P)(a,{fontSize:t,letterSpacing:r}),w,b):(0,i.P)(a,{fontSize:t,letterSpacing:r})[O]},x=f.length>=2?(0,n.sA)(f[1].coordinate-f[0].coordinate):1,E=(0,o.y)(d,x,O);return"equidistantPreserveStart"===v?function(e,t,r,n,i){for(var a,c=(n||[]).slice(),{start:s,end:u}=t,f=0,d=1,p=s;d<=c.length;)if(a=function(){var t,a=null==n?void 0:n[f];if(void 0===a)return{v:(0,l.B)(n,d)};var c=f,y=()=>(void 0===t&&(t=r(a,c)),t),v=a.coordinate,h=0===f||(0,o.zN)(e,v,y,p,u);h||(f=0,p=s,d+=1),h&&(p=v+e*(y()/2+i),f+=d)}())return a.v;return[]}(x,E,P,f,p):("preserveStart"===v||"preserveStartEnd"===v?function(e,t,r,n,i,a){var l=(n||[]).slice(),c=l.length,{start:u,end:f}=t;if(a){var d=n[c-1],p=r(d,c-1),y=e*(d.coordinate+e*p/2-f);l[c-1]=d=s(s({},d),{},{tickCoord:y>0?d.coordinate-y*e:d.coordinate}),(0,o.zN)(e,d.tickCoord,()=>p,u,f)&&(f=d.tickCoord-e*(p/2+i),l[c-1]=s(s({},d),{},{isShow:!0}))}for(var v=a?c-1:c,h=function(t){var n,a=l[t],c=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var d=e*(a.coordinate-e*c()/2-u);l[t]=a=s(s({},a),{},{tickCoord:d<0?a.coordinate-d*e:a.coordinate})}else l[t]=a=s(s({},a),{},{tickCoord:a.coordinate});(0,o.zN)(e,a.tickCoord,c,u,f)&&(u=a.tickCoord+e*(c()/2+i),l[t]=s(s({},a),{},{isShow:!0}))},m=0;m<v;m++)h(m);return l}(x,E,P,f,p,"preserveStartEnd"===v):function(e,t,r,n,i){for(var a=(n||[]).slice(),l=a.length,{start:c}=t,{end:u}=t,f=function(t){var n,f=a[t],d=()=>(void 0===n&&(n=r(f,t)),n);if(t===l-1){var p=e*(f.coordinate+e*d()/2-u);a[t]=f=s(s({},f),{},{tickCoord:p>0?f.coordinate-p*e:f.coordinate})}else a[t]=f=s(s({},f),{},{tickCoord:f.coordinate});(0,o.zN)(e,f.tickCoord,d,c,u)&&(u=f.tickCoord-e*(d()/2+i),a[t]=s(s({},f),{},{isShow:!0}))},d=l-1;d>=0;d--)f(d);return a}(x,E,P,f,p)).filter(e=>e.isShow)}},77687:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.styleSingleton=void 0;var n=r(36967);t.styleSingleton=function(){var e=(0,n.styleHookSingleton)();return function(t){return e(t.styles,t.dynamic),null}}},94643:(e,t,r)=>{"use strict";r.d(t,{W:()=>g});var n=r(82015),i=r(79486),a=r(59009),o=r(34797),l=r(42714),c=r(68181),s=r(78813),u=r(81265),f=["children"],d=["dangerouslySetInnerHTML","ticks"];function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function h(e){var t=(0,o.j)(),r=(0,n.useMemo)(()=>{var{children:t}=e;return v(e,f)},[e]),i=(0,o.G)(e=>(0,c.Rl)(e,r.id)),a=r===i;return((0,n.useEffect)(()=>(t((0,l.Vi)(r)),()=>{t((0,l.MC)(r))}),[r,t]),a)?e.children:null}var m=e=>{var{xAxisId:t,className:r}=e,l=(0,o.G)(s.c2),f=(0,u.r)(),p="xAxis",h=(0,o.G)(e=>(0,c.iV)(e,p,t,f)),m=(0,o.G)(e=>(0,c.Zi)(e,p,t,f)),b=(0,o.G)(e=>(0,c.Lw)(e,t)),g=(0,o.G)(e=>(0,c.L$)(e,t));if(null==b||null==g)return null;var{dangerouslySetInnerHTML:O,ticks:w}=e,P=v(e,d);return n.createElement(a.u,y({},P,{scale:h,x:g.x,y:g.y,width:b.width,height:b.height,className:(0,i.$)("recharts-".concat(p," ").concat(p),r),viewBox:l,ticks:m}))},b=e=>{var t,r,i,a,o;return n.createElement(h,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter},n.createElement(m,e))};class g extends n.Component{render(){return n.createElement(b,this.props)}}p(g,"displayName","XAxis"),p(g,"defaultProps",{allowDataOverflow:c.PU.allowDataOverflow,allowDecimals:c.PU.allowDecimals,allowDuplicatedCategory:c.PU.allowDuplicatedCategory,height:c.PU.height,hide:!1,mirror:c.PU.mirror,orientation:c.PU.orientation,padding:c.PU.padding,reversed:c.PU.reversed,scale:c.PU.scale,tickCount:c.PU.tickCount,type:c.PU.type,xAxisId:0})}};