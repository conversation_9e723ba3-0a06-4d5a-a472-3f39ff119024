"use strict";exports.id=4211,exports.ids=[4211],exports.modules={27756:(e,o,r)=>{r.a(e,async(e,l)=>{try{r.d(o,{A:()=>b});var n=r(8732),s=r(82015),a=r(66909),t=r(64299),c=r(75975),i=r(3001),d=e([a,t,i]);[a,t,i]=d.then?(await d)():d;let h=[{value:"sendMessage",label:"\uD83D\uDCAC Send Message",category:"Message"},{value:"sendEmbed",label:"\uD83D\uDCCB Send Embed",category:"Message"},{value:"editMessage",label:"✏️ Edit Message",category:"Message"},{value:"deleteMessage",label:"\uD83D\uDDD1️ Delete Message",category:"Message"},{value:"addReaction",label:"\uD83D\uDC4D Add Reaction",category:"Message"},{value:"removeReaction",label:"\uD83D\uDC4E Remove Reaction",category:"Message"},{value:"addRole",label:"\uD83C\uDFAD Add Role",category:"Roles"},{value:"removeRole",label:"\uD83C\uDFAD Remove Role",category:"Roles"},{value:"kickUser",label:"\uD83D\uDC62 Kick User",category:"Moderation"},{value:"banUser",label:"\uD83D\uDD28 Ban User",category:"Moderation"},{value:"timeoutUser",label:"⏰ Timeout User",category:"Moderation"},{value:"unbanUser",label:"\uD83D\uDD13 Unban User",category:"Moderation"},{value:"createChannel",label:"\uD83D\uDCFA Create Channel",category:"Channel"},{value:"deleteChannel",label:"\uD83D\uDDD1️ Delete Channel",category:"Channel"},{value:"lockChannel",label:"\uD83D\uDD12 Lock Channel",category:"Channel"},{value:"unlockChannel",label:"\uD83D\uDD13 Unlock Channel",category:"Channel"},{value:"sendDM",label:"\uD83D\uDCEC Send DM",category:"Message"},{value:"createThread",label:"\uD83E\uDDF5 Create Thread",category:"Channel"},{value:"pinMessage",label:"\uD83D\uDCCC Pin Message",category:"Message"},{value:"unpinMessage",label:"\uD83D\uDCCC Unpin Message",category:"Message"}],m={user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.tag}",description:"User Tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{user.mention}",description:"User Mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{user.avatar}",description:"Avatar URL",icon:"\uD83D\uDDBC️"},{name:"{user.createdAt}",description:"Account Creation Date",icon:"\uD83D\uDCC5"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"},{name:"{user.roles}",description:"User Roles",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions",icon:"\uD83D\uDD10"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel Mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel Topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{channel.createdAt}",description:"Channel Creation Date",icon:"\uD83D\uDCC5"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server Icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{server.createdAt}",description:"Server Creation Date",icon:"\uD83D\uDCC5"},{name:"{server.owner}",description:"Server Owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server Boost Level",icon:"\uD83D\uDE80"},{name:"{server.boostCount}",description:"Server Boost Count",icon:"\uD83D\uDC8E"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83C\uDD94"},{name:"{message.content}",description:"Message Content",icon:"\uD83D\uDCAC"},{name:"{message.author}",description:"Message Author",icon:"\uD83D\uDC64"},{name:"{message.channel}",description:"Message Channel",icon:"\uD83D\uDCFA"},{name:"{message.createdAt}",description:"Message Creation Date",icon:"\uD83D\uDCC5"},{name:"{message.editedAt}",description:"Message Edit Date",icon:"✏️"},{name:"{message.reactions}",description:"Message Reactions",icon:"\uD83D\uDC4D"},{name:"{message.attachments}",description:"Message Attachments",icon:"\uD83D\uDCCE"}],api:[{name:"{response.data}",description:"API Response Data",icon:"\uD83D\uDCCA"},{name:"{response.status}",description:"HTTP Status Code",icon:"\uD83D\uDD22"},{name:"{response.headers}",description:"Response Headers",icon:"\uD83D\uDCCB"},{name:"{response.message}",description:"Response Message",icon:"\uD83D\uDCAC"},{name:"{response.error}",description:"Error Message",icon:"❌"}],random:[{name:"{random.number}",description:"Random Number (1-100)",icon:"\uD83C\uDFB2"},{name:"{random.uuid}",description:"Random UUID",icon:"\uD83C\uDD94"},{name:"{random.choice}",description:"Random Choice from Array",icon:"\uD83C\uDFAF"},{name:"{random.color}",description:"Random Hex Color",icon:"\uD83C\uDFA8"}],date:[{name:"{date.now}",description:"Current Date/Time",icon:"⏰"},{name:"{date.today}",description:"Today's Date",icon:"\uD83D\uDCC5"},{name:"{date.timestamp}",description:"Unix Timestamp",icon:"\uD83D\uDD50"},{name:"{date.iso}",description:"ISO Date String",icon:"\uD83D\uDCDD"}]},x=(0,s.memo)(({data:e,selected:o,id:r,updateNodeData:l})=>{let{currentScheme:d}=(0,i.DP)(),{isOpen:x,onOpen:b,onClose:u}=(0,t.useDisclosure)(),[p,g]=(0,s.useState)(()=>({embed:{fields:[],author:{name:""},footer:{text:""}},...e})),[j,C]=(0,s.useState)(!1),[v,S]=(0,s.useState)(!1),[f,y]=(0,s.useState)(null),[k,T]=(0,s.useState)(!1),F=e=>{g(o=>({...o,...e}))},D=e=>h.find(o=>o.value===e)?.label||e,M=e=>{navigator.clipboard.writeText(e)},R=async()=>{if(!f&&!k){T(!0);try{let e=await fetch("/api/admin/experimental/addon-builder/guild-data");if(e.ok){let o=await e.json();y({channels:o.channels,roles:o.roles,members:o.members})}}catch(e){}finally{T(!1)}}};(0,s.useEffect)(()=>{x&&R()},[x]);let I=(e,o,r)=>{let l=[...p.embed?.fields||[]];l[e]={...l[e],[o]:r},F({embed:{...p.embed,fields:l}})},z=e=>{let o=(p.embed?.fields||[]).filter((o,r)=>r!==e);F({embed:{...p.embed,fields:o}})};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(t.Box,{bg:d.colors.surface,border:`2px solid ${o?"#a855f7":d.colors.border}`,borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,n.jsx)(a.Handle,{type:"target",position:a.Position.Top,style:{background:"#a855f7",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,n.jsxs)(t.VStack,{spacing:1,align:"stretch",children:[(0,n.jsxs)(t.HStack,{justify:"space-between",align:"center",children:[(0,n.jsxs)(t.HStack,{spacing:1,children:[(0,n.jsx)(t.Box,{bg:"purple.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,n.jsx)(c.x_j,{})}),(0,n.jsx)(t.Text,{fontSize:"xs",fontWeight:"bold",color:d.colors.text,children:"Action"})]}),(0,n.jsx)(t.IconButton,{icon:(0,n.jsx)(c.VSk,{}),size:"xs",variant:"ghost",onClick:b,"aria-label":"Configure action"})]}),(0,n.jsx)(t.Box,{children:(0,n.jsxs)(t.HStack,{spacing:1,children:[p.actionType&&(0,n.jsx)(t.Text,{fontSize:"xs",children:(e=>{let o=h.find(o=>o.value===e);return o?.label.split(" ")[0]||"\uD83C\uDFAF"})(p.actionType)}),(0,n.jsx)(t.Text,{fontSize:"xs",color:d.colors.text,noOfLines:1,children:p.actionType?D(p.actionType).split(" ").slice(1).join(" "):"Select Action"})]})}),p.message&&(0,n.jsx)(t.Box,{children:(0,n.jsx)(t.Text,{fontSize:"xs",color:d.colors.textSecondary,noOfLines:1,children:p.message.length>20?p.message.substring(0,20)+"...":p.message})}),(0,n.jsxs)(t.HStack,{spacing:1,flexWrap:"wrap",children:[p.channel&&(0,n.jsxs)(t.Badge,{size:"xs",colorScheme:"purple",children:["#",p.channel]}),p.role&&(0,n.jsxs)(t.Badge,{size:"xs",colorScheme:"purple",children:["@",p.role]}),p.embed?.title&&(0,n.jsx)(t.Badge,{size:"xs",colorScheme:"blue",children:"\uD83D\uDCCB Embed"})]})]})]}),(0,n.jsxs)(t.Modal,{isOpen:x,onClose:()=>{l&&r&&l(r,p),u()},size:"4xl",children:[(0,n.jsx)(t.ModalOverlay,{bg:"blackAlpha.600"}),(0,n.jsxs)(t.ModalContent,{bg:d.colors.background,border:"2px solid",borderColor:"purple.400",maxW:"1200px",children:[(0,n.jsx)(t.ModalHeader,{color:d.colors.text,children:"\uD83C\uDFAF Configure Action"}),(0,n.jsx)(t.ModalCloseButton,{}),(0,n.jsx)(t.ModalBody,{pb:6,children:(0,n.jsxs)(t.VStack,{spacing:6,align:"stretch",children:[(0,n.jsxs)(t.Box,{children:[(0,n.jsxs)(t.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,n.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Available Variables"}),(0,n.jsxs)(t.Button,{size:"sm",variant:"ghost",leftIcon:j?(0,n.jsx)(c._NO,{}):(0,n.jsx)(c.Vap,{}),onClick:()=>C(!j),children:[j?"Hide":"Show"," Variables"]})]}),(0,n.jsxs)(t.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,n.jsx)(t.AlertIcon,{}),(0,n.jsx)(t.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your actions! Click any variable below to copy it. Variables are replaced with actual values when your addon runs."})]}),(0,n.jsx)(t.Collapse,{in:j,animateOpacity:!0,children:(0,n.jsx)(t.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,n.jsx)(t.Accordion,{allowMultiple:!0,children:Object.entries(m).map(([e,o])=>(0,n.jsxs)(t.AccordionItem,{border:"none",children:[(0,n.jsxs)(t.AccordionButton,{px:0,py:2,children:[(0,n.jsx)(t.Box,{flex:"1",textAlign:"left",children:(0,n.jsxs)(t.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,textTransform:"capitalize",children:[e," Variables"]})}),(0,n.jsx)(t.AccordionIcon,{})]}),(0,n.jsx)(t.AccordionPanel,{px:0,py:2,children:(0,n.jsx)(t.VStack,{spacing:2,align:"stretch",children:o.map(e=>(0,n.jsxs)(t.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>M(e.name),children:[(0,n.jsx)(t.Text,{fontSize:"sm",children:e.icon}),(0,n.jsx)(t.Code,{fontSize:"xs",colorScheme:"blue",children:e.name}),(0,n.jsx)(t.Text,{fontSize:"xs",color:d.colors.textSecondary,flex:"1",children:e.description}),(0,n.jsx)(t.IconButton,{icon:(0,n.jsx)(c.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),M(e.name)}})]},e.name))})})]},e))})})})]}),(0,n.jsx)(t.Divider,{}),(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Action Type"}),(0,n.jsx)(t.Select,{value:p.actionType||"",onChange:e=>F({actionType:e.target.value}),placeholder:"Select an action type",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:Object.entries(h.reduce((e,o)=>(e[o.category]||(e[o.category]=[]),e[o.category].push(o),e),{})).map(([e,o])=>(0,n.jsx)("optgroup",{label:e,children:o.map(e=>(0,n.jsx)("option",{value:e.value,children:e.label},e.value))},e))})]}),p.actionType&&(0,n.jsx)(n.Fragment,{children:"sendEmbed"===p.actionType?(0,n.jsxs)(t.Tabs,{variant:"enclosed",colorScheme:"purple",children:[(0,n.jsxs)(t.TabList,{children:[(0,n.jsx)(t.Tab,{children:"Embed Builder"}),(0,n.jsx)(t.Tab,{children:"Preview"})]}),(0,n.jsxs)(t.TabPanels,{children:[(0,n.jsx)(t.TabPanel,{children:(0,n.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Channel"}),(0,n.jsxs)(t.VStack,{spacing:2,align:"stretch",children:[(0,n.jsx)(t.Select,{value:p.channel||"",onChange:e=>F({channel:e.target.value}),placeholder:k?"Loading channels...":"Select a channel",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,isDisabled:k,children:f?.channels.filter(e=>"text"===e.type).map(e=>(0,n.jsxs)("option",{value:e.name,children:["#",e.name]},e.id))}),(0,n.jsx)(t.Input,{value:p.channel||"",onChange:e=>F({channel:e.target.value}),placeholder:"Or type: general or {channel.name}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]}),(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Message Content (appears above embed)"}),(0,n.jsx)(t.Textarea,{value:p.message||"",onChange:e=>F({message:e.target.value}),placeholder:"Hello {user.username}! This text appears above the embed...",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"100px"})]}),(0,n.jsxs)(t.SimpleGrid,{columns:2,spacing:4,children:[(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Embed Title"}),(0,n.jsx)(t.Input,{value:p.embed?.title||"",onChange:e=>F({embed:{...p.embed,title:e.target.value}}),placeholder:"Welcome to {server.name}!",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]}),(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Embed Color"}),(0,n.jsxs)(t.VStack,{spacing:2,align:"stretch",children:[(0,n.jsxs)(t.HStack,{spacing:2,children:[(0,n.jsx)(t.Input,{type:"color",value:p.embed?.color||"#5865F2",onChange:e=>F({embed:{...p.embed,color:e.target.value}}),w:"60px",h:"40px",p:1,bg:d.colors.background,borderColor:d.colors.border}),(0,n.jsx)(t.Input,{value:p.embed?.color||"",onChange:e=>F({embed:{...p.embed,color:e.target.value}}),placeholder:"#5865F2",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,flex:"1"})]}),(0,n.jsx)(t.HStack,{spacing:1,flexWrap:"wrap",children:["#5865F2","#57F287","#FEE75C","#EB459E","#ED4245","#FF6B35","#00ADB5","#9B59B6"].map(e=>(0,n.jsx)(t.Button,{size:"xs",bg:e,w:"30px",h:"20px",minW:"30px",p:0,onClick:()=>F({embed:{...p.embed,color:e}}),_hover:{transform:"scale(1.1)"},border:"1px solid",borderColor:d.colors.border},e))})]})]})]}),(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Embed Description"}),(0,n.jsx)(t.Textarea,{value:p.embed?.description||"",onChange:e=>F({embed:{...p.embed,description:e.target.value}}),placeholder:"This is the description that appears inside the embed...",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"100px"})]}),(0,n.jsxs)(t.SimpleGrid,{columns:2,spacing:4,children:[(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Thumbnail URL"}),(0,n.jsx)(t.Input,{value:p.embed?.thumbnail||"",onChange:e=>F({embed:{...p.embed,thumbnail:e.target.value}}),placeholder:"https://example.com/image.png",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]}),(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Image URL"}),(0,n.jsx)(t.Input,{value:p.embed?.image||"",onChange:e=>F({embed:{...p.embed,image:e.target.value}}),placeholder:"https://example.com/image.png",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),(0,n.jsxs)(t.Box,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Author"}),(0,n.jsxs)(t.VStack,{spacing:2,align:"stretch",children:[(0,n.jsx)(t.Input,{value:p.embed?.author?.name||"",onChange:e=>F({embed:{...p.embed,author:{...p.embed?.author,name:e.target.value}}}),placeholder:"Author name",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,n.jsxs)(t.SimpleGrid,{columns:2,spacing:2,children:[(0,n.jsx)(t.Input,{value:p.embed?.author?.url||"",onChange:e=>F({embed:{...p.embed,author:{...p.embed?.author,url:e.target.value}}}),placeholder:"Author URL",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,n.jsx)(t.Input,{value:p.embed?.author?.iconUrl||"",onChange:e=>F({embed:{...p.embed,author:{...p.embed?.author,iconUrl:e.target.value}}}),placeholder:"Author icon URL",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]})]}),(0,n.jsxs)(t.Box,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Footer"}),(0,n.jsxs)(t.VStack,{spacing:2,align:"stretch",children:[(0,n.jsx)(t.Input,{value:p.embed?.footer?.text||"",onChange:e=>F({embed:{...p.embed,footer:{...p.embed?.footer,text:e.target.value}}}),placeholder:"Footer text",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,n.jsx)(t.Input,{value:p.embed?.footer?.iconUrl||"",onChange:e=>F({embed:{...p.embed,footer:{...p.embed?.footer,iconUrl:e.target.value}}}),placeholder:"Footer icon URL",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),(0,n.jsxs)(t.Box,{children:[(0,n.jsxs)(t.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,mb:0,children:"Embed Fields"}),(0,n.jsx)(t.Button,{size:"sm",leftIcon:(0,n.jsx)(c.GGD,{}),onClick:()=>{let e=p.embed?.fields||[];F({embed:{...p.embed,fields:[...e,{name:"",value:"",inline:!1}]}})},colorScheme:"blue",children:"Add Field"})]}),(0,n.jsx)(t.VStack,{spacing:3,align:"stretch",children:p.embed?.fields?.map((e,o)=>(0,n.jsxs)(t.Box,{p:3,bg:d.colors.surface,borderRadius:"md",border:"1px solid",borderColor:d.colors.border,children:[(0,n.jsxs)(t.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,n.jsxs)(t.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:["Field ",o+1]}),(0,n.jsx)(t.IconButton,{icon:(0,n.jsx)(c.IXo,{}),size:"xs",colorScheme:"red",variant:"ghost",onClick:()=>z(o),"aria-label":"Remove field"})]}),(0,n.jsxs)(t.VStack,{spacing:2,align:"stretch",children:[(0,n.jsx)(t.Input,{value:e.name,onChange:e=>I(o,"name",e.target.value),placeholder:"Field name",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,n.jsx)(t.Textarea,{value:e.value,onChange:e=>I(o,"value",e.target.value),placeholder:"Field value",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"}),(0,n.jsxs)(t.HStack,{children:[(0,n.jsx)(t.Switch,{isChecked:e.inline||!1,onChange:e=>I(o,"inline",e.target.checked),colorScheme:"purple"}),(0,n.jsx)(t.Text,{fontSize:"sm",color:d.colors.text,children:"Display inline"})]})]})]},o))})]}),(0,n.jsxs)(t.HStack,{children:[(0,n.jsx)(t.Switch,{isChecked:p.embed?.timestamp||!1,onChange:e=>F({embed:{...p.embed,timestamp:e.target.checked}}),colorScheme:"purple"}),(0,n.jsx)(t.Text,{fontSize:"sm",color:d.colors.text,children:"Show current timestamp"})]})]})}),(0,n.jsx)(t.TabPanel,{children:(0,n.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,n.jsx)(t.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Embed Preview"}),(0,n.jsxs)(t.Alert,{status:"info",borderRadius:"md",children:[(0,n.jsx)(t.AlertIcon,{}),(0,n.jsx)(t.AlertDescription,{fontSize:"sm",children:"This shows your message content (above) and embed (below) as they will appear in Discord. Variables will be replaced with actual values when sent."})]}),(()=>{let e=p.embed||{};return(0,n.jsxs)(t.VStack,{spacing:3,align:"stretch",maxW:"500px",children:[p.message&&(0,n.jsxs)(t.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:3,children:[(0,n.jsx)(t.Text,{fontSize:"sm",color:d.colors.text,fontWeight:"medium",children:"\uD83D\uDCE9 Message Content:"}),(0,n.jsx)(t.Text,{fontSize:"sm",color:d.colors.text,mt:1,children:p.message})]}),(0,n.jsxs)(t.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,borderLeft:`4px solid ${e.color||"#5865F2"}`,children:[e.author?.name&&(0,n.jsxs)(t.HStack,{spacing:2,mb:2,children:[e.author.iconUrl&&(0,n.jsx)(t.Box,{w:6,h:6,borderRadius:"full",bg:"gray.300",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"xs",children:"\uD83D\uDC64"}),(0,n.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:e.author.name})]}),e.title&&(0,n.jsx)(t.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,mb:2,children:e.title}),e.description&&(0,n.jsx)(t.Text,{fontSize:"sm",color:d.colors.textSecondary,mb:3,children:e.description}),e.fields&&e.fields.length>0&&(0,n.jsx)(t.VStack,{spacing:2,align:"stretch",mb:3,children:e.fields.map((e,o)=>(0,n.jsxs)(t.Box,{children:[(0,n.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:e.name}),(0,n.jsx)(t.Text,{fontSize:"sm",color:d.colors.textSecondary,children:e.value})]},o))}),e.footer?.text&&(0,n.jsxs)(t.HStack,{spacing:2,mt:3,pt:2,borderTop:"1px solid",borderColor:d.colors.border,children:[e.footer.iconUrl&&(0,n.jsx)(t.Box,{w:4,h:4,borderRadius:"full",bg:"gray.300",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"xs",children:"ℹ️"}),(0,n.jsx)(t.Text,{fontSize:"xs",color:d.colors.textSecondary,children:e.footer.text})]}),e.timestamp&&(0,n.jsxs)(t.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:2,children:["\uD83D\uDD52 ",new Date().toLocaleString()]})]})]})})()]})})]})]}):(0,n.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[("sendMessage"===p.actionType||"sendDM"===p.actionType)&&(0,n.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Message Content"}),(0,n.jsx)(t.Textarea,{value:p.message||"",onChange:e=>F({message:e.target.value}),placeholder:"Hello {user.username}! Welcome to {server.name}!",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"100px"})]}),"sendDM"!==p.actionType&&(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Channel"}),(0,n.jsxs)(t.VStack,{spacing:2,align:"stretch",children:[(0,n.jsx)(t.Select,{value:p.channel||"",onChange:e=>F({channel:e.target.value}),placeholder:k?"Loading channels...":"Select a channel",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,isDisabled:k,children:f?.channels.filter(e=>"text"===e.type).map(e=>(0,n.jsxs)("option",{value:e.name,children:["#",e.name]},e.id))}),(0,n.jsx)(t.Input,{value:p.channel||"",onChange:e=>F({channel:e.target.value}),placeholder:"Or type: general or {channel.name}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]})]}),("addRole"===p.actionType||"removeRole"===p.actionType)&&(0,n.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Role Name"}),(0,n.jsxs)(t.VStack,{spacing:2,align:"stretch",children:[(0,n.jsx)(t.Select,{value:p.role||"",onChange:e=>F({role:e.target.value}),placeholder:k?"Loading roles...":"Select a role",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,isDisabled:k,children:f?.roles.map(e=>(0,n.jsxs)("option",{value:e.name,children:["@",e.name]},e.id))}),(0,n.jsx)(t.Input,{value:p.role||"",onChange:e=>F({role:e.target.value}),placeholder:"Or type: Member or {user.role}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]}),(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Reason"}),(0,n.jsx)(t.Input,{value:p.reason||"",onChange:e=>F({reason:e.target.value}),placeholder:"Role updated by bot",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),("kickUser"===p.actionType||"banUser"===p.actionType)&&(0,n.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"User"}),(0,n.jsxs)(t.VStack,{spacing:2,align:"stretch",children:[(0,n.jsx)(t.Select,{value:p.user||"",onChange:e=>F({user:e.target.value}),placeholder:k?"Loading members...":"Select a user",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,isDisabled:k,children:f?.members.map(e=>(0,n.jsxs)("option",{value:e.username,children:[e.displayName," (@",e.username,")"]},e.id))}),(0,n.jsx)(t.Input,{value:p.user||"",onChange:e=>F({user:e.target.value}),placeholder:"Or type: username or {user.id}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]}),(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Reason"}),(0,n.jsx)(t.Input,{value:p.reason||"",onChange:e=>F({reason:e.target.value}),placeholder:"Violation of server rules",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]}),"banUser"===p.actionType&&(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Delete Message History"}),(0,n.jsx)(t.Switch,{isChecked:p.deleteMessages||!1,onChange:e=>F({deleteMessages:e.target.checked}),colorScheme:"purple"})]})]}),"timeoutUser"===p.actionType&&(0,n.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"User"}),(0,n.jsxs)(t.VStack,{spacing:2,align:"stretch",children:[(0,n.jsx)(t.Select,{value:p.user||"",onChange:e=>F({user:e.target.value}),placeholder:k?"Loading members...":"Select a user",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,isDisabled:k,children:f?.members.map(e=>(0,n.jsxs)("option",{value:e.username,children:[e.displayName," (@",e.username,")"]},e.id))}),(0,n.jsx)(t.Input,{value:p.user||"",onChange:e=>F({user:e.target.value}),placeholder:"Or type: username or {user.id}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]}),(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Duration (minutes)"}),(0,n.jsxs)(t.NumberInput,{value:p.duration||10,onChange:e=>F({duration:parseInt(e)||10}),min:1,max:40320,children:[(0,n.jsx)(t.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,n.jsxs)(t.NumberInputStepper,{children:[(0,n.jsx)(t.NumberIncrementStepper,{}),(0,n.jsx)(t.NumberDecrementStepper,{})]})]})]}),(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Reason"}),(0,n.jsx)(t.Input,{value:p.reason||"",onChange:e=>F({reason:e.target.value}),placeholder:"Timeout for spam",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),"addReaction"===p.actionType&&(0,n.jsx)(t.VStack,{spacing:4,align:"stretch",children:(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Reaction (emoji)"}),(0,n.jsx)(t.Input,{value:p.reaction||"",onChange:e=>F({reaction:e.target.value}),placeholder:"\uD83D\uDC4D or :thumbsup:",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})}),"createChannel"===p.actionType&&(0,n.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Channel Name"}),(0,n.jsx)(t.Input,{value:p.channelName||"",onChange:e=>F({channelName:e.target.value}),placeholder:"new-channel",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]}),(0,n.jsxs)(t.FormControl,{children:[(0,n.jsx)(t.FormLabel,{color:d.colors.text,children:"Channel Type"}),(0,n.jsxs)(t.Select,{value:p.channelType||"text",onChange:e=>F({channelType:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:[(0,n.jsx)("option",{value:"text",children:"Text Channel"}),(0,n.jsx)("option",{value:"voice",children:"Voice Channel"}),(0,n.jsx)("option",{value:"category",children:"Category"})]})]})]})]})}),(0,n.jsx)(t.Button,{colorScheme:"purple",onClick:()=>{e.actionType=p.actionType,e.message=p.message,e.channel=p.channel,e.role=p.role,e.user=p.user,e.embed=p.embed,e.reason=p.reason,e.duration=p.duration,e.deleteMessages=p.deleteMessages,e.reaction=p.reaction,e.channelName=p.channelName,e.channelType=p.channelType,e.label=p.actionType?D(p.actionType):"Action",u()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});x.displayName="ActionNode";let b=x;l()}catch(e){l(e)}})}};