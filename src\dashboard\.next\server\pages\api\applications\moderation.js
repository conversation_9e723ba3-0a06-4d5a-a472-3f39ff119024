"use strict";(()=>{var e={};e.id=6413,e.ids=[6413],e.modules={9244:(e,t,r)=>{r.r(t),r.d(t,{config:()=>h,default:()=>m,routeModule:()=>g});var s={};r.r(s),r.d(s,{default:()=>f});var a=r(93433),n=r(20264),i=r(20584),o=r(15806),u=r(94506),d=r(12518),l=r(98580);let p=null;async function c(){if(p)return p;try{let e=await d.MongoClient.connect(l.dashboardConfig.database.url,{...l.dashboardConfig.database.options});return p=e,e}catch(e){throw e}}async function f(e,t){let r=await (0,o.getServerSession)(e,t,u.authOptions);if(!r)return t.status(401).json({error:"Unauthorized"});if("POST"!==e.method&&!r.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});try{let r=(await c()).db(l.dashboardConfig.database.name).collection("applications");if("POST"===e.method){let{userId:s,username:a,answers:n,quizAnswers:i,timezone:o,age:u,hoursPerWeek:d,extraInfo:l}=e.body;if(!s||!n?.statement||!i||!o||!u||!d)return t.status(400).json({error:"Missing required fields"});try{if(await r.findOne({userId:s}))return t.status(400).json({error:"You have already submitted an application"});let e=await r.insertOne({userId:s,username:a,answers:n,quizAnswers:i,timezone:o,age:u,hoursPerWeek:d,extraInfo:l,date:new Date,status:"pending"});return t.status(200).json({success:!0,id:e.insertedId})}catch(e){return t.status(500).json({error:"Failed to save application"})}}if("GET"===e.method){let e=await r.find().sort({date:-1}).toArray();return t.status(200).json(e)}if("PUT"===e.method){let{applicationId:s}=e.query,{status:a}=e.body;if(!s||"string"!=typeof s||!a)return t.status(400).json({error:"Missing required fields"});let n=await r.updateOne({_id:new d.ObjectId(s)},{$set:{status:a}});if(0===n.matchedCount)return t.status(404).json({error:"Application not found"});return t.status(200).json({message:"Application updated successfully"})}if("DELETE"===e.method){let{applicationId:s}=e.query;if(!s||"string"!=typeof s)return t.status(400).json({error:"Missing application ID"});let a=await r.deleteOne({_id:new d.ObjectId(s)});if(0===a.deletedCount)return t.status(404).json({error:"Application not found"});return t.status(200).json({message:"Application deleted successfully"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let m=(0,i.M)(s,"default"),h=(0,i.M)(s,"config"),g=new a.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/applications/moderation",pathname:"/api/applications/moderation",bundlePath:"",filename:""},userland:s})},12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(9244));module.exports=s})();