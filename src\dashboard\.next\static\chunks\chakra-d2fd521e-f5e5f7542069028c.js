"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2457],{56084:(e,t,n)=>{n.d(t,{Px:()=>_,vG:()=>i,c9:()=>u.c,ic:()=>c,I5:()=>l,j1:()=>p.j,ML:()=>a,Xb:()=>v,Sp:()=>S,wf:()=>h,cC:()=>L,$$:()=>T,SV:()=>j,jz:()=>x,ZC:()=>I,UQ:()=>m,Z3:()=>P,w5:()=>b});var r=n(79435),o=n(94285),u=n(65507);function a(e,t,n,r){let a=(0,u.c)(n);return(0,o.useEffect)(()=>{let o="function"==typeof e?e():e??document;if(n&&o)return o.addEventListener(t,a,r),()=>{o.removeEventListener(t,a,r)}},[t,e,r,a,n]),()=>{let n="function"==typeof e?e():e??document;n?.removeEventListener(t,a,r)}}function i(e){let{isOpen:t,ref:n}=e,[u,i]=(0,o.useState)(t),[c,l]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{c||(i(t),l(!0))},[t,c,u]),a(()=>n.current,"animationend",()=>{i(t)}),{present:!(!t&&!u),onComplete(){let e=new((0,r.mD)(n.current)).CustomEvent("animationend",{bubbles:!0});n.current?.dispatchEvent(e)}}}function c(e){let{value:t,defaultValue:n,onChange:r,shouldUpdate:a=(e,t)=>e!==t}=e,i=(0,u.c)(r),c=(0,u.c)(a),[l,s]=(0,o.useState)(n),f=void 0!==t,d=f?t:l,p=(0,u.c)(e=>{let t="function"==typeof e?e(d):e;c(d,t)&&(f||s(t),i(t))},[f,i,d,c]);return[d,p]}function l(e={}){let{onChange:t,precision:n,defaultValue:a,value:i,step:c=1,min:p=Number.MIN_SAFE_INTEGER,max:m=Number.MAX_SAFE_INTEGER,keepWithinRange:b=!0}=e,v=(0,u.c)(t),[E,h]=(0,o.useState)(()=>null==a?"":d(a,c,n)??""),w=void 0!==i,g=w?i:E,k=f(s(g),c),y=n??k,C=(0,o.useCallback)(e=>{e!==g&&(w||h(e.toString()),v?.(e.toString(),s(e)))},[v,w,g]),S=(0,o.useCallback)(e=>{let t=e;return b&&(t=(0,r.L3)(t,p,m)),(0,r.QX)(t,y)},[y,b,m,p]),L=(0,o.useCallback)((e=c)=>{let t;C(S(""===g?s(e):s(g)+e))},[S,c,C,g]),T=(0,o.useCallback)((e=c)=>{let t;C(S(""===g?s(-e):s(g)-e))},[S,c,C,g]),_=(0,o.useCallback)(()=>{let e;C(null==a?"":d(a,c,n)??p)},[a,n,c,C,p]),j=(0,o.useCallback)(e=>{C(d(e,c,y)??p)},[y,c,C,p]),x=s(g),M=x>m||x<p;return{isOutOfRange:M,isAtMax:x===m,isAtMin:x===p,precision:y,value:g,valueAsNumber:x,update:C,reset:_,increment:L,decrement:T,clamp:S,cast:j,setValue:h}}function s(e){return parseFloat(e.toString().replace(/[^\w.-]+/g,""))}function f(e,t){return Math.max((0,r.FZ)(t),(0,r.FZ)(e))}function d(e,t,n){let o=s(e);if(Number.isNaN(o))return;let u=f(o,t);return(0,r.QX)(o,n??u)}n(76338);var p=n(95845);let m=globalThis?.document?o.useLayoutEffect:o.useEffect,b=(e,t)=>{let n=(0,o.useRef)(!1),r=(0,o.useRef)(!1);(0,o.useEffect)(()=>{if(n.current&&r.current)return e();r.current=!0},t),(0,o.useEffect)(()=>(n.current=!0,()=>{n.current=!1}),[])};function v(e,t){let{shouldFocus:n,visible:o,focusRef:u}=t,a=n&&!o;b(()=>{let t;if(!a||function(e){let t=e.current;if(!t)return!1;let n=(0,r.bq)(t);return!(!n||t.contains(n))&&!!(0,r.AO)(n)}(e))return;let n=u?.current||e.current;if(n)return t=requestAnimationFrame(()=>{n.focus({preventScroll:!0})}),()=>{cancelAnimationFrame(t)}},[a,e,u])}let E={preventScroll:!0,shouldFocus:!1};function h(e,t=E){let{focusRef:n,preventScroll:u,shouldFocus:i,visible:c}=t,l="current"in e?e.current:e,s=i&&c,f=(0,o.useRef)(s),d=(0,o.useRef)(c);m(()=>{!d.current&&c&&(f.current=s),d.current=c},[c,s]);let p=(0,o.useCallback)(()=>{if(c&&l&&f.current&&(f.current=!1,!l.contains(document.activeElement)))if(n?.current)requestAnimationFrame(()=>{n.current?.focus({preventScroll:u})});else{let e=(0,r.ep)(l);e.length>0&&requestAnimationFrame(()=>{e[0].focus({preventScroll:u})})}},[c,u,l,n]);b(()=>{p()},[p]),a(l,"transitionend",p)}let w=()=>"undefined"!=typeof window,g=e=>w()&&e.test(navigator.vendor),k=e=>w()&&e.test(function(){let e=navigator.userAgentData;return e?.platform??navigator.platform}()),y=()=>k(/mac|iphone|ipad|ipod/i),C=()=>y()&&g(/apple/i);function S(e){let{ref:t,elements:n,enabled:r}=e,o=()=>t.current?.ownerDocument??document;a(o,"pointerdown",e=>{if(!C()||!r)return;let u=e.composedPath?.()?.[0]??e.target,a=(n??[t]).some(e=>{let t="current"in e?e.current:e;return t?.contains(u)||t===u});o().activeElement!==u&&a&&(e.preventDefault(),u.focus())})}function L(e,...t){let n=function(e,t){let n=(0,o.useId)();return(0,o.useMemo)(()=>e||[void 0,n].filter(Boolean).join("-"),[e,void 0,n])}(e);return(0,o.useMemo)(()=>t.map(e=>`${e}-${n}`),[n,t])}function T(e,t){let n=(0,u.c)(e);(0,o.useEffect)(()=>{let e=null;return null!==t&&(e=window.setInterval(()=>n(),t)),()=>{e&&window.clearInterval(e)}},[t,n])}function _(...e){return t=>{e.forEach(e=>{!function(e,t){if(null!=e){if("function"==typeof e)return e(t);try{e.current=t}catch(n){throw Error(`Cannot assign value '${t}' to ref '${e}'`)}}}(e,t)})}}function j(...e){return(0,o.useMemo)(()=>_(...e),e)}function x(e){let{ref:t,handler:n,enabled:r=!0}=e,a=(0,u.c)(n),i=(0,o.useRef)({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}).current;(0,o.useEffect)(()=>{if(!r)return;let e=e=>{M(e,t)&&(i.isPointerDown=!0)},o=e=>{if(i.ignoreEmulatedMouseEvents){i.ignoreEmulatedMouseEvents=!1;return}i.isPointerDown&&n&&M(e,t)&&(i.isPointerDown=!1,a(e))},u=e=>{i.ignoreEmulatedMouseEvents=!0,n&&i.isPointerDown&&M(e,t)&&(i.isPointerDown=!1,a(e))},c=D(t.current);return c.addEventListener("mousedown",e,!0),c.addEventListener("mouseup",o,!0),c.addEventListener("touchstart",e,!0),c.addEventListener("touchend",u,!0),()=>{c.removeEventListener("mousedown",e,!0),c.removeEventListener("mouseup",o,!0),c.removeEventListener("touchstart",e,!0),c.removeEventListener("touchend",u,!0)}},[n,t,a,i,r])}function M(e,t){let n=e.composedPath?.()[0]??e.target;return(!n||!!D(n).contains(n))&&!t.current?.contains(n)}function D(e){return e?.ownerDocument??document}function I(e){let t=(0,o.useRef)(void 0);return(0,o.useEffect)(()=>{t.current=e},[e]),t.current}function P(e,t){let n=(0,u.c)(e);(0,o.useEffect)(()=>{if(null==t)return;let e=null;return e=window.setTimeout(()=>{n()},t),()=>{e&&window.clearTimeout(e)}},[t,n])}n(80829),globalThis?.document?o.useLayoutEffect:o.useEffect,n(8954);var O=Object.defineProperty},65507:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(94285);function o(e,t=[]){let n=(0,r.useRef)(e);return(0,r.useEffect)(()=>{n.current=e}),(0,r.useCallback)((...e)=>n.current?.(...e),t)}},72128:(e,t,n)=>{function r(e,t={}){let n=!1;function o(t){let n=(["container","root"].includes(t??"")?[e]:[e,t]).filter(Boolean).join("__"),r=`chakra-${n}`;return{className:r,selector:`.${r}`,toString:()=>t}}return{parts:function(...u){for(let e of(!function(){if(!n){n=!0;return}throw Error("[anatomy] .part(...) should only be called once. Did you mean to use .extend(...) ?")}(),u))t[e]=o(e);return r(e,t)},toPart:o,extend:function(...n){for(let e of n)e in t||(t[e]=o(e));return r(e,t)},selectors:function(){return Object.fromEntries(Object.entries(t).map(([e,t])=>[e,t.selector]))},classnames:function(){return Object.fromEntries(Object.entries(t).map(([e,t])=>[e,t.className]))},get keys(){return Object.keys(t)},__type:{}}}n.d(t,{aH:()=>o,Ov:()=>u,ZO:()=>a,RG:()=>i,M9:()=>j,fZ:()=>c,Lx:()=>l,Is:()=>s,Ip:()=>f,_8:()=>d,Gq:()=>p,yj:()=>m,Pe:()=>b,Zt:()=>v,zV:()=>E,vI:()=>h,j_:()=>w,Vg:()=>g,oc:()=>k,tC:()=>y,S4:()=>C,af:()=>S,e:()=>L,Us:()=>T,K_:()=>_});let o=r("accordion").parts("root","container","button","panel","icon"),u=r("alert").parts("title","description","container","icon","spinner"),a=r("avatar").parts("label","badge","container","excessLabel","group"),i=r("breadcrumb").parts("link","item","container","separator");r("button").parts();let c=r("checkbox").parts("control","icon","container","label");r("progress").parts("track","filledTrack","label");let l=r("drawer").parts("overlay","dialogContainer","dialog","header","closeButton","body","footer"),s=r("editable").parts("preview","input","textarea"),f=r("form").parts("container","requiredIndicator","helperText"),d=r("formError").parts("text","icon"),p=r("input").parts("addon","field","element","group"),m=r("list").parts("container","item","icon"),b=r("menu").parts("button","list","item","groupTitle","icon","command","divider"),v=r("modal").parts("overlay","dialogContainer","dialog","header","closeButton","body","footer"),E=r("numberinput").parts("root","field","stepperGroup","stepper");r("pininput").parts("field");let h=r("popover").parts("content","header","body","footer","popper","arrow","closeButton"),w=r("progress").parts("label","filledTrack","track"),g=r("radio").parts("container","control","label"),k=r("select").parts("field","icon"),y=r("slider").parts("container","track","thumb","filledTrack","mark"),C=r("stat").parts("container","label","helpText","number","icon"),S=r("switch").parts("container","track","thumb","label"),L=r("table").parts("table","thead","tbody","tr","th","td","tfoot","caption"),T=r("tabs").parts("root","tab","tablist","tabpanel","tabpanels","indicator"),_=r("tag").parts("container","label","closeButton"),j=r("card").parts("container","header","body","footer");r("stepper").parts("stepper","step","title","description","indicator","separator","icon","number")},76338:(e,t,n)=>{n.d(t,{i:()=>u});var r=n(94285),o=n(37211);function u(e,t={}){let[n,a]=(0,r.useState)(!1),[i,c]=(0,r.useState)(e);(0,r.useEffect)(()=>c(e),[e]);let{timeout:l=1500,...s}="number"==typeof t?{timeout:t}:t,f=(0,r.useCallback)(e=>{let t="string"==typeof e?e:i;"clipboard"in navigator?navigator.clipboard.writeText(t).then(()=>a(!0)).catch(()=>a(o(t,s))):a(o(t,s))},[i,s]);return(0,r.useEffect)(()=>{let e=null;return n&&(e=window.setTimeout(()=>{a(!1)},l)),()=>{e&&window.clearTimeout(e)}},[l,n]),{value:i,setValue:c,onCopy:f,hasCopied:n}}},95845:(e,t,n)=>{n.d(t,{j:()=>u});var r=n(65507),o=n(94285);function u(e={}){let{onClose:t,onOpen:n,isOpen:a,id:i}=e,c=(0,r.c)(n),l=(0,r.c)(t),[s,f]=(0,o.useState)(e.defaultIsOpen||!1),d=void 0!==a?a:s,p=void 0!==a,m=(0,o.useId)(),b=i??`disclosure-${m}`,v=(0,o.useCallback)(()=>{p||f(!1),l?.()},[p,l]),E=(0,o.useCallback)(()=>{p||f(!0),c?.()},[p,c]),h=(0,o.useCallback)(()=>{d?v():E()},[d,E,v]);return{isOpen:d,onOpen:E,onClose:v,onToggle:h,isControlled:p,getButtonProps:function(e={}){return{...e,"aria-expanded":d,"aria-controls":b,onClick(t){e.onClick?.(t),h()}}},getDisclosureProps:function(e={}){return{...e,hidden:!d,id:b}}}}}}]);