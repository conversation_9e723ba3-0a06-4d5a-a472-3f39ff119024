"use strict";exports.id=8990,exports.ids=[8990],exports.modules={78487:(t,e,i)=>{function n(t,e){-1===t.indexOf(e)&&t.push(e)}function s(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{W9:()=>l,vY:()=>y,Kq:()=>n,bt:()=>D,ZZ:()=>F,tn:()=>V,yT:()=>$,qE:()=>r,am:()=>I,KI:()=>N,Sb:()=>R,V1:()=>o,DW:()=>X,h0:()=>q,iW:()=>u,Gv:()=>h,$X:()=>d,ph:()=>m,Xu:()=>b,lQ:()=>p,Fs:()=>f,qB:()=>g,Ai:()=>s,fD:()=>v,fj:()=>T,$e:()=>a});let r=(t,e,i)=>i>e?e:i<t?t:i,a=()=>{},o=()=>{},l={},u=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function h(t){return"object"==typeof t&&null!==t}let d=t=>/^0[^.\s]+$/u.test(t);function m(t){let e;return()=>(void 0===e&&(e=t()),e)}let p=t=>t,c=(t,e)=>i=>e(t(i)),f=(...t)=>t.reduce(c),g=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};class y{constructor(){this.subscriptions=[]}add(t){return n(this.subscriptions,t),()=>s(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){let n=this.subscriptions[s];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let v=t=>1e3*t,b=t=>t/1e3;function T(t,e){return e?1e3/e*t:0}let w=(t,e,i)=>{let n=e-t;return((i-t)%n+n)%n+t},M=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function A(t,e,i,n){if(t===e&&i===n)return p;let s=e=>(function(t,e,i,n,s){let r,a,o=0;do(r=M(a=e+(i-e)/2,n,s)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:M(s(t),e,n)}let x=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,k=t=>e=>1-t(1-e),E=A(.33,1.53,.69,.99),S=k(E),F=x(S),D=t=>(t*=2)<1?.5*S(t):.5*(2-Math.pow(2,-10*(t-1))),P=t=>1-Math.sin(Math.acos(t)),$=k(P),V=x(P),W=A(.42,0,1,1),O=A(0,0,.58,1),I=A(.42,0,.58,1),q=t=>Array.isArray(t)&&"number"!=typeof t[0];function R(t,e){return q(t)?t[w(0,t.length,e)]:t}let X=t=>Array.isArray(t)&&"number"==typeof t[0],C={linear:p,easeIn:W,easeInOut:I,easeOut:O,circIn:P,circInOut:V,circOut:$,backIn:S,backInOut:F,backOut:E,anticipate:D},K=t=>"string"==typeof t,N=t=>{if(X(t)){o(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,s]=t;return A(e,i,n,s)}return K(t)?(o(void 0!==C[t],`Invalid easing type '${t}'`),C[t]):t}},87558:(t,e,i)=>{let n;i.d(e,{AT:()=>eo,KN:()=>eE,b3:()=>eu,sb:()=>tV,hP:()=>t2,KG:()=>eW,MW:()=>eF,qU:()=>c,WG:()=>u,f:()=>U,XG:()=>tc,ZC:()=>tx,zs:()=>tK,fs:()=>tA,tD:()=>e6,Gt:()=>l,uv:()=>h,PP:()=>d,Ju:()=>ex,Df:()=>eA,eK:()=>eP,rU:()=>ed,PT:()=>eK,j4:()=>g,WH:()=>t7,$P:()=>es,SS:()=>e3,Mc:()=>eL,xZ:()=>e_,h1:()=>e5,k2:()=>eI,k$:()=>H,OQ:()=>eO,Wh:()=>ew,rq:()=>O,$y:()=>em,c$:()=>eU,px:()=>I,Ib:()=>tL,KJ:()=>eD,Wp:()=>eR,oz:()=>tw,Qu:()=>a,kB:()=>p,Us:()=>tB,fu:()=>tZ});var s=i(78487);let r=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],a={value:null,addProjectionMetrics:null};function o(t,e){let i=!1,n=!0,o={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,u=r.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,s=!1,r=!1,o=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function h(e){o.has(e)&&(d.schedule(e),t()),u++,e(l)}let d={schedule:(t,e=!1,r=!1)=>{let a=r&&s?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(l=t,s){r=!0;return}s=!0,[i,n]=[n,i],i.forEach(h),e&&a.value&&a.value.frameloop[e].push(u),u=0,i.clear(),s=!1,r&&(r=!1,d.process(t))}};return d}(l,e?i:void 0),t),{}),{setup:h,read:d,resolveKeyframes:m,preUpdate:p,update:c,preRender:f,render:g,postRender:y}=u,v=()=>{let r=s.W9.useManualTiming?o.timestamp:performance.now();i=!1,s.W9.useManualTiming||(o.delta=n?1e3/60:Math.max(Math.min(r-o.timestamp,40),1)),o.timestamp=r,o.isProcessing=!0,h.process(o),d.process(o),m.process(o),p.process(o),c.process(o),f.process(o),g.process(o),y.process(o),o.isProcessing=!1,i&&e&&(n=!1,t(v))},b=()=>{i=!0,n=!0,o.isProcessing||t(v)};return{schedule:r.reduce((t,e)=>{let n=u[e];return t[e]=(t,e=!1,s=!1)=>(i||b(),n.schedule(t,e,s)),t},{}),cancel:t=>{for(let e=0;e<r.length;e++)u[r[e]].cancel(t)},state:o,steps:u}}let{schedule:l,cancel:u,state:h,steps:d}=o("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:s.lQ,!0);function m(){n=void 0}let p={now:()=>(void 0===n&&p.set(h.isProcessing||s.W9.useManualTiming?h.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(m)}},c={layout:0,mainThread:0,waapi:0},f=t=>e=>"string"==typeof e&&e.startsWith(t),g=f("--"),y=f("var(--"),v=t=>!!y(t)&&b.test(t.split("/*")[0].trim()),b=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,T={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},w={...T,transform:t=>(0,s.qE)(0,1,t)},M={...T,default:1},A=t=>Math.round(1e5*t)/1e5,x=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,k=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,E=(t,e)=>i=>!!("string"==typeof i&&k.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),S=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[s,r,a,o]=n.match(x);return{[t]:parseFloat(s),[e]:parseFloat(r),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},F=t=>(0,s.qE)(0,255,t),D={...T,transform:t=>Math.round(F(t))},P={test:E("rgb","red"),parse:S("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+D.transform(t)+", "+D.transform(e)+", "+D.transform(i)+", "+A(w.transform(n))+")"},$={test:E("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:P.transform},V=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),W=V("deg"),O=V("%"),I=V("px"),q=V("vh"),R=V("vw"),X={...O,parse:t=>O.parse(t)/100,transform:t=>O.transform(100*t)},C={test:E("hsl","hue"),parse:S("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+O.transform(A(e))+", "+O.transform(A(i))+", "+A(w.transform(n))+")"},K={test:t=>P.test(t)||$.test(t)||C.test(t),parse:t=>P.test(t)?P.parse(t):C.test(t)?C.parse(t):$.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?P.transform(t):C.transform(t),getAnimatableNone:t=>{let e=K.parse(t);return e.alpha=0,K.transform(e)}},N=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,L="number",Y="color",B=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Z(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[],r=0,a=e.replace(B,t=>(K.test(t)?(n.color.push(r),s.push(Y),i.push(K.parse(t))):t.startsWith("var(")?(n.var.push(r),s.push("var"),i.push(t)):(n.number.push(r),s.push(L),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:a,indexes:n,types:s}}function z(t){return Z(t).values}function j(t){let{split:e,types:i}=Z(t),n=e.length;return t=>{let s="";for(let r=0;r<n;r++)if(s+=e[r],void 0!==t[r]){let e=i[r];e===L?s+=A(t[r]):e===Y?s+=K.transform(t[r]):s+=t[r]}return s}}let G=t=>"number"==typeof t?0:K.test(t)?K.getAnimatableNone(t):t,U={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(x)?.length||0)+(t.match(N)?.length||0)>0},parse:z,createTransformer:j,getAnimatableNone:function(t){let e=z(t);return j(t)(e.map(G))}};function _(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function Q(t,e){return i=>i>0?e:t}let H=(t,e,i)=>t+(e-t)*i,J=(t,e,i)=>{let n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},tt=[$,P,C],te=t=>tt.find(e=>e.test(t));function ti(t){let e=te(t);if((0,s.$e)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===C&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,r=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;s=_(o,n,t+1/3),r=_(o,n,t),a=_(o,n,t-1/3)}else s=r=a=i;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*a),alpha:n}}(i)),i}let tn=(t,e)=>{let i=ti(t),n=ti(e);if(!i||!n)return Q(t,e);let s={...i};return t=>(s.red=J(i.red,n.red,t),s.green=J(i.green,n.green,t),s.blue=J(i.blue,n.blue,t),s.alpha=H(i.alpha,n.alpha,t),P.transform(s))},ts=new Set(["none","hidden"]);function tr(t,e){return i=>H(t,e,i)}function ta(t){return"number"==typeof t?tr:"string"==typeof t?v(t)?Q:K.test(t)?tn:tu:Array.isArray(t)?to:"object"==typeof t?K.test(t)?tn:tl:Q}function to(t,e){let i=[...t],n=i.length,s=t.map((t,i)=>ta(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function tl(t,e){let i={...t,...e},n={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=ta(t[s])(t[s],e[s]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tu=(t,e)=>{let i=U.createTransformer(e),n=Z(t),r=Z(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?ts.has(t)&&!r.values.length||ts.has(e)&&!n.values.length?function(t,e){return ts.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,s.Fs)(to(function(t,e){let i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let r=e.types[s],a=t.indexes[r][n[r]],o=t.values[a]??0;i[s]=o,n[r]++}return i}(n,r),r.values),i):((0,s.$e)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),Q(t,e))};function th(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?H(t,e,i):ta(t)(t,e)}let td=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>l.update(e,t),stop:()=>u(e),now:()=>h.isProcessing?h.timestamp:p.now()}},tm=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function tp(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tc(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(tp(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:(0,s.Xu)(r)}}function tf(t,e,i){let n=Math.max(e-5,0);return(0,s.fj)(i-t(n),e-n)}let tg={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function ty(t,e){return t*Math.sqrt(1-e*e)}let tv=["duration","bounce"],tb=["stiffness","damping","mass"];function tT(t,e){return e.some(e=>void 0!==t[e])}function tw(t=tg.visualDuration,e=tg.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:a}=n,o=n.keyframes[0],l=n.keyframes[n.keyframes.length-1],u={done:!1,value:o},{stiffness:h,damping:d,mass:m,duration:p,velocity:c,isResolvedFromDuration:f}=function(t){let e={velocity:tg.velocity,stiffness:tg.stiffness,damping:tg.damping,mass:tg.mass,isResolvedFromDuration:!1,...t};if(!tT(t,tb)&&tT(t,tv))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*(0,s.qE)(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:tg.mass,stiffness:n,damping:r}}else{let i=function({duration:t=tg.duration,bounce:e=tg.bounce,velocity:i=tg.velocity,mass:n=tg.mass}){let r,a;(0,s.$e)(t<=(0,s.fD)(tg.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=(0,s.qE)(tg.minDamping,tg.maxDamping,o),t=(0,s.qE)(tg.minDuration,tg.maxDuration,(0,s.Xu)(t)),o<1?(r=e=>{let n=e*o,s=n*t;return .001-(n-i)/ty(e,o)*Math.exp(-s)},a=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=ty(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),a=e=>t*t*(i-e)*Math.exp(-e*t));let l=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,a,5/t);if(t=(0,s.fD)(t),isNaN(l))return{stiffness:tg.stiffness,damping:tg.damping,duration:t};{let e=Math.pow(l,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:tg.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-(0,s.Xu)(n.velocity||0)}),g=c||0,y=d/(2*Math.sqrt(h*m)),v=l-o,b=(0,s.Xu)(Math.sqrt(h/m)),T=5>Math.abs(v);if(r||(r=T?tg.restSpeed.granular:tg.restSpeed.default),a||(a=T?tg.restDelta.granular:tg.restDelta.default),y<1){let t=ty(b,y);i=e=>l-Math.exp(-y*b*e)*((g+y*b*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===y)i=t=>l-Math.exp(-b*t)*(v+(g+b*v)*t);else{let t=b*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*b*e),n=Math.min(t*e,300);return l-i*((g+y*b*v)*Math.sinh(n)+t*v*Math.cosh(n))/t}}let w={calculatedDuration:f&&p||null,next:t=>{let e=i(t);if(f)u.done=t>=p;else{let n=0===t?g:0;y<1&&(n=0===t?(0,s.fD)(g):tf(i,t,e));let o=Math.abs(l-e)<=a;u.done=Math.abs(n)<=r&&o}return u.value=u.done?l:e,u},toString:()=>{let t=Math.min(tp(w),2e4),e=tm(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tM({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){let d,m,p=t[0],c={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,y=i*e,v=p+y,b=void 0===a?v:a(v);b!==v&&(y=b-p);let T=t=>-y*Math.exp(-t/n),w=t=>b+T(t),M=t=>{let e=T(t),i=w(t);c.done=Math.abs(e)<=u,c.value=c.done?b:i},A=t=>{f(c.value)&&(d=t,m=tw({keyframes:[c.value,g(c.value)],velocity:tf(w,t,c.value),damping:s,stiffness:r,restDelta:u,restSpeed:h}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return(m||void 0!==d||(e=!0,M(t),A(t)),void 0!==d&&t>=d)?m.next(t-d):(e||M(t),c)}}}function tA(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=(0,s.qB)(0,e,n);t.push(H(i,1,r))}}function tx(t){let e=[0];return tA(e,t.length-1),e}function tk({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let a=(0,s.h0)(n)?n.map(s.KI):(0,s.KI)(n),o={done:!1,value:e[0]},l=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let a=t.length;if((0,s.V1)(a===e.length,"Both input and output ranges must be the same length"),1===a)return()=>e[0];if(2===a&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),e=[...e].reverse());let l=function(t,e,i){let n=[],r=i||s.W9.mix||th,a=t.length-1;for(let i=0;i<a;i++){let a=r(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||s.lQ:e;a=(0,s.Fs)(t,a)}n.push(a)}return n}(e,n,r),u=l.length,h=i=>{if(o&&i<t[0])return e[0];let n=0;if(u>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=(0,s.qB)(t[n],t[n+1],i);return l[n](r)};return i?e=>h((0,s.qE)(t[0],t[a-1],e)):h}((r=i&&i.length===e.length?i:tx(e),r.map(e=>e*t)),e,{ease:Array.isArray(a)?a:e.map(()=>a||s.am).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=l(e),o.done=e>=t,o)}}tw.applyToOptions=t=>{let e=tc(t,100,tw);return t.ease=e.ease,t.duration=(0,s.fD)(e.duration),t.type="keyframes",t};let tE=t=>null!==t;function tS(t,{repeat:e,repeatType:i="loop"},n,s=1){let r=t.filter(tE),a=s<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==n?n:r[a]}let tF={decay:tM,inertia:tM,tween:tk,keyframes:tk,spring:tw};function tD(t){"string"==typeof t.type&&(t.type=tF[t.type])}class tP{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let t$=t=>t/100;class tV extends tP{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==p.now()&&this.tick(p.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},c.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;tD(t);let{type:e=tk,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:a=0}=t,{keyframes:o}=t,l=e||tk;l!==tk&&"number"!=typeof o[0]&&(this.mixKeyframes=(0,s.Fs)(t$,th(o[0],o[1])),o=[0,100]);let u=l({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=l({...t,keyframes:[...o].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=tp(u));let{calculatedDuration:h}=u;this.calculatedDuration=h,this.resolvedDuration=h+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=u}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);let{delay:u=0,keyframes:h,repeat:d,repeatType:m,repeatDelay:p,type:c,onUpdate:f,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?y<0:y>n;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let b=this.currentTime,T=i;if(d){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,d+1))%2&&("reverse"===m?(i=1-i,p&&(i-=p/o)):"mirror"===m&&(T=a)),b=(0,s.qE)(0,1,i)*o}let w=v?{done:!1,value:h[0]}:T.next(b);r&&(w.value=r(w.value));let{done:M}=w;v||null===l||(M=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&M);return A&&c!==tM&&(w.value=tS(h,this.options,g,this.speed)),f&&f(w.value),A&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return(0,s.Xu)(this.calculatedDuration)}get time(){return(0,s.Xu)(this.currentTime)}set time(t){t=(0,s.fD)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(p.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,s.Xu)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=td,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(p.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,c.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let tW=t=>180*t/Math.PI,tO=t=>tq(tW(Math.atan2(t[1],t[0]))),tI={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:tO,rotateZ:tO,skewX:t=>tW(Math.atan(t[1])),skewY:t=>tW(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},tq=t=>((t%=360)<0&&(t+=360),t),tR=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),tX=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),tC={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tR,scaleY:tX,scale:t=>(tR(t)+tX(t))/2,rotateX:t=>tq(tW(Math.atan2(t[6],t[5]))),rotateY:t=>tq(tW(Math.atan2(-t[2],t[0]))),rotateZ:tO,rotate:tO,skewX:t=>tW(Math.atan(t[4])),skewY:t=>tW(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function tK(t){return+!!t.includes("scale")}function tN(t,e){let i,n;if(!t||"none"===t)return tK(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=tC,n=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tI,n=e}if(!n)return tK(e);let r=i[e],a=n[1].split(",").map(tY);return"function"==typeof r?r(a):a[r]}let tL=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return tN(i,e)};function tY(t){return parseFloat(t.trim())}let tB=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],tZ=new Set(tB),tz=t=>t===T||t===I,tj=new Set(["x","y","z"]),tG=tB.filter(t=>!tj.has(t)),tU={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>tN(e,"x"),y:(t,{transform:e})=>tN(e,"y")};tU.translateX=tU.x,tU.translateY=tU.y;let t_=new Set,tQ=!1,tH=!1,tJ=!1;function t0(){if(tH){let t=Array.from(t_).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return tG.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tH=!1,tQ=!1,t_.forEach(t=>t.complete(tJ)),t_.clear()}function t1(){t_.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tH=!0)})}class t2{constructor(t,e,i,n,s,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(t_.add(this),tQ||(tQ=!0,l.read(t1),l.resolveKeyframes(t0))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let s=n?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),t_.delete(this)}cancel(){"scheduled"===this.state&&(t_.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let t5=t=>t.startsWith("--"),t3=(0,s.ph)(()=>void 0!==window.ScrollTimeline),t4={},t6=function(t,e){let i=(0,s.ph)(t);return()=>t4[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),t9=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,t8={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:t9([0,.65,.55,1]),circOut:t9([.55,0,1,.45]),backIn:t9([.31,.01,.66,-.59]),backOut:t9([.33,1.53,.69,.99])};function t7(t){return"function"==typeof t&&"applyToOptions"in t}class et extends tP{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:o=!1,finalKeyframe:l,onComplete:u}=t;this.isPseudoElement=!!r,this.allowFlatten=o,this.options=t,(0,s.V1)("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let h=function({type:t,...e}){return t7(t)&&t6()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:o=0,repeatType:l="loop",ease:u="easeOut",times:h}={},d){let m={[e]:i};h&&(m.offset=h);let p=function t(e,i){if(e)return"function"==typeof e?t6()?tm(e,i):"ease-out":(0,s.DW)(e)?t9(e):Array.isArray(e)?e.map(e=>t(e,i)||t8.easeOut):t8[e]}(u,r);Array.isArray(p)&&(m.easing=p),a.value&&c.waapi++;let f={delay:n,duration:r,easing:Array.isArray(p)?"linear":p,fill:"both",iterations:o+1,direction:"reverse"===l?"alternate":"normal"};d&&(f.pseudoElement=d);let g=t.animate(m,f);return a.value&&g.finished.finally(()=>{c.waapi--}),g}(e,i,n,h,r),!1===h.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=tS(n,this.options,l,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){t5(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}u?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,s.Xu)(Number(t))}get time(){return(0,s.Xu)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,s.fD)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&t3())?(this.animation.timeline=t,s.lQ):e(this)}}let ee={anticipate:s.bt,backInOut:s.ZZ,circInOut:s.tn};class ei extends et{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in ee&&(t.ease=ee[t.ease])}(t),tD(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...a}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new tV({...a,autoplay:!1}),l=(0,s.fD)(this.finishedTime??this.time);e.setWithVelocity(o.sample(l-10).value,o.sample(l).value,10),o.stop()}}let en=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(U.test(t)||"0"===t)&&!t.startsWith("url("));function es(t){return(0,s.Gv)(t)&&"offsetHeight"in t}let er=new Set(["opacity","clipPath","filter","transform"]),ea=(0,s.ph)(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eo extends tP{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=p.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,name:o,motionValue:l,element:u,...h},m=u?.KeyframeResolver||t2;this.keyframeResolver=new m(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:a,velocity:o,delay:l,isHandoff:u,onUpdate:h}=i;this.resolvedAt=p.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let a=t[t.length-1],o=en(r,e),l=en(a,e);return(0,s.$e)(o===l,`You are trying to animate ${e} from "${r}" to "${a}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${a} via the \`style\` property.`),!!o&&!!l&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||t7(i))&&n)}(t,r,a,o)&&((s.W9.instantAnimations||!l)&&h?.(tS(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let d={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},m=!u&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:a}=t;if(!es(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return ea()&&i&&er.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==s&&0!==r&&"inertia"!==a}(d)?new ei({...d,element:d.motionValue.owner.current}):new tV(d);m.finished.then(()=>this.notifyFinished()).catch(s.lQ),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tJ=!0,t1(),t0(),tJ=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}class el{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t){let e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class eu extends el{then(t,e){return this.finished.finally(t).then(()=>{})}}new WeakMap;let eh=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ed(t,e){return t?.[e]??t?.default??t}let em=new Set(["width","height","top","left","right","bottom",...tB]),ep=t=>e=>e.test(t),ec=[T,I,O,W,R,q,{test:t=>"auto"===t,parse:t=>t}],ef=t=>ec.find(ep(t)),eg=new Set(["brightness","contrast","saturate","opacity"]);function ey(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(x)||[];if(!n)return t;let s=i.replace(n,""),r=+!!eg.has(e);return n!==i&&(r*=100),e+"("+r+s+")"}let ev=/\b([a-z-]*)\(.*?\)/gu,eb={...U,getAnimatableNone:t=>{let e=t.match(ev);return e?e.map(ey).join(" "):t}},eT={...T,transform:Math.round},ew={borderWidth:I,borderTopWidth:I,borderRightWidth:I,borderBottomWidth:I,borderLeftWidth:I,borderRadius:I,radius:I,borderTopLeftRadius:I,borderTopRightRadius:I,borderBottomRightRadius:I,borderBottomLeftRadius:I,width:I,maxWidth:I,height:I,maxHeight:I,top:I,right:I,bottom:I,left:I,padding:I,paddingTop:I,paddingRight:I,paddingBottom:I,paddingLeft:I,margin:I,marginTop:I,marginRight:I,marginBottom:I,marginLeft:I,backgroundPositionX:I,backgroundPositionY:I,rotate:W,rotateX:W,rotateY:W,rotateZ:W,scale:M,scaleX:M,scaleY:M,scaleZ:M,skew:W,skewX:W,skewY:W,distance:I,translateX:I,translateY:I,translateZ:I,x:I,y:I,z:I,perspective:I,transformPerspective:I,opacity:w,originX:X,originY:X,originZ:I,zIndex:eT,fillOpacity:w,strokeOpacity:w,numOctaves:eT},eM={...ew,color:K,backgroundColor:K,outlineColor:K,fill:K,stroke:K,borderColor:K,borderTopColor:K,borderRightColor:K,borderBottomColor:K,borderLeftColor:K,filter:eb,WebkitFilter:eb},eA=t=>eM[t];function ex(t,e){let i=eA(t);return i!==eb&&(i=U),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let ek=new Set(["auto","none","0"]);class eE extends t2{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&v(n=n.trim())){let r=function t(e,i,n=1){(0,s.V1)(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,a]=function(t){let e=eh.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${i??n}`,s]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return(0,s.iW)(t)?parseFloat(t):t}return v(a)?t(a,i,n+1):a}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!em.has(i)||2!==t.length)return;let[n,r]=t,a=ef(n),o=ef(r);if(a!==o)if(tz(a)&&tz(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tU[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||(0,s.$X)(n)))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!ek.has(e)&&Z(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=ex(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tU[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let s=i.length-1,r=i[s];i[s]=tU[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}function eS(t){return!!("function"==typeof t&&supportsLinearEasing()||!t||"string"==typeof t&&(t in supportedWaapiEasing||supportsLinearEasing())||isBezierDefinition(t)||Array.isArray(t)&&t.every(eS))}let eF=new Set(["opacity","clipPath","filter","transform"]);function eD(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let n=document;e&&(n=e.current);let s=i?.[t]??n.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}let eP=(t,e)=>e&&"number"==typeof t?e.transform(t):t,e$=t=>!isNaN(parseFloat(t)),eV={current:void 0};class eW{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=p.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=p.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=e$(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new s.vY);let i=this.events[t].add(e);return"change"===t?()=>{i(),l.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return eV.current&&eV.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=p.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,s.fj)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function eO(t,e){return new eW(t,e)}I.transform;let{schedule:eI}=o(queueMicrotask,!1),eq={x:!1,y:!1};function eR(t){if("x"===t||"y"===t)if(eq[t])return null;else return eq[t]=!0,()=>{eq[t]=!1};return eq.x||eq.y?null:(eq.x=eq.y=!0,()=>{eq.x=eq.y=!1})}function eX(t,e){let i=eD(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function eC(t){return!("touch"===t.pointerType||eq.x||eq.y)}function eK(t,e,i={}){let[n,s,r]=eX(t,i),a=t=>{if(!eC(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let r=t=>{eC(t)&&(n(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,s)};return n.forEach(t=>{t.addEventListener("pointerenter",a,s)}),r}let eN=(t,e)=>!!e&&(t===e||eN(t,e.parentElement)),eL=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,eY=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),eB=new WeakSet;function eZ(t){return e=>{"Enter"===e.key&&t(e)}}function ez(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ej=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=eZ(()=>{if(eB.has(i))return;ez(i,"down");let t=eZ(()=>{ez(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ez(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function eG(t){return eL(t)&&!(eq.x||eq.y)}function eU(t,e,i={}){let[n,s,r]=eX(t,i),a=t=>{let n=t.currentTarget;if(!eG(t))return;eB.add(n);let r=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),eB.has(n)&&eB.delete(n),eG(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||eN(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,s),es(t))&&(t.addEventListener("focus",t=>ej(t,s)),eY.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}function e_(t){return(0,s.Gv)(t)&&"ownerSVGElement"in t}let eQ=new WeakMap;function eH({target:t,borderBoxSize:e}){eQ.get(t)?.forEach(i=>{i(t,{get width(){return(null)(t,e)},get height(){return(null)(t,e)}})})}function eJ(){let{value:t}=statsBuffer;if(null===t)return void cancelFrame(eJ);t.frameloop.rate.push(frameData.delta),t.animations.mainThread.push(activeAnimations.mainThread),t.animations.waapi.push(activeAnimations.waapi),t.animations.layout.push(activeAnimations.layout)}function e0(t){return t.reduce((t,e)=>t+e,0)/t.length}function e1(t,e=e0){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}let e2=t=>Math.round(1e3/t);function e5(t){return e_(t)&&"svg"===t.tagName}let e3=t=>!!(t&&t.getVelocity),e4=[...ec,K,U],e6=t=>e4.find(ep(t)),e9=null,e8=null;r.reduce((t,e)=>(t[e]=t=>u(t),t),{})}};