"use strict";(()=>{var e={};e.id=9511,e.ids=[9511],e.modules={224:e=>{e.exports=import("@discordjs/rest")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},33915:e=>{e.exports=import("discord-api-types/v10")},36952:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>p});var i=r(15806),a=r(94506),o=r(224),u=r(33915),n=r(20381),d=e([o,u]);async function p(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{if(!await (0,i.getServerSession)(e,t,a.authOptions)){let r=e.headers.authorization;if(!r||!r.startsWith("Bearer "))return t.status(401).json({error:"Unauthorized"})}let{userId:r}=e.body;if(!r)return t.status(400).json({error:"Missing userId"});let s=new o.REST({version:"10"}).setToken(n._.DISCORD_BOT_TOKEN),d=n._.DISCORD_GUILD_ID;await s.patch(u.Routes.guildMember(d,r),{body:{communication_disabled_until:null}}),t.status(200).json({success:!0})}catch(e){t.status(500).json({error:"Failed to remove timeout"})}}[o,u]=d.then?(await d)():d,s()}catch(e){s(e)}})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},73687:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>p,default:()=>d,routeModule:()=>c});var i=r(93433),a=r(20264),o=r(20584),u=r(36952),n=e([u]);u=(n.then?(await n)():n)[0];let d=(0,o.M)(u,"default"),p=(0,o.M)(u,"config"),c=new i.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/discord/users/untimeout",pathname:"/api/discord/users/untimeout",bundlePath:"",filename:""},userland:u});s()}catch(e){s(e)}})},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(73687));module.exports=s})();