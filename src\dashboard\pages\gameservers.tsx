import React, { useState, useEffect } from 'react';
import {
  VS<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  But<PERSON>,
  useToast,
  SimpleGrid,
  HStack,
  <PERSON>ner,
  IconButton,
  useDisclosure,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Box,
  Container,
  Icon,
} from '@chakra-ui/react';
import { FaSync, FaPlus, FaEdit, FaTrash, FaGamepad } from 'react-icons/fa';
import Layout from '../components/Layout';
import { GameServerCard } from '../components/GameServerCard';
import { GameServerDialog } from '../components/GameServerDialog';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './api/auth/[...nextauth]';
import { useSession } from 'next-auth/react';

interface GameServerStatus {
  _id?: string;
  name: string;
  host: string;
  port: number;
  type: string;
  online: boolean;
  players?: Array<{ name: string }>;
  maxPlayers?: number;
  map?: string;
  ping?: number;
  error?: string;
  lastUpdated: string;
  description?: string;
  hasPassword?: boolean;
  password?: string;
}

interface GameServerInput {
  _id?: string;
  name: string;
  host: string;
  port: number;
  type: string;
  description?: string;
  hasPassword?: boolean;
  password?: string;
}

export default function GameServers() {
  const { data: session } = useSession();
  const isAdmin = (session?.user as any)?.isAdmin === true;
  const [servers, setServers] = useState<GameServerStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedServer, setSelectedServer] = useState<GameServerInput | undefined>();
  const [serverToDelete, setServerToDelete] = useState<GameServerStatus | undefined>();
  const { isOpen: isAddEditOpen, onOpen: onAddEditOpen, onClose: onAddEditClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const cancelRef = React.useRef<HTMLButtonElement>(null);
  const toast = useToast();

  const fetchServers = async () => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/gameservers/query');
      if (!response.ok) {
        throw new Error('Failed to fetch server status');
      }
      const data = await response.json();
      setServers(data);
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to fetch server status',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchServers();
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchServers, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleAddEdit = async (server: GameServerInput) => {
    try {
      // First validate the game type
      const validateResponse = await fetch(`/api/gameservers/games?type=${encodeURIComponent(server.type)}`);
      if (!validateResponse.ok) {
        throw new Error('Invalid game type');
      }
      const { type: validatedType } = await validateResponse.json();
      server.type = validatedType;

      // Then save the server
      const url = '/api/gameservers/manage';
      const method = server._id ? 'PUT' : 'POST';
      
      console.log('Server operation:', method, 'Server data:', server); // Enhanced debug log
      
      let requestBody;
      if (method === 'PUT') {
        // For PUT requests, we need the id separate from the data
        const { _id, ...serverData } = server;
        requestBody = { id: _id, ...serverData };
      } else {
        // For POST requests, we don't want the _id field at all
        const { _id, ...serverData } = server;
        requestBody = serverData;
      }
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to save server');
      }

      fetchServers();
    
      toast({
        title: server._id ? 'Server Updated' : 'Server Added',
        description: server._id ? 'The server has been updated successfully' : 'The server has been added successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Save error:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save server',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      throw error;
    }
  };

  const handleDelete = async () => {
    if (!serverToDelete?._id) {
      console.error('No server selected for deletion', { serverToDelete });
      toast({
        title: 'Error',
        description: 'No server selected for deletion',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    try {
      console.log('Attempting to delete server:', serverToDelete);
      const response = await fetch('/api/gameservers/manage', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: serverToDelete._id }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete server');
      }

      // Remove the server from the local state
      setServers(prev => prev.filter(s => s._id !== serverToDelete._id));
      
      toast({
        title: 'Server Deleted',
        description: 'The server has been successfully deleted',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      setServerToDelete(undefined);
      onDeleteClose();
    } catch (error) {
      console.error('Delete error:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete server',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const openEditDialog = (server: GameServerStatus) => {
    setSelectedServer({
      _id: server._id,
      name: server.name,
      type: server.type,
      host: server.host,
      port: server.port,
      description: server.description,
      hasPassword: server.hasPassword,
      password: server.password
    });
    onAddEditOpen();
  };

  const openDeleteDialog = (server: GameServerStatus) => {
    console.log('Opening delete dialog for server:', server);
    setSelectedServer(server);
    onDeleteOpen();
  };

  const handleDeleteServer = async () => {
    if (!selectedServer) return;

    try {
      // Always use the server's ID if available
      const deleteBody = { id: selectedServer._id };

      console.log('Sending delete request with body:', deleteBody);

      const response = await fetch('/api/gameservers/manage', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(deleteBody),
      });

      const responseData = await response.json();
      
      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to delete server');
      }

      // Remove the server from the local state
      setServers(prev => prev.filter(s => s._id !== selectedServer._id));

      setSelectedServer(undefined);
      onDeleteClose();
      toast({
        title: 'Success',
        description: 'Server deleted successfully',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error: any) {
      console.error('Error deleting server:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete server',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <Layout>
      <Box w="full" p={4}>
        {/* Header Card */}
        <Box
          maxW="4xl"
          mx="auto"
          mb={8}
          mt={8}
          bg="rgba(255,255,255,0.08)"
          p={8}
          rounded="2xl"
          backdropFilter="blur(10px)"
          border="2px solid"
          borderColor="green.400"
          boxShadow="0 0 15px rgba(72, 187, 120, 0.4)"
          textAlign="center"
        >
          <Heading
            size="2xl"
            bgGradient="linear(to-r, green.300, teal.400)"
            bgClip="text"
            mb={4}
          >
            Game Servers
          </Heading>
          <Text color="gray.300" fontSize="lg" mb={6}>
            Monitor and manage your game servers in real-time
          </Text>

          <HStack spacing={4} justify="center">
            {isAdmin && (
            <Button
              leftIcon={<FaPlus />}
              colorScheme="green"
              onClick={() => {
                setSelectedServer(undefined);
                onAddEditOpen();
              }}
              size="md"
              variant="solid"
              _hover={{ transform: 'translateY(-2px)', shadow: 'lg' }}
            >
              Add Server
            </Button>
            )}
            <Button
              leftIcon={<FaSync />}
              onClick={fetchServers}
              isLoading={refreshing}
              loadingText="Refreshing"
              size="md"
              variant="outline"
              colorScheme="green"
              _hover={{ transform: 'translateY(-2px)', shadow: 'lg' }}
            >
              Refresh Status
            </Button>
          </HStack>
        </Box>

        {/* Servers Grid */}
        <Box maxW="7xl" mx="auto">
          {loading ? (
            <VStack 
              py={8} 
              bg="rgba(255,255,255,0.08)"
              rounded="2xl"
              backdropFilter="blur(10px)"
              border="1px solid"
              borderColor="whiteAlpha.200"
            >
              <Spinner size="xl" color="green.400" />
              <Text color="gray.400">Loading servers...</Text>
            </VStack>
          ) : servers.length === 0 ? (
            <VStack
              spacing={4}
              p={8}
              bg="rgba(255,255,255,0.08)"
              rounded="2xl"
              backdropFilter="blur(10px)"
              border="1px solid"
              borderColor="whiteAlpha.200"
              textAlign="center"
            >
              <Icon as={FaGamepad} boxSize={12} color="green.400" />
              <Text color="gray.300" fontSize="lg">No game servers found</Text>
              <Text fontSize="md" color="gray.500">
                Add your first game server to start monitoring
              </Text>
              {isAdmin && (
              <Button
                leftIcon={<FaPlus />}
                colorScheme="green"
                onClick={() => {
                  setSelectedServer(undefined);
                  onAddEditOpen();
                }}
                size="md"
                variant="outline"
                _hover={{ transform: 'translateY(-2px)', shadow: 'lg' }}
              >
                Add Your First Server
              </Button>
              )}
            </VStack>
          ) : (
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {servers.map((server, index) => (
                <Box 
                  key={`${server._id || `${server.host}:${server.port}-${index}`}`}
                  position="relative"
                  transition="all 0.2s"
                  _hover={isAdmin ? {
                    transform: 'translateY(-4px)',
                    '& > .server-actions': {
                      opacity: 1,
                      transform: 'translateY(0)',
                    }
                  } : undefined}
                >
                  <GameServerCard server={server} />
                  {isAdmin && (
                  <HStack
                    className="server-actions"
                    position="absolute"
                    top={2}
                    right={2}
                    spacing={1}
                    bg="blackAlpha.800"
                    p={1}
                    borderRadius="md"
                    opacity={0}
                    transform="translateY(-4px)"
                    transition="all 0.2s"
                    zIndex={2}
                    backdropFilter="blur(8px)"
                  >
                    <IconButton
                      aria-label="Edit server"
                      icon={<FaEdit />}
                      size="sm"
                      variant="ghost"
                      colorScheme="green"
                      onClick={() => openEditDialog(server)}
                      _hover={{ bg: 'green.700' }}
                    />
                    <IconButton
                      aria-label="Delete server"
                      icon={<FaTrash />}
                      size="sm"
                      variant="ghost"
                      colorScheme="red"
                      onClick={() => openDeleteDialog(server)}
                      _hover={{ bg: 'red.700' }}
                    />
                  </HStack>
                  )}
                </Box>
              ))}
            </SimpleGrid>
          )}
        </Box>

        {isAdmin && (
          <GameServerDialog
            isOpen={isAddEditOpen}
            onClose={onAddEditClose}
            server={selectedServer}
            onSave={handleAddEdit}
          />
        )}

        <AlertDialog
          isOpen={isDeleteOpen}
          leastDestructiveRef={cancelRef}
          onClose={onDeleteClose}
          isCentered
        >
          <AlertDialogOverlay backdropFilter="blur(10px)">
            <AlertDialogContent bg="gray.800" border="1px" borderColor="whiteAlpha.200">
              <AlertDialogHeader fontSize="lg" fontWeight="bold" color="white">
                Delete Server
              </AlertDialogHeader>

              <AlertDialogBody color="gray.300">
                Are you sure you want to delete {selectedServer?.name || `${selectedServer?.host}:${selectedServer?.port}`}? This action cannot be undone.
              </AlertDialogBody>

              <AlertDialogFooter gap={3}>
                <Button ref={cancelRef} onClick={onDeleteClose} variant="ghost" color="gray.300">
                  Cancel
                </Button>
                <Button 
                  colorScheme="red" 
                  onClick={handleDeleteServer}
                  _hover={{ bg: 'red.600' }}
                  _active={{ bg: 'red.700' }}
                >
                  Delete
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
      </Box>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);
  if (!session) {
    return {
      redirect: {
        destination: '/signin',
        permanent: false,
      },
    };
  }
  return { props: {} };
}; 