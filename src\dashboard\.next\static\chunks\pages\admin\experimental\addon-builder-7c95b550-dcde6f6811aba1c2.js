"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6156],{95916:(e,o,r)=>{r.d(o,{A:()=>m});var s=r(94513),t=r(94285),l=r(6179),n=r(88395),a=r(82339),i=r(27263);let c={user:[{name:"{user.id}",description:"User ID for authentication",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username for requests",icon:"\uD83D\uDC64"},{name:"{user.token}",description:"User auth token",icon:"\uD83D\uDD11"},{name:"{user.email}",description:"User email address",icon:"\uD83D\uDCE7"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDFE0"},{name:"{server.name}",description:"Server name",icon:"\uD83D\uDCDD"},{name:"{server.memberCount}",description:"Member count",icon:"\uD83D\uDC65"},{name:"{server.region}",description:"Server region",icon:"\uD83C\uDF0D"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83D\uDCAC"},{name:"{message.content}",description:"Message content",icon:"\uD83D\uDCDD"},{name:"{message.channelId}",description:"Channel ID",icon:"\uD83D\uDCFA"},{name:"{message.authorId}",description:"Author ID",icon:"\uD83D\uDC64"}],response:[{name:"{response.data}",description:"Full response data",icon:"\uD83D\uDCCA"},{name:"{response.status}",description:"HTTP status code",icon:"\uD83D\uDD22"},{name:"{response.headers}",description:"Response headers",icon:"\uD83D\uDCCB"},{name:"{response.error}",description:"Error message if failed",icon:"❌"}],time:[{name:"{time.now}",description:"Current timestamp",icon:"⏰"},{name:"{time.iso}",description:"ISO timestamp",icon:"\uD83D\uDCC5"},{name:"{time.unix}",description:"Unix timestamp",icon:"\uD83D\uDD50"}],random:[{name:"{random.uuid}",description:"Random UUID",icon:"\uD83C\uDFB2"},{name:"{random.number}",description:"Random number",icon:"\uD83D\uDD22"},{name:"{random.string}",description:"Random string",icon:"\uD83D\uDD24"}]},d=[{value:"GET",label:"GET",description:"Retrieve data from server",color:"green"},{value:"POST",label:"POST",description:"Send data to create new resource",color:"blue"},{value:"PUT",label:"PUT",description:"Update existing resource",color:"orange"},{value:"PATCH",label:"PATCH",description:"Partially update resource",color:"yellow"},{value:"DELETE",label:"DELETE",description:"Remove resource from server",color:"red"},{value:"HEAD",label:"HEAD",description:"Get headers only",color:"purple"},{value:"OPTIONS",label:"OPTIONS",description:"Get allowed methods",color:"gray"}],x=[{value:"json",label:"JSON",description:"JavaScript Object Notation"},{value:"form",label:"Form Data",description:"URL-encoded form data"},{value:"text",label:"Plain Text",description:"Raw text content"},{value:"xml",label:"XML",description:"Extensible Markup Language"}],u=[{value:"ignore",label:"Ignore Errors",description:"Continue flow on API errors"},{value:"log",label:"Log Errors",description:"Log errors but continue"},{value:"throw",label:"Throw Errors",description:"Stop flow on API errors"},{value:"retry",label:"Retry on Error",description:"Retry failed requests"}],h=[{key:"Authorization",value:"Bearer {user.token}"},{key:"Content-Type",value:"application/json"},{key:"User-Agent",value:"Discord Bot API Client"},{key:"Accept",value:"application/json"},{key:"X-API-Key",value:"{api.key}"}],p=(0,t.memo)(e=>{var o,r,p,m,b,j,g;let{data:S,selected:f,id:y,updateNodeData:T}=e,{currentScheme:v}=(0,i.DP)(),{isOpen:C,onOpen:k,onClose:w}=(0,n.useDisclosure)(),z=(0,n.useToast)(),[I,A]=(0,t.useState)(()=>({method:"GET",headers:[],bodyType:"json",timeout:5e3,errorHandling:"log",retryCount:0,retryDelay:1e3,followRedirects:!0,validateSSL:!0,...S})),[R,P]=(0,t.useState)(!1),[H,D]=(0,t.useState)(null),[B,F]=(0,t.useState)(null),[E,L]=(0,t.useState)([]),[V,N]=(0,t.useState)(!1),O=e=>{A(o=>({...o,...e}))},q=e=>{navigator.clipboard.writeText(e),z({title:"Copied!",description:"Variable ".concat(e," copied to clipboard"),status:"success",duration:2e3,isClosable:!0})},U=async()=>{if(!I.url){F("Please enter a URL first"),z({title:"Test Failed",description:"Please enter a URL first",status:"error",duration:3e3,isClosable:!0});return}P(!0),F(null),D(null);try{var e;let o,r={};null==(e=I.headers)||e.forEach(e=>{e.key&&e.value&&(r[e.key]=e.value)}),I.body&&("POST"===I.method||"PUT"===I.method||"PATCH"===I.method)&&("json"===I.bodyType?r["Content-Type"]="application/json":"form"===I.bodyType?r["Content-Type"]="application/x-www-form-urlencoded":"xml"===I.bodyType&&(r["Content-Type"]="application/xml"));let s={method:I.method||"GET",headers:r};if(I.body&&("POST"===I.method||"PUT"===I.method||"PATCH"===I.method))if("json"===I.bodyType)try{JSON.parse(I.body),s.body=I.body}catch(e){throw Error("Invalid JSON in request body")}else s.body=I.body;let t=await fetch(I.url,s),l=await t.text();try{o=JSON.parse(l)}catch(e){o=l}if(!t.ok)throw Error("HTTP ".concat(t.status,": ").concat(t.statusText));D({status:t.status,statusText:t.statusText,headers:Object.fromEntries(t.headers.entries()),data:o});let n=W(o);L(n),z({title:"API Test Successful!",description:"Request completed with status ".concat(t.status),status:"success",duration:3e3,isClosable:!0})}catch(o){let e=o instanceof Error?o.message:"Request failed";F(e),z({title:"API Test Failed",description:e,status:"error",duration:5e3,isClosable:!0})}finally{P(!1)}},W=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=[];return e&&"object"==typeof e&&(Array.isArray(e)?e.forEach((e,s)=>{let t=o?"".concat(o,".").concat(s):"".concat(s);r.push(t),"object"==typeof e&&null!==e&&r.push(...W(e,t))}):Object.keys(e).forEach(s=>{let t=o?"".concat(o,".").concat(s):s;r.push(t),"object"==typeof e[s]&&null!==e[s]&&r.push(...W(e[s],t))})),r},G=(e,o,r)=>{let s=[...I.headers||[]];s[e][o]=r,O({headers:s})},M=e=>{O({headers:(I.headers||[]).filter((o,r)=>r!==e)})},_=e=>{O({headers:[...I.headers||[],e]})};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(n.Box,{bg:v.colors.surface,border:"2px solid ".concat(f?"#06b6d4":v.colors.border),borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(l.h7,{type:"target",position:l.yX.Top,style:{background:"#06b6d4",border:"2px solid ".concat(v.colors.surface),width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(n.VStack,{spacing:1,align:"stretch",children:[(0,s.jsxs)(n.HStack,{justify:"space-between",align:"center",children:[(0,s.jsxs)(n.HStack,{spacing:1,children:[(0,s.jsx)(n.Box,{bg:"teal.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(a.VeH,{})}),(0,s.jsx)(n.Text,{fontSize:"xs",fontWeight:"bold",color:v.colors.text,children:"API Request"})]}),(0,s.jsx)(n.IconButton,{icon:(0,s.jsx)(a.VSk,{}),size:"xs",variant:"ghost",onClick:k,"aria-label":"Configure API request"})]}),(0,s.jsx)(n.Box,{children:(0,s.jsxs)(n.HStack,{spacing:1,children:[I.method&&(0,s.jsx)(n.Text,{fontSize:"xs",children:(e=>{switch(e){case"GET":return"\uD83D\uDCE5";case"POST":return"\uD83D\uDCE4";case"PUT":return"\uD83D\uDD04";case"PATCH":return"✏️";case"DELETE":return"\uD83D\uDDD1️";default:return"\uD83C\uDF10"}})(I.method)}),(0,s.jsxs)(n.Text,{fontSize:"xs",color:v.colors.text,noOfLines:1,children:[I.method||"GET"," Request"]})]})}),I.url&&(0,s.jsx)(n.Box,{children:(0,s.jsx)(n.Text,{fontSize:"xs",color:v.colors.textSecondary,noOfLines:1,children:I.url.length>25?I.url.substring(0,25)+"...":I.url})}),(0,s.jsxs)(n.HStack,{spacing:1,flexWrap:"wrap",children:[I.method&&(0,s.jsx)(n.Badge,{size:"xs",colorScheme:(e=>{let o=d.find(o=>o.value===e);return(null==o?void 0:o.color)||"gray"})(I.method),children:I.method}),(null!=(j=null==(o=I.headers)?void 0:o.length)?j:0)>0&&(0,s.jsxs)(n.Badge,{size:"xs",colorScheme:"blue",children:[null==(r=I.headers)?void 0:r.length," header",(null!=(g=null==(p=I.headers)?void 0:p.length)?g:0)!==1?"s":""]}),I.saveToVariable&&(0,s.jsx)(n.Badge,{size:"xs",colorScheme:"green",children:"Saves Data"})]})]}),(0,s.jsx)(l.h7,{type:"source",position:l.yX.Bottom,style:{background:"#06b6d4",border:"2px solid ".concat(v.colors.surface),width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,s.jsxs)(n.Modal,{isOpen:C,onClose:()=>{T&&y&&T(y,I),w()},size:"6xl",children:[(0,s.jsx)(n.ModalOverlay,{bg:"blackAlpha.600"}),(0,s.jsxs)(n.ModalContent,{bg:v.colors.background,border:"2px solid",borderColor:"teal.400",maxW:"1400px",children:[(0,s.jsx)(n.ModalHeader,{color:v.colors.text,children:"\uD83C\uDF10 Configure API Request"}),(0,s.jsx)(n.ModalCloseButton,{}),(0,s.jsx)(n.ModalBody,{pb:6,children:(0,s.jsxs)(n.VStack,{spacing:6,align:"stretch",children:[(0,s.jsxs)(n.Box,{children:[(0,s.jsxs)(n.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",color:v.colors.text,children:"Available Variables"}),(0,s.jsxs)(n.Button,{size:"sm",variant:"ghost",leftIcon:V?(0,s.jsx)(a._NO,{}):(0,s.jsx)(a.Vap,{}),onClick:()=>N(!V),children:[V?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(n.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsx)(n.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your API requests! Click any variable below to copy it. Variables are replaced with actual values when the request executes."})]}),(0,s.jsx)(n.Collapse,{in:V,animateOpacity:!0,children:(0,s.jsx)(n.Box,{bg:v.colors.surface,border:"1px solid",borderColor:v.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)(n.Accordion,{allowMultiple:!0,children:Object.entries(c).map(e=>{let[o,r]=e;return(0,s.jsxs)(n.AccordionItem,{border:"none",children:[(0,s.jsxs)(n.AccordionButton,{px:0,py:2,children:[(0,s.jsx)(n.Box,{flex:"1",textAlign:"left",children:(0,s.jsxs)(n.Text,{fontSize:"sm",fontWeight:"bold",color:v.colors.text,textTransform:"capitalize",children:[o," Variables"]})}),(0,s.jsx)(n.AccordionIcon,{})]}),(0,s.jsx)(n.AccordionPanel,{px:0,py:2,children:(0,s.jsx)(n.VStack,{spacing:2,align:"stretch",children:r.map(e=>(0,s.jsxs)(n.HStack,{spacing:2,p:2,bg:v.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:v.colors.surface},onClick:()=>q(e.name),children:[(0,s.jsx)(n.Text,{fontSize:"sm",children:e.icon}),(0,s.jsx)(n.Code,{fontSize:"xs",colorScheme:"teal",children:e.name}),(0,s.jsx)(n.Text,{fontSize:"xs",color:v.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(n.IconButton,{icon:(0,s.jsx)(a.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),q(e.name)}})]},e.name))})})]},o)})})})})]}),(0,s.jsx)(n.Divider,{}),(0,s.jsxs)(n.Tabs,{variant:"enclosed",colorScheme:"teal",children:[(0,s.jsxs)(n.TabList,{children:[(0,s.jsx)(n.Tab,{children:"Request"}),(0,s.jsx)(n.Tab,{children:"Headers"}),(0,s.jsx)(n.Tab,{children:"Body"}),(0,s.jsx)(n.Tab,{children:"Settings"}),(0,s.jsx)(n.Tab,{children:"Test"})]}),(0,s.jsxs)(n.TabPanels,{children:[(0,s.jsx)(n.TabPanel,{children:(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(n.FormControl,{isRequired:!0,children:[(0,s.jsx)(n.FormLabel,{color:v.colors.text,children:"Request URL"}),(0,s.jsx)(n.Input,{value:I.url||"",onChange:e=>O({url:e.target.value}),placeholder:"https://api.example.com/data or {server.webhook.url}",bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:v.colors.text,children:"HTTP Method"}),(0,s.jsx)(n.Select,{value:I.method||"GET",onChange:e=>O({method:e.target.value}),bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border,children:d.map(e=>(0,s.jsxs)("option",{value:e.value,children:[e.label," - ",e.description]},e.value))})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:v.colors.text,children:"Save Response To Variable"}),(0,s.jsx)(n.Input,{value:I.saveToVariable||"",onChange:e=>O({saveToVariable:e.target.value}),placeholder:"response_data (access with {response_data.field})",bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border}),(0,s.jsx)(n.Text,{fontSize:"xs",color:v.colors.textSecondary,mt:1,children:"Variable name to store the API response. Leave empty if you don't need the response."})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:v.colors.text,children:"Description"}),(0,s.jsx)(n.Textarea,{value:I.description||"",onChange:e=>O({description:e.target.value}),placeholder:"Describe what this API request does",bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border,minH:"80px"})]})]})}),(0,s.jsx)(n.TabPanel,{children:(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(n.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(n.Text,{fontSize:"lg",fontWeight:"bold",color:v.colors.text,children:"Request Headers"}),(0,s.jsx)(n.Button,{leftIcon:(0,s.jsx)(a.GGD,{}),onClick:()=>{O({headers:[...I.headers||[],{key:"",value:""}]})},colorScheme:"teal",size:"sm",children:"Add Header"})]}),(0,s.jsxs)(n.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsx)(n.AlertDescription,{fontSize:"sm",children:"Headers provide additional information about the request. Common headers are automatically set based on content type."})]}),(0,s.jsxs)(n.Box,{children:[(0,s.jsx)(n.Text,{fontSize:"md",fontWeight:"bold",color:v.colors.text,mb:2,children:"Quick Add Common Headers:"}),(0,s.jsx)(n.Wrap,{spacing:2,children:h.map((e,o)=>(0,s.jsx)(n.WrapItem,{children:(0,s.jsx)(n.Button,{size:"sm",variant:"outline",onClick:()=>_(e),leftIcon:(0,s.jsx)(a.GGD,{}),children:e.key})},o))})]}),(0,s.jsxs)(n.VStack,{spacing:3,align:"stretch",children:[null==(m=I.headers)?void 0:m.map((e,o)=>(0,s.jsxs)(n.Box,{p:3,bg:v.colors.surface,borderRadius:"md",border:"1px solid",borderColor:v.colors.border,children:[(0,s.jsxs)(n.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsxs)(n.Text,{fontSize:"sm",fontWeight:"bold",color:v.colors.text,children:["Header ",o+1]}),(0,s.jsx)(n.IconButton,{icon:(0,s.jsx)(a.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>M(o),"aria-label":"Remove header"})]}),(0,s.jsxs)(n.SimpleGrid,{columns:2,spacing:3,children:[(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{fontSize:"sm",color:v.colors.text,children:"Header Name"}),(0,s.jsx)(n.Input,{value:e.key,onChange:e=>G(o,"key",e.target.value),placeholder:"Authorization, Content-Type, etc.",bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border,size:"sm"})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{fontSize:"sm",color:v.colors.text,children:"Header Value"}),(0,s.jsx)(n.Input,{value:e.value,onChange:e=>G(o,"value",e.target.value),placeholder:"Bearer {user.token}, application/json, etc.",bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border,size:"sm"})]})]})]},o)),(!I.headers||0===I.headers.length)&&(0,s.jsxs)(n.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsx)(n.AlertDescription,{children:"No custom headers configured. Default headers will be set automatically based on request type."})]})]})]})}),(0,s.jsx)(n.TabPanel,{children:(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(n.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(n.Text,{fontSize:"lg",fontWeight:"bold",color:v.colors.text,children:"Request Body"}),(0,s.jsx)(n.Text,{fontSize:"sm",color:v.colors.textSecondary,children:"Only used for POST, PUT, PATCH requests"})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:v.colors.text,children:"Body Type"}),(0,s.jsx)(n.Select,{value:I.bodyType||"json",onChange:e=>O({bodyType:e.target.value}),bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border,children:x.map(e=>(0,s.jsxs)("option",{value:e.value,children:[e.label," - ",e.description]},e.value))})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:v.colors.text,children:"Request Body"}),(0,s.jsx)(n.Textarea,{value:I.body||"",onChange:e=>O({body:e.target.value}),placeholder:"json"===I.bodyType?'{"key": "value", "user": "{user.id}"}':"form"===I.bodyType?"key=value&user={user.id}":"Raw text content with {variables}",bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border,minH:"200px",fontFamily:"monospace",fontSize:"sm"}),(0,s.jsxs)(n.Text,{fontSize:"xs",color:v.colors.textSecondary,mt:1,children:["json"===I.bodyType&&"Must be valid JSON format","form"===I.bodyType&&"Use key=value&key2=value2 format","text"===I.bodyType&&"Plain text content"]})]}),(0,s.jsxs)(n.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsxs)(n.Box,{children:[(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:"\uD83D\uDCA1 Body Examples:"}),(0,s.jsxs)(n.VStack,{align:"start",spacing:1,fontSize:"sm",fontFamily:"monospace",children:[(0,s.jsxs)(n.Text,{children:["JSON: ",'{"message": "{message.content}", "user_id": "{user.id}"}']}),(0,s.jsxs)(n.Text,{children:["Form: user_id=","{user.id}","&message=","{message.content}"]}),(0,s.jsxs)(n.Text,{children:["Text: User ","{user.username}"," said: ","{message.content}"]})]})]})]})]})}),(0,s.jsx)(n.TabPanel,{children:(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(n.Text,{fontSize:"lg",fontWeight:"bold",color:v.colors.text,children:"Advanced Settings"}),(0,s.jsxs)(n.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:v.colors.text,children:"Timeout (milliseconds)"}),(0,s.jsxs)(n.NumberInput,{value:I.timeout||5e3,onChange:e=>O({timeout:parseInt(e)||5e3}),min:1e3,max:6e4,children:[(0,s.jsx)(n.NumberInputField,{bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border}),(0,s.jsxs)(n.NumberInputStepper,{children:[(0,s.jsx)(n.NumberIncrementStepper,{}),(0,s.jsx)(n.NumberDecrementStepper,{})]})]}),(0,s.jsx)(n.Text,{fontSize:"xs",color:v.colors.textSecondary,mt:1,children:"Maximum time to wait for response (5000ms = 5 seconds)"})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:v.colors.text,children:"Error Handling"}),(0,s.jsx)(n.Select,{value:I.errorHandling||"log",onChange:e=>O({errorHandling:e.target.value}),bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border,children:u.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,s.jsx)(n.Text,{fontSize:"xs",color:v.colors.textSecondary,mt:1,children:null==(b=u.find(e=>e.value===I.errorHandling))?void 0:b.description})]})]}),"retry"===I.errorHandling&&(0,s.jsxs)(n.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:v.colors.text,children:"Retry Count"}),(0,s.jsxs)(n.NumberInput,{value:I.retryCount||0,onChange:e=>O({retryCount:parseInt(e)||0}),min:0,max:5,children:[(0,s.jsx)(n.NumberInputField,{bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border}),(0,s.jsxs)(n.NumberInputStepper,{children:[(0,s.jsx)(n.NumberIncrementStepper,{}),(0,s.jsx)(n.NumberDecrementStepper,{})]})]})]}),(0,s.jsxs)(n.FormControl,{children:[(0,s.jsx)(n.FormLabel,{color:v.colors.text,children:"Retry Delay (ms)"}),(0,s.jsxs)(n.NumberInput,{value:I.retryDelay||1e3,onChange:e=>O({retryDelay:parseInt(e)||1e3}),min:500,max:1e4,children:[(0,s.jsx)(n.NumberInputField,{bg:v.colors.background,color:v.colors.text,borderColor:v.colors.border}),(0,s.jsxs)(n.NumberInputStepper,{children:[(0,s.jsx)(n.NumberIncrementStepper,{}),(0,s.jsx)(n.NumberDecrementStepper,{})]})]})]})]}),(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(n.HStack,{spacing:4,children:[(0,s.jsx)(n.Switch,{isChecked:I.followRedirects,onChange:e=>O({followRedirects:e.target.checked}),colorScheme:"teal"}),(0,s.jsxs)(n.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",color:v.colors.text,children:"Follow Redirects"}),(0,s.jsx)(n.Text,{fontSize:"xs",color:v.colors.textSecondary,children:"Automatically follow HTTP redirects (3xx responses)"})]})]}),(0,s.jsxs)(n.HStack,{spacing:4,children:[(0,s.jsx)(n.Switch,{isChecked:I.validateSSL,onChange:e=>O({validateSSL:e.target.checked}),colorScheme:"red"}),(0,s.jsxs)(n.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",color:v.colors.text,children:"Validate SSL Certificates"}),(0,s.jsx)(n.Text,{fontSize:"xs",color:v.colors.textSecondary,children:"Verify SSL certificates (disable only for testing)"})]})]})]})]})}),(0,s.jsx)(n.TabPanel,{children:(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(n.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(n.Text,{fontSize:"lg",fontWeight:"bold",color:v.colors.text,children:"Test API Request"}),(0,s.jsx)(n.Button,{leftIcon:R?(0,s.jsx)(n.Spinner,{size:"sm"}):(0,s.jsx)(a.aze,{}),onClick:U,colorScheme:"teal",isLoading:R,loadingText:"Testing...",isDisabled:!I.url,children:"Test Request"})]}),(0,s.jsxs)(n.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsx)(n.AlertDescription,{fontSize:"sm",children:"⚠️ This will make a real API request! Variables will not be replaced during testing. Make sure your URL and credentials are correct."})]}),B&&(0,s.jsxs)(n.Alert,{status:"error",borderRadius:"md",children:[(0,s.jsx)(n.AlertIcon,{}),(0,s.jsxs)(n.Box,{children:[(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:"Request Failed"}),(0,s.jsx)(n.Text,{fontSize:"sm",children:B})]})]}),H&&(0,s.jsxs)(n.Box,{children:[(0,s.jsx)(n.Text,{fontSize:"md",fontWeight:"bold",color:v.colors.text,mb:2,children:"Response:"}),(0,s.jsx)(n.Box,{p:4,bg:v.colors.surface,borderRadius:"md",border:"1px solid",borderColor:v.colors.border,maxH:"400px",overflowY:"auto",children:(0,s.jsxs)(n.VStack,{spacing:3,align:"stretch",children:[(0,s.jsxs)(n.HStack,{justify:"space-between",children:[(0,s.jsxs)(n.Badge,{colorScheme:"green",size:"lg",children:[H.status," ",H.statusText]}),(0,s.jsxs)(n.HStack,{spacing:2,children:[(0,s.jsx)(a.YrT,{color:"green"}),(0,s.jsx)(n.Text,{fontSize:"sm",color:"green.500",children:"Success"})]})]}),(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",color:v.colors.text,children:"Response Data:"}),(0,s.jsx)(n.Box,{bg:v.colors.background,p:3,borderRadius:"md",fontFamily:"monospace",fontSize:"xs",overflowX:"auto",children:(0,s.jsx)("pre",{children:JSON.stringify(H.data,null,2)})}),E.length>0&&(0,s.jsxs)(n.Box,{children:[(0,s.jsx)(n.Text,{fontSize:"sm",fontWeight:"bold",color:v.colors.text,mb:2,children:"Available Response Variables:"}),(0,s.jsxs)(n.VStack,{spacing:1,align:"stretch",maxH:"150px",overflowY:"auto",children:[E.slice(0,20).map(e=>(0,s.jsxs)(n.HStack,{spacing:2,p:2,bg:v.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:v.colors.surface},onClick:()=>q("{response.".concat(e,"}")),children:[(0,s.jsx)(n.Code,{fontSize:"xs",colorScheme:"teal",children:"{response.".concat(e,"}")}),(0,s.jsx)(n.IconButton,{icon:(0,s.jsx)(a.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable"})]},e)),E.length>20&&(0,s.jsxs)(n.Text,{fontSize:"xs",color:v.colors.textSecondary,children:["...and ",E.length-20," more variables"]})]})]})]})})]})]})})]})]}),(0,s.jsx)(n.Button,{colorScheme:"teal",onClick:()=>{S.url=I.url,S.method=I.method,S.headers=I.headers,S.body=I.body,S.bodyType=I.bodyType,S.timeout=I.timeout,S.saveToVariable=I.saveToVariable,S.errorHandling=I.errorHandling,S.description=I.description,S.retryCount=I.retryCount,S.retryDelay=I.retryDelay,S.followRedirects=I.followRedirects,S.validateSSL=I.validateSSL,S.label="".concat(I.method||"GET"," Request"),w()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});p.displayName="ApiRequestNode";let m=p}}]);