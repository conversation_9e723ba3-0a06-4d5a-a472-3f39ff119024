(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8063],{18306:(e,t,r)=>{"use strict";e.exports=r(84406)},37973:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),f=Symbol.for("react.context"),s=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),m=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case u:case i:case c:case d:case m:return e;default:switch(e=e&&e.$$typeof){case s:case f:case l:case y:case p:case a:return e;default:return t}}case o:return t}}}(e)===u}},48981:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>H});var n,o,u=r(54872),c=r(63535),i=r(94285);r(77117);var a=r(24051),f=r(40012),s={width:"1px",height:"0px",padding:0,overflow:"hidden",position:"fixed",top:"1px",left:"1px"},l=r(58766),d=(0,l.CL)({},function(e){return{target:e.target,currentTarget:e.currentTarget}}),m=(0,l.CL)(),p=(0,l.CL)(),y=(0,l.fi)({async:!0,ssr:"undefined"!=typeof document}),v=(0,i.createContext)(void 0),b=[],h=(0,i.forwardRef)(function(e,t){var r,n=(0,i.useState)(),o=n[0],u=n[1],l=(0,i.useRef)(),p=(0,i.useRef)(!1),h=(0,i.useRef)(null),S=(0,i.useState)({})[1],g=e.children,E=e.disabled,w=void 0!==E&&E,x=e.noFocusGuards,$=void 0!==x&&x,C=e.persistentFocus,F=e.crossFrame,A=e.autoFocus,M=(e.allowTextSelection,e.group),O=e.className,k=e.whiteList,L=e.hasPositiveIndices,_=e.shards,P=void 0===_?b:_,R=e.as,j=e.lockProps,I=e.sideCar,N=e.returnFocus,T=void 0!==N&&N,z=e.focusOptions,B=e.onActivation,G=e.onDeactivation,D=(0,i.useState)({})[0],V=(0,i.useCallback)(function(e){var t=e.captureFocusRestore;if(!h.current){var r,n=null==(r=document)?void 0:r.activeElement;h.current=n,n!==document.body&&(h.current=t(n))}l.current&&B&&B(l.current),p.current=!0,S()},[B]),U=(0,i.useCallback)(function(){p.current=!1,G&&G(l.current),S()},[G]),q=(0,i.useCallback)(function(e){var t=h.current;if(t){var r=("function"==typeof t?t():t)||document.body,n="function"==typeof T?T(r):T;if(n){var o="object"==typeof n?n:void 0;h.current=null,e?Promise.resolve().then(function(){return r.focus(o)}):r.focus(o)}}},[T]),K=(0,i.useCallback)(function(e){p.current&&d.useMedium(e)},[]),Q=m.useMedium,W=(0,i.useCallback)(function(e){l.current!==e&&(l.current=e,u(e))},[]),X=(0,c.A)(((r={})[a.iY]=w&&"disabled",r[a.dt]=M,r),void 0===j?{}:j),Y=!0!==$,H=Y&&"tail"!==$,J=(0,f.SV)([t,W]),Z=(0,i.useMemo)(function(){return{observed:l,shards:P,enabled:!w,active:p.current}},[w,p.current,P,o]);return i.createElement(i.Fragment,null,Y&&[i.createElement("div",{key:"guard-first","data-focus-guard":!0,tabIndex:w?-1:0,style:s}),L?i.createElement("div",{key:"guard-nearest","data-focus-guard":!0,tabIndex:w?-1:1,style:s}):null],!w&&i.createElement(I,{id:D,sideCar:y,observed:o,disabled:w,persistentFocus:void 0!==C&&C,crossFrame:void 0===F||F,autoFocus:void 0===A||A,whiteList:k,shards:P,onActivation:V,onDeactivation:U,returnFocus:q,focusOptions:z,noFocusGuards:$}),i.createElement(void 0===R?"div":R,(0,c.A)({ref:J},X,{className:O,onBlur:Q,onFocus:K}),i.createElement(v.Provider,{value:Z},g)),H&&i.createElement("div",{"data-focus-guard":!0,tabIndex:w?-1:0,style:s}))});h.propTypes={};var S=r(69194),g=r(91023),E=r(16156);function w(e){setTimeout(e,1)}var x=function(e){return e&&"current"in e?e.current:e},$=function(){return document&&document.activeElement===document.body},C=null,F=null,A=function(){return null},M=null,O=!1,k=!1,L=function(e,t){M={observerNode:e,portaledElement:t}};function _(e,t,r,n){var o=null,u=e;do{var c=n[u];if(c.guard)c.node.dataset.focusAutoGuard&&(o=c);else if(c.lockItem){if(u!==e)return;o=null}else break}while((u+=r)!==t);o&&(o.node.tabIndex=0)}var P=function(e){return(0,E.QK)(e,new Map)},R=function(){var e=!1;if(C){var t=C,r=t.observed,n=t.persistentFocus,o=t.autoFocus,u=t.shards,c=t.crossFrame,i=t.focusOptions,a=t.noFocusGuards,f=r||M&&M.portaledElement;if($()&&F&&F!==document.body&&(!document.body.contains(F)||!P([(d=F).parentNode]).some(function(e){return e.node===d}))){var s=A();s&&s.focus()}var l=document&&document.activeElement;if(f){var d,m=[f].concat(u.map(x).filter(Boolean));if((!l||(C.whiteList||function(){return!0})(l))&&(n||function(){if(!(c?!!O:"meanwhile"===O)||!a||!F||k)return!1;var e=P(m),t=e.findIndex(function(e){return e.node===F});return 0===t||t===e.length-1}()||!($()||(0,E.XM)())||!F&&o)&&(f&&!((0,E.Rb)(m)||l&&m.some(function(e){return function e(t,r,n){return r&&(r.host===t&&(!r.activeElement||n.contains(r.activeElement))||r.parentNode&&e(t,r.parentNode,n))}(l,e,e)})||M&&M.portaledElement===l)&&(document&&!F&&l&&!o?(l.blur&&l.blur(),document.body.focus()):(e=(0,E.OG)(m,F,{focusOptions:i}),M={})),(F=document&&document.activeElement)!==document.body&&(A=(0,E.fi)(F)),O=!1),document&&l!==document.activeElement&&document.querySelector("[data-focus-auto-guard]")){var p=document&&document.activeElement,y=(0,E.Ib)(m),v=y.map(function(e){return e.node}).indexOf(p);v>-1&&(y.filter(function(e){var t=e.guard,r=e.node;return t&&r.dataset.focusAutoGuard}).forEach(function(e){return e.node.removeAttribute("tabIndex")}),_(v,y.length,1,y),_(v,-1,-1,y))}}}return e},j=function(e){R()&&e&&(e.stopPropagation(),e.preventDefault())},I=function(){return w(R)},N=function(){k=!0},T=function(){k=!1,O="just",w(function(){O="meanwhile"})},z=function(){document.addEventListener("focusin",j),document.addEventListener("focusout",I),window.addEventListener("focus",N),window.addEventListener("blur",T)},B=function(){document.removeEventListener("focusin",j),document.removeEventListener("focusout",I),window.removeEventListener("focus",N),window.removeEventListener("blur",T)},G={moveFocusInside:E.OG,focusInside:E.Rb,focusNextElement:E.Mi,focusPrevElement:E.IO,focusFirstElement:E.Ry,focusLastElement:E.bL,captureFocusRestore:E.fi};d.assignSyncMedium(function(e){var t=e.target,r=e.currentTarget;r.contains(t)||L(r,t)}),m.assignMedium(I),p.assignMedium(function(e){return e(G)});let D=(n=function(e){return e.filter(function(e){return!e.disabled})},o=function(e){var t=e.slice(-1)[0];t&&!C&&z();var r=C,n=r&&t&&t.id===r.id;C=t,r&&!n&&(r.onDeactivation(),e.filter(function(e){return e.id===r.id}).length||r.returnFocus(!t)),t?(F=null,n&&r.observed===t.observed||t.onActivation(G),R(!0),w(R)):(B(),F=null)},function(e){var t,r=[];function u(){o(t=n(r.map(function(e){return e.props})))}var c=function(n){function o(){return n.apply(this,arguments)||this}(0,S.A)(o,n),o.peek=function(){return t};var c=o.prototype;return c.componentDidMount=function(){r.push(this),u()},c.componentDidUpdate=function(){u()},c.componentWillUnmount=function(){var e=r.indexOf(this);r.splice(e,1),u()},c.render=function(){return i.createElement(e,this.props)},o}(i.PureComponent);return(0,g.A)(c,"displayName","SideEffect("+(e.displayName||e.name||"Component")+")"),c})(function(){return null});var V=(0,i.forwardRef)(function(e,t){return i.createElement(h,(0,c.A)({sideCar:D,ref:t},e))}),U=h.propTypes||{};U.sideCar,(0,u.A)(U,["sideCar"]),V.propTypes={};var q=function(e){return e.map(extractRef).filter(Boolean)},K=function(e){return new Promise(function(t){return mediumEffect.useMedium(function(){t(e.apply(void 0,arguments))})})},Q={emit:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(var o=0,u=this.events[e]||[],c=u.length;o<c;o++)u[o].apply(u,r)},events:{},on:function(e,t){var r,n=this;return((r=this.events)[e]||(r[e]=[])).push(t),function(){var r;n.events[e]=null==(r=n.events[e])?void 0:r.filter(function(e){return t!==e})}}},W=0,X=function(e){return Q.emit("assign",e.target)},Y=function(e){return Q.emit("reset",e.target)};let H=V},63449:(e,t,r)=>{"use strict";e.exports=r(37973)},64757:e=>{var t="undefined"!=typeof Element,r="function"==typeof Map,n="function"==typeof Set,o="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;e.exports=function(e,u){try{return function e(u,c){if(u===c)return!0;if(u&&c&&"object"==typeof u&&"object"==typeof c){var i,a,f,s;if(u.constructor!==c.constructor)return!1;if(Array.isArray(u)){if((i=u.length)!=c.length)return!1;for(a=i;0!=a--;)if(!e(u[a],c[a]))return!1;return!0}if(r&&u instanceof Map&&c instanceof Map){if(u.size!==c.size)return!1;for(s=u.entries();!(a=s.next()).done;)if(!c.has(a.value[0]))return!1;for(s=u.entries();!(a=s.next()).done;)if(!e(a.value[1],c.get(a.value[0])))return!1;return!0}if(n&&u instanceof Set&&c instanceof Set){if(u.size!==c.size)return!1;for(s=u.entries();!(a=s.next()).done;)if(!c.has(a.value[0]))return!1;return!0}if(o&&ArrayBuffer.isView(u)&&ArrayBuffer.isView(c)){if((i=u.length)!=c.length)return!1;for(a=i;0!=a--;)if(u[a]!==c[a])return!1;return!0}if(u.constructor===RegExp)return u.source===c.source&&u.flags===c.flags;if(u.valueOf!==Object.prototype.valueOf&&"function"==typeof u.valueOf&&"function"==typeof c.valueOf)return u.valueOf()===c.valueOf();if(u.toString!==Object.prototype.toString&&"function"==typeof u.toString&&"function"==typeof c.toString)return u.toString()===c.toString();if((i=(f=Object.keys(u)).length)!==Object.keys(c).length)return!1;for(a=i;0!=a--;)if(!Object.prototype.hasOwnProperty.call(c,f[a]))return!1;if(t&&u instanceof Element)return!1;for(a=i;0!=a--;)if(("_owner"!==f[a]&&"__v"!==f[a]&&"__o"!==f[a]||!u.$$typeof)&&!e(u[f[a]],c[f[a]]))return!1;return!0}return u!=u&&c!=c}(e,u)}catch(e){if((e.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw e}}},84406:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,o=r?Symbol.for("react.portal"):60106,u=r?Symbol.for("react.fragment"):60107,c=r?Symbol.for("react.strict_mode"):60108,i=r?Symbol.for("react.profiler"):60114,a=r?Symbol.for("react.provider"):60109,f=r?Symbol.for("react.context"):60110,s=r?Symbol.for("react.async_mode"):60111,l=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,m=r?Symbol.for("react.suspense"):60113,p=r?Symbol.for("react.suspense_list"):60120,y=r?Symbol.for("react.memo"):60115,v=r?Symbol.for("react.lazy"):60116,b=r?Symbol.for("react.block"):60121,h=r?Symbol.for("react.fundamental"):60117,S=r?Symbol.for("react.responder"):60118,g=r?Symbol.for("react.scope"):60119;function E(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case s:case l:case u:case i:case c:case m:return e;default:switch(e=e&&e.$$typeof){case f:case d:case v:case y:case a:return e;default:return t}}case o:return t}}}function w(e){return E(e)===l}t.AsyncMode=s,t.ConcurrentMode=l,t.ContextConsumer=f,t.ContextProvider=a,t.Element=n,t.ForwardRef=d,t.Fragment=u,t.Lazy=v,t.Memo=y,t.Portal=o,t.Profiler=i,t.StrictMode=c,t.Suspense=m,t.isAsyncMode=function(e){return w(e)||E(e)===s},t.isConcurrentMode=w,t.isContextConsumer=function(e){return E(e)===f},t.isContextProvider=function(e){return E(e)===a},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return E(e)===d},t.isFragment=function(e){return E(e)===u},t.isLazy=function(e){return E(e)===v},t.isMemo=function(e){return E(e)===y},t.isPortal=function(e){return E(e)===o},t.isProfiler=function(e){return E(e)===i},t.isStrictMode=function(e){return E(e)===c},t.isSuspense=function(e){return E(e)===m},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===u||e===l||e===i||e===c||e===m||e===p||"object"==typeof e&&null!==e&&(e.$$typeof===v||e.$$typeof===y||e.$$typeof===a||e.$$typeof===f||e.$$typeof===d||e.$$typeof===h||e.$$typeof===S||e.$$typeof===g||e.$$typeof===b)},t.typeOf=E}}]);