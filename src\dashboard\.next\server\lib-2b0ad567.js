exports.id=8360,exports.ids=[8360],exports.modules={24036:(e,t)=>{"use strict";function n(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function r(e){var t=n(e).Element;return e instanceof t||e instanceof Element}function o(e){var t=n(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function i(e){if("undefined"==typeof ShadowRoot)return!1;var t=n(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var a=Math.max,s=Math.min,f=Math.round;function c(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function p(){return!/^((?!chrome|android).)*safari/i.test(c())}function u(e,t,i){void 0===t&&(t=!1),void 0===i&&(i=!1);var a=e.getBoundingClientRect(),s=1,c=1;t&&o(e)&&(s=e.offsetWidth>0&&f(a.width)/e.offsetWidth||1,c=e.offsetHeight>0&&f(a.height)/e.offsetHeight||1);var u=(r(e)?n(e):window).visualViewport,l=!p()&&i,d=(a.left+(l&&u?u.offsetLeft:0))/s,h=(a.top+(l&&u?u.offsetTop:0))/c,m=a.width/s,v=a.height/c;return{width:m,height:v,top:h,right:d+m,bottom:h+v,left:d,x:d,y:h}}function l(e){var t=n(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function d(e){return e?(e.nodeName||"").toLowerCase():null}function h(e){return((r(e)?e.ownerDocument:e.document)||window.document).documentElement}function m(e){return u(h(e)).left+l(e).scrollLeft}function v(e){return n(e).getComputedStyle(e)}function y(e){var t=v(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function g(e){var t=u(e),n=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(t.width-n)&&(n=t.width),1>=Math.abs(t.height-r)&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function b(e){return"html"===d(e)?e:e.assignedSlot||e.parentNode||(i(e)?e.host:null)||h(e)}function x(e,t){void 0===t&&(t=[]);var r,i=function e(t){return["html","body","#document"].indexOf(d(t))>=0?t.ownerDocument.body:o(t)&&y(t)?t:e(b(t))}(e),a=i===(null==(r=e.ownerDocument)?void 0:r.body),s=n(i),f=a?[s].concat(s.visualViewport||[],y(i)?i:[]):i,c=t.concat(f);return a?c:c.concat(x(b(f)))}function w(e){return o(e)&&"fixed"!==v(e).position?e.offsetParent:null}function O(e){for(var t=n(e),r=w(e);r&&["table","td","th"].indexOf(d(r))>=0&&"static"===v(r).position;)r=w(r);return r&&("html"===d(r)||"body"===d(r)&&"static"===v(r).position)?t:r||function(e){var t=/firefox/i.test(c());if(/Trident/i.test(c())&&o(e)&&"fixed"===v(e).position)return null;var n=b(e);for(i(n)&&(n=n.host);o(n)&&0>["html","body"].indexOf(d(n));){var r=v(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}var j="bottom",E="right",D="left",A="auto",P=["top",j,E,D],T="start",k="viewport",L="popper",W=P.reduce(function(e,t){return e.concat([t+"-"+T,t+"-end"])},[]),R=[].concat(P,[A]).reduce(function(e,t){return e.concat([t,t+"-"+T,t+"-end"])},[]),M=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function B(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&i(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function H(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function S(e,t,o){var i,s,f,c,d,y,g,b,x,w;return t===k?H(function(e,t){var r=n(e),o=h(e),i=r.visualViewport,a=o.clientWidth,s=o.clientHeight,f=0,c=0;if(i){a=i.width,s=i.height;var u=p();(u||!u&&"fixed"===t)&&(f=i.offsetLeft,c=i.offsetTop)}return{width:a,height:s,x:f+m(e),y:c}}(e,o)):r(t)?((i=u(t,!1,"fixed"===o)).top=i.top+t.clientTop,i.left=i.left+t.clientLeft,i.bottom=i.top+t.clientHeight,i.right=i.left+t.clientWidth,i.width=t.clientWidth,i.height=t.clientHeight,i.x=i.left,i.y=i.top,i):H((s=h(e),c=h(s),d=l(s),y=null==(f=s.ownerDocument)?void 0:f.body,g=a(c.scrollWidth,c.clientWidth,y?y.scrollWidth:0,y?y.clientWidth:0),b=a(c.scrollHeight,c.clientHeight,y?y.scrollHeight:0,y?y.clientHeight:0),x=-d.scrollLeft+m(s),w=-d.scrollTop,"rtl"===v(y||c).direction&&(x+=a(c.clientWidth,y?y.clientWidth:0)-g),{width:g,height:b,x:x,y:w}))}function C(e){return e.split("-")[0]}function V(e){return e.split("-")[1]}function _(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function q(e){var t,n=e.reference,r=e.element,o=e.placement,i=o?C(o):null,a=o?V(o):null,s=n.x+n.width/2-r.width/2,f=n.y+n.height/2-r.height/2;switch(i){case"top":t={x:s,y:n.y-r.height};break;case j:t={x:s,y:n.y+n.height};break;case E:t={x:n.x+n.width,y:f};break;case D:t={x:n.x-r.width,y:f};break;default:t={x:n.x,y:n.y}}var c=i?_(i):null;if(null!=c){var p="y"===c?"height":"width";switch(a){case T:t[c]=t[c]-(n[p]/2-r[p]/2);break;case"end":t[c]=t[c]+(n[p]/2-r[p]/2)}}return t}function I(){return{top:0,right:0,bottom:0,left:0}}function N(e){return Object.assign({},I(),e)}function U(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}function F(e,t){void 0===t&&(t={});var n,i,f,c,p,l,m,y,g=t,w=g.placement,D=void 0===w?e.placement:w,A=g.strategy,T=void 0===A?e.strategy:A,W=g.boundary,R=g.rootBoundary,M=g.elementContext,C=void 0===M?L:M,V=g.altBoundary,_=g.padding,I=void 0===_?0:_,F=N("number"!=typeof I?I:U(I,P)),z=e.rects.popper,Y=e.elements[void 0!==V&&V?C===L?"reference":L:C],X=(n=r(Y)?Y:Y.contextElement||h(e.elements.popper),i=void 0===W?"clippingParents":W,f=void 0===R?k:R,m=(l=[].concat("clippingParents"===i?(c=x(b(n)),!r(p=["absolute","fixed"].indexOf(v(n).position)>=0&&o(n)?O(n):n)?[]:c.filter(function(e){return r(e)&&B(e,p)&&"body"!==d(e)})):[].concat(i),[f]))[0],(y=l.reduce(function(e,t){var r=S(n,t,T);return e.top=a(r.top,e.top),e.right=s(r.right,e.right),e.bottom=s(r.bottom,e.bottom),e.left=a(r.left,e.left),e},S(n,m,T))).width=y.right-y.left,y.height=y.bottom-y.top,y.x=y.left,y.y=y.top,y),G=u(e.elements.reference),J=q({reference:G,element:z,strategy:"absolute",placement:D}),K=H(Object.assign({},z,J)),Q=C===L?K:G,Z={top:X.top-Q.top+F.top,bottom:Q.bottom-X.bottom+F.bottom,left:X.left-Q.left+F.left,right:Q.right-X.right+F.right},$=e.modifiersData.offset;if(C===L&&$){var ee=$[D];Object.keys(Z).forEach(function(e){var t=[E,j].indexOf(e)>=0?1:-1,n=["top",j].indexOf(e)>=0?"y":"x";Z[e]+=ee[n]*t})}return Z}var z={placement:"bottom",modifiers:[],strategy:"absolute"};function Y(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}function X(e){void 0===e&&(e={});var t=e,i=t.defaultModifiers,a=void 0===i?[]:i,s=t.defaultOptions,c=void 0===s?z:s;return function(e,t,i){void 0===i&&(i=c);var s,p,v={placement:"bottom",orderedModifiers:[],options:Object.assign({},z,c),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},b=[],w=!1,j={state:v,setOptions:function(n){var o,i,s,f,p,u,l="function"==typeof n?n(v.options):n;E(),v.options=Object.assign({},c,v.options,l),v.scrollParents={reference:r(e)?x(e):e.contextElement?x(e.contextElement):[],popper:x(t)};var d=(i=Object.keys(o=[].concat(a,v.options.modifiers).reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{})).map(function(e){return o[e]}),s=new Map,f=new Set,p=[],i.forEach(function(e){s.set(e.name,e)}),i.forEach(function(e){f.has(e.name)||function e(t){f.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!f.has(t)){var n=s.get(t);n&&e(n)}}),p.push(t)}(e)}),u=p,M.reduce(function(e,t){return e.concat(u.filter(function(e){return e.phase===t}))},[]));return v.orderedModifiers=d.filter(function(e){return e.enabled}),v.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=e.effect;if("function"==typeof r){var o=r({state:v,name:t,instance:j,options:void 0===n?{}:n});b.push(o||function(){})}}),j.update()},forceUpdate:function(){if(!w){var e=v.elements,t=e.reference,r=e.popper;if(Y(t,r)){v.rects={reference:(i=O(r),a="fixed"===v.options.strategy,s=o(i),x=o(i)&&(p=f((c=i.getBoundingClientRect()).width)/i.offsetWidth||1,b=f(c.height)/i.offsetHeight||1,1!==p||1!==b),E=h(i),D=u(t,x,a),A={scrollLeft:0,scrollTop:0},P={x:0,y:0},(s||!s&&!a)&&(("body"!==d(i)||y(E))&&(A=function(e){return e!==n(e)&&o(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:l(e)}(i)),o(i)?(P=u(i,!0),P.x+=i.clientLeft,P.y+=i.clientTop):E&&(P.x=m(E))),{x:D.left+A.scrollLeft-P.x,y:D.top+A.scrollTop-P.y,width:D.width,height:D.height}),popper:g(r)},v.reset=!1,v.placement=v.options.placement,v.orderedModifiers.forEach(function(e){return v.modifiersData[e.name]=Object.assign({},e.data)});for(var i,a,s,c,p,b,x,E,D,A,P,T=0;T<v.orderedModifiers.length;T++){if(!0===v.reset){v.reset=!1,T=-1;continue}var k=v.orderedModifiers[T],L=k.fn,W=k.options,R=void 0===W?{}:W,M=k.name;"function"==typeof L&&(v=L({state:v,options:R,name:M,instance:j})||v)}}}},update:(s=function(){return new Promise(function(e){j.forceUpdate(),e(v)})},function(){return p||(p=new Promise(function(e){Promise.resolve().then(function(){p=void 0,e(s())})})),p}),destroy:function(){E(),w=!0}};if(!Y(e,t))return j;function E(){b.forEach(function(e){return e()}),b=[]}return j.setOptions(i).then(function(e){!w&&i.onFirstUpdate&&i.onFirstUpdate(e)}),j}}var G={passive:!0},J={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,r=e.instance,o=e.options,i=o.scroll,a=void 0===i||i,s=o.resize,f=void 0===s||s,c=n(t.elements.popper),p=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&p.forEach(function(e){e.addEventListener("scroll",r.update,G)}),f&&c.addEventListener("resize",r.update,G),function(){a&&p.forEach(function(e){e.removeEventListener("scroll",r.update,G)}),f&&c.removeEventListener("resize",r.update,G)}},data:{}},K={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=q({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},Q={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Z(e){var t,r,o,i,a,s,c,p=e.popper,u=e.popperRect,l=e.placement,d=e.variation,m=e.offsets,y=e.position,g=e.gpuAcceleration,b=e.adaptive,x=e.roundOffsets,w=e.isFixed,A=m.x,P=void 0===A?0:A,T=m.y,k=void 0===T?0:T,L="function"==typeof x?x({x:P,y:k}):{x:P,y:k};P=L.x,k=L.y;var W=m.hasOwnProperty("x"),R=m.hasOwnProperty("y"),M=D,B="top",H=window;if(b){var S=O(p),C="clientHeight",V="clientWidth";S===n(p)&&"static"!==v(S=h(p)).position&&"absolute"===y&&(C="scrollHeight",V="scrollWidth"),("top"===l||(l===D||l===E)&&"end"===d)&&(B=j,k-=(w&&S===H&&H.visualViewport?H.visualViewport.height:S[C])-u.height,k*=g?1:-1),(l===D||("top"===l||l===j)&&"end"===d)&&(M=E,P-=(w&&S===H&&H.visualViewport?H.visualViewport.width:S[V])-u.width,P*=g?1:-1)}var _=Object.assign({position:y},b&&Q),q=!0===x?(t={x:P,y:k},r=n(p),o=t.x,i=t.y,{x:f(o*(a=r.devicePixelRatio||1))/a||0,y:f(i*a)/a||0}):{x:P,y:k};return(P=q.x,k=q.y,g)?Object.assign({},_,((c={})[B]=R?"0":"",c[M]=W?"0":"",c.transform=1>=(H.devicePixelRatio||1)?"translate("+P+"px, "+k+"px)":"translate3d("+P+"px, "+k+"px, 0)",c)):Object.assign({},_,((s={})[B]=R?k+"px":"",s[M]=W?P+"px":"",s.transform="",s))}var $={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=n.adaptive,i=n.roundOffsets,a=void 0===i||i,s={placement:C(t.placement),variation:V(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Z(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===o||o,roundOffsets:a})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Z(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},ee={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},i=t.elements[e];o(i)&&d(i)&&(Object.assign(i.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],i=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});o(r)&&d(r)&&(Object.assign(r.style,a),Object.keys(i).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]},et={left:"right",right:"left",bottom:"top",top:"bottom"};function en(e){return e.replace(/left|right|bottom|top/g,function(e){return et[e]})}var er={start:"end",end:"start"};function eo(e){return e.replace(/start|end/g,function(e){return er[e]})}function ei(e,t,n){return a(e,s(t,n))}function ea(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function es(e){return["top",E,j,D].some(function(t){return e[t]>=0})}t.n4=X({defaultModifiers:[J,K,$,ee,{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,i=void 0===o?[0,0]:o,a=R.reduce(function(e,n){var r,o,a,s,f,c;return e[n]=(r=t.rects,a=[D,"top"].indexOf(o=C(n))>=0?-1:1,f=(s="function"==typeof i?i(Object.assign({},r,{placement:n})):i)[0],c=s[1],f=f||0,c=(c||0)*a,[D,E].indexOf(o)>=0?{x:c,y:f}:{x:f,y:c}),e},{}),s=a[t.placement],f=s.x,c=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=f,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=a}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,i=void 0===o||o,a=n.altAxis,s=void 0===a||a,f=n.fallbackPlacements,c=n.padding,p=n.boundary,u=n.rootBoundary,l=n.altBoundary,d=n.flipVariations,h=void 0===d||d,m=n.allowedAutoPlacements,v=t.options.placement,y=C(v)===v,g=f||(y||!h?[en(v)]:function(e){if(C(e)===A)return[];var t=en(e);return[eo(e),t,eo(t)]}(v)),b=[v].concat(g).reduce(function(e,n){var r,o,i,a,s,f,l,d,v,y,g,b;return e.concat(C(n)===A?(o=(r={placement:n,boundary:p,rootBoundary:u,padding:c,flipVariations:h,allowedAutoPlacements:m}).placement,i=r.boundary,a=r.rootBoundary,s=r.padding,f=r.flipVariations,d=void 0===(l=r.allowedAutoPlacements)?R:l,0===(g=(y=(v=V(o))?f?W:W.filter(function(e){return V(e)===v}):P).filter(function(e){return d.indexOf(e)>=0})).length&&(g=y),Object.keys(b=g.reduce(function(e,n){return e[n]=F(t,{placement:n,boundary:i,rootBoundary:a,padding:s})[C(n)],e},{})).sort(function(e,t){return b[e]-b[t]})):n)},[]),x=t.rects.reference,w=t.rects.popper,O=new Map,k=!0,L=b[0],M=0;M<b.length;M++){var B=b[M],H=C(B),S=V(B)===T,_=["top",j].indexOf(H)>=0,q=_?"width":"height",I=F(t,{placement:B,boundary:p,rootBoundary:u,altBoundary:l,padding:c}),N=_?S?E:D:S?j:"top";x[q]>w[q]&&(N=en(N));var U=en(N),z=[];if(i&&z.push(I[H]<=0),s&&z.push(I[N]<=0,I[U]<=0),z.every(function(e){return e})){L=B,k=!1;break}O.set(B,z)}if(k)for(var Y=h?3:1,X=function(e){var t=b.find(function(t){var n=O.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return L=t,"break"},G=Y;G>0&&"break"!==X(G);G--);t.placement!==L&&(t.modifiersData[r]._skip=!0,t.placement=L,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,i=n.altAxis,f=n.boundary,c=n.rootBoundary,p=n.altBoundary,u=n.padding,l=n.tether,d=void 0===l||l,h=n.tetherOffset,m=void 0===h?0:h,v=F(t,{boundary:f,rootBoundary:c,padding:u,altBoundary:p}),y=C(t.placement),b=V(t.placement),x=!b,w=_(y),A="x"===w?"y":"x",P=t.modifiersData.popperOffsets,k=t.rects.reference,L=t.rects.popper,W="function"==typeof m?m(Object.assign({},t.rects,{placement:t.placement})):m,R="number"==typeof W?{mainAxis:W,altAxis:W}:Object.assign({mainAxis:0,altAxis:0},W),M=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,B={x:0,y:0};if(P){if(void 0===o||o){var H,S="y"===w?"top":D,q="y"===w?j:E,N="y"===w?"height":"width",U=P[w],z=U+v[S],Y=U-v[q],X=d?-L[N]/2:0,G=b===T?k[N]:L[N],J=b===T?-L[N]:-k[N],K=t.elements.arrow,Q=d&&K?g(K):{width:0,height:0},Z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:I(),$=Z[S],ee=Z[q],et=ei(0,k[N],Q[N]),en=x?k[N]/2-X-et-$-R.mainAxis:G-et-$-R.mainAxis,er=x?-k[N]/2+X+et+ee+R.mainAxis:J+et+ee+R.mainAxis,eo=t.elements.arrow&&O(t.elements.arrow),ea=eo?"y"===w?eo.clientTop||0:eo.clientLeft||0:0,es=null!=(H=null==M?void 0:M[w])?H:0,ef=ei(d?s(z,U+en-es-ea):z,U,d?a(Y,U+er-es):Y);P[w]=ef,B[w]=ef-U}if(void 0!==i&&i){var ec,ep,eu="x"===w?"top":D,el="x"===w?j:E,ed=P[A],eh="y"===A?"height":"width",em=ed+v[eu],ev=ed-v[el],ey=-1!==["top",D].indexOf(y),eg=null!=(ep=null==M?void 0:M[A])?ep:0,eb=ey?em:ed-k[eh]-L[eh]-eg+R.altAxis,ex=ey?ed+k[eh]+L[eh]-eg-R.altAxis:ev,ew=d&&ey?(ec=ei(eb,ed,ex))>ex?ex:ec:ei(d?eb:em,ed,d?ex:ev);P[A]=ew,B[A]=ew-ed}t.modifiersData[r]=B}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,s=C(n.placement),f=_(s),c=[D,E].indexOf(s)>=0?"height":"width";if(i&&a){var p,u=(p=o.padding,N("number"!=typeof(p="function"==typeof p?p(Object.assign({},n.rects,{placement:n.placement})):p)?p:U(p,P))),l=g(i),d="y"===f?"top":D,h="y"===f?j:E,m=n.rects.reference[c]+n.rects.reference[f]-a[f]-n.rects.popper[c],v=a[f]-n.rects.reference[f],y=O(i),b=y?"y"===f?y.clientHeight||0:y.clientWidth||0:0,x=u[d],w=b-l[c]-u[h],A=b/2-l[c]/2+(m/2-v/2),T=ei(x,A,w);n.modifiersData[r]=((t={})[f]=T,t.centerOffset=T-A,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;if(null!=r)("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&B(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=F(t,{elementContext:"reference"}),s=F(t,{altBoundary:!0}),f=ea(a,r),c=ea(s,o,i),p=es(f),u=es(c);t.modifiersData[n]={referenceClippingOffsets:f,popperEscapeOffsets:c,isReferenceHidden:p,hasPopperEscaped:u},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":u})}}]})},24796:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},37079:(e,t,n)=>{e.exports=n(37121)()},37121:(e,t,n)=>{"use strict";var r=n(24796);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,i,a){if(a!==r){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}}};