import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { dashboardConfig } from '../../../../core/config';
import { MongoClient } from 'mongodb';

// Reuse connection pattern
let cachedClient: MongoClient | null = null;
const mongoUrl = dashboardConfig.database?.url || 'mongodb://localhost:27017';
const dbName = dashboardConfig.database?.name || 'discord_bot';

async function getDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl, {
      ...(dashboardConfig.database?.options || {}),
    });
  }
  return cachedClient.db(dbName);
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check admin permission
    const isAdmin = (session.user as any).isAdmin;
    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const db = await getDb();
    
    // Get today's date range
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

    // Get yesterday's date range for trend calculation
    const yesterday = new Date(startOfDay.getTime() - 24 * 60 * 60 * 1000);
    const startOfYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());

    // Fetch error logs (latest 100)
    const errors = await db.collection('error_logs')
      .find({})
      .sort({ timestamp: -1 })
      .limit(100)
      .toArray()
      .catch(() => []);

    // Calculate statistics
    const totalErrors = await db.collection('error_logs')
      .countDocuments({})
      .catch(() => 0);

    const errorsToday = await db.collection('error_logs')
      .countDocuments({
        timestamp: { $gte: startOfDay, $lt: endOfDay }
      })
      .catch(() => 0);

    const errorsYesterday = await db.collection('error_logs')
      .countDocuments({
        timestamp: { $gte: startOfYesterday, $lt: startOfDay }
      })
      .catch(() => 0);

    // Calculate trend
    let recentTrend: 'up' | 'down' | 'stable' = 'stable';
    if (errorsToday > errorsYesterday) {
      recentTrend = 'up';
    } else if (errorsToday < errorsYesterday) {
      recentTrend = 'down';
    }

    // Get error counts by type
    const errorsByTypeAgg = await db.collection('error_logs')
      .aggregate([
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 }
          }
        }
      ])
      .toArray()
      .catch(() => []);

    const errorsByType: Record<string, number> = {};
    errorsByTypeAgg.forEach(item => {
      errorsByType[item._id] = item.count;
    });

    const stats = {
      totalErrors,
      errorsToday,
      errorsByType,
      recentTrend,
    };

    res.status(200).json({
      errors: errors.map(error => ({
        ...error,
        _id: error._id.toString(),
      })),
      stats,
    });
  } catch (error: any) {
    console.error('Error fetching error logs:', error);
    res.status(500).json({ error: 'Internal server error', details: error.message });
  }
} 