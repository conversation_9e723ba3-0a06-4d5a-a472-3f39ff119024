"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/discord/roles";
exports.ids = ["pages/api/discord/roles"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Froles&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Croles%5Cindex.ts&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Froles&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Croles%5Cindex.ts&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_discord_roles_index_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\discord\\roles\\index.ts */ \"(api-node)/./pages/api/discord/roles/index.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_roles_index_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_roles_index_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/discord/roles\",\n        pathname: \"/api/discord/roles\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_discord_roles_index_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Froles&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Croles%5Cindex.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/discord/roles/index.ts":
/*!******************************************!*\
  !*** ./pages/api/discord/roles/index.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../core/config */ \"(api-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nasync function handler(req, res) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session?.user) {\n            return res.status(401).json({\n                error: 'Unauthorized'\n            });\n        }\n        // For sensitive operations like creating roles we require admin.\n        const isAdmin = session.user.isAdmin;\n        if (!isAdmin) {\n            return res.status(403).json({\n                error: 'Forbidden - Admin access required'\n            });\n        }\n        const { guildId, token } = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot;\n        if (!token || !guildId) {\n            console.error('Missing bot configuration');\n            return res.status(500).json({\n                error: 'Bot configuration missing'\n            });\n        }\n        if (req.method === 'POST') {\n            try {\n                const { name, color, permissions = [], hoist, mentionable } = req.body;\n                if (!name) {\n                    return res.status(400).json({\n                        error: 'Role name is required'\n                    });\n                }\n                // Helper map of Discord permission flags to their bit values\n                const PERMISSION_BIT_MAP = {\n                    // General\n                    CREATE_INSTANT_INVITE: 1n << 0n,\n                    KICK_MEMBERS: 1n << 1n,\n                    BAN_MEMBERS: 1n << 2n,\n                    ADMINISTRATOR: 1n << 3n,\n                    MANAGE_CHANNELS: 1n << 4n,\n                    MANAGE_GUILD: 1n << 5n,\n                    ADD_REACTIONS: 1n << 6n,\n                    VIEW_AUDIT_LOG: 1n << 7n,\n                    PRIORITY_SPEAKER: 1n << 8n,\n                    STREAM: 1n << 9n,\n                    VIEW_CHANNEL: 1n << 10n,\n                    SEND_MESSAGES: 1n << 11n,\n                    SEND_TTS_MESSAGES: 1n << 12n,\n                    MANAGE_MESSAGES: 1n << 13n,\n                    EMBED_LINKS: 1n << 14n,\n                    ATTACH_FILES: 1n << 15n,\n                    READ_MESSAGE_HISTORY: 1n << 16n,\n                    MENTION_EVERYONE: 1n << 17n,\n                    USE_EXTERNAL_EMOJIS: 1n << 18n,\n                    // 1n << 19n is currently VIEW_GUILD_INSIGHTS which is not used in UI\n                    CONNECT: 1n << 20n,\n                    SPEAK: 1n << 21n,\n                    MUTE_MEMBERS: 1n << 22n,\n                    DEAFEN_MEMBERS: 1n << 23n,\n                    MOVE_MEMBERS: 1n << 24n,\n                    USE_VAD: 1n << 25n,\n                    CHANGE_NICKNAME: 1n << 26n,\n                    MANAGE_NICKNAMES: 1n << 27n,\n                    MANAGE_ROLES: 1n << 28n,\n                    MANAGE_WEBHOOKS: 1n << 29n,\n                    MANAGE_EMOJIS_AND_STICKERS: 1n << 30n,\n                    USE_APPLICATION_COMMANDS: 1n << 31n\n                };\n                // Calculate the permission bitfield from the provided permission names (if any)\n                let permissionBits = 0n;\n                if (Array.isArray(permissions)) {\n                    for (const perm of permissions){\n                        const bit = PERMISSION_BIT_MAP[perm];\n                        if (typeof bit === 'bigint') {\n                            permissionBits |= bit;\n                        }\n                    }\n                }\n                // Build the role payload for Discord\n                const roleData = {\n                    name,\n                    hoist: !!hoist,\n                    mentionable: !!mentionable\n                };\n                // Only include color if provided\n                if (color) {\n                    const colorDecimal = parseInt(color.replace('#', ''), 16);\n                    if (!isNaN(colorDecimal)) {\n                        roleData.color = colorDecimal;\n                    }\n                }\n                // Only include permissions if at least one permission flag was set\n                if (permissionBits !== 0n) {\n                    // Discord expects the permissions value to be sent as a string-encoded integer\n                    roleData.permissions = permissionBits.toString();\n                }\n                console.log('Creating role with data:', roleData);\n                const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/roles`, {\n                    method: 'POST',\n                    headers: {\n                        Authorization: `Bot ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(roleData)\n                });\n                if (!response.ok) {\n                    let error;\n                    try {\n                        error = await response.json();\n                    } catch  {\n                        error = await response.text();\n                    }\n                    console.error('Discord API error:', error);\n                    return res.status(response.status).json(error);\n                }\n                const newRole = await response.json();\n                return res.status(201).json(newRole);\n            } catch (error) {\n                console.error('Error creating role:', error);\n                return res.status(500).json({\n                    error: 'Failed to create role'\n                });\n            }\n        }\n        if (req.method === 'GET') {\n            try {\n                const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/roles`, {\n                    headers: {\n                        Authorization: `Bot ${token}`\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error('Failed to fetch roles');\n                }\n                const roles = await response.json();\n                return res.status(200).json(roles);\n            } catch (error) {\n                console.error('Error fetching roles:', error);\n                return res.status(500).json({\n                    error: 'Failed to fetch roles'\n                });\n            }\n        }\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    } catch (error) {\n        console.error('Error in roles handler:', error);\n        return res.status(500).json({\n            error: 'Internal server error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/discord/roles/index.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_d3-sc","lib-node_modules_pnpm_d3-time-","lib-node_modules_pnpm_dec","lib-node_modules_pnpm_e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_immer_10_1_1_node_modules_immer_dist_immer_mjs-806fdd73","lib-node_modules_pnpm_i","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_react-c","lib-node_modules_pnpm_react-red","lib-node_modules_pnpm_rea","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-5c215c87","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-bf697780","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-334e6784","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-d89a0eeb","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-4c6b09db","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-d29869f5","lib-node_modules_pnpm_rec","lib-node_modules_pnpm_redux_","lib-node_modules_pnpm_r","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Froles&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Croles%5Cindex.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();