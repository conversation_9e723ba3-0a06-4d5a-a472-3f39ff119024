"use strict";(()=>{var e={};e.id=8739,e.ids=[8739],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},38508:(e,r,t)=>{t.r(r),t.d(r,{config:()=>S,default:()=>d,routeModule:()=>l});var n={};t.r(n),t.d(n,{default:()=>A});var o=t(93433),s=t(20264),i=t(20584),a=t(15806),E=t(94506),u=t(98580);async function A(e,r){try{let t=await (0,a.getServerSession)(e,r,E.authOptions);if(!t?.user)return r.status(401).json({error:"Unauthorized"});if(!t.user.isAdmin)return r.status(403).json({error:"Forbidden - Admin access required"});let{guildId:n,token:o}=u.dashboardConfig.bot;if(!o||!n)return r.status(500).json({error:"Bot configuration missing"});if("POST"===e.method)try{let{name:t,color:s,permissions:i=[],hoist:a,mentionable:E}=e.body;if(!t)return r.status(400).json({error:"Role name is required"});let u={CREATE_INSTANT_INVITE:1n<<0n,KICK_MEMBERS:1n<<1n,BAN_MEMBERS:1n<<2n,ADMINISTRATOR:1n<<3n,MANAGE_CHANNELS:1n<<4n,MANAGE_GUILD:1n<<5n,ADD_REACTIONS:1n<<6n,VIEW_AUDIT_LOG:1n<<7n,PRIORITY_SPEAKER:1n<<8n,STREAM:1n<<9n,VIEW_CHANNEL:1n<<10n,SEND_MESSAGES:1n<<11n,SEND_TTS_MESSAGES:1n<<12n,MANAGE_MESSAGES:1n<<13n,EMBED_LINKS:1n<<14n,ATTACH_FILES:1n<<15n,READ_MESSAGE_HISTORY:1n<<16n,MENTION_EVERYONE:1n<<17n,USE_EXTERNAL_EMOJIS:1n<<18n,CONNECT:1n<<20n,SPEAK:1n<<21n,MUTE_MEMBERS:1n<<22n,DEAFEN_MEMBERS:1n<<23n,MOVE_MEMBERS:1n<<24n,USE_VAD:1n<<25n,CHANGE_NICKNAME:1n<<26n,MANAGE_NICKNAMES:1n<<27n,MANAGE_ROLES:1n<<28n,MANAGE_WEBHOOKS:1n<<29n,MANAGE_EMOJIS_AND_STICKERS:1n<<30n,USE_APPLICATION_COMMANDS:1n<<31n},A=0n;if(Array.isArray(i))for(let e of i){let r=u[e];"bigint"==typeof r&&(A|=r)}let d={name:t,hoist:!!a,mentionable:!!E};if(s){let e=parseInt(s.replace("#",""),16);isNaN(e)||(d.color=e)}0n!==A&&(d.permissions=A.toString());let S=await fetch(`https://discord.com/api/v10/guilds/${n}/roles`,{method:"POST",headers:{Authorization:`Bot ${o}`,"Content-Type":"application/json"},body:JSON.stringify(d)});if(!S.ok){let e;try{e=await S.json()}catch{e=await S.text()}return r.status(S.status).json(e)}let l=await S.json();return r.status(201).json(l)}catch(e){return r.status(500).json({error:"Failed to create role"})}if("GET"===e.method)try{let e=await fetch(`https://discord.com/api/v10/guilds/${n}/roles`,{headers:{Authorization:`Bot ${o}`}});if(!e.ok)throw Error("Failed to fetch roles");let t=await e.json();return r.status(200).json(t)}catch(e){return r.status(500).json({error:"Failed to fetch roles"})}return r.status(405).json({error:"Method not allowed"})}catch(e){return r.status(500).json({error:"Internal server error"})}}let d=(0,i.M)(n,"default"),S=(0,i.M)(n,"config"),l=new o.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/discord/roles",pathname:"/api/discord/roles",bundlePath:"",filename:""},userland:n})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(38508));module.exports=n})();