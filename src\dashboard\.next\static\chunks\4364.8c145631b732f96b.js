"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4364],{14364:(e,l,a)=>{a.r(l),a.d(l,{default:()=>c});var i=a(94513),n=a(79028),r=a(5142),o=a(28365),t=a(31862),s=a(15975),u=a(59220),h=a(35339),d=a(30301),b=a(52442),T=a(3037),M=a(94285);let v=[{value:"GMT-12:00",label:"(GMT-12:00) International Date Line West"},{value:"GMT-11:00",label:"(GMT-11:00) Midway Island, Samoa"},{value:"GMT-10:00",label:"(GMT-10:00) Hawaii"},{value:"GMT-09:00",label:"(GMT-09:00) Alaska"},{value:"GMT-08:00",label:"(GMT-08:00) Pacific Time (US & Canada)"},{value:"GMT-07:00",label:"(GMT-07:00) Mountain Time (US & Canada)"},{value:"GMT-06:00",label:"(GMT-06:00) Central Time (US & Canada)"},{value:"GMT-05:00",label:"(GMT-05:00) Eastern Time (US & Canada)"},{value:"GMT-04:00",label:"(GMT-04:00) Atlantic Time (Canada)"},{value:"GMT-03:00",label:"(GMT-03:00) Buenos Aires, Georgetown"},{value:"GMT-02:00",label:"(GMT-02:00) Mid-Atlantic"},{value:"GMT-01:00",label:"(GMT-01:00) Azores, Cape Verde Islands"},{value:"GMT+00:00",label:"(GMT+00:00) London, Dublin, Edinburgh"},{value:"GMT+01:00",label:"(GMT+01:00) Paris, Amsterdam, Berlin"},{value:"GMT+02:00",label:"(GMT+02:00) Athens, Istanbul, Helsinki"},{value:"GMT+03:00",label:"(GMT+03:00) Moscow, Baghdad, Kuwait"},{value:"GMT+04:00",label:"(GMT+04:00) Abu Dhabi, Dubai, Baku"},{value:"GMT+05:00",label:"(GMT+05:00) Karachi, Tashkent"},{value:"GMT+06:00",label:"(GMT+06:00) Dhaka, Almaty"},{value:"GMT+07:00",label:"(GMT+07:00) Bangkok, Jakarta"},{value:"GMT+08:00",label:"(GMT+08:00) Beijing, Singapore, Hong Kong"},{value:"GMT+09:00",label:"(GMT+09:00) Tokyo, Seoul, Osaka"},{value:"GMT+10:00",label:"(GMT+10:00) Sydney, Melbourne, Brisbane"},{value:"GMT+11:00",label:"(GMT+11:00) Solomon Islands"},{value:"GMT+12:00",label:"(GMT+12:00) Auckland, Wellington"}],c=M.memo(e=>{var l,a,c,G;let{session:g,onFormChange:p,initialData:m,motivationLabel:x="Why do you want to be a moderator?",motivationPlaceholder:j="Tell us about your motivation and what you can bring to the team..."}=e,A=(0,M.useRef)(null),[w,k]=(0,M.useState)({age:(null==m?void 0:m.age)||"",hoursPerWeek:(null==m?void 0:m.hoursPerWeek)||"",timezone:(null==m?void 0:m.timezone)||"",motivation:(null==m?void 0:m.motivation)||""}),C=(0,M.useRef)(w);C.current=w,(0,M.useEffect)(()=>{A.current&&A.current.scrollIntoView({behavior:"smooth",block:"start"})},[]),(0,M.useEffect)(()=>{let e=setTimeout(()=>{p(C.current)},100);return()=>clearTimeout(e)},[w,p]);let y=(0,M.useCallback)((e,l)=>{k(a=>({...a,[e]:l}))},[]);return(0,i.jsx)(n.a,{ref:A,children:(0,i.jsxs)(T.T,{spacing:6,align:"stretch",children:[(0,i.jsx)(r.Z,{bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",children:(0,i.jsx)(o.b,{children:(0,i.jsxs)(d.r,{columns:2,spacing:4,children:[(0,i.jsxs)(t.MJ,{children:[(0,i.jsx)(s.l,{children:"Discord Username"}),(0,i.jsx)(u.p,{value:null!=(c=null==g||null==(l=g.user)?void 0:l.name)?c:"",isReadOnly:!0,bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"}})]}),(0,i.jsxs)(t.MJ,{children:[(0,i.jsx)(s.l,{children:"Discord User ID"}),(0,i.jsx)(u.p,{value:null!=(G=null==g||null==(a=g.user)?void 0:a.id)?G:"",isReadOnly:!0,bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"}})]})]})})}),(0,i.jsx)(r.Z,{bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",mt:4,children:(0,i.jsx)(o.b,{children:(0,i.jsxs)(d.r,{columns:3,spacing:4,children:[(0,i.jsxs)(t.MJ,{isRequired:!0,children:[(0,i.jsx)(s.l,{children:"Age"}),(0,i.jsx)(u.p,{type:"number",value:w.age,onChange:e=>y("age",e.target.value),min:13,placeholder:"Enter your age",bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"}})]}),(0,i.jsxs)(t.MJ,{isRequired:!0,children:[(0,i.jsx)(s.l,{children:"Hours per Week"}),(0,i.jsx)(u.p,{type:"number",value:w.hoursPerWeek,onChange:e=>y("hoursPerWeek",e.target.value),min:1,placeholder:"Hours available",bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"}})]}),(0,i.jsxs)(t.MJ,{isRequired:!0,children:[(0,i.jsx)(s.l,{children:"Timezone"}),(0,i.jsx)(h.l,{value:w.timezone,onChange:e=>y("timezone",e.target.value),placeholder:"Select timezone",bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"},children:v.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})})}),(0,i.jsx)(r.Z,{bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",mt:4,children:(0,i.jsx)(o.b,{children:(0,i.jsxs)(t.MJ,{isRequired:!0,children:[(0,i.jsx)(s.l,{children:x}),(0,i.jsx)(b.T,{value:w.motivation,onChange:e=>y("motivation",e.target.value),placeholder:j,minH:"200px",bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"},resize:"vertical"})]})})})]})})})}}]);