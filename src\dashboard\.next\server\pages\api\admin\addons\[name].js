"use strict";(()=>{var e={};e.id=4390,e.ids=[4390],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},60842:(e,s,d)=>{d.r(s),d.d(s,{config:()=>h,default:()=>b,routeModule:()=>S});var n={};d.r(n),d.d(n,{default:()=>g});var r=d(93433),o=d(20264),t=d(20584),i=d(15806),a=d(94506),c=d(29021),l=d.n(c),u=d(33873),f=d.n(u),y=d(72115),m=d.n(y);function p(e=process.cwd()){let s=e;for(;s!==f().parse(s).root;){if(l().existsSync(f().join(s,"package.json")))return s;s=f().dirname(s)}return e}async function g(e,s){let d=await (0,i.getServerSession)(e,s,a.authOptions);if(!d)return s.status(401).json({error:"Unauthorized"});if(!d.user.isAdmin)return s.status(403).json({error:"Forbidden - Admin access required"});let{name:n}=e.query;if(!n||"string"!=typeof n)return s.status(400).json({error:"Invalid addon name"});try{let r=function(){let e=["404-bot/config.yml","config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>f().resolve(process.cwd(),e)),s=e.find(e=>l().existsSync(e));if(!s&&(s=e.map(e=>e.replace("config.yml","config.example.yml")).find(e=>l().existsSync(e))))try{let e=s.replace("config.example.yml","config.yml");l().copyFileSync(s,e),s=e}catch(e){}if(!s){let e=f().resolve(process.cwd(),"config.yml");try{l().writeFileSync(e,m().stringify({addons:{enabled:!0,disabled:[]}})),s=e}catch{}}if(!s)throw Error("config.yml not found and could not create default");return s}(),o=l().readFileSync(r,"utf8"),t=m().parse(o),i=function(e){let s=[`404-bot/src/addons/${e}/config.yml`,`src/addons/${e}/config.yml`,`../src/addons/${e}/config.yml`,`../../src/addons/${e}/config.yml`,`../../../src/addons/${e}/config.yml`,`../../../../src/addons/${e}/config.yml`].map(e=>f().resolve(process.cwd(),e)).find(e=>l().existsSync(e));if(!s){let d=f().resolve(__dirname,`../../../../../../../src/addons/${e}/config.yml`);l().existsSync(d)&&(s=d)}return s||null}(n);switch(e.method){case"GET":{let e=t.addons?.enabled&&(!t.addons.disabled||!t.addons.disabled.includes(n)),d="";return d=i?l().readFileSync(i,"utf8"):m().stringify({addon:{name:n,version:"1.0.0",description:"No description available",author:"Unknown",enabled:!0}}),s.status(200).json({name:n,enabled:e,configYaml:d})}case"PATCH":{let{enabled:o}=e.body;if("boolean"!=typeof o)return s.status(400).json({error:"Invalid request body"});t.addons||(t.addons={}),t.addons.disabled||(t.addons.disabled=[]),o?t.addons.disabled=t.addons.disabled.filter(e=>e!==n):t.addons.disabled.includes(n)||t.addons.disabled.push(n),l().writeFileSync(r,m().stringify(t));try{let e=p(),s=f().join(e,"addon-reload.signal");l().writeFileSync(s,JSON.stringify({timestamp:Date.now(),requestedBy:d.user?.email||"dashboard"}))}catch{}return s.status(200).json({name:n,enabled:o,configYaml:i?l().readFileSync(i,"utf8"):""})}case"PUT":{let{configYaml:d}=e.body;if(!d||"string"!=typeof d)return s.status(400).json({error:"Invalid configuration"});try{m().parse(d);let e=f().dirname(i||f().resolve(process.cwd(),`src/addons/${n}`));return l().existsSync(e)||l().mkdirSync(e,{recursive:!0}),l().writeFileSync(i||f().resolve(e,"config.yml"),d),s.status(200).json({name:n,enabled:!t.addons.disabled?.includes(n),configYaml:d})}catch(e){return s.status(400).json({error:"Invalid YAML syntax"})}}case"DELETE":{let e=function(e){let s=function(){let e=["404-bot/src/addons","src/addons","../src/addons","../../src/addons","../../../src/addons","../../../../src/addons","404-bot/dist/addons","dist/addons","dist/dashboard/dist/addons","dashboard/dist/addons","../dist/addons","../dist/dashboard/dist/addons","../../dist/addons","../../dist/dashboard/dist/addons","../../../dist/addons","../../../../dist/addons"].map(e=>f().resolve(process.cwd(),e)),s=[];e.forEach(e=>{l().existsSync(e)&&s.push(e)});let d=f().resolve(__dirname,"../../../../../../../src/addons"),n=f().resolve(__dirname,"../../../../../../../dist/addons");if(l().existsSync(d)&&s.push(d),l().existsSync(n)&&s.push(n),0===s.length)throw Error("No addons directories found");return Array.from(new Set(s))}(),d=e.replace(/\s+/g,"-");for(let n of s)for(let s of[f().join(n,e),f().join(n,d)])if(l().existsSync(s))return s;return null}(n);if(!e){let s=process.cwd().includes("dashboard")?f().resolve(process.cwd(),"..",".."):process.cwd();for(let d of[f().join(s,"src","addons",n),f().join(process.cwd(),"..","..","src","addons",n),f().join(process.cwd(),"src","addons",n)])if(l().existsSync(d)){e=d;break}}if(!e)return s.status(404).json({error:"Addon not found",details:`Could not locate addon "${n}" in any expected directories`});let o=f().join(e,"flow.json"),i=!1;if(l().existsSync(o)&&(i=!0),!i){let s=f().join(e,"index.ts");if(l().existsSync(s)){let e=l().readFileSync(s,"utf8");i=e.includes("Generated addon from visual builder")&&e.includes("author: 'Addon Builder'")}if(!i&&l().existsSync(f().join(e,"config.yml"))){let s=l().readFileSync(f().join(e,"config.yml"),"utf8"),d=m().parse(s);i=d?.author==="Addon Builder"||d?.description==="Generated addon from visual builder"}}if(!i)return s.status(403).json({error:"Cannot delete built-in addon",details:"Only custom addons created by the addon builder can be deleted"});t.addons?.disabled?.includes(n)&&(t.addons.disabled=t.addons.disabled.filter(e=>e!==n),l().writeFileSync(r,m().stringify(t)));let a=e=>{if(l().existsSync(e))try{l().readdirSync(e).forEach(s=>{let d=f().join(e,s);l().lstatSync(d).isDirectory()?a(d):l().unlinkSync(d)}),l().rmdirSync(e)}catch(e){throw Error(`Failed to delete addon directory: ${e}`)}};if(a(e),l().existsSync(e))throw Error(`Addon directory still exists after deletion attempt: ${e}`);let c=process.cwd().includes("dashboard")?f().resolve(process.cwd(),"..",".."):process.cwd();for(let e of[f().join(c,"dist","addons",n),f().join(process.cwd(),"..","..","dist","addons",n),f().join(process.cwd(),"dist","addons",n)])if(l().existsSync(e))try{a(e)}catch(e){}try{let e=p(),s=f().join(e,"addon-reload.signal");l().writeFileSync(s,JSON.stringify({timestamp:Date.now(),requestedBy:d.user?.email||"dashboard",action:"addon-deleted",addonName:n}))}catch{}return s.status(200).json({message:"Custom addon deleted successfully",name:n})}default:return s.status(405).json({error:"Method not allowed"})}}catch(e){return s.status(500).json({error:"Internal server error",details:e.message})}}let b=(0,t.M)(n,"default"),h=(0,t.M)(n,"config"),S=new r.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/admin/addons/[name]",pathname:"/api/admin/addons/[name]",bundlePath:"",filename:""},userland:n})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var s=require("../../../../webpack-api-runtime.js");s.C(e);var d=e=>s(s.s=e),n=s.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>d(60842));module.exports=n})();