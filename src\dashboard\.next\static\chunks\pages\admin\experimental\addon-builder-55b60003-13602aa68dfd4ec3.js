"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2437],{24641:(e,r,o)=>{o.d(r,{A:()=>p});var n=o(94513),t=o(94285),i=o(6179),s=o(54881),a=o(14027),l=o(27263);let c={event:[{name:"{event.type}",description:"Type of event that triggered",icon:"\uD83D\uDCE1"},{name:"{event.timestamp}",description:"When the event occurred",icon:"⏰"},{name:"{event.guild}",description:"Server where event occurred",icon:"\uD83C\uDFE0"},{name:"{event.channel}",description:"Channel where event occurred",icon:"\uD83D\uDCFA"},{name:"{event.user}",description:"User who triggered the event",icon:"\uD83D\uDC64"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83C\uDD94"},{name:"{message.content}",description:"Message content",icon:"\uD83D\uDCAC"},{name:"{message.author}",description:"Message author",icon:"\uD83D\uDC64"},{name:"{message.channel}",description:"Message channel",icon:"\uD83D\uDCFA"},{name:"{message.createdAt}",description:"Message creation time",icon:"\uD83D\uDCC5"},{name:"{message.editedAt}",description:"Message edit time",icon:"✏️"},{name:"{message.attachments}",description:"Message attachments",icon:"\uD83D\uDCCE"},{name:"{message.embeds}",description:"Message embeds",icon:"\uD83D\uDCCB"},{name:"{message.reactions}",description:"Message reactions",icon:"\uD83D\uDC4D"},{name:"{message.mentions}",description:"Message mentions",icon:"\uD83D\uDCE2"}],member:[{name:"{member.id}",description:"Member ID",icon:"\uD83C\uDD94"},{name:"{member.username}",description:"Member username",icon:"\uD83D\uDC64"},{name:"{member.displayName}",description:"Member display name",icon:"\uD83D\uDCDD"},{name:"{member.tag}",description:"Member tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{member.mention}",description:"Member mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{member.avatar}",description:"Member avatar URL",icon:"\uD83D\uDDBC️"},{name:"{member.joinedAt}",description:"Server join date",icon:"\uD83D\uDEAA"},{name:"{member.roles}",description:"Member roles",icon:"\uD83C\uDFAD"},{name:"{member.permissions}",description:"Member permissions",icon:"\uD83D\uDD10"},{name:"{member.isBot}",description:"Is member a bot",icon:"\uD83E\uDD16"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member count",icon:"\uD83D\uDC65"},{name:"{channel.position}",description:"Channel position",icon:"\uD83D\uDCCD"},{name:"{channel.nsfw}",description:"Is NSFW channel",icon:"\uD83D\uDD1E"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Total member count",icon:"\uD83D\uDC65"},{name:"{server.owner}",description:"Server owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server boost level",icon:"\uD83D\uDE80"},{name:"{server.boostCount}",description:"Server boost count",icon:"\uD83D\uDC8E"},{name:"{server.createdAt}",description:"Server creation date",icon:"\uD83D\uDCC5"}],reaction:[{name:"{reaction.emoji}",description:"Reaction emoji",icon:"\uD83D\uDE00"},{name:"{reaction.count}",description:"Reaction count",icon:"\uD83D\uDD22"},{name:"{reaction.users}",description:"Users who reacted",icon:"\uD83D\uDC65"},{name:"{reaction.me}",description:"Bot reacted",icon:"\uD83E\uDD16"}],voice:[{name:"{voice.channelId}",description:"Voice channel ID",icon:"\uD83D\uDD0A"},{name:"{voice.channelName}",description:"Voice channel name",icon:"\uD83D\uDD0A"},{name:"{voice.memberCount}",description:"Voice channel member count",icon:"\uD83D\uDC65"},{name:"{voice.muted}",description:"Is member muted",icon:"\uD83D\uDD07"},{name:"{voice.deafened}",description:"Is member deafened",icon:"\uD83D\uDD07"},{name:"{voice.streaming}",description:"Is member streaming",icon:"\uD83D\uDCFA"},{name:"{voice.camera}",description:"Is member using camera",icon:"\uD83D\uDCF9"}],role:[{name:"{role.id}",description:"Role ID",icon:"\uD83C\uDD94"},{name:"{role.name}",description:"Role name",icon:"\uD83C\uDFAD"},{name:"{role.mention}",description:"Role mention (<@&id>)",icon:"\uD83D\uDCE2"},{name:"{role.color}",description:"Role color",icon:"\uD83C\uDFA8"},{name:"{role.position}",description:"Role position",icon:"\uD83D\uDCCD"},{name:"{role.permissions}",description:"Role permissions",icon:"\uD83D\uDD10"},{name:"{role.mentionable}",description:"Is role mentionable",icon:"\uD83D\uDCE2"},{name:"{role.hoisted}",description:"Is role hoisted",icon:"\uD83D\uDCCC"}]},d=[{value:"messageCreate",label:"\uD83D\uDCAC Message Created",category:"Messages",description:"When a new message is sent"},{value:"messageUpdate",label:"✏️ Message Edited",category:"Messages",description:"When a message is edited"},{value:"messageDelete",label:"\uD83D\uDDD1️ Message Deleted",category:"Messages",description:"When a message is deleted"},{value:"messageReactionAdd",label:"\uD83D\uDC4D Reaction Added",category:"Messages",description:"When a reaction is added to a message"},{value:"messageReactionRemove",label:"\uD83D\uDC4E Reaction Removed",category:"Messages",description:"When a reaction is removed from a message"},{value:"messageReactionRemoveAll",label:"\uD83E\uDDF9 All Reactions Removed",category:"Messages",description:"When all reactions are removed from a message"},{value:"guildMemberAdd",label:"\uD83D\uDEAA Member Joined",category:"Members",description:"When a new member joins the server"},{value:"guildMemberRemove",label:"\uD83D\uDC4B Member Left",category:"Members",description:"When a member leaves the server"},{value:"guildMemberUpdate",label:"\uD83D\uDC64 Member Updated",category:"Members",description:"When member info changes (roles, nickname, etc.)"},{value:"userUpdate",label:"\uD83D\uDCDD User Updated",category:"Members",description:"When user profile changes (avatar, username, etc.)"},{value:"presenceUpdate",label:"\uD83D\uDFE2 Presence Updated",category:"Members",description:"When member status/activity changes"},{value:"guildBanAdd",label:"\uD83D\uDD28 Member Banned",category:"Moderation",description:"When a member is banned"},{value:"guildBanRemove",label:"\uD83D\uDD13 Member Unbanned",category:"Moderation",description:"When a member is unbanned"},{value:"messageDeleteBulk",label:"\uD83E\uDDF9 Bulk Message Delete",category:"Moderation",description:"When multiple messages are deleted at once"},{value:"voiceStateUpdate",label:"\uD83D\uDD0A Voice State Changed",category:"Voice",description:"When member joins/leaves/mutes in voice"},{value:"channelCreate",label:"\uD83D\uDCFA Channel Created",category:"Channels",description:"When a new channel is created"},{value:"channelDelete",label:"\uD83D\uDDD1️ Channel Deleted",category:"Channels",description:"When a channel is deleted"},{value:"channelUpdate",label:"⚙️ Channel Updated",category:"Channels",description:"When channel settings change"},{value:"channelPinsUpdate",label:"\uD83D\uDCCC Channel Pins Updated",category:"Channels",description:"When pinned messages change"},{value:"roleCreate",label:"\uD83C\uDFAD Role Created",category:"Roles",description:"When a new role is created"},{value:"roleDelete",label:"\uD83D\uDDD1️ Role Deleted",category:"Roles",description:"When a role is deleted"},{value:"roleUpdate",label:"⚙️ Role Updated",category:"Roles",description:"When role settings change"},{value:"threadCreate",label:"\uD83E\uDDF5 Thread Created",category:"Threads",description:"When a thread is created"},{value:"threadDelete",label:"\uD83D\uDDD1️ Thread Deleted",category:"Threads",description:"When a thread is deleted"},{value:"threadUpdate",label:"⚙️ Thread Updated",category:"Threads",description:"When thread settings change"},{value:"threadMemberUpdate",label:"\uD83D\uDC64 Thread Member Update",category:"Threads",description:"When someone joins/leaves a thread"},{value:"interactionCreate",label:"\uD83C\uDF9B️ Interaction Created",category:"Interactions",description:"When buttons/selects are used"},{value:"applicationCommandPermissionsUpdate",label:"\uD83D\uDD10 Command Permissions Updated",category:"Interactions",description:"When command permissions change"},{value:"guildUpdate",label:"\uD83C\uDFE0 Server Updated",category:"Server",description:"When server settings change"},{value:"guildUnavailable",label:"⚠️ Server Unavailable",category:"Server",description:"When server becomes unavailable"},{value:"guildIntegrationsUpdate",label:"\uD83D\uDD17 Integrations Updated",category:"Server",description:"When server integrations change"},{value:"inviteCreate",label:"\uD83D\uDD17 Invite Created",category:"Server",description:"When an invite is created"},{value:"inviteDelete",label:"\uD83D\uDDD1️ Invite Deleted",category:"Server",description:"When an invite is deleted"},{value:"emojiCreate",label:"\uD83D\uDE00 Emoji Created",category:"Server",description:"When a custom emoji is added"},{value:"emojiDelete",label:"\uD83D\uDDD1️ Emoji Deleted",category:"Server",description:"When a custom emoji is removed"},{value:"emojiUpdate",label:"⚙️ Emoji Updated",category:"Server",description:"When a custom emoji is modified"},{value:"stickerCreate",label:"\uD83C\uDFF7️ Sticker Created",category:"Server",description:"When a custom sticker is added"},{value:"stickerDelete",label:"\uD83D\uDDD1️ Sticker Deleted",category:"Server",description:"When a custom sticker is removed"},{value:"stickerUpdate",label:"⚙️ Sticker Updated",category:"Server",description:"When a custom sticker is modified"}],m=[{value:"channel",label:"\uD83D\uDCFA Channel Filter",description:"Filter by specific channels"},{value:"role",label:"\uD83C\uDFAD Role Filter",description:"Filter by user roles"},{value:"user",label:"\uD83D\uDC64 User Filter",description:"Filter by specific users"},{value:"regex",label:"\uD83D\uDD0D Regex Pattern",description:"Filter using regular expressions"},{value:"cooldown",label:"⏰ Cooldown",description:"Rate limit event triggers"},{value:"permission",label:"\uD83D\uDD10 Permission",description:"Filter by user permissions"},{value:"content",label:"\uD83D\uDCAC Content Filter",description:"Filter by message content"},{value:"custom",label:"⚙️ Custom",description:"Custom filter condition"}],h=(0,t.memo)(e=>{var r,o,h,p,x,g,b,u;let{data:v,selected:j,id:S,updateNodeData:y}=e,{currentScheme:f}=(0,l.DP)(),{isOpen:C,onOpen:k,onClose:T}=(0,s.useDisclosure)(),[W,R]=(0,t.useState)(()=>({ignoreBot:!0,ignoreSystem:!0,rateLimited:!1,rateLimit:1e3,priority:1,async:!1,retryOnError:!1,maxRetries:3,filters:[],channelRestrictions:[],roleRestrictions:[],...v})),[M,z]=(0,t.useState)(!1),w=e=>{R(r=>({...r,...e}))},I=e=>{let r=d.find(r=>r.value===e);return r?r.label.split(" ").slice(1).join(" "):e},D=(e,r)=>{let o=[...W.filters||[]];o[e]={...o[e],...r},w({filters:o})},F=e=>{w({filters:(W.filters||[]).filter((r,o)=>o!==e)})},A=e=>{navigator.clipboard.writeText(e)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(s.Box,{bg:f.colors.surface,border:"2px solid ".concat(j?"#10b981":f.colors.border),borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,n.jsx)(i.h7,{type:"target",position:i.yX.Top,style:{background:"#10b981",border:"2px solid ".concat(f.colors.surface),width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,n.jsxs)(s.VStack,{spacing:1,align:"stretch",children:[(0,n.jsxs)(s.HStack,{justify:"space-between",align:"center",children:[(0,n.jsxs)(s.HStack,{spacing:1,children:[(0,n.jsx)(s.Box,{bg:"green.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,n.jsx)(a.DQs,{})}),(0,n.jsx)(s.Text,{fontSize:"xs",fontWeight:"bold",color:f.colors.text,children:"Event"})]}),(0,n.jsx)(s.IconButton,{icon:(0,n.jsx)(a.VSk,{}),size:"xs",variant:"ghost",onClick:k,"aria-label":"Configure event"})]}),(0,n.jsx)(s.Box,{children:(0,n.jsxs)(s.HStack,{spacing:1,children:[W.eventType&&(0,n.jsx)(s.Text,{fontSize:"xs",children:(e=>{let r=d.find(r=>r.value===e);return r?r.label.split(" ")[0]:"\uD83D\uDCE1"})(W.eventType)}),(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.text,noOfLines:1,children:W.eventType?I(W.eventType):"Select Event"})]})}),W.description&&(0,n.jsx)(s.Box,{children:(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.textSecondary,noOfLines:1,children:W.description.length>25?W.description.substring(0,25)+"...":W.description})}),(0,n.jsxs)(s.HStack,{spacing:1,flexWrap:"wrap",children:[(null!=(b=null==(r=W.filters)?void 0:r.length)?b:0)>0&&(0,n.jsxs)(s.Badge,{size:"xs",colorScheme:"green",children:[null==(o=W.filters)?void 0:o.length," filter",(null!=(u=null==(h=W.filters)?void 0:h.length)?u:0)!==1?"s":""]}),W.ignoreBot&&(0,n.jsx)(s.Badge,{size:"xs",colorScheme:"orange",children:"No Bots"}),W.rateLimited&&(0,n.jsx)(s.Badge,{size:"xs",colorScheme:"yellow",children:"Rate Limited"})]})]}),(0,n.jsx)(i.h7,{type:"source",position:i.yX.Bottom,style:{background:"#10b981",border:"2px solid ".concat(f.colors.surface),width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,n.jsxs)(s.Modal,{isOpen:C,onClose:()=>{y&&S&&y(S,W),T()},size:"4xl",children:[(0,n.jsx)(s.ModalOverlay,{bg:"blackAlpha.600"}),(0,n.jsxs)(s.ModalContent,{bg:f.colors.background,border:"2px solid",borderColor:"green.400",maxW:"1200px",children:[(0,n.jsx)(s.ModalHeader,{color:f.colors.text,children:"\uD83D\uDCE1 Configure Event"}),(0,n.jsx)(s.ModalCloseButton,{}),(0,n.jsx)(s.ModalBody,{pb:6,children:(0,n.jsxs)(s.VStack,{spacing:6,align:"stretch",children:[(0,n.jsxs)(s.Box,{children:[(0,n.jsxs)(s.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,n.jsx)(s.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Available Variables"}),(0,n.jsxs)(s.Button,{size:"sm",variant:"ghost",leftIcon:M?(0,n.jsx)(a._NO,{}):(0,n.jsx)(a.Vap,{}),onClick:()=>z(!M),children:[M?"Hide":"Show"," Variables"]})]}),(0,n.jsxs)(s.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,n.jsx)(s.AlertIcon,{}),(0,n.jsx)(s.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your event responses! Click any variable below to copy it. Variables are replaced with actual values when your event triggers."})]}),(0,n.jsx)(s.Collapse,{in:M,animateOpacity:!0,children:(0,n.jsx)(s.Box,{bg:f.colors.surface,border:"1px solid",borderColor:f.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,n.jsx)(s.Accordion,{allowMultiple:!0,children:Object.entries(c).map(e=>{let[r,o]=e;return(0,n.jsxs)(s.AccordionItem,{border:"none",children:[(0,n.jsxs)(s.AccordionButton,{px:0,py:2,children:[(0,n.jsx)(s.Box,{flex:"1",textAlign:"left",children:(0,n.jsxs)(s.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,textTransform:"capitalize",children:[r," Variables"]})}),(0,n.jsx)(s.AccordionIcon,{})]}),(0,n.jsx)(s.AccordionPanel,{px:0,py:2,children:(0,n.jsx)(s.VStack,{spacing:2,align:"stretch",children:o.map(e=>(0,n.jsxs)(s.HStack,{spacing:2,p:2,bg:f.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:f.colors.surface},onClick:()=>A(e.name),children:[(0,n.jsx)(s.Text,{fontSize:"sm",children:e.icon}),(0,n.jsx)(s.Code,{fontSize:"xs",colorScheme:"green",children:e.name}),(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.textSecondary,flex:"1",children:e.description}),(0,n.jsx)(s.IconButton,{icon:(0,n.jsx)(a.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:r=>{r.stopPropagation(),A(e.name)}})]},e.name))})})]},r)})})})})]}),(0,n.jsx)(s.Divider,{}),(0,n.jsxs)(s.Tabs,{variant:"enclosed",colorScheme:"green",children:[(0,n.jsxs)(s.TabList,{children:[(0,n.jsx)(s.Tab,{children:"Event Type"}),(0,n.jsx)(s.Tab,{children:"Filters"}),(0,n.jsx)(s.Tab,{children:"Settings"}),(0,n.jsx)(s.Tab,{children:"Advanced"})]}),(0,n.jsxs)(s.TabPanels,{children:[(0,n.jsx)(s.TabPanel,{children:(0,n.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(s.FormControl,{isRequired:!0,children:[(0,n.jsx)(s.FormLabel,{color:f.colors.text,children:"Event Type"}),(0,n.jsx)(s.Select,{value:W.eventType||"",onChange:e=>w({eventType:e.target.value}),placeholder:"Select an event type",bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,children:Object.entries(d.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{})).map(e=>{let[r,o]=e;return(0,n.jsx)("optgroup",{label:r,children:o.map(e=>(0,n.jsx)("option",{value:e.value,children:e.label},e.value))},r)})})]}),W.eventType&&(0,n.jsxs)(s.Alert,{status:"info",borderRadius:"md",children:[(0,n.jsx)(s.AlertIcon,{}),(0,n.jsxs)(s.Box,{children:[(0,n.jsx)(s.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:null==(p=d.find(e=>e.value===W.eventType))?void 0:p.label}),(0,n.jsx)(s.Text,{fontSize:"sm",children:null==(x=d.find(e=>e.value===W.eventType))?void 0:x.description})]})]}),(0,n.jsxs)(s.FormControl,{children:[(0,n.jsx)(s.FormLabel,{color:f.colors.text,children:"Description"}),(0,n.jsx)(s.Textarea,{value:W.description||"",onChange:e=>w({description:e.target.value}),placeholder:"Describe when this event should trigger",bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,minH:"80px"})]})]})}),(0,n.jsx)(s.TabPanel,{children:(0,n.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(s.HStack,{justify:"space-between",align:"center",children:[(0,n.jsx)(s.Text,{fontSize:"lg",fontWeight:"bold",color:f.colors.text,children:"Event Filters"}),(0,n.jsx)(s.Button,{leftIcon:(0,n.jsx)(a.GGD,{}),onClick:()=>{w({filters:[...W.filters||[],{type:"channel",value:"",operator:"equals"}]})},colorScheme:"green",size:"sm",children:"Add Filter"})]}),(0,n.jsxs)(s.Alert,{status:"info",borderRadius:"md",children:[(0,n.jsx)(s.AlertIcon,{}),(0,n.jsx)(s.AlertDescription,{fontSize:"sm",children:"Filters determine when this event should trigger. Only events that pass all filters will execute the connected actions."})]}),(0,n.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[null==(g=W.filters)?void 0:g.map((e,r)=>{var o;return(0,n.jsxs)(s.Box,{p:4,bg:f.colors.surface,borderRadius:"md",border:"1px solid",borderColor:f.colors.border,children:[(0,n.jsxs)(s.HStack,{justify:"space-between",align:"center",mb:3,children:[(0,n.jsxs)(s.Text,{fontSize:"md",fontWeight:"bold",color:f.colors.text,children:["Filter ",r+1]}),(0,n.jsx)(s.IconButton,{icon:(0,n.jsx)(a.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>F(r),"aria-label":"Remove filter"})]}),(0,n.jsxs)(s.VStack,{spacing:3,align:"stretch",children:[(0,n.jsxs)(s.SimpleGrid,{columns:2,spacing:3,children:[(0,n.jsxs)(s.FormControl,{children:[(0,n.jsx)(s.FormLabel,{fontSize:"sm",color:f.colors.text,children:"Filter Type"}),(0,n.jsx)(s.Select,{value:e.type,onChange:e=>D(r,{type:e.target.value}),bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,size:"sm",children:m.map(e=>(0,n.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,n.jsxs)(s.FormControl,{children:[(0,n.jsx)(s.FormLabel,{fontSize:"sm",color:f.colors.text,children:"Operator"}),(0,n.jsxs)(s.Select,{value:e.operator||"equals",onChange:e=>D(r,{operator:e.target.value}),bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,size:"sm",children:[(0,n.jsx)("option",{value:"equals",children:"Equals"}),(0,n.jsx)("option",{value:"contains",children:"Contains"}),(0,n.jsx)("option",{value:"startsWith",children:"Starts With"}),(0,n.jsx)("option",{value:"endsWith",children:"Ends With"}),(0,n.jsx)("option",{value:"regex",children:"Regex"})]})]})]}),(0,n.jsxs)(s.FormControl,{children:[(0,n.jsx)(s.FormLabel,{fontSize:"sm",color:f.colors.text,children:"Filter Value"}),(0,n.jsx)(s.Input,{value:e.value,onChange:e=>D(r,{value:e.target.value}),placeholder:"channel"===e.type?"general or {channel.name}":"role"===e.type?"Member or {role.name}":"user"===e.type?"username or {user.id}":"regex"===e.type?"^Hello.*":"content"===e.type?"hello world":"Filter value",bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,size:"sm"})]}),(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.textSecondary,children:null==(o=m.find(r=>r.value===e.type))?void 0:o.description})]})]},r)}),(!W.filters||0===W.filters.length)&&(0,n.jsxs)(s.Alert,{status:"info",borderRadius:"md",children:[(0,n.jsx)(s.AlertIcon,{}),(0,n.jsx)(s.AlertDescription,{children:"No filters configured. This event will trigger for all occurrences of the selected event type."})]})]})]})}),(0,n.jsx)(s.TabPanel,{children:(0,n.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,n.jsx)(s.Text,{fontSize:"lg",fontWeight:"bold",color:f.colors.text,children:"Event Settings"}),(0,n.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(s.HStack,{spacing:4,children:[(0,n.jsx)(s.Switch,{isChecked:W.ignoreBot,onChange:e=>w({ignoreBot:e.target.checked}),colorScheme:"green"}),(0,n.jsxs)(s.VStack,{align:"start",spacing:0,children:[(0,n.jsx)(s.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Ignore Bot Messages"}),(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.textSecondary,children:"Don't trigger on messages from bots (recommended)"})]})]}),(0,n.jsxs)(s.HStack,{spacing:4,children:[(0,n.jsx)(s.Switch,{isChecked:W.ignoreSystem,onChange:e=>w({ignoreSystem:e.target.checked}),colorScheme:"green"}),(0,n.jsxs)(s.VStack,{align:"start",spacing:0,children:[(0,n.jsx)(s.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Ignore System Messages"}),(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.textSecondary,children:"Don't trigger on Discord system messages"})]})]}),(0,n.jsxs)(s.HStack,{spacing:4,children:[(0,n.jsx)(s.Switch,{isChecked:W.rateLimited,onChange:e=>w({rateLimited:e.target.checked}),colorScheme:"orange"}),(0,n.jsxs)(s.VStack,{align:"start",spacing:0,children:[(0,n.jsx)(s.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Rate Limited"}),(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.textSecondary,children:"Limit how often this event can trigger"})]})]}),W.rateLimited&&(0,n.jsxs)(s.FormControl,{children:[(0,n.jsx)(s.FormLabel,{color:f.colors.text,children:"Rate Limit (milliseconds)"}),(0,n.jsxs)(s.NumberInput,{value:W.rateLimit||1e3,onChange:e=>w({rateLimit:parseInt(e)||1e3}),min:100,max:6e4,children:[(0,n.jsx)(s.NumberInputField,{bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border}),(0,n.jsxs)(s.NumberInputStepper,{children:[(0,n.jsx)(s.NumberIncrementStepper,{}),(0,n.jsx)(s.NumberDecrementStepper,{})]})]}),(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.textSecondary,mt:1,children:"Minimum time between triggers (1000ms = 1 second)"})]})]})]})}),(0,n.jsx)(s.TabPanel,{children:(0,n.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,n.jsx)(s.Text,{fontSize:"lg",fontWeight:"bold",color:f.colors.text,children:"Advanced Settings"}),(0,n.jsxs)(s.FormControl,{children:[(0,n.jsx)(s.FormLabel,{color:f.colors.text,children:"Event Priority"}),(0,n.jsxs)(s.NumberInput,{value:W.priority||1,onChange:e=>w({priority:parseInt(e)||1}),min:1,max:10,children:[(0,n.jsx)(s.NumberInputField,{bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border}),(0,n.jsxs)(s.NumberInputStepper,{children:[(0,n.jsx)(s.NumberIncrementStepper,{}),(0,n.jsx)(s.NumberDecrementStepper,{})]})]}),(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.textSecondary,mt:1,children:"Higher priority events execute first (1 = highest, 10 = lowest)"})]}),(0,n.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(s.HStack,{spacing:4,children:[(0,n.jsx)(s.Switch,{isChecked:W.async,onChange:e=>w({async:e.target.checked}),colorScheme:"green"}),(0,n.jsxs)(s.VStack,{align:"start",spacing:0,children:[(0,n.jsx)(s.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Async Processing"}),(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.textSecondary,children:"Don't wait for this event to complete before processing others"})]})]}),(0,n.jsxs)(s.HStack,{spacing:4,children:[(0,n.jsx)(s.Switch,{isChecked:W.retryOnError,onChange:e=>w({retryOnError:e.target.checked}),colorScheme:"red"}),(0,n.jsxs)(s.VStack,{align:"start",spacing:0,children:[(0,n.jsx)(s.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Retry on Error"}),(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.textSecondary,children:"Automatically retry if event processing fails"})]})]}),W.retryOnError&&(0,n.jsxs)(s.FormControl,{children:[(0,n.jsx)(s.FormLabel,{color:f.colors.text,children:"Max Retries"}),(0,n.jsxs)(s.NumberInput,{value:W.maxRetries||3,onChange:e=>w({maxRetries:parseInt(e)||3}),min:1,max:10,children:[(0,n.jsx)(s.NumberInputField,{bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border}),(0,n.jsxs)(s.NumberInputStepper,{children:[(0,n.jsx)(s.NumberIncrementStepper,{}),(0,n.jsx)(s.NumberDecrementStepper,{})]})]}),(0,n.jsx)(s.Text,{fontSize:"xs",color:f.colors.textSecondary,mt:1,children:"Maximum number of retry attempts"})]})]})]})})]})]}),(0,n.jsx)(s.Button,{colorScheme:"green",onClick:()=>{v.eventType=W.eventType,v.description=W.description,v.filters=W.filters,v.ignoreBot=W.ignoreBot,v.ignoreSystem=W.ignoreSystem,v.rateLimited=W.rateLimited,v.rateLimit=W.rateLimit,v.priority=W.priority,v.async=W.async,v.retryOnError=W.retryOnError,v.maxRetries=W.maxRetries,v.channelRestrictions=W.channelRestrictions,v.roleRestrictions=W.roleRestrictions,v.label=W.eventType?I(W.eventType):"Event",T()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});h.displayName="EventNode";let p=h},94069:(e,r,o)=>{o.d(r,{A:()=>d});var n=o(94513),t=o(94285),i=o(6179),s=o(96867),a=o(75551),l=o(27263);let c=(0,t.memo)(e=>{let{data:r,selected:o}=e,{currentScheme:t}=(0,l.DP)();return(0,n.jsxs)(s.az,{bg:t.colors.surface,border:"2px solid ".concat(o?t.colors.primary:t.colors.border),borderRadius:"full",p:2,minW:"80px",minH:"80px",boxShadow:"lg",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",_hover:{boxShadow:"xl",transform:"scale(1.05)",borderColor:t.colors.primary},transition:"all 0.2s",children:[(0,n.jsxs)(s.Tk,{spacing:1,align:"center",children:[(0,n.jsx)(s.az,{bg:t.colors.primary,color:"white",borderRadius:"full",p:1,fontSize:"sm",boxShadow:"sm",children:(0,n.jsx)(a.aze,{})}),(0,n.jsx)(s.EY,{fontSize:"xs",fontWeight:"bold",color:t.colors.text,textAlign:"center",children:r.label})]}),(0,n.jsx)(i.h7,{type:"source",position:i.yX.Bottom,style:{background:t.colors.background,border:"2px solid ".concat(t.colors.primary),width:"16px",height:"16px",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",bottom:"-8px",left:"50%",transform:"translateX(-50%)"}})]})});c.displayName="TriggerNode";let d=c}}]);