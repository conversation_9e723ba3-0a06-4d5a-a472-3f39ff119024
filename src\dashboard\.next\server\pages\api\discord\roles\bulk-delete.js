"use strict";(()=>{var e={};e.id=6858,e.ids=[6858],e.modules={15806:e=>{e.exports=require("next-auth/next")},18378:(e,r,t)=>{t.r(r),t.d(r,{config:()=>p,default:()=>h,routeModule:()=>m});var s={};t.r(s),t.d(s,{default:()=>f});var i=t(93433),a=t(20264),o=t(20584),u=t(15806),d=t(94506),n=t(98580);async function l(e,r,t,s=0){try{let i=await fetch(`https://discord.com/api/v10/guilds/${r}/roles/${t}`,{method:"DELETE",headers:{Authorization:`Bot ${e}`}});if(i.ok)return!0;if(429===i.status){let a=await i.json(),o=1e3*(a.retry_after||1);if(await new Promise(e=>setTimeout(e,o)),s<2)return l(e,r,t,s+1)}return!1}catch(i){if(s<2)return await new Promise(e=>setTimeout(e,1e3)),l(e,r,t,s+1);return!1}}async function c(e,r,t){return(await Promise.all(t.map(async t=>{let s=await l(e,r,t);return{roleId:t,success:s}}))).reduce((e,{roleId:r,success:t})=>(t?e.succeeded.push(r):e.failed.push(r),e),{succeeded:[],failed:[]})}async function f(e,r){try{let t=await (0,u.getServerSession)(e,r,d.authOptions);if(!t?.user)return r.status(401).json({error:"Unauthorized"});if(!t.user.isAdmin)return r.status(403).json({error:"Forbidden - Admin access required"});let{guildId:s,token:i}=n.dashboardConfig.bot;if(!i||!s)return r.status(500).json({error:"Bot configuration missing"});if("POST"===e.method)try{let{roleIds:t}=e.body;if(!Array.isArray(t)||0===t.length)return r.status(400).json({error:"Role IDs array is required"});let a={succeeded:[],failed:[]};for(let e=0;e<t.length;e+=5){let r=t.slice(e,e+5),o=await c(i,s,r);a.succeeded.push(...o.succeeded),a.failed.push(...o.failed),e+5<t.length&&await new Promise(e=>setTimeout(e,1e3))}return r.status(200).json({message:`Successfully deleted ${a.succeeded.length} roles${a.failed.length>0?`, failed to delete ${a.failed.length} roles`:""}`,succeeded:a.succeeded,failed:a.failed})}catch(e){return r.status(500).json({error:"Failed to delete roles"})}return r.status(405).json({error:"Method not allowed"})}catch(e){return r.status(500).json({error:"Internal server error"})}}let h=(0,o.M)(s,"default"),p=(0,o.M)(s,"config"),m=new i.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/discord/roles/bulk-delete",pathname:"/api/discord/roles/bulk-delete",bundlePath:"",filename:""},userland:s})},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(18378));module.exports=s})();