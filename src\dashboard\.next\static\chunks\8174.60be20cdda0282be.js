"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8174],{98174:(e,s,n)=>{n.r(s),n.d(s,{default:()=>E});var t=n(94513),r=n(64349),i=n(31862),a=n(15975),l=n(59220),c=n(46949),o=n(81139),h=n(95066),d=n(53083),x=n(24490),p=n(31840),j=n(29607),u=n(35624),m=n(35339),C=n(71569),g=n(55206),y=n(7836),f=n(94285);let w={GUILD_TEXT:0,GUILD_VOICE:2,GUILD_CATEGORY:4};function E(e){let{isOpen:s,onClose:n,onSuccess:E}=e,L=(0,y.d)(),[I,T]=(0,f.useState)(!1),[b,v]=(0,f.useState)([]),[O,_]=(0,f.useState)({name:"",type:"GUILD_TEXT",parent:"",topic:"",nsfw:!1,rateLimitPerUser:0,position:0,bitrate:64e3,userLimit:0});(0,f.useEffect)(()=>{s&&G()},[s]);let G=async()=>{try{let e=await fetch("/api/discord/channels");if(!e.ok)throw Error("Failed to fetch channels");let s=(await e.json()).filter(e=>"number"==typeof e.raw_type?e.raw_type===w.GUILD_CATEGORY:"GUILD_CATEGORY"===e.type||"category"===e.type);v(s)}catch(e){L({title:"Error",description:"Failed to fetch categories",status:"error",duration:3e3})}},U=(e,s)=>{_(n=>({...n,[e]:s}))},S=async()=>{try{if(T(!0),!O.name.trim())return void L({title:"Error",description:"Channel name is required",status:"error",duration:3e3});let e=O.name.toLowerCase().replace(/\s+/g,"-"),s=w[O.type],t=await fetch("/api/discord/channels",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...O,name:e,type:s})});if(!t.ok){let e=await t.text(),s="Failed to create channel";try{let n=JSON.parse(e);s=n.message||n.error||s}catch(n){s=e}throw Error(s)}L({title:"Success",description:"Channel created successfully",status:"success",duration:3e3}),null==E||E(),n()}catch(e){L({title:"Error",description:e.message||"Failed to create channel",status:"error",duration:5e3})}finally{T(!1)}};return(0,f.useEffect)(()=>{s||_({name:"",type:"GUILD_TEXT",parent:"",topic:"",nsfw:!1,rateLimitPerUser:0,position:0,bitrate:64e3,userLimit:0})},[s]),(0,t.jsxs)(c.aF,{isOpen:s,onClose:n,size:"xl",children:[(0,t.jsx)(j.m,{backdropFilter:"blur(10px)"}),(0,t.jsxs)(d.$,{bg:"gray.800",border:"1px",borderColor:"blue.500",children:[(0,t.jsx)(p.r,{children:"Create Channel"}),(0,t.jsx)(h.s,{}),(0,t.jsx)(o.c,{children:(0,t.jsxs)(C.B,{spacing:4,children:[(0,t.jsxs)(i.MJ,{isRequired:!0,children:[(0,t.jsx)(a.l,{children:"Channel Name"}),(0,t.jsx)(l.p,{placeholder:"Enter channel name",value:O.name,onChange:e=>U("name",e.target.value)}),(0,t.jsx)(i.eK,{children:"Channel name will be automatically formatted (lowercase, hyphens instead of spaces)"})]}),(0,t.jsxs)(i.MJ,{children:[(0,t.jsx)(a.l,{children:"Channel Type"}),(0,t.jsxs)(m.l,{value:O.type,onChange:e=>U("type",e.target.value),children:[(0,t.jsx)("option",{value:"GUILD_TEXT",children:"Text Channel"}),(0,t.jsx)("option",{value:"GUILD_VOICE",children:"Voice Channel"}),(0,t.jsx)("option",{value:"GUILD_CATEGORY",children:"Category"})]})]}),"GUILD_CATEGORY"!==O.type&&(0,t.jsxs)(i.MJ,{children:[(0,t.jsx)(a.l,{children:"Parent Category"}),(0,t.jsxs)(m.l,{placeholder:"Select category",value:O.parent,onChange:e=>U("parent",e.target.value),children:[(0,t.jsx)("option",{value:"",children:"None"}),b.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),"GUILD_TEXT"===O.type&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(i.MJ,{children:[(0,t.jsx)(a.l,{children:"Channel Topic"}),(0,t.jsx)(l.p,{placeholder:"Enter channel topic",value:O.topic,onChange:e=>U("topic",e.target.value)})]}),(0,t.jsxs)(i.MJ,{children:[(0,t.jsx)(a.l,{children:"Slowmode (seconds)"}),(0,t.jsxs)(u.Q7,{min:0,max:21600,value:O.rateLimitPerUser,onChange:e=>U("rateLimitPerUser",parseInt(e)),children:[(0,t.jsx)(u.OO,{}),(0,t.jsxs)(u.lw,{children:[(0,t.jsx)(u.Q0,{}),(0,t.jsx)(u.Sh,{})]})]}),(0,t.jsx)(i.eK,{children:"Set how long users must wait between sending messages (0 to disable)"})]}),(0,t.jsxs)(i.MJ,{display:"flex",alignItems:"center",children:[(0,t.jsx)(a.l,{mb:"0",children:"Age-Restricted (NSFW)"}),(0,t.jsx)(g.d,{isChecked:O.nsfw,onChange:e=>U("nsfw",e.target.checked)})]})]}),"GUILD_VOICE"===O.type&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(i.MJ,{children:[(0,t.jsx)(a.l,{children:"Bitrate (kbps)"}),(0,t.jsxs)(u.Q7,{min:8,max:96,value:O.bitrate/1e3,onChange:e=>U("bitrate",1e3*parseInt(e)),children:[(0,t.jsx)(u.OO,{}),(0,t.jsxs)(u.lw,{children:[(0,t.jsx)(u.Q0,{}),(0,t.jsx)(u.Sh,{})]})]})]}),(0,t.jsxs)(i.MJ,{children:[(0,t.jsx)(a.l,{children:"User Limit"}),(0,t.jsxs)(u.Q7,{min:0,max:99,value:O.userLimit,onChange:e=>U("userLimit",parseInt(e)),children:[(0,t.jsx)(u.OO,{}),(0,t.jsxs)(u.lw,{children:[(0,t.jsx)(u.Q0,{}),(0,t.jsx)(u.Sh,{})]})]}),(0,t.jsx)(i.eK,{children:"Set to 0 for unlimited users"})]})]}),(0,t.jsxs)(i.MJ,{children:[(0,t.jsx)(a.l,{children:"Position"}),(0,t.jsxs)(u.Q7,{min:0,value:O.position,onChange:e=>U("position",parseInt(e)),children:[(0,t.jsx)(u.OO,{}),(0,t.jsxs)(u.lw,{children:[(0,t.jsx)(u.Q0,{}),(0,t.jsx)(u.Sh,{})]})]}),(0,t.jsx)(i.eK,{children:"Channel position in the list (0 = top)"})]})]})}),(0,t.jsxs)(x.j,{children:[(0,t.jsx)(r.$,{variant:"ghost",mr:3,onClick:n,children:"Cancel"}),(0,t.jsx)(r.$,{colorScheme:"blue",onClick:S,isLoading:I,loadingText:"Creating...",children:"Create Channel"})]})]})]})}}}]);