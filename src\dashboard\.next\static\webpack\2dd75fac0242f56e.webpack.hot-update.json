{"c": ["pages/admin/guilds-pages_admin_guilds_tsx-d15b7b25", "webpack", "_pages-dir-browser_components_EditRoleDialog_tsx", "_pages-dir-browser_components_CreateRoleDialog_tsx", "pages/admin/experimental/addon-builder-__"], "r": ["pages/admin/applications-builder", "/_error", "commons", "pages/admin/guilds-__"], "m": ["(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CUsers%5CPete%20Gaming%20PC%5CDesktop%5C404%20Bot%5Csrc%5Cdashboard%5Cpages%5Cadmin%5Capplications-builder.tsx&page=%2Fadmin%2Fapplications-builder!", "(pages-dir-browser)/./pages/admin/applications-builder.tsx", "(pages-dir-browser)/__barrel_optimize__?names=<PERSON><PERSON>,<PERSON>ertIcon,Badge,Box,Button,Card,CardBody,Divider,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,Menu,MenuButton,MenuItem,MenuList,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs", "(pages-dir-browser)/__barrel_optimize__?names=FaChevronDown,FaClipboardList,FaCog,FaEdit,FaEye,FaLayerGroup,FaPalette,FaPlus,FaRocket,FaTrash,FaWrench!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs", "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CUsers%5CPete%20Gaming%20PC%5CDesktop%5C404%20Bot%5Cnode_modules%5C.pnpm%5Cnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!"]}