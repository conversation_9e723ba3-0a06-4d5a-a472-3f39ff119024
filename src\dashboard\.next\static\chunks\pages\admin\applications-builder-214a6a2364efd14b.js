(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4779],{64331:(e,i,t)=>{"use strict";t.r(i),t.d(i,{default:()=>V});var s=t(94513),l=t(94285),r=t(67116),n=t(1648),a=t(43700),o=t(79028),c=t(64349),d=t(5142),p=t(28365),u=t(36468),h=t(31862),x=t(15975),j=t(1871),b=t(78813),g=t(29484),m=t(22184),y=t(59220),q=t(52156),f=t(75975),w=t(11593),v=t(78723),C=t(46949),k=t(81139),S=t(95066),T=t(53083),E=t(24490),A=t(31840),z=t(29607),D=t(35339),M=t(30301),F=t(55206),P=t(93493),J=t(91140),R=t(57688),_=t(21181),O=t(56858),N=t(5130),W=t(52442),I=t(3037),H=t(7836),L=t(84622);t(75632),t(82273);var $=t(99500),G=t(97119),B=t(53424),K=t(58686);let Q=[{value:"text",label:"Short Text",icon:$.uO9},{value:"textarea",label:"Long Text",icon:$.kkc},{value:"select",label:"Dropdown",icon:$.Vr3},{value:"radio",label:"Multiple Choice",icon:$.aQJ},{value:"checkbox",label:"Checkboxes",icon:$.aQJ},{value:"number",label:"Number",icon:$.Ph},{value:"email",label:"Email",icon:$.uoG}],U=["blue","green","red","purple","orange","pink","teal","cyan","yellow"];function V(){let[e,i]=(0,l.useState)([]),[t,u]=(0,l.useState)(null),[D,W]=(0,l.useState)(!0),[Q,U]=(0,l.useState)(!1),[V,ee]=(0,l.useState)(0),{isOpen:ei,onOpen:et,onClose:es}=(0,L.j)(),{isOpen:el,onOpen:er,onClose:en}=(0,L.j)(),ea=(0,H.d)(),{data:eo}=(0,B.useSession)();(0,K.useRouter)(),(0,l.useEffect)(()=>{ec()},[]);let ec=async()=>{try{W(!0);let e=await fetch("/api/admin/applications-builder");if(e.ok){let t=await e.json();i(t.applications||[])}}catch(e){ea({title:"Error",description:"Failed to load applications",status:"error",duration:3e3})}finally{W(!1)}},ed=e=>{u(e),et()},ep=async e=>{try{(await fetch("/api/admin/applications-builder/".concat(e),{method:"DELETE"})).ok&&(i(i=>i.filter(i=>i.id!==e)),ea({title:"Success",description:"Application deleted successfully",status:"success",duration:3e3}))}catch(e){ea({title:"Error",description:"Failed to delete application",status:"error",duration:3e3})}},eu=async e=>{U(!0);try{let i=await fetch("/api/admin/applications-builder",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(i.ok)await i.json(),await ec(),es(),ea({title:"Success",description:"Application saved successfully",status:"success",duration:3e3});else{let e=await i.json();throw Error(e.error||"Failed to save application")}}catch(e){ea({title:"Error",description:e instanceof Error?e.message:"Failed to save application",status:"error",duration:5e3})}finally{U(!1)}},eh=async(i,t)=>{try{let s=e.find(e=>e.id===i);if(!s)return;let l={...s,enabled:t},r=await fetch("/api/admin/applications-builder",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(r.ok)await ec(),ea({title:"Success",description:"Application ".concat(t?"activated":"deactivated"," successfully"),status:"success",duration:3e3});else{let e=await r.json();throw Error(e.error||"Failed to update application status")}}catch(e){ea({title:"Error",description:e instanceof Error?e.message:"Failed to update application status",status:"error",duration:3e3})}};return(0,s.jsxs)(G.A,{children:[(0,s.jsx)(o.a,{p:8,children:(0,s.jsxs)(I.T,{align:"stretch",spacing:6,children:[(0,s.jsxs)(j.z,{justify:"space-between",children:[(0,s.jsxs)(I.T,{align:"start",spacing:1,children:[(0,s.jsx)(b.D,{size:"lg",children:"Applications Builder"}),(0,s.jsx)(N.E,{color:"gray.600",_dark:{color:"gray.300"},children:"Create and manage custom application forms for your server"})]}),(0,s.jsx)(c.$,{colorScheme:"blue",leftIcon:(0,s.jsx)($.OiG,{}),onClick:()=>{u({id:"app-".concat(Date.now()),title:"New Application",description:"Description for new application",color:"blue",icon:"FaClipboardList",enabled:!1,questions:[],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!1,notificationChannels:[]}}),et()},children:"Create Application"})]}),(0,s.jsxs)(O.t,{index:V,onChange:ee,variant:"enclosed",children:[(0,s.jsxs)(J.w,{children:[(0,s.jsx)(P.o,{children:"Applications"}),(0,s.jsx)(P.o,{children:"Templates"}),(0,s.jsx)(P.o,{children:"Settings"})]}),(0,s.jsxs)(_.T,{children:[(0,s.jsx)(R.K,{children:(0,s.jsxs)(I.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(r.F,{status:"info",children:[(0,s.jsx)(n._,{}),(0,s.jsx)(N.E,{children:"Build custom application forms with drag-and-drop ease. Create different types of applications for your server members."})]}),(0,s.jsx)(M.r,{columns:{base:1,md:2,lg:3},spacing:4,children:e.map(e=>(0,s.jsx)(d.Z,{borderTop:"4px solid",borderTopColor:"".concat(e.color,".500"),children:(0,s.jsx)(p.b,{children:(0,s.jsxs)(I.T,{align:"stretch",spacing:3,children:[(0,s.jsxs)(j.z,{justify:"space-between",children:[(0,s.jsxs)(I.T,{align:"start",spacing:1,children:[(0,s.jsx)(b.D,{size:"md",children:e.title}),(0,s.jsx)(N.E,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,s.jsx)(a.E,{colorScheme:e.enabled?"green":"gray",children:e.enabled?"Active":"Inactive"})]}),(0,s.jsxs)(N.E,{fontSize:"sm",color:"gray.500",children:[e.questions.length," questions configured"]}),(0,s.jsxs)(j.z,{spacing:2,children:[(0,s.jsx)(c.$,{size:"sm",colorScheme:"blue",leftIcon:(0,s.jsx)($.uO9,{}),onClick:()=>ed(e),children:"Edit"}),(0,s.jsx)(c.$,{size:"sm",variant:"outline",leftIcon:(0,s.jsx)($.Ny1,{}),onClick:()=>{u(e),er()},children:"Preview"}),(0,s.jsxs)(q.W,{children:[(0,s.jsx)(f.I,{as:m.K,icon:(0,s.jsx)($.Pcn,{}),size:"sm"}),(0,s.jsxs)(v.c,{children:[(0,s.jsx)(w.D,{icon:(0,s.jsx)($.Pcn,{}),onClick:()=>eh(e.id,!e.enabled),children:e.enabled?"Deactivate":"Activate"}),(0,s.jsx)(w.D,{icon:(0,s.jsx)($.qbC,{}),color:"red.500",onClick:()=>ep(e.id),children:"Delete"})]})]})]})]})})},e.id))})]})}),(0,s.jsx)(R.K,{children:(0,s.jsxs)(I.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(r.F,{status:"info",children:[(0,s.jsx)(n._,{}),(0,s.jsx)(N.E,{children:"Pre-built templates to get you started quickly. Choose from common application types."})]}),(0,s.jsx)(M.r,{columns:{base:1,md:2,lg:3},spacing:4,children:(0,s.jsx)(X,{onSelectTemplate:e=>{u({...e,id:"app-".concat(Date.now()),enabled:!1}),et()}})})]})}),(0,s.jsx)(R.K,{children:(0,s.jsxs)(I.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(r.F,{status:"info",children:[(0,s.jsx)(n._,{}),(0,s.jsx)(N.E,{children:"Global settings for all applications. Configure defaults and system-wide preferences."})]}),(0,s.jsx)(d.Z,{children:(0,s.jsx)(p.b,{children:(0,s.jsxs)(I.T,{align:"stretch",spacing:4,children:[(0,s.jsx)(b.D,{size:"md",children:"Global Settings"}),(0,s.jsxs)(h.MJ,{children:[(0,s.jsx)(x.l,{children:"Default Auto-Response"}),(0,s.jsx)(F.d,{})]}),(0,s.jsxs)(h.MJ,{children:[(0,s.jsx)(x.l,{children:"Require Email Verification"}),(0,s.jsx)(F.d,{})]}),(0,s.jsxs)(h.MJ,{children:[(0,s.jsx)(x.l,{children:"Application Cooldown (days)"}),(0,s.jsx)(y.p,{type:"number",defaultValue:30})]})]})})})]})})]})]})]})}),(0,s.jsxs)(C.aF,{isOpen:ei,onClose:es,size:"6xl",scrollBehavior:"inside",children:[(0,s.jsx)(z.m,{}),(0,s.jsxs)(T.$,{children:[(0,s.jsx)(A.r,{children:(0,s.jsxs)(j.z,{children:[(0,s.jsx)(g.I,{as:$.lV_,color:"blue.500"}),(0,s.jsxs)(N.E,{children:[(null==t?void 0:t.id.startsWith("app-"))?"Create":"Edit"," Application"]})]})}),(0,s.jsx)(S.s,{}),(0,s.jsx)(k.c,{children:t&&(0,s.jsx)(Z,{application:t,onSave:eu,onCancel:es,isSaving:Q})})]})]}),(0,s.jsxs)(C.aF,{isOpen:el,onClose:en,size:"4xl",children:[(0,s.jsx)(z.m,{}),(0,s.jsxs)(T.$,{children:[(0,s.jsx)(A.r,{children:(0,s.jsxs)(j.z,{children:[(0,s.jsx)(g.I,{as:$.Ny1,color:"green.500"}),(0,s.jsxs)(N.E,{children:["Preview: ",null==t?void 0:t.title]})]})}),(0,s.jsx)(S.s,{}),(0,s.jsx)(k.c,{children:t&&(0,s.jsx)(Y,{application:t})}),(0,s.jsx)(E.j,{children:(0,s.jsx)(c.$,{onClick:en,children:"Close"})})]})]})]})}function Z(e){let{application:i,onSave:t,onCancel:r,isSaving:n=!1}=e,[a,u]=(0,l.useState)(i),[b,g]=(0,l.useState)(0),q=e=>{u(i=>({...i,...e}))},f=(e,i)=>{u(t=>({...t,questions:t.questions.map(t=>t.id===e?{...t,...i}:t)}))},w=e=>{u(i=>({...i,questions:i.questions.filter(i=>i.id!==e)}))};return(0,s.jsxs)(I.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(O.t,{index:b,onChange:g,children:[(0,s.jsxs)(J.w,{children:[(0,s.jsx)(P.o,{children:"Basic Info"}),(0,s.jsx)(P.o,{children:"Questions"}),(0,s.jsx)(P.o,{children:"Settings"})]}),(0,s.jsxs)(_.T,{children:[(0,s.jsx)(R.K,{children:(0,s.jsxs)(I.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(h.MJ,{children:[(0,s.jsx)(x.l,{children:"Application Title"}),(0,s.jsx)(y.p,{value:a.title,onChange:e=>q({title:e.target.value}),placeholder:"Enter application title"})]}),(0,s.jsxs)(h.MJ,{children:[(0,s.jsx)(x.l,{children:"Description"}),(0,s.jsx)(W.T,{value:a.description,onChange:e=>q({description:e.target.value}),placeholder:"Enter application description"})]}),(0,s.jsxs)(h.MJ,{children:[(0,s.jsx)(x.l,{children:"Color Scheme"}),(0,s.jsx)(j.z,{spacing:2,children:U.map(e=>(0,s.jsx)(o.a,{w:8,h:8,bg:"".concat(e,".500"),rounded:"md",cursor:"pointer",border:a.color===e?"3px solid":"1px solid",borderColor:a.color===e?"white":"gray.300",onClick:()=>q({color:e})},e))})]}),(0,s.jsxs)(h.MJ,{children:[(0,s.jsx)(x.l,{children:"Status"}),(0,s.jsxs)(j.z,{children:[(0,s.jsx)(F.d,{isChecked:a.enabled,onChange:e=>q({enabled:e.target.checked})}),(0,s.jsx)(N.E,{children:a.enabled?"Active":"Inactive"})]})]})]})}),(0,s.jsx)(R.K,{children:(0,s.jsxs)(I.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(j.z,{justify:"space-between",children:[(0,s.jsxs)(N.E,{fontWeight:"bold",children:["Questions (",a.questions.length,")"]}),(0,s.jsx)(c.$,{size:"sm",colorScheme:"blue",leftIcon:(0,s.jsx)($.OiG,{}),onClick:()=>{let e={id:"q-".concat(Date.now()),type:"text",label:"New Question",required:!1};u(i=>({...i,questions:[...i.questions,e]}))},children:"Add Question"})]}),a.questions.map(e=>(0,s.jsx)(d.Z,{borderLeft:"4px solid",borderLeftColor:"blue.500",children:(0,s.jsx)(p.b,{children:(0,s.jsxs)(I.T,{align:"stretch",spacing:3,children:[(0,s.jsxs)(j.z,{justify:"space-between",children:[(0,s.jsxs)(h.MJ,{flex:1,children:[(0,s.jsx)(x.l,{children:"Question Label"}),(0,s.jsx)(y.p,{value:e.label,onChange:i=>f(e.id,{label:i.target.value}),placeholder:"Enter question label"})]}),(0,s.jsxs)(h.MJ,{w:"200px",children:[(0,s.jsx)(x.l,{children:"Type"}),(0,s.jsx)(D.l,{value:e.type,onChange:i=>f(e.id,{type:i.target.value}),children:Q.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsx)(m.K,{"aria-label":"Delete question",icon:(0,s.jsx)($.qbC,{}),colorScheme:"red",size:"sm",onClick:()=>w(e.id)})]}),(0,s.jsxs)(h.MJ,{children:[(0,s.jsx)(x.l,{children:"Placeholder"}),(0,s.jsx)(y.p,{value:e.placeholder||"",onChange:i=>f(e.id,{placeholder:i.target.value}),placeholder:"Enter placeholder text"})]}),(0,s.jsx)(j.z,{children:(0,s.jsx)(h.MJ,{children:(0,s.jsxs)(j.z,{children:[(0,s.jsx)(F.d,{isChecked:e.required,onChange:i=>f(e.id,{required:i.target.checked})}),(0,s.jsx)(N.E,{children:"Required"})]})})})]})})},e.id))]})}),(0,s.jsx)(R.K,{children:(0,s.jsxs)(I.T,{align:"stretch",spacing:4,children:[(0,s.jsx)(h.MJ,{children:(0,s.jsxs)(j.z,{children:[(0,s.jsx)(F.d,{isChecked:a.settings.allowMultipleSubmissions,onChange:e=>q({settings:{...a.settings,allowMultipleSubmissions:e.target.checked}})}),(0,s.jsx)(N.E,{children:"Allow Multiple Submissions"})]})}),(0,s.jsx)(h.MJ,{children:(0,s.jsxs)(j.z,{children:[(0,s.jsx)(F.d,{isChecked:a.settings.requireApproval,onChange:e=>q({settings:{...a.settings,requireApproval:e.target.checked}})}),(0,s.jsx)(N.E,{children:"Require Manual Approval"})]})}),(0,s.jsx)(h.MJ,{children:(0,s.jsxs)(j.z,{children:[(0,s.jsx)(F.d,{isChecked:a.settings.autoResponse,onChange:e=>q({settings:{...a.settings,autoResponse:e.target.checked}})}),(0,s.jsx)(N.E,{children:"Send Auto-Response"})]})})]})})]})]}),(0,s.jsxs)(j.z,{justify:"flex-end",spacing:4,children:[(0,s.jsx)(c.$,{onClick:r,isDisabled:n,children:"Cancel"}),(0,s.jsx)(c.$,{colorScheme:"blue",onClick:()=>t(a),isLoading:n,loadingText:"Saving...",children:"Save Application"})]})]})}function X(e){let{onSelectTemplate:i}=e;return(0,s.jsx)(s.Fragment,{children:[{id:"template-moderator",title:"Moderator Application",description:"Standard moderator application with experience and scenario questions",color:"blue",icon:"FaUserShield",enabled:!1,questions:[{id:"q1",type:"text",label:"What is your Discord username?",required:!0},{id:"q2",type:"number",label:"How old are you?",required:!0},{id:"q3",type:"text",label:"What timezone are you in?",required:!0},{id:"q4",type:"number",label:"How many hours per week can you dedicate to moderation?",required:!0},{id:"q5",type:"textarea",label:"Why do you want to be a moderator?",required:!0},{id:"q6",type:"textarea",label:"Do you have any previous moderation experience?",required:!1},{id:"q7",type:"textarea",label:"How would you handle a heated argument between two members?",required:!0}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-developer",title:"Developer Application",description:"Technical application for developer positions",color:"green",icon:"FaCode",enabled:!1,questions:[{id:"q1",type:"text",label:"Full Name",required:!0},{id:"q2",type:"email",label:"Email Address",required:!0},{id:"q3",type:"textarea",label:"Tell us about your programming experience",required:!0},{id:"q4",type:"select",label:"Primary Programming Language",required:!0,options:["JavaScript","Python","Java","C#","Go","Other"]},{id:"q5",type:"textarea",label:"Describe a challenging project you worked on",required:!0},{id:"q6",type:"text",label:"GitHub/Portfolio URL",required:!1},{id:"q7",type:"radio",label:"Are you available for full-time work?",required:!0,options:["Yes","No","Part-time only"]}],settings:{allowMultipleSubmissions:!0,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-event-host",title:"Event Host Application",description:"Application for community event organizers",color:"purple",icon:"FaCalendar",enabled:!1,questions:[{id:"q1",type:"text",label:"Discord Username",required:!0},{id:"q2",type:"text",label:"Preferred Name",required:!0},{id:"q3",type:"textarea",label:"What types of events would you like to host?",required:!0},{id:"q4",type:"textarea",label:"Do you have experience organizing events?",required:!1},{id:"q5",type:"select",label:"How often would you like to host events?",required:!0,options:["Weekly","Bi-weekly","Monthly","As needed"]},{id:"q6",type:"textarea",label:"Describe an event idea you have",required:!0}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-support",title:"Support Team Application",description:"Customer support and help desk application",color:"orange",icon:"FaHeadset",enabled:!1,questions:[{id:"q1",type:"text",label:"Discord Username",required:!0},{id:"q2",type:"text",label:"Age",required:!0},{id:"q3",type:"text",label:"Timezone",required:!0},{id:"q4",type:"textarea",label:"Why do you want to join the support team?",required:!0},{id:"q5",type:"checkbox",label:"Which areas can you help with?",required:!0,options:["Technical Issues","Account Problems","General Questions","Bug Reports","Feature Requests"]},{id:"q6",type:"textarea",label:"How would you help a frustrated user?",required:!0}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-content",title:"Content Creator Application",description:"Application for content creators and influencers",color:"pink",icon:"FaVideo",enabled:!1,questions:[{id:"q1",type:"text",label:"Creator Name/Handle",required:!0},{id:"q2",type:"email",label:"Contact Email",required:!0},{id:"q3",type:"select",label:"Primary Content Platform",required:!0,options:["YouTube","Twitch","TikTok","Instagram","Twitter","Other"]},{id:"q4",type:"text",label:"Channel/Profile URL",required:!0},{id:"q5",type:"textarea",label:"What type of content do you create?",required:!0},{id:"q6",type:"number",label:"How many followers/subscribers do you have?",required:!1},{id:"q7",type:"textarea",label:"How would you promote our community?",required:!0}],settings:{allowMultipleSubmissions:!0,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-beta",title:"Beta Tester Application",description:"Application for beta testing programs",color:"teal",icon:"FaFlask",enabled:!1,questions:[{id:"q1",type:"text",label:"Discord Username",required:!0},{id:"q2",type:"textarea",label:"What interests you about beta testing?",required:!0},{id:"q3",type:"textarea",label:"Do you have experience finding and reporting bugs?",required:!1},{id:"q4",type:"select",label:"How much time can you dedicate to testing?",required:!0,options:["1-2 hours/week","3-5 hours/week","6-10 hours/week","10+ hours/week"]},{id:"q5",type:"checkbox",label:"Which platforms do you have access to?",required:!0,options:["Windows","Mac","Linux","iOS","Android","Web Browser"]}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}}].map(e=>(0,s.jsx)(d.Z,{borderTop:"4px solid",borderTopColor:"".concat(e.color,".500"),children:(0,s.jsx)(p.b,{children:(0,s.jsxs)(I.T,{align:"stretch",spacing:3,children:[(0,s.jsxs)(I.T,{align:"start",spacing:1,children:[(0,s.jsx)(b.D,{size:"md",children:e.title}),(0,s.jsx)(N.E,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,s.jsxs)(N.E,{fontSize:"sm",color:"gray.500",children:[e.questions.length," pre-configured questions"]}),(0,s.jsx)(c.$,{size:"sm",colorScheme:e.color,onClick:()=>i(e),leftIcon:(0,s.jsx)($.uoG,{}),children:"Use Template"})]})})},e.id))})}function Y(e){let{application:i}=e;return(0,s.jsxs)(I.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(I.T,{align:"center",spacing:2,children:[(0,s.jsx)(b.D,{size:"lg",children:i.title}),(0,s.jsx)(N.E,{color:"gray.600",_dark:{color:"gray.400"},children:i.description})]}),(0,s.jsx)(u.c,{}),(0,s.jsx)(I.T,{align:"stretch",spacing:4,children:i.questions.map(e=>{var i;return(0,s.jsxs)(h.MJ,{isRequired:e.required,children:[(0,s.jsx)(x.l,{children:e.label}),"text"===e.type&&(0,s.jsx)(y.p,{placeholder:e.placeholder}),"textarea"===e.type&&(0,s.jsx)(W.T,{placeholder:e.placeholder}),"select"===e.type&&(0,s.jsx)(D.l,{placeholder:e.placeholder,children:null==(i=e.options)?void 0:i.map(e=>(0,s.jsx)("option",{value:e,children:e},e))}),"number"===e.type&&(0,s.jsx)(y.p,{type:"number",placeholder:e.placeholder}),"email"===e.type&&(0,s.jsx)(y.p,{type:"email",placeholder:e.placeholder})]},e.id)})}),(0,s.jsx)(c.$,{colorScheme:i.color,size:"lg",isDisabled:!0,children:"Submit Application (Preview)"})]})}},91295:(e,i,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/applications-builder",function(){return t(64331)}])}},e=>{var i=i=>e(e.s=i);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,636,7398,1203,8792],()=>i(91295)),_N_E=e.O()}]);