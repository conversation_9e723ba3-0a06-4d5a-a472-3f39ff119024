"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040";
exports.ids = ["lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addDomEvent: () => (/* binding */ addDomEvent)\n/* harmony export */ });\nfunction addDomEvent(target, eventName, handler, options = { passive: true }) {\n    target.addEventListener(eventName, handler, options);\n    return () => target.removeEventListener(eventName, handler);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZXZlbnRzL2FkZC1kb20tZXZlbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2REFBNkQsZUFBZTtBQUM1RTtBQUNBO0FBQ0E7O0FBRXVCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXGV2ZW50c1xcYWRkLWRvbS1ldmVudC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gYWRkRG9tRXZlbnQodGFyZ2V0LCBldmVudE5hbWUsIGhhbmRsZXIsIG9wdGlvbnMgPSB7IHBhc3NpdmU6IHRydWUgfSkge1xuICAgIHRhcmdldC5hZGRFdmVudExpc3RlbmVyKGV2ZW50TmFtZSwgaGFuZGxlciwgb3B0aW9ucyk7XG4gICAgcmV0dXJuICgpID0+IHRhcmdldC5yZW1vdmVFdmVudExpc3RlbmVyKGV2ZW50TmFtZSwgaGFuZGxlcik7XG59XG5cbmV4cG9ydCB7IGFkZERvbUV2ZW50IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPointerEvent: () => (/* binding */ addPointerEvent)\n/* harmony export */ });\n/* harmony import */ var _add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./add-dom-event.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n/* harmony import */ var _event_info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./event-info.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\");\n\n\n\nfunction addPointerEvent(target, eventName, handler, options) {\n    return (0,_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_0__.addDomEvent)(target, eventName, (0,_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__.addPointerInfo)(handler), options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZXZlbnRzL2FkZC1wb2ludGVyLWV2ZW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0Q7QUFDQTs7QUFFbEQ7QUFDQSxXQUFXLCtEQUFXLG9CQUFvQiwrREFBYztBQUN4RDs7QUFFMkIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcZXZlbnRzXFxhZGQtcG9pbnRlci1ldmVudC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYWRkRG9tRXZlbnQgfSBmcm9tICcuL2FkZC1kb20tZXZlbnQubWpzJztcbmltcG9ydCB7IGFkZFBvaW50ZXJJbmZvIH0gZnJvbSAnLi9ldmVudC1pbmZvLm1qcyc7XG5cbmZ1bmN0aW9uIGFkZFBvaW50ZXJFdmVudCh0YXJnZXQsIGV2ZW50TmFtZSwgaGFuZGxlciwgb3B0aW9ucykge1xuICAgIHJldHVybiBhZGREb21FdmVudCh0YXJnZXQsIGV2ZW50TmFtZSwgYWRkUG9pbnRlckluZm8oaGFuZGxlciksIG9wdGlvbnMpO1xufVxuXG5leHBvcnQgeyBhZGRQb2ludGVyRXZlbnQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs":
/*!******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPointerInfo: () => (/* binding */ addPointerInfo),\n/* harmony export */   extractEventInfo: () => (/* binding */ extractEventInfo)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n\n\nfunction extractEventInfo(event) {\n    return {\n        point: {\n            x: event.pageX,\n            y: event.pageY,\n        },\n    };\n}\nconst addPointerInfo = (handler) => {\n    return (event) => (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isPrimaryPointer)(event) && handler(event, extractEventInfo(event));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZXZlbnRzL2V2ZW50LWluZm8ubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4Qzs7QUFFOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNERBQWdCO0FBQ3RDOztBQUU0QyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxldmVudHNcXGV2ZW50LWluZm8ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzUHJpbWFyeVBvaW50ZXIgfSBmcm9tICdtb3Rpb24tZG9tJztcblxuZnVuY3Rpb24gZXh0cmFjdEV2ZW50SW5mbyhldmVudCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHBvaW50OiB7XG4gICAgICAgICAgICB4OiBldmVudC5wYWdlWCxcbiAgICAgICAgICAgIHk6IGV2ZW50LnBhZ2VZLFxuICAgICAgICB9LFxuICAgIH07XG59XG5jb25zdCBhZGRQb2ludGVySW5mbyA9IChoYW5kbGVyKSA9PiB7XG4gICAgcmV0dXJuIChldmVudCkgPT4gaXNQcmltYXJ5UG9pbnRlcihldmVudCkgJiYgaGFuZGxlcihldmVudCwgZXh0cmFjdEV2ZW50SW5mbyhldmVudCkpO1xufTtcblxuZXhwb3J0IHsgYWRkUG9pbnRlckluZm8sIGV4dHJhY3RFdmVudEluZm8gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/use-dom-event.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/use-dom-event.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDomEvent: () => (/* binding */ useDomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./add-dom-event.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n\n\n\n/**\n * Attaches an event listener directly to the provided DOM element.\n *\n * Bypassing React's event system can be desirable, for instance when attaching non-passive\n * event handlers.\n *\n * ```jsx\n * const ref = useRef(null)\n *\n * useDomEvent(ref, 'wheel', onWheel, { passive: false })\n *\n * return <div ref={ref} />\n * ```\n *\n * @param ref - React.RefObject that's been provided to the element you want to bind the listener to.\n * @param eventName - Name of the event you want listen for.\n * @param handler - Function to fire when receiving the event.\n * @param options - Options to pass to `Event.addEventListener`.\n *\n * @public\n */\nfunction useDomEvent(ref, eventName, handler, options) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        const element = ref.current;\n        if (handler && element) {\n            return (0,_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_1__.addDomEvent)(element, eventName, handler, options);\n        }\n    }, [ref, eventName, handler, options]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/use-dom-event.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs":
/*!****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VisualElementDragControls: () => (/* binding */ VisualElementDragControls),\n/* harmony export */   elementDragControls: () => (/* binding */ elementDragControls)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _animation_interfaces_motion_value_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../animation/interfaces/motion-value.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs\");\n/* harmony import */ var _events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../events/add-dom-event.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n/* harmony import */ var _events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../events/add-pointer-event.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\");\n/* harmony import */ var _events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../events/event-info.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\");\n/* harmony import */ var _projection_geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../projection/geometry/conversion.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs\");\n/* harmony import */ var _projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../projection/geometry/delta-calc.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\");\n/* harmony import */ var _projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../projection/geometry/models.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/models.mjs\");\n/* harmony import */ var _projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../projection/utils/each-axis.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs\");\n/* harmony import */ var _projection_utils_measure_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../projection/utils/measure.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/utils/measure.mjs\");\n/* harmony import */ var _utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/get-context-window.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/get-context-window.mjs\");\n/* harmony import */ var _utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../utils/is-ref-object.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/is-ref-object.mjs\");\n/* harmony import */ var _value_use_will_change_add_will_change_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../value/use-will-change/add-will-change.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/value/use-will-change/add-will-change.mjs\");\n/* harmony import */ var _pan_PanSession_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../pan/PanSession.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs\");\n/* harmony import */ var _utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/constraints.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst elementDragControls = new WeakMap();\nclass VisualElementDragControls {\n    constructor(visualElement) {\n        this.openDragLock = null;\n        this.isDragging = false;\n        this.currentDirection = null;\n        this.originPoint = { x: 0, y: 0 };\n        /**\n         * The permitted boundaries of travel, in pixels.\n         */\n        this.constraints = false;\n        this.hasMutatedConstraints = false;\n        /**\n         * The per-axis resolved elastic values.\n         */\n        this.elastic = (0,_projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_0__.createBox)();\n        /**\n         * The latest pointer event. Used as fallback when the `cancel` and `stop` functions are called without arguments.\n         */\n        this.latestPointerEvent = null;\n        /**\n         * The latest pan info. Used as fallback when the `cancel` and `stop` functions are called without arguments.\n         */\n        this.latestPanInfo = null;\n        this.visualElement = visualElement;\n    }\n    start(originEvent, { snapToCursor = false, distanceThreshold } = {}) {\n        /**\n         * Don't start dragging if this component is exiting\n         */\n        const { presenceContext } = this.visualElement;\n        if (presenceContext && presenceContext.isPresent === false)\n            return;\n        const onSessionStart = (event) => {\n            const { dragSnapToOrigin } = this.getProps();\n            // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n            // the component.\n            dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n            if (snapToCursor) {\n                this.snapToCursor((0,_events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__.extractEventInfo)(event).point);\n            }\n        };\n        const onStart = (event, info) => {\n            // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n            const { drag, dragPropagation, onDragStart } = this.getProps();\n            if (drag && !dragPropagation) {\n                if (this.openDragLock)\n                    this.openDragLock();\n                this.openDragLock = (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.setDragLock)(drag);\n                // If we don 't have the lock, don't start dragging\n                if (!this.openDragLock)\n                    return;\n            }\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            this.isDragging = true;\n            this.currentDirection = null;\n            this.resolveConstraints();\n            if (this.visualElement.projection) {\n                this.visualElement.projection.isAnimationBlocked = true;\n                this.visualElement.projection.target = undefined;\n            }\n            /**\n             * Record gesture origin\n             */\n            (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n                let current = this.getAxisMotionValue(axis).get() || 0;\n                /**\n                 * If the MotionValue is a percentage value convert to px\n                 */\n                if (motion_dom__WEBPACK_IMPORTED_MODULE_2__.percent.test(current)) {\n                    const { projection } = this.visualElement;\n                    if (projection && projection.layout) {\n                        const measuredAxis = projection.layout.layoutBox[axis];\n                        if (measuredAxis) {\n                            const length = (0,_projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_4__.calcLength)(measuredAxis);\n                            current = length * (parseFloat(current) / 100);\n                        }\n                    }\n                }\n                this.originPoint[axis] = current;\n            });\n            // Fire onDragStart event\n            if (onDragStart) {\n                motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.postRender(() => onDragStart(event, info));\n            }\n            (0,_value_use_will_change_add_will_change_mjs__WEBPACK_IMPORTED_MODULE_5__.addValueToWillChange)(this.visualElement, \"transform\");\n            const { animationState } = this.visualElement;\n            animationState && animationState.setActive(\"whileDrag\", true);\n        };\n        const onMove = (event, info) => {\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            const { dragPropagation, dragDirectionLock, onDirectionLock, onDrag, } = this.getProps();\n            // If we didn't successfully receive the gesture lock, early return.\n            if (!dragPropagation && !this.openDragLock)\n                return;\n            const { offset } = info;\n            // Attempt to detect drag direction if directionLock is true\n            if (dragDirectionLock && this.currentDirection === null) {\n                this.currentDirection = getCurrentDirection(offset);\n                // If we've successfully set a direction, notify listener\n                if (this.currentDirection !== null) {\n                    onDirectionLock && onDirectionLock(this.currentDirection);\n                }\n                return;\n            }\n            // Update each point with the latest position\n            this.updateAxis(\"x\", info.point, offset);\n            this.updateAxis(\"y\", info.point, offset);\n            /**\n             * Ideally we would leave the renderer to fire naturally at the end of\n             * this frame but if the element is about to change layout as the result\n             * of a re-render we want to ensure the browser can read the latest\n             * bounding box to ensure the pointer and element don't fall out of sync.\n             */\n            this.visualElement.render();\n            /**\n             * This must fire after the render call as it might trigger a state\n             * change which itself might trigger a layout update.\n             */\n            onDrag && onDrag(event, info);\n        };\n        const onSessionEnd = (event, info) => {\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            this.stop(event, info);\n            this.latestPointerEvent = null;\n            this.latestPanInfo = null;\n        };\n        const resumeAnimation = () => (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => this.getAnimationState(axis) === \"paused\" &&\n            this.getAxisMotionValue(axis).animation?.play());\n        const { dragSnapToOrigin } = this.getProps();\n        this.panSession = new _pan_PanSession_mjs__WEBPACK_IMPORTED_MODULE_6__.PanSession(originEvent, {\n            onSessionStart,\n            onStart,\n            onMove,\n            onSessionEnd,\n            resumeAnimation,\n        }, {\n            transformPagePoint: this.visualElement.getTransformPagePoint(),\n            dragSnapToOrigin,\n            distanceThreshold,\n            contextWindow: (0,_utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_7__.getContextWindow)(this.visualElement),\n        });\n    }\n    /**\n     * @internal\n     */\n    stop(event, panInfo) {\n        const finalEvent = event || this.latestPointerEvent;\n        const finalPanInfo = panInfo || this.latestPanInfo;\n        const isDragging = this.isDragging;\n        this.cancel();\n        if (!isDragging || !finalPanInfo || !finalEvent)\n            return;\n        const { velocity } = finalPanInfo;\n        this.startAnimation(velocity);\n        const { onDragEnd } = this.getProps();\n        if (onDragEnd) {\n            motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.postRender(() => onDragEnd(finalEvent, finalPanInfo));\n        }\n    }\n    /**\n     * @internal\n     */\n    cancel() {\n        this.isDragging = false;\n        const { projection, animationState } = this.visualElement;\n        if (projection) {\n            projection.isAnimationBlocked = false;\n        }\n        this.panSession && this.panSession.end();\n        this.panSession = undefined;\n        const { dragPropagation } = this.getProps();\n        if (!dragPropagation && this.openDragLock) {\n            this.openDragLock();\n            this.openDragLock = null;\n        }\n        animationState && animationState.setActive(\"whileDrag\", false);\n    }\n    updateAxis(axis, _point, offset) {\n        const { drag } = this.getProps();\n        // If we're not dragging this axis, do an early return.\n        if (!offset || !shouldDrag(axis, drag, this.currentDirection))\n            return;\n        const axisValue = this.getAxisMotionValue(axis);\n        let next = this.originPoint[axis] + offset[axis];\n        // Apply constraints\n        if (this.constraints && this.constraints[axis]) {\n            next = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.applyConstraints)(next, this.constraints[axis], this.elastic[axis]);\n        }\n        axisValue.set(next);\n    }\n    resolveConstraints() {\n        const { dragConstraints, dragElastic } = this.getProps();\n        const layout = this.visualElement.projection &&\n            !this.visualElement.projection.layout\n            ? this.visualElement.projection.measure(false)\n            : this.visualElement.projection?.layout;\n        const prevConstraints = this.constraints;\n        if (dragConstraints && (0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__.isRefObject)(dragConstraints)) {\n            if (!this.constraints) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        }\n        else {\n            if (dragConstraints && layout) {\n                this.constraints = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.calcRelativeConstraints)(layout.layoutBox, dragConstraints);\n            }\n            else {\n                this.constraints = false;\n            }\n        }\n        this.elastic = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.resolveDragElastic)(dragElastic);\n        /**\n         * If we're outputting to external MotionValues, we want to rebase the measured constraints\n         * from viewport-relative to component-relative.\n         */\n        if (prevConstraints !== this.constraints &&\n            layout &&\n            this.constraints &&\n            !this.hasMutatedConstraints) {\n            (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n                if (this.constraints !== false &&\n                    this.getAxisMotionValue(axis)) {\n                    this.constraints[axis] = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.rebaseAxisConstraints)(layout.layoutBox[axis], this.constraints[axis]);\n                }\n            });\n        }\n    }\n    resolveRefConstraints() {\n        const { dragConstraints: constraints, onMeasureDragConstraints } = this.getProps();\n        if (!constraints || !(0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__.isRefObject)(constraints))\n            return false;\n        const constraintsElement = constraints.current;\n        (0,motion_utils__WEBPACK_IMPORTED_MODULE_10__.invariant)(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n        const { projection } = this.visualElement;\n        // TODO\n        if (!projection || !projection.layout)\n            return false;\n        const constraintsBox = (0,_projection_utils_measure_mjs__WEBPACK_IMPORTED_MODULE_11__.measurePageBox)(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n        let measuredConstraints = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.calcViewportConstraints)(projection.layout.layoutBox, constraintsBox);\n        /**\n         * If there's an onMeasureDragConstraints listener we call it and\n         * if different constraints are returned, set constraints to that\n         */\n        if (onMeasureDragConstraints) {\n            const userConstraints = onMeasureDragConstraints((0,_projection_geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_12__.convertBoxToBoundingBox)(measuredConstraints));\n            this.hasMutatedConstraints = !!userConstraints;\n            if (userConstraints) {\n                measuredConstraints = (0,_projection_geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_12__.convertBoundingBoxToBox)(userConstraints);\n            }\n        }\n        return measuredConstraints;\n    }\n    startAnimation(velocity) {\n        const { drag, dragMomentum, dragElastic, dragTransition, dragSnapToOrigin, onDragTransitionEnd, } = this.getProps();\n        const constraints = this.constraints || {};\n        const momentumAnimations = (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            if (!shouldDrag(axis, drag, this.currentDirection)) {\n                return;\n            }\n            let transition = (constraints && constraints[axis]) || {};\n            if (dragSnapToOrigin)\n                transition = { min: 0, max: 0 };\n            /**\n             * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n             * of spring animations so we should look into adding a disable spring option to `inertia`.\n             * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n             * using the value of `dragElastic`.\n             */\n            const bounceStiffness = dragElastic ? 200 : 1000000;\n            const bounceDamping = dragElastic ? 40 : 10000000;\n            const inertia = {\n                type: \"inertia\",\n                velocity: dragMomentum ? velocity[axis] : 0,\n                bounceStiffness,\n                bounceDamping,\n                timeConstant: 750,\n                restDelta: 1,\n                restSpeed: 10,\n                ...dragTransition,\n                ...transition,\n            };\n            // If we're not animating on an externally-provided `MotionValue` we can use the\n            // component's animation controls which will handle interactions with whileHover (etc),\n            // otherwise we just have to animate the `MotionValue` itself.\n            return this.startAxisValueAnimation(axis, inertia);\n        });\n        // Run all animations and then resolve the new drag constraints.\n        return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n    }\n    startAxisValueAnimation(axis, transition) {\n        const axisValue = this.getAxisMotionValue(axis);\n        (0,_value_use_will_change_add_will_change_mjs__WEBPACK_IMPORTED_MODULE_5__.addValueToWillChange)(this.visualElement, axis);\n        return axisValue.start((0,_animation_interfaces_motion_value_mjs__WEBPACK_IMPORTED_MODULE_13__.animateMotionValue)(axis, axisValue, 0, transition, this.visualElement, false));\n    }\n    stopAnimation() {\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => this.getAxisMotionValue(axis).stop());\n    }\n    pauseAnimation() {\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => this.getAxisMotionValue(axis).animation?.pause());\n    }\n    getAnimationState(axis) {\n        return this.getAxisMotionValue(axis).animation?.state;\n    }\n    /**\n     * Drag works differently depending on which props are provided.\n     *\n     * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n     * - Otherwise, we apply the delta to the x/y motion values.\n     */\n    getAxisMotionValue(axis) {\n        const dragKey = `_drag${axis.toUpperCase()}`;\n        const props = this.visualElement.getProps();\n        const externalMotionValue = props[dragKey];\n        return externalMotionValue\n            ? externalMotionValue\n            : this.visualElement.getValue(axis, (props.initial\n                ? props.initial[axis]\n                : undefined) || 0);\n    }\n    snapToCursor(point) {\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            const { drag } = this.getProps();\n            // If we're not dragging this axis, do an early return.\n            if (!shouldDrag(axis, drag, this.currentDirection))\n                return;\n            const { projection } = this.visualElement;\n            const axisValue = this.getAxisMotionValue(axis);\n            if (projection && projection.layout) {\n                const { min, max } = projection.layout.layoutBox[axis];\n                axisValue.set(point[axis] - (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.mixNumber)(min, max, 0.5));\n            }\n        });\n    }\n    /**\n     * When the viewport resizes we want to check if the measured constraints\n     * have changed and, if so, reposition the element within those new constraints\n     * relative to where it was before the resize.\n     */\n    scalePositionWithinConstraints() {\n        if (!this.visualElement.current)\n            return;\n        const { drag, dragConstraints } = this.getProps();\n        const { projection } = this.visualElement;\n        if (!(0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__.isRefObject)(dragConstraints) || !projection || !this.constraints)\n            return;\n        /**\n         * Stop current animations as there can be visual glitching if we try to do\n         * this mid-animation\n         */\n        this.stopAnimation();\n        /**\n         * Record the relative position of the dragged element relative to the\n         * constraints box and save as a progress value.\n         */\n        const boxProgress = { x: 0, y: 0 };\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            const axisValue = this.getAxisMotionValue(axis);\n            if (axisValue && this.constraints !== false) {\n                const latest = axisValue.get();\n                boxProgress[axis] = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.calcOrigin)({ min: latest, max: latest }, this.constraints[axis]);\n            }\n        });\n        /**\n         * Update the layout of this element and resolve the latest drag constraints\n         */\n        const { transformTemplate } = this.visualElement.getProps();\n        this.visualElement.current.style.transform = transformTemplate\n            ? transformTemplate({}, \"\")\n            : \"none\";\n        projection.root && projection.root.updateScroll();\n        projection.updateLayout();\n        this.resolveConstraints();\n        /**\n         * For each axis, calculate the current progress of the layout axis\n         * within the new constraints.\n         */\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            if (!shouldDrag(axis, drag, null))\n                return;\n            /**\n             * Calculate a new transform based on the previous box progress\n             */\n            const axisValue = this.getAxisMotionValue(axis);\n            const { min, max } = this.constraints[axis];\n            axisValue.set((0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.mixNumber)(min, max, boxProgress[axis]));\n        });\n    }\n    addListeners() {\n        if (!this.visualElement.current)\n            return;\n        elementDragControls.set(this.visualElement, this);\n        const element = this.visualElement.current;\n        /**\n         * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n         */\n        const stopPointerListener = (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_14__.addPointerEvent)(element, \"pointerdown\", (event) => {\n            const { drag, dragListener = true } = this.getProps();\n            drag && dragListener && this.start(event);\n        });\n        const measureDragConstraints = () => {\n            const { dragConstraints } = this.getProps();\n            if ((0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__.isRefObject)(dragConstraints) && dragConstraints.current) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        };\n        const { projection } = this.visualElement;\n        const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n        if (projection && !projection.layout) {\n            projection.root && projection.root.updateScroll();\n            projection.updateLayout();\n        }\n        motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.read(measureDragConstraints);\n        /**\n         * Attach a window resize listener to scale the draggable target within its defined\n         * constraints as the window resizes.\n         */\n        const stopResizeListener = (0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_15__.addDomEvent)(window, \"resize\", () => this.scalePositionWithinConstraints());\n        /**\n         * If the element's layout changes, calculate the delta and apply that to\n         * the drag gesture's origin point.\n         */\n        const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", (({ delta, hasLayoutChanged }) => {\n            if (this.isDragging && hasLayoutChanged) {\n                (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n                    const motionValue = this.getAxisMotionValue(axis);\n                    if (!motionValue)\n                        return;\n                    this.originPoint[axis] += delta[axis].translate;\n                    motionValue.set(motionValue.get() + delta[axis].translate);\n                });\n                this.visualElement.render();\n            }\n        }));\n        return () => {\n            stopResizeListener();\n            stopPointerListener();\n            stopMeasureLayoutListener();\n            stopLayoutUpdateListener && stopLayoutUpdateListener();\n        };\n    }\n    getProps() {\n        const props = this.visualElement.getProps();\n        const { drag = false, dragDirectionLock = false, dragPropagation = false, dragConstraints = false, dragElastic = _utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.defaultElastic, dragMomentum = true, } = props;\n        return {\n            ...props,\n            drag,\n            dragDirectionLock,\n            dragPropagation,\n            dragConstraints,\n            dragElastic,\n            dragMomentum,\n        };\n    }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n    return ((drag === true || drag === direction) &&\n        (currentDirection === null || currentDirection === direction));\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n    let direction = null;\n    if (Math.abs(offset.y) > lockThreshold) {\n        direction = \"y\";\n    }\n    else if (Math.abs(offset.x) > lockThreshold) {\n        direction = \"x\";\n    }\n    return direction;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/index.mjs":
/*!********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/index.mjs ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragGesture: () => (/* binding */ DragGesture)\n/* harmony export */ });\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../motion/features/Feature.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _VisualElementDragControls_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./VisualElementDragControls.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs\");\n\n\n\n\nclass DragGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor(node) {\n        super(node);\n        this.removeGroupControls = motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop;\n        this.removeListeners = motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop;\n        this.controls = new _VisualElementDragControls_mjs__WEBPACK_IMPORTED_MODULE_2__.VisualElementDragControls(node);\n    }\n    mount() {\n        // If we've been provided a DragControls for manual control over the drag gesture,\n        // subscribe this component to it on mount.\n        const { dragControls } = this.node.getProps();\n        if (dragControls) {\n            this.removeGroupControls = dragControls.subscribe(this.controls);\n        }\n        this.removeListeners = this.controls.addListeners() || motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop;\n    }\n    unmount() {\n        this.removeGroupControls();\n        this.removeListeners();\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/use-drag-controls.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/use-drag-controls.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragControls: () => (/* binding */ DragControls),\n/* harmony export */   useDragControls: () => (/* binding */ useDragControls)\n/* harmony export */ });\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n\n\n/**\n * Can manually trigger a drag gesture on one or more `drag`-enabled `motion` components.\n *\n * ```jsx\n * const dragControls = useDragControls()\n *\n * function startDrag(event) {\n *   dragControls.start(event, { snapToCursor: true })\n * }\n *\n * return (\n *   <>\n *     <div onPointerDown={startDrag} />\n *     <motion.div drag=\"x\" dragControls={dragControls} />\n *   </>\n * )\n * ```\n *\n * @public\n */\nclass DragControls {\n    constructor() {\n        this.componentControls = new Set();\n    }\n    /**\n     * Subscribe a component's internal `VisualElementDragControls` to the user-facing API.\n     *\n     * @internal\n     */\n    subscribe(controls) {\n        this.componentControls.add(controls);\n        return () => this.componentControls.delete(controls);\n    }\n    /**\n     * Start a drag gesture on every `motion` component that has this set of drag controls\n     * passed into it via the `dragControls` prop.\n     *\n     * ```jsx\n     * dragControls.start(e, {\n     *   snapToCursor: true\n     * })\n     * ```\n     *\n     * @param event - PointerEvent\n     * @param options - Options\n     *\n     * @public\n     */\n    start(event, options) {\n        this.componentControls.forEach((controls) => {\n            controls.start(event.nativeEvent || event, options);\n        });\n    }\n    /**\n     * Cancels a drag gesture.\n     *\n     * ```jsx\n     * dragControls.cancel()\n     * ```\n     *\n     * @public\n     */\n    cancel() {\n        this.componentControls.forEach((controls) => {\n            controls.cancel();\n        });\n    }\n    /**\n     * Stops a drag gesture.\n     *\n     * ```jsx\n     * dragControls.stop()\n     * ```\n     *\n     * @public\n     */\n    stop() {\n        this.componentControls.forEach((controls) => {\n            controls.stop();\n        });\n    }\n}\nconst createDragControls = () => new DragControls();\n/**\n * Usually, dragging is initiated by pressing down on a `motion` component with a `drag` prop\n * and moving it. For some use-cases, for instance clicking at an arbitrary point on a video scrubber, we\n * might want to initiate that dragging from a different component than the draggable one.\n *\n * By creating a `dragControls` using the `useDragControls` hook, we can pass this into\n * the draggable component's `dragControls` prop. It exposes a `start` method\n * that can start dragging from pointer events on other components.\n *\n * ```jsx\n * const dragControls = useDragControls()\n *\n * function startDrag(event) {\n *   dragControls.start(event, { snapToCursor: true })\n * }\n *\n * return (\n *   <>\n *     <div onPointerDown={startDrag} />\n *     <motion.div drag=\"x\" dragControls={dragControls} />\n *   </>\n * )\n * ```\n *\n * @public\n */\nfunction useDragControls() {\n    return (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_0__.useConstant)(createDragControls);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/use-drag-controls.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyConstraints: () => (/* binding */ applyConstraints),\n/* harmony export */   calcOrigin: () => (/* binding */ calcOrigin),\n/* harmony export */   calcRelativeAxisConstraints: () => (/* binding */ calcRelativeAxisConstraints),\n/* harmony export */   calcRelativeConstraints: () => (/* binding */ calcRelativeConstraints),\n/* harmony export */   calcViewportAxisConstraints: () => (/* binding */ calcViewportAxisConstraints),\n/* harmony export */   calcViewportConstraints: () => (/* binding */ calcViewportConstraints),\n/* harmony export */   defaultElastic: () => (/* binding */ defaultElastic),\n/* harmony export */   rebaseAxisConstraints: () => (/* binding */ rebaseAxisConstraints),\n/* harmony export */   resolveAxisElastic: () => (/* binding */ resolveAxisElastic),\n/* harmony export */   resolveDragElastic: () => (/* binding */ resolveDragElastic),\n/* harmony export */   resolvePointElastic: () => (/* binding */ resolvePointElastic)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../projection/geometry/delta-calc.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\");\n\n\n\n\n/**\n * Apply constraints to a point. These constraints are both physical along an\n * axis, and an elastic factor that determines how much to constrain the point\n * by if it does lie outside the defined parameters.\n */\nfunction applyConstraints(point, { min, max }, elastic) {\n    if (min !== undefined && point < min) {\n        // If we have a min point defined, and this is outside of that, constrain\n        point = elastic\n            ? (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(min, point, elastic.min)\n            : Math.max(point, min);\n    }\n    else if (max !== undefined && point > max) {\n        // If we have a max point defined, and this is outside of that, constrain\n        point = elastic\n            ? (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(max, point, elastic.max)\n            : Math.min(point, max);\n    }\n    return point;\n}\n/**\n * Calculate constraints in terms of the viewport when defined relatively to the\n * measured axis. This is measured from the nearest edge, so a max constraint of 200\n * on an axis with a max value of 300 would return a constraint of 500 - axis length\n */\nfunction calcRelativeAxisConstraints(axis, min, max) {\n    return {\n        min: min !== undefined ? axis.min + min : undefined,\n        max: max !== undefined\n            ? axis.max + max - (axis.max - axis.min)\n            : undefined,\n    };\n}\n/**\n * Calculate constraints in terms of the viewport when\n * defined relatively to the measured bounding box.\n */\nfunction calcRelativeConstraints(layoutBox, { top, left, bottom, right }) {\n    return {\n        x: calcRelativeAxisConstraints(layoutBox.x, left, right),\n        y: calcRelativeAxisConstraints(layoutBox.y, top, bottom),\n    };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative axis\n */\nfunction calcViewportAxisConstraints(layoutAxis, constraintsAxis) {\n    let min = constraintsAxis.min - layoutAxis.min;\n    let max = constraintsAxis.max - layoutAxis.max;\n    // If the constraints axis is actually smaller than the layout axis then we can\n    // flip the constraints\n    if (constraintsAxis.max - constraintsAxis.min <\n        layoutAxis.max - layoutAxis.min) {\n        [min, max] = [max, min];\n    }\n    return { min, max };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative box\n */\nfunction calcViewportConstraints(layoutBox, constraintsBox) {\n    return {\n        x: calcViewportAxisConstraints(layoutBox.x, constraintsBox.x),\n        y: calcViewportAxisConstraints(layoutBox.y, constraintsBox.y),\n    };\n}\n/**\n * Calculate a transform origin relative to the source axis, between 0-1, that results\n * in an asthetically pleasing scale/transform needed to project from source to target.\n */\nfunction calcOrigin(source, target) {\n    let origin = 0.5;\n    const sourceLength = (0,_projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_1__.calcLength)(source);\n    const targetLength = (0,_projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_1__.calcLength)(target);\n    if (targetLength > sourceLength) {\n        origin = (0,motion_utils__WEBPACK_IMPORTED_MODULE_2__.progress)(target.min, target.max - sourceLength, source.min);\n    }\n    else if (sourceLength > targetLength) {\n        origin = (0,motion_utils__WEBPACK_IMPORTED_MODULE_2__.progress)(source.min, source.max - targetLength, target.min);\n    }\n    return (0,motion_utils__WEBPACK_IMPORTED_MODULE_2__.clamp)(0, 1, origin);\n}\n/**\n * Rebase the calculated viewport constraints relative to the layout.min point.\n */\nfunction rebaseAxisConstraints(layout, constraints) {\n    const relativeConstraints = {};\n    if (constraints.min !== undefined) {\n        relativeConstraints.min = constraints.min - layout.min;\n    }\n    if (constraints.max !== undefined) {\n        relativeConstraints.max = constraints.max - layout.min;\n    }\n    return relativeConstraints;\n}\nconst defaultElastic = 0.35;\n/**\n * Accepts a dragElastic prop and returns resolved elastic values for each axis.\n */\nfunction resolveDragElastic(dragElastic = defaultElastic) {\n    if (dragElastic === false) {\n        dragElastic = 0;\n    }\n    else if (dragElastic === true) {\n        dragElastic = defaultElastic;\n    }\n    return {\n        x: resolveAxisElastic(dragElastic, \"left\", \"right\"),\n        y: resolveAxisElastic(dragElastic, \"top\", \"bottom\"),\n    };\n}\nfunction resolveAxisElastic(dragElastic, minLabel, maxLabel) {\n    return {\n        min: resolvePointElastic(dragElastic, minLabel),\n        max: resolvePointElastic(dragElastic, maxLabel),\n    };\n}\nfunction resolvePointElastic(dragElastic, label) {\n    return typeof dragElastic === \"number\"\n        ? dragElastic\n        : dragElastic[label] || 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/focus.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/focus.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGesture: () => (/* binding */ FocusGesture)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../events/add-dom-event.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../motion/features/Feature.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\n\n\nclass FocusGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor() {\n        super(...arguments);\n        this.isActive = false;\n    }\n    onFocus() {\n        let isFocusVisible = false;\n        /**\n         * If this element doesn't match focus-visible then don't\n         * apply whileHover. But, if matches throws that focus-visible\n         * is not a valid selector then in that browser outline styles will be applied\n         * to the element by default and we want to match that behaviour with whileFocus.\n         */\n        try {\n            isFocusVisible = this.node.current.matches(\":focus-visible\");\n        }\n        catch (e) {\n            isFocusVisible = true;\n        }\n        if (!isFocusVisible || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", true);\n        this.isActive = true;\n    }\n    onBlur() {\n        if (!this.isActive || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", false);\n        this.isActive = false;\n    }\n    mount() {\n        this.unmount = (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.pipe)((0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_2__.addDomEvent)(this.node.current, \"focus\", () => this.onFocus()), (0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_2__.addDomEvent)(this.node.current, \"blur\", () => this.onBlur()));\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/focus.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/hover.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/hover.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HoverGesture: () => (/* binding */ HoverGesture)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../events/event-info.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../motion/features/Feature.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\n\n\nfunction handleHoverEvent(node, event, lifecycle) {\n    const { props } = node;\n    if (node.animationState && props.whileHover) {\n        node.animationState.setActive(\"whileHover\", lifecycle === \"Start\");\n    }\n    const eventName = (\"onHover\" + lifecycle);\n    const callback = props[eventName];\n    if (callback) {\n        motion_dom__WEBPACK_IMPORTED_MODULE_0__.frame.postRender(() => callback(event, (0,_events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__.extractEventInfo)(event)));\n    }\n}\nclass HoverGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_2__.Feature {\n    mount() {\n        const { current } = this.node;\n        if (!current)\n            return;\n        this.unmount = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.hover)(current, (_element, startEvent) => {\n            handleHoverEvent(this.node, startEvent, \"Start\");\n            return (endEvent) => handleHoverEvent(this.node, endEvent, \"End\");\n        });\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/hover.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PanSession: () => (/* binding */ PanSession)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../events/add-pointer-event.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\");\n/* harmony import */ var _events_event_info_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../events/event-info.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\");\n/* harmony import */ var _utils_distance_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/distance.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/distance.mjs\");\n\n\n\n\n\n\n/**\n * @internal\n */\nclass PanSession {\n    constructor(event, handlers, { transformPagePoint, contextWindow = window, dragSnapToOrigin = false, distanceThreshold = 3, } = {}) {\n        /**\n         * @internal\n         */\n        this.startEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEventInfo = null;\n        /**\n         * @internal\n         */\n        this.handlers = {};\n        /**\n         * @internal\n         */\n        this.contextWindow = window;\n        this.updatePoint = () => {\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const info = getPanInfo(this.lastMoveEventInfo, this.history);\n            const isPanStarted = this.startEvent !== null;\n            // Only start panning if the offset is larger than 3 pixels. If we make it\n            // any larger than this we'll want to reset the pointer history\n            // on the first update to avoid visual snapping to the cursor.\n            const isDistancePastThreshold = (0,_utils_distance_mjs__WEBPACK_IMPORTED_MODULE_0__.distance2D)(info.offset, { x: 0, y: 0 }) >= this.distanceThreshold;\n            if (!isPanStarted && !isDistancePastThreshold)\n                return;\n            const { point } = info;\n            const { timestamp } = motion_dom__WEBPACK_IMPORTED_MODULE_1__.frameData;\n            this.history.push({ ...point, timestamp });\n            const { onStart, onMove } = this.handlers;\n            if (!isPanStarted) {\n                onStart && onStart(this.lastMoveEvent, info);\n                this.startEvent = this.lastMoveEvent;\n            }\n            onMove && onMove(this.lastMoveEvent, info);\n        };\n        this.handlePointerMove = (event, info) => {\n            this.lastMoveEvent = event;\n            this.lastMoveEventInfo = transformPoint(info, this.transformPagePoint);\n            // Throttle mouse move event to once per frame\n            motion_dom__WEBPACK_IMPORTED_MODULE_1__.frame.update(this.updatePoint, true);\n        };\n        this.handlePointerUp = (event, info) => {\n            this.end();\n            const { onEnd, onSessionEnd, resumeAnimation } = this.handlers;\n            if (this.dragSnapToOrigin)\n                resumeAnimation && resumeAnimation();\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const panInfo = getPanInfo(event.type === \"pointercancel\"\n                ? this.lastMoveEventInfo\n                : transformPoint(info, this.transformPagePoint), this.history);\n            if (this.startEvent && onEnd) {\n                onEnd(event, panInfo);\n            }\n            onSessionEnd && onSessionEnd(event, panInfo);\n        };\n        // If we have more than one touch, don't start detecting this gesture\n        if (!(0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.isPrimaryPointer)(event))\n            return;\n        this.dragSnapToOrigin = dragSnapToOrigin;\n        this.handlers = handlers;\n        this.transformPagePoint = transformPagePoint;\n        this.distanceThreshold = distanceThreshold;\n        this.contextWindow = contextWindow || window;\n        const info = (0,_events_event_info_mjs__WEBPACK_IMPORTED_MODULE_2__.extractEventInfo)(event);\n        const initialInfo = transformPoint(info, this.transformPagePoint);\n        const { point } = initialInfo;\n        const { timestamp } = motion_dom__WEBPACK_IMPORTED_MODULE_1__.frameData;\n        this.history = [{ ...point, timestamp }];\n        const { onSessionStart } = handlers;\n        onSessionStart &&\n            onSessionStart(event, getPanInfo(initialInfo, this.history));\n        this.removeListeners = (0,motion_utils__WEBPACK_IMPORTED_MODULE_3__.pipe)((0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_4__.addPointerEvent)(this.contextWindow, \"pointermove\", this.handlePointerMove), (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_4__.addPointerEvent)(this.contextWindow, \"pointerup\", this.handlePointerUp), (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_4__.addPointerEvent)(this.contextWindow, \"pointercancel\", this.handlePointerUp));\n    }\n    updateHandlers(handlers) {\n        this.handlers = handlers;\n    }\n    end() {\n        this.removeListeners && this.removeListeners();\n        (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.cancelFrame)(this.updatePoint);\n    }\n}\nfunction transformPoint(info, transformPagePoint) {\n    return transformPagePoint ? { point: transformPagePoint(info.point) } : info;\n}\nfunction subtractPoint(a, b) {\n    return { x: a.x - b.x, y: a.y - b.y };\n}\nfunction getPanInfo({ point }, history) {\n    return {\n        point,\n        delta: subtractPoint(point, lastDevicePoint(history)),\n        offset: subtractPoint(point, startDevicePoint(history)),\n        velocity: getVelocity(history, 0.1),\n    };\n}\nfunction startDevicePoint(history) {\n    return history[0];\n}\nfunction lastDevicePoint(history) {\n    return history[history.length - 1];\n}\nfunction getVelocity(history, timeDelta) {\n    if (history.length < 2) {\n        return { x: 0, y: 0 };\n    }\n    let i = history.length - 1;\n    let timestampedPoint = null;\n    const lastPoint = lastDevicePoint(history);\n    while (i >= 0) {\n        timestampedPoint = history[i];\n        if (lastPoint.timestamp - timestampedPoint.timestamp >\n            (0,motion_utils__WEBPACK_IMPORTED_MODULE_3__.secondsToMilliseconds)(timeDelta)) {\n            break;\n        }\n        i--;\n    }\n    if (!timestampedPoint) {\n        return { x: 0, y: 0 };\n    }\n    const time = (0,motion_utils__WEBPACK_IMPORTED_MODULE_3__.millisecondsToSeconds)(lastPoint.timestamp - timestampedPoint.timestamp);\n    if (time === 0) {\n        return { x: 0, y: 0 };\n    }\n    const currentVelocity = {\n        x: (lastPoint.x - timestampedPoint.x) / time,\n        y: (lastPoint.y - timestampedPoint.y) / time,\n    };\n    if (currentVelocity.x === Infinity) {\n        currentVelocity.x = 0;\n    }\n    if (currentVelocity.y === Infinity) {\n        currentVelocity.y = 0;\n    }\n    return currentVelocity;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/index.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/index.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PanGesture: () => (/* binding */ PanGesture)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../events/add-pointer-event.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../motion/features/Feature.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var _utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/get-context-window.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/get-context-window.mjs\");\n/* harmony import */ var _PanSession_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PanSession.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs\");\n\n\n\n\n\n\n\nconst asyncHandler = (handler) => (event, info) => {\n    if (handler) {\n        motion_dom__WEBPACK_IMPORTED_MODULE_0__.frame.postRender(() => handler(event, info));\n    }\n};\nclass PanGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_1__.Feature {\n    constructor() {\n        super(...arguments);\n        this.removePointerDownListener = motion_utils__WEBPACK_IMPORTED_MODULE_2__.noop;\n    }\n    onPointerDown(pointerDownEvent) {\n        this.session = new _PanSession_mjs__WEBPACK_IMPORTED_MODULE_3__.PanSession(pointerDownEvent, this.createPanHandlers(), {\n            transformPagePoint: this.node.getTransformPagePoint(),\n            contextWindow: (0,_utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_4__.getContextWindow)(this.node),\n        });\n    }\n    createPanHandlers() {\n        const { onPanSessionStart, onPanStart, onPan, onPanEnd } = this.node.getProps();\n        return {\n            onSessionStart: asyncHandler(onPanSessionStart),\n            onStart: asyncHandler(onPanStart),\n            onMove: onPan,\n            onEnd: (event, info) => {\n                delete this.session;\n                if (onPanEnd) {\n                    motion_dom__WEBPACK_IMPORTED_MODULE_0__.frame.postRender(() => onPanEnd(event, info));\n                }\n            },\n        };\n    }\n    mount() {\n        this.removePointerDownListener = (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__.addPointerEvent)(this.node.current, \"pointerdown\", (event) => this.onPointerDown(event));\n    }\n    update() {\n        this.session && this.session.updateHandlers(this.createPanHandlers());\n    }\n    unmount() {\n        this.removePointerDownListener();\n        this.session && this.session.end();\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/press.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/press.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PressGesture: () => (/* binding */ PressGesture)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../events/event-info.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../motion/features/Feature.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\n\n\nfunction handlePressEvent(node, event, lifecycle) {\n    const { props } = node;\n    if (node.current instanceof HTMLButtonElement && node.current.disabled) {\n        return;\n    }\n    if (node.animationState && props.whileTap) {\n        node.animationState.setActive(\"whileTap\", lifecycle === \"Start\");\n    }\n    const eventName = (\"onTap\" + (lifecycle === \"End\" ? \"\" : lifecycle));\n    const callback = props[eventName];\n    if (callback) {\n        motion_dom__WEBPACK_IMPORTED_MODULE_0__.frame.postRender(() => callback(event, (0,_events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__.extractEventInfo)(event)));\n    }\n}\nclass PressGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_2__.Feature {\n    mount() {\n        const { current } = this.node;\n        if (!current)\n            return;\n        this.unmount = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.press)(current, (_element, startEvent) => {\n            handlePressEvent(this.node, startEvent, \"Start\");\n            return (endEvent, { success }) => handlePressEvent(this.node, endEvent, success ? \"End\" : \"Cancel\");\n        }, { useGlobalTarget: this.node.props.globalTapTarget });\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/press.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addDomEvent: () => (/* binding */ addDomEvent)\n/* harmony export */ });\nfunction addDomEvent(target, eventName, handler, options = { passive: true }) {\n    target.addEventListener(eventName, handler, options);\n    return () => target.removeEventListener(eventName, handler);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2V2ZW50cy9hZGQtZG9tLWV2ZW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkRBQTZELGVBQWU7QUFDNUU7QUFDQTtBQUNBOztBQUV1QiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxldmVudHNcXGFkZC1kb20tZXZlbnQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGFkZERvbUV2ZW50KHRhcmdldCwgZXZlbnROYW1lLCBoYW5kbGVyLCBvcHRpb25zID0geyBwYXNzaXZlOiB0cnVlIH0pIHtcbiAgICB0YXJnZXQuYWRkRXZlbnRMaXN0ZW5lcihldmVudE5hbWUsIGhhbmRsZXIsIG9wdGlvbnMpO1xuICAgIHJldHVybiAoKSA9PiB0YXJnZXQucmVtb3ZlRXZlbnRMaXN0ZW5lcihldmVudE5hbWUsIGhhbmRsZXIpO1xufVxuXG5leHBvcnQgeyBhZGREb21FdmVudCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPointerEvent: () => (/* binding */ addPointerEvent)\n/* harmony export */ });\n/* harmony import */ var _add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./add-dom-event.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n/* harmony import */ var _event_info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./event-info.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\");\n\n\n\nfunction addPointerEvent(target, eventName, handler, options) {\n    return (0,_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_0__.addDomEvent)(target, eventName, (0,_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__.addPointerInfo)(handler), options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2V2ZW50cy9hZGQtcG9pbnRlci1ldmVudC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtEO0FBQ0E7O0FBRWxEO0FBQ0EsV0FBVywrREFBVyxvQkFBb0IsK0RBQWM7QUFDeEQ7O0FBRTJCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXGV2ZW50c1xcYWRkLXBvaW50ZXItZXZlbnQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFkZERvbUV2ZW50IH0gZnJvbSAnLi9hZGQtZG9tLWV2ZW50Lm1qcyc7XG5pbXBvcnQgeyBhZGRQb2ludGVySW5mbyB9IGZyb20gJy4vZXZlbnQtaW5mby5tanMnO1xuXG5mdW5jdGlvbiBhZGRQb2ludGVyRXZlbnQodGFyZ2V0LCBldmVudE5hbWUsIGhhbmRsZXIsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gYWRkRG9tRXZlbnQodGFyZ2V0LCBldmVudE5hbWUsIGFkZFBvaW50ZXJJbmZvKGhhbmRsZXIpLCBvcHRpb25zKTtcbn1cblxuZXhwb3J0IHsgYWRkUG9pbnRlckV2ZW50IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs":
/*!******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPointerInfo: () => (/* binding */ addPointerInfo),\n/* harmony export */   extractEventInfo: () => (/* binding */ extractEventInfo)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n\n\nfunction extractEventInfo(event) {\n    return {\n        point: {\n            x: event.pageX,\n            y: event.pageY,\n        },\n    };\n}\nconst addPointerInfo = (handler) => {\n    return (event) => (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isPrimaryPointer)(event) && handler(event, extractEventInfo(event));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2V2ZW50cy9ldmVudC1pbmZvLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7O0FBRTlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDREQUFnQjtBQUN0Qzs7QUFFNEMiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcZXZlbnRzXFxldmVudC1pbmZvLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc1ByaW1hcnlQb2ludGVyIH0gZnJvbSAnbW90aW9uLWRvbSc7XG5cbmZ1bmN0aW9uIGV4dHJhY3RFdmVudEluZm8oZXZlbnQpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBwb2ludDoge1xuICAgICAgICAgICAgeDogZXZlbnQucGFnZVgsXG4gICAgICAgICAgICB5OiBldmVudC5wYWdlWSxcbiAgICAgICAgfSxcbiAgICB9O1xufVxuY29uc3QgYWRkUG9pbnRlckluZm8gPSAoaGFuZGxlcikgPT4ge1xuICAgIHJldHVybiAoZXZlbnQpID0+IGlzUHJpbWFyeVBvaW50ZXIoZXZlbnQpICYmIGhhbmRsZXIoZXZlbnQsIGV4dHJhY3RFdmVudEluZm8oZXZlbnQpKTtcbn07XG5cbmV4cG9ydCB7IGFkZFBvaW50ZXJJbmZvLCBleHRyYWN0RXZlbnRJbmZvIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/use-dom-event.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/use-dom-event.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDomEvent: () => (/* binding */ useDomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./add-dom-event.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n\n\n\n/**\n * Attaches an event listener directly to the provided DOM element.\n *\n * Bypassing React's event system can be desirable, for instance when attaching non-passive\n * event handlers.\n *\n * ```jsx\n * const ref = useRef(null)\n *\n * useDomEvent(ref, 'wheel', onWheel, { passive: false })\n *\n * return <div ref={ref} />\n * ```\n *\n * @param ref - React.RefObject that's been provided to the element you want to bind the listener to.\n * @param eventName - Name of the event you want listen for.\n * @param handler - Function to fire when receiving the event.\n * @param options - Options to pass to `Event.addEventListener`.\n *\n * @public\n */\nfunction useDomEvent(ref, eventName, handler, options) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        const element = ref.current;\n        if (handler && element) {\n            return (0,_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_1__.addDomEvent)(element, eventName, handler, options);\n        }\n    }, [ref, eventName, handler, options]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/use-dom-event.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs":
/*!****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VisualElementDragControls: () => (/* binding */ VisualElementDragControls),\n/* harmony export */   elementDragControls: () => (/* binding */ elementDragControls)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! motion-utils */ \"../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _animation_interfaces_motion_value_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../animation/interfaces/motion-value.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs\");\n/* harmony import */ var _events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../events/add-dom-event.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n/* harmony import */ var _events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../events/add-pointer-event.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\");\n/* harmony import */ var _events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../events/event-info.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\");\n/* harmony import */ var _projection_geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../projection/geometry/conversion.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs\");\n/* harmony import */ var _projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../projection/geometry/delta-calc.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\");\n/* harmony import */ var _projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../projection/geometry/models.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/models.mjs\");\n/* harmony import */ var _projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../projection/utils/each-axis.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs\");\n/* harmony import */ var _projection_utils_measure_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../projection/utils/measure.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/utils/measure.mjs\");\n/* harmony import */ var _utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/get-context-window.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/get-context-window.mjs\");\n/* harmony import */ var _utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../utils/is-ref-object.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/is-ref-object.mjs\");\n/* harmony import */ var _value_use_will_change_add_will_change_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../value/use-will-change/add-will-change.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/value/use-will-change/add-will-change.mjs\");\n/* harmony import */ var _pan_PanSession_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../pan/PanSession.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs\");\n/* harmony import */ var _utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/constraints.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst elementDragControls = new WeakMap();\nclass VisualElementDragControls {\n    constructor(visualElement) {\n        this.openDragLock = null;\n        this.isDragging = false;\n        this.currentDirection = null;\n        this.originPoint = { x: 0, y: 0 };\n        /**\n         * The permitted boundaries of travel, in pixels.\n         */\n        this.constraints = false;\n        this.hasMutatedConstraints = false;\n        /**\n         * The per-axis resolved elastic values.\n         */\n        this.elastic = (0,_projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_0__.createBox)();\n        /**\n         * The latest pointer event. Used as fallback when the `cancel` and `stop` functions are called without arguments.\n         */\n        this.latestPointerEvent = null;\n        /**\n         * The latest pan info. Used as fallback when the `cancel` and `stop` functions are called without arguments.\n         */\n        this.latestPanInfo = null;\n        this.visualElement = visualElement;\n    }\n    start(originEvent, { snapToCursor = false, distanceThreshold } = {}) {\n        /**\n         * Don't start dragging if this component is exiting\n         */\n        const { presenceContext } = this.visualElement;\n        if (presenceContext && presenceContext.isPresent === false)\n            return;\n        const onSessionStart = (event) => {\n            const { dragSnapToOrigin } = this.getProps();\n            // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n            // the component.\n            dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n            if (snapToCursor) {\n                this.snapToCursor((0,_events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__.extractEventInfo)(event).point);\n            }\n        };\n        const onStart = (event, info) => {\n            // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n            const { drag, dragPropagation, onDragStart } = this.getProps();\n            if (drag && !dragPropagation) {\n                if (this.openDragLock)\n                    this.openDragLock();\n                this.openDragLock = (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.setDragLock)(drag);\n                // If we don 't have the lock, don't start dragging\n                if (!this.openDragLock)\n                    return;\n            }\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            this.isDragging = true;\n            this.currentDirection = null;\n            this.resolveConstraints();\n            if (this.visualElement.projection) {\n                this.visualElement.projection.isAnimationBlocked = true;\n                this.visualElement.projection.target = undefined;\n            }\n            /**\n             * Record gesture origin\n             */\n            (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n                let current = this.getAxisMotionValue(axis).get() || 0;\n                /**\n                 * If the MotionValue is a percentage value convert to px\n                 */\n                if (motion_dom__WEBPACK_IMPORTED_MODULE_2__.percent.test(current)) {\n                    const { projection } = this.visualElement;\n                    if (projection && projection.layout) {\n                        const measuredAxis = projection.layout.layoutBox[axis];\n                        if (measuredAxis) {\n                            const length = (0,_projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_4__.calcLength)(measuredAxis);\n                            current = length * (parseFloat(current) / 100);\n                        }\n                    }\n                }\n                this.originPoint[axis] = current;\n            });\n            // Fire onDragStart event\n            if (onDragStart) {\n                motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.postRender(() => onDragStart(event, info));\n            }\n            (0,_value_use_will_change_add_will_change_mjs__WEBPACK_IMPORTED_MODULE_5__.addValueToWillChange)(this.visualElement, \"transform\");\n            const { animationState } = this.visualElement;\n            animationState && animationState.setActive(\"whileDrag\", true);\n        };\n        const onMove = (event, info) => {\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            const { dragPropagation, dragDirectionLock, onDirectionLock, onDrag, } = this.getProps();\n            // If we didn't successfully receive the gesture lock, early return.\n            if (!dragPropagation && !this.openDragLock)\n                return;\n            const { offset } = info;\n            // Attempt to detect drag direction if directionLock is true\n            if (dragDirectionLock && this.currentDirection === null) {\n                this.currentDirection = getCurrentDirection(offset);\n                // If we've successfully set a direction, notify listener\n                if (this.currentDirection !== null) {\n                    onDirectionLock && onDirectionLock(this.currentDirection);\n                }\n                return;\n            }\n            // Update each point with the latest position\n            this.updateAxis(\"x\", info.point, offset);\n            this.updateAxis(\"y\", info.point, offset);\n            /**\n             * Ideally we would leave the renderer to fire naturally at the end of\n             * this frame but if the element is about to change layout as the result\n             * of a re-render we want to ensure the browser can read the latest\n             * bounding box to ensure the pointer and element don't fall out of sync.\n             */\n            this.visualElement.render();\n            /**\n             * This must fire after the render call as it might trigger a state\n             * change which itself might trigger a layout update.\n             */\n            onDrag && onDrag(event, info);\n        };\n        const onSessionEnd = (event, info) => {\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            this.stop(event, info);\n            this.latestPointerEvent = null;\n            this.latestPanInfo = null;\n        };\n        const resumeAnimation = () => (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => this.getAnimationState(axis) === \"paused\" &&\n            this.getAxisMotionValue(axis).animation?.play());\n        const { dragSnapToOrigin } = this.getProps();\n        this.panSession = new _pan_PanSession_mjs__WEBPACK_IMPORTED_MODULE_6__.PanSession(originEvent, {\n            onSessionStart,\n            onStart,\n            onMove,\n            onSessionEnd,\n            resumeAnimation,\n        }, {\n            transformPagePoint: this.visualElement.getTransformPagePoint(),\n            dragSnapToOrigin,\n            distanceThreshold,\n            contextWindow: (0,_utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_7__.getContextWindow)(this.visualElement),\n        });\n    }\n    /**\n     * @internal\n     */\n    stop(event, panInfo) {\n        const finalEvent = event || this.latestPointerEvent;\n        const finalPanInfo = panInfo || this.latestPanInfo;\n        const isDragging = this.isDragging;\n        this.cancel();\n        if (!isDragging || !finalPanInfo || !finalEvent)\n            return;\n        const { velocity } = finalPanInfo;\n        this.startAnimation(velocity);\n        const { onDragEnd } = this.getProps();\n        if (onDragEnd) {\n            motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.postRender(() => onDragEnd(finalEvent, finalPanInfo));\n        }\n    }\n    /**\n     * @internal\n     */\n    cancel() {\n        this.isDragging = false;\n        const { projection, animationState } = this.visualElement;\n        if (projection) {\n            projection.isAnimationBlocked = false;\n        }\n        this.panSession && this.panSession.end();\n        this.panSession = undefined;\n        const { dragPropagation } = this.getProps();\n        if (!dragPropagation && this.openDragLock) {\n            this.openDragLock();\n            this.openDragLock = null;\n        }\n        animationState && animationState.setActive(\"whileDrag\", false);\n    }\n    updateAxis(axis, _point, offset) {\n        const { drag } = this.getProps();\n        // If we're not dragging this axis, do an early return.\n        if (!offset || !shouldDrag(axis, drag, this.currentDirection))\n            return;\n        const axisValue = this.getAxisMotionValue(axis);\n        let next = this.originPoint[axis] + offset[axis];\n        // Apply constraints\n        if (this.constraints && this.constraints[axis]) {\n            next = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.applyConstraints)(next, this.constraints[axis], this.elastic[axis]);\n        }\n        axisValue.set(next);\n    }\n    resolveConstraints() {\n        const { dragConstraints, dragElastic } = this.getProps();\n        const layout = this.visualElement.projection &&\n            !this.visualElement.projection.layout\n            ? this.visualElement.projection.measure(false)\n            : this.visualElement.projection?.layout;\n        const prevConstraints = this.constraints;\n        if (dragConstraints && (0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__.isRefObject)(dragConstraints)) {\n            if (!this.constraints) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        }\n        else {\n            if (dragConstraints && layout) {\n                this.constraints = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.calcRelativeConstraints)(layout.layoutBox, dragConstraints);\n            }\n            else {\n                this.constraints = false;\n            }\n        }\n        this.elastic = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.resolveDragElastic)(dragElastic);\n        /**\n         * If we're outputting to external MotionValues, we want to rebase the measured constraints\n         * from viewport-relative to component-relative.\n         */\n        if (prevConstraints !== this.constraints &&\n            layout &&\n            this.constraints &&\n            !this.hasMutatedConstraints) {\n            (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n                if (this.constraints !== false &&\n                    this.getAxisMotionValue(axis)) {\n                    this.constraints[axis] = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.rebaseAxisConstraints)(layout.layoutBox[axis], this.constraints[axis]);\n                }\n            });\n        }\n    }\n    resolveRefConstraints() {\n        const { dragConstraints: constraints, onMeasureDragConstraints } = this.getProps();\n        if (!constraints || !(0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__.isRefObject)(constraints))\n            return false;\n        const constraintsElement = constraints.current;\n        (0,motion_utils__WEBPACK_IMPORTED_MODULE_10__.invariant)(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n        const { projection } = this.visualElement;\n        // TODO\n        if (!projection || !projection.layout)\n            return false;\n        const constraintsBox = (0,_projection_utils_measure_mjs__WEBPACK_IMPORTED_MODULE_11__.measurePageBox)(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n        let measuredConstraints = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.calcViewportConstraints)(projection.layout.layoutBox, constraintsBox);\n        /**\n         * If there's an onMeasureDragConstraints listener we call it and\n         * if different constraints are returned, set constraints to that\n         */\n        if (onMeasureDragConstraints) {\n            const userConstraints = onMeasureDragConstraints((0,_projection_geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_12__.convertBoxToBoundingBox)(measuredConstraints));\n            this.hasMutatedConstraints = !!userConstraints;\n            if (userConstraints) {\n                measuredConstraints = (0,_projection_geometry_conversion_mjs__WEBPACK_IMPORTED_MODULE_12__.convertBoundingBoxToBox)(userConstraints);\n            }\n        }\n        return measuredConstraints;\n    }\n    startAnimation(velocity) {\n        const { drag, dragMomentum, dragElastic, dragTransition, dragSnapToOrigin, onDragTransitionEnd, } = this.getProps();\n        const constraints = this.constraints || {};\n        const momentumAnimations = (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            if (!shouldDrag(axis, drag, this.currentDirection)) {\n                return;\n            }\n            let transition = (constraints && constraints[axis]) || {};\n            if (dragSnapToOrigin)\n                transition = { min: 0, max: 0 };\n            /**\n             * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n             * of spring animations so we should look into adding a disable spring option to `inertia`.\n             * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n             * using the value of `dragElastic`.\n             */\n            const bounceStiffness = dragElastic ? 200 : 1000000;\n            const bounceDamping = dragElastic ? 40 : 10000000;\n            const inertia = {\n                type: \"inertia\",\n                velocity: dragMomentum ? velocity[axis] : 0,\n                bounceStiffness,\n                bounceDamping,\n                timeConstant: 750,\n                restDelta: 1,\n                restSpeed: 10,\n                ...dragTransition,\n                ...transition,\n            };\n            // If we're not animating on an externally-provided `MotionValue` we can use the\n            // component's animation controls which will handle interactions with whileHover (etc),\n            // otherwise we just have to animate the `MotionValue` itself.\n            return this.startAxisValueAnimation(axis, inertia);\n        });\n        // Run all animations and then resolve the new drag constraints.\n        return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n    }\n    startAxisValueAnimation(axis, transition) {\n        const axisValue = this.getAxisMotionValue(axis);\n        (0,_value_use_will_change_add_will_change_mjs__WEBPACK_IMPORTED_MODULE_5__.addValueToWillChange)(this.visualElement, axis);\n        return axisValue.start((0,_animation_interfaces_motion_value_mjs__WEBPACK_IMPORTED_MODULE_13__.animateMotionValue)(axis, axisValue, 0, transition, this.visualElement, false));\n    }\n    stopAnimation() {\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => this.getAxisMotionValue(axis).stop());\n    }\n    pauseAnimation() {\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => this.getAxisMotionValue(axis).animation?.pause());\n    }\n    getAnimationState(axis) {\n        return this.getAxisMotionValue(axis).animation?.state;\n    }\n    /**\n     * Drag works differently depending on which props are provided.\n     *\n     * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n     * - Otherwise, we apply the delta to the x/y motion values.\n     */\n    getAxisMotionValue(axis) {\n        const dragKey = `_drag${axis.toUpperCase()}`;\n        const props = this.visualElement.getProps();\n        const externalMotionValue = props[dragKey];\n        return externalMotionValue\n            ? externalMotionValue\n            : this.visualElement.getValue(axis, (props.initial\n                ? props.initial[axis]\n                : undefined) || 0);\n    }\n    snapToCursor(point) {\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            const { drag } = this.getProps();\n            // If we're not dragging this axis, do an early return.\n            if (!shouldDrag(axis, drag, this.currentDirection))\n                return;\n            const { projection } = this.visualElement;\n            const axisValue = this.getAxisMotionValue(axis);\n            if (projection && projection.layout) {\n                const { min, max } = projection.layout.layoutBox[axis];\n                axisValue.set(point[axis] - (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.mixNumber)(min, max, 0.5));\n            }\n        });\n    }\n    /**\n     * When the viewport resizes we want to check if the measured constraints\n     * have changed and, if so, reposition the element within those new constraints\n     * relative to where it was before the resize.\n     */\n    scalePositionWithinConstraints() {\n        if (!this.visualElement.current)\n            return;\n        const { drag, dragConstraints } = this.getProps();\n        const { projection } = this.visualElement;\n        if (!(0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__.isRefObject)(dragConstraints) || !projection || !this.constraints)\n            return;\n        /**\n         * Stop current animations as there can be visual glitching if we try to do\n         * this mid-animation\n         */\n        this.stopAnimation();\n        /**\n         * Record the relative position of the dragged element relative to the\n         * constraints box and save as a progress value.\n         */\n        const boxProgress = { x: 0, y: 0 };\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            const axisValue = this.getAxisMotionValue(axis);\n            if (axisValue && this.constraints !== false) {\n                const latest = axisValue.get();\n                boxProgress[axis] = (0,_utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.calcOrigin)({ min: latest, max: latest }, this.constraints[axis]);\n            }\n        });\n        /**\n         * Update the layout of this element and resolve the latest drag constraints\n         */\n        const { transformTemplate } = this.visualElement.getProps();\n        this.visualElement.current.style.transform = transformTemplate\n            ? transformTemplate({}, \"\")\n            : \"none\";\n        projection.root && projection.root.updateScroll();\n        projection.updateLayout();\n        this.resolveConstraints();\n        /**\n         * For each axis, calculate the current progress of the layout axis\n         * within the new constraints.\n         */\n        (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n            if (!shouldDrag(axis, drag, null))\n                return;\n            /**\n             * Calculate a new transform based on the previous box progress\n             */\n            const axisValue = this.getAxisMotionValue(axis);\n            const { min, max } = this.constraints[axis];\n            axisValue.set((0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.mixNumber)(min, max, boxProgress[axis]));\n        });\n    }\n    addListeners() {\n        if (!this.visualElement.current)\n            return;\n        elementDragControls.set(this.visualElement, this);\n        const element = this.visualElement.current;\n        /**\n         * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n         */\n        const stopPointerListener = (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_14__.addPointerEvent)(element, \"pointerdown\", (event) => {\n            const { drag, dragListener = true } = this.getProps();\n            drag && dragListener && this.start(event);\n        });\n        const measureDragConstraints = () => {\n            const { dragConstraints } = this.getProps();\n            if ((0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__.isRefObject)(dragConstraints) && dragConstraints.current) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        };\n        const { projection } = this.visualElement;\n        const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n        if (projection && !projection.layout) {\n            projection.root && projection.root.updateScroll();\n            projection.updateLayout();\n        }\n        motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.read(measureDragConstraints);\n        /**\n         * Attach a window resize listener to scale the draggable target within its defined\n         * constraints as the window resizes.\n         */\n        const stopResizeListener = (0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_15__.addDomEvent)(window, \"resize\", () => this.scalePositionWithinConstraints());\n        /**\n         * If the element's layout changes, calculate the delta and apply that to\n         * the drag gesture's origin point.\n         */\n        const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", (({ delta, hasLayoutChanged }) => {\n            if (this.isDragging && hasLayoutChanged) {\n                (0,_projection_utils_each_axis_mjs__WEBPACK_IMPORTED_MODULE_3__.eachAxis)((axis) => {\n                    const motionValue = this.getAxisMotionValue(axis);\n                    if (!motionValue)\n                        return;\n                    this.originPoint[axis] += delta[axis].translate;\n                    motionValue.set(motionValue.get() + delta[axis].translate);\n                });\n                this.visualElement.render();\n            }\n        }));\n        return () => {\n            stopResizeListener();\n            stopPointerListener();\n            stopMeasureLayoutListener();\n            stopLayoutUpdateListener && stopLayoutUpdateListener();\n        };\n    }\n    getProps() {\n        const props = this.visualElement.getProps();\n        const { drag = false, dragDirectionLock = false, dragPropagation = false, dragConstraints = false, dragElastic = _utils_constraints_mjs__WEBPACK_IMPORTED_MODULE_8__.defaultElastic, dragMomentum = true, } = props;\n        return {\n            ...props,\n            drag,\n            dragDirectionLock,\n            dragPropagation,\n            dragConstraints,\n            dragElastic,\n            dragMomentum,\n        };\n    }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n    return ((drag === true || drag === direction) &&\n        (currentDirection === null || currentDirection === direction));\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n    let direction = null;\n    if (Math.abs(offset.y) > lockThreshold) {\n        direction = \"y\";\n    }\n    else if (Math.abs(offset.x) > lockThreshold) {\n        direction = \"x\";\n    }\n    return direction;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/index.mjs":
/*!********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/index.mjs ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragGesture: () => (/* binding */ DragGesture)\n/* harmony export */ });\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../motion/features/Feature.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _VisualElementDragControls_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./VisualElementDragControls.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs\");\n\n\n\n\nclass DragGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor(node) {\n        super(node);\n        this.removeGroupControls = motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop;\n        this.removeListeners = motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop;\n        this.controls = new _VisualElementDragControls_mjs__WEBPACK_IMPORTED_MODULE_2__.VisualElementDragControls(node);\n    }\n    mount() {\n        // If we've been provided a DragControls for manual control over the drag gesture,\n        // subscribe this component to it on mount.\n        const { dragControls } = this.node.getProps();\n        if (dragControls) {\n            this.removeGroupControls = dragControls.subscribe(this.controls);\n        }\n        this.removeListeners = this.controls.addListeners() || motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop;\n    }\n    unmount() {\n        this.removeGroupControls();\n        this.removeListeners();\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/index.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/use-drag-controls.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/use-drag-controls.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragControls: () => (/* binding */ DragControls),\n/* harmony export */   useDragControls: () => (/* binding */ useDragControls)\n/* harmony export */ });\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n\n\n/**\n * Can manually trigger a drag gesture on one or more `drag`-enabled `motion` components.\n *\n * ```jsx\n * const dragControls = useDragControls()\n *\n * function startDrag(event) {\n *   dragControls.start(event, { snapToCursor: true })\n * }\n *\n * return (\n *   <>\n *     <div onPointerDown={startDrag} />\n *     <motion.div drag=\"x\" dragControls={dragControls} />\n *   </>\n * )\n * ```\n *\n * @public\n */\nclass DragControls {\n    constructor() {\n        this.componentControls = new Set();\n    }\n    /**\n     * Subscribe a component's internal `VisualElementDragControls` to the user-facing API.\n     *\n     * @internal\n     */\n    subscribe(controls) {\n        this.componentControls.add(controls);\n        return () => this.componentControls.delete(controls);\n    }\n    /**\n     * Start a drag gesture on every `motion` component that has this set of drag controls\n     * passed into it via the `dragControls` prop.\n     *\n     * ```jsx\n     * dragControls.start(e, {\n     *   snapToCursor: true\n     * })\n     * ```\n     *\n     * @param event - PointerEvent\n     * @param options - Options\n     *\n     * @public\n     */\n    start(event, options) {\n        this.componentControls.forEach((controls) => {\n            controls.start(event.nativeEvent || event, options);\n        });\n    }\n    /**\n     * Cancels a drag gesture.\n     *\n     * ```jsx\n     * dragControls.cancel()\n     * ```\n     *\n     * @public\n     */\n    cancel() {\n        this.componentControls.forEach((controls) => {\n            controls.cancel();\n        });\n    }\n    /**\n     * Stops a drag gesture.\n     *\n     * ```jsx\n     * dragControls.stop()\n     * ```\n     *\n     * @public\n     */\n    stop() {\n        this.componentControls.forEach((controls) => {\n            controls.stop();\n        });\n    }\n}\nconst createDragControls = () => new DragControls();\n/**\n * Usually, dragging is initiated by pressing down on a `motion` component with a `drag` prop\n * and moving it. For some use-cases, for instance clicking at an arbitrary point on a video scrubber, we\n * might want to initiate that dragging from a different component than the draggable one.\n *\n * By creating a `dragControls` using the `useDragControls` hook, we can pass this into\n * the draggable component's `dragControls` prop. It exposes a `start` method\n * that can start dragging from pointer events on other components.\n *\n * ```jsx\n * const dragControls = useDragControls()\n *\n * function startDrag(event) {\n *   dragControls.start(event, { snapToCursor: true })\n * }\n *\n * return (\n *   <>\n *     <div onPointerDown={startDrag} />\n *     <motion.div drag=\"x\" dragControls={dragControls} />\n *   </>\n * )\n * ```\n *\n * @public\n */\nfunction useDragControls() {\n    return (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_0__.useConstant)(createDragControls);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/use-drag-controls.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyConstraints: () => (/* binding */ applyConstraints),\n/* harmony export */   calcOrigin: () => (/* binding */ calcOrigin),\n/* harmony export */   calcRelativeAxisConstraints: () => (/* binding */ calcRelativeAxisConstraints),\n/* harmony export */   calcRelativeConstraints: () => (/* binding */ calcRelativeConstraints),\n/* harmony export */   calcViewportAxisConstraints: () => (/* binding */ calcViewportAxisConstraints),\n/* harmony export */   calcViewportConstraints: () => (/* binding */ calcViewportConstraints),\n/* harmony export */   defaultElastic: () => (/* binding */ defaultElastic),\n/* harmony export */   rebaseAxisConstraints: () => (/* binding */ rebaseAxisConstraints),\n/* harmony export */   resolveAxisElastic: () => (/* binding */ resolveAxisElastic),\n/* harmony export */   resolveDragElastic: () => (/* binding */ resolveDragElastic),\n/* harmony export */   resolvePointElastic: () => (/* binding */ resolvePointElastic)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-utils */ \"../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../projection/geometry/delta-calc.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\");\n\n\n\n\n/**\n * Apply constraints to a point. These constraints are both physical along an\n * axis, and an elastic factor that determines how much to constrain the point\n * by if it does lie outside the defined parameters.\n */\nfunction applyConstraints(point, { min, max }, elastic) {\n    if (min !== undefined && point < min) {\n        // If we have a min point defined, and this is outside of that, constrain\n        point = elastic\n            ? (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(min, point, elastic.min)\n            : Math.max(point, min);\n    }\n    else if (max !== undefined && point > max) {\n        // If we have a max point defined, and this is outside of that, constrain\n        point = elastic\n            ? (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(max, point, elastic.max)\n            : Math.min(point, max);\n    }\n    return point;\n}\n/**\n * Calculate constraints in terms of the viewport when defined relatively to the\n * measured axis. This is measured from the nearest edge, so a max constraint of 200\n * on an axis with a max value of 300 would return a constraint of 500 - axis length\n */\nfunction calcRelativeAxisConstraints(axis, min, max) {\n    return {\n        min: min !== undefined ? axis.min + min : undefined,\n        max: max !== undefined\n            ? axis.max + max - (axis.max - axis.min)\n            : undefined,\n    };\n}\n/**\n * Calculate constraints in terms of the viewport when\n * defined relatively to the measured bounding box.\n */\nfunction calcRelativeConstraints(layoutBox, { top, left, bottom, right }) {\n    return {\n        x: calcRelativeAxisConstraints(layoutBox.x, left, right),\n        y: calcRelativeAxisConstraints(layoutBox.y, top, bottom),\n    };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative axis\n */\nfunction calcViewportAxisConstraints(layoutAxis, constraintsAxis) {\n    let min = constraintsAxis.min - layoutAxis.min;\n    let max = constraintsAxis.max - layoutAxis.max;\n    // If the constraints axis is actually smaller than the layout axis then we can\n    // flip the constraints\n    if (constraintsAxis.max - constraintsAxis.min <\n        layoutAxis.max - layoutAxis.min) {\n        [min, max] = [max, min];\n    }\n    return { min, max };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative box\n */\nfunction calcViewportConstraints(layoutBox, constraintsBox) {\n    return {\n        x: calcViewportAxisConstraints(layoutBox.x, constraintsBox.x),\n        y: calcViewportAxisConstraints(layoutBox.y, constraintsBox.y),\n    };\n}\n/**\n * Calculate a transform origin relative to the source axis, between 0-1, that results\n * in an asthetically pleasing scale/transform needed to project from source to target.\n */\nfunction calcOrigin(source, target) {\n    let origin = 0.5;\n    const sourceLength = (0,_projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_1__.calcLength)(source);\n    const targetLength = (0,_projection_geometry_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_1__.calcLength)(target);\n    if (targetLength > sourceLength) {\n        origin = (0,motion_utils__WEBPACK_IMPORTED_MODULE_2__.progress)(target.min, target.max - sourceLength, source.min);\n    }\n    else if (sourceLength > targetLength) {\n        origin = (0,motion_utils__WEBPACK_IMPORTED_MODULE_2__.progress)(source.min, source.max - targetLength, target.min);\n    }\n    return (0,motion_utils__WEBPACK_IMPORTED_MODULE_2__.clamp)(0, 1, origin);\n}\n/**\n * Rebase the calculated viewport constraints relative to the layout.min point.\n */\nfunction rebaseAxisConstraints(layout, constraints) {\n    const relativeConstraints = {};\n    if (constraints.min !== undefined) {\n        relativeConstraints.min = constraints.min - layout.min;\n    }\n    if (constraints.max !== undefined) {\n        relativeConstraints.max = constraints.max - layout.min;\n    }\n    return relativeConstraints;\n}\nconst defaultElastic = 0.35;\n/**\n * Accepts a dragElastic prop and returns resolved elastic values for each axis.\n */\nfunction resolveDragElastic(dragElastic = defaultElastic) {\n    if (dragElastic === false) {\n        dragElastic = 0;\n    }\n    else if (dragElastic === true) {\n        dragElastic = defaultElastic;\n    }\n    return {\n        x: resolveAxisElastic(dragElastic, \"left\", \"right\"),\n        y: resolveAxisElastic(dragElastic, \"top\", \"bottom\"),\n    };\n}\nfunction resolveAxisElastic(dragElastic, minLabel, maxLabel) {\n    return {\n        min: resolvePointElastic(dragElastic, minLabel),\n        max: resolvePointElastic(dragElastic, maxLabel),\n    };\n}\nfunction resolvePointElastic(dragElastic, label) {\n    return typeof dragElastic === \"number\"\n        ? dragElastic\n        : dragElastic[label] || 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/focus.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/focus.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGesture: () => (/* binding */ FocusGesture)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../events/add-dom-event.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-dom-event.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../motion/features/Feature.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\n\n\nclass FocusGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor() {\n        super(...arguments);\n        this.isActive = false;\n    }\n    onFocus() {\n        let isFocusVisible = false;\n        /**\n         * If this element doesn't match focus-visible then don't\n         * apply whileHover. But, if matches throws that focus-visible\n         * is not a valid selector then in that browser outline styles will be applied\n         * to the element by default and we want to match that behaviour with whileFocus.\n         */\n        try {\n            isFocusVisible = this.node.current.matches(\":focus-visible\");\n        }\n        catch (e) {\n            isFocusVisible = true;\n        }\n        if (!isFocusVisible || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", true);\n        this.isActive = true;\n    }\n    onBlur() {\n        if (!this.isActive || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", false);\n        this.isActive = false;\n    }\n    mount() {\n        this.unmount = (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.pipe)((0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_2__.addDomEvent)(this.node.current, \"focus\", () => this.onFocus()), (0,_events_add_dom_event_mjs__WEBPACK_IMPORTED_MODULE_2__.addDomEvent)(this.node.current, \"blur\", () => this.onBlur()));\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/focus.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/hover.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/hover.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HoverGesture: () => (/* binding */ HoverGesture)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../events/event-info.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../motion/features/Feature.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\n\n\nfunction handleHoverEvent(node, event, lifecycle) {\n    const { props } = node;\n    if (node.animationState && props.whileHover) {\n        node.animationState.setActive(\"whileHover\", lifecycle === \"Start\");\n    }\n    const eventName = (\"onHover\" + lifecycle);\n    const callback = props[eventName];\n    if (callback) {\n        motion_dom__WEBPACK_IMPORTED_MODULE_0__.frame.postRender(() => callback(event, (0,_events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__.extractEventInfo)(event)));\n    }\n}\nclass HoverGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_2__.Feature {\n    mount() {\n        const { current } = this.node;\n        if (!current)\n            return;\n        this.unmount = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.hover)(current, (_element, startEvent) => {\n            handleHoverEvent(this.node, startEvent, \"Start\");\n            return (endEvent) => handleHoverEvent(this.node, endEvent, \"End\");\n        });\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/hover.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PanSession: () => (/* binding */ PanSession)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-utils */ \"../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../events/add-pointer-event.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\");\n/* harmony import */ var _events_event_info_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../events/event-info.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\");\n/* harmony import */ var _utils_distance_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/distance.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/distance.mjs\");\n\n\n\n\n\n\n/**\n * @internal\n */\nclass PanSession {\n    constructor(event, handlers, { transformPagePoint, contextWindow = window, dragSnapToOrigin = false, distanceThreshold = 3, } = {}) {\n        /**\n         * @internal\n         */\n        this.startEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEventInfo = null;\n        /**\n         * @internal\n         */\n        this.handlers = {};\n        /**\n         * @internal\n         */\n        this.contextWindow = window;\n        this.updatePoint = () => {\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const info = getPanInfo(this.lastMoveEventInfo, this.history);\n            const isPanStarted = this.startEvent !== null;\n            // Only start panning if the offset is larger than 3 pixels. If we make it\n            // any larger than this we'll want to reset the pointer history\n            // on the first update to avoid visual snapping to the cursor.\n            const isDistancePastThreshold = (0,_utils_distance_mjs__WEBPACK_IMPORTED_MODULE_0__.distance2D)(info.offset, { x: 0, y: 0 }) >= this.distanceThreshold;\n            if (!isPanStarted && !isDistancePastThreshold)\n                return;\n            const { point } = info;\n            const { timestamp } = motion_dom__WEBPACK_IMPORTED_MODULE_1__.frameData;\n            this.history.push({ ...point, timestamp });\n            const { onStart, onMove } = this.handlers;\n            if (!isPanStarted) {\n                onStart && onStart(this.lastMoveEvent, info);\n                this.startEvent = this.lastMoveEvent;\n            }\n            onMove && onMove(this.lastMoveEvent, info);\n        };\n        this.handlePointerMove = (event, info) => {\n            this.lastMoveEvent = event;\n            this.lastMoveEventInfo = transformPoint(info, this.transformPagePoint);\n            // Throttle mouse move event to once per frame\n            motion_dom__WEBPACK_IMPORTED_MODULE_1__.frame.update(this.updatePoint, true);\n        };\n        this.handlePointerUp = (event, info) => {\n            this.end();\n            const { onEnd, onSessionEnd, resumeAnimation } = this.handlers;\n            if (this.dragSnapToOrigin)\n                resumeAnimation && resumeAnimation();\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const panInfo = getPanInfo(event.type === \"pointercancel\"\n                ? this.lastMoveEventInfo\n                : transformPoint(info, this.transformPagePoint), this.history);\n            if (this.startEvent && onEnd) {\n                onEnd(event, panInfo);\n            }\n            onSessionEnd && onSessionEnd(event, panInfo);\n        };\n        // If we have more than one touch, don't start detecting this gesture\n        if (!(0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.isPrimaryPointer)(event))\n            return;\n        this.dragSnapToOrigin = dragSnapToOrigin;\n        this.handlers = handlers;\n        this.transformPagePoint = transformPagePoint;\n        this.distanceThreshold = distanceThreshold;\n        this.contextWindow = contextWindow || window;\n        const info = (0,_events_event_info_mjs__WEBPACK_IMPORTED_MODULE_2__.extractEventInfo)(event);\n        const initialInfo = transformPoint(info, this.transformPagePoint);\n        const { point } = initialInfo;\n        const { timestamp } = motion_dom__WEBPACK_IMPORTED_MODULE_1__.frameData;\n        this.history = [{ ...point, timestamp }];\n        const { onSessionStart } = handlers;\n        onSessionStart &&\n            onSessionStart(event, getPanInfo(initialInfo, this.history));\n        this.removeListeners = (0,motion_utils__WEBPACK_IMPORTED_MODULE_3__.pipe)((0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_4__.addPointerEvent)(this.contextWindow, \"pointermove\", this.handlePointerMove), (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_4__.addPointerEvent)(this.contextWindow, \"pointerup\", this.handlePointerUp), (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_4__.addPointerEvent)(this.contextWindow, \"pointercancel\", this.handlePointerUp));\n    }\n    updateHandlers(handlers) {\n        this.handlers = handlers;\n    }\n    end() {\n        this.removeListeners && this.removeListeners();\n        (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.cancelFrame)(this.updatePoint);\n    }\n}\nfunction transformPoint(info, transformPagePoint) {\n    return transformPagePoint ? { point: transformPagePoint(info.point) } : info;\n}\nfunction subtractPoint(a, b) {\n    return { x: a.x - b.x, y: a.y - b.y };\n}\nfunction getPanInfo({ point }, history) {\n    return {\n        point,\n        delta: subtractPoint(point, lastDevicePoint(history)),\n        offset: subtractPoint(point, startDevicePoint(history)),\n        velocity: getVelocity(history, 0.1),\n    };\n}\nfunction startDevicePoint(history) {\n    return history[0];\n}\nfunction lastDevicePoint(history) {\n    return history[history.length - 1];\n}\nfunction getVelocity(history, timeDelta) {\n    if (history.length < 2) {\n        return { x: 0, y: 0 };\n    }\n    let i = history.length - 1;\n    let timestampedPoint = null;\n    const lastPoint = lastDevicePoint(history);\n    while (i >= 0) {\n        timestampedPoint = history[i];\n        if (lastPoint.timestamp - timestampedPoint.timestamp >\n            (0,motion_utils__WEBPACK_IMPORTED_MODULE_3__.secondsToMilliseconds)(timeDelta)) {\n            break;\n        }\n        i--;\n    }\n    if (!timestampedPoint) {\n        return { x: 0, y: 0 };\n    }\n    const time = (0,motion_utils__WEBPACK_IMPORTED_MODULE_3__.millisecondsToSeconds)(lastPoint.timestamp - timestampedPoint.timestamp);\n    if (time === 0) {\n        return { x: 0, y: 0 };\n    }\n    const currentVelocity = {\n        x: (lastPoint.x - timestampedPoint.x) / time,\n        y: (lastPoint.y - timestampedPoint.y) / time,\n    };\n    if (currentVelocity.x === Infinity) {\n        currentVelocity.x = 0;\n    }\n    if (currentVelocity.y === Infinity) {\n        currentVelocity.y = 0;\n    }\n    return currentVelocity;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/index.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/index.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PanGesture: () => (/* binding */ PanGesture)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-utils */ \"../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../events/add-pointer-event.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../motion/features/Feature.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var _utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/get-context-window.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/get-context-window.mjs\");\n/* harmony import */ var _PanSession_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PanSession.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs\");\n\n\n\n\n\n\n\nconst asyncHandler = (handler) => (event, info) => {\n    if (handler) {\n        motion_dom__WEBPACK_IMPORTED_MODULE_0__.frame.postRender(() => handler(event, info));\n    }\n};\nclass PanGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_1__.Feature {\n    constructor() {\n        super(...arguments);\n        this.removePointerDownListener = motion_utils__WEBPACK_IMPORTED_MODULE_2__.noop;\n    }\n    onPointerDown(pointerDownEvent) {\n        this.session = new _PanSession_mjs__WEBPACK_IMPORTED_MODULE_3__.PanSession(pointerDownEvent, this.createPanHandlers(), {\n            transformPagePoint: this.node.getTransformPagePoint(),\n            contextWindow: (0,_utils_get_context_window_mjs__WEBPACK_IMPORTED_MODULE_4__.getContextWindow)(this.node),\n        });\n    }\n    createPanHandlers() {\n        const { onPanSessionStart, onPanStart, onPan, onPanEnd } = this.node.getProps();\n        return {\n            onSessionStart: asyncHandler(onPanSessionStart),\n            onStart: asyncHandler(onPanStart),\n            onMove: onPan,\n            onEnd: (event, info) => {\n                delete this.session;\n                if (onPanEnd) {\n                    motion_dom__WEBPACK_IMPORTED_MODULE_0__.frame.postRender(() => onPanEnd(event, info));\n                }\n            },\n        };\n    }\n    mount() {\n        this.removePointerDownListener = (0,_events_add_pointer_event_mjs__WEBPACK_IMPORTED_MODULE_5__.addPointerEvent)(this.node.current, \"pointerdown\", (event) => this.onPointerDown(event));\n    }\n    update() {\n        this.session && this.session.updateHandlers(this.createPanHandlers());\n    }\n    unmount() {\n        this.removePointerDownListener();\n        this.session && this.session.end();\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/index.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/press.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/press.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PressGesture: () => (/* binding */ PressGesture)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../events/event-info.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/events/event-info.mjs\");\n/* harmony import */ var _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../motion/features/Feature.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\n\n\nfunction handlePressEvent(node, event, lifecycle) {\n    const { props } = node;\n    if (node.current instanceof HTMLButtonElement && node.current.disabled) {\n        return;\n    }\n    if (node.animationState && props.whileTap) {\n        node.animationState.setActive(\"whileTap\", lifecycle === \"Start\");\n    }\n    const eventName = (\"onTap\" + (lifecycle === \"End\" ? \"\" : lifecycle));\n    const callback = props[eventName];\n    if (callback) {\n        motion_dom__WEBPACK_IMPORTED_MODULE_0__.frame.postRender(() => callback(event, (0,_events_event_info_mjs__WEBPACK_IMPORTED_MODULE_1__.extractEventInfo)(event)));\n    }\n}\nclass PressGesture extends _motion_features_Feature_mjs__WEBPACK_IMPORTED_MODULE_2__.Feature {\n    mount() {\n        const { current } = this.node;\n        if (!current)\n            return;\n        this.unmount = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.press)(current, (_element, startEvent) => {\n            handlePressEvent(this.node, startEvent, \"Start\");\n            return (endEvent, { success }) => handlePressEvent(this.node, endEvent, success ? \"End\" : \"Cancel\");\n        }, { useGlobalTarget: this.node.props.globalTapTarget });\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/press.mjs\n");

/***/ })

};
;