import React from 'react';
import { HStack, Circle, Box, Text, useBreakpointValue, VStack } from '@chakra-ui/react';
import { FiCheck } from 'react-icons/fi';

interface StepIndicatorProps {
  steps: string[];
  currentStep: number;
  completedSteps: number[];
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ steps, currentStep, completedSteps }) => {
  const isMobile = useBreakpointValue({ base: true, md: false });

  return (
    <HStack spacing={0} justify="center" mb={8} position="relative" w="full">
      {/* Connector Line */}
      <Box
        position="absolute"
        width="90%"
        height="2px"
        bg="whiteAlpha.200"
        top="16px"
        left="5%"
        zIndex={0}
      />
      
      {/* Progress Line */}
      <Box
        position="absolute"
        width={`${(Math.max(currentStep - 1, 0) / (steps.length - 1)) * 90}%`}
        height="2px"
        bg="blue.400"
        top="16px"
        left="5%"
        zIndex={0}
        transition="width 0.3s ease-in-out"
      />

      {steps.map((step, index) => {
        const isCompleted = completedSteps.includes(index);
        const isCurrent = currentStep === index;
        const isActive = index <= currentStep;

        return (
          <HStack
            key={step}
            flex={1}
            align="center"
            justify="center"
            position="relative"
            zIndex={1}
          >
            <VStack spacing={2} align="center">
              <Circle
                size="32px"
                bg={isCompleted ? "green.400" : isCurrent ? "blue.400" : isActive ? "blue.600" : "whiteAlpha.200"}
                color={isCompleted || isCurrent ? "white" : "whiteAlpha.600"}
                borderWidth={2}
                borderColor={isCurrent ? "blue.200" : "transparent"}
                transition="all 0.2s"
                _hover={{
                  transform: "scale(1.1)",
                  boxShadow: "0 0 0 3px rgba(66, 153, 225, 0.3)"
                }}
              >
                {isCompleted ? (
                  <FiCheck size={18} />
                ) : (
                  <Text fontSize="sm" fontWeight="bold">
                    {index + 1}
                  </Text>
                )}
              </Circle>
              
              <Text
                fontSize={isMobile ? "xs" : "sm"}
                fontWeight={isCurrent ? "bold" : "medium"}
                color={isActive ? "white" : "whiteAlpha.600"}
                textAlign="center"
                transition="all 0.2s"
                opacity={isActive ? 1 : 0.7}
              >
                {step}
              </Text>
            </VStack>
          </HStack>
        );
      })}
    </HStack>
  );
};

export default React.memo(StepIndicator); 