"use strict";(()=>{var t={};t.id=4159,t.ids=[4159],t.modules={12518:t=>{t.exports=require("mongodb")},15806:t=>{t.exports=require("next-auth/next")},20396:t=>{t.exports=require("next-auth/providers/discord")},29021:t=>{t.exports=require("fs")},33873:t=>{t.exports=require("path")},65542:t=>{t.exports=require("next-auth")},72115:t=>{t.exports=require("yaml")},75600:t=>{t.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},84179:(t,e,a)=>{a.r(e),a.d(e,{config:()=>b,default:()=>m,routeModule:()=>x});var r={};a.r(r),a.d(r,{default:()=>f});var i=a(93433),s=a(20264),n=a(20584),o=a(15806),c=a(94506),d=a(12518),l=a(98580);let{url:p,name:h}=l.dashboardConfig.database,g=null;async function u(){return g||(g=await d.MongoClient.connect(p))}async function f(t,e){let a,r,i=await (0,o.getServerSession)(t,e,c.authOptions);if(!i)return e.status(401).json({error:"Unauthorized"});let{id:s}=t.query;if(!s||Array.isArray(s))return e.status(400).json({error:"Invalid ticket id"});let{token:n,guildId:p,ticketLogChannelId:g}=l.dashboardConfig.bot;if(!n||!p)return e.status(500).json({error:"Bot configuration missing"});try{a=await u()}catch(t){return e.status(500).json({error:"Database connection failed"})}let f=a.db(h).collection("tickets");try{if(!(r=await f.findOne({_id:new d.ObjectId(s)})))return e.status(404).json({error:"Ticket not found"})}catch(t){return e.status(500).json({error:"Database error"})}let m=i.user.isAdmin,b=r.creatorId===i.user.id;if(!m&&!b)return e.status(403).json({error:"Forbidden"});switch(t.method){case"PATCH":{if("closed"===r.status)return e.status(400).json({error:"Ticket already closed"});try{await fetch(`https://discord.com/api/v10/channels/${r.channelId}`,{method:"PATCH",headers:{Authorization:`Bot ${n}`,"Content-Type":"application/json"},body:JSON.stringify({name:`closed-${r.channelId}`.slice(0,100),permission_overwrites:[{id:p,type:0,deny:"2048"},{id:r.creatorId,type:1,deny:"2048"}]})})}catch(t){}let t=async()=>{let t,e=[];for(;;){let a=`https://discord.com/api/v10/channels/${r.channelId}/messages?limit=100${t?`&before=${t}`:""}`,i=await fetch(a,{headers:{Authorization:`Bot ${n}`}});if(!i.ok)break;let s=await i.json();if(e.push(...s),s.length<100)break;t=s[s.length-1].id}return e.reverse(),e},a=await t(),o=async(t,e)=>{try{let a=await fetch(t),r=await a.arrayBuffer();if(r.byteLength>1048576)return t;let i=Buffer.from(r).toString("base64");return`data:${e};base64,${i}`}catch{return t}},c=async(t,e,a)=>{let r=Array.from(t.matchAll(e));if(0===r.length)return t;let i=await Promise.all(r.map(t=>a(...t))),s=t;for(let t=r.length-1;t>=0;t--){let e=r[t],a=i[t];s=s.substring(0,e.index)+a+s.substring(e.index+e[0].length)}return s},l=async t=>{if(!t.content_type?.startsWith("image/")||t.size>1048576)return t.content_type?.startsWith("image/")?"":`<a href="${t.url}" target="_blank">${t.filename}</a>`;try{let e=await (await fetch(t.url)).arrayBuffer(),a=Buffer.from(e).toString("base64");return`<img class="attach" src="data:${t.content_type};base64,${a}" />`}catch{return`<img class="attach" src="${t.url}" />`}},h=a.map(async t=>{let e=new Date(t.timestamp).toLocaleString(),a=t.author.avatar?`https://cdn.discordapp.com/avatars/${t.author.id}/${t.author.avatar}.png?size=32`:`https://cdn.discordapp.com/embed/avatars/${parseInt(t.author.discriminator)%5}.png`,r=t.content||"",i=r.match(/<a?:[\w]+:\d+>/g);if(i)for(let t of i){let e=t.startsWith("<a:"),a=t.match(/:(\d+)>/);if(a){let i=a[1],s=e?"gif":"png",n=`https://cdn.discordapp.com/emojis/${i}.${s}`;try{let e=await o(n,`image/${s}`);r=r.replace(t,`<img class="emoji" src="${e}" />`)}catch(t){}}}let s=(r=await c(r,/(https?:\/\/tenor\.com\/view\/[^\s]+)/g,async t=>{let e=t.match(/-(\d+)(?:\?.*)?$/);if(!e)return`<a href="${t}" target="_blank">🎬 Tenor GIF</a>`;let a=e[1];try{let t,e=await fetch(`https://g.tenor.com/v1/gifs?ids=${a}&key=LIVDSRZULELA`),r=await e.json();if(!r?.results?.length)throw 0;let i=r.results[0].media_formats||r.results[0].media?.[0];for(let e of["mediumgif","gif","tinygif","nanogif"])if(i?.[e]?.url){t=i[e].url;break}if(!t)throw 0;let s=await o(t,"image/gif");if(s.startsWith("data:"))return`<img class="attach" src="${s}" />`;return`<img class="attach" src="${t}" />`}catch{return`<a href="${t}" target="_blank">🎬 Tenor GIF</a>`}})).match(/https?:\/\/[^\s]+\.(png|jpe?g|gif|webp)/gi);if(s)for(let t of s)try{let e=t.endsWith(".gif")?"image/gif":t.endsWith(".webp")?"image/webp":t.endsWith(".png")?"image/png":"image/jpeg",a=await o(t,e);r=r.replace(t,`<img class="attach" src="${a}" />`)}catch(t){}r=r.replace(/</g,"|||LT|||").replace(/>/g,"|||GT|||").replace(/|||LT|||img/g,"<img").replace(/src="[^"]*"[^>]*|||GT|||/g,t=>t.replace(/|||GT|||/g,">")).replace(/\/|||GT|||/g,"/>").replace(/|||LT|||\/img|||GT|||/g,"</img>").replace(/|||LT|||a href/g,"<a href").replace(/|||LT|||\/a|||GT|||/g,"</a>").replace(/target="_blank"[^>]*|||GT|||/g,t=>t.replace(/|||GT|||/g,">")).replace(/|||LT|||/g,"&lt;").replace(/|||GT|||/g,"&gt;").replace(/\n/g,"<br/>");let n="";return t.attachments?.length>0&&(n=(await Promise.all(t.attachments.map(l))).join("")),`<div class="msg"><img class="avatar" src="${a}"/><div class="bubble"><div class="meta"><span class="name">${t.author.username}${t.author.discriminator?`#${t.author.discriminator}`:""}</span><span class="time">${e}</span></div><div class="content">${r}${n}</div></div></div>`}),u=(await Promise.all(h)).join("\n"),m=`<!DOCTYPE html>
<html lang="en"><head><meta charset="utf-8"/><title>Ticket ${s} Transcript</title>
<style>
*{box-sizing:border-box;margin:0;padding:0}
body{font-family:Segoe UI,Arial,sans-serif;background:#0f172a;color:#e2e8f0;min-height:100vh;display:flex;flex-direction:column;align-items:center;padding:40px 12px;background-image:radial-gradient(circle at 25% 25%,rgba(29,78,216,.4),transparent 60%),radial-gradient(circle at 75% 75%,rgba(60,46,123,.35),transparent 60%)}
h2{color:#60a5fa;margin-bottom:12px;font-weight:600;letter-spacing:.3px;text-shadow:0 1px 2px rgba(0,0,0,.4)}
#wrapper{width:100%;max-width:860px}
.msg{display:flex;gap:12px;margin-bottom:18px;animation:fadeIn .4s ease}
.avatar{width:42px;height:42px;border-radius:50%;flex-shrink:0;box-shadow:0 0 0 2px rgba(255,255,255,.07)}
.bubble{background:rgba(255,255,255,0.06);padding:10px 14px;border-radius:10px;backdrop-filter:blur(14px) saturate(130%);width:fit-content;max-width:90%}
.meta{font-size:12px;color:#94a3b8;margin-bottom:6px;display:flex;flex-wrap:wrap;gap:6px 10px}
.name{color:#82b1ff;font-weight:600}
.content{font-size:14px;line-height:1.5;word-break:break-word}
.emoji{width:20px;height:20px;vertical-align:-4px}
.attach{margin-top:8px;max-width:260px;border-radius:8px;box-shadow:0 2px 6px rgba(0,0,0,.4)}
@keyframes fadeIn{from{opacity:0;transform:translateY(6px)}to{opacity:1;transform:translateY(0)}}
.badge{display:inline-block;padding:4px 10px;border-radius:999px;font-size:12px;font-weight:600;margin-bottom:28px;background:var(--badge-bg);color:#fff;box-shadow:0 1px 3px rgba(0,0,0,.4)}
</style></head><body>
<h2>Ticket ${s} Transcript</h2>
<span class="badge" style="--badge-bg:${{support:"#3b82f6","18plus":"#ef4444",other:"#a855f7"}[r.category||"other"]||"#6b7280"}">${r.category||"Support"}</span>
<div id="wrapper">
${u}
</div>
</body></html>`;if(await f.updateOne({_id:new d.ObjectId(s)},{$set:{status:"closed",closedAt:new Date,transcriptHtml:m}}),g)try{await fetch(`https://discord.com/api/v10/channels/${g}/messages`,{method:"POST",headers:{Authorization:`Bot ${n}`,"Content-Type":"application/json"},body:JSON.stringify({content:`✅ Ticket <#${r.channelId}> closed by <@${i.user.id}>`})})}catch{}return e.status(200).json({message:"Ticket closed"})}case"DELETE":if(!m)return e.status(403).json({error:"Admin only"});try{await fetch(`https://discord.com/api/v10/channels/${r.channelId}`,{method:"DELETE",headers:{Authorization:`Bot ${n}`}})}catch(t){}if(await f.deleteOne({_id:new d.ObjectId(s)}),g)try{await fetch(`https://discord.com/api/v10/channels/${g}/messages`,{method:"POST",headers:{Authorization:`Bot ${n}`,"Content-Type":"application/json"},body:JSON.stringify({content:`🗑️ Ticket ${s} deleted by <@${i.user.id}>`})})}catch{}return e.status(200).json({message:"Ticket deleted"});default:return e.setHeader("Allow",["PATCH","DELETE"]),e.status(405).json({error:`Method ${t.method} not allowed`})}}let m=(0,n.M)(r,"default"),b=(0,n.M)(r,"config"),x=new i.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/discord/tickets/[id]",pathname:"/api/discord/tickets/[id]",bundlePath:"",filename:""},userland:r})}};var e=require("../../../../webpack-api-runtime.js");e.C(t);var a=t=>e(e.s=t),r=e.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>a(84179));module.exports=r})();