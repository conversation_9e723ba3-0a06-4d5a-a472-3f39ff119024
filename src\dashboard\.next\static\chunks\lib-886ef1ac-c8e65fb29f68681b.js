"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8637],{9799:(e,t,r)=>{var n=r(94285),i=r(45617),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,s=n.useRef,u=n.useEffect,h=n.useMemo,c=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var l=s(null);if(null===l.current){var f={hasValue:!1,value:null};l.current=f}else f=l.current;var d=o(e,(l=h(function(){function e(e){if(!u){if(u=!0,o=e,e=n(e),void 0!==i&&f.hasValue){var t=f.value;if(i(t,e))return s=t}return s=e}if(t=s,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,s=r)}var o,s,u=!1,h=void 0===r?null:r;return[function(){return e(t())},null===h?void 0:function(){return e(h())}]},[t,r,n,i]))[0],l[1]);return u(function(){f.hasValue=!0,f.value=d},[d]),c(d),d}},16863:(e,t,r)=>{r(91625)},40012:(e,t,r)=>{function n(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}r.d(t,{SV:()=>s});var i=r(94285),a="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,o=new WeakMap;function s(e,t){var r,s,u,h=(r=t||null,s=function(t){return e.forEach(function(e){return n(e,t)})},(u=(0,i.useState)(function(){return{value:r,callback:s,facade:{get current(){return u.value},set current(value){var e=u.value;e!==value&&(u.value=value,u.callback(value,e))}}}})[0]).callback=s,u.facade);return a(function(){var t=o.get(h);if(t){var r=new Set(t),i=new Set(e),a=h.current;r.forEach(function(e){i.has(e)||n(e,null)}),i.forEach(function(e){r.has(e)||n(e,a)})}o.set(h,e)},[e]),h}var u=function(){return null},h=new WeakMap},45617:(e,t,r)=>{e.exports=r(81664)},58766:(e,t,r)=>{r.d(t,{CL:()=>s,fi:()=>u,mb:()=>c});var n=r(80594),i=r(94285);function a(e){return e}function o(e,t){void 0===t&&(t=a);var r=[],n=!1;return{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:e},useMedium:function(e){var i=t(e,n);return r.push(i),function(){r=r.filter(function(e){return e!==i})}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var i=r;r=[],i.forEach(e),t=r}var a=function(){var r=t;t=[],r.forEach(e)},o=function(){return Promise.resolve().then(a)};o(),r={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),r}}}}}function s(e,t){return void 0===t&&(t=a),o(e,t)}function u(e){void 0===e&&(e={});var t=o(null);return t.options=(0,n.Cl)({async:!0,ssr:!1},e),t}r(5e3).L,new WeakMap;var h=function(e){var t=e.sideCar,r=(0,n.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var a=t.read();if(!a)throw Error("Sidecar medium not found");return i.createElement(a,(0,n.Cl)({},r))};function c(e,t){return e.useMedium(t),h}h.isSideCarExport=!0},78165:(e,t,r)=>{r.d(t,{r:()=>a});var n,i=r(2209);(()=>{let e;var t,r={975:e=>{function t(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}function r(e,t){for(var r,n="",i=0,a=-1,o=0,s=0;s<=e.length;++s){if(s<e.length)r=e.charCodeAt(s);else{if(47===r)break;r=47}if(47===r){if(a===s-1||1===o);else if(a!==s-1&&2===o){if(n.length<2||2!==i||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2)){if(n.length>2){var u=n.lastIndexOf("/");if(u!==n.length-1){-1===u?(n="",i=0):i=(n=n.slice(0,u)).length-1-n.lastIndexOf("/"),a=s,o=0;continue}}else if(2===n.length||1===n.length){n="",i=0,a=s,o=0;continue}}t&&(n.length>0?n+="/..":n="..",i=2)}else n.length>0?n+="/"+e.slice(a+1,s):n=e.slice(a+1,s),i=s-a-1;a=s,o=0}else 46===r&&-1!==o?++o:o=-1}return n}var n={resolve:function(){for(var e,n,a="",o=!1,s=arguments.length-1;s>=-1&&!o;s--)s>=0?e=arguments[s]:(void 0===n&&(n=i.cwd()),e=n),t(e),0!==e.length&&(a=e+"/"+a,o=47===e.charCodeAt(0));return a=r(a,!o),o?a.length>0?"/"+a:"/":a.length>0?a:"."},normalize:function(e){if(t(e),0===e.length)return".";var n=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=r(e,!n)).length||n||(e="."),e.length>0&&i&&(e+="/"),n?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0==arguments.length)return".";for(var e,r=0;r<arguments.length;++r){var i=arguments[r];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":n.normalize(e)},relative:function(e,r){if(t(e),t(r),e===r||(e=n.resolve(e))===(r=n.resolve(r)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var a=e.length,o=a-i,s=1;s<r.length&&47===r.charCodeAt(s);++s);for(var u=r.length-s,h=o<u?o:u,c=-1,l=0;l<=h;++l){if(l===h){if(u>h){if(47===r.charCodeAt(s+l))return r.slice(s+l+1);if(0===l)return r.slice(s+l)}else o>h&&(47===e.charCodeAt(i+l)?c=l:0===l&&(c=0));break}var f=e.charCodeAt(i+l);if(f!==r.charCodeAt(s+l))break;47===f&&(c=l)}var d="";for(l=i+c+1;l<=a;++l)l!==a&&47!==e.charCodeAt(l)||(0===d.length?d+="..":d+="/..");return d.length>0?d+r.slice(s+c):(s+=c,47===r.charCodeAt(s)&&++s,r.slice(s))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var r=e.charCodeAt(0),n=47===r,i=-1,a=!0,o=e.length-1;o>=1;--o)if(47===(r=e.charCodeAt(o))){if(!a){i=o;break}}else a=!1;return -1===i?n?"/":".":n&&1===i?"//":e.slice(0,i)},basename:function(e,r){if(void 0!==r&&"string"!=typeof r)throw TypeError('"ext" argument must be a string');t(e);var n,i=0,a=-1,o=!0;if(void 0!==r&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return"";var s=r.length-1,u=-1;for(n=e.length-1;n>=0;--n){var h=e.charCodeAt(n);if(47===h){if(!o){i=n+1;break}}else -1===u&&(o=!1,u=n+1),s>=0&&(h===r.charCodeAt(s)?-1==--s&&(a=n):(s=-1,a=u))}return i===a?a=u:-1===a&&(a=e.length),e.slice(i,a)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!o){i=n+1;break}}else -1===a&&(o=!1,a=n+1);return -1===a?"":e.slice(i,a)},extname:function(e){t(e);for(var r=-1,n=0,i=-1,a=!0,o=0,s=e.length-1;s>=0;--s){var u=e.charCodeAt(s);if(47!==u)-1===i&&(a=!1,i=s+1),46===u?-1===r?r=s:1!==o&&(o=1):-1!==r&&(o=-1);else if(!a){n=s+1;break}}return -1===r||-1===i||0===o||1===o&&r===i-1&&r===n+1?"":e.slice(r,i)},format:function(e){var t,r;if(null===e||"object"!=typeof e)throw TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return t=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||""),t?t===e.root?t+r:t+"/"+r:r},parse:function(e){t(e);var r={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return r;var n,i=e.charCodeAt(0),a=47===i;a?(r.root="/",n=1):n=0;for(var o=-1,s=0,u=-1,h=!0,c=e.length-1,l=0;c>=n;--c)if(47!==(i=e.charCodeAt(c)))-1===u&&(h=!1,u=c+1),46===i?-1===o?o=c:1!==l&&(l=1):-1!==o&&(l=-1);else if(!h){s=c+1;break}return -1===o||-1===u||0===l||1===l&&o===u-1&&o===s+1?-1!==u&&(r.base=r.name=0===s&&a?e.slice(1,u):e.slice(s,u)):(0===s&&a?(r.name=e.slice(1,o),r.base=e.slice(1,u)):(r.name=e.slice(s,o),r.base=e.slice(s,u)),r.ext=e.slice(o,u)),s>0?r.dir=e.slice(0,s-1):a&&(r.dir="/"),r},sep:"/",delimiter:":",win32:null,posix:null};n.posix=n,e.exports=n}},a={};function o(e){var t=a[e];if(void 0!==t)return t.exports;var n=a[e]={exports:{}};return r[e](n,n.exports,o),n.exports}o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};(o.r(s),o.d(s,{URI:()=>d,Utils:()=>t}),"object"==typeof i)?e="win32"===i.platform:"object"==typeof navigator&&(e=navigator.userAgent.indexOf("Windows")>=0);let u=/^\w[\w\d+.-]*$/,h=/^\//,c=/^\/\//;function l(e,t){if(!e.scheme&&t)throw Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!u.test(e.scheme))throw Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!h.test(e.path))throw Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(c.test(e.path))throw Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}let f=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class d{static isUri(e){return e instanceof d||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}scheme;authority;path;query;fragment;constructor(e,t,r,n,i,a=!1){"object"==typeof e?(this.scheme=e.scheme||"",this.authority=e.authority||"",this.path=e.path||"",this.query=e.query||"",this.fragment=e.fragment||""):(this.scheme=function(e,t){return e||t?e:"file"}(e,a),this.authority=t||"",this.path=function(e,t){switch(e){case"https":case"http":case"file":t?"/"!==t[0]&&(t="/"+t):t="/"}return t}(this.scheme,r||""),this.query=n||"",this.fragment=i||"",l(this,a))}get fsPath(){return b(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:r,path:n,query:i,fragment:a}=e;return void 0===t?t=this.scheme:null===t&&(t=""),void 0===r?r=this.authority:null===r&&(r=""),void 0===n?n=this.path:null===n&&(n=""),void 0===i?i=this.query:null===i&&(i=""),void 0===a?a=this.fragment:null===a&&(a=""),t===this.scheme&&r===this.authority&&n===this.path&&i===this.query&&a===this.fragment?this:new p(t,r,n,i,a)}static parse(e,t=!1){let r=f.exec(e);return r?new p(r[2]||"",S(r[4]||""),S(r[5]||""),S(r[7]||""),S(r[9]||""),t):new p("","","","","")}static file(t){let r="";if(e&&(t=t.replace(/\\/g,"/")),"/"===t[0]&&"/"===t[1]){let e=t.indexOf("/",2);-1===e?(r=t.substring(2),t="/"):(r=t.substring(2,e),t=t.substring(e)||"/")}return new p("file",r,t,"","")}static from(e){let t=new p(e.scheme,e.authority,e.path,e.query,e.fragment);return l(t,!0),t}toString(e=!1){return C(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof d)return e;{let t=new p(e);return t._formatted=e.external,t._fsPath=e._sep===g?e.fsPath:null,t}}return e}}let g=e?1:void 0;class p extends d{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=b(this,!1)),this._fsPath}toString(e=!1){return e?C(this,!0):(this._formatted||(this._formatted=C(this,!1)),this._formatted)}toJSON(){let e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=g),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}let v={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function m(e,t,r){let n,i=-1;for(let a=0;a<e.length;a++){let o=e.charCodeAt(a);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o||r&&91===o||r&&93===o||r&&58===o)-1!==i&&(n+=encodeURIComponent(e.substring(i,a)),i=-1),void 0!==n&&(n+=e.charAt(a));else{void 0===n&&(n=e.substr(0,a));let t=v[o];void 0!==t?(-1!==i&&(n+=encodeURIComponent(e.substring(i,a)),i=-1),n+=t):-1===i&&(i=a)}}return -1!==i&&(n+=encodeURIComponent(e.substring(i))),void 0!==n?n:e}function y(e){let t;for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);35===n||63===n?(void 0===t&&(t=e.substr(0,r)),t+=v[n]):void 0!==t&&(t+=e[r])}return void 0!==t?t:e}function b(t,r){let n;return n=t.authority&&t.path.length>1&&"file"===t.scheme?`//${t.authority}${t.path}`:47===t.path.charCodeAt(0)&&(t.path.charCodeAt(1)>=65&&90>=t.path.charCodeAt(1)||t.path.charCodeAt(1)>=97&&122>=t.path.charCodeAt(1))&&58===t.path.charCodeAt(2)?r?t.path.substr(1):t.path[1].toLowerCase()+t.path.substr(2):t.path,e&&(n=n.replace(/\//g,"\\")),n}function C(e,t){let r=t?y:m,n="",{scheme:i,authority:a,path:o,query:s,fragment:u}=e;if(i&&(n+=i,n+=":"),(a||"file"===i)&&(n+="/",n+="/"),a){let e=a.indexOf("@");if(-1!==e){let t=a.substr(0,e);a=a.substr(e+1),-1===(e=t.lastIndexOf(":"))?n+=r(t,!1,!1):(n+=r(t.substr(0,e),!1,!1),n+=":",n+=r(t.substr(e+1),!1,!0)),n+="@"}-1===(e=(a=a.toLowerCase()).lastIndexOf(":"))?n+=r(a,!1,!0):(n+=r(a.substr(0,e),!1,!0),n+=a.substr(e))}if(o){if(o.length>=3&&47===o.charCodeAt(0)&&58===o.charCodeAt(2)){let e=o.charCodeAt(1);e>=65&&e<=90&&(o=`/${String.fromCharCode(e+32)}:${o.substr(3)}`)}else if(o.length>=2&&58===o.charCodeAt(1)){let e=o.charCodeAt(0);e>=65&&e<=90&&(o=`${String.fromCharCode(e+32)}:${o.substr(2)}`)}n+=r(o,!0,!1)}return s&&(n+="?",n+=r(s,!1,!1)),u&&(n+="#",n+=t?u:m(u,!1,!1)),n}let w=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function S(e){return e.match(w)?e.replace(w,e=>(function e(t){try{return decodeURIComponent(t)}catch{return t.length>3?t.substr(0,3)+e(t.substr(3)):t}})(e)):e}var A=o(975);let x=A.posix||A;!function(e){e.joinPath=function(e,...t){return e.with({path:x.join(e.path,...t)})},e.resolvePath=function(e,...t){let r=e.path,n=!1;"/"!==r[0]&&(r="/"+r,n=!0);let i=x.resolve(r,...t);return n&&"/"===i[0]&&!e.authority&&(i=i.substring(1)),e.with({path:i})},e.dirname=function(e){if(0===e.path.length||"/"===e.path)return e;let t=x.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)&&(t=""),e.with({path:t})},e.basename=function(e){return x.basename(e.path)},e.extname=function(e){return x.extname(e.path)}}(t||(t={})),n=s})();let{URI:a,Utils:o}=n},78869:(e,t,r)=>{r.d(t,{BV:()=>n.BV,GZ:()=>n.GZ,HR:()=>n.HR,IA:()=>n.IA,IJ:()=>n.IJ,Lx:()=>n.Lx,N8:()=>n.N8,PG:()=>n.PG,Re:()=>n.Re,UP:()=>n.UP,Wc:()=>n.Wc,Wi:()=>n.Wi,Xf:()=>n.Xf,YW:()=>n.YW,Yu:()=>n.Yu,ZK:()=>n.ZK,dy:()=>n.dy,e9:()=>n.e9,hK:()=>n.hK,j:()=>n.j,lU:()=>n.lU,n8:()=>n.n8,nV:()=>n.nV,qI:()=>n.qI,qr:()=>n.qr,rM:()=>n.rM,t$:()=>n.t$,ux:()=>n.ux,yD:()=>n.yD});var n=r(36392)},79007:(e,t,r)=>{e.exports=r(9799)},81664:(e,t,r)=>{var n=r(94285),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,s=n.useLayoutEffect,u=n.useDebugValue;function h(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,c=n[1];return s(function(){i.value=r,i.getSnapshot=t,h(i)&&c({inst:i})},[e,r,t]),o(function(){return h(i)&&c({inst:i}),e(function(){h(i)&&c({inst:i})})},[e]),u(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},91625:(e,t,r)=>{var n=r(94285);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},95222:(e,t,r)=>{r.r(t),r.d(t,{scaleBand:()=>n.WH,scaleDiverging:()=>n.Mb,scaleDivergingLog:()=>n.Cr,scaleDivergingPow:()=>n.yj,scaleDivergingSqrt:()=>n.q9,scaleDivergingSymlog:()=>n.xh,scaleIdentity:()=>n.jo,scaleImplicit:()=>n.U4,scaleLinear:()=>n.m4,scaleLog:()=>n.ZE,scaleOrdinal:()=>n.UM,scalePoint:()=>n.hq,scalePow:()=>n.RW,scaleQuantile:()=>n.QL,scaleQuantize:()=>n.WT,scaleRadial:()=>n.af,scaleSequential:()=>n.ex,scaleSequentialLog:()=>n.M3,scaleSequentialPow:()=>n.ui,scaleSequentialQuantile:()=>n.T,scaleSequentialSqrt:()=>n.ye,scaleSequentialSymlog:()=>n.nV,scaleSqrt:()=>n.Bv,scaleSymlog:()=>n.aX,scaleThreshold:()=>n.c3,scaleTime:()=>n.w7,scaleUtc:()=>n.Pp,tickFormat:()=>n.Vr});var n=r(60427)}}]);