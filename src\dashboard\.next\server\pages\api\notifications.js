"use strict";(()=>{var t={};t.id=3781,t.ids=[3781],t.modules={10795:(t,e,r)=>{r.r(e),r.d(e,{config:()=>m,default:()=>p,routeModule:()=>h});var i={};r.r(i),r.d(i,{createNotification:()=>f,default:()=>l});var s=r(93433),a=r(20264),n=r(20584),o=r(15806),u=r(94506),d=r(72290),c=r(12518);async function l(t,e){let r=await (0,o.getServerSession)(t,e,u.authOptions);if(!r?.user?.id)return e.status(401).json({error:"Unauthorized"});let i=(await (0,d.L)()).collection("notifications");if("GET"===t.method)try{let t=await i.find({userId:r.user.id}).sort({timestamp:-1}).limit(50).toArray();return e.status(200).json({notifications:t})}catch(t){return e.status(500).json({error:"Failed to fetch notifications"})}if("POST"===t.method)try{let{type:r,title:s,message:a,targetUserId:n,actionUrl:o,relatedId:u}=t.body;if(!r||!s||!a||!n)return e.status(400).json({error:"Missing required fields"});let d={userId:n,type:r,title:s,message:a,timestamp:new Date,read:!1,actionUrl:o,relatedId:u},c=await i.insertOne(d);return e.status(200).json({id:c.insertedId,...d})}catch(t){return e.status(500).json({error:"Failed to create notification"})}if("PATCH"===t.method)try{let{notificationIds:s,markAsRead:a}=t.body;if(!Array.isArray(s))return e.status(400).json({error:"notificationIds must be an array"});return await i.updateMany({_id:{$in:s.map(t=>new c.ObjectId(t))},userId:r.user.id},{$set:{read:!0===a}}),e.status(200).json({success:!0})}catch(t){return e.status(500).json({error:"Failed to update notifications"})}if("DELETE"===t.method)try{let{notificationIds:s}=t.body;if(!Array.isArray(s))return e.status(400).json({error:"notificationIds must be an array"});return await i.deleteMany({_id:{$in:s.map(t=>new c.ObjectId(t))},userId:r.user.id}),e.status(200).json({success:!0})}catch(t){return e.status(500).json({error:"Failed to delete notifications"})}return e.status(405).json({error:"Method not allowed"})}async function f(t,e,r,i,s,a){try{let n=(await (0,d.L)()).collection("notifications"),o={userId:t,type:e,title:r,message:i,timestamp:new Date,read:!1,actionUrl:s,relatedId:a};return{id:(await n.insertOne(o)).insertedId,...o}}catch(t){throw t}}let p=(0,n.M)(i,"default"),m=(0,n.M)(i,"config"),h=new s.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/notifications",pathname:"/api/notifications",bundlePath:"",filename:""},userland:i})},12518:t=>{t.exports=require("mongodb")},15806:t=>{t.exports=require("next-auth/next")},20396:t=>{t.exports=require("next-auth/providers/discord")},29021:t=>{t.exports=require("fs")},33873:t=>{t.exports=require("path")},65542:t=>{t.exports=require("next-auth")},72115:t=>{t.exports=require("yaml")},75600:t=>{t.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var e=require("../../webpack-api-runtime.js");e.C(t);var r=t=>e(e.s=t),i=e.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(10795));module.exports=i})();