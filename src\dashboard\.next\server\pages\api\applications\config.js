"use strict";(()=>{var e={};e.id=4023,e.ids=[4023],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},44937:(e,n,i)=>{i.r(n),i.d(n,{config:()=>y,default:()=>f,routeModule:()=>v});var t={};i.r(t),i.d(t,{default:()=>g});var r=i(93433),o=i(20264),s=i(20584),a=i(15806),c=i(94506),l=i(12518);let{url:p,name:u}=i(98580).dashboardConfig.database,d=null;async function m(){if(d)return d;let e=await l.MongoClient.connect(p);return d=e,e}let h={isOpen:!0,type:"applications",questions:["Why do you want to be a moderator?","What experience do you have with moderation?","How would you handle a difficult situation with a user?"],scenarios:[{id:1,scenario:"A user is repeatedly posting invite links to other Discord servers in general chat, despite being warned by other members.",context:"Server Rules: No advertising or self-promotion without permission."},{id:2,scenario:"Two users are having a heated argument about politics in the gaming channel, using increasingly hostile language.",context:"Server Rules: Keep discussions on-topic, no political discussions, maintain respectful communication."},{id:3,scenario:"A member reports that another user is sending them unwanted DMs with inappropriate content.",context:"Server Rules: No harassment, respect privacy, no NSFW content."},{id:4,scenario:"A user is spamming emojis and text across multiple channels simultaneously.",context:"Server Rules: No spamming, maintain channel cleanliness."},{id:5,scenario:"A well-known member is caught using racial slurs in voice chat.",context:"Server Rules: Zero tolerance for hate speech and discrimination."},{id:6,scenario:"A user is sharing what appears to be leaked personal information about another member.",context:"Server Rules: No doxxing, respect privacy, protect personal information."},{id:7,scenario:"Multiple users are organizing a raid on another Discord server.",context:"Server Rules: No organizing or participating in raids, maintain good relations with other communities."},{id:8,scenario:"A user is repeatedly asking for free items/currency in the trading channel.",context:"Server Rules: No begging, follow trading channel guidelines."},{id:9,scenario:"A member is posting links to suspicious websites claiming to offer free Discord Nitro.",context:"Server Rules: No scam links, protect community safety."},{id:10,scenario:"A user is using alternate accounts to bypass a temporary mute.",context:"Server Rules: No ban/mute evasion, respect moderator actions."},{id:11,scenario:"Several users are sharing memes with subtle but inappropriate sexual references in the general chat.",context:"Server Rules: Keep content family-friendly, no NSFW content or innuendos."},{id:12,scenario:"A user is repeatedly mentioning everyone in non-emergency situations.",context:"Server Rules: Don't abuse mentions, respect notification settings."},{id:13,scenario:"A member is threatening self-harm in a public channel.",context:"Server Rules: Take mental health concerns seriously, have protocol for crisis situations."},{id:14,scenario:"Users are sharing copyrighted content (movies/games) in the media channel.",context:"Server Rules: No piracy, respect intellectual property rights."},{id:15,scenario:"A user is roleplaying inappropriately in serious discussion channels.",context:"Server Rules: Keep roleplay in designated channels, respect channel purposes."}],open:!0};async function g(e,n){if(!await (0,a.getServerSession)(e,n,c.authOptions))return n.status(401).json({error:"Unauthorized"});if("GET"===e.method)try{let e=(await m()).db(u),i=await e.collection("config").findOne({type:"applications"});return i?(i={...h,...i,scenarios:h.scenarios,questions:h.questions},"boolean"==typeof i.isOpen&&i.open!==i.isOpen?(await e.collection("config").updateOne({type:"applications"},{$set:{open:i.isOpen}}),i.open=i.isOpen):"boolean"==typeof i.open&&i.isOpen!==i.open&&(await e.collection("config").updateOne({type:"applications"},{$set:{isOpen:i.open}}),i.isOpen=i.open)):i=h,n.status(200).json(i)}catch(e){return n.status(500).json({error:"Internal server error"})}if("POST"===e.method){let{isOpen:i}=e.body;if("boolean"!=typeof i)return n.status(400).json({error:"Missing or invalid isOpen field"});try{let e=(await m()).db(u),t=await e.collection("config").findOne({type:"applications"})||h;await e.collection("config").updateOne({type:"applications"},{$set:{...t,type:"applications",isOpen:i,open:i,scenarios:h.scenarios,questions:h.questions}},{upsert:!0});let r=await e.collection("config").findOne({type:"applications"});return n.status(200).json(r)}catch(e){return n.status(500).json({error:"Internal server error"})}}return n.setHeader("Allow",["GET","POST"]),n.status(405).json({error:"Method not allowed"})}let f=(0,s.M)(t,"default"),y=(0,s.M)(t,"config"),v=new r.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/applications/config",pathname:"/api/applications/config",bundlePath:"",filename:""},userland:t})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var n=require("../../../webpack-api-runtime.js");n.C(e);var i=e=>n(n.s=e),t=n.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>i(44937));module.exports=t})();