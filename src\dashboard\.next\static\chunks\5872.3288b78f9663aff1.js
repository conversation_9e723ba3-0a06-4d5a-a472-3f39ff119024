"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5872],{35872:(e,s,i)=>{i.r(s),i.d(s,{default:()=>c});var n=i(94513),o=i(13279),l=i(94285),r=i(43557);let t={General:{icon:r.pcC,permissions:["ADMINISTRATOR","VIEW_AUDIT_LOG","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","MANAGE_EMOJIS_AND_STICKERS","MANAGE_WEBHOOKS","VIEW_CHANNEL"]},Text:{icon:r.mEP,permissions:["SEND_MESSAGES","EMBED_LINKS","ATTACH_FILES","ADD_REACTIONS","USE_EXTERNAL_EMOJIS","MENTION_EVERYONE","<PERSON><PERSON><PERSON>_MESSAGES","READ_MESSAGE_HISTORY"]},Voice:{icon:r.o77,permissions:["CONNECT","SPEAK","STREAM","USE_VAD","PRIORITY_SPEAKER","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS"]},Members:{icon:r.cfS,permissions:["KICK_MEMBERS","BAN_MEMBERS","CHANGE_NICKNAME","MANAGE_NICKNAMES","CREATE_INSTANT_INVITE"]}};function c(e){let{isOpen:s,onClose:i,onSuccess:r,role:c}=e,a=(0,o.dj)(),[E,d]=(0,l.useState)(!1),[h,A]=(0,l.useState)({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1});(0,l.useEffect)(()=>{c&&A({name:c.name||"",color:c.color||"#99AAB5",permissions:c.permissions||[],hoist:c.hoist||!1,mentionable:c.mentionable||!1})},[c]);let S=async()=>{d(!0);try{let e=await fetch("/api/discord/roles/".concat(c.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(h)});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to update role")}a({title:"Success",description:"Role updated successfully",status:"success",duration:3e3}),r(),i()}catch(e){a({title:"Error",description:e.message||"Failed to update role",status:"error",duration:5e3})}finally{d(!1)}},m=e=>{A(s=>({...s,permissions:s.permissions.includes(e)?s.permissions.filter(s=>s!==e):[...s.permissions,e]}))},p=(e,s)=>{A(i=>({...i,[e]:s}))};return(0,n.jsxs)(o.aF,{isOpen:s,onClose:i,size:"xl",scrollBehavior:"inside",children:[(0,n.jsx)(o.mH,{backdropFilter:"blur(10px)"}),(0,n.jsxs)(o.$m,{bg:"gray.800",children:[(0,n.jsx)(o.rQ,{children:"Edit Role"}),(0,n.jsx)(o.s_,{}),(0,n.jsx)(o.cw,{children:(0,n.jsxs)(o.Tk,{spacing:6,children:[(0,n.jsxs)(o.MJ,{children:[(0,n.jsx)(o.lR,{children:"Role Name"}),(0,n.jsx)(o.pd,{placeholder:"Enter role name",value:h.name,onChange:e=>p("name",e.target.value)})]}),(0,n.jsxs)(o.MJ,{children:[(0,n.jsx)(o.lR,{children:"Role Color"}),(0,n.jsx)(o.pd,{type:"color",value:h.color,onChange:e=>p("color",e.target.value)})]}),(0,n.jsx)(o.MJ,{children:(0,n.jsxs)(o.zt,{spacing:4,children:[(0,n.jsx)(o.Sc,{isChecked:h.hoist,onChange:e=>p("hoist",e.target.checked),children:"Display role separately"}),(0,n.jsx)(o.Sc,{isChecked:h.mentionable,onChange:e=>p("mentionable",e.target.checked),children:"Allow anyone to @mention"})]})}),(0,n.jsx)(o.cG,{}),(0,n.jsx)(o.EY,{fontSize:"lg",fontWeight:"bold",alignSelf:"flex-start",children:"Permissions"}),Object.entries(t).map(e=>{let[s,i]=e;return(0,n.jsxs)(o.az,{w:"full",children:[(0,n.jsxs)(o.zt,{mb:2,children:[(0,n.jsx)(o.In,{as:i.icon}),(0,n.jsx)(o.EY,{fontWeight:"semibold",children:s})]}),(0,n.jsx)(o.rS,{columns:2,spacing:2,children:i.permissions.map(e=>(0,n.jsx)(o.Sc,{isChecked:h.permissions.includes(e),onChange:()=>m(e),children:e.split("_").map(e=>e.charAt(0)+e.slice(1).toLowerCase()).join(" ")},e))})]},s)})]})}),(0,n.jsxs)(o.jl,{children:[(0,n.jsx)(o.$n,{variant:"ghost",mr:3,onClick:i,children:"Cancel"}),(0,n.jsx)(o.$n,{colorScheme:"blue",onClick:S,isLoading:E,children:"Save Changes"})]})]})]})}}}]);