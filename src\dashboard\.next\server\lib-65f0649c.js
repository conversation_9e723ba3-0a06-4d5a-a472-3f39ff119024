"use strict";exports.id=9450,exports.ids=[9450],exports.modules={7262:(t,e,r)=>{r.d(e,{l:()=>f,z:()=>v});var n=r(82015),a=r(9373),i=r(29408),o=r(77331),c=7311==r.j?["x","y"]:null;function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){var n,a,i;n=t,a=e,i=r[e],(a=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d(t,e){var{x:r,y:n}=t,a=function(t,e){if(null==t)return{};var r,n,a=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(a[r]=t[r])}return a}(t,c),i=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(e.height||a.height),10),u=parseInt("".concat(e.width||a.width),10);return s(s(s(s(s({},e),a),i?{x:i}:{}),o?{y:o}:{}),{},{height:l,width:u,name:e.name,radius:e.radius})}function v(t){return n.createElement(i.y,l({shapeType:"rectangle",propTransformer:d,activeClassName:"recharts-active-bar"},t))}var f=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,o.Et)(t))return t;var i=(0,o.Et)(r)||(0,o.uy)(r);return i?t(r,n):(i||(0,a.A)(!1),e)}}},12591:(t,e,r)=>{r.d(e,{P:()=>u});var n=r(13141);function a(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a(Object(r),!0).forEach(function(e){var n,a,i;n=t,a=e,i=r[e],(a=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var o={widthCache:{},cacheCount:0},c={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},l="recharts_measurement_span",u=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.m.isSsr)return{width:0,height:0};var a=(Object.keys(e=i({},r)).forEach(t=>{e[t]||delete e[t]}),e),u=JSON.stringify({text:t,copyStyle:a});if(o.widthCache[u])return o.widthCache[u];try{var s=document.getElementById(l);s||((s=document.createElement("span")).setAttribute("id",l),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var d=i(i({},c),a);Object.assign(s.style,d),s.textContent="".concat(t);var v=s.getBoundingClientRect(),f={width:v.width,height:v.height};return o.widthCache[u]=f,++o.cacheCount>2e3&&(o.cacheCount=0,o.widthCache={}),f}catch(t){return{width:0,height:0}}}},12738:(t,e,r)=>{function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}r.d(e,{HX:()=>l,pB:()=>d,y:()=>u,zN:()=>s});class i{static create(t){return new i(t)}constructor(t){this.scale=t}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(t){var{bandAware:e,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==t){if(r)switch(r){case"start":default:return this.scale(t);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+n;case"end":var a=this.bandwidth?this.bandwidth():0;return this.scale(t)+a}if(e){var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i}return this.scale(t)}}isInRange(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}a(i,"EPS",1e-4);var o=function(t){var{width:e,height:r}=t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(a>i&&a<Math.PI-i?r/Math.sin(a):e/Math.cos(a))},c=r(1240);function l(t,e,r){return o({width:t.width+e.width,height:t.height+e.height},r)}function u(t,e,r){var n="width"===r,{x:a,y:i,width:o,height:c}=t;return 1===e?{start:n?a:i,end:n?a+o:i+c}:{start:n?a+o:i+c,end:n?a:i}}function s(t,e,r,n,a){if(t*e<t*n||t*e>t*a)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-a)<=0}function d(t,e){return(0,c.B)(t,e+1)}},13111:(t,e,r)=>{r.d(e,{F0:()=>n,tQ:()=>i,um:()=>a});var n="data-recharts-item-index",a="data-recharts-item-data-key",i=60},13141:(t,e,r)=>{r.d(e,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)}},29408:(t,e,r)=>{r.d(e,{y:()=>y});var n=r(82015),a=r(10026),i=r.n(a),o=r(21162),c=r(75025),l=r(96157),u=r(65132),s=r(84906),d=7311==r.j?["option","shapeType","propTransformer","activeClassName","isActive"]:null;function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach(function(e){var n,a,i;n=t,a=e,i=r[e],(a=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e){return f(f({},e),t)}function p(t){var{shapeType:e,elementProps:r}=t;switch(e){case"rectangle":return n.createElement(o.M,r);case"trapezoid":return n.createElement(c.j,r);case"sector":return n.createElement(l.h,r);case"symbols":if("symbols"===e)return n.createElement(s.i,r);break;default:return null}}function y(t){var e,{option:r,shapeType:a,propTransformer:o=h,activeClassName:c="recharts-active-shape",isActive:l}=t,s=function(t,e){if(null==t)return{};var r,n,a=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(a[r]=t[r])}return a}(t,d);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,f(f({},s),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)e=r(s);else if(i()(r)&&"boolean"!=typeof r){var v=o(r,s);e=n.createElement(p,{shapeType:a,elementProps:v})}else e=n.createElement(p,{shapeType:a,elementProps:s});return l?n.createElement(u.W,{className:c},e):e}},29655:(t,e,r)=>{r.d(e,{$8:()=>x,DW:()=>P,GF:()=>A,Hj:()=>k,IH:()=>S,Mk:()=>E,PW:()=>y,Rh:()=>m,SW:()=>T,YB:()=>b,_L:()=>p,_f:()=>g,bk:()=>K,gH:()=>f,kr:()=>v,qx:()=>N,r4:()=>C,s0:()=>h,uM:()=>D,y2:()=>j,yy:()=>w});var n=r(87206),a=r.n(n),i=r(67063),o=r.n(i),c=r(14799),l=r(77331),u=r(39683);function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,a,i;n=t,a=e,i=r[e],(a=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function v(t,e,r){return(0,l.uy)(t)||(0,l.uy)(e)?r:(0,l.vh)(e)?o()(t,e,r):"function"==typeof e?e(t):r}var f=(t,e,r,n,a)=>{var i,o=-1,c=null!=(i=null==e?void 0:e.length)?i:0;if(c<=1||null==t)return 0;if("angleAxis"===n&&null!=a&&1e-6>=Math.abs(Math.abs(a[1]-a[0])-360))for(var u=0;u<c;u++){var s=u>0?r[u-1].coordinate:r[c-1].coordinate,d=r[u].coordinate,v=u>=c-1?r[0].coordinate:r[u+1].coordinate,f=void 0;if((0,l.sA)(d-s)!==(0,l.sA)(v-d)){var h=[];if((0,l.sA)(v-d)===(0,l.sA)(a[1]-a[0])){f=v;var p=d+a[1]-a[0];h[0]=Math.min(p,(p+s)/2),h[1]=Math.max(p,(p+s)/2)}else{f=s;var y=v+a[1]-a[0];h[0]=Math.min(d,(y+d)/2),h[1]=Math.max(d,(y+d)/2)}var m=[Math.min(d,(f+d)/2),Math.max(d,(f+d)/2)];if(t>m[0]&&t<=m[1]||t>=h[0]&&t<=h[1]){({index:o}=r[u]);break}}else{var b=Math.min(s,v),g=Math.max(s,v);if(t>(b+d)/2&&t<=(g+d)/2){({index:o}=r[u]);break}}}else if(e){for(var O=0;O<c;O++)if(0===O&&t<=(e[O].coordinate+e[O+1].coordinate)/2||O>0&&O<c-1&&t>(e[O].coordinate+e[O-1].coordinate)/2&&t<=(e[O].coordinate+e[O+1].coordinate)/2||O===c-1&&t>(e[O].coordinate+e[O-1].coordinate)/2){({index:o}=e[O]);break}}return o},h=(t,e,r)=>{if(e&&r){var{width:n,height:a}=r,{align:i,verticalAlign:o,layout:c}=e;if(("vertical"===c||"horizontal"===c&&"middle"===o)&&"center"!==i&&(0,l.Et)(t[i]))return d(d({},t),{},{[i]:t[i]+(n||0)});if(("horizontal"===c||"vertical"===c&&"center"===i)&&"middle"!==o&&(0,l.Et)(t[o]))return d(d({},t),{},{[o]:t[o]+(a||0)})}return t},p=(t,e)=>"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e,y=(t,e,r,n)=>{if(n)return t.map(t=>t.coordinate);var a,i,o=t.map(t=>(t.coordinate===e&&(a=!0),t.coordinate===r&&(i=!0),t.coordinate));return a||o.push(e),i||o.push(r),o},m=(t,e,r)=>{if(!t)return null;var{duplicateDomain:n,type:a,range:i,scale:o,realScaleType:c,isCategorical:u,categoricalDomain:s,tickCount:d,ticks:v,niceTicks:f,axisType:h}=t;if(!o)return null;var p="scaleBand"===c&&o.bandwidth?o.bandwidth()/2:2,y=(e||r)&&"category"===a&&o.bandwidth?o.bandwidth()/p:0;return(y="angleAxis"===h&&i&&i.length>=2?2*(0,l.sA)(i[0]-i[1])*y:y,e&&(v||f))?(v||f||[]).map((t,e)=>({coordinate:o(n?n.indexOf(t):t)+y,value:t,offset:y,index:e})).filter(t=>!(0,l.M8)(t.coordinate)):u&&s?s.map((t,e)=>({coordinate:o(t)+y,value:t,index:e,offset:y})):o.ticks&&!r&&null!=d?o.ticks(d).map((t,e)=>({coordinate:o(t)+y,value:t,offset:y,index:e})):o.domain().map((t,e)=>({coordinate:o(t)+y,value:n?n[t]:t,index:e,offset:y}))},b=t=>{var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),a=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,o=t(e[0]),c=t(e[r-1]);(o<a||o>i||c<a||c>i)&&t.domain([e[0],e[r-1]])}},g=(t,e)=>{if(!e||2!==e.length||!(0,l.Et)(e[0])||!(0,l.Et)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),a=[t[0],t[1]];return(!(0,l.Et)(t[0])||t[0]<r)&&(a[0]=r),(!(0,l.Et)(t[1])||t[1]>n)&&(a[1]=n),a[0]>n&&(a[0]=n),a[1]<r&&(a[1]=r),a},O={sign:t=>{var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var a=0,i=0,o=0;o<e;++o){var c=(0,l.M8)(t[o][r][1])?t[o][r][0]:t[o][r][1];c>=0?(t[o][r][0]=a,t[o][r][1]=a+c,a=t[o][r][1]):(t[o][r][0]=i,t[o][r][1]=i+c,i=t[o][r][1])}},expand:c.qI,none:c.YW,silhouette:c.e9,wiggle:c.Re,positive:t=>{var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var a=0,i=0;i<e;++i){var o=(0,l.M8)(t[i][r][1])?t[i][r][0]:t[i][r][1];o>=0?(t[i][r][0]=a,t[i][r][1]=a+o,a=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},w=(t,e,r)=>{var n=O[r];return(0,c.t$)().keys(e).value((t,e)=>+v(t,e,0)).order(c.rM).offset(n)(t)};function x(t){return null==t?void 0:String(t)}var j=t=>{var{axis:e,ticks:r,offset:n,bandSize:a,entry:i,index:o}=t;if("category"===e.type)return r[o]?r[o].coordinate+n:null;var c=v(i,e.dataKey,e.scale.domain()[o]);return(0,l.uy)(c)?null:e.scale(c)-a/2+n},P=t=>{var{numericAxis:e}=t,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),a=Math.max(r[0],r[1]);return n<=0&&a>=0?0:a<0?a:n}return r[0]},I=t=>{var e=t.flat(2).filter(l.Et);return[Math.min(...e),Math.max(...e)]},M=t=>[t[0]===1/0?0:t[0],t[1]===-1/0?0:t[1]],E=(t,e,r)=>{if(null!=t)return M(Object.keys(t).reduce((n,a)=>{var{stackedData:i}=t[a],o=i.reduce((t,n)=>{var a=I(n.slice(e,r+1));return[Math.min(t[0],a[0]),Math.max(t[1],a[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},S=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,N=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,k=(t,e,r)=>{if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var i=a()(e,t=>t.coordinate),o=1/0,c=1,l=i.length;c<l;c++){var u=i[c],s=i[c-1];o=Math.min((u.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function A(t){var{tooltipEntrySettings:e,dataKey:r,payload:n,value:a,name:i}=t;return d(d({},e),{},{dataKey:r,payload:n,value:a,name:i})}function D(t,e){return t?String(t):"string"==typeof e?e:void 0}function C(t,e,r,n,a){return"horizontal"===r||"vertical"===r?t>=a.left&&t<=a.left+a.width&&e>=a.top&&e<=a.top+a.height?{x:t,y:e}:null:n?(0,u.yy)({x:t,y:e},n):null}var K=(t,e,r,n)=>{var a=e.find(t=>t&&t.index===r);if(a){if("horizontal"===t)return{x:a.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:a.coordinate};if("centric"===t){var i=a.coordinate,{radius:o}=n;return d(d(d({},n),(0,u.IZ)(n.cx,n.cy,o,i)),{},{angle:i,radius:o})}var c=a.coordinate,{angle:l}=n;return d(d(d({},n),(0,u.IZ)(n.cx,n.cy,c,l)),{},{angle:l,radius:c})}return{x:0,y:0}},T=(t,e)=>"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius},38987:(t,e,r)=>{r.d(e,{e:()=>s,k:()=>d});var n=r(78456),a=r(95322),i=r(28237),o=r(88223),c=r(82645),l=r(13111),u=r(42367),s=(0,n.VP)("touchMove"),d=(0,n.Nc)();d.startListening({actionCreator:s,effect:(t,e)=>{var r=t.payload,n=e.getState(),s=(0,c.au)(n,n.tooltip.settings.shared);if("axis"===s){var d=(0,i.g)(n,(0,o.w)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==d?void 0:d.activeIndex)!=null&&e.dispatch((0,a.Nt)({activeIndex:d.activeIndex,activeDataKey:void 0,activeCoordinate:d.activeCoordinate}))}else if("item"===s){var v,f=r.touches[0],h=document.elementFromPoint(f.clientX,f.clientY);if(!h||!h.getAttribute)return;var p=h.getAttribute(l.F0),y=null!=(v=h.getAttribute(l.um))?v:void 0,m=(0,u.u)(e.getState(),p,y);e.dispatch((0,a.RD)({activeDataKey:y,activeIndex:p,activeCoordinate:m}))}}})},39683:(t,e,r)=>{function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach(function(e){var n,a,i;n=t,a=e,i=r[e],(a=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}r.d(e,{IZ:()=>c,Kg:()=>i,lY:()=>l,yy:()=>f}),r(82015);var i=Math.PI/180,o=t=>180*t/Math.PI,c=(t,e,r,n)=>({x:t+Math.cos(-i*n)*r,y:e+Math.sin(-i*n)*r}),l=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},u=(t,e)=>{var{x:r,y:n}=t,{x:a,y:i}=e;return Math.sqrt((r-a)**2+(n-i)**2)},s=(t,e)=>{var{x:r,y:n}=t,{cx:a,cy:i}=e,c=u({x:r,y:n},{x:a,y:i});if(c<=0)return{radius:c,angle:0};var l=Math.acos((r-a)/c);return n>i&&(l=2*Math.PI-l),{radius:c,angle:o(l),angleInRadian:l}},d=t=>{var{startAngle:e,endAngle:r}=t,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},v=(t,e)=>{var{startAngle:r,endAngle:n}=e;return t+360*Math.min(Math.floor(r/360),Math.floor(n/360))},f=(t,e)=>{var r,{x:n,y:i}=t,{radius:o,angle:c}=s({x:n,y:i},e),{innerRadius:l,outerRadius:u}=e;if(o<l||o>u||0===o)return null;var{startAngle:f,endAngle:h}=d(e),p=c;if(f<=h){for(;p>h;)p-=360;for(;p<f;)p+=360;r=p>=f&&p<=h}else{for(;p>f;)p-=360;for(;p<h;)p+=360;r=p>=h&&p<=f}return r?a(a({},e),{},{radius:o,angle:v(p,e)}):null}},47684:(t,e,r)=>{r.d(e,{l:()=>f});var n=r(77331),a=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,i=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,o=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,c=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,l={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},u=Object.keys(l);class s{static parse(t){var e,[,r,n]=null!=(e=c.exec(t))?e:[];return new s(parseFloat(r),null!=n?n:"")}constructor(t,e){this.num=t,this.unit=e,this.num=t,this.unit=e,(0,n.M8)(t)&&(this.unit=""),""===e||o.test(e)||(this.num=NaN,this.unit=""),u.includes(e)&&(this.num=t*l[e],this.unit="px")}add(t){return this.unit!==t.unit?new s(NaN,""):new s(this.num+t.num,this.unit)}subtract(t){return this.unit!==t.unit?new s(NaN,""):new s(this.num-t.num,this.unit)}multiply(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new s(NaN,""):new s(this.num*t.num,this.unit||t.unit)}divide(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new s(NaN,""):new s(this.num/t.num,this.unit||t.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,n.M8)(this.num)}}function d(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,[,n,o,c]=null!=(r=a.exec(e))?r:[],l=s.parse(null!=n?n:""),u=s.parse(null!=c?c:""),d="*"===o?l.multiply(u):l.divide(u);if(d.isNaN())return"NaN";e=e.replace(a,d.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var v,[,f,h,p]=null!=(v=i.exec(e))?v:[],y=s.parse(null!=f?f:""),m=s.parse(null!=p?p:""),b="+"===h?y.add(m):y.subtract(m);if(b.isNaN())return"NaN";e=e.replace(i,b.toString())}return e}var v=/\(([^()]*)\)/;function f(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e,r=t;null!=(e=v.exec(r));){var[,n]=e;r=r.replace(v,d(n))}return r}(e),e=d(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}},50765:(t,e,r)=>{r.d(e,{z:()=>n});var n=t=>{var{ticks:e,label:r,labelGapWithTick:n=5,tickSize:a=0,tickMargin:i=0}=t,o=0;if(e){e.forEach(t=>{if(t){var e=t.getBoundingClientRect();e.width>o&&(o=e.width)}});var c=r?r.getBoundingClientRect().width:0;return Math.round(o+(a+i)+c+(r?n:0))}return 0}},54621:(t,e,r)=>{r.d(e,{R:()=>a});var n=7311!=r.j&&null,a=function(t,e){for(var r=arguments.length,a=Array(r>2?r-2:0),i=2;i<r;i++)a[i-2]=arguments[i];if(n&&"undefined"!=typeof console&&console.warn&&(void 0===e&&console.warn("LogUtils requires an error message argument"),!t))if(void 0===e)console.warn("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var o=0;console.warn(e.replace(/%s/g,()=>a[o++]))}}},77331:(t,e,r)=>{r.d(e,{CG:()=>f,Dj:()=>h,Et:()=>l,F4:()=>v,M8:()=>o,NF:()=>d,Zb:()=>m,_3:()=>c,eP:()=>p,sA:()=>i,uy:()=>y,vh:()=>u});var n=r(67063),a=r.n(n),i=t=>0===t?0:t>0?1:-1,o=t=>"number"==typeof t&&t!=+t,c=t=>"string"==typeof t&&t.indexOf("%")===t.length-1,l=t=>("number"==typeof t||t instanceof Number)&&!o(t),u=t=>l(t)||"string"==typeof t,s=0,d=t=>{var e=++s;return"".concat(t||"").concat(e)},v=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!l(t)&&"string"!=typeof t)return n;if(c(t)){if(null==e)return n;var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return o(r)&&(r=n),a&&null!=e&&r>e&&(r=e),r},f=t=>{if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},h=(t,e)=>l(t)&&l(e)?r=>t+r*(e-t):()=>e;function p(t,e,r){if(t&&t.length)return t.find(t=>t&&("function"==typeof e?e(t):a()(t,e))===r)}var y=t=>null==t,m=t=>y(t)?t:"".concat(t.charAt(0).toUpperCase()).concat(t.slice(1))},80020:(t,e,r)=>{r.d(e,{J9:()=>p,aS:()=>f});var n=r(67063),a=r.n(n),i=r(82015),o=r(26851),c=r(77331),l=r(50991),u=t=>"string"==typeof t?t:t?t.displayName||t.name||"Component":"",s=null,d=null,v=t=>{if(t===s&&Array.isArray(d))return d;var e=[];return i.Children.forEach(t,t=>{(0,c.uy)(t)||((0,o.isFragment)(t)?e=e.concat(v(t.props.children)):e.push(t))}),d=e,s=t,e};function f(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(t=>u(t)):[u(e)],v(t).forEach(t=>{var e=a()(t,"type.displayName")||a()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}var h=(t,e,r,n)=>{var a,i=null!=(a=n&&(null===l.VU||void 0===l.VU?void 0:l.VU[n]))?a:[];return e.startsWith("data-")||"function"!=typeof t&&(n&&i.includes(e)||l.QQ.includes(e))||r&&l.j2.includes(e)},p=(t,e,r)=>{if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,i.isValidElement)(t)&&(n=t.props),"object"!=typeof n&&"function"!=typeof n)return null;var a={};return Object.keys(n).forEach(t=>{var i;h(null==(i=n)?void 0:i[t],t,e,r)&&(a[t]=n[t])}),a}},89192:(t,e,r)=>{r.d(e,{b:()=>n});function n(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}},91055:(t,e,r)=>{r.d(e,{l3:()=>m,m7:()=>b});var n=r(82015),a=r(34797),i=r(87654),o=new(r(90955)).A,c="recharts.syncEvent.tooltip",l="recharts.syncEvent.brush",u=r(58971),s=r(95322),d=r(97900),v=r(77741);function f(t){return t.tooltip.syncInteraction}var h=r(28158),p=r(87769),y=()=>{};function m(){var t,e,r,d,f,m,b,g,O,w,x,j=(0,a.j)();(0,n.useEffect)(()=>{j((0,u.dl)())},[j]),t=(0,a.G)(i.lZ),e=(0,a.G)(i.pH),r=(0,a.j)(),d=(0,a.G)(i.hX),f=(0,a.G)(v.R4),m=(0,h.WX)(),b=(0,h.sk)(),g=(0,a.G)(t=>t.rootProps.className),(0,n.useEffect)(()=>{if(null==t)return y;var n=(n,a,i)=>{if(e!==i&&t===n){if("index"===d)return void r(a);if(null!=f){if("function"==typeof d){var o,c=d(f,{activeTooltipIndex:null==a.payload.index?void 0:Number(a.payload.index),isTooltipActive:a.payload.active,activeIndex:null==a.payload.index?void 0:Number(a.payload.index),activeLabel:a.payload.label,activeDataKey:a.payload.dataKey,activeCoordinate:a.payload.coordinate});o=f[c]}else"value"===d&&(o=f.find(t=>String(t.value)===a.payload.label));var{coordinate:l}=a.payload;if(null==o||!1===a.payload.active||null==l||null==b)return void r((0,s.E1)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:u,y:v}=l,h=Math.min(u,b.x+b.width),p=Math.min(v,b.y+b.height),y={x:"horizontal"===m?o.coordinate:h,y:"horizontal"===m?p:o.coordinate};r((0,s.E1)({active:a.payload.active,coordinate:y,dataKey:a.payload.dataKey,index:String(o.index),label:a.payload.label}))}}};return o.on(c,n),()=>{o.off(c,n)}},[g,r,e,t,d,f,m,b]),O=(0,a.G)(i.lZ),w=(0,a.G)(i.pH),x=(0,a.j)(),(0,n.useEffect)(()=>{if(null==O)return y;var t=(t,e,r)=>{w!==r&&O===t&&x((0,p.M)(e))};return o.on(l,t),()=>{o.off(l,t)}},[x,w,O])}function b(t,e,r,l,u,v){var h=(0,a.G)(r=>(0,d.dp)(r,t,e)),p=(0,a.G)(i.pH),y=(0,a.G)(i.lZ),m=(0,a.G)(i.hX),b=(0,a.G)(f),g=null==b?void 0:b.active;(0,n.useEffect)(()=>{if(!g&&null!=y&&null!=p){var t=(0,s.E1)({active:v,coordinate:r,dataKey:h,index:u,label:"number"==typeof l?String(l):l});o.emit(c,y,t,p)}},[g,r,h,u,l,p,y,m,v])}},95322:(t,e,r)=>{r.d(e,{E1:()=>y,En:()=>b,Ix:()=>c,ML:()=>f,Nt:()=>h,RD:()=>s,UF:()=>u,XB:()=>l,jF:()=>p,k_:()=>i,o4:()=>m,oP:()=>d,xS:()=>v});var n=r(78456),a=r(7555),i={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:i,hover:i},axisInteraction:{click:i,hover:i},keyboardInteraction:i,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(t,e){t.tooltipItemPayloads.push((0,a.h4)(e.payload))},removeTooltipEntrySettings(t,e){var r=(0,n.ss)(t).tooltipItemPayloads.indexOf((0,a.h4)(e.payload));r>-1&&t.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(t,e){t.settings=e.payload},setActiveMouseOverItemIndex(t,e){t.syncInteraction.active=!1,t.keyboardInteraction.active=!1,t.itemInteraction.hover.active=!0,t.itemInteraction.hover.index=e.payload.activeIndex,t.itemInteraction.hover.dataKey=e.payload.activeDataKey,t.itemInteraction.hover.coordinate=e.payload.activeCoordinate},mouseLeaveChart(t){t.itemInteraction.hover.active=!1,t.axisInteraction.hover.active=!1},mouseLeaveItem(t){t.itemInteraction.hover.active=!1},setActiveClickItemIndex(t,e){t.syncInteraction.active=!1,t.itemInteraction.click.active=!0,t.keyboardInteraction.active=!1,t.itemInteraction.click.index=e.payload.activeIndex,t.itemInteraction.click.dataKey=e.payload.activeDataKey,t.itemInteraction.click.coordinate=e.payload.activeCoordinate},setMouseOverAxisIndex(t,e){t.syncInteraction.active=!1,t.axisInteraction.hover.active=!0,t.keyboardInteraction.active=!1,t.axisInteraction.hover.index=e.payload.activeIndex,t.axisInteraction.hover.dataKey=e.payload.activeDataKey,t.axisInteraction.hover.coordinate=e.payload.activeCoordinate},setMouseClickAxisIndex(t,e){t.syncInteraction.active=!1,t.keyboardInteraction.active=!1,t.axisInteraction.click.active=!0,t.axisInteraction.click.index=e.payload.activeIndex,t.axisInteraction.click.dataKey=e.payload.activeDataKey,t.axisInteraction.click.coordinate=e.payload.activeCoordinate},setSyncInteraction(t,e){t.syncInteraction=e.payload},setKeyboardInteraction(t,e){t.keyboardInteraction.active=e.payload.active,t.keyboardInteraction.index=e.payload.activeIndex,t.keyboardInteraction.coordinate=e.payload.activeCoordinate,t.keyboardInteraction.dataKey=e.payload.activeDataKey}}}),{addTooltipEntrySettings:c,removeTooltipEntrySettings:l,setTooltipSettingsState:u,setActiveMouseOverItemIndex:s,mouseLeaveItem:d,mouseLeaveChart:v,setActiveClickItemIndex:f,setMouseOverAxisIndex:h,setMouseClickAxisIndex:p,setSyncInteraction:y,setKeyboardInteraction:m}=o.actions,b=o.reducer}};