"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/admin/experimental/addon-builder-components_flow_ActionNode_tsx-e3ef49fc"],{

/***/ "(pages-dir-browser)/./components/flow/ActionNode.tsx":
/*!****************************************!*\
  !*** ./components/flow/ActionNode.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var reactflow__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactflow */ \"(pages-dir-browser)/../../node_modules/.pnpm/reactflow@11.11.4_@types+re_67d5462d77eed4181e6818cd1ca61a06/node_modules/reactflow/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionButton,AccordionIcon,AccordionItem,AccordionPanel,Alert,AlertDescription,AlertIcon,Badge,Box,Button,Code,Collapse,Divider,FormControl,FormLabel,HStack,IconButton,Input,Modal,ModalBody,ModalCloseButton,ModalContent,ModalHeader,ModalOverlay,NumberDecrementStepper,NumberIncrementStepper,NumberInput,NumberInputField,NumberInputStepper,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure!=!@chakra-ui/react */ \"(pages-dir-browser)/__barrel_optimize__?names=Accordion,AccordionButton,AccordionIcon,AccordionItem,AccordionPanel,Alert,AlertDescription,AlertIcon,Badge,Box,Button,Code,Collapse,Divider,FormControl,FormLabel,HStack,IconButton,Input,Modal,ModalBody,ModalCloseButton,ModalContent,ModalHeader,ModalOverlay,NumberDecrementStepper,NumberIncrementStepper,NumberInput,NumberInputField,NumberInputStepper,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiCopy,FiEye,FiEyeOff,FiPlus,FiSettings,FiTarget,FiTrash2!=!react-icons/fi */ \"(pages-dir-browser)/__barrel_optimize__?names=FiCopy,FiEye,FiEyeOff,FiPlus,FiSettings,FiTarget,FiTrash2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/ThemeContext */ \"(pages-dir-browser)/./contexts/ThemeContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst actionTypes = [\n    {\n        value: 'sendMessage',\n        label: '💬 Send Message',\n        category: 'Message'\n    },\n    {\n        value: 'sendEmbed',\n        label: '📋 Send Embed',\n        category: 'Message'\n    },\n    {\n        value: 'editMessage',\n        label: '✏️ Edit Message',\n        category: 'Message'\n    },\n    {\n        value: 'deleteMessage',\n        label: '🗑️ Delete Message',\n        category: 'Message'\n    },\n    {\n        value: 'addReaction',\n        label: '👍 Add Reaction',\n        category: 'Message'\n    },\n    {\n        value: 'removeReaction',\n        label: '👎 Remove Reaction',\n        category: 'Message'\n    },\n    {\n        value: 'addRole',\n        label: '🎭 Add Role',\n        category: 'Roles'\n    },\n    {\n        value: 'removeRole',\n        label: '🎭 Remove Role',\n        category: 'Roles'\n    },\n    {\n        value: 'kickUser',\n        label: '👢 Kick User',\n        category: 'Moderation'\n    },\n    {\n        value: 'banUser',\n        label: '🔨 Ban User',\n        category: 'Moderation'\n    },\n    {\n        value: 'timeoutUser',\n        label: '⏰ Timeout User',\n        category: 'Moderation'\n    },\n    {\n        value: 'unbanUser',\n        label: '🔓 Unban User',\n        category: 'Moderation'\n    },\n    {\n        value: 'createChannel',\n        label: '📺 Create Channel',\n        category: 'Channel'\n    },\n    {\n        value: 'deleteChannel',\n        label: '🗑️ Delete Channel',\n        category: 'Channel'\n    },\n    {\n        value: 'lockChannel',\n        label: '🔒 Lock Channel',\n        category: 'Channel'\n    },\n    {\n        value: 'unlockChannel',\n        label: '🔓 Unlock Channel',\n        category: 'Channel'\n    },\n    {\n        value: 'sendDM',\n        label: '📬 Send DM',\n        category: 'Message'\n    },\n    {\n        value: 'createThread',\n        label: '🧵 Create Thread',\n        category: 'Channel'\n    },\n    {\n        value: 'pinMessage',\n        label: '📌 Pin Message',\n        category: 'Message'\n    },\n    {\n        value: 'unpinMessage',\n        label: '📌 Unpin Message',\n        category: 'Message'\n    }\n];\n// Available variables organized by category\nconst availableVariables = {\n    user: [\n        {\n            name: '{user.id}',\n            description: 'User ID',\n            icon: '🆔'\n        },\n        {\n            name: '{user.username}',\n            description: 'Username',\n            icon: '👤'\n        },\n        {\n            name: '{user.displayName}',\n            description: 'Display Name',\n            icon: '📝'\n        },\n        {\n            name: '{user.tag}',\n            description: 'User Tag (username#0000)',\n            icon: '🏷️'\n        },\n        {\n            name: '{user.mention}',\n            description: 'User Mention (<@id>)',\n            icon: '📢'\n        },\n        {\n            name: '{user.avatar}',\n            description: 'Avatar URL',\n            icon: '🖼️'\n        },\n        {\n            name: '{user.createdAt}',\n            description: 'Account Creation Date',\n            icon: '📅'\n        },\n        {\n            name: '{user.joinedAt}',\n            description: 'Server Join Date',\n            icon: '🚪'\n        },\n        {\n            name: '{user.roles}',\n            description: 'User Roles',\n            icon: '🎭'\n        },\n        {\n            name: '{user.permissions}',\n            description: 'User Permissions',\n            icon: '🔐'\n        }\n    ],\n    channel: [\n        {\n            name: '{channel.id}',\n            description: 'Channel ID',\n            icon: '🆔'\n        },\n        {\n            name: '{channel.name}',\n            description: 'Channel Name',\n            icon: '📺'\n        },\n        {\n            name: '{channel.mention}',\n            description: 'Channel Mention (<#id>)',\n            icon: '📢'\n        },\n        {\n            name: '{channel.type}',\n            description: 'Channel Type',\n            icon: '📋'\n        },\n        {\n            name: '{channel.topic}',\n            description: 'Channel Topic',\n            icon: '💬'\n        },\n        {\n            name: '{channel.memberCount}',\n            description: 'Member Count',\n            icon: '👥'\n        },\n        {\n            name: '{channel.createdAt}',\n            description: 'Channel Creation Date',\n            icon: '📅'\n        }\n    ],\n    server: [\n        {\n            name: '{server.id}',\n            description: 'Server ID',\n            icon: '🆔'\n        },\n        {\n            name: '{server.name}',\n            description: 'Server Name',\n            icon: '🏠'\n        },\n        {\n            name: '{server.icon}',\n            description: 'Server Icon URL',\n            icon: '🖼️'\n        },\n        {\n            name: '{server.memberCount}',\n            description: 'Member Count',\n            icon: '👥'\n        },\n        {\n            name: '{server.createdAt}',\n            description: 'Server Creation Date',\n            icon: '📅'\n        },\n        {\n            name: '{server.owner}',\n            description: 'Server Owner',\n            icon: '👑'\n        },\n        {\n            name: '{server.boostLevel}',\n            description: 'Server Boost Level',\n            icon: '🚀'\n        },\n        {\n            name: '{server.boostCount}',\n            description: 'Server Boost Count',\n            icon: '💎'\n        }\n    ],\n    message: [\n        {\n            name: '{message.id}',\n            description: 'Message ID',\n            icon: '🆔'\n        },\n        {\n            name: '{message.content}',\n            description: 'Message Content',\n            icon: '💬'\n        },\n        {\n            name: '{message.author}',\n            description: 'Message Author',\n            icon: '👤'\n        },\n        {\n            name: '{message.channel}',\n            description: 'Message Channel',\n            icon: '📺'\n        },\n        {\n            name: '{message.createdAt}',\n            description: 'Message Creation Date',\n            icon: '📅'\n        },\n        {\n            name: '{message.editedAt}',\n            description: 'Message Edit Date',\n            icon: '✏️'\n        },\n        {\n            name: '{message.reactions}',\n            description: 'Message Reactions',\n            icon: '👍'\n        },\n        {\n            name: '{message.attachments}',\n            description: 'Message Attachments',\n            icon: '📎'\n        }\n    ],\n    api: [\n        {\n            name: '{response.data}',\n            description: 'API Response Data',\n            icon: '📊'\n        },\n        {\n            name: '{response.status}',\n            description: 'HTTP Status Code',\n            icon: '🔢'\n        },\n        {\n            name: '{response.headers}',\n            description: 'Response Headers',\n            icon: '📋'\n        },\n        {\n            name: '{response.message}',\n            description: 'Response Message',\n            icon: '💬'\n        },\n        {\n            name: '{response.error}',\n            description: 'Error Message',\n            icon: '❌'\n        }\n    ],\n    random: [\n        {\n            name: '{random.number}',\n            description: 'Random Number (1-100)',\n            icon: '🎲'\n        },\n        {\n            name: '{random.uuid}',\n            description: 'Random UUID',\n            icon: '🆔'\n        },\n        {\n            name: '{random.choice}',\n            description: 'Random Choice from Array',\n            icon: '🎯'\n        },\n        {\n            name: '{random.color}',\n            description: 'Random Hex Color',\n            icon: '🎨'\n        }\n    ],\n    date: [\n        {\n            name: '{date.now}',\n            description: 'Current Date/Time',\n            icon: '⏰'\n        },\n        {\n            name: '{date.today}',\n            description: 'Today\\'s Date',\n            icon: '📅'\n        },\n        {\n            name: '{date.timestamp}',\n            description: 'Unix Timestamp',\n            icon: '🕐'\n        },\n        {\n            name: '{date.iso}',\n            description: 'ISO Date String',\n            icon: '📝'\n        }\n    ]\n};\nconst ActionNode = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s((param)=>{\n    let { data, selected, id, updateNodeData: updateParentNodeData } = param;\n    var _nodeData_embed, _nodeData_embed1, _nodeData_embed2, _nodeData_embed3, _nodeData_embed4, _nodeData_embed5, _nodeData_embed6, _nodeData_embed_author, _nodeData_embed7, _nodeData_embed_author1, _nodeData_embed8, _nodeData_embed_author2, _nodeData_embed9, _nodeData_embed_footer, _nodeData_embed10, _nodeData_embed_footer1, _nodeData_embed11, _nodeData_embed_fields, _nodeData_embed12, _nodeData_embed13;\n    _s();\n    const { currentScheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const { isOpen, onOpen, onClose } = (0,_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)();\n    const [nodeData, setNodeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ActionNode.useState\": ()=>({\n                embed: {\n                    fields: [],\n                    author: {\n                        name: ''\n                    },\n                    footer: {\n                        text: ''\n                    }\n                },\n                ...data\n            })\n    }[\"ActionNode.useState\"]);\n    const [showVariables, setShowVariables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [embedPreview, setEmbedPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guildData, setGuildData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingGuildData, setLoadingGuildData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateNodeData = (updates)=>{\n        setNodeData((prev)=>({\n                ...prev,\n                ...updates\n            }));\n    };\n    const handleModalClose = ()=>{\n        // Update parent nodes array when modal closes\n        if (updateParentNodeData && id) {\n            updateParentNodeData(id, nodeData);\n        }\n        onClose();\n    };\n    const getActionLabel = (actionType)=>{\n        var _actionTypes_find;\n        return ((_actionTypes_find = actionTypes.find((a)=>a.value === actionType)) === null || _actionTypes_find === void 0 ? void 0 : _actionTypes_find.label) || actionType;\n    };\n    const getActionIcon = (actionType)=>{\n        const action = actionTypes.find((a)=>a.value === actionType);\n        return (action === null || action === void 0 ? void 0 : action.label.split(' ')[0]) || '🎯';\n    };\n    const copyVariable = (variable)=>{\n        navigator.clipboard.writeText(variable);\n    };\n    const fetchGuildData = async ()=>{\n        if (guildData || loadingGuildData) return; // Don't fetch if already loaded or loading\n        setLoadingGuildData(true);\n        try {\n            const response = await fetch('/api/admin/experimental/addon-builder/guild-data');\n            if (response.ok) {\n                const data = await response.json();\n                setGuildData({\n                    channels: data.channels,\n                    roles: data.roles,\n                    members: data.members\n                });\n            } else {\n                console.error('Failed to fetch guild data:', await response.text());\n            }\n        } catch (error) {\n            console.error('Error fetching guild data:', error);\n        } finally{\n            setLoadingGuildData(false);\n        }\n    };\n    // Fetch guild data when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActionNode.useEffect\": ()=>{\n            if (isOpen) {\n                fetchGuildData();\n            }\n        }\n    }[\"ActionNode.useEffect\"], [\n        isOpen\n    ]);\n    const addEmbedField = ()=>{\n        var _nodeData_embed;\n        const currentFields = ((_nodeData_embed = nodeData.embed) === null || _nodeData_embed === void 0 ? void 0 : _nodeData_embed.fields) || [];\n        updateNodeData({\n            embed: {\n                ...nodeData.embed,\n                fields: [\n                    ...currentFields,\n                    {\n                        name: '',\n                        value: '',\n                        inline: false\n                    }\n                ]\n            }\n        });\n    };\n    const updateEmbedField = (index, field, value)=>{\n        var _nodeData_embed;\n        const currentFields = ((_nodeData_embed = nodeData.embed) === null || _nodeData_embed === void 0 ? void 0 : _nodeData_embed.fields) || [];\n        const newFields = [\n            ...currentFields\n        ];\n        newFields[index] = {\n            ...newFields[index],\n            [field]: value\n        };\n        updateNodeData({\n            embed: {\n                ...nodeData.embed,\n                fields: newFields\n            }\n        });\n    };\n    const removeEmbedField = (index)=>{\n        var _nodeData_embed;\n        const currentFields = ((_nodeData_embed = nodeData.embed) === null || _nodeData_embed === void 0 ? void 0 : _nodeData_embed.fields) || [];\n        const newFields = currentFields.filter((_, i)=>i !== index);\n        updateNodeData({\n            embed: {\n                ...nodeData.embed,\n                fields: newFields\n            }\n        });\n    };\n    const renderEmbedPreview = ()=>{\n        var _embed_author, _embed_footer;\n        const embed = nodeData.embed || {};\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n            spacing: 3,\n            align: \"stretch\",\n            maxW: \"500px\",\n            children: [\n                nodeData.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                    bg: currentScheme.colors.surface,\n                    border: \"1px solid\",\n                    borderColor: currentScheme.colors.border,\n                    borderRadius: \"md\",\n                    p: 3,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                            fontSize: \"sm\",\n                            color: currentScheme.colors.text,\n                            fontWeight: \"medium\",\n                            children: \"\\uD83D\\uDCE9 Message Content:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                            fontSize: \"sm\",\n                            color: currentScheme.colors.text,\n                            mt: 1,\n                            children: nodeData.message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                    bg: currentScheme.colors.surface,\n                    border: \"1px solid\",\n                    borderColor: currentScheme.colors.border,\n                    borderRadius: \"md\",\n                    p: 4,\n                    borderLeft: \"4px solid \".concat(embed.color || '#5865F2'),\n                    children: [\n                        ((_embed_author = embed.author) === null || _embed_author === void 0 ? void 0 : _embed_author.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                            spacing: 2,\n                            mb: 2,\n                            children: [\n                                embed.author.iconUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                    w: 6,\n                                    h: 6,\n                                    borderRadius: \"full\",\n                                    bg: \"gray.300\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    fontSize: \"xs\",\n                                    children: \"\\uD83D\\uDC64\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    fontSize: \"sm\",\n                                    fontWeight: \"bold\",\n                                    color: currentScheme.colors.text,\n                                    children: embed.author.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, undefined),\n                        embed.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                            fontSize: \"md\",\n                            fontWeight: \"bold\",\n                            color: currentScheme.colors.text,\n                            mb: 2,\n                            children: embed.title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, undefined),\n                        embed.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                            fontSize: \"sm\",\n                            color: currentScheme.colors.textSecondary,\n                            mb: 3,\n                            children: embed.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, undefined),\n                        embed.fields && embed.fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                            spacing: 2,\n                            align: \"stretch\",\n                            mb: 3,\n                            children: embed.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                            fontSize: \"sm\",\n                                            fontWeight: \"bold\",\n                                            color: currentScheme.colors.text,\n                                            children: field.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                            fontSize: \"sm\",\n                                            color: currentScheme.colors.textSecondary,\n                                            children: field.value\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, undefined),\n                        ((_embed_footer = embed.footer) === null || _embed_footer === void 0 ? void 0 : _embed_footer.text) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                            spacing: 2,\n                            mt: 3,\n                            pt: 2,\n                            borderTop: \"1px solid\",\n                            borderColor: currentScheme.colors.border,\n                            children: [\n                                embed.footer.iconUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                    w: 4,\n                                    h: 4,\n                                    borderRadius: \"full\",\n                                    bg: \"gray.300\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    fontSize: \"xs\",\n                                    children: \"ℹ️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    fontSize: \"xs\",\n                                    color: currentScheme.colors.textSecondary,\n                                    children: embed.footer.text\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 13\n                        }, undefined),\n                        embed.timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                            fontSize: \"xs\",\n                            color: currentScheme.colors.textSecondary,\n                            mt: 2,\n                            children: [\n                                \"\\uD83D\\uDD52 \",\n                                new Date().toLocaleString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n            lineNumber: 300,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderVariablesList = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Collapse, {\n            in: showVariables,\n            animateOpacity: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                bg: currentScheme.colors.surface,\n                border: \"1px solid\",\n                borderColor: currentScheme.colors.border,\n                borderRadius: \"md\",\n                p: 4,\n                mt: 3,\n                maxH: \"400px\",\n                overflowY: \"auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Accordion, {\n                    allowMultiple: true,\n                    children: Object.entries(availableVariables).map((param)=>{\n                        let [category, variables] = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AccordionItem, {\n                            border: \"none\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AccordionButton, {\n                                    px: 0,\n                                    py: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                            flex: \"1\",\n                                            textAlign: \"left\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                fontSize: \"sm\",\n                                                fontWeight: \"bold\",\n                                                color: currentScheme.colors.text,\n                                                textTransform: \"capitalize\",\n                                                children: [\n                                                    category,\n                                                    \" Variables\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AccordionIcon, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AccordionPanel, {\n                                    px: 0,\n                                    py: 2,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                        spacing: 2,\n                                        align: \"stretch\",\n                                        children: variables.map((variable)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                spacing: 2,\n                                                p: 2,\n                                                bg: currentScheme.colors.background,\n                                                borderRadius: \"md\",\n                                                cursor: \"pointer\",\n                                                _hover: {\n                                                    bg: currentScheme.colors.surface\n                                                },\n                                                onClick: ()=>copyVariable(variable.name),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                        fontSize: \"sm\",\n                                                        children: variable.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Code, {\n                                                        fontSize: \"xs\",\n                                                        colorScheme: \"blue\",\n                                                        children: variable.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                        fontSize: \"xs\",\n                                                        color: currentScheme.colors.textSecondary,\n                                                        flex: \"1\",\n                                                        children: variable.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.IconButton, {\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCopy, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        size: \"xs\",\n                                                        variant: \"ghost\",\n                                                        \"aria-label\": \"Copy variable\",\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            copyVariable(variable.name);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, variable.name, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 13\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n            lineNumber: 410,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                bg: currentScheme.colors.surface,\n                border: \"2px solid \".concat(selected ? '#a855f7' : currentScheme.colors.border),\n                borderRadius: \"md\",\n                p: 2,\n                minW: \"140px\",\n                maxW: \"180px\",\n                boxShadow: \"sm\",\n                position: \"relative\",\n                _hover: {\n                    boxShadow: 'md',\n                    transform: 'translateY(-1px)'\n                },\n                transition: \"all 0.2s\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_5__.Handle, {\n                        type: \"target\",\n                        position: reactflow__WEBPACK_IMPORTED_MODULE_5__.Position.Top,\n                        style: {\n                            background: '#a855f7',\n                            border: \"2px solid \".concat(currentScheme.colors.surface),\n                            width: '12px',\n                            height: '12px',\n                            top: '-6px',\n                            left: '50%',\n                            transform: 'translateX(-50%)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                        spacing: 1,\n                        align: \"stretch\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                justify: \"space-between\",\n                                align: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                        spacing: 1,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                                bg: \"purple.500\",\n                                                color: \"white\",\n                                                borderRadius: \"full\",\n                                                p: 0.5,\n                                                fontSize: \"xs\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTarget, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                fontSize: \"xs\",\n                                                fontWeight: \"bold\",\n                                                color: currentScheme.colors.text,\n                                                children: \"Action\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.IconButton, {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiSettings, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        size: \"xs\",\n                                        variant: \"ghost\",\n                                        onClick: onOpen,\n                                        \"aria-label\": \"Configure action\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                    spacing: 1,\n                                    children: [\n                                        nodeData.actionType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                            fontSize: \"xs\",\n                                            children: getActionIcon(nodeData.actionType)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                            fontSize: \"xs\",\n                                            color: currentScheme.colors.text,\n                                            noOfLines: 1,\n                                            children: nodeData.actionType ? getActionLabel(nodeData.actionType).split(' ').slice(1).join(' ') : 'Select Action'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 11\n                            }, undefined),\n                            nodeData.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    fontSize: \"xs\",\n                                    color: currentScheme.colors.textSecondary,\n                                    noOfLines: 1,\n                                    children: nodeData.message.length > 20 ? nodeData.message.substring(0, 20) + '...' : nodeData.message\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                spacing: 1,\n                                flexWrap: \"wrap\",\n                                children: [\n                                    nodeData.channel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"purple\",\n                                        children: [\n                                            \"#\",\n                                            nodeData.channel\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    nodeData.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"purple\",\n                                        children: [\n                                            \"@\",\n                                            nodeData.role\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    ((_nodeData_embed = nodeData.embed) === null || _nodeData_embed === void 0 ? void 0 : _nodeData_embed.title) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"blue\",\n                                        children: \"\\uD83D\\uDCCB Embed\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                lineNumber: 475,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n                isOpen: isOpen,\n                onClose: handleModalClose,\n                size: \"4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalOverlay, {\n                        bg: \"blackAlpha.600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                        lineNumber: 572,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalContent, {\n                        bg: currentScheme.colors.background,\n                        border: \"2px solid\",\n                        borderColor: \"purple.400\",\n                        maxW: \"1200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalHeader, {\n                                color: currentScheme.colors.text,\n                                children: \"\\uD83C\\uDFAF Configure Action\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalCloseButton, {}, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalBody, {\n                                pb: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                    spacing: 6,\n                                    align: \"stretch\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                    justify: \"space-between\",\n                                                    align: \"center\",\n                                                    mb: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                            fontSize: \"sm\",\n                                                            fontWeight: \"bold\",\n                                                            color: currentScheme.colors.text,\n                                                            children: \"Available Variables\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"ghost\",\n                                                            leftIcon: showVariables ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiEyeOff, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 47\n                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiEye, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 62\n                                                            }, void 0),\n                                                            onClick: ()=>setShowVariables(!showVariables),\n                                                            children: [\n                                                                showVariables ? 'Hide' : 'Show',\n                                                                \" Variables\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                                                    status: \"info\",\n                                                    borderRadius: \"md\",\n                                                    mb: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertIcon, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                                                            fontSize: \"sm\",\n                                                            children: \"\\uD83D\\uDCA1 Use variables in your actions! Click any variable below to copy it. Variables are replaced with actual values when your addon runs.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                renderVariablesList()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Divider, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                    color: currentScheme.colors.text,\n                                                    children: \"Action Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                    value: nodeData.actionType || '',\n                                                    onChange: (e)=>updateNodeData({\n                                                            actionType: e.target.value\n                                                        }),\n                                                    placeholder: \"Select an action type\",\n                                                    bg: currentScheme.colors.background,\n                                                    color: currentScheme.colors.text,\n                                                    borderColor: currentScheme.colors.border,\n                                                    children: Object.entries(actionTypes.reduce((acc, action)=>{\n                                                        if (!acc[action.category]) acc[action.category] = [];\n                                                        acc[action.category].push(action);\n                                                        return acc;\n                                                    }, {})).map((param)=>{\n                                                        let [category, actions] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                                            label: category,\n                                                            children: actions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: action.value,\n                                                                    children: action.label\n                                                                }, action.value, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 21\n                                                                }, undefined))\n                                                        }, category, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 21\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        nodeData.actionType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: nodeData.actionType === 'sendEmbed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                                variant: \"enclosed\",\n                                                colorScheme: \"purple\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.TabList, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                                                children: \"Embed Builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                                                children: \"Preview\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.TabPanels, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Channel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 650,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                    spacing: 2,\n                                                                                    align: \"stretch\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                                                            value: nodeData.channel || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    channel: e.target.value\n                                                                                                }),\n                                                                                            placeholder: loadingGuildData ? 'Loading channels...' : 'Select a channel',\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border,\n                                                                                            isDisabled: loadingGuildData,\n                                                                                            children: guildData === null || guildData === void 0 ? void 0 : guildData.channels.filter((channel)=>channel.type === 'text').map((channel)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: channel.name,\n                                                                                                    children: [\n                                                                                                        \"#\",\n                                                                                                        channel.name\n                                                                                                    ]\n                                                                                                }, channel.id, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 664,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 652,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            value: nodeData.channel || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    channel: e.target.value\n                                                                                                }),\n                                                                                            placeholder: \"Or type: general or {channel.name}\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border,\n                                                                                            size: \"sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 669,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 651,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Message Content (appears above embed)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 683,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                                                                    value: nodeData.message || '',\n                                                                                    onChange: (e)=>updateNodeData({\n                                                                                            message: e.target.value\n                                                                                        }),\n                                                                                    placeholder: \"Hello {user.username}! This text appears above the embed...\",\n                                                                                    bg: currentScheme.colors.background,\n                                                                                    color: currentScheme.colors.text,\n                                                                                    borderColor: currentScheme.colors.border,\n                                                                                    minH: \"100px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 684,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.SimpleGrid, {\n                                                                            columns: 2,\n                                                                            spacing: 4,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                            color: currentScheme.colors.text,\n                                                                                            children: \"Embed Title\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 697,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            value: ((_nodeData_embed1 = nodeData.embed) === null || _nodeData_embed1 === void 0 ? void 0 : _nodeData_embed1.title) || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        title: e.target.value\n                                                                                                    }\n                                                                                                }),\n                                                                                            placeholder: \"Welcome to {server.name}!\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 698,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 696,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                            color: currentScheme.colors.text,\n                                                                                            children: \"Embed Color\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 711,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                            spacing: 2,\n                                                                                            align: \"stretch\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                            type: \"color\",\n                                                                                                            value: ((_nodeData_embed2 = nodeData.embed) === null || _nodeData_embed2 === void 0 ? void 0 : _nodeData_embed2.color) || '#5865F2',\n                                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                                    embed: {\n                                                                                                                        ...nodeData.embed,\n                                                                                                                        color: e.target.value\n                                                                                                                    }\n                                                                                                                }),\n                                                                                                            w: \"60px\",\n                                                                                                            h: \"40px\",\n                                                                                                            p: 1,\n                                                                                                            bg: currentScheme.colors.background,\n                                                                                                            borderColor: currentScheme.colors.border\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 714,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                            value: ((_nodeData_embed3 = nodeData.embed) === null || _nodeData_embed3 === void 0 ? void 0 : _nodeData_embed3.color) || '',\n                                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                                    embed: {\n                                                                                                                        ...nodeData.embed,\n                                                                                                                        color: e.target.value\n                                                                                                                    }\n                                                                                                                }),\n                                                                                                            placeholder: \"#5865F2\",\n                                                                                                            bg: currentScheme.colors.background,\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            borderColor: currentScheme.colors.border,\n                                                                                                            flex: \"1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 726,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 713,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                                    spacing: 1,\n                                                                                                    flexWrap: \"wrap\",\n                                                                                                    children: [\n                                                                                                        '#5865F2',\n                                                                                                        '#57F287',\n                                                                                                        '#FEE75C',\n                                                                                                        '#EB459E',\n                                                                                                        '#ED4245',\n                                                                                                        '#FF6B35',\n                                                                                                        '#00ADB5',\n                                                                                                        '#9B59B6'\n                                                                                                    ].map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                            size: \"xs\",\n                                                                                                            bg: color,\n                                                                                                            w: \"30px\",\n                                                                                                            h: \"20px\",\n                                                                                                            minW: \"30px\",\n                                                                                                            p: 0,\n                                                                                                            onClick: ()=>updateNodeData({\n                                                                                                                    embed: {\n                                                                                                                        ...nodeData.embed,\n                                                                                                                        color\n                                                                                                                    }\n                                                                                                                }),\n                                                                                                            _hover: {\n                                                                                                                transform: 'scale(1.1)'\n                                                                                                            },\n                                                                                                            border: \"1px solid\",\n                                                                                                            borderColor: currentScheme.colors.border\n                                                                                                        }, color, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 740,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 738,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 712,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 710,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 695,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Embed Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 763,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                                                                    value: ((_nodeData_embed4 = nodeData.embed) === null || _nodeData_embed4 === void 0 ? void 0 : _nodeData_embed4.description) || '',\n                                                                                    onChange: (e)=>updateNodeData({\n                                                                                            embed: {\n                                                                                                ...nodeData.embed,\n                                                                                                description: e.target.value\n                                                                                            }\n                                                                                        }),\n                                                                                    placeholder: \"This is the description that appears inside the embed...\",\n                                                                                    bg: currentScheme.colors.background,\n                                                                                    color: currentScheme.colors.text,\n                                                                                    borderColor: currentScheme.colors.border,\n                                                                                    minH: \"100px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 764,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 762,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.SimpleGrid, {\n                                                                            columns: 2,\n                                                                            spacing: 4,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                            color: currentScheme.colors.text,\n                                                                                            children: \"Thumbnail URL\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 779,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            value: ((_nodeData_embed5 = nodeData.embed) === null || _nodeData_embed5 === void 0 ? void 0 : _nodeData_embed5.thumbnail) || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        thumbnail: e.target.value\n                                                                                                    }\n                                                                                                }),\n                                                                                            placeholder: \"https://example.com/image.png\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 780,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 778,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                            color: currentScheme.colors.text,\n                                                                                            children: \"Image URL\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 793,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            value: ((_nodeData_embed6 = nodeData.embed) === null || _nodeData_embed6 === void 0 ? void 0 : _nodeData_embed6.image) || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        image: e.target.value\n                                                                                                    }\n                                                                                                }),\n                                                                                            placeholder: \"https://example.com/image.png\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 794,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 792,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 777,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Author\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 809,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                    spacing: 2,\n                                                                                    align: \"stretch\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            value: ((_nodeData_embed7 = nodeData.embed) === null || _nodeData_embed7 === void 0 ? void 0 : (_nodeData_embed_author = _nodeData_embed7.author) === null || _nodeData_embed_author === void 0 ? void 0 : _nodeData_embed_author.name) || '',\n                                                                                            onChange: (e)=>{\n                                                                                                var _nodeData_embed;\n                                                                                                return updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        author: {\n                                                                                                            ...(_nodeData_embed = nodeData.embed) === null || _nodeData_embed === void 0 ? void 0 : _nodeData_embed.author,\n                                                                                                            name: e.target.value\n                                                                                                        }\n                                                                                                    }\n                                                                                                });\n                                                                                            },\n                                                                                            placeholder: \"Author name\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 811,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.SimpleGrid, {\n                                                                                            columns: 2,\n                                                                                            spacing: 2,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                    value: ((_nodeData_embed8 = nodeData.embed) === null || _nodeData_embed8 === void 0 ? void 0 : (_nodeData_embed_author1 = _nodeData_embed8.author) === null || _nodeData_embed_author1 === void 0 ? void 0 : _nodeData_embed_author1.url) || '',\n                                                                                                    onChange: (e)=>{\n                                                                                                        var _nodeData_embed;\n                                                                                                        return updateNodeData({\n                                                                                                            embed: {\n                                                                                                                ...nodeData.embed,\n                                                                                                                author: {\n                                                                                                                    ...(_nodeData_embed = nodeData.embed) === null || _nodeData_embed === void 0 ? void 0 : _nodeData_embed.author,\n                                                                                                                    url: e.target.value\n                                                                                                                }\n                                                                                                            }\n                                                                                                        });\n                                                                                                    },\n                                                                                                    placeholder: \"Author URL\",\n                                                                                                    bg: currentScheme.colors.background,\n                                                                                                    color: currentScheme.colors.text,\n                                                                                                    borderColor: currentScheme.colors.border\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 825,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                    value: ((_nodeData_embed9 = nodeData.embed) === null || _nodeData_embed9 === void 0 ? void 0 : (_nodeData_embed_author2 = _nodeData_embed9.author) === null || _nodeData_embed_author2 === void 0 ? void 0 : _nodeData_embed_author2.iconUrl) || '',\n                                                                                                    onChange: (e)=>{\n                                                                                                        var _nodeData_embed;\n                                                                                                        return updateNodeData({\n                                                                                                            embed: {\n                                                                                                                ...nodeData.embed,\n                                                                                                                author: {\n                                                                                                                    ...(_nodeData_embed = nodeData.embed) === null || _nodeData_embed === void 0 ? void 0 : _nodeData_embed.author,\n                                                                                                                    iconUrl: e.target.value\n                                                                                                                }\n                                                                                                            }\n                                                                                                        });\n                                                                                                    },\n                                                                                                    placeholder: \"Author icon URL\",\n                                                                                                    bg: currentScheme.colors.background,\n                                                                                                    color: currentScheme.colors.text,\n                                                                                                    borderColor: currentScheme.colors.border\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 838,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 824,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 810,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 808,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Footer\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 857,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                    spacing: 2,\n                                                                                    align: \"stretch\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            value: ((_nodeData_embed10 = nodeData.embed) === null || _nodeData_embed10 === void 0 ? void 0 : (_nodeData_embed_footer = _nodeData_embed10.footer) === null || _nodeData_embed_footer === void 0 ? void 0 : _nodeData_embed_footer.text) || '',\n                                                                                            onChange: (e)=>{\n                                                                                                var _nodeData_embed;\n                                                                                                return updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        footer: {\n                                                                                                            ...(_nodeData_embed = nodeData.embed) === null || _nodeData_embed === void 0 ? void 0 : _nodeData_embed.footer,\n                                                                                                            text: e.target.value\n                                                                                                        }\n                                                                                                    }\n                                                                                                });\n                                                                                            },\n                                                                                            placeholder: \"Footer text\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 859,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            value: ((_nodeData_embed11 = nodeData.embed) === null || _nodeData_embed11 === void 0 ? void 0 : (_nodeData_embed_footer1 = _nodeData_embed11.footer) === null || _nodeData_embed_footer1 === void 0 ? void 0 : _nodeData_embed_footer1.iconUrl) || '',\n                                                                                            onChange: (e)=>{\n                                                                                                var _nodeData_embed;\n                                                                                                return updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        footer: {\n                                                                                                            ...(_nodeData_embed = nodeData.embed) === null || _nodeData_embed === void 0 ? void 0 : _nodeData_embed.footer,\n                                                                                                            iconUrl: e.target.value\n                                                                                                        }\n                                                                                                    }\n                                                                                                });\n                                                                                            },\n                                                                                            placeholder: \"Footer icon URL\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 872,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 858,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                    justify: \"space-between\",\n                                                                                    align: \"center\",\n                                                                                    mb: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                            color: currentScheme.colors.text,\n                                                                                            mb: 0,\n                                                                                            children: \"Embed Fields\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 891,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPlus, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                lineNumber: 894,\n                                                                                                columnNumber: 45\n                                                                                            }, void 0),\n                                                                                            onClick: addEmbedField,\n                                                                                            colorScheme: \"blue\",\n                                                                                            children: \"Add Field\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 892,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 890,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                    spacing: 3,\n                                                                                    align: \"stretch\",\n                                                                                    children: (_nodeData_embed12 = nodeData.embed) === null || _nodeData_embed12 === void 0 ? void 0 : (_nodeData_embed_fields = _nodeData_embed12.fields) === null || _nodeData_embed_fields === void 0 ? void 0 : _nodeData_embed_fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                                                                            p: 3,\n                                                                                            bg: currentScheme.colors.surface,\n                                                                                            borderRadius: \"md\",\n                                                                                            border: \"1px solid\",\n                                                                                            borderColor: currentScheme.colors.border,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                                    justify: \"space-between\",\n                                                                                                    align: \"center\",\n                                                                                                    mb: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                            fontSize: \"sm\",\n                                                                                                            fontWeight: \"bold\",\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            children: [\n                                                                                                                \"Field \",\n                                                                                                                index + 1\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 912,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.IconButton, {\n                                                                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTrash2, {}, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                                lineNumber: 916,\n                                                                                                                columnNumber: 47\n                                                                                                            }, void 0),\n                                                                                                            size: \"xs\",\n                                                                                                            colorScheme: \"red\",\n                                                                                                            variant: \"ghost\",\n                                                                                                            onClick: ()=>removeEmbedField(index),\n                                                                                                            \"aria-label\": \"Remove field\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 915,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 911,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                                    spacing: 2,\n                                                                                                    align: \"stretch\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                            value: field.name,\n                                                                                                            onChange: (e)=>updateEmbedField(index, 'name', e.target.value),\n                                                                                                            placeholder: \"Field name\",\n                                                                                                            bg: currentScheme.colors.background,\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            borderColor: currentScheme.colors.border\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 925,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                                                                                            value: field.value,\n                                                                                                            onChange: (e)=>updateEmbedField(index, 'value', e.target.value),\n                                                                                                            placeholder: \"Field value\",\n                                                                                                            bg: currentScheme.colors.background,\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            borderColor: currentScheme.colors.border,\n                                                                                                            minH: \"80px\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 933,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                                                                    isChecked: field.inline || false,\n                                                                                                                    onChange: (e)=>updateEmbedField(index, 'inline', e.target.checked),\n                                                                                                                    colorScheme: \"purple\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                                    lineNumber: 943,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                                    fontSize: \"sm\",\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    children: \"Display inline\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                                    lineNumber: 948,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 942,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 924,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, index, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 903,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 901,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 889,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                                    isChecked: ((_nodeData_embed13 = nodeData.embed) === null || _nodeData_embed13 === void 0 ? void 0 : _nodeData_embed13.timestamp) || false,\n                                                                                    onChange: (e)=>updateNodeData({\n                                                                                            embed: {\n                                                                                                ...nodeData.embed,\n                                                                                                timestamp: e.target.checked\n                                                                                            }\n                                                                                        }),\n                                                                                    colorScheme: \"purple\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 959,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Show current timestamp\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 966,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 958,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                            fontSize: \"lg\",\n                                                                            fontWeight: \"bold\",\n                                                                            color: currentScheme.colors.text,\n                                                                            children: \"Embed Preview\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 976,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                                                                            status: \"info\",\n                                                                            borderRadius: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertIcon, {}, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 980,\n                                                                                    columnNumber: 21\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                                                                                    fontSize: \"sm\",\n                                                                                    children: \"This shows your message content (above) and embed (below) as they will appear in Discord. Variables will be replaced with actual values when sent.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 981,\n                                                                                    columnNumber: 21\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 979,\n                                                                            columnNumber: 19\n                                                                        }, undefined),\n                                                                        renderEmbedPreview()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                    lineNumber: 975,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                spacing: 4,\n                                                align: \"stretch\",\n                                                children: [\n                                                    (nodeData.actionType === 'sendMessage' || nodeData.actionType === 'sendDM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Message Content\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 996,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                                                        value: nodeData.message || '',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                message: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Hello {user.username}! Welcome to {server.name}!\",\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border,\n                                                                        minH: \"100px\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 997,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            nodeData.actionType !== 'sendDM' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Channel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1010,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                        spacing: 2,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                                                value: nodeData.channel || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        channel: e.target.value\n                                                                                    }),\n                                                                                placeholder: loadingGuildData ? 'Loading channels...' : 'Select a channel',\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                isDisabled: loadingGuildData,\n                                                                                children: guildData === null || guildData === void 0 ? void 0 : guildData.channels.filter((channel)=>channel.type === 'text').map((channel)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: channel.name,\n                                                                                        children: [\n                                                                                            \"#\",\n                                                                                            channel.name\n                                                                                        ]\n                                                                                    }, channel.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1024,\n                                                                                        columnNumber: 39\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1012,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                value: nodeData.channel || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        channel: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"Or type: general or {channel.name}\",\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                size: \"sm\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1029,\n                                                                                columnNumber: 21\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1011,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1009,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    (nodeData.actionType === 'addRole' || nodeData.actionType === 'removeRole') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Role Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1048,\n                                                                        columnNumber: 19\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                        spacing: 2,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                                                value: nodeData.role || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        role: e.target.value\n                                                                                    }),\n                                                                                placeholder: loadingGuildData ? 'Loading roles...' : 'Select a role',\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                isDisabled: loadingGuildData,\n                                                                                children: guildData === null || guildData === void 0 ? void 0 : guildData.roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: role.name,\n                                                                                        children: [\n                                                                                            \"@\",\n                                                                                            role.name\n                                                                                        ]\n                                                                                    }, role.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1060,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1050,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                value: nodeData.role || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        role: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"Or type: Member or {user.role}\",\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                size: \"sm\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1065,\n                                                                                columnNumber: 19\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1049,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1047,\n                                                                columnNumber: 17\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Reason\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1077,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        value: nodeData.reason || '',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                reason: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Role updated by bot\",\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1078,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1076,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    (nodeData.actionType === 'kickUser' || nodeData.actionType === 'banUser') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"User\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1094,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                        spacing: 2,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                                                value: nodeData.user || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        user: e.target.value\n                                                                                    }),\n                                                                                placeholder: loadingGuildData ? 'Loading members...' : 'Select a user',\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                isDisabled: loadingGuildData,\n                                                                                children: guildData === null || guildData === void 0 ? void 0 : guildData.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: member.username,\n                                                                                        children: [\n                                                                                            member.displayName,\n                                                                                            \" (@\",\n                                                                                            member.username,\n                                                                                            \")\"\n                                                                                        ]\n                                                                                    }, member.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1106,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1096,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                value: nodeData.user || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        user: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"Or type: username or {user.id}\",\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                size: \"sm\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1111,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1095,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1093,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Reason\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1123,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        value: nodeData.reason || '',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                reason: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Violation of server rules\",\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1124,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1122,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            nodeData.actionType === 'banUser' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Delete Message History\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1135,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                        isChecked: nodeData.deleteMessages || false,\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                deleteMessages: e.target.checked\n                                                                            }),\n                                                                        colorScheme: \"purple\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1136,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1134,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    nodeData.actionType === 'timeoutUser' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"User\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1150,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                        spacing: 2,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                                                value: nodeData.user || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        user: e.target.value\n                                                                                    }),\n                                                                                placeholder: loadingGuildData ? 'Loading members...' : 'Select a user',\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                isDisabled: loadingGuildData,\n                                                                                children: guildData === null || guildData === void 0 ? void 0 : guildData.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: member.username,\n                                                                                        children: [\n                                                                                            member.displayName,\n                                                                                            \" (@\",\n                                                                                            member.username,\n                                                                                            \")\"\n                                                                                        ]\n                                                                                    }, member.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1162,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1152,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                value: nodeData.user || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        user: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"Or type: username or {user.id}\",\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                size: \"sm\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1167,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1151,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1149,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Duration (minutes)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1179,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.NumberInput, {\n                                                                        value: nodeData.duration || 10,\n                                                                        onChange: (valueString)=>updateNodeData({\n                                                                                duration: parseInt(valueString) || 10\n                                                                            }),\n                                                                        min: 1,\n                                                                        max: 40320,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.NumberInputField, {\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1186,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.NumberInputStepper, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.NumberIncrementStepper, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1192,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.NumberDecrementStepper, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1193,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1191,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1180,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1178,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Reason\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1198,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        value: nodeData.reason || '',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                reason: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Timeout for spam\",\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1199,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1197,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    nodeData.actionType === 'addReaction' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                    color: currentScheme.colors.text,\n                                                                    children: \"Reaction (emoji)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                    lineNumber: 1215,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    value: nodeData.reaction || '',\n                                                                    onChange: (e)=>updateNodeData({\n                                                                            reaction: e.target.value\n                                                                        }),\n                                                                    placeholder: \"\\uD83D\\uDC4D or :thumbsup:\",\n                                                                    bg: currentScheme.colors.background,\n                                                                    color: currentScheme.colors.text,\n                                                                    borderColor: currentScheme.colors.border\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                    lineNumber: 1216,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 1214,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 1213,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    nodeData.actionType === 'createChannel' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Channel Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1232,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        value: nodeData.channelName || '',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                channelName: e.target.value\n                                                                            }),\n                                                                        placeholder: \"new-channel\",\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1233,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1231,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Channel Type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1243,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                                        value: nodeData.channelType || 'text',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                channelType: e.target.value\n                                                                            }),\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"text\",\n                                                                                children: \"Text Channel\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1251,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"voice\",\n                                                                                children: \"Voice Channel\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1252,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"category\",\n                                                                                children: \"Category\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1253,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1244,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1242,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 1230,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 991,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            colorScheme: \"purple\",\n                                            onClick: ()=>{\n                                                // Save the configuration\n                                                data.actionType = nodeData.actionType;\n                                                data.message = nodeData.message;\n                                                data.channel = nodeData.channel;\n                                                data.role = nodeData.role;\n                                                data.user = nodeData.user;\n                                                data.embed = nodeData.embed;\n                                                data.reason = nodeData.reason;\n                                                data.duration = nodeData.duration;\n                                                data.deleteMessages = nodeData.deleteMessages;\n                                                data.reaction = nodeData.reaction;\n                                                data.channelName = nodeData.channelName;\n                                                data.channelType = nodeData.channelType;\n                                                data.label = nodeData.actionType ? getActionLabel(nodeData.actionType) : 'Action';\n                                                onClose();\n                                            },\n                                            size: \"lg\",\n                                            width: \"full\",\n                                            children: \"Save Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 1263,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                lineNumber: 571,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"E4W8niAFRMiSx43TSi7pB08vHaw=\", false, function() {\n    return [\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useTheme,\n        _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useDisclosure\n    ];\n})), \"E4W8niAFRMiSx43TSi7pB08vHaw=\", false, function() {\n    return [\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useTheme,\n        _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useDisclosure\n    ];\n});\n_c1 = ActionNode;\nActionNode.displayName = 'ActionNode';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ActionNode);\nvar _c, _c1;\n$RefreshReg$(_c, \"ActionNode$memo\");\n$RefreshReg$(_c1, \"ActionNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/flow/ActionNode.tsx\n"));

/***/ })

}]);