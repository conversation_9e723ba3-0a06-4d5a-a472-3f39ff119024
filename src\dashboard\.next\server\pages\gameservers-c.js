"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/gameservers-c";
exports.ids = ["pages/gameservers-c"];
exports.modules = {

/***/ "(pages-dir-node)/./core/config.ts":
/*!************************!*\
  !*** ./core/config.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dashboardConfig: () => (/* binding */ dashboardConfig),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n// @ts-nocheck\n\n\n\nlet config = {};\ntry {\n    // Locate config.yml by walking up directories (dashboard may run from nested cwd)\n    const possible = [\n        'config.yml',\n        '../config.yml',\n        '../../config.yml',\n        '../../../config.yml',\n        '../../../../config.yml'\n    ].map((rel)=>path__WEBPACK_IMPORTED_MODULE_2__.resolve(process.cwd(), rel));\n    let configPath = possible.find((p)=>fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(p));\n    if (!configPath) {\n        // fallback relative to file location\n        const dirBased = path__WEBPACK_IMPORTED_MODULE_2__.resolve(__dirname, '../../../config.yml');\n        if (fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(dirBased)) configPath = dirBased;\n    }\n    if (!configPath) {\n        throw new Error('config.yml not found');\n    }\n    const fileContents = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(configPath, 'utf8');\n    config = yaml__WEBPACK_IMPORTED_MODULE_1___default().parse(fileContents);\n} catch (error) {\n    console.error('Error: Failed to load config.yml:', error);\n    process.exit(1); // Exit if we can't load the config\n}\n// Export the dashboard-specific config\nconst dashboardConfig = {\n    bot: {\n        token: config.bot.token,\n        clientId: config.bot.clientId,\n        clientSecret: config.bot.clientSecret,\n        guildId: config.bot.guildId,\n        ticketCategoryId: config.bot.ticketCategoryId || null,\n        ticketLogChannelId: config.bot.ticketLogChannelId || null,\n        prefix: config.bot.prefix\n    },\n    dashboard: {\n        admins: config.dashboard?.admins || [],\n        adminRoleIds: config.dashboard?.adminRoleIds || [],\n        session: {\n            secret: config.dashboard?.session?.secret || config.bot.clientSecret\n        }\n    },\n    database: {\n        url: config.database.url,\n        name: config.database.name,\n        options: {\n            maxPoolSize: config.database.options?.maxPoolSize || 10,\n            minPoolSize: config.database.options?.minPoolSize || 1,\n            maxIdleTimeMS: config.database.options?.maxIdleTimeMS || 30000,\n            serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,\n            socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,\n            connectTimeoutMS: config.database.options?.connectTimeoutMS || 10000,\n            retryWrites: config.database.options?.retryWrites !== false,\n            retryReads: config.database.options?.retryReads !== false\n        }\n    }\n};\n// Validate required configuration\nif (!dashboardConfig.bot.token) {\n    console.error('Error: Discord bot token is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.clientId || !dashboardConfig.bot.clientSecret) {\n    console.error('Error: Discord OAuth2 credentials (clientId and clientSecret) are required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.guildId) {\n    console.error('Error: Guild ID is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.database.url || !dashboardConfig.database.name) {\n    console.error('Error: Database configuration (url and name) is required in config.yml');\n    process.exit(1);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dashboardConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./core/config.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useExperimentalFeatures.ts":
/*!******************************************!*\
  !*** ./hooks/useExperimentalFeatures.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useExperimentalFeatures)\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction useExperimentalFeatures() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.useSession)();\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reason, setReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useExperimentalFeatures.useEffect\": ()=>{\n            async function checkAccess() {\n                if (!session?.user) {\n                    setHasAccess(false);\n                    setReason('unauthenticated');\n                    setIsLoading(false);\n                    return;\n                }\n                try {\n                    const response = await fetch('/api/discord/user/experimental');\n                    const data = await response.json();\n                    setHasAccess(data.hasAccess);\n                    setReason(data.reason);\n                } catch (error) {\n                    console.error('Error checking experimental features access:', error);\n                    setHasAccess(false);\n                    setReason('error');\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n            checkAccess();\n        }\n    }[\"useExperimentalFeatures.useEffect\"], [\n        session\n    ]);\n    return {\n        hasAccess,\n        reason,\n        isLoading,\n        isDeveloper: reason === 'developer',\n        isTester: reason === 'tester'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useExperimentalFeatures.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useGuildInfo.ts":
/*!*******************************!*\
  !*** ./hooks/useGuildInfo.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useGuildInfo)\n/* harmony export */ });\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swr */ \"swr\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swr__WEBPACK_IMPORTED_MODULE_0__]);\nswr__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst fetcher = async (url)=>{\n    const res = await fetch(url);\n    if (!res.ok) {\n        if (res.status === 401) {\n            // Return default data for unauthorized state\n            return {\n                name: '404 Bot',\n                botName: '404 Bot'\n            };\n        }\n        throw new Error('Failed to fetch guild info');\n    }\n    return res.json();\n};\nfunction useGuildInfo() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    // Only fetch if we're authenticated\n    const shouldFetch = status === 'authenticated';\n    const { data, error } = (0,swr__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(shouldFetch ? '/api/discord/guild' : null, fetcher, {\n        revalidateOnFocus: false,\n        revalidateOnReconnect: false\n    });\n    // Local preference state (guild vs bot)\n    const [pref, setPref] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"useGuildInfo.useState\": ()=>{\n            if (true) return 'guild';\n            return localStorage.getItem('dashboardDisplayNamePref') || 'guild';\n        }\n    }[\"useGuildInfo.useState\"]);\n    // Function to update preference and broadcast change\n    const updatePreference = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useGuildInfo.useCallback[updatePreference]\": (newPref)=>{\n            setPref(newPref);\n            if (false) {}\n        }\n    }[\"useGuildInfo.useCallback[updatePreference]\"], []);\n    // Listen for preference changes in this tab (custom event) or other tabs (storage event)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useGuildInfo.useEffect\": ()=>{\n            if (true) return;\n            const handleCustom = {\n                \"useGuildInfo.useEffect.handleCustom\": (e)=>{\n                    if (e?.detail) setPref(e.detail);\n                }\n            }[\"useGuildInfo.useEffect.handleCustom\"];\n            const handleStorage = {\n                \"useGuildInfo.useEffect.handleStorage\": (e)=>{\n                    if (e.key === 'dashboardDisplayNamePref') {\n                        setPref(e.newValue || 'guild');\n                    }\n                }\n            }[\"useGuildInfo.useEffect.handleStorage\"];\n            window.addEventListener('displayNamePrefChanged', handleCustom);\n            window.addEventListener('storage', handleStorage);\n            return ({\n                \"useGuildInfo.useEffect\": ()=>{\n                    window.removeEventListener('displayNamePrefChanged', handleCustom);\n                    window.removeEventListener('storage', handleStorage);\n                }\n            })[\"useGuildInfo.useEffect\"];\n        }\n    }[\"useGuildInfo.useEffect\"], []);\n    // Default display name when not authenticated\n    const defaultName = '404 Bot Dashboard';\n    // Determine displayName\n    let displayName = defaultName;\n    if (data) {\n        if (pref === 'bot' && data.botName) {\n            displayName = data.botName;\n        } else {\n            displayName = data.name || defaultName;\n        }\n    }\n    return {\n        guild: data,\n        displayName,\n        pref,\n        updatePreference,\n        isLoading: shouldFetch && !error && !data,\n        isError: !!error\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useGuildInfo.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/discord */ \"next-auth/providers/discord\");\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(pages-dir-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nconst authOptions = {\n    providers: [\n        next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId,\n            clientSecret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n            authorization: {\n                params: {\n                    scope: 'identify email guilds'\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, account, profile }) {\n            if (account && profile) {\n                token.accessToken = account.access_token || null;\n                token.id = profile.id || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                // Ensure we have valid values for serialization\n                const userId = token.id || null;\n                const accessToken = token.accessToken || null;\n                // Attach Discord user ID to session\n                session.user.id = userId;\n                session.user.accessToken = accessToken;\n                // Default to false for admin status\n                let isAdmin = false;\n                if (userId) {\n                    console.log('Checking admin status for user:', userId);\n                    // Check explicit admin IDs\n                    const adminIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.admins || [];\n                    console.log('Configured admin IDs:', adminIds);\n                    if (adminIds.includes(userId)) {\n                        console.log('User is in admin list');\n                        isAdmin = true;\n                    } else {\n                        console.log('User not in admin list, checking roles...');\n                        // Check roles if configured\n                        const roleIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.adminRoleIds || [];\n                        console.log('Configured admin role IDs:', roleIds);\n                        if (roleIds.length && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId) {\n                            try {\n                                console.log('Fetching member roles from Discord API...');\n                                console.log('Bot token (first 20 chars):', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token.substring(0, 20) + '...');\n                                console.log('Guild ID:', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId);\n                                console.log('User ID:', userId);\n                                console.log('Admin role IDs to check:', roleIds);\n                                const res = await fetch(`https://discord.com/api/v10/guilds/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId}/members/${userId}`, {\n                                    headers: {\n                                        Authorization: `Bot ${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token}`\n                                    }\n                                });\n                                console.log('Discord API response status:', res.status);\n                                if (res.ok) {\n                                    const member = await res.json();\n                                    console.log('Member data:', JSON.stringify(member, null, 2));\n                                    console.log('Member roles:', member.roles);\n                                    isAdmin = roleIds.some((rid)=>member.roles?.includes(rid)) || false;\n                                    console.log('Has admin role:', isAdmin);\n                                } else {\n                                    const errorText = await res.text();\n                                    console.error('Failed to fetch member - Status:', res.status);\n                                    console.error('Error response:', errorText);\n                                }\n                            } catch (error) {\n                                console.error('Failed to fetch guild member:', error);\n                            }\n                        } else {\n                            console.log('No role IDs configured or missing bot token/guild ID');\n                        }\n                    }\n                } else {\n                    console.log('No user ID available');\n                }\n                // Set admin status\n                session.user.isAdmin = isAdmin;\n                console.log('Final admin status:', isAdmin);\n                // Ensure all session values are serializable\n                session.user = {\n                    ...session.user,\n                    id: session.user.id || null,\n                    accessToken: session.user.accessToken || null,\n                    isAdmin: session.user.isAdmin || false,\n                    name: session.user.name || null,\n                    email: session.user.email || null,\n                    image: session.user.image || null\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Dynamically derive equivalent localhost URL (same protocol & port)\n            const parsed = new URL(baseUrl);\n            const localhostBase = `${parsed.protocol}//localhost${parsed.port ? `:${parsed.port}` : ''}`;\n            if (url.startsWith(baseUrl) || url.startsWith(localhostBase)) {\n                return url;\n            }\n            return baseUrl;\n        }\n    },\n    secret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.session.secret || _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n    pages: {\n        signIn: '/signin'\n    },\n    // Always show auth errors, but only debug logs in development\n    debug: \"development\" === 'development',\n    logger: {\n        error: (code, metadata)=>{\n            console.error('[NextAuth Error]', code, metadata);\n        },\n        warn: (code)=>{\n            if (true) {\n                console.warn('[NextAuth Warn]', code);\n            }\n        },\n        debug: (code, metadata)=>{\n            if (true) {\n                console.debug('[NextAuth Debug]', code, metadata);\n            }\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/gameservers.tsx":
/*!*******************************!*\
  !*** ./pages/gameservers.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GameServers),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertDialog,AlertDialogBody,AlertDialogContent,AlertDialogFooter,AlertDialogHeader,AlertDialogOverlay,Box,Button,HStack,Heading,Icon,IconButton,SimpleGrid,Spinner,Text,VStack,useDisclosure,useToast!=!@chakra-ui/react */ \"(pages-dir-node)/__barrel_optimize__?names=AlertDialog,AlertDialogBody,AlertDialogContent,AlertDialogFooter,AlertDialogHeader,AlertDialogOverlay,Box,Button,HStack,Heading,Icon,IconButton,SimpleGrid,Spinner,Text,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaEdit,FaGamepad,FaPlus,FaSync,FaTrash!=!react-icons/fa */ \"(pages-dir-node)/__barrel_optimize__?names=FaEdit,FaGamepad,FaPlus,FaSync,FaTrash!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-node)/./components/Layout.tsx\");\n/* harmony import */ var _components_GameServerCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/GameServerCard */ \"(pages-dir-node)/./components/GameServerCard.tsx\");\n/* harmony import */ var _components_GameServerDialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GameServerDialog */ \"(pages-dir-node)/./components/GameServerDialog.tsx\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _api_auth_nextauth___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./api/auth/[...nextauth] */ \"(pages-dir-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_7__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Layout__WEBPACK_IMPORTED_MODULE_2__, _components_GameServerCard__WEBPACK_IMPORTED_MODULE_3__, _components_GameServerDialog__WEBPACK_IMPORTED_MODULE_4__, _barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__]);\n([_components_Layout__WEBPACK_IMPORTED_MODULE_2__, _components_GameServerCard__WEBPACK_IMPORTED_MODULE_3__, _components_GameServerDialog__WEBPACK_IMPORTED_MODULE_4__, _barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nfunction GameServers() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_7__.useSession)();\n    const isAdmin = session?.user?.isAdmin === true;\n    const [servers, setServers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedServer, setSelectedServer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [serverToDelete, setServerToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const { isOpen: isAddEditOpen, onOpen: onAddEditOpen, onClose: onAddEditClose } = (0,_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.useDisclosure)();\n    const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = (0,_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.useDisclosure)();\n    const cancelRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const toast = (0,_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const fetchServers = async ()=>{\n        try {\n            setRefreshing(true);\n            const response = await fetch('/api/gameservers/query');\n            if (!response.ok) {\n                throw new Error('Failed to fetch server status');\n            }\n            const data = await response.json();\n            console.log('API Response:', data, 'Type:', typeof data, 'Is Array:', Array.isArray(data));\n            // Ensure data is always an array\n            const serversArray = Array.isArray(data) ? data : [];\n            setServers(serversArray);\n        } catch (error) {\n            // Set servers to empty array to prevent map() error\n            setServers([]);\n            toast({\n                title: 'Error',\n                description: error instanceof Error ? error.message : 'Failed to fetch server status',\n                status: 'error',\n                duration: 5000,\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GameServers.useEffect\": ()=>{\n            fetchServers();\n            // Set up auto-refresh every 30 seconds\n            const interval = setInterval(fetchServers, 30000);\n            return ({\n                \"GameServers.useEffect\": ()=>clearInterval(interval)\n            })[\"GameServers.useEffect\"];\n        }\n    }[\"GameServers.useEffect\"], []);\n    const handleAddEdit = async (server)=>{\n        try {\n            // First validate the game type\n            const validateResponse = await fetch(`/api/gameservers/games?type=${encodeURIComponent(server.type)}`);\n            if (!validateResponse.ok) {\n                throw new Error('Invalid game type');\n            }\n            const { type: validatedType } = await validateResponse.json();\n            server.type = validatedType;\n            // Then save the server\n            const url = '/api/gameservers/manage';\n            const method = server._id ? 'PUT' : 'POST';\n            console.log('Server operation:', method, 'Server data:', server); // Enhanced debug log\n            let requestBody;\n            if (method === 'PUT') {\n                // For PUT requests, we need the id separate from the data\n                const { _id, ...serverData } = server;\n                requestBody = {\n                    id: _id,\n                    ...serverData\n                };\n            } else {\n                // For POST requests, we don't want the _id field at all\n                const { _id, ...serverData } = server;\n                requestBody = serverData;\n            }\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to save server');\n            }\n            fetchServers();\n            toast({\n                title: server._id ? 'Server Updated' : 'Server Added',\n                description: server._id ? 'The server has been updated successfully' : 'The server has been added successfully',\n                status: 'success',\n                duration: 3000,\n                isClosable: true\n            });\n        } catch (error) {\n            console.error('Save error:', error);\n            toast({\n                title: 'Error',\n                description: error instanceof Error ? error.message : 'Failed to save server',\n                status: 'error',\n                duration: 5000,\n                isClosable: true\n            });\n            throw error;\n        }\n    };\n    const handleDelete = async ()=>{\n        if (!serverToDelete?._id) {\n            console.error('No server selected for deletion', {\n                serverToDelete\n            });\n            toast({\n                title: 'Error',\n                description: 'No server selected for deletion',\n                status: 'error',\n                duration: 5000,\n                isClosable: true\n            });\n            return;\n        }\n        try {\n            console.log('Attempting to delete server:', serverToDelete);\n            const response = await fetch('/api/gameservers/manage', {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    id: serverToDelete._id\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to delete server');\n            }\n            // Remove the server from the local state\n            setServers((prev)=>prev.filter((s)=>s._id !== serverToDelete._id));\n            toast({\n                title: 'Server Deleted',\n                description: 'The server has been successfully deleted',\n                status: 'success',\n                duration: 3000,\n                isClosable: true\n            });\n            setServerToDelete(undefined);\n            onDeleteClose();\n        } catch (error) {\n            console.error('Delete error:', error);\n            toast({\n                title: 'Error',\n                description: error instanceof Error ? error.message : 'Failed to delete server',\n                status: 'error',\n                duration: 5000,\n                isClosable: true\n            });\n        }\n    };\n    const openEditDialog = (server)=>{\n        setSelectedServer({\n            _id: server._id,\n            name: server.name,\n            type: server.type,\n            host: server.host,\n            port: server.port,\n            description: server.description,\n            hasPassword: server.hasPassword,\n            password: server.password\n        });\n        onAddEditOpen();\n    };\n    const openDeleteDialog = (server)=>{\n        console.log('Opening delete dialog for server:', server);\n        setSelectedServer(server);\n        onDeleteOpen();\n    };\n    const handleDeleteServer = async ()=>{\n        if (!selectedServer) return;\n        try {\n            // Always use the server's ID if available\n            const deleteBody = {\n                id: selectedServer._id\n            };\n            console.log('Sending delete request with body:', deleteBody);\n            const response = await fetch('/api/gameservers/manage', {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(deleteBody)\n            });\n            const responseData = await response.json();\n            if (!response.ok) {\n                throw new Error(responseData.error || 'Failed to delete server');\n            }\n            // Remove the server from the local state\n            setServers((prev)=>prev.filter((s)=>s._id !== selectedServer._id));\n            setSelectedServer(undefined);\n            onDeleteClose();\n            toast({\n                title: 'Success',\n                description: 'Server deleted successfully',\n                status: 'success',\n                duration: 5000,\n                isClosable: true\n            });\n        } catch (error) {\n            console.error('Error deleting server:', error);\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to delete server',\n                status: 'error',\n                duration: 5000,\n                isClosable: true\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Box, {\n            w: \"full\",\n            p: 4,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Box, {\n                    maxW: \"4xl\",\n                    mx: \"auto\",\n                    mb: 8,\n                    mt: 8,\n                    bg: \"rgba(255,255,255,0.08)\",\n                    p: 8,\n                    rounded: \"2xl\",\n                    backdropFilter: \"blur(10px)\",\n                    border: \"2px solid\",\n                    borderColor: \"green.400\",\n                    boxShadow: \"0 0 15px rgba(72, 187, 120, 0.4)\",\n                    textAlign: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Heading, {\n                            size: \"2xl\",\n                            bgGradient: \"linear(to-r, green.300, teal.400)\",\n                            bgClip: \"text\",\n                            mb: 4,\n                            children: \"Game Servers\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                            color: \"gray.300\",\n                            fontSize: \"lg\",\n                            mb: 6,\n                            children: \"Monitor and manage your game servers in real-time\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.HStack, {\n                            spacing: 4,\n                            justify: \"center\",\n                            children: [\n                                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaPlus, {}, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    colorScheme: \"green\",\n                                    onClick: ()=>{\n                                        setSelectedServer(undefined);\n                                        onAddEditOpen();\n                                    },\n                                    size: \"md\",\n                                    variant: \"solid\",\n                                    _hover: {\n                                        transform: 'translateY(-2px)',\n                                        shadow: 'lg'\n                                    },\n                                    children: \"Add Server\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaSync, {}, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    onClick: fetchServers,\n                                    isLoading: refreshing,\n                                    loadingText: \"Refreshing\",\n                                    size: \"md\",\n                                    variant: \"outline\",\n                                    colorScheme: \"green\",\n                                    _hover: {\n                                        transform: 'translateY(-2px)',\n                                        shadow: 'lg'\n                                    },\n                                    children: \"Refresh Status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Box, {\n                    maxW: \"7xl\",\n                    mx: \"auto\",\n                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.VStack, {\n                        py: 8,\n                        bg: \"rgba(255,255,255,0.08)\",\n                        rounded: \"2xl\",\n                        backdropFilter: \"blur(10px)\",\n                        border: \"1px solid\",\n                        borderColor: \"whiteAlpha.200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Spinner, {\n                                size: \"xl\",\n                                color: \"green.400\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                color: \"gray.400\",\n                                children: \"Loading servers...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 13\n                    }, this) : !servers || servers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.VStack, {\n                        spacing: 4,\n                        p: 8,\n                        bg: \"rgba(255,255,255,0.08)\",\n                        rounded: \"2xl\",\n                        backdropFilter: \"blur(10px)\",\n                        border: \"1px solid\",\n                        borderColor: \"whiteAlpha.200\",\n                        textAlign: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Icon, {\n                                as: _barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaGamepad,\n                                boxSize: 12,\n                                color: \"green.400\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                color: \"gray.300\",\n                                fontSize: \"lg\",\n                                children: \"No game servers found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                fontSize: \"md\",\n                                color: \"gray.500\",\n                                children: \"Add your first game server to start monitoring\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 15\n                            }, this),\n                            isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaPlus, {}, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 27\n                                }, void 0),\n                                colorScheme: \"green\",\n                                onClick: ()=>{\n                                    setSelectedServer(undefined);\n                                    onAddEditOpen();\n                                },\n                                size: \"md\",\n                                variant: \"outline\",\n                                _hover: {\n                                    transform: 'translateY(-2px)',\n                                    shadow: 'lg'\n                                },\n                                children: \"Add Your First Server\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.SimpleGrid, {\n                        columns: {\n                            base: 1,\n                            md: 2,\n                            lg: 3\n                        },\n                        spacing: 6,\n                        children: (()=>{\n                            console.log('Rendering servers:', servers, 'Type:', typeof servers, 'Is Array:', Array.isArray(servers));\n                            return (servers || []).map((server, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Box, {\n                                    position: \"relative\",\n                                    transition: \"all 0.2s\",\n                                    _hover: isAdmin ? {\n                                        transform: 'translateY(-4px)',\n                                        '& > .server-actions': {\n                                            opacity: 1,\n                                            transform: 'translateY(0)'\n                                        }\n                                    } : undefined,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GameServerCard__WEBPACK_IMPORTED_MODULE_3__.GameServerCard, {\n                                            server: server\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, this),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.HStack, {\n                                            className: \"server-actions\",\n                                            position: \"absolute\",\n                                            top: 2,\n                                            right: 2,\n                                            spacing: 1,\n                                            bg: \"blackAlpha.800\",\n                                            p: 1,\n                                            borderRadius: \"md\",\n                                            opacity: 0,\n                                            transform: \"translateY(-4px)\",\n                                            transition: \"all 0.2s\",\n                                            zIndex: 2,\n                                            backdropFilter: \"blur(8px)\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.IconButton, {\n                                                    \"aria-label\": \"Edit server\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaEdit, {}, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    size: \"sm\",\n                                                    variant: \"ghost\",\n                                                    colorScheme: \"green\",\n                                                    onClick: ()=>openEditDialog(server),\n                                                    _hover: {\n                                                        bg: 'green.700'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.IconButton, {\n                                                    \"aria-label\": \"Delete server\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaTrash, {}, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    size: \"sm\",\n                                                    variant: \"ghost\",\n                                                    colorScheme: \"red\",\n                                                    onClick: ()=>openDeleteDialog(server),\n                                                    _hover: {\n                                                        bg: 'red.700'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, `${server._id || `${server.host}:${server.port}-${index}`}`, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 17\n                                }, this));\n                        })()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, this),\n                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GameServerDialog__WEBPACK_IMPORTED_MODULE_4__.GameServerDialog, {\n                    isOpen: isAddEditOpen,\n                    onClose: onAddEditClose,\n                    server: selectedServer,\n                    onSave: handleAddEdit\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.AlertDialog, {\n                    isOpen: isDeleteOpen,\n                    leastDestructiveRef: cancelRef,\n                    onClose: onDeleteClose,\n                    isCentered: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.AlertDialogOverlay, {\n                        backdropFilter: \"blur(10px)\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.AlertDialogContent, {\n                            bg: \"gray.800\",\n                            border: \"1px\",\n                            borderColor: \"whiteAlpha.200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.AlertDialogHeader, {\n                                    fontSize: \"lg\",\n                                    fontWeight: \"bold\",\n                                    color: \"white\",\n                                    children: \"Delete Server\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.AlertDialogBody, {\n                                    color: \"gray.300\",\n                                    children: [\n                                        \"Are you sure you want to delete \",\n                                        selectedServer?.name || `${selectedServer?.host}:${selectedServer?.port}`,\n                                        \"? This action cannot be undone.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.AlertDialogFooter, {\n                                    gap: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                            ref: cancelRef,\n                                            onClick: onDeleteClose,\n                                            variant: \"ghost\",\n                                            color: \"gray.300\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                            colorScheme: \"red\",\n                                            onClick: handleDeleteServer,\n                                            _hover: {\n                                                bg: 'red.600'\n                                            },\n                                            _active: {\n                                                bg: 'red.700'\n                                            },\n                                            children: \"Delete\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n            lineNumber: 295,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\nconst getServerSideProps = async (ctx)=>{\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_5__.getServerSession)(ctx.req, ctx.res, _api_auth_nextauth___WEBPACK_IMPORTED_MODULE_6__.authOptions);\n    if (!session) {\n        return {\n            redirect: {\n                destination: '/signin',\n                permanent: false\n            }\n        };\n    }\n    return {\n        props: {}\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/gameservers.tsx\n");

/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/react");;

/***/ }),

/***/ "@emotion/styled":
/*!**********************************!*\
  !*** external "@emotion/styled" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("@emotion/styled");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "swr":
/*!**********************!*\
  !*** external "swr" ***!
  \**********************/
/***/ ((module) => {

module.exports = import("swr");;

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c","chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-4215ff4a","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-0e951d65","chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9","chakra-node_modules_pnpm_chakra-ui_theme-","chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_fa_index_mjs-b1cc012-b4687165","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_f","lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons","pages/gameservers-_"], () => (__webpack_exec__("(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fgameservers&preferredRegion=&absolutePagePath=.%2Fpages%5Cgameservers.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();