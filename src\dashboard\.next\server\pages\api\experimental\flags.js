"use strict";(()=>{var e={};e.id=9361,e.ids=[9361],e.modules={9216:(e,r,t)=>{t.r(r),t.d(r,{config:()=>f,default:()=>l,routeModule:()=>x});var a={};t.r(a),t.d(a,{default:()=>p});var i=t(93433),s=t(20264),u=t(20584),n=t(15806),o=t(94506),d=t(72290);async function p(e,r){let t=await (0,n.getServerSession)(e,r,o.authOptions);if(!t)return r.status(401).json({error:"Unauthorized"});let a=(await (0,d.L)()).collection("experimental_flags");if("GET"===e.method){let e=await a.find({}).toArray();return r.status(200).json({flags:e})}if("POST"===e.method){if(t.user?.id!=="933023999770918932")return r.status(403).json({error:"Forbidden"});let{feature:i,enabled:s=!1}=e.body;if(!i)return r.status(400).json({error:"feature required"});await a.updateOne({feature:i},{$set:{feature:i,enabled:s,updatedBy:t.user.id,updatedAt:new Date}},{upsert:!0});let u=await a.findOne({feature:i});return r.status(200).json({flag:u})}return r.status(405).json({error:"Method not allowed"})}let l=(0,u.M)(a,"default"),f=(0,u.M)(a,"config"),x=new i.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/experimental/flags",pathname:"/api/experimental/flags",bundlePath:"",filename:""},userland:a})},12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(9216));module.exports=a})();