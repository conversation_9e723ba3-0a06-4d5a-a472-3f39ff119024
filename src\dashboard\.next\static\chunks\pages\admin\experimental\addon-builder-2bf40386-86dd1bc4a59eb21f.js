"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6099],{89654:(e,o,n)=>{n.d(o,{A:()=>x});var s=n(94513),r=n(94285),i=n(6179),t=n(81330),a=n(85952),c=n(27263);let l={command:[{name:"{command.name}",description:"Command name that was executed",icon:"⚡"},{name:"{command.user}",description:"User who executed the command",icon:"\uD83D\uDC64"},{name:"{command.channel}",description:"Channel where command was executed",icon:"\uD83D\uDCFA"},{name:"{command.server}",description:"Server where command was executed",icon:"\uD83C\uDFE0"},{name:"{command.timestamp}",description:"When the command was executed",icon:"⏰"}],options:[{name:"{option.name}",description:"Value of a specific option",icon:"\uD83D\uDD27"},{name:"{option.user}",description:"User option value",icon:"\uD83D\uDC64"},{name:"{option.channel}",description:"Channel option value",icon:"\uD83D\uDCFA"},{name:"{option.role}",description:"Role option value",icon:"\uD83C\uDFAD"},{name:"{option.string}",description:"String option value",icon:"\uD83D\uDCAC"},{name:"{option.number}",description:"Number option value",icon:"\uD83D\uDD22"},{name:"{option.boolean}",description:"Boolean option value",icon:"✅"}],user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.tag}",description:"User Tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{user.mention}",description:"User Mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{user.avatar}",description:"Avatar URL",icon:"\uD83D\uDDBC️"},{name:"{user.roles}",description:"User Roles",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions",icon:"\uD83D\uDD10"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel Mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel Topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server Icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{server.owner}",description:"Server Owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server Boost Level",icon:"\uD83D\uDE80"}]},d=["ADMINISTRATOR","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","KICK_MEMBERS","BAN_MEMBERS","MANAGE_MESSAGES","EMBED_LINKS","ATTACH_FILES","READ_MESSAGE_HISTORY","MENTION_EVERYONE","USE_EXTERNAL_EMOJIS","CONNECT","SPEAK","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS","USE_VAD","CHANGE_NICKNAME","MANAGE_NICKNAMES","MANAGE_WEBHOOKS","MANAGE_EMOJIS","MODERATE_MEMBERS","VIEW_AUDIT_LOG","MANAGE_EVENTS","MANAGE_THREADS","CREATE_PUBLIC_THREADS","CREATE_PRIVATE_THREADS","USE_EXTERNAL_STICKERS","SEND_MESSAGES_IN_THREADS","START_EMBEDDED_ACTIVITIES"],h=[{value:"string",label:"\uD83D\uDCDD String - Text input"},{value:"integer",label:"\uD83D\uDD22 Integer - Whole number"},{value:"number",label:"\uD83D\uDD22 Number - Decimal number"},{value:"boolean",label:"✅ Boolean - True/False"},{value:"user",label:"\uD83D\uDC64 User - Discord user"},{value:"channel",label:"\uD83D\uDCFA Channel - Discord channel"},{value:"role",label:"\uD83C\uDFAD Role - Discord role"},{value:"mentionable",label:"\uD83D\uDCE2 Mentionable - User or role"},{value:"attachment",label:"\uD83D\uDCCE Attachment - File upload"}],m=(0,r.memo)(e=>{var o,n,m,x,p,u,g;let{data:b,selected:j,id:S,updateNodeData:v}=e,{currentScheme:f}=(0,c.DP)(),{isOpen:C,onOpen:y,onClose:T}=(0,t.useDisclosure)(),[k,A]=(0,r.useState)(()=>({guildOnly:!1,adminOnly:!1,allowDMs:!1,cooldown:0,options:[],category:"general",examples:[],permissions:[],ephemeral:!1,deferReply:!1,...b})),[w,E]=(0,r.useState)(!1),M=e=>{A(o=>({...o,...e}))},R=(e,o)=>{let n=[...k.options||[]];n[e]={...n[e],...o},M({options:n})},z=e=>{M({options:(k.options||[]).filter((o,n)=>n!==e)})},I=e=>{let o=[...k.options||[]];o[e].choices||(o[e].choices=[]),o[e].choices.push({name:"",value:""}),M({options:o})},D=(e,o,n,s)=>{let r=[...k.options||[]];r[e].choices&&(r[e].choices[o][n]=s,M({options:r}))},N=(e,o)=>{let n=[...k.options||[]];n[e].choices&&(n[e].choices=n[e].choices.filter((e,n)=>n!==o),M({options:n}))},B=e=>{navigator.clipboard.writeText(e)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(t.Box,{bg:f.colors.surface,border:"2px solid ".concat(j?"#3b82f6":f.colors.border),borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(i.h7,{type:"target",position:i.yX.Top,style:{background:"#3b82f6",border:"2px solid ".concat(f.colors.surface),width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(t.VStack,{spacing:1,align:"stretch",children:[(0,s.jsxs)(t.HStack,{justify:"space-between",align:"center",children:[(0,s.jsxs)(t.HStack,{spacing:1,children:[(0,s.jsx)(t.Box,{bg:"blue.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(a.FrA,{})}),(0,s.jsx)(t.Text,{fontSize:"xs",fontWeight:"bold",color:f.colors.text,children:"Command"})]}),(0,s.jsx)(t.IconButton,{icon:(0,s.jsx)(a.VSk,{}),size:"xs",variant:"ghost",onClick:y,"aria-label":"Configure command"})]}),(0,s.jsx)(t.Box,{children:(0,s.jsxs)(t.Text,{fontSize:"xs",color:f.colors.text,noOfLines:1,children:["/",k.commandName||"unnamed"]})}),k.description&&(0,s.jsx)(t.Box,{children:(0,s.jsx)(t.Text,{fontSize:"xs",color:f.colors.textSecondary,noOfLines:1,children:k.description.length>25?k.description.substring(0,25)+"...":k.description})}),(0,s.jsxs)(t.HStack,{spacing:1,flexWrap:"wrap",children:[(null!=(u=null==(o=k.options)?void 0:o.length)?u:0)>0&&(0,s.jsxs)(t.Badge,{size:"xs",colorScheme:"blue",children:[null==(n=k.options)?void 0:n.length," option",(null!=(g=null==(m=k.options)?void 0:m.length)?g:0)!==1?"s":""]}),k.adminOnly&&(0,s.jsx)(t.Badge,{size:"xs",colorScheme:"red",children:"Admin"}),k.cooldown&&k.cooldown>0&&(0,s.jsxs)(t.Badge,{size:"xs",colorScheme:"orange",children:[k.cooldown,"s"]})]})]}),(0,s.jsx)(i.h7,{type:"source",position:i.yX.Bottom,style:{background:"#3b82f6",border:"2px solid ".concat(f.colors.surface),width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,s.jsxs)(t.Modal,{isOpen:C,onClose:()=>{v&&S&&v(S,k),T()},size:"4xl",children:[(0,s.jsx)(t.ModalOverlay,{bg:"blackAlpha.600"}),(0,s.jsxs)(t.ModalContent,{bg:f.colors.background,border:"2px solid",borderColor:"blue.400",maxW:"1200px",children:[(0,s.jsx)(t.ModalHeader,{color:f.colors.text,children:"⚡ Configure Command"}),(0,s.jsx)(t.ModalCloseButton,{}),(0,s.jsx)(t.ModalBody,{pb:6,children:(0,s.jsxs)(t.VStack,{spacing:6,align:"stretch",children:[(0,s.jsxs)(t.Box,{children:[(0,s.jsxs)(t.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Available Variables"}),(0,s.jsxs)(t.Button,{size:"sm",variant:"ghost",leftIcon:w?(0,s.jsx)(a._NO,{}):(0,s.jsx)(a.Vap,{}),onClick:()=>E(!w),children:[w?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(t.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(t.AlertIcon,{}),(0,s.jsx)(t.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your command responses! Click any variable below to copy it. Variables are replaced with actual values when your command runs."})]}),(0,s.jsx)(t.Collapse,{in:w,animateOpacity:!0,children:(0,s.jsx)(t.Box,{bg:f.colors.surface,border:"1px solid",borderColor:f.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)(t.Accordion,{allowMultiple:!0,children:Object.entries(l).map(e=>{let[o,n]=e;return(0,s.jsxs)(t.AccordionItem,{border:"none",children:[(0,s.jsxs)(t.AccordionButton,{px:0,py:2,children:[(0,s.jsx)(t.Box,{flex:"1",textAlign:"left",children:(0,s.jsxs)(t.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,textTransform:"capitalize",children:[o," Variables"]})}),(0,s.jsx)(t.AccordionIcon,{})]}),(0,s.jsx)(t.AccordionPanel,{px:0,py:2,children:(0,s.jsx)(t.VStack,{spacing:2,align:"stretch",children:n.map(e=>(0,s.jsxs)(t.HStack,{spacing:2,p:2,bg:f.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:f.colors.surface},onClick:()=>B(e.name),children:[(0,s.jsx)(t.Text,{fontSize:"sm",children:e.icon}),(0,s.jsx)(t.Code,{fontSize:"xs",colorScheme:"blue",children:e.name}),(0,s.jsx)(t.Text,{fontSize:"xs",color:f.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(t.IconButton,{icon:(0,s.jsx)(a.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),B(e.name)}})]},e.name))})})]},o)})})})})]}),(0,s.jsx)(t.Divider,{}),(0,s.jsxs)(t.Tabs,{variant:"enclosed",colorScheme:"blue",children:[(0,s.jsxs)(t.TabList,{children:[(0,s.jsx)(t.Tab,{children:"Basic Info"}),(0,s.jsx)(t.Tab,{children:"Options"}),(0,s.jsx)(t.Tab,{children:"Permissions"}),(0,s.jsx)(t.Tab,{children:"Advanced"})]}),(0,s.jsxs)(t.TabPanels,{children:[(0,s.jsx)(t.TabPanel,{children:(0,s.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(t.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(t.FormControl,{isRequired:!0,children:[(0,s.jsx)(t.FormLabel,{color:f.colors.text,children:"Command Name"}),(0,s.jsxs)(t.InputGroup,{children:[(0,s.jsx)(t.InputLeftAddon,{bg:f.colors.surface,color:f.colors.text,children:"/"}),(0,s.jsx)(t.Input,{value:k.commandName||"",onChange:e=>M({commandName:e.target.value}),placeholder:"ping",bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border})]})]}),(0,s.jsxs)(t.FormControl,{children:[(0,s.jsx)(t.FormLabel,{color:f.colors.text,children:"Category"}),(0,s.jsxs)(t.Select,{value:k.category||"general",onChange:e=>M({category:e.target.value}),bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,children:[(0,s.jsx)("option",{value:"general",children:"General"}),(0,s.jsx)("option",{value:"moderation",children:"Moderation"}),(0,s.jsx)("option",{value:"fun",children:"Fun"}),(0,s.jsx)("option",{value:"utility",children:"Utility"}),(0,s.jsx)("option",{value:"admin",children:"Admin"}),(0,s.jsx)("option",{value:"info",children:"Info"}),(0,s.jsx)("option",{value:"music",children:"Music"}),(0,s.jsx)("option",{value:"games",children:"Games"})]})]})]}),(0,s.jsxs)(t.FormControl,{isRequired:!0,children:[(0,s.jsx)(t.FormLabel,{color:f.colors.text,children:"Description"}),(0,s.jsx)(t.Textarea,{value:k.description||"",onChange:e=>M({description:e.target.value}),placeholder:"What does this command do?",bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,minH:"80px"})]}),(0,s.jsxs)(t.FormControl,{children:[(0,s.jsx)(t.FormLabel,{color:f.colors.text,children:"Usage Examples"}),(0,s.jsx)(t.Textarea,{value:(null==(x=k.examples)?void 0:x.join("\n"))||"",onChange:e=>M({examples:e.target.value.split("\n").filter(e=>e.trim())}),placeholder:"/ping\n/ping server\n/ping {user.mention}",bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,minH:"80px"}),(0,s.jsx)(t.Text,{fontSize:"xs",color:f.colors.textSecondary,mt:1,children:"One example per line"})]})]})}),(0,s.jsx)(t.TabPanel,{children:(0,s.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(t.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(t.Text,{fontSize:"lg",fontWeight:"bold",color:f.colors.text,children:"Command Options"}),(0,s.jsx)(t.Button,{leftIcon:(0,s.jsx)(a.GGD,{}),onClick:()=>{M({options:[...k.options||[],{name:"",description:"",type:"string",required:!1,choices:[]}]})},colorScheme:"blue",size:"sm",children:"Add Option"})]}),(0,s.jsxs)(t.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(t.AlertIcon,{}),(0,s.jsx)(t.AlertDescription,{fontSize:"sm",children:"Options are parameters users can provide with your command. They appear as autocomplete fields in Discord."})]}),(0,s.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[null==(p=k.options)?void 0:p.map((e,o)=>{var n;return(0,s.jsxs)(t.Box,{p:4,bg:f.colors.surface,borderRadius:"md",border:"1px solid",borderColor:f.colors.border,children:[(0,s.jsxs)(t.HStack,{justify:"space-between",align:"center",mb:3,children:[(0,s.jsxs)(t.Text,{fontSize:"md",fontWeight:"bold",color:f.colors.text,children:["Option ",o+1]}),(0,s.jsx)(t.IconButton,{icon:(0,s.jsx)(a.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>z(o),"aria-label":"Remove option"})]}),(0,s.jsxs)(t.VStack,{spacing:3,align:"stretch",children:[(0,s.jsxs)(t.SimpleGrid,{columns:2,spacing:3,children:[(0,s.jsxs)(t.FormControl,{isRequired:!0,children:[(0,s.jsx)(t.FormLabel,{fontSize:"sm",color:f.colors.text,children:"Option Name"}),(0,s.jsx)(t.Input,{value:e.name,onChange:e=>R(o,{name:e.target.value}),placeholder:"user",bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,size:"sm"})]}),(0,s.jsxs)(t.FormControl,{isRequired:!0,children:[(0,s.jsx)(t.FormLabel,{fontSize:"sm",color:f.colors.text,children:"Option Type"}),(0,s.jsx)(t.Select,{value:e.type,onChange:e=>R(o,{type:e.target.value}),bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,size:"sm",children:h.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]})]}),(0,s.jsxs)(t.FormControl,{children:[(0,s.jsx)(t.FormLabel,{fontSize:"sm",color:f.colors.text,children:"Description"}),(0,s.jsx)(t.Input,{value:e.description,onChange:e=>R(o,{description:e.target.value}),placeholder:"The user to ping",bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,size:"sm"})]}),(0,s.jsxs)(t.HStack,{children:[(0,s.jsx)(t.Switch,{isChecked:e.required,onChange:e=>R(o,{required:e.target.checked}),colorScheme:"blue"}),(0,s.jsx)(t.Text,{fontSize:"sm",color:f.colors.text,children:"Required option"})]}),("string"===e.type||"integer"===e.type||"number"===e.type)&&(0,s.jsxs)(t.Box,{children:[(0,s.jsxs)(t.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Predefined Choices (Optional)"}),(0,s.jsx)(t.Button,{size:"xs",leftIcon:(0,s.jsx)(a.GGD,{}),onClick:()=>I(o),colorScheme:"blue",variant:"ghost",children:"Add Choice"})]}),(0,s.jsx)(t.VStack,{spacing:2,align:"stretch",children:null==(n=e.choices)?void 0:n.map((e,n)=>(0,s.jsxs)(t.HStack,{spacing:2,children:[(0,s.jsx)(t.Input,{value:e.name,onChange:e=>D(o,n,"name",e.target.value),placeholder:"Choice name",bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,size:"sm"}),(0,s.jsx)(t.Input,{value:e.value,onChange:e=>D(o,n,"value",e.target.value),placeholder:"Choice value",bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border,size:"sm"}),(0,s.jsx)(t.IconButton,{icon:(0,s.jsx)(a.QLg,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>N(o,n),"aria-label":"Remove choice"})]},n))})]})]})]},o)}),(!k.options||0===k.options.length)&&(0,s.jsxs)(t.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(t.AlertIcon,{}),(0,s.jsx)(t.AlertDescription,{children:"No options configured. Your command will work without any parameters."})]})]})]})}),(0,s.jsx)(t.TabPanel,{children:(0,s.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(t.Text,{fontSize:"lg",fontWeight:"bold",color:f.colors.text,children:"Command Permissions"}),(0,s.jsxs)(t.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(t.AlertIcon,{}),(0,s.jsx)(t.AlertDescription,{fontSize:"sm",children:"Be careful with permissions! Overly restrictive permissions can prevent legitimate users from using your command."})]}),(0,s.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(t.HStack,{spacing:4,children:[(0,s.jsx)(t.Switch,{isChecked:k.adminOnly,onChange:e=>M({adminOnly:e.target.checked}),colorScheme:"red"}),(0,s.jsxs)(t.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Admin Only"}),(0,s.jsx)(t.Text,{fontSize:"xs",color:f.colors.textSecondary,children:"Only server administrators can use this command"})]})]}),(0,s.jsxs)(t.HStack,{spacing:4,children:[(0,s.jsx)(t.Switch,{isChecked:k.guildOnly,onChange:e=>M({guildOnly:e.target.checked}),colorScheme:"blue"}),(0,s.jsxs)(t.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Server Only"}),(0,s.jsx)(t.Text,{fontSize:"xs",color:f.colors.textSecondary,children:"Command can only be used in servers, not DMs"})]})]}),(0,s.jsxs)(t.HStack,{spacing:4,children:[(0,s.jsx)(t.Switch,{isChecked:k.allowDMs,onChange:e=>M({allowDMs:e.target.checked}),colorScheme:"green"}),(0,s.jsxs)(t.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Allow DMs"}),(0,s.jsx)(t.Text,{fontSize:"xs",color:f.colors.textSecondary,children:"Command can be used in direct messages"})]})]})]}),(0,s.jsx)(t.Divider,{}),(0,s.jsxs)(t.Box,{children:[(0,s.jsx)(t.Text,{fontSize:"md",fontWeight:"bold",color:f.colors.text,mb:3,children:"Required Permissions"}),(0,s.jsx)(t.Text,{fontSize:"sm",color:f.colors.textSecondary,mb:3,children:"Select the Discord permissions users need to use this command"}),(0,s.jsx)(t.CheckboxGroup,{value:k.permissions||[],onChange:e=>M({permissions:e}),children:(0,s.jsx)(t.SimpleGrid,{columns:3,spacing:2,children:d.map(e=>(0,s.jsx)(t.Checkbox,{value:e,colorScheme:"blue",size:"sm",children:(0,s.jsx)(t.Text,{fontSize:"xs",color:f.colors.text,children:e.replace(/_/g," ").toLowerCase().replace(/\b\w/g,e=>e.toUpperCase())})},e))})})]})]})}),(0,s.jsx)(t.TabPanel,{children:(0,s.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(t.Text,{fontSize:"lg",fontWeight:"bold",color:f.colors.text,children:"Advanced Settings"}),(0,s.jsxs)(t.FormControl,{children:[(0,s.jsx)(t.FormLabel,{color:f.colors.text,children:"Cooldown (seconds)"}),(0,s.jsxs)(t.NumberInput,{value:k.cooldown||0,onChange:e=>M({cooldown:parseInt(e)||0}),min:0,max:3600,children:[(0,s.jsx)(t.NumberInputField,{bg:f.colors.background,color:f.colors.text,borderColor:f.colors.border}),(0,s.jsxs)(t.NumberInputStepper,{children:[(0,s.jsx)(t.NumberIncrementStepper,{}),(0,s.jsx)(t.NumberDecrementStepper,{})]})]}),(0,s.jsx)(t.Text,{fontSize:"xs",color:f.colors.textSecondary,mt:1,children:"How long users must wait between uses (0 = no cooldown)"})]}),(0,s.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(t.HStack,{spacing:4,children:[(0,s.jsx)(t.Switch,{isChecked:k.ephemeral,onChange:e=>M({ephemeral:e.target.checked}),colorScheme:"blue"}),(0,s.jsxs)(t.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Ephemeral Response"}),(0,s.jsx)(t.Text,{fontSize:"xs",color:f.colors.textSecondary,children:"Command response is only visible to the user who ran it"})]})]}),(0,s.jsxs)(t.HStack,{spacing:4,children:[(0,s.jsx)(t.Switch,{isChecked:k.deferReply,onChange:e=>M({deferReply:e.target.checked}),colorScheme:"orange"}),(0,s.jsxs)(t.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:f.colors.text,children:"Defer Reply"}),(0,s.jsx)(t.Text,{fontSize:"xs",color:f.colors.textSecondary,children:'Show "thinking..." message while processing (for slow commands)'})]})]})]})]})})]})]}),(0,s.jsx)(t.Button,{colorScheme:"blue",onClick:()=>{b.commandName=k.commandName,b.description=k.description,b.options=k.options,b.permissions=k.permissions,b.cooldown=k.cooldown,b.guildOnly=k.guildOnly,b.adminOnly=k.adminOnly,b.allowDMs=k.allowDMs,b.category=k.category,b.examples=k.examples,b.ephemeral=k.ephemeral,b.deferReply=k.deferReply,b.label=k.commandName?"/".concat(k.commandName):"Command",T()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});m.displayName="CommandNode";let x=m},99538:(e,o,n)=>{n.d(o,{A:()=>x});var s=n(94513),r=n(94285),i=n(6179),t=n(60423),a=n(56237),c=n(27263);let l={user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.roles}",description:"User Roles (array)",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions (array)",icon:"\uD83D\uDD10"},{name:"{user.isBot}",description:"Is Bot (true/false)",icon:"\uD83E\uDD16"},{name:"{user.createdAt}",description:"Account Creation Date",icon:"\uD83D\uDCC5"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.nsfw}",description:"Is NSFW (true/false)",icon:"\uD83D\uDD1E"},{name:"{channel.memberCount}",description:"Member Count (number)",icon:"\uD83D\uDC65"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.memberCount}",description:"Total Members (number)",icon:"\uD83D\uDC65"},{name:"{server.boostLevel}",description:"Boost Level (number)",icon:"\uD83D\uDE80"},{name:"{server.owner}",description:"Server Owner ID",icon:"\uD83D\uDC51"}],message:[{name:"{message.content}",description:"Message Content",icon:"\uD83D\uDCAC"},{name:"{message.length}",description:"Message Length (number)",icon:"\uD83D\uDCCF"},{name:"{message.mentions}",description:"Message Mentions (array)",icon:"\uD83D\uDCE2"},{name:"{message.attachments}",description:"Attachments Count (number)",icon:"\uD83D\uDCCE"},{name:"{message.embeds}",description:"Embeds Count (number)",icon:"\uD83D\uDCCB"}],api:[{name:"{response.status}",description:"HTTP Status Code (number)",icon:"\uD83D\uDD22"},{name:"{response.data}",description:"Response Data",icon:"\uD83D\uDCCA"},{name:"{response.error}",description:"Error Message",icon:"❌"},{name:"{response.length}",description:"Response Array Length",icon:"\uD83D\uDCCF"}],time:[{name:"{time.hour}",description:"Current Hour (0-23)",icon:"\uD83D\uDD50"},{name:"{time.day}",description:"Day of Week (0-6)",icon:"\uD83D\uDCC5"},{name:"{time.date}",description:"Current Date",icon:"\uD83D\uDCC6"},{name:"{time.timestamp}",description:"Unix Timestamp",icon:"⏰"}],random:[{name:"{random.number}",description:"Random Number (1-100)",icon:"\uD83C\uDFB2"},{name:"{random.boolean}",description:"Random True/False",icon:"\uD83C\uDFAF"}]},d=[{value:"userHasRole",label:"\uD83C\uDFAD User Has Role",category:"User",description:"Check if user has a specific role"},{value:"userIsAdmin",label:"\uD83D\uDC51 User Is Admin",category:"User",description:"Check if user is server admin"},{value:"userHasPermission",label:"\uD83D\uDD10 User Has Permission",category:"User",description:"Check if user has specific permission"},{value:"userIsBot",label:"\uD83E\uDD16 User Is Bot",category:"User",description:"Check if user is a bot"},{value:"userJoinedRecently",label:"\uD83D\uDEAA User Joined Recently",category:"User",description:"Check if user joined within timeframe"},{value:"messageContains",label:"\uD83D\uDCAC Message Contains",category:"Message",description:"Check if message contains text"},{value:"messageLength",label:"\uD83D\uDCCF Message Length",category:"Message",description:"Check message character count"},{value:"messageHasMentions",label:"\uD83D\uDCE2 Message Has Mentions",category:"Message",description:"Check if message mentions users/roles"},{value:"messageHasAttachments",label:"\uD83D\uDCCE Message Has Attachments",category:"Message",description:"Check if message has files"},{value:"messageHasEmbeds",label:"\uD83D\uDCCB Message Has Embeds",category:"Message",description:"Check if message has embeds"},{value:"channelType",label:"\uD83D\uDCFA Channel Type",category:"Channel",description:"Check channel type (text, voice, etc.)"},{value:"channelIsNSFW",label:"\uD83D\uDD1E Channel Is NSFW",category:"Channel",description:"Check if channel is NSFW"},{value:"channelMemberCount",label:"\uD83D\uDC65 Channel Member Count",category:"Channel",description:"Check voice channel member count"},{value:"serverMemberCount",label:"\uD83D\uDC65 Server Member Count",category:"Server",description:"Check total server members"},{value:"serverBoostLevel",label:"\uD83D\uDE80 Server Boost Level",category:"Server",description:"Check server boost level"},{value:"serverName",label:"\uD83C\uDFE0 Server Name",category:"Server",description:"Check server name"},{value:"timeOfDay",label:"\uD83D\uDD50 Time of Day",category:"Time",description:"Check current hour of day"},{value:"dayOfWeek",label:"\uD83D\uDCC5 Day of Week",category:"Time",description:"Check day of the week"},{value:"apiResponseStatus",label:"\uD83D\uDD22 API Response Status",category:"API",description:"Check HTTP status code"},{value:"apiResponseData",label:"\uD83D\uDCCA API Response Data",category:"API",description:"Check API response content"},{value:"customVariable",label:"⚙️ Custom Variable",category:"Custom",description:"Check custom variable value"},{value:"randomChance",label:"\uD83C\uDFB2 Random Chance",category:"Custom",description:"Random percentage chance"}],h=[{value:"equals",label:"= Equals",description:"Exact match"},{value:"notEquals",label:"≠ Not Equals",description:"Does not match"},{value:"contains",label:"\uD83D\uDD0D Contains",description:"Contains substring"},{value:"notContains",label:"\uD83D\uDEAB Not Contains",description:"Does not contain substring"},{value:"startsWith",label:"▶️ Starts With",description:"Begins with text"},{value:"endsWith",label:"◀️ Ends With",description:"Ends with text"},{value:"greaterThan",label:"> Greater Than",description:"Numeric greater than"},{value:"lessThan",label:"< Less Than",description:"Numeric less than"},{value:"greaterEqual",label:"≥ Greater or Equal",description:"Numeric greater than or equal"},{value:"lessEqual",label:"≤ Less or Equal",description:"Numeric less than or equal"},{value:"regex",label:"\uD83D\uDD0D Regex Match",description:"Regular expression pattern"},{value:"inArray",label:"\uD83D\uDCCB In Array",description:"Value exists in array"},{value:"hasLength",label:"\uD83D\uDCCF Has Length",description:"Array/string has specific length"}],m=(0,r.memo)(e=>{var o,n;let{data:m,selected:x,id:p,updateNodeData:u}=e,{currentScheme:g}=(0,c.DP)(),{isOpen:b,onOpen:j,onClose:S}=(0,t.useDisclosure)(),[v,f]=(0,r.useState)(()=>({operator:"equals",logicalOperator:"AND",caseSensitive:!1,conditions:[],...m})),[C,y]=(0,r.useState)(!1),T=e=>{f(o=>({...o,...e}))},k=e=>{let o=d.find(o=>o.value===e);return o?o.label.split(" ").slice(1).join(" "):e},A=e=>{navigator.clipboard.writeText(e)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(t.Box,{bg:g.colors.surface,border:"2px solid ".concat(x?"#f59e0b":g.colors.border),borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(i.h7,{type:"target",position:i.yX.Top,style:{background:"#f59e0b",border:"2px solid ".concat(g.colors.surface),width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(t.VStack,{spacing:1,align:"stretch",children:[(0,s.jsxs)(t.HStack,{justify:"space-between",align:"center",children:[(0,s.jsxs)(t.HStack,{spacing:1,children:[(0,s.jsx)(t.Box,{bg:"orange.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(a.lrG,{})}),(0,s.jsx)(t.Text,{fontSize:"xs",fontWeight:"bold",color:g.colors.text,children:"Condition"})]}),(0,s.jsx)(t.IconButton,{icon:(0,s.jsx)(a.VSk,{}),size:"xs",variant:"ghost",onClick:j,"aria-label":"Configure condition"})]}),(0,s.jsx)(t.Box,{children:(0,s.jsxs)(t.HStack,{spacing:1,children:[v.conditionType&&(0,s.jsx)(t.Text,{fontSize:"xs",children:(e=>{let o=d.find(o=>o.value===e);return o?o.label.split(" ")[0]:"❓"})(v.conditionType)}),(0,s.jsx)(t.Text,{fontSize:"xs",color:g.colors.text,noOfLines:1,children:v.conditionType?k(v.conditionType):"Select Condition"})]})}),v.operator&&v.value&&(0,s.jsx)(t.Box,{children:(0,s.jsxs)(t.Text,{fontSize:"xs",color:g.colors.textSecondary,noOfLines:1,children:[(e=>{var o;return(null==(o=h.find(o=>o.value===e))?void 0:o.label)||e})(v.operator).split(" ").slice(1).join(" "),' "',v.value.length>15?v.value.substring(0,15)+"...":v.value,'"']})}),(0,s.jsxs)(t.HStack,{spacing:1,justify:"space-between",children:[(0,s.jsx)(t.Badge,{size:"xs",colorScheme:"green",children:"TRUE"}),(0,s.jsx)(t.Badge,{size:"xs",colorScheme:"red",children:"FALSE"})]})]}),(0,s.jsx)(i.h7,{type:"source",position:i.yX.Bottom,id:"true",style:{background:"#38a169",border:"2px solid ".concat(g.colors.surface),width:"12px",height:"12px",bottom:"-6px",left:"25%",transform:"translateX(-50%)"}}),(0,s.jsx)(i.h7,{type:"source",position:i.yX.Bottom,id:"false",style:{background:"#e53e3e",border:"2px solid ".concat(g.colors.surface),width:"12px",height:"12px",bottom:"-6px",left:"75%",transform:"translateX(-50%)"}})]}),(0,s.jsxs)(t.Modal,{isOpen:b,onClose:()=>{u&&p&&u(p,v),S()},size:"4xl",children:[(0,s.jsx)(t.ModalOverlay,{bg:"blackAlpha.600"}),(0,s.jsxs)(t.ModalContent,{bg:g.colors.background,border:"2px solid",borderColor:"orange.400",maxW:"1200px",children:[(0,s.jsx)(t.ModalHeader,{color:g.colors.text,children:"❓ Configure Condition"}),(0,s.jsx)(t.ModalCloseButton,{}),(0,s.jsx)(t.ModalBody,{pb:6,children:(0,s.jsxs)(t.VStack,{spacing:6,align:"stretch",children:[(0,s.jsxs)(t.Box,{children:[(0,s.jsxs)(t.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:g.colors.text,children:"Available Variables"}),(0,s.jsxs)(t.Button,{size:"sm",variant:"ghost",leftIcon:C?(0,s.jsx)(a._NO,{}):(0,s.jsx)(a.Vap,{}),onClick:()=>y(!C),children:[C?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(t.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(t.AlertIcon,{}),(0,s.jsx)(t.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your conditions! Click any variable below to copy it. Conditions determine which path (TRUE or FALSE) the flow takes."})]}),(0,s.jsx)(t.Collapse,{in:C,animateOpacity:!0,children:(0,s.jsx)(t.Box,{bg:g.colors.surface,border:"1px solid",borderColor:g.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)(t.Accordion,{allowMultiple:!0,children:Object.entries(l).map(e=>{let[o,n]=e;return(0,s.jsxs)(t.AccordionItem,{border:"none",children:[(0,s.jsxs)(t.AccordionButton,{px:0,py:2,children:[(0,s.jsx)(t.Box,{flex:"1",textAlign:"left",children:(0,s.jsxs)(t.Text,{fontSize:"sm",fontWeight:"bold",color:g.colors.text,textTransform:"capitalize",children:[o," Variables"]})}),(0,s.jsx)(t.AccordionIcon,{})]}),(0,s.jsx)(t.AccordionPanel,{px:0,py:2,children:(0,s.jsx)(t.VStack,{spacing:2,align:"stretch",children:n.map(e=>(0,s.jsxs)(t.HStack,{spacing:2,p:2,bg:g.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:g.colors.surface},onClick:()=>A(e.name),children:[(0,s.jsx)(t.Text,{fontSize:"sm",children:e.icon}),(0,s.jsx)(t.Code,{fontSize:"xs",colorScheme:"orange",children:e.name}),(0,s.jsx)(t.Text,{fontSize:"xs",color:g.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(t.IconButton,{icon:(0,s.jsx)(a.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),A(e.name)}})]},e.name))})})]},o)})})})})]}),(0,s.jsx)(t.Divider,{}),(0,s.jsxs)(t.Tabs,{variant:"enclosed",colorScheme:"orange",children:[(0,s.jsxs)(t.TabList,{children:[(0,s.jsx)(t.Tab,{children:"Basic Condition"}),(0,s.jsx)(t.Tab,{children:"Advanced"})]}),(0,s.jsxs)(t.TabPanels,{children:[(0,s.jsx)(t.TabPanel,{children:(0,s.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(t.FormControl,{isRequired:!0,children:[(0,s.jsx)(t.FormLabel,{color:g.colors.text,children:"Condition Type"}),(0,s.jsx)(t.Select,{value:v.conditionType||"",onChange:e=>T({conditionType:e.target.value}),placeholder:"Select a condition type",bg:g.colors.background,color:g.colors.text,borderColor:g.colors.border,children:Object.entries(d.reduce((e,o)=>(e[o.category]||(e[o.category]=[]),e[o.category].push(o),e),{})).map(e=>{let[o,n]=e;return(0,s.jsx)("optgroup",{label:o,children:n.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))},o)})})]}),v.conditionType&&(0,s.jsxs)(t.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(t.AlertIcon,{}),(0,s.jsxs)(t.Box,{children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:null==(o=d.find(e=>e.value===v.conditionType))?void 0:o.label}),(0,s.jsx)(t.Text,{fontSize:"sm",children:null==(n=d.find(e=>e.value===v.conditionType))?void 0:n.description})]})]}),(0,s.jsxs)(t.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(t.FormControl,{children:[(0,s.jsx)(t.FormLabel,{color:g.colors.text,children:"Operator"}),(0,s.jsx)(t.Select,{value:v.operator||"equals",onChange:e=>T({operator:e.target.value}),bg:g.colors.background,color:g.colors.text,borderColor:g.colors.border,children:h.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)(t.FormControl,{children:[(0,s.jsx)(t.FormLabel,{color:g.colors.text,children:"Compare Value"}),(0,s.jsx)(t.Input,{value:v.value||"",onChange:e=>T({value:e.target.value}),placeholder:"userHasRole"===v.conditionType?"Member or {user.roles}":"messageContains"===v.conditionType?"hello or {message.content}":"serverMemberCount"===v.conditionType?"100 or {server.memberCount}":"timeOfDay"===v.conditionType?"14 (for 2 PM)":"Value to compare against",bg:g.colors.background,color:g.colors.text,borderColor:g.colors.border})]})]}),(0,s.jsxs)(t.FormControl,{children:[(0,s.jsx)(t.FormLabel,{color:g.colors.text,children:"Description"}),(0,s.jsx)(t.Textarea,{value:v.description||"",onChange:e=>T({description:e.target.value}),placeholder:"Describe what this condition checks for",bg:g.colors.background,color:g.colors.text,borderColor:g.colors.border,minH:"80px"})]}),(0,s.jsxs)(t.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(t.AlertIcon,{}),(0,s.jsxs)(t.Box,{children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:"Understanding TRUE/FALSE Paths"}),(0,s.jsx)(t.Text,{fontSize:"sm",children:"• **TRUE (Left)**: Actions that run when the condition passes • **FALSE (Right)**: Actions that run when the condition fails • You can connect different actions to each path"})]})]})]})}),(0,s.jsx)(t.TabPanel,{children:(0,s.jsxs)(t.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(t.Text,{fontSize:"lg",fontWeight:"bold",color:g.colors.text,children:"Advanced Settings"}),(0,s.jsx)(t.VStack,{spacing:4,align:"stretch",children:(0,s.jsxs)(t.HStack,{spacing:4,children:[(0,s.jsx)(t.Switch,{isChecked:v.caseSensitive,onChange:e=>T({caseSensitive:e.target.checked}),colorScheme:"orange"}),(0,s.jsxs)(t.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",color:g.colors.text,children:"Case Sensitive"}),(0,s.jsx)(t.Text,{fontSize:"xs",color:g.colors.textSecondary,children:"Match exact capitalization for text comparisons"})]})]})}),(0,s.jsxs)(t.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(t.AlertIcon,{}),(0,s.jsxs)(t.Box,{children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",mb:2,children:"\uD83D\uDCA1 Condition Examples:"}),(0,s.jsxs)(t.VStack,{align:"start",spacing:1,fontSize:"sm",children:[(0,s.jsx)(t.Text,{children:'• Check if user has "Admin" role: **User Has Role** equals "Admin"'}),(0,s.jsx)(t.Text,{children:'• Check if message contains swear word: **Message Contains** contains "badword"'}),(0,s.jsx)(t.Text,{children:"• Check if server has many members: **Server Member Count** greater than 1000"}),(0,s.jsx)(t.Text,{children:"• Check if it's nighttime: **Time of Day** greater than 22"}),(0,s.jsx)(t.Text,{children:"• Check API status: **API Response Status** equals 200"})]})]})]}),(0,s.jsxs)(t.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(t.AlertIcon,{}),(0,s.jsxs)(t.Box,{children:[(0,s.jsx)(t.Text,{fontSize:"sm",fontWeight:"bold",mb:2,children:"⚠️ Important Notes:"}),(0,s.jsxs)(t.VStack,{align:"start",spacing:1,fontSize:"sm",children:[(0,s.jsxs)(t.Text,{children:["• Use variables like ","{user.roles}"," to check dynamic values"]}),(0,s.jsx)(t.Text,{children:"• Number comparisons work with Greater/Less Than operators"}),(0,s.jsx)(t.Text,{children:"• Text comparisons work with Contains, Starts With, etc."}),(0,s.jsx)(t.Text,{children:"• Always connect both TRUE and FALSE paths for complete logic"})]})]})]})]})})]})]}),(0,s.jsx)(t.Button,{colorScheme:"orange",onClick:()=>{m.conditionType=v.conditionType,m.operator=v.operator,m.value=v.value,m.caseSensitive=v.caseSensitive,m.description=v.description,m.logicalOperator=v.logicalOperator,m.label=v.conditionType?k(v.conditionType):"Condition",S()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});m.displayName="ConditionNode";let x=m}}]);