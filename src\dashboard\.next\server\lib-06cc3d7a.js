exports.id=3640,exports.ids=[3640],exports.modules={286:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sidecar=void 0;var n=r(5818),o=n.__importStar(r(82015)),a=r(69177);t.sidecar=function(e,t){var r=function(){return t};return function(u){var i=(0,a.useSidecar)(e,u.sideCar),c=i[0];return i[1]&&t?r:c?o.createElement(c,n.__assign({},u)):null}}},784:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useMergeRefs=void 0;var n=r(5818).__importStar(r(82015)),o=r(28096),a=r(97562),u="undefined"!=typeof window?n.useLayoutEffect:n.useEffect,i=new WeakMap;t.useMergeRefs=function(e,t){var r=(0,a.useCallbackRef)(t||null,function(t){return e.forEach(function(e){return(0,o.assignRef)(e,t)})});return u(function(){var t=i.get(r);if(t){var n=new Set(t),a=new Set(e),u=r.current;n.forEach(function(e){a.has(e)||(0,o.assignRef)(e,null)}),a.forEach(function(e){n.has(e)||(0,o.assignRef)(e,u)})}i.set(r,e)},[e]),r}},2403:(e,t)=>{"use strict";t._=function(e){return e&&e.__esModule?e:{default:e}}},5004:(e,t,r)=>{"use strict";var n=r(82015),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,u=n.useEffect,i=n.useLayoutEffect,c=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),o=n[0].inst,l=n[1];return i(function(){o.value=r,o.getSnapshot=t,s(o)&&l({inst:o})},[e,r,t]),u(function(){return s(o)&&l({inst:o}),e(function(){s(o)&&l({inst:o})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:l},5150:(e,t,r)=>{"use strict";r.r(t),r.d(t,{scaleBand:()=>n.WH,scaleDiverging:()=>n.Mb,scaleDivergingLog:()=>n.Cr,scaleDivergingPow:()=>n.yj,scaleDivergingSqrt:()=>n.q9,scaleDivergingSymlog:()=>n.xh,scaleIdentity:()=>n.jo,scaleImplicit:()=>n.U4,scaleLinear:()=>n.m4,scaleLog:()=>n.ZE,scaleOrdinal:()=>n.UM,scalePoint:()=>n.hq,scalePow:()=>n.RW,scaleQuantile:()=>n.QL,scaleQuantize:()=>n.WT,scaleRadial:()=>n.af,scaleSequential:()=>n.ex,scaleSequentialLog:()=>n.M3,scaleSequentialPow:()=>n.ui,scaleSequentialQuantile:()=>n.T,scaleSequentialSqrt:()=>n.ye,scaleSequentialSymlog:()=>n.nV,scaleSqrt:()=>n.Bv,scaleSymlog:()=>n.aX,scaleThreshold:()=>n.c3,scaleTime:()=>n.w7,scaleUtc:()=>n.Pp,tickFormat:()=>n.Vr});var n=r(31465)},5818:(e,t,r)=>{"use strict";r.r(t),r.d(t,{__addDisposableResource:()=>A,__assign:()=>a,__asyncDelegator:()=>P,__asyncGenerator:()=>S,__asyncValues:()=>E,__await:()=>O,__awaiter:()=>v,__classPrivateFieldGet:()=>T,__classPrivateFieldIn:()=>I,__classPrivateFieldSet:()=>D,__createBinding:()=>b,__decorate:()=>i,__disposeResources:()=>W,__esDecorate:()=>s,__exportStar:()=>h,__extends:()=>o,__generator:()=>y,__importDefault:()=>k,__importStar:()=>C,__makeTemplateObject:()=>R,__metadata:()=>p,__param:()=>c,__propKey:()=>f,__read:()=>g,__rest:()=>u,__rewriteRelativeImportExtension:()=>q,__runInitializers:()=>l,__setFunctionName:()=>d,__spread:()=>_,__spreadArray:()=>j,__spreadArrays:()=>w,__values:()=>m,default:()=>V});var n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var a=function(){return(a=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function u(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}function i(e,t,r,n){var o,a=arguments.length,u=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)u=Reflect.decorate(e,t,r,n);else for(var i=e.length-1;i>=0;i--)(o=e[i])&&(u=(a<3?o(u):a>3?o(t,r,u):o(t,r))||u);return a>3&&u&&Object.defineProperty(t,r,u),u}function c(e,t){return function(r,n){t(r,n,e)}}function s(e,t,r,n,o,a){function u(e){if(void 0!==e&&"function"!=typeof e)throw TypeError("Function expected");return e}for(var i,c=n.kind,s="getter"===c?"get":"setter"===c?"set":"value",l=!t&&e?n.static?e:e.prototype:null,f=t||(l?Object.getOwnPropertyDescriptor(l,n.name):{}),d=!1,p=r.length-1;p>=0;p--){var v={};for(var y in n)v[y]="access"===y?{}:n[y];for(var y in n.access)v.access[y]=n.access[y];v.addInitializer=function(e){if(d)throw TypeError("Cannot add initializers after decoration has completed");a.push(u(e||null))};var b=(0,r[p])("accessor"===c?{get:f.get,set:f.set}:f[s],v);if("accessor"===c){if(void 0===b)continue;if(null===b||"object"!=typeof b)throw TypeError("Object expected");(i=u(b.get))&&(f.get=i),(i=u(b.set))&&(f.set=i),(i=u(b.init))&&o.unshift(i)}else(i=u(b))&&("field"===c?o.unshift(i):f[s]=i)}l&&Object.defineProperty(l,n.name,f),d=!0}function l(e,t,r){for(var n=arguments.length>2,o=0;o<t.length;o++)r=n?t[o].call(e,r):t[o].call(e);return n?r:void 0}function f(e){return"symbol"==typeof e?e:"".concat(e)}function d(e,t,r){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})}function p(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function v(e,t,r,n){return new(r||(r=Promise))(function(o,a){function u(e){try{c(n.next(e))}catch(e){a(e)}}function i(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(u,i)}c((n=n.apply(e,t||[])).next())})}function y(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},u=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return u.next=i(0),u.throw=i(1),u.return=i(2),"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function i(i){return function(c){var s=[i,c];if(r)throw TypeError("Generator is already executing.");for(;u&&(u=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===s[0]||2===s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}}}var b=Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]};function h(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||b(t,e,r)}function m(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function g(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),u=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)u.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return u}function _(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(g(arguments[t]));return e}function w(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;for(var n=Array(e),o=0,t=0;t<r;t++)for(var a=arguments[t],u=0,i=a.length;u<i;u++,o++)n[o]=a[u];return n}function j(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function O(e){return this instanceof O?(this.v=e,this):new O(e)}function S(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(e,t||[]),a=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),u("next"),u("throw"),u("return",function(e){return function(t){return Promise.resolve(t).then(e,s)}}),n[Symbol.asyncIterator]=function(){return this},n;function u(e,t){o[e]&&(n[e]=function(t){return new Promise(function(r,n){a.push([e,t,r,n])>1||i(e,t)})},t&&(n[e]=t(n[e])))}function i(e,t){try{var r;(r=o[e](t)).value instanceof O?Promise.resolve(r.value.v).then(c,s):l(a[0][2],r)}catch(e){l(a[0][3],e)}}function c(e){i("next",e)}function s(e){i("throw",e)}function l(e,t){e(t),a.shift(),a.length&&i(a[0][0],a[0][1])}}function P(e){var t,r;return t={},n("next"),n("throw",function(e){throw e}),n("return"),t[Symbol.iterator]=function(){return this},t;function n(n,o){t[n]=e[n]?function(t){return(r=!r)?{value:O(e[n](t)),done:!1}:o?o(t):t}:o}}function E(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=m(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,o){var a,u,i;a=n,u=o,i=(t=e[r](t)).done,Promise.resolve(t.value).then(function(e){a({value:e,done:i})},u)})}}}function R(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var M=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},x=function(e){return(x=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t})(e)};function C(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=x(e),n=0;n<r.length;n++)"default"!==r[n]&&b(t,e,r[n]);return M(t,e),t}function k(e){return e&&e.__esModule?e:{default:e}}function T(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)}function D(e,t,r,n,o){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(e,r):o?o.value=r:t.set(e,r),r}function I(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function A(e,t,r){if(null!=t){var n,o;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(r){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(void 0===n){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(o=n)}if("function"!=typeof n)throw TypeError("Object not disposable.");o&&(n=function(){try{o.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}var L="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};function W(e){function t(t){e.error=e.hasError?new L(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}var r,n=0;return function o(){for(;r=e.stack.pop();)try{if(!r.async&&1===n)return n=0,e.stack.push(r),Promise.resolve().then(o);if(r.dispose){var a=r.dispose.call(r.value);if(r.async)return n|=2,Promise.resolve(a).then(o,function(e){return t(e),o()})}else n|=1}catch(e){t(e)}if(1===n)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()}function q(e,t){return"string"==typeof e&&/^\.\.?\//.test(e)?e.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(e,r,n,o,a){return r?t?".jsx":".js":!n||o&&a?n+o+"."+a.toLowerCase()+"js":e}):e}let V={__extends:o,__assign:a,__rest:u,__decorate:i,__param:c,__esDecorate:s,__runInitializers:l,__propKey:f,__setFunctionName:d,__metadata:p,__awaiter:v,__generator:y,__createBinding:b,__exportStar:h,__values:m,__read:g,__spread:_,__spreadArrays:w,__spreadArray:j,__await:O,__asyncGenerator:S,__asyncDelegator:P,__asyncValues:E,__makeTemplateObject:R,__importStar:C,__importDefault:k,__classPrivateFieldGet:T,__classPrivateFieldSet:D,__classPrivateFieldIn:I,__addDisposableResource:A,__disposeResources:W,__rewriteRelativeImportExtension:q}},7665:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.env=void 0,t.env={isNode:r(41108).isNode,forceCache:!1}},9373:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=7311==r.j||null,o="Invariant failed";function a(e,t){if(!e){if(n)throw Error(o);var r="function"==typeof t?t():t;throw Error(r?"".concat(o,": ").concat(r):o)}}},12742:(e,t)=>{"use strict";function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}t._=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&Object.prototype.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(o,u,i):o[u]=e[u]}return o.default=e,n&&n.set(e,o),o}},13658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useRefToCallback=t.refToCallback=t.transformRef=t.useTransformRef=t.useMergeRefs=t.mergeRefs=t.createCallbackRef=t.useCallbackRef=t.assignRef=void 0;var n=r(28096);Object.defineProperty(t,"assignRef",{enumerable:!0,get:function(){return n.assignRef}});var o=r(97562);Object.defineProperty(t,"useCallbackRef",{enumerable:!0,get:function(){return o.useCallbackRef}});var a=r(62309);Object.defineProperty(t,"createCallbackRef",{enumerable:!0,get:function(){return a.createCallbackRef}});var u=r(78173);Object.defineProperty(t,"mergeRefs",{enumerable:!0,get:function(){return u.mergeRefs}});var i=r(784);Object.defineProperty(t,"useMergeRefs",{enumerable:!0,get:function(){return i.useMergeRefs}});var c=r(79980);Object.defineProperty(t,"useTransformRef",{enumerable:!0,get:function(){return c.useTransformRef}});var s=r(21517);Object.defineProperty(t,"transformRef",{enumerable:!0,get:function(){return s.transformRef}});var l=r(24431);Object.defineProperty(t,"refToCallback",{enumerable:!0,get:function(){return l.refToCallback}}),Object.defineProperty(t,"useRefToCallback",{enumerable:!0,get:function(){return l.useRefToCallback}})},14799:(e,t,r)=>{"use strict";r.d(t,{BV:()=>n.BV,GZ:()=>n.GZ,HR:()=>n.HR,IA:()=>n.IA,IJ:()=>n.IJ,Lx:()=>n.Lx,N8:()=>n.N8,PG:()=>n.PG,Re:()=>n.Re,UP:()=>n.UP,Wc:()=>n.Wc,Wi:()=>n.Wi,Xf:()=>n.Xf,YW:()=>n.YW,Yu:()=>n.Yu,ZK:()=>n.ZK,dy:()=>n.dy,e9:()=>n.e9,hK:()=>n.hK,j:()=>n.j,lU:()=>n.lU,n8:()=>n.n8,nV:()=>n.nV,qI:()=>n.qI,qr:()=>n.qr,rM:()=>n.rM,t$:()=>n.t$,ux:()=>n.ux,yD:()=>n.yD});var n=r(66550)},21517:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.transformRef=void 0;var n=r(28096),o=r(62309);t.transformRef=function(e,t){return(0,o.createCallbackRef)(function(r){return(0,n.assignRef)(e,t(r))})}},24431:(e,t)=>{"use strict";function r(e){return function(t){"function"==typeof e?e(t):e&&(e.current=t)}}Object.defineProperty(t,"__esModule",{value:!0}),t.useRefToCallback=t.refToCallback=void 0,t.refToCallback=r;var n=function(){return null},o=new WeakMap,a=function(e){var t=e||n,a=o.get(t);if(a)return a;var u=r(t);return o.set(t,u),u};t.useRefToCallback=function(e){return a(e)}},27767:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,r=[],n=0;n<e.rangeCount;n++)r.push(e.getRangeAt(n));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||r.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},28096:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assignRef=void 0,t.assignRef=function(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}},30005:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.exportSidecar=void 0;var n=r(5818),o=n.__importStar(r(82015)),a=function(e){var t=e.sideCar,r=n.__rest(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var a=t.read();if(!a)throw Error("Sidecar medium not found");return o.createElement(a,n.__assign({},r))};a.isSideCarExport=!0,t.exportSidecar=function(e,t){return e.useMedium(t),a}},34960:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.exportSidecar=t.renderCar=t.createSidecarMedium=t.createMedium=t.setConfig=t.useSidecar=t.sidecar=void 0;var n=r(286);Object.defineProperty(t,"sidecar",{enumerable:!0,get:function(){return n.sidecar}});var o=r(69177);Object.defineProperty(t,"useSidecar",{enumerable:!0,get:function(){return o.useSidecar}});var a=r(92250);Object.defineProperty(t,"setConfig",{enumerable:!0,get:function(){return a.setConfig}});var u=r(83181);Object.defineProperty(t,"createMedium",{enumerable:!0,get:function(){return u.createMedium}}),Object.defineProperty(t,"createSidecarMedium",{enumerable:!0,get:function(){return u.createSidecarMedium}});var i=r(52511);Object.defineProperty(t,"renderCar",{enumerable:!0,get:function(){return i.renderCar}});var c=r(30005);Object.defineProperty(t,"exportSidecar",{enumerable:!0,get:function(){return c.exportSidecar}})},36315:(e,t,r)=>{"use strict";e.exports=r(5004)},50544:(e,t,r)=>{"use strict";r.d(t,{Yy:()=>_});var n=e=>void 0!==e.nodeType;var o=()=>"undefined"!=typeof document,a=e=>o()&&e.test(function(){let e=navigator.userAgentData;return e?.platform??navigator.platform}()),u=()=>o()&&!!navigator.maxTouchPoints,i=!1,c=null,s=!1,l=!1,f=new Set;function d(e,t){f.forEach(r=>r(e,t))}var p="undefined"!=typeof window&&null!=window.navigator&&/^Mac/.test(window.navigator.platform);function v(e){s=!0,e.metaKey||!p&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key||(c="keyboard",d("keyboard",e))}function y(e){if(c="pointer","mousedown"===e.type||"pointerdown"===e.type){s=!0;let t=e.composedPath?e.composedPath()[0]:e.target,r=!1;try{r=t.matches(":focus-visible")}catch{}r||d("pointer",e)}}function b(e){(0===e.mozInputSource&&e.isTrusted||0===e.detail&&!e.pointerType)&&(s=!0,c="virtual")}function h(e){e.target!==window&&e.target!==document&&(e.target instanceof Element&&e.target.hasAttribute("tabindex")||(s||l||(c="virtual",d("virtual",e)),s=!1,l=!1))}function m(){s=!1,l=!0}function g(){return"pointer"!==c}function _(e){(function(){if(!o()||i)return;let{focus:e}=HTMLElement.prototype;HTMLElement.prototype.focus=function(...t){s=!0,e.apply(this,t)},document.addEventListener("keydown",v,!0),document.addEventListener("keyup",v,!0),document.addEventListener("click",b,!0),window.addEventListener("focus",h,!0),window.addEventListener("blur",m,!1),"undefined"!=typeof PointerEvent?(document.addEventListener("pointerdown",y,!0),document.addEventListener("pointermove",y,!0),document.addEventListener("pointerup",y,!0)):(document.addEventListener("mousedown",y,!0),document.addEventListener("mousemove",y,!0),document.addEventListener("mouseup",y,!0)),i=!0})(),e(g());let t=()=>e(g());return f.add(t),()=>{f.delete(t)}}},51053:(e,t,r)=>{"use strict";e.exports=r(78997)},52511:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.renderCar=void 0;var n=r(5818),o=n.__importStar(r(82015)),a=r(82015);t.renderCar=function(e,t){function r(t){var r=t.stateRef,u=t.props,i=(0,a.useCallback)(function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return(0,a.useLayoutEffect)(function(){r.current(e)}),null},[]);return o.createElement(e,n.__assign({},u,{children:i}))}var u=o.memo(function(e){var t=e.stateRef,r=e.defaultState,n=e.children,o=(0,a.useState)(r.current),u=o[0],i=o[1];return(0,a.useEffect)(function(){t.current=i},[]),n.apply(void 0,u)},function(){return!0});return function(e){var n=o.useRef(t(e)),a=o.useRef(function(e){return n.current=e});return o.createElement(o.Fragment,null,o.createElement(r,{stateRef:a,props:e}),o.createElement(u,{stateRef:a,defaultState:n,children:e.children}))}}},54903:(e,t,r)=>{},62309:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createCallbackRef=void 0,t.createCallbackRef=function(e){var t=null;return{get current(){return t},set current(value){var r=t;r!==value&&(t=value,e(value,r))}}}},69177:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useSidecar=void 0;var n=r(82015),o=r(7665),a=new WeakMap,u={};t.useSidecar=function(e,t){var r,i,c,s,l,f,d,p,v,y,b=t&&t.options||u;return o.env.isNode&&!b.ssr?[null,null]:(r=e,c=(i=t)&&i.options||u,s=o.env.forceCache||o.env.isNode&&!!c.ssr||!c.async,f=(l=(0,n.useState)(s?function(){return a.get(r)}:void 0))[0],d=l[1],v=(p=(0,n.useState)(null))[0],y=p[1],(0,n.useEffect)(function(){f||r().then(function(e){var t,n=i?i.read():e.default||e;if(!n)throw console.error("Sidecar error: with importer",r),i?(console.error("Sidecar error: with medium",i),t=Error("Sidecar medium was not found")):t=Error("Sidecar was not found in exports"),y(function(){return t}),t;a.set(r,n),d(function(){return n})},function(e){return y(function(){return e})})},[]),[f,v])}},72915:(e,t,r)=>{"use strict";var n=r(82015);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},78173:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mergeRefs=void 0;var n=r(28096),o=r(62309);t.mergeRefs=function(e){return(0,o.createCallbackRef)(function(t){return e.forEach(function(e){return(0,n.assignRef)(e,t)})})}},78997:(e,t,r)=>{"use strict";var n=r(82015),o=r(36315),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},u=o.useSyncExternalStore,i=n.useRef,c=n.useEffect,s=n.useMemo,l=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,o){var f=i(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=u(e,(f=s(function(){function e(e){if(!c){if(c=!0,u=e,e=n(e),void 0!==o&&d.hasValue){var t=d.value;if(o(t,e))return i=t}return i=e}if(t=i,a(u,e))return t;var r=n(e);return void 0!==o&&o(t,r)?(u=e,t):(u=e,i=r)}var u,i,c=!1,s=void 0===r?null:r;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,r,n,o]))[0],f[1]);return c(function(){d.hasValue=!0,d.value=p},[p]),l(p),p}},79980:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useTransformRef=void 0;var n=r(28096),o=r(97562);t.useTransformRef=function(e,t){return(0,o.useCallbackRef)(null,function(r){return(0,n.assignRef)(e,t(r))})}},83181:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createSidecarMedium=t.createMedium=void 0;var n=r(5818);function o(e){return e}function a(e,t){void 0===t&&(t=o);var r=[],n=!1;return{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:e},useMedium:function(e){var o=t(e,n);return r.push(o),function(){r=r.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var o=r;r=[],o.forEach(e),t=r}var a=function(){var r=t;t=[],r.forEach(e)},u=function(){return Promise.resolve().then(a)};u(),r={push:function(e){t.push(e),u()},filter:function(e){return t=t.filter(e),r}}}}}t.createMedium=function(e,t){return void 0===t&&(t=o),a(e,t)},t.createSidecarMedium=function(e){void 0===e&&(e={});var t=a(null);return t.options=n.__assign({async:!0,ssr:!1},e),t}},87242:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>w});var n=e=>Array.isArray(e)?e:[e],o=0,a=null,u=class{revision=o;_value;_lastValue;_isEqual=i;constructor(e,t=i){this._value=this._lastValue=e,this._isEqual=t}get value(){return a?.add(this),this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++o)}};function i(e,t){return e===t}function c(e){return e instanceof u||console.warn("Not a valid cell! ",e),e.value}var s=(e,t)=>!1;function l(){return function(e,t=i){return new u(null,t)}(0,s)}var f=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=l()),c(t)};Symbol();var d=0,p=Object.getPrototypeOf({}),v=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,y);tag=l();tags={};children={};collectionTag=null;id=d++},y={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in p)return n;if("object"==typeof n&&null!==n){let r=e.children[t];return void 0===r&&(r=e.children[t]=function(e){return Array.isArray(e)?new b(e):new v(e)}(n)),r.tag&&c(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=l()).value=n),c(r),n}})(),ownKeys:e=>(f(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},b=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],h);tag=l();tags={};children={};collectionTag=null;id=d++},h={get:([e],t)=>("length"===t&&f(e),y.get(e,t)),ownKeys:([e])=>y.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>y.getOwnPropertyDescriptor(e,t),has:([e],t)=>y.has(e,t)},m="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function g(){return{s:0,v:void 0,o:null,p:null}}function _(e,t={}){let r,n=g(),{resultEqualityCheck:o}=t,a=0;function u(){let t,u=n,{length:i}=arguments;for(let e=0;e<i;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=u.o;null===e&&(u.o=e=new WeakMap);let r=e.get(t);void 0===r?(u=g(),e.set(t,u)):u=r}else{let e=u.p;null===e&&(u.p=e=new Map);let r=e.get(t);void 0===r?(u=g(),e.set(t,u)):u=r}}let c=u;if(1===u.s)t=u.v;else if(t=e.apply(null,arguments),a++,o){let e=r?.deref?.()??r;null!=e&&o(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new m(t):t}return c.s=1,c.v=t,t}return u.clearCache=()=>{n=g(),u.resetResultsCount()},u.resultsCount=()=>a,u.resetResultsCount=()=>{a=0},u}var w=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,o=(...e)=>{let t,o=0,a=0,u={},i=e.pop();"object"==typeof i&&(u=i,i=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(i,`createSelector expects an output function after the inputs, but received: [${typeof i}]`);let{memoize:c,memoizeOptions:s=[],argsMemoize:l=_,argsMemoizeOptions:f=[],devModeChecks:d={}}={...r,...u},p=n(s),v=n(f),y=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),b=c(function(){return o++,i.apply(null,arguments)},...p);return Object.assign(l(function(){a++;let e=function(e,t){let r=[],{length:n}=e;for(let o=0;o<n;o++)r.push(e[o].apply(null,t));return r}(y,arguments);return t=b.apply(null,e)},...v),{resultFunc:i,memoizedResultFunc:b,dependencies:y,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>o,resetRecomputations:()=>{o=0},memoize:c,argsMemoize:l})};return Object.assign(o,{withTypes:()=>o}),o}(_),j=Object.assign((e,t=w)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>j})},92250:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setConfig=t.config=void 0,t.config={onError:function(e){return console.error(e)}},t.setConfig=function(e){Object.assign(t.config,e)}},97562:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useCallbackRef=void 0;var n=r(82015);t.useCallbackRef=function(e,t){var r=(0,n.useState)(function(){return{value:e,callback:t,facade:{get current(){return r.value},set current(value){var n=r.value;n!==value&&(r.value=value,r.callback(value,n))}}}})[0];return r.callback=t,r.facade}},97645:(e,t,r)=>{"use strict";r(72915)}};