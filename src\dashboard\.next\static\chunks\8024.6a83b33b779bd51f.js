"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8024],{38024:(e,i,n)=>{n.r(i),n.d(i,{default:()=>x});var s=n(94513),r=n(94285),t=n(79028),o=n(5142),a=n(28365),l=n(78813),c=n(52545),u=n(5130),h=n(52442),d=n(3037);let p=[{id:1,scenario:"A user is repeatedly posting invite links to other Discord servers in general chat, despite being warned by other members.",context:"Server Rules: No advertising or self-promotion without permission."},{id:2,scenario:"Two users are having a heated argument about politics in the gaming channel, using increasingly hostile language.",context:"Server Rules: Keep discussions on-topic, no political discussions, maintain respectful communication."},{id:3,scenario:"A member reports that another user is sending them unwanted DMs with inappropriate content.",context:"Server Rules: No harassment, respect privacy, no NSFW content."},{id:4,scenario:"A user is spamming emojis and text across multiple channels simultaneously.",context:"Server Rules: No spamming, maintain channel cleanliness."},{id:5,scenario:"A well-known member is caught using racial slurs in voice chat.",context:"Server Rules: Zero tolerance for hate speech and discrimination."},{id:6,scenario:"A user is sharing what appears to be leaked personal information about another member.",context:"Server Rules: No doxxing, respect privacy, protect personal information."},{id:7,scenario:"Multiple users are organizing a raid on another Discord server.",context:"Server Rules: No organizing or participating in raids, maintain good relations with other communities."},{id:8,scenario:"A user is repeatedly asking for free items/currency in the trading channel.",context:"Server Rules: No begging, follow trading channel guidelines."},{id:9,scenario:"A member is posting links to suspicious websites claiming to offer free Discord Nitro.",context:"Server Rules: No scam links, protect community safety."},{id:10,scenario:"A user is using alternate accounts to bypass a temporary mute.",context:"Server Rules: No ban/mute evasion, respect moderator actions."},{id:11,scenario:"Several users are sharing memes with subtle but inappropriate sexual references in the general chat.",context:"Server Rules: Keep content family-friendly, no NSFW content or innuendos."},{id:12,scenario:"A user is repeatedly mentioning everyone in non-emergency situations.",context:"Server Rules: Don't abuse mentions, respect notification settings."},{id:13,scenario:"A member is threatening self-harm in a public channel.",context:"Server Rules: Take mental health concerns seriously, have protocol for crisis situations."},{id:14,scenario:"Users are sharing copyrighted content (movies/games) in the media channel.",context:"Server Rules: No piracy, respect intellectual property rights."},{id:15,scenario:"A user is roleplaying inappropriately in serious discussion channels.",context:"Server Rules: Keep roleplay in designated channels, respect channel purposes."}],m=e=>[...p].sort(()=>Math.random()-.5).map((e,i)=>({...e,displayId:i+1})).slice(0,e),g=(0,r.memo)(e=>{let{scenario:i,response:n,onResponseChange:r}=e;return(0,s.jsx)(o.Z,{bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",mb:4,children:(0,s.jsx)(a.b,{children:(0,s.jsxs)(d.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(t.a,{children:[(0,s.jsxs)(u.E,{fontWeight:"bold",mb:2,children:["Scenario ",i.displayId,":"]}),(0,s.jsx)(u.E,{children:i.scenario})]}),(0,s.jsxs)(t.a,{children:[(0,s.jsx)(u.E,{fontWeight:"bold",color:"blue.300",mb:2,children:"Context:"}),(0,s.jsx)(u.E,{children:i.context})]}),(0,s.jsxs)(t.a,{children:[(0,s.jsx)(u.E,{fontWeight:"bold",color:"green.300",mb:2,children:"How would you handle this situation?"}),(0,s.jsx)(h.T,{value:n,onChange:e=>r(e.target.value),placeholder:"Explain your approach to handling this situation...",minH:"150px",bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"},resize:"vertical"})]})]})})})});g.displayName="ScenarioCard";let x=(0,r.memo)(e=>{let{onFormChange:i,initialData:n}=e,[o,a]=(0,r.useState)([]),[h,p]=(0,r.useState)(n||{});(0,r.useEffect)(()=>{a(m(15))},[]),(0,r.useEffect)(()=>{let e=setTimeout(()=>{i(h)},100);return()=>clearTimeout(e)},[h,i]);let x=(0,r.useCallback)((e,i)=>{p(n=>({...n,[e]:i}))},[]),b=Object.keys(h).length/o.length*100;return(0,s.jsxs)(d.T,{spacing:6,align:"stretch",position:"relative",children:[(0,s.jsxs)(t.a,{position:"sticky",top:0,bg:"gray.800",p:4,zIndex:1,children:[(0,s.jsx)(l.D,{size:"md",mb:2,children:"Moderation Scenarios"}),(0,s.jsx)(c.k,{value:b,size:"sm",colorScheme:"blue",borderRadius:"full"}),(0,s.jsxs)(u.E,{mt:2,fontSize:"sm",color:"gray.400",children:[Math.round(b),"% Complete (",Object.keys(h).length," of ",o.length," scenarios)"]})]}),o.map(e=>(0,s.jsx)(g,{scenario:e,response:h[e.id]||"",onResponseChange:i=>x(e.id,i)},e.id))]})})}}]);