"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4020],{3033:(e,t,n)=>{n.d(t,{R:()=>i});var s=n(20552),r=n(42946),o=n(93092);function i(e,t,n){let i;if((0,r.wz)(e)&&(e=e.contents),(0,r.Ll)(e))return e;if((0,r.tO)(e)){let t=n.schema[r.lC].createNode?.(n.schema,null,n);return t.items.push(e),t}(e instanceof String||e instanceof Number||e instanceof Boolean||"undefined"!=typeof BigInt&&e instanceof BigInt)&&(e=e.valueOf());let{aliasDuplicateObjects:a,onAnchor:c,onTagObj:l,schema:u,sourceObjects:h}=n;if(a&&e&&"object"==typeof e){if(i=h.get(e))return i.anchor??(i.anchor=c(e)),new s.x(i.anchor);i={anchor:null,node:null},h.set(e,i)}t?.startsWith("!!")&&(t="tag:yaml.org,2002:"+t.slice(2));let f=function(e,t,n){if(t){let e=n.filter(e=>e.tag===t),s=e.find(e=>!e.format)??e[0];if(!s)throw Error(`Tag ${t} not found`);return s}return n.find(t=>t.identify?.(e)&&!t.format)}(e,t,u.tags);if(!f){if(e&&"function"==typeof e.toJSON&&(e=e.toJSON()),!e||"object"!=typeof e){let t=new o.X(e);return i&&(i.node=t),t}f=e instanceof Map?u[r.lC]:Symbol.iterator in Object(e)?u[r.kN]:u[r.lC]}l&&(l(f),delete n.onTagObj);let m=f?.createNode?f.createNode(n.schema,e,n):"function"==typeof f?.nodeClass?.from?f.nodeClass.from(n.schema,e,n):new o.X(e);return t?m.tag=t:f.default||(m.tag=f.tag),i&&(i.node=m),m}},4995:(e,t,n)=>{n.d(t,{R:()=>o});var s=n(93092),r=n(48897);function o(e,t,n){let o,c,{offset:l,type:u,source:h,end:f}=e,m=(e,t,s)=>n(l+e,t,s);switch(u){case"scalar":o=s.X.PLAIN,c=function(e,t){let n="";switch(e[0]){case"	":n="a tab character";break;case",":n="flow indicator character ,";break;case"%":n="directive indicator character %";break;case"|":case">":n=`block scalar indicator ${e[0]}`;break;case"@":case"`":n=`reserved character ${e[0]}`}return n&&t(0,"BAD_SCALAR_START",`Plain value cannot start with ${n}`),i(e)}(h,m);break;case"single-quoted-scalar":var d,g;o=s.X.QUOTE_SINGLE,d=h,g=m,("'"!==d[d.length-1]||1===d.length)&&g(d.length,"MISSING_CHAR","Missing closing 'quote"),c=i(d.slice(1,-1)).replace(/''/g,"'");break;case"double-quoted-scalar":o=s.X.QUOTE_DOUBLE,c=function(e,t){let n="";for(let s=1;s<e.length-1;++s){let r=e[s];if("\r"!==r||"\n"!==e[s+1])if("\n"===r){let{fold:t,offset:r}=function(e,t){let n="",s=e[t+1];for(;(" "===s||"	"===s||"\n"===s||"\r"===s)&&("\r"!==s||"\n"===e[t+2]);)"\n"===s&&(n+="\n"),t+=1,s=e[t+1];return n||(n=" "),{fold:n,offset:t}}(e,s);n+=t,s=r}else if("\\"===r){let r=e[++s],o=a[r];if(o)n+=o;else if("\n"===r)for(r=e[s+1];" "===r||"	"===r;)r=e[++s+1];else if("\r"===r&&"\n"===e[s+1])for(r=e[++s+1];" "===r||"	"===r;)r=e[++s+1];else if("x"===r||"u"===r||"U"===r){let o={x:2,u:4,U:8}[r];n+=function(e,t,n,s){let r=e.substr(t,n),o=r.length===n&&/^[0-9a-fA-F]+$/.test(r)?parseInt(r,16):NaN;if(isNaN(o)){let r=e.substr(t-2,n+2);return s(t-2,"BAD_DQ_ESCAPE",`Invalid escape sequence ${r}`),r}return String.fromCodePoint(o)}(e,s+1,o,t),s+=o}else{let r=e.substr(s-1,2);t(s-1,"BAD_DQ_ESCAPE",`Invalid escape sequence ${r}`),n+=r}}else if(" "===r||"	"===r){let t=s,o=e[s+1];for(;" "===o||"	"===o;)o=e[++s+1];"\n"!==o&&("\r"!==o||"\n"!==e[s+2])&&(n+=s>t?e.slice(t,s+1):r)}else n+=r}return('"'!==e[e.length-1]||1===e.length)&&t(e.length,"MISSING_CHAR",'Missing closing "quote'),n}(h,m);break;default:return n(e,"UNEXPECTED_TOKEN",`Expected a flow scalar value, but found: ${u}`),{value:"",type:null,comment:"",range:[l,l+h.length,l+h.length]}}let p=l+h.length,y=(0,r.U)(f,p,t,n);return{value:c,type:o,comment:y.comment,range:[l,p,y.offset]}}function i(e){let t,n;try{t=RegExp("(.*?)(?<![ 	])[ 	]*\r?\n","sy"),n=RegExp("[ 	]*(.*?)(?:(?<![ 	])[ 	]*)?\r?\n","sy")}catch{t=/(.*?)[ \t]*\r?\n/sy,n=/[ \t]*(.*?)[ \t]*\r?\n/sy}let s=t.exec(e);if(!s)return e;let r=s[1],o=" ",i=t.lastIndex;for(n.lastIndex=i;s=n.exec(e);)""===s[1]?"\n"===o?r+=o:o="\n":(r+=o+s[1],o=" "),i=n.lastIndex;let a=/[ \t]*(.*)/sy;return a.lastIndex=i,s=a.exec(e),r+o+(s?.[1]??"")}let a={0:"\0",a:"\x07",b:"\b",e:"\x1b",f:"\f",n:"\n",r:"\r",t:"	",v:"\v",N:"\x85",_:"\xa0",L:"\u2028",P:"\u2029"," ":" ",'"':'"',"/":"/","\\":"\\","	":"	"}},39919:(e,t,n)=>{n.d(t,{D:()=>a});var s=n(42946),r=n(12257);let o={"!":"%21",",":"%2C","[":"%5B","]":"%5D","{":"%7B","}":"%7D"},i=e=>e.replace(/[!,[\]{}]/g,e=>o[e]);class a{constructor(e,t){this.docStart=null,this.docEnd=!1,this.yaml=Object.assign({},a.defaultYaml,e),this.tags=Object.assign({},a.defaultTags,t)}clone(){let e=new a(this.yaml,this.tags);return e.docStart=this.docStart,e}atDocument(){let e=new a(this.yaml,this.tags);switch(this.yaml.version){case"1.1":this.atNextDocument=!0;break;case"1.2":this.atNextDocument=!1,this.yaml={explicit:a.defaultYaml.explicit,version:"1.2"},this.tags=Object.assign({},a.defaultTags)}return e}add(e,t){this.atNextDocument&&(this.yaml={explicit:a.defaultYaml.explicit,version:"1.1"},this.tags=Object.assign({},a.defaultTags),this.atNextDocument=!1);let n=e.trim().split(/[ \t]+/),s=n.shift();switch(s){case"%TAG":{if(2!==n.length&&(t(0,"%TAG directive should contain exactly two parts"),n.length<2))return!1;let[e,s]=n;return this.tags[e]=s,!0}case"%YAML":{if(this.yaml.explicit=!0,1!==n.length)return t(0,"%YAML directive should contain exactly one part"),!1;let[e]=n;if("1.1"===e||"1.2"===e)return this.yaml.version=e,!0;{let n=/^\d+\.\d+$/.test(e);return t(6,`Unsupported YAML version ${e}`,n),!1}}default:return t(0,`Unknown directive ${s}`,!0),!1}}tagName(e,t){if("!"===e)return"!";if("!"!==e[0])return t(`Not a valid tag: ${e}`),null;if("<"===e[1]){let n=e.slice(2,-1);return"!"===n||"!!"===n?(t(`Verbatim tags aren't resolved, so ${e} is invalid.`),null):(">"!==e[e.length-1]&&t("Verbatim tags must end with a >"),n)}let[,n,s]=e.match(/^(.*!)([^!]*)$/s);s||t(`The ${e} tag has no suffix`);let r=this.tags[n];if(r)try{return r+decodeURIComponent(s)}catch(e){return t(String(e)),null}return"!"===n?e:(t(`Could not resolve tag: ${e}`),null)}tagString(e){for(let[t,n]of Object.entries(this.tags))if(e.startsWith(n))return t+i(e.substring(n.length));return"!"===e[0]?e:`!<${e}>`}toString(e){let t,n=this.yaml.explicit?[`%YAML ${this.yaml.version||"1.2"}`]:[],o=Object.entries(this.tags);if(e&&o.length>0&&(0,s.Ll)(e.contents)){let n={};(0,r.Y)(e.contents,(e,t)=>{(0,s.Ll)(t)&&t.tag&&(n[t.tag]=!0)}),t=Object.keys(n)}else t=[];for(let[s,r]of o)("!!"!==s||"tag:yaml.org,2002:"!==r)&&(!e||t.some(e=>e.startsWith(r)))&&n.push(`%TAG ${s} ${r}`);return n.join("\n")}}a.defaultYaml={explicit:!1,version:"1.2"},a.defaultTags={"!!":"tag:yaml.org,2002:"}},40028:(e,t,n)=>{n.d(t,{y:()=>d});var s=n(20552),r=n(83844),o=n(42946),i=n(73813),a=n(21358),c=n(33220),l=n(97436),u=n(43313),h=n(68156),f=n(3033),m=n(39919);class d{constructor(e,t,n){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,o.Qu,{value:o.dQ});let s=null;"function"==typeof t||Array.isArray(t)?s=t:void 0===n&&t&&(n=t,t=void 0);let r=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,stringKeys:!1,uniqueKeys:!0,version:"1.2"},n);this.options=r;let{version:i}=r;n?._directives?(this.directives=n._directives.atDocument(),this.directives.yaml.explicit&&(i=this.directives.yaml.version)):this.directives=new m.D({version:i}),this.setSchema(i,n),this.contents=void 0===e?null:this.createNode(e,s,n)}clone(){let e=Object.create(d.prototype,{[o.Qu]:{value:o.dQ}});return e.commentBefore=this.commentBefore,e.comment=this.comment,e.errors=this.errors.slice(),e.warnings=this.warnings.slice(),e.options=Object.assign({},this.options),this.directives&&(e.directives=this.directives.clone()),e.schema=this.schema.clone(),e.contents=(0,o.Ll)(this.contents)?this.contents.clone(e.schema):this.contents,this.range&&(e.range=this.range.slice()),e}add(e){g(this.contents)&&this.contents.add(e)}addIn(e,t){g(this.contents)&&this.contents.addIn(e,t)}createAlias(e,t){if(!e.anchor){let n=(0,u.ig)(this);e.anchor=!t||n.has(t)?(0,u.lw)(t||"a",n):t}return new s.x(e.anchor)}createNode(e,t,n){let s;if("function"==typeof t)e=t.call({"":e},"",e),s=t;else if(Array.isArray(t)){let e=t.filter(e=>"number"==typeof e||e instanceof String||e instanceof Number).map(String);e.length>0&&(t=t.concat(e)),s=t}else void 0===n&&t&&(n=t,t=void 0);let{aliasDuplicateObjects:r,anchorPrefix:i,flow:a,keepUndefined:c,onTagObj:l,tag:h}=n??{},{onAnchor:m,setAnchors:d,sourceObjects:g}=(0,u.DB)(this,i||"a"),p={aliasDuplicateObjects:r??!0,keepUndefined:c??!1,onAnchor:m,onTagObj:l,replacer:s,schema:this.schema,sourceObjects:g},y=(0,f.R)(e,h,p);return a&&(0,o.P3)(y)&&(y.flow=!0),d(),y}createPair(e,t,n={}){let s=this.createNode(e,null,n),r=this.createNode(t,null,n);return new i.R(s,r)}delete(e){return!!g(this.contents)&&this.contents.delete(e)}deleteIn(e){return(0,r.bn)(e)?null!=this.contents&&(this.contents=null,!0):!!g(this.contents)&&this.contents.deleteIn(e)}get(e,t){return(0,o.P3)(this.contents)?this.contents.get(e,t):void 0}getIn(e,t){return(0,r.bn)(e)?!t&&(0,o.jn)(this.contents)?this.contents.value:this.contents:(0,o.P3)(this.contents)?this.contents.getIn(e,t):void 0}has(e){return!!(0,o.P3)(this.contents)&&this.contents.has(e)}hasIn(e){return(0,r.bn)(e)?void 0!==this.contents:!!(0,o.P3)(this.contents)&&this.contents.hasIn(e)}set(e,t){null==this.contents?this.contents=(0,r.GP)(this.schema,[e],t):g(this.contents)&&this.contents.set(e,t)}setIn(e,t){(0,r.bn)(e)?this.contents=t:null==this.contents?this.contents=(0,r.GP)(this.schema,Array.from(e),t):g(this.contents)&&this.contents.setIn(e,t)}setSchema(e,t={}){let n;switch("number"==typeof e&&(e=String(e)),e){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new m.D({version:"1.1"}),n={resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=e:this.directives=new m.D({version:e}),n={resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,n=null;break;default:{let t=JSON.stringify(e);throw Error(`Expected '1.1', '1.2' or null as first argument, but found: ${t}`)}}if(t.schema instanceof Object)this.schema=t.schema;else if(n)this.schema=new c.S(Object.assign(n,t));else throw Error("With a null YAML version, the { schema: Schema } option is required")}toJS({json:e,jsonArg:t,mapAsMap:n,maxAliasCount:s,onAnchor:r,reviver:o}={}){let i={anchors:new Map,doc:this,keep:!e,mapAsMap:!0===n,mapKeyWarned:!1,maxAliasCount:"number"==typeof s?s:100},c=(0,a.H)(this.contents,t??"",i);if("function"==typeof r)for(let{count:e,res:t}of i.anchors.values())r(t,e);return"function"==typeof o?(0,h.a)(o,{"":c},"",c):c}toJSON(e,t){return this.toJS({json:!0,jsonArg:e,mapAsMap:!1,onAnchor:t})}toString(e={}){if(this.errors.length>0)throw Error("Document with errors cannot be stringified");if("indent"in e&&(!Number.isInteger(e.indent)||0>=Number(e.indent))){let t=JSON.stringify(e.indent);throw Error(`"indent" option must be a positive integer, not ${t}`)}return(0,l.Z)(this,e)}}function g(e){if((0,o.P3)(e))return!0;throw Error("Expected a YAML collection as document contents")}},43313:(e,t,n)=>{n.d(t,{DB:()=>c,ig:()=>i,lw:()=>a,qN:()=>o});var s=n(42946),r=n(12257);function o(e){if(/[\x00-\x19\s,[\]{}]/.test(e)){let t=JSON.stringify(e);throw Error(`Anchor must not contain whitespace or control characters: ${t}`)}return!0}function i(e){let t=new Set;return(0,r.Y)(e,{Value(e,n){n.anchor&&t.add(n.anchor)}}),t}function a(e,t){for(let n=1;;++n){let s=`${e}${n}`;if(!t.has(s))return s}}function c(e,t){let n=[],r=new Map,o=null;return{onAnchor:s=>{n.push(s),o??(o=i(e));let r=a(t,o);return o.add(r),r},setAnchors:()=>{for(let e of n){let t=r.get(e);if("object"==typeof t&&t.anchor&&((0,s.jn)(t.node)||(0,s.P3)(t.node)))t.node.anchor=t.anchor;else{let t=Error("Failed to resolve repeated object (this should not happen)");throw t.source=e,t}}},sourceObjects:r}}},48897:(e,t,n)=>{n.d(t,{U:()=>s});function s(e,t,n,s){let r="";if(e){let o=!1,i="";for(let a of e){let{source:e,type:c}=a;switch(c){case"space":o=!0;break;case"comment":{n&&!o&&s(a,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let t=e.substring(1)||" ";r?r+=i+t:r=t,i="";break}case"newline":r&&(i+=e),o=!0;break;default:s(a,"UNEXPECTED_TOKEN",`Unexpected ${c} at node end`)}t+=e.length}}return{comment:r,offset:t}}},68156:(e,t,n)=>{n.d(t,{a:()=>function e(t,n,s,r){if(r&&"object"==typeof r)if(Array.isArray(r))for(let n=0,s=r.length;n<s;++n){let s=r[n],o=e(t,r,String(n),s);void 0===o?delete r[n]:o!==s&&(r[n]=o)}else if(r instanceof Map)for(let n of Array.from(r.keys())){let s=r.get(n),o=e(t,r,n,s);void 0===o?r.delete(n):o!==s&&r.set(n,o)}else if(r instanceof Set)for(let n of Array.from(r)){let s=e(t,r,n,n);void 0===s?r.delete(n):s!==n&&(r.delete(n),r.add(s))}else for(let[n,s]of Object.entries(r)){let o=e(t,r,n,s);void 0===o?delete r[n]:o!==s&&(r[n]=o)}return t.call(n,s,r)}})},71832:(e,t,n)=>{n.d(t,{y:()=>r});var s=n(93092);function r(e,t,n){let r=t.offset,o=function({offset:e,props:t},n,s){if("block-scalar-header"!==t[0].type)return s(t[0],"IMPOSSIBLE","Block scalar header not found"),null;let{source:r}=t[0],o=r[0],i=0,a="",c=-1;for(let t=1;t<r.length;++t){let n=r[t];if(a||"-"!==n&&"+"!==n){let s=Number(n);!i&&s?i=s:-1===c&&(c=e+t)}else a=n}-1!==c&&s(c,"UNEXPECTED_TOKEN",`Block scalar header includes extra characters: ${r}`);let l=!1,u="",h=r.length;for(let e=1;e<t.length;++e){let r=t[e];switch(r.type){case"space":l=!0;case"newline":h+=r.source.length;break;case"comment":n&&!l&&s(r,"MISSING_CHAR","Comments must be separated from other tokens by white space characters"),h+=r.source.length,u=r.source.substring(1);break;case"error":s(r,"UNEXPECTED_TOKEN",r.message),h+=r.source.length;break;default:{let e=`Unexpected token in block scalar header: ${r.type}`;s(r,"UNEXPECTED_TOKEN",e);let t=r.source;t&&"string"==typeof t&&(h+=t.length)}}}return{mode:o,indent:i,chomp:a,comment:u,length:h}}(t,e.options.strict,n);if(!o)return{value:"",type:null,comment:"",range:[r,r,r]};let i=">"===o.mode?s.X.BLOCK_FOLDED:s.X.BLOCK_LITERAL,a=t.source?function(e){let t=e.split(/\n( *)/),n=t[0],s=n.match(/^( *)/),r=[s?.[1]?[s[1],n.slice(s[1].length)]:["",n]];for(let e=1;e<t.length;e+=2)r.push([t[e],t[e+1]]);return r}(t.source):[],c=a.length;for(let e=a.length-1;e>=0;--e){let t=a[e][1];if(""===t||"\r"===t)c=e;else break}if(0===c){let e="+"===o.chomp&&a.length>0?"\n".repeat(Math.max(1,a.length-1)):"",n=r+o.length;return t.source&&(n+=t.source.length),{value:e,type:i,comment:o.comment,range:[r,n,n]}}let l=t.indent+o.indent,u=t.offset+o.length,h=0;for(let t=0;t<c;++t){let[s,r]=a[t];if(""===r||"\r"===r)0===o.indent&&s.length>l&&(l=s.length);else{s.length<l&&n(u+s.length,"MISSING_CHAR","Block scalars with more-indented leading empty lines must use an explicit indentation indicator"),0===o.indent&&(l=s.length),h=t,0!==l||e.atRoot||n(u,"BAD_INDENT","Block scalar values in collections must be indented");break}u+=s.length+r.length+1}for(let e=a.length-1;e>=c;--e)a[e][0].length>l&&(c=e+1);let f="",m="",d=!1;for(let e=0;e<h;++e)f+=a[e][0].slice(l)+"\n";for(let e=h;e<c;++e){let[t,r]=a[e];u+=t.length+r.length+1;let c="\r"===r[r.length-1];if(c&&(r=r.slice(0,-1)),r&&t.length<l){let e=o.indent?"explicit indentation indicator":"first line",s=`Block scalar lines must not be less indented than their ${e}`;n(u-r.length-(c?2:1),"BAD_INDENT",s),t=""}i===s.X.BLOCK_LITERAL?(f+=m+t.slice(l)+r,m="\n"):t.length>l||"	"===r[0]?(" "===m?m="\n":d||"\n"!==m||(m="\n\n"),f+=m+t.slice(l)+r,m="\n",d=!0):""===r?"\n"===m?f+="\n":m="\n":(f+=m+r,m=" ",d=!1)}switch(o.chomp){case"-":break;case"+":for(let e=c;e<a.length;++e)f+="\n"+a[e][0].slice(l);"\n"!==f[f.length-1]&&(f+="\n");break;default:f+="\n"}let g=r+o.length+t.source.length;return{value:f,type:i,comment:o.comment,range:[r,g,g]}}},93244:(e,t,n)=>{n.d(t,{D:()=>O});var s=n(39919),r=n(40028),o=n(33365),i=n(42946),a=n(20552),c=n(93092),l=n(46343),u=n(17706),h=n(73813);function f(e,{flow:t,indicator:n,next:s,offset:r,onError:o,parentIndent:i,startOnNewline:a}){let c=!1,l=a,u=a,h="",f="",m=!1,d=!1,g=null,p=null,y=null,b=null,E=null,w=null,k=null;for(let r of e)switch(d&&("space"!==r.type&&"newline"!==r.type&&"comma"!==r.type&&o(r.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),d=!1),g&&(l&&"comment"!==r.type&&"newline"!==r.type&&o(g,"TAB_AS_INDENT","Tabs are not allowed as indentation"),g=null),r.type){case"space":!t&&("doc-start"!==n||s?.type!=="flow-collection")&&r.source.includes("	")&&(g=r),u=!0;break;case"comment":{u||o(r,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let e=r.source.substring(1)||" ";h?h+=f+e:h=e,f="",l=!1;break}case"newline":l?h?h+=r.source:w&&"seq-item-ind"===n||(c=!0):f+=r.source,l=!0,m=!0,(p||y)&&(b=r),u=!0;break;case"anchor":p&&o(r,"MULTIPLE_ANCHORS","A node can have at most one anchor"),r.source.endsWith(":")&&o(r.offset+r.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),p=r,k??(k=r.offset),l=!1,u=!1,d=!0;break;case"tag":y&&o(r,"MULTIPLE_TAGS","A node can have at most one tag"),y=r,k??(k=r.offset),l=!1,u=!1,d=!0;break;case n:(p||y)&&o(r,"BAD_PROP_ORDER",`Anchors and tags must be after the ${r.source} indicator`),w&&o(r,"UNEXPECTED_TOKEN",`Unexpected ${r.source} in ${t??"collection"}`),w=r,l="seq-item-ind"===n||"explicit-key-ind"===n,u=!1;break;case"comma":if(t){E&&o(r,"UNEXPECTED_TOKEN",`Unexpected , in ${t}`),E=r,l=!1,u=!1;break}default:o(r,"UNEXPECTED_TOKEN",`Unexpected ${r.type} token`),l=!1,u=!1}let I=e[e.length-1],A=I?I.offset+I.source.length:r;return d&&s&&"space"!==s.type&&"newline"!==s.type&&"comma"!==s.type&&("scalar"!==s.type||""!==s.source)&&o(s.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),g&&(l&&g.indent<=i||s?.type==="block-map"||s?.type==="block-seq")&&o(g,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:E,found:w,spaceBefore:c,comment:h,hasNewline:m,anchor:p,tag:y,newlineAfterProp:b,end:A,start:k??A}}function m(e){if(!e)return null;switch(e.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(e.source.includes("\n"))return!0;if(e.end){for(let t of e.end)if("newline"===t.type)return!0}return!1;case"flow-collection":for(let t of e.items){for(let e of t.start)if("newline"===e.type)return!0;if(t.sep){for(let e of t.sep)if("newline"===e.type)return!0}if(m(t.key)||m(t.value))return!0}return!1;default:return!0}}function d(e,t,n){if(t?.type==="flow-collection"){let s=t.end[0];s.indent===e&&("]"===s.source||"}"===s.source)&&m(t)&&n(s,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}function g(e,t,n){let{uniqueKeys:s}=e.options;if(!1===s)return!1;let r="function"==typeof s?s:(e,t)=>e===t||(0,i.jn)(e)&&(0,i.jn)(t)&&e.value===t.value;return t.some(e=>r(e.key,n))}let p="All mapping items must start at the same column";var y=n(48897);let b="Block collections are not allowed within flow collections",E=e=>e&&("block-map"===e.type||"block-seq"===e.type);function w(e,t,n,s,r,o){let a="block-map"===n.type?function({composeNode:e,composeEmptyNode:t},n,s,r,o){let i=new(o?.nodeClass??l.C)(n.schema);n.atRoot&&(n.atRoot=!1);let a=s.offset,c=null;for(let o of s.items){let{start:l,key:u,sep:y,value:b}=o,E=f(l,{indicator:"explicit-key-ind",next:u??y?.[0],offset:a,onError:r,parentIndent:s.indent,startOnNewline:!0}),w=!E.found;if(w){if(u&&("block-seq"===u.type?r(a,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in u&&u.indent!==s.indent&&r(a,"BAD_INDENT",p)),!E.anchor&&!E.tag&&!y){c=E.end,E.comment&&(i.comment?i.comment+="\n"+E.comment:i.comment=E.comment);continue}(E.newlineAfterProp||m(u))&&r(u??l[l.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else E.found?.indent!==s.indent&&r(a,"BAD_INDENT",p);n.atKey=!0;let k=E.end,I=u?e(n,u,E,r):t(n,k,l,null,E,r);n.schema.compat&&d(s.indent,u,r),n.atKey=!1,g(n,i.items,I)&&r(k,"DUPLICATE_KEY","Map keys must be unique");let A=f(y??[],{indicator:"map-value-ind",next:b,offset:I.range[2],onError:r,parentIndent:s.indent,startOnNewline:!u||"block-scalar"===u.type});if(a=A.end,A.found){w&&(b?.type!=="block-map"||A.hasNewline||r(a,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),n.options.strict&&E.start<A.found.offset-1024&&r(I.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));let c=b?e(n,b,A,r):t(n,a,y,null,A,r);n.schema.compat&&d(s.indent,b,r),a=c.range[2];let l=new h.R(I,c);n.options.keepSourceTokens&&(l.srcToken=o),i.items.push(l)}else{w&&r(I.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),A.comment&&(I.comment?I.comment+="\n"+A.comment:I.comment=A.comment);let e=new h.R(I);n.options.keepSourceTokens&&(e.srcToken=o),i.items.push(e)}}return c&&c<a&&r(c,"IMPOSSIBLE","Map comment with trailing content"),i.range=[s.offset,a,c??a],i}(e,t,n,s,o):"block-seq"===n.type?function({composeNode:e,composeEmptyNode:t},n,s,r,o){let i=new(o?.nodeClass??u.R)(n.schema);n.atRoot&&(n.atRoot=!1),n.atKey&&(n.atKey=!1);let a=s.offset,c=null;for(let{start:o,value:l}of s.items){let u=f(o,{indicator:"seq-item-ind",next:l,offset:a,onError:r,parentIndent:s.indent,startOnNewline:!0});if(!u.found)if(u.anchor||u.tag||l)l&&"block-seq"===l.type?r(u.end,"BAD_INDENT","All sequence items must start at the same column"):r(a,"MISSING_CHAR","Sequence item without - indicator");else{c=u.end,u.comment&&(i.comment=u.comment);continue}let h=l?e(n,l,u,r):t(n,u.end,o,null,u,r);n.schema.compat&&d(s.indent,l,r),a=h.range[2],i.items.push(h)}return i.range=[s.offset,a,c??a],i}(e,t,n,s,o):function({composeNode:e,composeEmptyNode:t},n,s,r,o){let a="{"===s.start.source,c=a?"flow map":"flow sequence",d=new(o?.nodeClass??(a?l.C:u.R))(n.schema);d.flow=!0;let p=n.atRoot;p&&(n.atRoot=!1),n.atKey&&(n.atKey=!1);let w=s.offset+s.start.source.length;for(let o=0;o<s.items.length;++o){let u=s.items[o],{start:p,key:y,sep:k,value:I}=u,A=f(p,{flow:c,indicator:"explicit-key-ind",next:y??k?.[0],offset:w,onError:r,parentIndent:s.indent,startOnNewline:!1});if(!A.found){if(!A.anchor&&!A.tag&&!k&&!I){0===o&&A.comma?r(A.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${c}`):o<s.items.length-1&&r(A.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${c}`),A.comment&&(d.comment?d.comment+="\n"+A.comment:d.comment=A.comment),w=A.end;continue}!a&&n.options.strict&&m(y)&&r(y,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(0===o)A.comma&&r(A.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${c}`);else if(A.comma||r(A.start,"MISSING_CHAR",`Missing , between ${c} items`),A.comment){let e="";e:for(let t of p)switch(t.type){case"comma":case"space":break;case"comment":e=t.source.substring(1);break e;default:break e}if(e){let t=d.items[d.items.length-1];(0,i.tO)(t)&&(t=t.value??t.key),t.comment?t.comment+="\n"+e:t.comment=e,A.comment=A.comment.substring(e.length+1)}}if(a||k||A.found){n.atKey=!0;let o=A.end,i=y?e(n,y,A,r):t(n,o,p,null,A,r);E(y)&&r(i.range,"BLOCK_IN_FLOW",b),n.atKey=!1;let m=f(k??[],{flow:c,indicator:"map-value-ind",next:I,offset:i.range[2],onError:r,parentIndent:s.indent,startOnNewline:!1});if(m.found){if(!a&&!A.found&&n.options.strict){if(k)for(let e of k){if(e===m.found)break;if("newline"===e.type){r(e,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}A.start<m.found.offset-1024&&r(m.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else I&&("source"in I&&I.source&&":"===I.source[0]?r(I,"MISSING_CHAR",`Missing space after : in ${c}`):r(m.start,"MISSING_CHAR",`Missing , or : between ${c} items`));let N=I?e(n,I,m,r):m.found?t(n,m.end,k,null,m,r):null;N?E(I)&&r(N.range,"BLOCK_IN_FLOW",b):m.comment&&(i.comment?i.comment+="\n"+m.comment:i.comment=m.comment);let v=new h.R(i,N);if(n.options.keepSourceTokens&&(v.srcToken=u),a)g(n,d.items,i)&&r(o,"DUPLICATE_KEY","Map keys must be unique"),d.items.push(v);else{let e=new l.C(n.schema);e.flow=!0,e.items.push(v);let t=(N??i).range;e.range=[i.range[0],t[1],t[2]],d.items.push(e)}w=N?N.range[2]:m.end}else{let s=I?e(n,I,A,r):t(n,A.end,k,null,A,r);d.items.push(s),w=s.range[2],E(I)&&r(s.range,"BLOCK_IN_FLOW",b)}}let k=a?"}":"]",[I,...A]=s.end,N=w;if(I&&I.source===k)N=I.offset+I.source.length;else{let e=c[0].toUpperCase()+c.substring(1),t=p?`${e} must end with a ${k}`:`${e} in block collection must be sufficiently indented and end with a ${k}`;r(w,p?"MISSING_CHAR":"BAD_INDENT",t),I&&1!==I.source.length&&A.unshift(I)}if(A.length>0){let e=(0,y.U)(A,N,n.options.strict,r);e.comment&&(d.comment?d.comment+="\n"+e.comment:d.comment=e.comment),d.range=[s.offset,N,e.offset]}else d.range=[s.offset,N,N];return d}(e,t,n,s,o),c=a.constructor;return"!"===r||r===c.tagName?a.tag=c.tagName:r&&(a.tag=r),a}var k=n(71832),I=n(4995);function A(e,t,n,s){let r,o,{value:a,type:l,comment:u,range:h}="block-scalar"===t.type?(0,k.y)(e,t,s):(0,I.R)(t,e.options.strict,s),f=n?e.directives.tagName(n.source,e=>s(n,"TAG_RESOLVE_FAILED",e)):null;r=e.options.stringKeys&&e.atKey?e.schema[i.jf]:f?function(e,t,n,s,r){if("!"===n)return e[i.jf];let o=[];for(let t of e.tags)if(!t.collection&&t.tag===n)if(!t.default||!t.test)return t;else o.push(t);for(let e of o)if(e.test?.test(t))return e;let a=e.knownTags[n];return a&&!a.collection?(e.tags.push(Object.assign({},a,{default:!1,test:void 0})),a):(r(s,"TAG_RESOLVE_FAILED",`Unresolved tag: ${n}`,"tag:yaml.org,2002:str"!==n),e[i.jf])}(e.schema,a,f,n,s):"scalar"===t.type?function({atKey:e,directives:t,schema:n},s,r,o){let a=n.tags.find(t=>(!0===t.default||e&&"key"===t.default)&&t.test?.test(s))||n[i.jf];if(n.compat){let e=n.compat.find(e=>e.default&&e.test?.test(s))??n[i.jf];if(a.tag!==e.tag){let n=t.tagString(a.tag),s=t.tagString(e.tag);o(r,"TAG_RESOLVE_FAILED",`Value may be parsed as either ${n} or ${s}`,!0)}}return a}(e,a,t,s):e.schema[i.jf];try{let l=r.resolve(a,e=>s(n??t,"TAG_RESOLVE_FAILED",e),e.options);o=(0,i.jn)(l)?l:new c.X(l)}catch(e){s(n??t,"TAG_RESOLVE_FAILED",e instanceof Error?e.message:String(e)),o=new c.X(a)}return o.range=h,o.source=a,l&&(o.type=l),f&&(o.tag=f),r.format&&(o.format=r.format),u&&(o.comment=u),o}let N={composeNode:v,composeEmptyNode:_};function v(e,t,n,s){let r,o=e.atKey,{spaceBefore:h,comment:f,anchor:m,tag:d}=n,g=!0;switch(t.type){case"alias":r=function({options:e},{offset:t,source:n,end:s},r){let o=new a.x(n.substring(1));""===o.source&&r(t,"BAD_ALIAS","Alias cannot be an empty string"),o.source.endsWith(":")&&r(t+n.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);let i=t+n.length,c=(0,y.U)(s,i,e.strict,r);return o.range=[t,i,c.offset],c.comment&&(o.comment=c.comment),o}(e,t,s),(m||d)&&s(t,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":r=A(e,t,d,s),m&&(r.anchor=m.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":r=function(e,t,n,s,r){let o=s.tag,a=o?t.directives.tagName(o.source,e=>r(o,"TAG_RESOLVE_FAILED",e)):null;if("block-seq"===n.type){let{anchor:e,newlineAfterProp:t}=s,n=e&&o?e.offset>o.offset?e:o:e??o;n&&(!t||t.offset<n.offset)&&r(n,"MISSING_CHAR","Missing newline after block sequence props")}let h="block-map"===n.type?"map":"block-seq"===n.type?"seq":"{"===n.start.source?"map":"seq";if(!o||!a||"!"===a||a===l.C.tagName&&"map"===h||a===u.R.tagName&&"seq"===h)return w(e,t,n,r,a);let f=t.schema.tags.find(e=>e.tag===a&&e.collection===h);if(!f){let s=t.schema.knownTags[a];if(!s||s.collection!==h)return s?r(o,"BAD_COLLECTION_TYPE",`${s.tag} used for ${h} collection, but expects ${s.collection??"scalar"}`,!0):r(o,"TAG_RESOLVE_FAILED",`Unresolved tag: ${a}`,!0),w(e,t,n,r,a);t.schema.tags.push(Object.assign({},s,{default:!1})),f=s}let m=w(e,t,n,r,a,f),d=f.resolve?.(m,e=>r(o,"TAG_RESOLVE_FAILED",e),t.options)??m,g=(0,i.Ll)(d)?d:new c.X(d);return g.range=m.range,g.tag=a,f?.format&&(g.format=f.format),g}(N,e,t,n,s),m&&(r.anchor=m.source.substring(1));break;default:{let o="error"===t.type?t.message:`Unsupported token (type: ${t.type})`;s(t,"UNEXPECTED_TOKEN",o),r=_(e,t.offset,void 0,null,n,s),g=!1}}return m&&""===r.anchor&&s(m,"BAD_ALIAS","Anchor cannot be an empty string"),o&&e.options.stringKeys&&(!(0,i.jn)(r)||"string"!=typeof r.value||r.tag&&"tag:yaml.org,2002:str"!==r.tag)&&s(d??t,"NON_STRING_KEY","With stringKeys, all keys must be strings"),h&&(r.spaceBefore=!0),f&&("scalar"===t.type&&""===t.source?r.comment=f:r.commentBefore=f),e.options.keepSourceTokens&&g&&(r.srcToken=t),r}function _(e,t,n,s,{spaceBefore:r,comment:o,anchor:i,tag:a,end:c},l){let u=A(e,{type:"scalar",offset:function(e,t,n){if(t){n??(n=t.length);for(let s=n-1;s>=0;--s){let n=t[s];switch(n.type){case"space":case"comment":case"newline":e-=n.source.length;continue}for(n=t[++s];n?.type==="space";)e+=n.source.length,n=t[++s];break}}return e}(t,n,s),indent:-1,source:""},a,l);return i&&(u.anchor=i.source.substring(1),""===u.anchor&&l(i,"BAD_ALIAS","Anchor cannot be an empty string")),r&&(u.spaceBefore=!0),o&&(u.comment=o,u.range[2]=c),u}function S(e){if("number"==typeof e)return[e,e+1];if(Array.isArray(e))return 2===e.length?e:[e[0],e[1]];let{offset:t,source:n}=e;return[t,t+("string"==typeof n?n.length:1)]}function T(e){let t="",n=!1,s=!1;for(let r=0;r<e.length;++r){let o=e[r];switch(o[0]){case"#":t+=(""===t?"":s?"\n\n":"\n")+(o.substring(1)||" "),n=!0,s=!1;break;case"%":e[r+1]?.[0]!=="#"&&(r+=1),n=!1;break;default:n||(s=!0),n=!1}}return{comment:t,afterEmptyLine:s}}class O{constructor(e={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(e,t,n,s)=>{let r=S(e);s?this.warnings.push(new o.so(r,t,n)):this.errors.push(new o.XT(r,t,n))},this.directives=new s.D({version:e.version||"1.2"}),this.options=e}decorate(e,t){let{comment:n,afterEmptyLine:s}=T(this.prelude);if(n){let r=e.contents;if(t)e.comment=e.comment?`${e.comment}
${n}`:n;else if(s||e.directives.docStart||!r)e.commentBefore=n;else if((0,i.P3)(r)&&!r.flow&&r.items.length>0){let e=r.items[0];(0,i.tO)(e)&&(e=e.key);let t=e.commentBefore;e.commentBefore=t?`${n}
${t}`:n}else{let e=r.commentBefore;r.commentBefore=e?`${n}
${e}`:n}}t?(Array.prototype.push.apply(e.errors,this.errors),Array.prototype.push.apply(e.warnings,this.warnings)):(e.errors=this.errors,e.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:T(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(e,t=!1,n=-1){for(let t of e)yield*this.next(t);yield*this.end(t,n)}*next(e){switch(e.type){case"directive":this.directives.add(e.source,(t,n,s)=>{let r=S(e);r[0]+=t,this.onError(r,"BAD_DIRECTIVE",n,s)}),this.prelude.push(e.source),this.atDirectives=!0;break;case"document":{let t=function(e,t,{offset:n,start:s,value:o,end:i},a){let c=Object.assign({_directives:t},e),l=new r.y(void 0,c),u={atKey:!1,atRoot:!0,directives:l.directives,options:l.options,schema:l.schema},h=f(s,{indicator:"doc-start",next:o??i?.[0],offset:n,onError:a,parentIndent:0,startOnNewline:!0});h.found&&(l.directives.docStart=!0,o&&("block-map"===o.type||"block-seq"===o.type)&&!h.hasNewline&&a(h.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),l.contents=o?v(u,o,h,a):_(u,h.end,s,null,h,a);let m=l.contents.range[2],d=(0,y.U)(i,m,!1,a);return d.comment&&(l.comment=d.comment),l.range=[n,m,d.offset],l}(this.options,this.directives,e,this.onError);this.atDirectives&&!t.directives.docStart&&this.onError(e,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(t,!1),this.doc&&(yield this.doc),this.doc=t,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(e.source);break;case"error":{let t=e.source?`${e.message}: ${JSON.stringify(e.source)}`:e.message,n=new o.XT(S(e),"UNEXPECTED_TOKEN",t);this.atDirectives||!this.doc?this.errors.push(n):this.doc.errors.push(n);break}case"doc-end":{if(!this.doc){this.errors.push(new o.XT(S(e),"UNEXPECTED_TOKEN","Unexpected doc-end without preceding document"));break}this.doc.directives.docEnd=!0;let t=(0,y.U)(e.end,e.offset+e.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),t.comment){let e=this.doc.comment;this.doc.comment=e?`${e}
${t.comment}`:t.comment}this.doc.range[2]=t.offset;break}default:this.errors.push(new o.XT(S(e),"UNEXPECTED_TOKEN",`Unsupported token ${e.type}`))}}*end(e=!1,t=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(e){let e=Object.assign({_directives:this.directives},this.options),n=new r.y(void 0,e);this.atDirectives&&this.onError(t,"MISSING_CHAR","Missing directives-end indicator line"),n.range=[0,t,t],this.decorate(n,!1),yield n}}}}}]);