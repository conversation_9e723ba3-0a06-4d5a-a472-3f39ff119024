"use strict";(()=>{var e={};e.id=9719,e.ids=[9719],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},74991:(e,t,r)=>{r.r(t),r.d(t,{config:()=>g,default:()=>x,routeModule:()=>h});var a={};r.r(a),r.d(a,{default:()=>w});var n=r(93433),s=r(20264),o=r(20584),u=r(15806),i=r(94506),d=r(12518),l=r(98580),c=r(76732);let{url:p,name:b}=l.dashboardConfig.database,f=null;async function m(){if(f)return f;let e=await d.MongoClient.connect(p);return f=e,e}async function w(e,t){if(!await (0,u.getServerSession)(e,t,i.authOptions))return t.status(401).json({error:"Unauthorized"});let{collection:r,id:a}=e.query;if(!r)return t.status(400).json({error:"Collection name is required"});try{let n=(await m()).db(b).collection(r);switch(e.method){case"GET":let s=await n.find({}).toArray();return await (0,c.logDatabaseOperation)("query",r,{count:s.length}),t.status(200).json({documents:s});case"POST":let o=await n.insertOne(e.body);return await (0,c.logDatabaseOperation)("insert",r,{document:e.body}),t.status(201).json({result:o});case"PUT":if(!a)return t.status(400).json({error:"Document ID is required"});let u=await n.updateOne({_id:new d.ObjectId(a)},{$set:e.body});return await (0,c.logDatabaseOperation)("update",r,{id:a,document:e.body}),t.status(200).json({result:u});case"DELETE":if(!a)return t.status(400).json({error:"Document ID is required"});let i=await n.deleteOne({_id:new d.ObjectId(a)});return await (0,c.logDatabaseOperation)("delete",r,{id:a}),t.status(200).json({result:i});default:return t.status(405).json({error:"Method not allowed"})}}catch(e){return t.status(500).json({error:"Internal server error"})}}let x=(0,o.M)(a,"default"),g=(0,o.M)(a,"config"),h=new n.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/database/documents",pathname:"/api/database/documents",bundlePath:"",filename:""},userland:a})},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(74991));module.exports=a})();