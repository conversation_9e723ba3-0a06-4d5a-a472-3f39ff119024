"use strict";(()=>{var e={};e.id=3571,e.ids=[3571],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},51884:(e,o,n)=>{n.r(o),n.d(o,{config:()=>x,default:()=>b,routeModule:()=>w});var t={};n.r(t),n.d(t,{default:()=>g});var r=n(93433),a=n(20264),s=n(20584),d=n(15806),i=n(94506),c=n(12518),m=n(29021),l=n.n(m),u=n(33873),p=n.n(u);let f={url:process.env.DATABASE_URL||"mongodb://localhost:27017",name:process.env.DATABASE_NAME||"discord-bot"};async function g(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});let n=await (0,d.getServerSession)(e,o,i.authOptions);if(!n)return o.status(401).json({error:"Unauthorized"});try{let t=await c.MongoClient.connect(f.url),r=t.db(f.name),a={userId:n.user.id},[s,d]=await Promise.all([r.collection("experimental_testers").findOne(a),r.collection("experimental_flags").findOne(a)]);if(!(s||d||n.user.isAdmin))return await t.close(),o.status(403).json({error:"Experimental features access required"});let i=e.body,m=function(e){let o=[];return(!e.name||e.name.length<2)&&o.push("Addon name must be at least 2 characters long"),/^[a-z0-9-]+$/.test(e.name)||o.push("Addon name must contain only lowercase letters, numbers, and hyphens"),e.version&&/^\d+\.\d+\.\d+$/.test(e.version)||o.push("Version must be in semver format (e.g., 1.0.0)"),(!e.description||e.description.length<10)&&o.push("Description must be at least 10 characters long"),(!e.author||e.author.length<2)&&o.push("Author name must be at least 2 characters long"),e.settings?.embedColor&&/^#[0-9a-fA-F]{6}$/.test(e.settings.embedColor)||o.push("Embed color must be a valid hex color (e.g., #0099FF)"),{isValid:0===o.length,errors:o}}(i);if(!m.isValid)return await t.close(),o.status(400).json({error:"Invalid addon configuration",details:m.errors});let u=function(e){var o,n;let t={};return t["index.ts"]=(o=0,`import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import YAML from 'yaml';
import type { Addon, BotInstance } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

async function loadCommands(): Promise<any[]> {
  const commands: any[] = [];
  const commandsPath = path.join(__dirname, 'commands');
  
  if (!fs.existsSync(commandsPath)) {
    return commands;
  }

  const commandFiles = fs.readdirSync(commandsPath).filter(file => 
    file.endsWith('.js') || file.endsWith('.ts')
  );

  for (const file of commandFiles) {
    try {
      const commandPath = path.join(commandsPath, file);
      const commandUrl = pathToFileURL(commandPath).href;
      const commandModule = await import(commandUrl);
      
      if (commandModule.data && commandModule.execute) {
        commands.push({
          data: commandModule.data,
          execute: commandModule.execute,
          cooldown: commandModule.cooldown || 3000
        });
      }
    } catch (error) {
      console.error(\`Failed to load command \${file}:\`, error);
    }
  }

  return commands;
}

const addon: Addon = {
  info: {
    name: config.addon.name,
    version: config.addon.version,
    description: config.addon.description,
    author: config.addon.author
  },

  commands: await loadCommands(),

  events: [
    {
      name: 'ready',
      once: true,
      execute: async (client) => {
        const logger = Logger.createAddonLogger(config.addon.name);
        logger.info(\`\${config.addon.name} addon loaded! Bot ready as \${client.user?.tag}\`);
      }
    }
  ],

  onLoad: async (bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(\`Loading \${config.addon.name} addon...\`);
  },

  onUnload: async (_bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(\`Unloading \${config.addon.name} addon...\`);
  }
};

export default addon;`),t["config.yml"]=function(e){let o={addon:{name:e.name,version:e.version,description:e.description,author:e.author,enabled:!0},commands:{},settings:{embedColor:parseInt(e.settings.embedColor.slice(1),16)}};return e.commands.forEach(e=>{o.commands[e.name]={enabled:e.enabled,cooldown:e.cooldown||3e3,description:e.description}}),`addon:
  name: "${e.name}"
  version: "${e.version}"
  description: "${e.description}"
  author: "${e.author}"
  enabled: true

settings:
  embedColor: 0x${e.settings.embedColor.slice(1)}

logging:
  enabled: true
  logCommands: true
  logLevel: "info"`}(e),t["README.md"]=(n=e,`# ${n.name}

${n.description}

**Author:** ${n.author}  
**Version:** ${n.version}

## Commands

${n.commands.map(e=>`- **/${e.name}** - ${e.description}`).join("\n")||"No commands available."}

## Settings

- **Embed Color:** ${n.settings.embedColor}

---

*This addon was generated using the 404 Bot Addon Builder.*`),e.commands.forEach(e=>{var o;t[`commands/${e.name.toLowerCase()}.ts`]=(o=e,`import { SlashCommandBuilder, EmbedBuilder, ChatInputCommandInteraction } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('${o.name}')
  .setDescription('${o.description}');

export async function execute(interaction: ChatInputCommandInteraction, bot: BotInstance) {
  try {
    ${o.code}
  } catch (error) {
    console.error('Error executing ${o.name} command:', error);
    
    const errorMessage = 'There was an error executing this command!';
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({ content: errorMessage, ephemeral: true });
    }
  }
}

export const cooldown = ${o.cooldown||3e3};`)}),t}(i),g=await h(i.name);await y(g,u),await r.collection("addon_builder_logs").insertOne({userId:n.user.id,userEmail:n.user.email,addonName:i.name,action:"created",timestamp:new Date,fileCount:Object.keys(u).length});let b=process.cwd().includes(p().join("src","dashboard"))?p().resolve(process.cwd(),"..",".."):process.cwd(),x=p().join(b,"addon-reload.signal");l().writeFileSync(x,JSON.stringify({requestedBy:n.user.email||"addon-builder",timestamp:Date.now(),action:"addon-created",addonName:i.name})),await t.close(),o.status(201).json({message:"Addon created successfully",addonName:i.name,files:Object.keys(u),path:g})}catch(e){o.status(500).json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"})}}async function h(e){let o=process.cwd().includes(p().join("src","dashboard"))?p().resolve(process.cwd(),"..",".."):process.cwd(),n=p().join(o,"src","addons"),t=p().join(n,e);if(l().existsSync(t))throw Error(`Addon '${e}' already exists`);return l().mkdirSync(t,{recursive:!0}),l().mkdirSync(p().join(t,"commands"),{recursive:!0}),t}async function y(e,o){for(let[n,t]of Object.entries(o)){let o=p().join(e,n),r=p().dirname(o);l().existsSync(r)||l().mkdirSync(r,{recursive:!0}),l().writeFileSync(o,t,"utf8")}}let b=(0,s.M)(t,"default"),x=(0,s.M)(t,"config"),w=new r.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/experimental/addon-builder/create",pathname:"/api/experimental/addon-builder/create",bundlePath:"",filename:""},userland:t})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var o=require("../../../../webpack-api-runtime.js");o.C(e);var n=e=>o(o.s=e),t=o.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>n(51884));module.exports=t})();