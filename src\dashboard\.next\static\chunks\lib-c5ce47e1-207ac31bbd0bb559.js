"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9176],{2162:(t,e,i)=>{i.d(e,{P:()=>tg});var o=i(24590),s=i(72899),r=i(65660),n=i(61394),a=i(43912),l=i(83346),h=i(53746);let u=["TopLeft","TopRight","BottomLeft","BottomRight"],d=u.length,c=t=>"string"==typeof t?parseFloat(t):t,p=t=>"number"==typeof t||o.px.test(t);function m(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let y=g(0,.5,s.yT),f=g(.5,.95,s.lQ);function g(t,e,i){return o=>o<t?0:o>e?1:i((0,s.qB)(t,e,o))}function v(t,e){t.min=e.min,t.max=e.max}function x(t,e){v(t.x,e.x),v(t.y,e.y)}function T(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}var P=i(34251),S=i(97032);function D(t,e,i,o,s){return t-=e,t=(0,P.hq)(t,1/i,o),void 0!==s&&(t=(0,P.hq)(t,1/s,o)),t}function w(t,e,[i,s,r],n,a){!function(t,e=0,i=1,s=.5,r,n=t,a=t){if(o.rq.test(e)&&(e=parseFloat(e),e=(0,o.k$)(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let l=(0,o.k$)(n.min,n.max,s);t===n&&(l-=e),t.min=D(t.min,e,i,l,r),t.max=D(t.max,e,i,l,r)}(t,e[i],e[s],e[r],e.scale,n,a)}let j=["x","scaleX","originX"],R=["y","scaleY","originY"];function A(t,e,i,o){w(t.x,e,j,i?i.x:void 0,o?o.x:void 0),w(t.y,e,R,i?i.y:void 0,o?o.y:void 0)}var k=i(27518);function C(t){return 0===t.translate&&1===t.scale}function b(t){return C(t.x)&&C(t.y)}function L(t,e){return t.min===e.min&&t.max===e.max}function B(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function E(t,e){return B(t.x,e.x)&&B(t.y,e.y)}function V(t){return(0,S.CQ)(t.x)/(0,S.CQ)(t.y)}function U(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class F{constructor(){this.members=[]}add(t){(0,s.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,s.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:o}=t.options;!1===o&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var M=i(31729),$=i(37170),O=i(17202),I=i(98217);let H={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},Q=["","X","Y","Z"],N=0;function W(t,e,i,o){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),o&&(o[t]=0))}function X({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:g,resetTransform:v}){return class{constructor(t={},i=e?.()){this.id=N++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,o.Qu.value&&(H.nodes=H.calculatedTargetDeltas=H.calculatedProjections=0),this.nodes.forEach(G),this.nodes.forEach(te),this.nodes.forEach(ti),this.nodes.forEach(q),o.Qu.addProjectionMetrics&&o.Qu.addProjectionMetrics(H)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new a.Y)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new s.vY),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=(0,o.xZ)(e)&&!(0,o.h1)(e),this.instance=e;let{layoutId:i,layout:s,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=0,r=()=>this.root.updateBlockedByResize=!1;o.Gt.read(()=>{s=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==s&&(s=t,this.root.updateBlockedByResize=!0,i&&i(),i=(0,l.c)(r,250),I.w.hasAnimatedSinceResize&&(I.w.hasAnimatedSinceResize=!1,this.nodes.forEach(tt)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||tl,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=r.getProps(),h=!this.targetLayout||!E(this.targetLayout,s),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...(0,o.rU)(n,"layout"),onPlay:a,onComplete:l};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||tt(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,o.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(to),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=(0,n.P)(i);if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",o.Gt,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Z);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(J);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(K),this.nodes.forEach(Y),this.nodes.forEach(z)):this.nodes.forEach(J),this.clearAllSnapshots();let t=o.kB.now();o.uv.delta=(0,s.qE)(0,1e3/60,t-o.uv.timestamp),o.uv.timestamp=t,o.uv.isProcessing=!0,o.PP.update.process(o.uv),o.PP.preRender.process(o.uv),o.PP.render.process(o.uv),o.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,o.k2.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(_),this.sharedNodes.forEach(ts)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,o.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){o.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||(0,S.CQ)(this.snapshot.measuredBox.x)||(0,S.CQ)(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,k.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=g(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!v)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!b(this.projectionDelta),i=this.getTransformTemplate(),o=i?i(this.latestValues,""):void 0,s=o!==this.prevTransformTemplateValue;t&&this.instance&&(e||(0,O.HD)(this.latestValues)||s)&&(v(this.instance,o),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),o=this.removeElementScroll(i);return t&&(o=this.removeTransform(o)),td((e=o).x),td(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:o,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return(0,k.ge)();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(tp))){let{scroll:t}=this.root;t&&((0,P.Ql)(e.x,t.offset.x),(0,P.Ql)(e.y,t.offset.y))}return e}removeElementScroll(t){let e=(0,k.ge)();if(x(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let o=this.path[i],{scroll:s,options:r}=o;o!==this.root&&s&&r.layoutScroll&&(s.wasRoot&&x(e,t),(0,P.Ql)(e.x,s.offset.x),(0,P.Ql)(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=(0,k.ge)();x(i,t);for(let t=0;t<this.path.length;t++){let o=this.path[t];!e&&o.options.layoutScroll&&o.scroll&&o!==o.root&&(0,P.Ww)(i,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),(0,O.HD)(o.latestValues)&&(0,P.Ww)(i,o.latestValues)}return(0,O.HD)(this.latestValues)&&(0,P.Ww)(i,this.latestValues),i}removeTransform(t){let e=(0,k.ge)();x(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!(0,O.HD)(i.latestValues))continue;(0,O.vk)(i.latestValues)&&i.updateSnapshot();let o=(0,k.ge)();x(o,i.measurePageBox()),A(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,o)}return(0,O.HD)(this.latestValues)&&A(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==o.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:r}=this.options;if(this.layout&&(s||r)){if(this.resolvedRelativeTargetAt=o.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,k.ge)(),this.relativeTargetOrigin=(0,k.ge)(),(0,S.jA)(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),x(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,k.ge)(),this.targetWithTransforms=(0,k.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),(0,S.N)(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):x(this.target,this.layout.layoutBox),(0,P.o4)(this.target,this.targetDelta)):x(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,k.ge)(),this.relativeTargetOrigin=(0,k.ge)(),(0,S.jA)(this.relativeTargetOrigin,this.target,t.target),x(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}o.Qu.value&&H.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||(0,O.vk)(this.parent.latestValues)||(0,O.vF)(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===o.uv.timestamp&&(i=!1),i)return;let{layout:s,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||r))return;x(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,a=this.treeScale.y;(0,P.OU)(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=(0,k.ge)());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(T(this.prevProjectionDelta.x,this.projectionDelta.x),T(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),(0,S.vb)(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===n&&this.treeScale.y===a&&U(this.projectionDelta.x,this.prevProjectionDelta.x)&&U(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),o.Qu.value&&H.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,k.xU)(),this.projectionDelta=(0,k.xU)(),this.projectionDeltaWithTransform=(0,k.xU)()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,r=s?s.latestValues:{},n={...this.latestValues},a=(0,k.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let l=(0,k.ge)(),h=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),g=this.getStack(),v=!g||g.members.length<=1,T=!!(h&&!v&&!0===this.options.crossfade&&!this.path.some(ta));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(tr(a.x,t.x,s),tr(a.y,t.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var g,P,D,w,j,R;(0,S.jA)(l,this.layout.layoutBox,this.relativeParent.layout.layoutBox),D=this.relativeTarget,w=this.relativeTargetOrigin,j=l,R=s,tn(D.x,w.x,j.x,R),tn(D.y,w.y,j.y,R),i&&(g=this.relativeTarget,P=i,L(g.x,P.x)&&L(g.y,P.y))&&(this.isProjectionDirty=!1),i||(i=(0,k.ge)()),x(i,this.relativeTarget)}h&&(this.animationValues=n,function(t,e,i,s,r,n){r?(t.opacity=(0,o.k$)(0,i.opacity??1,y(s)),t.opacityExit=(0,o.k$)(e.opacity??1,0,f(s))):n&&(t.opacity=(0,o.k$)(e.opacity??1,i.opacity??1,s));for(let r=0;r<d;r++){let n=`border${u[r]}Radius`,a=m(e,n),l=m(i,n);(void 0!==a||void 0!==l)&&(a||(a=0),l||(l=0),0===a||0===l||p(a)===p(l)?(t[n]=Math.max((0,o.k$)(c(a),c(l),s),0),(o.rq.test(l)||o.rq.test(a))&&(t[n]+="%")):t[n]=l)}(e.rotate||i.rotate)&&(t.rotate=(0,o.k$)(e.rotate||0,i.rotate||0,s))}(n,r,this.latestValues,s,T,v)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,o.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=o.Gt.update(()=>{I.w.hasAnimatedSinceResize=!0,o.qU.layout++,this.motionValue||(this.motionValue=(0,o.OQ)(0)),this.currentAnimation=(0,r.z)(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{o.qU.layout--},onComplete:()=>{o.qU.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:o,latestValues:s}=t;if(e&&i&&o){if(this!==t&&this.layout&&o&&tc(this.options.animationType,this.layout.layoutBox,o.layoutBox)){i=this.target||(0,k.ge)();let e=(0,S.CQ)(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let o=(0,S.CQ)(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+o}x(e,i),(0,P.Ww)(e,s),(0,S.vb)(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new F),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let o=this.getStack();o&&o.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let o={};i.z&&W("z",t,o,this.animationValues);for(let e=0;e<Q.length;e++)W(`rotate${Q[e]}`,t,o,this.animationValues),W(`skew${Q[e]}`,t,o,this.animationValues);for(let e in t.render(),o)t.setStaticValue(e,o[e]),this.animationValues&&(this.animationValues[e]=o[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=(0,h.u)(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=(0,h.u)(e?.pointerEvents)||""),this.hasProjected&&!(0,O.HD)(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let s=o.animationValues||o.latestValues;this.applyTransformsToTarget();let r=function(t,e,i){let o="",s=t.x.translate/e.x,r=t.y.translate/e.y,n=i?.z||0;if((s||r||n)&&(o=`translate3d(${s}px, ${r}px, ${n}px) `),(1!==e.x||1!==e.y)&&(o+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:r,skewX:n,skewY:a}=i;t&&(o=`perspective(${t}px) ${o}`),e&&(o+=`rotate(${e}deg) `),s&&(o+=`rotateX(${s}deg) `),r&&(o+=`rotateY(${r}deg) `),n&&(o+=`skewX(${n}deg) `),a&&(o+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(o+=`scale(${a}, ${l})`),o||"none"}(this.projectionDeltaWithTransform,this.treeScale,s);i&&(r=i(s,r)),t.transform=r;let{x:n,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*n.origin}% ${100*a.origin}% 0`,o.animationValues?t.opacity=o===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:t.opacity=o===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,M.H){if(void 0===s[e])continue;let{correct:i,applyTo:n,isCSSVariable:a}=M.H[e],l="none"===r?s[e]:i(s[e],o);if(n){let e=n.length;for(let i=0;i<e;i++)t[n[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=o===this?(0,h.u)(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(Z),this.root.sharedNodes.clear()}}}function Y(t){t.updateLayout()}function z(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:o}=t.layout,{animationType:s}=t.options,r=e.source!==t.layout.source;"size"===s?(0,$.X)(t=>{let o=r?e.measuredBox[t]:e.layoutBox[t],s=(0,S.CQ)(o);o.min=i[t].min,o.max=o.min+s}):tc(s,e.layoutBox,i)&&(0,$.X)(o=>{let s=r?e.measuredBox[o]:e.layoutBox[o],n=(0,S.CQ)(i[o]);s.max=s.min+n,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[o].max=t.relativeTarget[o].min+n)});let n=(0,k.xU)();(0,S.vb)(n,i,e.layoutBox);let a=(0,k.xU)();r?(0,S.vb)(a,t.applyTransform(o,!0),e.measuredBox):(0,S.vb)(a,i,e.layoutBox);let l=!b(n),h=!1;if(!t.resumeFrom){let o=t.getClosestProjectingParent();if(o&&!o.resumeFrom){let{snapshot:s,layout:r}=o;if(s&&r){let n=(0,k.ge)();(0,S.jA)(n,e.layoutBox,s.layoutBox);let a=(0,k.ge)();(0,S.jA)(a,i,r.layoutBox),E(n,a)||(h=!0),o.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=n,t.relativeParent=o)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:n,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function G(t){o.Qu.value&&H.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function q(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function _(t){t.clearSnapshot()}function Z(t){t.clearMeasurements()}function J(t){t.isLayoutDirty=!1}function K(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function tt(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function te(t){t.resolveTargetDelta()}function ti(t){t.calcProjection()}function to(t){t.resetSkewAndRotation()}function ts(t){t.removeLeadSnapshot()}function tr(t,e,i){t.translate=(0,o.k$)(e.translate,0,i),t.scale=(0,o.k$)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function tn(t,e,i,s){t.min=(0,o.k$)(e.min,i.min,s),t.max=(0,o.k$)(e.max,i.max,s)}function ta(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let tl={duration:.45,ease:[.4,0,.1,1]},th=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),tu=th("applewebkit/")&&!th("chrome/")?Math.round:s.lQ;function td(t){t.min=tu(t.min),t.max=tu(t.max)}function tc(t,e,i){return"position"===t||"preserve-aspect"===t&&!(0,S.HQ)(V(e),V(i),.2)}function tp(t){return t!==t.root&&t.scroll?.wasRoot}var tm=i(42326);let ty=X({attachResizeListener:(t,e)=>(0,tm.k)(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),tf={current:void 0},tg=X({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!tf.current){let t=new ty({});t.mount(window),t.setOptions({layoutScroll:!0}),tf.current=t}return tf.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position})},5649:(t,e,i)=>{i.d(e,{Nyo:()=>o.N,PY1:()=>s.P,tFS:()=>r.tF,xQ_:()=>r.xQ});var o=i(62595);i(27158),i(67139),i(47579),i(14062);var s=i(25643);i(2605),i(21110),i(31176),i(94888),i(97032),i(27518),i(5048),i(90332),i(36294),i(20090),i(10339),i(88917),i(83989),i(51023),i(34864),i(6689),i(57723),i(13392),i(57747),i(54890),i(17358),i(24418),i(93014),i(80364),i(41917),i(39628),i(53746),i(14532),i(44573),i(72899),i(92194),i(87660),i(41812),i(39143),i(38367);var r=i(15518);i(27649),i(84902),i(43054),i(18319),i(15894),i(95050),i(31729),i(34415),i(94285),i(2162),i(72933),i(60279),i(27816),i(35279),i(66967),i(19992),i(47720),i(15302),i(96860),i(7470),i(54265),i(40892),i(64975),i(74257),i(70177),i(43912),i(6578),i(41736),i(55906),i(34087),i(83346),i(52077),i(95537),i(69399),i(3363),i(66472),i(1674),i(15550),i(24590)},12387:(t,e,i)=>{},15894:(t,e,i)=>{i.d(e,{o:()=>o});let o=Symbol.for("motionComponentSymbol")},17202:(t,e,i)=>{function o(t){return void 0===t||1===t}function s({scale:t,scaleX:e,scaleY:i}){return!o(t)||!o(e)||!o(i)}function r(t){return s(t)||n(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function n(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}i.d(e,{HD:()=>r,vF:()=>n,vk:()=>s})},18319:(t,e,i)=>{i.d(e,{Z:()=>T});var o=i(94513);i(72899);var s=i(94285),r=i(54265),n=i(60066),a=i(40892),l=i(64975),h=i(22588),u=i(90332),d=i(27062),c=i(46016),p=i(15894),m=i(42903),y=i(24590),f=i(96860),g=i(74257),v=i(70177),x=i(20090);function T({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:T,Component:P}){function S(t,c){var p,S,D;let w,j={...(0,s.useContext)(a.Q),...t,layoutId:function({layoutId:t}){let e=(0,s.useContext)(r.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:R}=j,A=(0,h.z)(t),k=T(t,R);if(!R&&u.B){S=0,D=0,(0,s.useContext)(n.Y).strict;let t=function(t){let{drag:e,layout:i}=d.B;if(!e&&!i)return{};let o={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?o.MeasureLayout:void 0,ProjectionNode:o.ProjectionNode}}(j);w=t.MeasureLayout,A.visualElement=function(t,e,i,o,r){let{visualElement:h}=(0,s.useContext)(l.A),u=(0,s.useContext)(n.Y),d=(0,s.useContext)(g.t),c=(0,s.useContext)(a.Q).reducedMotion,p=(0,s.useRef)(null);o=o||u.renderer,!p.current&&o&&(p.current=o(t,{visualState:e,parent:h,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:c}));let T=p.current,P=(0,s.useContext)(v.N);T&&!T.projection&&r&&("html"===T.type||"svg"===T.type)&&function(t,e,i,o){let{layoutId:s,layout:r,drag:n,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!n||a&&(0,m.X)(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:o,crossfade:u,layoutScroll:l,layoutRoot:h})}(p.current,i,r,P);let S=(0,s.useRef)(!1);(0,s.useInsertionEffect)(()=>{T&&S.current&&T.update(i,d)});let D=i[f.n],w=(0,s.useRef)(!!D&&!window.MotionHandoffIsComplete?.(D)&&window.MotionHasOptimisedAnimation?.(D));return(0,x.E)(()=>{T&&(S.current=!0,window.MotionIsMounted=!0,T.updateFeatures(),y.k2.render(T.render),w.current&&T.animationState&&T.animationState.animateChanges())}),(0,s.useEffect)(()=>{T&&(!w.current&&T.animationState&&T.animationState.animateChanges(),w.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(D)}),w.current=!1))}),T}(P,k,j,e,t.ProjectionNode)}return(0,o.jsxs)(l.A.Provider,{value:A,children:[w&&A.visualElement?(0,o.jsx)(w,{visualElement:A.visualElement,...j}):null,i(P,t,(p=A.visualElement,(0,s.useCallback)(t=>{t&&k.onMount&&k.onMount(t),p&&(t?p.mount(t):p.unmount()),c&&("function"==typeof c?c(t):(0,m.X)(c)&&(c.current=t))},[p])),k,R,A.visualElement)]})}t&&(0,c.Y)(t),S.displayName=`motion.${"string"==typeof P?P:`create(${P.displayName??P.name??""})`}`;let D=(0,s.forwardRef)(S);return D[p.o]=P,D}},22081:(t,e,i)=>{i.d(e,{L:()=>n,m:()=>r});var o=i(58308),s=i(34251);function r(t,e){return(0,o.FY)((0,o.bS)(t.getBoundingClientRect(),e))}function n(t,e,i){let o=r(t,i),{scroll:n}=e;return n&&((0,s.Ql)(o.x,n.offset.x),(0,s.Ql)(o.y,n.offset.y)),o}},23808:(t,e,i)=>{i.d(e,{n:()=>p});var o=i(42994),s=i(66376),r=i(14069),n=i(37766);let a=new WeakMap,l=new WeakMap,h=t=>{let e=a.get(t.target);e&&e(t)},u=t=>{t.forEach(h)},d={some:0,all:1};class c extends n.X{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:o="some",once:s}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof o?o:d[o]};return function(t,e,i){let o=function({root:t,...e}){let i=t||document;l.has(i)||l.set(i,{});let o=l.get(i),s=JSON.stringify(e);return o[s]||(o[s]=new IntersectionObserver(u,{root:t,...e})),o[s]}(e);return a.set(t,i),o.observe(t),()=>{a.delete(t),o.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:o}=this.node.getProps(),r=e?i:o;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let p={inView:{Feature:c},tap:{Feature:r.H},focus:{Feature:s.c},hover:{Feature:o.e}}},27062:(t,e,i)=>{i.d(e,{B:()=>s});let o={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},s={};for(let t in o)s[t]={isEnabled:e=>o[t].some(t=>!!e[t])}},27518:(t,e,i)=>{i.d(e,{ge:()=>n,xU:()=>s});let o=()=>({translate:0,scale:1,origin:0,originPoint:0}),s=()=>({x:o(),y:o()}),r=()=>({min:0,max:0}),n=()=>({x:r(),y:r()})},31176:(t,e,i)=>{i.d(e,{W:()=>h});var o=i(98140),s=i(13481),r=i(37766);class n extends r.X{constructor(t){super(t),t.animationState||(t.animationState=(0,s.L)(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();(0,o.N)(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let a=0;class l extends r.X{constructor(){super(...arguments),this.id=a++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let o=this.node.animationState.setActive("exit",!t);e&&!t&&o.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let h={animation:{Feature:n},exit:{Feature:l}}},31729:(t,e,i)=>{i.d(e,{$:()=>r,H:()=>s});var o=i(24590);let s={};function r(t){for(let e in t)s[e]=t[e],(0,o.j4)(e)&&(s[e].isCSSVariable=!0)}},34251:(t,e,i)=>{i.d(e,{OU:()=>h,Ql:()=>u,Ww:()=>c,hq:()=>r,o4:()=>l});var o=i(24590),s=i(17202);function r(t,e,i){return i+e*(t-i)}function n(t,e,i,o,s){return void 0!==s&&(t=o+s*(t-o)),o+i*(t-o)+e}function a(t,e=0,i=1,o,s){t.min=n(t.min,e,i,o,s),t.max=n(t.max,e,i,o,s)}function l(t,{x:e,y:i}){a(t.x,e.translate,e.scale,e.originPoint),a(t.y,i.translate,i.scale,i.originPoint)}function h(t,e,i,o=!1){let r,n,a=i.length;if(a){e.x=e.y=1;for(let h=0;h<a;h++){n=(r=i[h]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(o&&r.options.layoutScroll&&r.scroll&&r!==r.root&&c(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(e.x*=n.x.scale,e.y*=n.y.scale,l(t,n)),o&&(0,s.HD)(r.latestValues)&&c(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}function u(t,e){t.min=t.min+e,t.max=t.max+e}function d(t,e,i,s,r=.5){let n=(0,o.k$)(t.min,t.max,r);a(t,e,i,n,s)}function c(t,e){d(t.x,e.x,e.scaleX,e.scale,e.originX),d(t.y,e.y,e.scaleY,e.scale,e.originY)}},34415:(t,e,i)=>{i(2162)},37170:(t,e,i)=>{i.d(e,{X:()=>o});function o(t){return[t("x"),t("y")]}},37766:(t,e,i)=>{i.d(e,{X:()=>o});class o{constructor(t){this.isMounted=!1,this.node=t}update(){}}},46016:(t,e,i)=>{i.d(e,{Y:()=>s});var o=i(27062);function s(t){for(let e in t)o.B[e]={...o.B[e],...t[e]}}},54519:(t,e,i)=>{i.d(e,{z:()=>r});var o=i(24590),s=i(31729);function r(t,{layout:e,layoutId:i}){return o.fu.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!s.H[t]||"opacity"===t)}},58308:(t,e,i)=>{function o({top:t,left:e,right:i,bottom:o}){return{x:{min:e,max:i},y:{min:t,max:o}}}function s({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function r(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),o=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:o.y,right:o.x}}i.d(e,{FY:()=>o,bS:()=>r,pA:()=>s})},66604:(t,e,i)=>{i.d(e,{Z:()=>r});var o=i(2162),s=i(71627);let r={layout:{ProjectionNode:o.P,MeasureLayout:s.$}}},66868:(t,e,i)=>{i.d(e,{$:()=>a});var o=i(66840),s=i(61456),r=i(71627),n=i(2162);let a={pan:{Feature:s.f},drag:{Feature:o.w,ProjectionNode:n.P,MeasureLayout:r.$}}},71627:(t,e,i)=>{i.d(e,{$:()=>y});var o=i(94513),s=i(24590),r=i(94285),n=i(15518),a=i(54265),l=i(70177),h=i(98217);function u(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let d={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!s.px.test(t))return t;else t=parseFloat(t);let i=u(t,e.target.x),o=u(t,e.target.y);return`${i}% ${o}%`}};var c=i(31729);let p=!1;class m extends r.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:o}=this.props,{projection:s}=t;(0,c.$)(f),s&&(e.group&&e.group.add(s),i&&i.register&&o&&i.register(s),p&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),h.w.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:o,isPresent:r}=this.props,{projection:n}=i;return n&&(n.isPresent=r,p=!0,o||t.layoutDependency!==e||void 0===e||t.isPresent!==r?n.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?n.promote():n.relegate()||s.Gt.postRender(()=>{let t=n.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),s.k2.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(o),i&&i.deregister&&i.deregister(o))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function y(t){let[e,i]=(0,n.xQ)(),s=(0,r.useContext)(a.L);return(0,o.jsx)(m,{...t,layoutGroup:s,switchLayoutGroup:(0,r.useContext)(l.N),isPresent:e,safeToRemove:i})}let f={borderRadius:{...d,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:d,borderTopRightRadius:d,borderBottomLeftRadius:d,borderBottomRightRadius:d,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let o=s.f.parse(t);if(o.length>5)return t;let r=s.f.createTransformer(t),n=+("number"!=typeof o[0]),a=i.x.scale*e.x,l=i.y.scale*e.y;o[0+n]/=a,o[1+n]/=l;let h=(0,s.k$)(a,l,.5);return"number"==typeof o[2+n]&&(o[2+n]/=h),"number"==typeof o[3+n]&&(o[3+n]/=h),r(o)}}}},94888:(t,e,i)=>{i.d(e,{T:()=>d});var o=i(94285),s=i(98140),r=i(64975),n=i(74257),a=i(64473),l=i(27655),h=i(68969),u=i(53746);let d=t=>(e,i)=>{let d=(0,o.useContext)(r.A),c=(0,o.useContext)(n.t),p=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,o,r){return{latestValues:function(t,e,i,o){let r={},n=o(t,{});for(let t in n)r[t]=(0,u.u)(n[t]);let{initial:h,animate:d}=t,c=(0,a.e)(t),p=(0,a.O)(t);e&&p&&!c&&!1!==t.inherit&&(void 0===h&&(h=e.initial),void 0===d&&(d=e.animate));let m=!!i&&!1===i.initial,y=(m=m||!1===h)?d:h;if(y&&"boolean"!=typeof y&&!(0,s.N)(y)){let e=Array.isArray(y)?y:[y];for(let i=0;i<e.length;i++){let o=(0,l.a)(t,e[i]);if(o){let{transitionEnd:t,transition:e,...i}=o;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=m?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,o,r,t),renderState:e()}})(t,e,d,c);return i?p():(0,h.M)(p)}},95050:(t,e,i)=>{i.d(e,{S:()=>s});let o=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function s(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||o.has(t)}},97032:(t,e,i)=>{i.d(e,{CQ:()=>s,HQ:()=>r,N:()=>h,jA:()=>d,vb:()=>a});var o=i(24590);function s(t){return t.max-t.min}function r(t,e,i){return Math.abs(t-e)<=i}function n(t,e,i,r=.5){t.origin=r,t.originPoint=(0,o.k$)(e.min,e.max,t.origin),t.scale=s(i)/s(e),t.translate=(0,o.k$)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function a(t,e,i,o){n(t.x,e.x,i.x,o?o.originX:void 0),n(t.y,e.y,i.y,o?o.originY:void 0)}function l(t,e,i){t.min=i.min+e.min,t.max=t.min+s(e)}function h(t,e,i){l(t.x,e.x,i.x),l(t.y,e.y,i.y)}function u(t,e,i){t.min=e.min-i.min,t.max=t.min+s(e)}function d(t,e,i){u(t.x,e.x,i.x),u(t.y,e.y,i.y)}},98217:(t,e,i)=>{i.d(e,{w:()=>o});let o={hasAnimatedSinceResize:!0,hasEverUpdated:!1}}}]);