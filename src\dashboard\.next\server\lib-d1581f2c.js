"use strict";exports.id=6835,exports.ids=[6835],exports.modules={6177:(e,t,r)=>{r.d(t,{a:()=>O});var n=r(82015),i=r(28158),a=r(79448),o=r(81265),l=r(79486),c=r(80020),u=["children","width","height","viewBox","className","style","title","desc"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var f=(0,n.forwardRef)((e,t)=>{var{children:r,width:i,height:a,viewBox:o,className:f,style:d,title:v,desc:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,u),h=o||{width:i,height:a,x:0,y:0},m=(0,l.$)("recharts-surface",f);return n.createElement("svg",s({},(0,c.J9)(y,!0,"svg"),{className:m,width:i,height:a,style:d,viewBox:"".concat(h.x," ").concat(h.y," ").concat(h.width," ").concat(h.height),ref:t}),n.createElement("title",null,v),n.createElement("desc",null,p),r)}),d=r(34797),v=r(99660),p=r(4236),y=["children"];function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var m={width:"100%",height:"100%"},b=(0,n.forwardRef)((e,t)=>{var r,o,l=(0,i.yi)(),c=(0,i.rY)(),u=(0,a.$)();if(!(0,p.F)(l)||!(0,p.F)(c))return null;var{children:s,otherAttributes:d,title:v,desc:y}=e;return r="number"==typeof d.tabIndex?d.tabIndex:u?0:void 0,o="string"==typeof d.role?d.role:u?"application":void 0,n.createElement(f,h({},d,{title:v,desc:y,role:o,tabIndex:r,width:l,height:c,style:m,ref:t}),s)}),g=e=>{var{children:t}=e,r=(0,d.G)(v.U);if(!r)return null;var{width:i,height:a,y:o,x:l}=r;return n.createElement(f,{width:i,height:a,x:l,y:o},t)},O=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y);return(0,o.r)()?n.createElement(g,null,r):n.createElement(b,h({ref:t},i),r)})},6776:(e,t,r)=>{r.d(t,{m:()=>et});var n=r(82015),i=r(22326),a=r(87206),o=r.n(a),l=r(79486),c=r(77331);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return Array.isArray(e)&&(0,c.vh)(e[0])&&(0,c.vh)(e[1])?e.join(" ~ "):e}var v=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:s,formatter:v,itemSorter:p,wrapperClassName:y,labelClassName:h,label:m,labelFormatter:b,accessibilityLayer:g=!1}=e,O=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),w=f({margin:0},a),j=!(0,c.uy)(m),x=j?m:"",P=(0,l.$)("recharts-default-tooltip",y),E=(0,l.$)("recharts-tooltip-label",h);return j&&b&&null!=s&&(x=b(m,s)),n.createElement("div",u({className:P,style:O},g?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:E,style:w},n.isValidElement(x)?x:"".concat(x)),(()=>{if(s&&s.length){var e=(p?o()(s,p):s).map((e,r)=>{if("none"===e.type)return null;var a=e.formatter||v||d,{value:o,name:l}=e,u=o,p=l;if(a){var y=a(o,l,e,r,s);if(Array.isArray(y))[u,p]=y;else{if(null==y)return null;u=y}}var h=f({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:h},(0,c.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,(0,c.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},u),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},p=r(38346);function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function m(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class b extends n.PureComponent{constructor(){super(...arguments),m(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),m(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:l,isAnimationActive:c,offset:u,position:s,reverseDirection:f,useTranslate3d:d,viewBox:v,wrapperStyle:y,lastBoundingBox:m,innerRef:b,hasPortalFromProps:g}=this.props,{cssClasses:O,cssProperties:w}=(0,p.eK)({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:u,position:s,reverseDirection:f,tooltipBox:{height:m.height,width:m.width},useTranslate3d:d,viewBox:v}),j=g?{}:h(h({transition:c&&e?"transform ".concat(r,"ms ").concat(i):void 0},w),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&l?"visible":"hidden",position:"absolute",top:0,left:0}),x=h(h({},j),{},{visibility:!this.state.dismissed&&e&&l?"visible":"hidden"},y);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:O,style:x,ref:b},a)}}var g=r(13141),O=r(82582),w=r(28158),j=r(79448),x=r(62358),P=r(82520),E=r(17737),A=r(2164),S=r(21162),k=r(78721),D=r(96157),N=r(19980),B=r(80020),C=r(34797),I=r(29655),R=r(77741);function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function L(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?T(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var W=()=>(0,C.G)(R.Dn),G=()=>{var e=W(),t=(0,C.G)(R.R4),r=(0,C.G)(R.fl);return(0,I.Hj)(L(L({},e),{},{scale:r}),t)},M=r(97900);function V(){return(V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function $(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function H(e){var t,r,{coordinate:i,payload:a,index:o,offset:c,tooltipAxisBandSize:u,layout:s,cursor:f,tooltipEventType:d,chartName:v}=e;if(!f||!i||"ScatterChart"!==v&&"axis"!==d)return null;if("ScatterChart"===v)t=i,r=E.F;else if("BarChart"===v)t=(0,A.C)(s,i,c,u),r=S.M;else if("radial"===s){var{cx:p,cy:y,radius:h,startAngle:m,endAngle:b}=(0,k.H)(i);t={cx:p,cy:y,startAngle:m,endAngle:b,innerRadius:h,outerRadius:h},r=D.h}else t={points:(0,N.K)(s,i,c)},r=P.I;var g="object"==typeof f&&"className"in f?f.className:void 0,O=F(F(F(F({stroke:"#ccc",pointerEvents:"none"},c),t),(0,B.J9)(f,!1)),{},{payload:a,payloadIndex:o,className:(0,l.$)("recharts-tooltip-cursor",g)});return(0,n.isValidElement)(f)?(0,n.cloneElement)(f,O):(0,n.createElement)(r,O)}function K(e){var t=G(),r=(0,w.W7)(),i=(0,w.WX)(),a=(0,M.fW)();return n.createElement(H,V({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:i,tooltipAxisBandSize:t,chartName:a}))}var J=r(36549),Z=r(95322),z=r(91055),U=r(82645),_=r(42127);function X(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?X(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):X(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Y(e){return e.dataKey}var Q=[],ee={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!g.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function et(e){var t,r=(0,_.e)(e,ee),{active:a,allowEscapeViewBox:o,animationDuration:l,animationEasing:c,content:u,filterNull:s,isAnimationActive:f,offset:d,payloadUniqBy:p,position:y,reverseDirection:h,useTranslate3d:m,wrapperStyle:g,cursor:P,shared:E,trigger:A,defaultIndex:S,portal:k,axisId:D}=r,N=(0,C.j)(),B="number"==typeof S?String(S):S;(0,n.useEffect)(()=>{N((0,Z.UF)({shared:E,trigger:A,axisId:D,active:a,defaultIndex:B}))},[N,E,A,D,a,B]);var I=(0,w.sk)(),R=(0,j.$)(),T=(0,U.Td)(E),{activeIndex:L,isActive:W}=(0,C.G)(e=>(0,M.yn)(e,T,A,B)),G=(0,C.G)(e=>(0,M.u9)(e,T,A,B)),V=(0,C.G)(e=>(0,M.BZ)(e,T,A,B)),$=(0,C.G)(e=>(0,M.dS)(e,T,A,B)),F=(0,J.X)(),H=null!=a?a:W,[X,et]=(0,x.V)([G,H]),er="axis"===T?V:void 0;(0,z.m7)(T,A,$,er,L,H);var en=null!=k?k:F;if(null==en)return null;var ei=null!=G?G:Q;H||(ei=Q),s&&ei.length&&(ei=(0,O.s)(G.filter(e=>null!=e.value&&(!0!==e.hide||r.includeHidden)),p,Y));var ea=ei.length>0,eo=n.createElement(b,{allowEscapeViewBox:o,animationDuration:l,animationEasing:c,isAnimationActive:f,active:H,coordinate:$,hasPayload:ea,offset:d,position:y,reverseDirection:h,useTranslate3d:m,viewBox:I,wrapperStyle:g,lastBoundingBox:X,innerRef:et,hasPortalFromProps:!!k},(t=q(q({},r),{},{payload:ei,label:er,active:H,coordinate:$,accessibilityLayer:R}),n.isValidElement(u)?n.cloneElement(u,t):"function"==typeof u?n.createElement(u,t):n.createElement(v,t)));return n.createElement(n.Fragment,null,(0,i.createPortal)(eo,en),H&&n.createElement(K,{cursor:P,tooltipEventType:T,coordinate:$,payload:G,index:L}))}},9843:(e,t,r)=>{r.d(t,{Cj:()=>a,Pg:()=>o,Ub:()=>l});var n=r(34797),i=r(95322),a=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.RD)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},o=e=>{var t=(0,n.j)();return(r,n)=>a=>{null==e||e(r,n,a),t((0,i.oP)())}},l=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.ML)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}}},11333:(e,t,r)=>{r.d(t,{J:()=>x,Z:()=>m});var n=r(82015),i=r(79486),a=r(68812),o=r(80020),l=r(77331),c=r(39683),u=r(28158),s=["offset"],f=["labelRef"];function d(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var h=e=>{var{value:t,formatter:r}=e,n=(0,l.uy)(e.children)?t:e.children;return"function"==typeof r?r(n):n},m=e=>null!=e&&"function"==typeof e,b=(e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),360),g=(e,t,r)=>{var a,o,{position:u,viewBox:s,offset:f,className:d}=e,{cx:v,cy:p,innerRadius:h,outerRadius:m,startAngle:g,endAngle:O,clockWise:w}=s,j=(h+m)/2,x=b(g,O),P=x>=0?1:-1;"insideStart"===u?(a=g+P*f,o=w):"insideEnd"===u?(a=O-P*f,o=!w):"end"===u&&(a=O+P*f,o=w),o=x<=0?o:!o;var E=(0,c.IZ)(v,p,j,a),A=(0,c.IZ)(v,p,j,a+(o?1:-1)*359),S="M".concat(E.x,",").concat(E.y,"\n    A").concat(j,",").concat(j,",0,1,").concat(+!o,",\n    ").concat(A.x,",").concat(A.y),k=(0,l.uy)(e.id)?(0,l.NF)("recharts-radial-line-"):e.id;return n.createElement("text",y({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",d)}),n.createElement("defs",null,n.createElement("path",{id:k,d:S})),n.createElement("textPath",{xlinkHref:"#".concat(k)},t))},O=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:u,endAngle:s}=t,f=(u+s)/2;if("outside"===n){var{x:d,y:v}=(0,c.IZ)(i,a,l+r,f);return{x:d,y:v,textAnchor:d>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:p,y}=(0,c.IZ)(i,a,(o+l)/2,f);return{x:p,y,textAnchor:"middle",verticalAnchor:"middle"}},w=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:c,height:u}=t,s=u>=0?1:-1,f=s*n,d=s>0?"end":"start",v=s>0?"start":"end",y=c>=0?1:-1,h=y*n,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===i)return p(p({},{x:a+c/2,y:o-s*n,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(o-r.y,0),width:c}:{});if("bottom"===i)return p(p({},{x:a+c/2,y:o+u+f,textAnchor:"middle",verticalAnchor:v}),r?{height:Math.max(r.y+r.height-(o+u),0),width:c}:{});if("left"===i){var g={x:a-h,y:o+u/2,textAnchor:m,verticalAnchor:"middle"};return p(p({},g),r?{width:Math.max(g.x-r.x,0),height:u}:{})}if("right"===i){var O={x:a+c+h,y:o+u/2,textAnchor:b,verticalAnchor:"middle"};return p(p({},O),r?{width:Math.max(r.x+r.width-O.x,0),height:u}:{})}var w=r?{width:c,height:u}:{};return"insideLeft"===i?p({x:a+h,y:o+u/2,textAnchor:b,verticalAnchor:"middle"},w):"insideRight"===i?p({x:a+c-h,y:o+u/2,textAnchor:m,verticalAnchor:"middle"},w):"insideTop"===i?p({x:a+c/2,y:o+f,textAnchor:"middle",verticalAnchor:v},w):"insideBottom"===i?p({x:a+c/2,y:o+u-f,textAnchor:"middle",verticalAnchor:d},w):"insideTopLeft"===i?p({x:a+h,y:o+f,textAnchor:b,verticalAnchor:v},w):"insideTopRight"===i?p({x:a+c-h,y:o+f,textAnchor:m,verticalAnchor:v},w):"insideBottomLeft"===i?p({x:a+h,y:o+u-f,textAnchor:b,verticalAnchor:d},w):"insideBottomRight"===i?p({x:a+c-h,y:o+u-f,textAnchor:m,verticalAnchor:d},w):i&&"object"==typeof i&&((0,l.Et)(i.x)||(0,l._3)(i.x))&&((0,l.Et)(i.y)||(0,l._3)(i.y))?p({x:a+(0,l.F4)(i.x,c),y:o+(0,l.F4)(i.y,u),textAnchor:"end",verticalAnchor:"end"},w):p({x:a+c/2,y:o+u/2,textAnchor:"middle",verticalAnchor:"middle"},w)},j=e=>"cx"in e&&(0,l.Et)(e.cx);function x(e){var t,{offset:r=5}=e,c=p({offset:r},d(e,s)),{viewBox:v,position:m,value:b,children:x,content:P,className:E="",textBreakAll:A,labelRef:S}=c,k=(0,u.sk)(),D=v||k;if(!D||(0,l.uy)(b)&&(0,l.uy)(x)&&!(0,n.isValidElement)(P)&&"function"!=typeof P)return null;if((0,n.isValidElement)(P)){var{labelRef:N}=c,B=d(c,f);return(0,n.cloneElement)(P,B)}if("function"==typeof P){if(t=(0,n.createElement)(P,c),(0,n.isValidElement)(t))return t}else t=h(c);var C=j(D),I=(0,o.J9)(c,!0);if(C&&("insideStart"===m||"insideEnd"===m||"end"===m))return g(c,t,I);var R=C?O(c):w(c,D);return n.createElement(a.E,y({ref:S,className:(0,i.$)("recharts-label",E)},I,R,{breakAll:A}),t)}x.displayName="Label";var P=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:c,innerRadius:u,outerRadius:s,x:f,y:d,top:v,left:p,width:y,height:h,clockWise:m,labelViewBox:b}=e;if(b)return b;if((0,l.Et)(y)&&(0,l.Et)(h)){if((0,l.Et)(f)&&(0,l.Et)(d))return{x:f,y:d,width:y,height:h};if((0,l.Et)(v)&&(0,l.Et)(p))return{x:v,y:p,width:y,height:h}}return(0,l.Et)(f)&&(0,l.Et)(d)?{x:f,y:d,width:0,height:0}:(0,l.Et)(t)&&(0,l.Et)(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:u||0,outerRadius:s||c||o||0,clockWise:m}:e.viewBox?e.viewBox:void 0},E=(e,t,r)=>{if(!e)return null;var i={viewBox:t,labelRef:r};return!0===e?n.createElement(x,y({key:"label-implicit"},i)):(0,l.vh)(e)?n.createElement(x,y({key:"label-implicit",value:e},i)):(0,n.isValidElement)(e)?e.type===x?(0,n.cloneElement)(e,p({key:"label-implicit"},i)):n.createElement(x,y({key:"label-implicit",content:e},i)):m(e)?n.createElement(x,y({key:"label-implicit",content:e},i)):e&&"object"==typeof e?n.createElement(x,y({},e,{key:"label-implicit"},i)):null};x.parseViewBox=P,x.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i,labelRef:a}=e,l=P(e),c=(0,o.aS)(i,x).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||l,key:"label-".concat(r)}));return r?[E(e.label,t||l,a),...c]:c}},27978:(e,t,r)=>{r.d(t,{G9:()=>f,_S:()=>d,pU:()=>v,zk:()=>s});var n=r(82015),i=r(53281),a=r(81265),o=7311==r.j?["children"]:null,l=()=>{},c=(0,n.createContext)({addErrorBar:l,removeErrorBar:l}),u=7311==r.j?(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0}):null;function s(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o);return n.createElement(u.Provider,{value:r},t)}var f=()=>(0,n.useContext)(u),d=e=>{var{children:t,xAxisId:r,yAxisId:o,zAxisId:l,dataKey:u,data:s,stackId:f,hide:d,type:v,barSize:p}=e,[y,h]=n.useState([]),m=(0,n.useCallback)(e=>{h(t=>[...t,e])},[h]),b=(0,n.useCallback)(e=>{h(t=>t.filter(t=>t!==e))},[h]),g=(0,a.r)();return n.createElement(c.Provider,{value:{addErrorBar:m,removeErrorBar:b}},n.createElement(i.p,{type:v,data:s,xAxisId:r,yAxisId:o,zAxisId:l,dataKey:u,errorBars:y,stackId:f,hide:d,barSize:p,isPanorama:g}),t)};function v(e){var{addErrorBar:t,removeErrorBar:r}=(0,n.useContext)(c);return(0,n.useEffect)(()=>(t(e),()=>{r(e)}),[t,r,e]),null}},28158:(e,t,r)=>{r.d(t,{W7:()=>s,WX:()=>p,fz:()=>v,rY:()=>d,sk:()=>c,yi:()=>f}),r(82015);var n=r(34797);r(48493);var i=r(78813),a=r(99391),o=r(81265),l=r(99660),c=()=>{var e,t=(0,o.r)(),r=(0,n.G)(i.Ds),a=(0,n.G)(l.U),c=null==(e=(0,n.G)(l.C))?void 0:e.padding;return t&&a&&c?{width:a.width-c.left-c.right,height:a.height-c.top-c.bottom,x:c.left,y:c.top}:r},u={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var e;return null!=(e=(0,n.G)(i.HZ))?e:u},f=()=>(0,n.G)(a.Lp),d=()=>(0,n.G)(a.A$),v=e=>e.layout.layoutType,p=()=>(0,n.G)(v)},36549:(e,t,r)=>{r.d(t,{$:()=>i,X:()=>a});var n=r(82015),i=7311==r.j?(0,n.createContext)(null):null,a=()=>(0,n.useContext)(i)},44581:(e,t,r)=>{r.d(t,{f:()=>n});var n=e=>null;n.displayName="Cell"},57001:(e,t,r)=>{r.d(t,{f:()=>l});var n=r(82015),i=r(77331),a=r(95167),o=7311==r.j?(0,n.createContext)(void 0):null,l=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,i.NF)("recharts"),"-clip")),l=(0,a.oM)();if(null==l)return null;var{x:c,y:u,width:s,height:f}=l;return n.createElement(o.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:c,y:u,height:f,width:s}))),t)}},60897:(e,t,r)=>{r.d(t,{Z:()=>b});var n=r(82015),i=r(69213),a=r.n(i),o=r(11333),l=r(65132),c=r(80020),u=r(29655),s=r(77331),f=["valueAccessor"],d=["data","dataKey","clockWise","id","textBreakAll"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var m=e=>Array.isArray(e.value)?a()(e.value):e.value;function b(e){var{valueAccessor:t=m}=e,r=h(e,f),{data:i,dataKey:a,clockWise:p,id:b,textBreakAll:g}=r,O=h(r,d);return i&&i.length?n.createElement(l.W,{className:"recharts-label-list"},i.map((e,r)=>{var i=(0,s.uy)(a)?t(e,r):(0,u.kr)(e&&e.payload,a),l=(0,s.uy)(b)?{}:{id:"".concat(b,"-").concat(r)};return n.createElement(o.J,v({},(0,c.J9)(e,!0),O,l,{parentViewBox:e.parentViewBox,value:i,textBreakAll:g,viewBox:o.J.parseViewBox((0,s.uy)(p)?e:y(y({},e),{},{clockWise:p})),key:"label-".concat(r),index:r}))})):null}b.displayName="LabelList",b.renderCallByParent=function(e,t){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&i&&!e.label)return null;var{children:a}=e,l=(0,c.aS)(a,b).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return i?[(r=e.label,r?!0===r?n.createElement(b,{key:"labelList-implicit",data:t}):n.isValidElement(r)||(0,o.Z)(r)?n.createElement(b,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?n.createElement(b,v({data:t},r,{key:"labelList-implicit"})):null:null),...l]:l}},65132:(e,t,r)=>{r.d(t,{W:()=>c});var n=r(82015),i=r(79486),a=r(80020),o=7311==r.j?["children","className"]:null;function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var c=7311==r.j?n.forwardRef((e,t)=>{var{children:r,className:c}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o),s=(0,i.$)("recharts-layer",c);return n.createElement("g",l({className:s},(0,a.J9)(u,!0),{ref:t}),r)}):null},68812:(e,t,r)=>{r.d(t,{E:()=>O});var n=r(82015),i=r(79486),a=r(77331),o=r(13141),l=r(80020),c=r(12591),u=r(47684),s=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],f=["dx","dy","angle","className","breakAll"];function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var p=/[ \f\n\r\t\v\u2028\u2029]+/,y=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];(0,a.uy)(t)||(i=r?t.toString().split(""):t.toString().split(p));var o=i.map(e=>({word:e,width:(0,c.P)(e,n).width})),l=r?0:(0,c.P)("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:l}}catch(e){return null}},h=(e,t,r,n,i)=>{var o,{maxLines:l,children:c,style:u,breakAll:s}=e,f=(0,a.Et)(l),d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},v=d(t),p=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!f||i||!(v.length>l||p(v).width>Number(n)))return v;for(var h=e=>{var t=d(y({breakAll:s,style:u,children:c.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>l||p(t).width>Number(n),t]},m=0,b=c.length-1,g=0;m<=b&&g<=c.length-1;){var O=Math.floor((m+b)/2),[w,j]=h(O-1),[x]=h(O);if(w||x||(m=O+1),w&&x&&(b=O-1),!w&&x){o=j;break}g++}return o||v},m=e=>[{words:(0,a.uy)(e)?[]:e.toString().split(p)}],b=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:l}=e;if((t||r)&&!o.m.isSsr){var c=y({breakAll:a,children:n,style:i});if(!c)return m(n);var{wordsWithComputedWidth:u,spaceWidth:s}=c;return h({breakAll:a,children:n,maxLines:l,style:i},u,s,t,r)}return m(n)},g="#808080",O=(0,n.forwardRef)((e,t)=>{var r,{x:o=0,y:c=0,lineHeight:p="1em",capHeight:y="0.71em",scaleToFit:h=!1,textAnchor:m="start",verticalAnchor:O="end",fill:w=g}=e,j=v(e,s),x=(0,n.useMemo)(()=>b({breakAll:j.breakAll,children:j.children,maxLines:j.maxLines,scaleToFit:h,style:j.style,width:j.width}),[j.breakAll,j.children,j.maxLines,h,j.style,j.width]),{dx:P,dy:E,angle:A,className:S,breakAll:k}=j,D=v(j,f);if(!(0,a.vh)(o)||!(0,a.vh)(c))return null;var N=o+((0,a.Et)(P)?P:0),B=c+((0,a.Et)(E)?E:0);switch(O){case"start":r=(0,u.l)("calc(".concat(y,")"));break;case"middle":r=(0,u.l)("calc(".concat((x.length-1)/2," * -").concat(p," + (").concat(y," / 2))"));break;default:r=(0,u.l)("calc(".concat(x.length-1," * -").concat(p,")"))}var C=[];if(h){var I=x[0].width,{width:R}=j;C.push("scale(".concat((0,a.Et)(R)?R/I:1,")"))}return A&&C.push("rotate(".concat(A,", ").concat(N,", ").concat(B,")")),C.length&&(D.transform=C.join(" ")),n.createElement("text",d({},(0,l.J9)(D,!0),{ref:t,x:N,y:B,className:(0,i.$)("recharts-text",S),textAnchor:m,fill:w.includes("url")?g:w}),x.map((e,t)=>{var i=e.words.join(k?"":" ");return n.createElement("tspan",{x:N,dy:0===t?r:p,key:"".concat(i,"-").concat(t)},i)}))});O.displayName="Text"},79332:(e,t,r)=>{r.d(t,{u:()=>f});var n=r(79486),i=r(82015),a=r(10433),o=r.n(a),l=r(77331),c=r(54621);function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f=7311==r.j?(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:u="100%",height:f="100%",minWidth:d=0,minHeight:v,maxHeight:p,children:y,debounce:h=0,id:m,className:b,onResize:g,style:O={}}=e,w=(0,i.useRef)(null),j=(0,i.useRef)();j.current=g,(0,i.useImperativeHandle)(t,()=>w.current);var[x,P]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),E=(0,i.useCallback)((e,t)=>{P(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;E(r,n),null==(t=j.current)||t.call(j,r,n)};h>0&&(e=o()(e,h,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=w.current.getBoundingClientRect();return E(r,n),t.observe(w.current),()=>{t.disconnect()}},[E,h]);var A=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=x;if(e<0||t<0)return null;(0,c.R)((0,l._3)(u)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",u,f),(0,c.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(u)?e:u,a=(0,l._3)(f)?t:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),p&&a>p&&(a=p)),(0,c.R)(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,u,f,d,v,r),i.Children.map(y,e=>(0,i.cloneElement)(e,{width:n,height:a,style:s({width:n,height:a},e.props.style)}))},[r,y,f,p,v,d,x,u]);return i.createElement("div",{id:m?"".concat(m):void 0,className:(0,n.$)("recharts-responsive-container",b),style:s(s({},O),{},{width:u,height:f,minWidth:d,minHeight:v,maxHeight:p}),ref:w},i.createElement("div",{style:{width:0,height:0,overflow:"visible"}},A))}):null},79448:(e,t,r)=>{r.d(t,{$:()=>i});var n=r(34797),i=()=>(0,n.G)(e=>e.rootProps.accessibilityLayer)},81265:(e,t,r)=>{r.d(t,{r:()=>a});var n=r(82015),i=7311==r.j?(0,n.createContext)(null):null,a=()=>null!=(0,n.useContext)(i)},92753:(e,t,r)=>{r.d(t,{t:()=>i});var n=r(82015),i=7311==r.j?(0,n.createContext)(null):null},98812:(e,t,r)=>{r.d(t,{TK:()=>l});var n=r(82015),i=r(87769),a=r(34797),o=r(81265),l=e=>{var{chartData:t}=e,r=(0,a.j)(),l=(0,o.r)();return(0,n.useEffect)(()=>l?()=>{}:(r((0,i.hq)(t)),()=>{r((0,i.hq)(void 0))}),[t,r,l]),null}}};