"use strict";exports.id=1581,exports.ids=[1581],exports.modules={1292:(e,t,r)=>{e.exports=r(40361)},16361:(e,t)=>{function r(e,t){if(t)return e.filter(({key:e})=>t.includes(e))}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getTracedMetadata",{enumerable:!0,get:function(){return r}})},16575:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cleanAmpPath:function(){return i},debounce:function(){return s},isBlockedPage:function(){return o}});let n=r(88198);function o(e){return n.BLOCKED_PAGES.includes(e)}function i(e){return e.match(/\?amp=(y|yes|true|1)/)&&(e=e.replace(/\?amp=(y|yes|true|1)&?/,"?")),e.match(/&amp=(y|yes|true|1)/)&&(e=e.replace(/&amp=(y|yes|true|1)/,"")),e=e.replace(/\?$/,"")}function s(e,t,r=1/0){let n,o,i,a=0,l=0;function c(){let s=Date.now(),u=l+t-s;u<=0||a+r>=s?(n=void 0,e.apply(i,o)):n=setTimeout(c,u)}return function(...e){o=e,i=this,l=Date.now(),void 0===n&&(a=l,n=setTimeout(c,t))}}},18774:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPageFiles",{enumerable:!0,get:function(){return i}});let n=r(68781),o=r(84754);function i(e,t){let r=(0,n.denormalizePagePath)((0,o.normalizePagePath)(t)),i=e.pages[r];return i||(console.warn(`Could not find files for ${r} in .next/build-manifest.json`),[])}},20264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},26428:(e,t,r)=>{function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(39403);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},28584:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Head:function(){return _},Html:function(){return P},Main:function(){return j},NextScript:function(){return b},default:function(){return R}});let n=r(8732),o=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=f(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(82015)),i=r(88198),s=r(18774),a=r(40758),l=function(e){return e&&e.__esModule?e:{default:e}}(r(24437)),c=r(96003),u=r(54440),d=r(56365),p=r(16361);function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(f=function(e){return e?r:t})(e)}let h=new Set;function g(e,t,r){let n=(0,s.getPageFiles)(e,"/_app"),o=r?[]:(0,s.getPageFiles)(e,t);return{sharedFiles:n,pageFiles:o,allFiles:[...new Set([...n,...o])]}}function m(e,t){let{assetPrefix:r,buildManifest:o,assetQueryString:i,disableOptimizedLoading:s,crossOrigin:a}=e;return o.polyfillFiles.filter(e=>e.endsWith(".js")&&!e.endsWith(".module.js")).map(e=>(0,n.jsx)("script",{defer:!s,nonce:t.nonce,crossOrigin:t.crossOrigin||a,noModule:!0,src:`${r}/_next/${(0,u.encodeURIPath)(e)}${i}`},e))}function x({styles:e}){if(!e)return null;let t=Array.isArray(e)?e:[];if(e.props&&Array.isArray(e.props.children)){let r=e=>{var t,r;return null==e||null==(r=e.props)||null==(t=r.dangerouslySetInnerHTML)?void 0:t.__html};e.props.children.forEach(e=>{Array.isArray(e)?e.forEach(e=>r(e)&&t.push(e)):r(e)&&t.push(e)})}return(0,n.jsx)("style",{"amp-custom":"",dangerouslySetInnerHTML:{__html:t.map(e=>e.props.dangerouslySetInnerHTML.__html).join("").replace(/\/\*# sourceMappingURL=.*\*\//g,"").replace(/\/\*@ sourceURL=.*?\*\//g,"")}})}function y(e,t,r){let{dynamicImports:o,assetPrefix:i,isDevelopment:s,assetQueryString:a,disableOptimizedLoading:l,crossOrigin:c}=e;return o.map(e=>!e.endsWith(".js")||r.allFiles.includes(e)?null:(0,n.jsx)("script",{async:!s&&l,defer:!l,src:`${i}/_next/${(0,u.encodeURIPath)(e)}${a}`,nonce:t.nonce,crossOrigin:t.crossOrigin||c},e))}function v(e,t,r){var o;let{assetPrefix:i,buildManifest:s,isDevelopment:a,assetQueryString:l,disableOptimizedLoading:c,crossOrigin:d}=e;return[...r.allFiles.filter(e=>e.endsWith(".js")),...null==(o=s.lowPriorityFiles)?void 0:o.filter(e=>e.endsWith(".js"))].map(e=>(0,n.jsx)("script",{src:`${i}/_next/${(0,u.encodeURIPath)(e)}${l}`,nonce:t.nonce,async:!a&&c,defer:!c,crossOrigin:t.crossOrigin||d},e))}function S(e,t){let{scriptLoader:r,disableOptimizedLoading:i,crossOrigin:s}=e,a=function(e,t){let{assetPrefix:r,scriptLoader:i,crossOrigin:s,nextScriptWorkers:a}=e;if(!a)return null;try{let{partytownSnippet:e}=require("@builder.io/partytown/integration"),a=(Array.isArray(t.children)?t.children:[t.children]).find(e=>{var t,r;return!!e&&!!e.props&&(null==e||null==(r=e.props)||null==(t=r.dangerouslySetInnerHTML)?void 0:t.__html.length)&&"data-partytown-config"in e.props});return(0,n.jsxs)(n.Fragment,{children:[!a&&(0,n.jsx)("script",{"data-partytown-config":"",dangerouslySetInnerHTML:{__html:`
            partytown = {
              lib: "${r}/_next/static/~partytown/"
            };
          `}}),(0,n.jsx)("script",{"data-partytown":"",dangerouslySetInnerHTML:{__html:e()}}),(i.worker||[]).map((e,r)=>{let{strategy:n,src:i,children:a,dangerouslySetInnerHTML:l,...c}=e,u={};if(i)u.src=i;else if(l&&l.__html)u.dangerouslySetInnerHTML={__html:l.__html};else if(a)u.dangerouslySetInnerHTML={__html:"string"==typeof a?a:Array.isArray(a)?a.join(""):""};else throw Object.defineProperty(Error("Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script"),"__NEXT_ERROR_CODE",{value:"E82",enumerable:!1,configurable:!0});return(0,o.createElement)("script",{...u,...c,type:"text/partytown",key:i||r,nonce:t.nonce,"data-nscript":"worker",crossOrigin:t.crossOrigin||s})})]})}catch(e){return(0,l.default)(e)&&e.code,null}}(e,t),c=(r.beforeInteractive||[]).filter(e=>e.src).map((e,r)=>{let{strategy:n,...a}=e;return(0,o.createElement)("script",{...a,key:a.src||r,defer:a.defer??!i,nonce:t.nonce,"data-nscript":"beforeInteractive",crossOrigin:t.crossOrigin||s})});return(0,n.jsxs)(n.Fragment,{children:[a,c]})}class _ extends o.default.Component{static #e=this.contextType=c.HtmlContext;getCssLinks(e){let{assetPrefix:t,assetQueryString:r,dynamicImports:o,dynamicCssManifest:i,crossOrigin:s,optimizeCss:a}=this.context,l=e.allFiles.filter(e=>e.endsWith(".css")),c=new Set(e.sharedFiles),d=new Set([]),p=Array.from(new Set(o.filter(e=>e.endsWith(".css"))));if(p.length){let e=new Set(l);d=new Set(p=p.filter(t=>!(e.has(t)||c.has(t)))),l.push(...p)}let f=[];return l.forEach(e=>{let o=c.has(e),l=d.has(e),p=i.has(e);a||f.push((0,n.jsx)("link",{nonce:this.props.nonce,rel:"preload",href:`${t}/_next/${(0,u.encodeURIPath)(e)}${r}`,as:"style",crossOrigin:this.props.crossOrigin||s},`${e}-preload`)),f.push((0,n.jsx)("link",{nonce:this.props.nonce,rel:"stylesheet",href:`${t}/_next/${(0,u.encodeURIPath)(e)}${r}`,crossOrigin:this.props.crossOrigin||s,"data-n-g":l?void 0:o?"":void 0,"data-n-p":o||l||p?void 0:""},e))}),0===f.length?null:f}getPreloadDynamicChunks(){let{dynamicImports:e,assetPrefix:t,assetQueryString:r,crossOrigin:o}=this.context;return e.map(e=>e.endsWith(".js")?(0,n.jsx)("link",{rel:"preload",href:`${t}/_next/${(0,u.encodeURIPath)(e)}${r}`,as:"script",nonce:this.props.nonce,crossOrigin:this.props.crossOrigin||o},e):null).filter(Boolean)}getPreloadMainLinks(e){let{assetPrefix:t,assetQueryString:r,scriptLoader:o,crossOrigin:i}=this.context,s=e.allFiles.filter(e=>e.endsWith(".js"));return[...(o.beforeInteractive||[]).map(e=>(0,n.jsx)("link",{nonce:this.props.nonce,rel:"preload",href:e.src,as:"script",crossOrigin:this.props.crossOrigin||i},e.src)),...s.map(e=>(0,n.jsx)("link",{nonce:this.props.nonce,rel:"preload",href:`${t}/_next/${(0,u.encodeURIPath)(e)}${r}`,as:"script",crossOrigin:this.props.crossOrigin||i},e))]}getBeforeInteractiveInlineScripts(){let{scriptLoader:e}=this.context,{nonce:t,crossOrigin:r}=this.props;return(e.beforeInteractive||[]).filter(e=>!e.src&&(e.dangerouslySetInnerHTML||e.children)).map((e,n)=>{let{strategy:i,children:s,dangerouslySetInnerHTML:a,src:l,...c}=e,u="";return a&&a.__html?u=a.__html:s&&(u="string"==typeof s?s:Array.isArray(s)?s.join(""):""),(0,o.createElement)("script",{...c,dangerouslySetInnerHTML:{__html:u},key:c.id||n,nonce:t,"data-nscript":"beforeInteractive",crossOrigin:r||void 0})})}getDynamicChunks(e){return y(this.context,this.props,e)}getPreNextScripts(){return S(this.context,this.props)}getScripts(e){return v(this.context,this.props,e)}getPolyfillScripts(){return m(this.context,this.props)}render(){let{styles:e,ampPath:t,inAmpMode:i,hybridAmp:s,canonicalBase:a,__NEXT_DATA__:l,dangerousAsPath:c,headTags:f,unstable_runtimeJS:h,unstable_JsPreload:m,disableOptimizedLoading:y,optimizeCss:v,assetPrefix:S,nextFontManifest:_}=this.context,b=!1===h,P=!1===m||!y;this.context.docComponentsRendered.Head=!0;let{head:j}=this.context,R=[],N=[];j&&(j.forEach(e=>{e&&"link"===e.type&&"preload"===e.props.rel&&"style"===e.props.as?this.context.strictNextHead?R.push(o.default.cloneElement(e,{"data-next-head":""})):R.push(e):e&&(this.context.strictNextHead?N.push(o.default.cloneElement(e,{"data-next-head":""})):N.push(e))}),j=R.concat(N));let A=o.default.Children.toArray(this.props.children).filter(Boolean),E=!1,T=!1;j=o.default.Children.map(j||[],e=>{if(!e)return e;let{type:t,props:r}=e;if(i){let e="";if("meta"===t&&"viewport"===r.name?e='name="viewport"':"link"===t&&"canonical"===r.rel?T=!0:"script"===t&&(r.src&&-1>r.src.indexOf("ampproject")||r.dangerouslySetInnerHTML&&(!r.type||"text/javascript"===r.type))&&(e="<script",Object.keys(r).forEach(t=>{e+=` ${t}="${r[t]}"`}),e+="/>"),e)return null}else"link"===t&&"amphtml"===r.rel&&(E=!0);return e});let M=g(this.context.buildManifest,this.context.__NEXT_DATA__.page,i),O=function(e,t,r=""){if(!e)return{preconnect:null,preload:null};let o=e.pages["/_app"],i=e.pages[t],s=Array.from(new Set([...o??[],...i??[]]));return{preconnect:0===s.length&&(o||i)?(0,n.jsx)("link",{"data-next-font":e.pagesUsingSizeAdjust?"size-adjust":"",rel:"preconnect",href:"/",crossOrigin:"anonymous"}):null,preload:s?s.map(e=>{let t=/\.(woff|woff2|eot|ttf|otf)$/.exec(e)[1];return(0,n.jsx)("link",{rel:"preload",href:`${r}/_next/${(0,u.encodeURIPath)(e)}`,as:"font",type:`font/${t}`,crossOrigin:"anonymous","data-next-font":e.includes("-s")?"size-adjust":""},e)}):null}}(_,c,S),C=((0,p.getTracedMetadata)((0,d.getTracer)().getTracePropagationData(),this.context.experimentalClientTraceMetadata)||[]).map(({key:e,value:t},r)=>(0,n.jsx)("meta",{name:e,content:t},`next-trace-data-${r}`));return(0,n.jsxs)("head",{...function(e){let{crossOrigin:t,nonce:r,...n}=e;return n}(this.props),children:[this.context.isDevelopment&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{"data-next-hide-fouc":!0,"data-ampdevmode":i?"true":void 0,dangerouslySetInnerHTML:{__html:"body{display:none}"}}),(0,n.jsx)("noscript",{"data-next-hide-fouc":!0,"data-ampdevmode":i?"true":void 0,children:(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{display:block}"}})})]}),j,this.context.strictNextHead?null:(0,n.jsx)("meta",{name:"next-head-count",content:o.default.Children.count(j||[]).toString()}),A,O.preconnect,O.preload,i&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("meta",{name:"viewport",content:"width=device-width,minimum-scale=1,initial-scale=1"}),!T&&(0,n.jsx)("link",{rel:"canonical",href:a+r(16575).cleanAmpPath(c)}),(0,n.jsx)("link",{rel:"preload",as:"script",href:"https://cdn.ampproject.org/v0.js"}),(0,n.jsx)(x,{styles:e}),(0,n.jsx)("style",{"amp-boilerplate":"",dangerouslySetInnerHTML:{__html:"body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}"}}),(0,n.jsx)("noscript",{children:(0,n.jsx)("style",{"amp-boilerplate":"",dangerouslySetInnerHTML:{__html:"body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}"}})}),(0,n.jsx)("script",{async:!0,src:"https://cdn.ampproject.org/v0.js"})]}),!i&&(0,n.jsxs)(n.Fragment,{children:[!E&&s&&(0,n.jsx)("link",{rel:"amphtml",href:a+(t||`${c}${c.includes("?")?"&":"?"}amp=1`)}),this.getBeforeInteractiveInlineScripts(),!v&&this.getCssLinks(M),!v&&(0,n.jsx)("noscript",{"data-n-css":this.props.nonce??""}),!b&&!P&&this.getPreloadDynamicChunks(),!b&&!P&&this.getPreloadMainLinks(M),!y&&!b&&this.getPolyfillScripts(),!y&&!b&&this.getPreNextScripts(),!y&&!b&&this.getDynamicChunks(M),!y&&!b&&this.getScripts(M),v&&this.getCssLinks(M),v&&(0,n.jsx)("noscript",{"data-n-css":this.props.nonce??""}),this.context.isDevelopment&&(0,n.jsx)("noscript",{id:"__next_css__DO_NOT_USE__"}),C,e||null]}),o.default.createElement(o.default.Fragment,{},...f||[])]})}}class b extends o.default.Component{static #e=this.contextType=c.HtmlContext;getDynamicChunks(e){return y(this.context,this.props,e)}getPreNextScripts(){return S(this.context,this.props)}getScripts(e){return v(this.context,this.props,e)}getPolyfillScripts(){return m(this.context,this.props)}static getInlineScriptSource(e){let{__NEXT_DATA__:t,largePageDataBytes:n}=e;try{let e=JSON.stringify(t);if(h.has(t.page))return(0,a.htmlEscapeJsonString)(e);let o=Buffer.from(e).byteLength;return r(64210).A,n&&o>n&&h.add(t.page),(0,a.htmlEscapeJsonString)(e)}catch(e){if((0,l.default)(e)&&-1!==e.message.indexOf("circular structure"))throw Object.defineProperty(Error(`Circular structure in "getInitialProps" result of page "${t.page}". https://nextjs.org/docs/messages/circular-structure`),"__NEXT_ERROR_CODE",{value:"E490",enumerable:!1,configurable:!0});throw e}}render(){let{assetPrefix:e,inAmpMode:t,buildManifest:r,unstable_runtimeJS:o,docComponentsRendered:i,assetQueryString:s,disableOptimizedLoading:a,crossOrigin:l}=this.context,c=!1===o;if(i.NextScript=!0,t)return null;let d=g(this.context.buildManifest,this.context.__NEXT_DATA__.page,t);return(0,n.jsxs)(n.Fragment,{children:[!c&&r.devFiles?r.devFiles.map(t=>(0,n.jsx)("script",{src:`${e}/_next/${(0,u.encodeURIPath)(t)}${s}`,nonce:this.props.nonce,crossOrigin:this.props.crossOrigin||l},t)):null,c?null:(0,n.jsx)("script",{id:"__NEXT_DATA__",type:"application/json",nonce:this.props.nonce,crossOrigin:this.props.crossOrigin||l,dangerouslySetInnerHTML:{__html:b.getInlineScriptSource(this.context)}}),a&&!c&&this.getPolyfillScripts(),a&&!c&&this.getPreNextScripts(),a&&!c&&this.getDynamicChunks(d),a&&!c&&this.getScripts(d)]})}}function P(e){let{inAmpMode:t,docComponentsRendered:r,locale:i,scriptLoader:s,__NEXT_DATA__:a}=(0,c.useHtmlContext)();return r.Html=!0,!function(e,t,r){var n,i,s,a;if(!r.children)return;let l=[],c=Array.isArray(r.children)?r.children:[r.children],u=null==(i=c.find(e=>e.type===_))||null==(n=i.props)?void 0:n.children,d=null==(a=c.find(e=>"body"===e.type))||null==(s=a.props)?void 0:s.children,p=[...Array.isArray(u)?u:[u],...Array.isArray(d)?d:[d]];o.default.Children.forEach(p,t=>{var r;if(t&&(null==(r=t.type)?void 0:r.__nextScript)){if("beforeInteractive"===t.props.strategy){e.beforeInteractive=(e.beforeInteractive||[]).concat([{...t.props}]);return}else if(["lazyOnload","afterInteractive","worker"].includes(t.props.strategy))return void l.push(t.props);else if(void 0===t.props.strategy)return void l.push({...t.props,strategy:"afterInteractive"})}}),t.scriptLoader=l}(s,a,e),(0,n.jsx)("html",{...e,lang:e.lang||i||void 0,amp:t?"":void 0,"data-ampdevmode":void 0})}function j(){let{docComponentsRendered:e}=(0,c.useHtmlContext)();return e.Main=!0,(0,n.jsx)("next-js-internal-body-render-target",{})}class R extends o.default.Component{static getInitialProps(e){return e.defaultGetInitialProps(e)}render(){return(0,n.jsxs)(P,{children:[(0,n.jsx)(_,{}),(0,n.jsxs)("body",{children:[(0,n.jsx)(j,{}),(0,n.jsx)(b,{})]})]})}}R[i.NEXT_BUILTIN_DOCUMENT]=function(){return(0,n.jsxs)(P,{children:[(0,n.jsx)(_,{}),(0,n.jsxs)("body",{children:[(0,n.jsx)(j,{}),(0,n.jsx)(b,{})]})]})}},34507:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NEXT_REQUEST_META:function(){return r},addRequestMeta:function(){return i},getRequestMeta:function(){return n},removeRequestMeta:function(){return s},setRequestMeta:function(){return o}});let r=Symbol.for("NextInternalRequestMeta");function n(e,t){let n=e[r]||{};return"string"==typeof t?n[t]:n}function o(e,t){return e[r]=t,t}function i(e,t,r){let i=n(e);return i[t]=r,o(e,i)}function s(e,t){let r=n(e);return delete r[t],o(e,r)}},40758:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ESCAPE_REGEX:function(){return n},htmlEscapeJsonString:function(){return o}});let r={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"},n=/[&><\u2028\u2029]/g;function o(e){return e.replace(n,e=>r[e])}},41195:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(2403),o=r(8732),i=n._(r(82015)),s=n._(r(8193)),a={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function l(e){let t,{req:n,res:o,err:i}=e,s=o&&o.statusCode?o.statusCode:i?i.statusCode:404;if(n){let{getRequestMeta:e}=r(34507),o=e(n,"initURL");o&&(t=new URL(o).hostname)}return{statusCode:s,hostname:t}}let c={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class u extends i.default.Component{render(){let{statusCode:e,withDarkMode:t=!0}=this.props,r=this.props.title||a[e]||"An unexpected error has occurred";return(0,o.jsxs)("div",{style:c.error,children:[(0,o.jsx)(s.default,{children:(0,o.jsx)("title",{children:e?e+": "+r:"Application error: a client-side exception has occurred"})}),(0,o.jsxs)("div",{style:c.desc,children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(t?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?(0,o.jsx)("h1",{className:"next-error-h1",style:c.h1,children:e}):null,(0,o.jsx)("div",{style:c.wrap,children:(0,o.jsxs)("h2",{style:c.h2,children:[this.props.title||e?r:(0,o.jsxs)(o.Fragment,{children:["Application error: a client-side exception has occurred"," ",!!this.props.hostname&&(0,o.jsxs)(o.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}u.displayName="ErrorPage",u.getInitialProps=l,u.origGetInitialProps=l,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56365:(e,t,r)=>{let n;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BubbledError:function(){return p},SpanKind:function(){return u},SpanStatusCode:function(){return c},getTracer:function(){return _},isBubbledError:function(){return f}});let o=r(56465),i=r(34977);try{n=r(21385)}catch(e){n=r(21385)}let{context:s,propagation:a,trace:l,SpanStatusCode:c,SpanKind:u,ROOT_CONTEXT:d}=n;class p extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}function f(e){return"object"==typeof e&&null!==e&&e instanceof p}let h=(e,t)=>{f(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:c.ERROR,message:null==t?void 0:t.message})),e.end()},g=new Map,m=n.createContextKey("next.rootSpanId"),x=0,y=()=>x++,v={set(e,t,r){e.push({key:t,value:r})}};class S{getTracerInstance(){return l.getTracer("next.js","0.0.1")}getContext(){return s}getTracePropagationData(){let e=s.active(),t=[];return a.inject(e,t,v),t}getActiveScopeSpan(){return l.getSpan(null==s?void 0:s.active())}withPropagatedContext(e,t,r){let n=s.active();if(l.getSpanContext(n))return t();let o=a.extract(n,e,r);return s.with(o,t)}trace(...e){var t;let[r,n,a]=e,{fn:c,options:u}="function"==typeof n?{fn:n,options:{}}:{fn:a,options:{...n}},p=u.spanName??r;if(!o.NextVanillaSpanAllowlist.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||u.hideSpan)return c();let f=this.getSpanContext((null==u?void 0:u.parentSpan)??this.getActiveScopeSpan()),x=!1;f?(null==(t=l.getSpanContext(f))?void 0:t.isRemote)&&(x=!0):(f=(null==s?void 0:s.active())??d,x=!0);let v=y();return u.attributes={"next.span_name":p,"next.span_type":r,...u.attributes},s.with(f.setValue(m,v),()=>this.getTracerInstance().startActiveSpan(p,u,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{g.delete(v),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&o.LogSpanAllowList.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};x&&g.set(v,new Map(Object.entries(u.attributes??{})));try{if(c.length>1)return c(e,t=>h(e,t));let t=c(e);if((0,i.isThenable)(t))return t.then(t=>(e.end(),t)).catch(t=>{throw h(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw h(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return o.NextVanillaSpanAllowlist.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let o=arguments.length-1,a=arguments[o];if("function"!=typeof a)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(s.active(),a);return t.trace(r,e,(e,t)=>(arguments[o]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?l.setSpan(s.active(),e):void 0}getRootSpanAttributes(){let e=s.active().getValue(m);return g.get(e)}setRootSpanAttribute(e,t){let r=s.active().getValue(m),n=g.get(r);n&&n.set(e,t)}}let _=(()=>{let e=new S;return()=>e})()},56465:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppRenderSpan:function(){return l},AppRouteRouteHandlersSpan:function(){return d},BaseServerSpan:function(){return r},LoadComponentsSpan:function(){return n},LogSpanAllowList:function(){return g},MiddlewareSpan:function(){return f},NextNodeServerSpan:function(){return i},NextServerSpan:function(){return o},NextVanillaSpanAllowlist:function(){return h},NodeSpan:function(){return u},RenderSpan:function(){return a},ResolveMetadataSpan:function(){return p},RouterSpan:function(){return c},StartServerSpan:function(){return s}});var r=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(r||{}),n=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(n||{}),o=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(o||{}),i=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(i||{}),s=function(e){return e.startServer="startServer.startServer",e}(s||{}),a=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(a||{}),l=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(l||{}),c=function(e){return e.executeRoute="Router.executeRoute",e}(c||{}),u=function(e){return e.runHandler="Node.runHandler",e}(u||{}),d=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(d||{}),p=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(p||{}),f=function(e){return e.execute="Middleware.execute",e}(f||{});let h=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],g=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"]},58834:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},58914:(e,t,r)=>{e.exports=r(1292).vendored.contexts.Loadable},75100:(e,t,r)=>{e.exports=r(1292).vendored.contexts.HeadManagerContext},78811:(e,t,r)=>{e.exports=r(1292).vendored.contexts.RouterContext},93118:(e,t,r)=>{e.exports=r(1292).vendored.contexts.AmpContext},93433:(e,t,r)=>{e.exports=r(75600)},96003:(e,t,r)=>{e.exports=r(1292).vendored.contexts.HtmlContext}};