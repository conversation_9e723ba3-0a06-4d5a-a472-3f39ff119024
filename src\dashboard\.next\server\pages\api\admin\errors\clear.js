"use strict";(()=>{var e={};e.id=3488,e.ids=[3488],e.modules={2943:(e,r,t)=>{t.r(r),t.d(r,{config:()=>g,default:()=>x,routeModule:()=>b});var a={};t.r(a),t.d(a,{default:()=>h});var o=t(93433),s=t(20264),n=t(20584),i=t(15806),d=t(94506),u=t(98580),l=t(12518);let c=null,p=u.dashboardConfig.database?.url||"mongodb://localhost:27017",m=u.dashboardConfig.database?.name||"discord_bot";async function f(){return c||(c=await l.MongoClient.connect(p,{...u.dashboardConfig.database?.options||{}})),c.db(m)}async function h(e,r){if("DELETE"!==e.method)return r.status(405).json({error:"Method not allowed"});try{let t=await (0,i.getServerSession)(e,r,d.authOptions);if(!t)return r.status(401).json({error:"Unauthorized"});if(!t.user.isAdmin)return r.status(403).json({error:"Forbidden - Admin access required"});let a=await f(),o=await a.collection("error_logs").deleteMany({});r.status(200).json({success:!0,deletedCount:o.deletedCount})}catch(e){r.status(500).json({error:"Internal server error",details:e.message})}}let x=(0,n.M)(a,"default"),g=(0,n.M)(a,"config"),b=new o.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/admin/errors/clear",pathname:"/api/admin/errors/clear",bundlePath:"",filename:""},userland:a})},12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(2943));module.exports=a})();