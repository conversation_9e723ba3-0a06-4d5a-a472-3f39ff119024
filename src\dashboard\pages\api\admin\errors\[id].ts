import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { dashboardConfig } from '../../../../core/config';
import { MongoClient, ObjectId } from 'mongodb';

// Reuse connection pattern
let cachedClient: MongoClient | null = null;
const mongoUrl = dashboardConfig.database?.url || 'mongodb://localhost:27017';
const dbName = dashboardConfig.database?.name || 'discord_bot';

async function getDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl, {
      ...(dashboardConfig.database?.options || {}),
    });
  }
  return cachedClient.db(dbName);
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Invalid error ID' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check admin permission
    const isAdmin = (session.user as any).isAdmin;
    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const db = await getDb();

    if (req.method === 'PATCH') {
      // Update error (mark as resolved)
      const { resolved } = req.body;

      const result = await db.collection('error_logs').updateOne(
        { _id: new ObjectId(id) },
        { 
          $set: { 
            resolved: resolved,
            resolvedAt: resolved ? new Date() : null,
            resolvedBy: resolved ? (session.user as any).id : null
          } 
        }
      );

      if (result.matchedCount === 0) {
        return res.status(404).json({ error: 'Error not found' });
      }

      res.status(200).json({ success: true });
    } else if (req.method === 'DELETE') {
      // Delete error
      const result = await db.collection('error_logs').deleteOne({
        _id: new ObjectId(id)
      });

      if (result.deletedCount === 0) {
        return res.status(404).json({ error: 'Error not found' });
      }

      res.status(200).json({ success: true });
    } else {
      res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error: any) {
    console.error('Error managing error log:', error);
    res.status(500).json({ error: 'Internal server error', details: error.message });
  }
} 