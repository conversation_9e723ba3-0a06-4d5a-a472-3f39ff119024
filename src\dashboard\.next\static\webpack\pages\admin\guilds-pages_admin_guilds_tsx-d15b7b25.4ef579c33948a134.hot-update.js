"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/guilds-pages_admin_guilds_tsx-d15b7b25",{

/***/ "(pages-dir-browser)/./pages/admin/guilds.tsx":
/*!********************************!*\
  !*** ./pages/admin/guilds.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: () => (/* binding */ __N_SSP),\n/* harmony export */   \"default\": () => (/* binding */ ServerManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Card,CardBody,CardHeader,Checkbox,Container,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,SimpleGrid,Skeleton,Spinner,Tab,TabList,TabPanel,TabPanels,Table,Tabs,Tbody,Td,Text,Th,Thead,Tooltip,Tr,VStack,useDisclosure,useToast!=!@chakra-ui/react */ \"(pages-dir-browser)/__barrel_optimize__?names=Badge,Box,Button,Card,CardBody,CardHeader,Checkbox,Container,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,SimpleGrid,Skeleton,Spinner,Tab,TabList,TabPanel,TabPanels,Table,Tabs,Tbody,Td,Text,Th,Thead,Tooltip,Tr,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/Layout */ \"(pages-dir-browser)/./components/Layout.tsx\");\n/* harmony import */ var _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useGuildInfo */ \"(pages-dir-browser)/./hooks/useGuildInfo.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit2,FiFolderPlus,FiHash,FiLock,FiMessageCircle,FiMessageSquare,FiPlus,FiRadio,FiSave,FiServer,FiSettings,FiTool,FiTrash2,FiUsers,FiVolume2,FiZap!=!react-icons/fi */ \"(pages-dir-browser)/__barrel_optimize__?names=FiEdit2,FiFolderPlus,FiHash,FiLock,FiMessageCircle,FiMessageSquare,FiPlus,FiRadio,FiSave,FiServer,FiSettings,FiTool,FiTrash2,FiUsers,FiVolume2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaPalette!=!react-icons/fa */ \"(pages-dir-browser)/__barrel_optimize__?names=FaPalette!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_4__);\n// @ts-nocheck\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Dynamic imports for heavy components\nconst CreateChannelDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_a\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"_pages-dir-browser_components_CreateChannelDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/CreateChannelDialog */ \"(pages-dir-browser)/./components/CreateChannelDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/CreateChannelDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 63,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c = CreateChannelDialog;\nconst EditChannelDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_a\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"_pages-dir-browser_components_EditChannelDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/EditChannelDialog */ \"(pages-dir-browser)/./components/EditChannelDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/EditChannelDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 68,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c1 = EditChannelDialog;\nconst EditRoleDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"commons\"), __webpack_require__.e(\"_pages-dir-browser_components_EditRoleDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/EditRoleDialog */ \"(pages-dir-browser)/./components/EditRoleDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/EditRoleDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 73,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c2 = EditRoleDialog;\nconst ColorBuilder = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-browser_components_ColorBuilder_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/ColorBuilder */ \"(pages-dir-browser)/./components/ColorBuilder.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/ColorBuilder\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 78,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c3 = ColorBuilder;\nconst CreateRoleDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"commons\"), __webpack_require__.e(\"_pages-dir-browser_components_CreateRoleDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/CreateRoleDialog */ \"(pages-dir-browser)/./components/CreateRoleDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/CreateRoleDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 83,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c4 = CreateRoleDialog;\n// Rate limiting constants\nconst RATE_LIMIT_MS = 2000; // 2 seconds between operations\nconst BULK_RATE_LIMIT_MS = 5000; // 5 seconds between bulk operations\n// Custom hook for rate limiting\nfunction useRateLimit() {\n    let delay = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : RATE_LIMIT_MS;\n    _s();\n    const [isRateLimited, setIsRateLimited] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const resetRateLimit = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"useRateLimit.useCallback[resetRateLimit]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            setIsRateLimited(true);\n            timeoutRef.current = setTimeout({\n                \"useRateLimit.useCallback[resetRateLimit]\": ()=>{\n                    setIsRateLimited(false);\n                }\n            }[\"useRateLimit.useCallback[resetRateLimit]\"], delay);\n        }\n    }[\"useRateLimit.useCallback[resetRateLimit]\"], [\n        delay\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"useRateLimit.useEffect\": ()=>{\n            return ({\n                \"useRateLimit.useEffect\": ()=>{\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"useRateLimit.useEffect\"];\n        }\n    }[\"useRateLimit.useEffect\"], []);\n    return [\n        isRateLimited,\n        resetRateLimit\n    ];\n}\n_s(useRateLimit, \"C8Tx8E3LpqDtNI/63DsUKsVihwU=\");\nconst CHANNEL_TYPE_CONFIG = {\n    0: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare,\n        color: 'blue',\n        label: 'Text'\n    },\n    2: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiVolume2,\n        color: 'green',\n        label: 'Voice'\n    },\n    4: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiFolderPlus,\n        color: 'purple',\n        label: 'Category'\n    },\n    5: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiRadio,\n        color: 'orange',\n        label: 'Announcement'\n    },\n    11: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageCircle,\n        color: 'cyan',\n        label: 'Public Thread'\n    },\n    12: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiLock,\n        color: 'pink',\n        label: 'Private Thread'\n    },\n    13: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHash,\n        color: 'teal',\n        label: 'Stage Voice'\n    },\n    15: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHash,\n        color: 'gray',\n        label: 'Forum'\n    }\n};\nconst PERMISSION_BADGES = {\n    ADMINISTRATOR: {\n        color: 'red',\n        label: 'Admin'\n    },\n    MANAGE_GUILD: {\n        color: 'orange',\n        label: 'Manage Server'\n    },\n    MANAGE_ROLES: {\n        color: 'yellow',\n        label: 'Manage Roles'\n    },\n    MANAGE_CHANNELS: {\n        color: 'green',\n        label: 'Manage Channels'\n    },\n    KICK_MEMBERS: {\n        color: 'purple',\n        label: 'Kick'\n    },\n    BAN_MEMBERS: {\n        color: 'pink',\n        label: 'Ban'\n    },\n    MANAGE_MESSAGES: {\n        color: 'blue',\n        label: 'Manage Messages'\n    },\n    MENTION_EVERYONE: {\n        color: 'cyan',\n        label: 'Mention @everyone'\n    }\n};\n// Add this helper map and function after PERMISSION_BADGES constant\nconst PERMISSION_FLAG_BITS = {\n    ADMINISTRATOR: 1n << 3n,\n    MANAGE_GUILD: 1n << 5n,\n    MANAGE_ROLES: 1n << 28n,\n    MANAGE_CHANNELS: 1n << 4n,\n    KICK_MEMBERS: 1n << 1n,\n    BAN_MEMBERS: 1n << 2n,\n    MANAGE_MESSAGES: 1n << 13n,\n    MENTION_EVERYONE: 1n << 17n\n};\nfunction decodePermissions(bitfield) {\n    if (!bitfield) return [];\n    if (Array.isArray(bitfield)) return bitfield;\n    try {\n        const permissions = [];\n        const bits = BigInt(bitfield);\n        for (const [permission, bit] of Object.entries(PERMISSION_FLAG_BITS)){\n            if ((bits & bit) === bit) {\n                permissions.push(permission);\n            }\n        }\n        return permissions;\n    } catch (error) {\n        console.error('Error decoding permissions:', error);\n        return [];\n    }\n}\nvar __N_SSP = true;\nfunction ServerManagement() {\n    _s1();\n    const toast = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const { displayName } = (0,_hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [isRateLimited, resetRateLimit] = useRateLimit();\n    const [isBulkRateLimited, resetBulkRateLimit] = useRateLimit(BULK_RATE_LIMIT_MS);\n    // State for guild settings\n    const [guildData, setGuildData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        prefix: '!',\n        botName: 'Bot',\n        guildName: '',\n        guildIcon: null,\n        activities: [\n            {\n                type: 'PLAYING',\n                name: 'with Discord.js'\n            }\n        ],\n        activityRotationInterval: 60\n    });\n    // State for roles and channels\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [channels, setChannels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [channelsLoading, setChannelsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Modal states\n    const { isOpen: isCreateChannelOpen, onOpen: onCreateChannelOpen, onClose: onCreateChannelClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isEditChannelOpen, onOpen: onEditChannelOpen, onClose: onEditChannelClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isCreateRoleOpen, onOpen: onCreateRoleOpen, onClose: onCreateRoleClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isEditRoleOpen, onOpen: onEditRoleOpen, onClose: onEditRoleClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isColorBuilderOpen, onOpen: onColorBuilderOpen, onClose: onColorBuilderClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    // Selected items for editing\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedChannel, setSelectedChannel] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // Bulk selection state\n    const [selectedRoles, setSelectedRoles] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedChannels, setSelectedChannels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bulkDeleting, setBulkDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // File upload state\n    const [iconFile, setIconFile] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [iconPreview, setIconPreview] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const handleIconFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            setIconFile(file);\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                return setIconPreview((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result);\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const uploadIcon = async ()=>{\n        if (!iconFile) return;\n        const formData = new FormData();\n        formData.append('icon', iconFile);\n        try {\n            const response = await fetch('/api/discord/settings', {\n                method: 'POST',\n                body: formData\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setGuildData((prev)=>({\n                        ...prev,\n                        guildIcon: data.iconUrl\n                    }));\n                setIconFile(null);\n                setIconPreview(null);\n                toast({\n                    title: 'Success',\n                    description: 'Guild icon updated successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to upload icon',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const fetchGuildData = async ()=>{\n        try {\n            const [guildResponse, rolesResponse] = await Promise.all([\n                fetch('/api/discord/guild'),\n                fetch('/api/discord/roles')\n            ]);\n            if (guildResponse.ok) {\n                const guild = await guildResponse.json();\n                setGuildData((prev)=>({\n                        ...prev,\n                        guildName: guild.name,\n                        guildIcon: guild.icon,\n                        botName: guild.botName || prev.botName // Use API botName or fallback to current\n                    }));\n            }\n            if (rolesResponse.ok) {\n                const rolesData = await rolesResponse.json();\n                const arr = Array.isArray(rolesData) ? rolesData : rolesData.roles || [];\n                setRoles(arr.sort((a, b)=>b.position - a.position));\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch guild data',\n                status: 'error',\n                duration: 3000\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchChannels = async ()=>{\n        try {\n            const response = await fetch('/api/discord/channels');\n            if (!response.ok) {\n                throw new Error('Failed to fetch channels');\n            }\n            const data = await response.json();\n            const sortedChannels = (data || []).sort((a, b)=>{\n                if (a.type === 4 && b.type !== 4) return -1;\n                if (a.type !== 4 && b.type === 4) return 1;\n                return a.position - b.position;\n            });\n            setChannels(sortedChannels);\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch channels',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setChannelsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"ServerManagement.useEffect\": ()=>{\n            fetchGuildData();\n            fetchChannels();\n        }\n    }[\"ServerManagement.useEffect\"], []);\n    const handleSettingChange = (setting, value)=>{\n        setGuildData((prev)=>({\n                ...prev,\n                [setting]: value\n            }));\n    };\n    const saveSettings = async ()=>{\n        if (saving || isRateLimited) return;\n        setSaving(true);\n        resetRateLimit();\n        try {\n            const response = await fetch('/api/discord/settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(guildData)\n            });\n            if (response.ok) {\n                toast({\n                    title: 'Success',\n                    description: 'Settings saved successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            } else {\n                throw new Error('Failed to save settings');\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to save settings',\n                status: 'error',\n                duration: 3000\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleRoleEdit = (role)=>{\n        setSelectedRole(role);\n        onEditRoleOpen();\n    };\n    const handleChannelEdit = (channel)=>{\n        setSelectedChannel(channel);\n        onEditChannelOpen();\n    };\n    const handleChannelCreate = ()=>{\n        onCreateChannelOpen();\n    };\n    const handleRoleCreate = ()=>{\n        onCreateRoleOpen();\n    };\n    const handleChannelDelete = async (channelId)=>{\n        if (isRateLimited) return;\n        try {\n            resetRateLimit();\n            const response = await fetch(\"/api/discord/channels/\".concat(channelId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchChannels();\n                toast({\n                    title: 'Success',\n                    description: 'Channel deleted successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to delete channel',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const getParentName = (parentId)=>{\n        if (!parentId || !channels) return '-';\n        const parent = channels.find((c)=>c.id === parentId);\n        return parent ? parent.name : '-';\n    };\n    // Bulk delete functions\n    const handleBulkDeleteRoles = async ()=>{\n        if (selectedRoles.length === 0 || bulkDeleting || isBulkRateLimited) return;\n        setBulkDeleting(true);\n        resetBulkRateLimit();\n        try {\n            const response = await fetch('/api/discord/roles/bulk-delete', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    roleIds: selectedRoles\n                })\n            });\n            const result = await response.json();\n            if (response.ok) {\n                toast({\n                    title: 'Success',\n                    description: result.message,\n                    status: 'success',\n                    duration: 5000\n                });\n                setSelectedRoles([]);\n                fetchGuildData(); // Refresh roles\n            } else {\n                throw new Error(result.error || 'Failed to delete roles');\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to delete roles',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setBulkDeleting(false);\n        }\n    };\n    const handleBulkDeleteChannels = async ()=>{\n        if (selectedChannels.length === 0 || bulkDeleting || isBulkRateLimited) return;\n        setBulkDeleting(true);\n        resetBulkRateLimit();\n        try {\n            const response = await fetch('/api/discord/channels/bulk-delete', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    channelIds: selectedChannels\n                })\n            });\n            const result = await response.json();\n            if (response.ok) {\n                toast({\n                    title: 'Success',\n                    description: result.message,\n                    status: 'success',\n                    duration: 5000\n                });\n                setSelectedChannels([]);\n                fetchChannels(); // Refresh channels\n            } else {\n                throw new Error(result.error || 'Failed to delete channels');\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to delete channels',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setBulkDeleting(false);\n        }\n    };\n    // Selection helper functions\n    const toggleRoleSelection = (roleId)=>{\n        setSelectedRoles((prev)=>prev.includes(roleId) ? prev.filter((id)=>id !== roleId) : [\n                ...prev,\n                roleId\n            ]);\n    };\n    const toggleChannelSelection = (channelId)=>{\n        setSelectedChannels((prev)=>prev.includes(channelId) ? prev.filter((id)=>id !== channelId) : [\n                ...prev,\n                channelId\n            ]);\n    };\n    const selectAllRoles = ()=>{\n        const selectableRoles = roles.filter((role)=>role.name !== '@everyone').map((role)=>role.id);\n        setSelectedRoles(selectedRoles.length === selectableRoles.length ? [] : selectableRoles);\n    };\n    const selectAllChannels = ()=>{\n        const channelIds = channels.map((channel)=>channel.id);\n        setSelectedChannels(selectedChannels.length === channelIds.length ? [] : channelIds);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                    spacing: 6,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                            height: \"60px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                            height: \"400px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 558,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                lineNumber: 557,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 556,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                    spacing: 8,\n                    align: \"stretch\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"rgba(255,255,255,0.08)\",\n                            p: 8,\n                            rounded: \"2xl\",\n                            backdropFilter: \"blur(10px)\",\n                            border: \"2px solid\",\n                            borderColor: \"blue.400\",\n                            boxShadow: \"0 0 15px rgba(66, 153, 225, 0.4)\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                justify: \"space-between\",\n                                align: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                size: \"xl\",\n                                                bgGradient: \"linear(to-r, blue.300, purple.400)\",\n                                                bgClip: \"text\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                        as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiServer,\n                                                        mr: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Server Management\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                color: \"gray.300\",\n                                                mt: 2,\n                                                children: [\n                                                    \"Comprehensive management for \",\n                                                    displayName || guildData.guildName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSave, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 27\n                                        }, void 0),\n                                        colorScheme: \"blue\",\n                                        onClick: saveSettings,\n                                        isLoading: saving,\n                                        isDisabled: isRateLimited,\n                                        size: \"lg\",\n                                        children: \"Save Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                            colorScheme: \"blue\",\n                            isLazy: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"General Settings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPalette,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Theme Builder\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTool,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Builders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Automation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanels, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 8,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                    size: \"md\",\n                                                                    children: \"Basic Settings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 636,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                                    columns: {\n                                                                        base: 1,\n                                                                        lg: 2\n                                                                    },\n                                                                    spacing: 6,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Bot Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 642,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.botName,\n                                                                                            onChange: (e)=>handleSettingChange('botName', e.target.value),\n                                                                                            placeholder: \"Enter bot name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 643,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 641,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Command Prefix\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 650,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.prefix,\n                                                                                            onChange: (e)=>handleSettingChange('prefix', e.target.value),\n                                                                                            placeholder: \"Enter command prefix\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 651,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 649,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 640,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Server Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 660,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.guildName || '',\n                                                                                            isReadOnly: true,\n                                                                                            bg: \"gray.50\",\n                                                                                            _dark: {\n                                                                                                bg: 'gray.700'\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 661,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 659,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Server ID\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 669,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.guildId || '',\n                                                                                            isReadOnly: true,\n                                                                                            bg: \"gray.50\",\n                                                                                            _dark: {\n                                                                                                bg: 'gray.700'\n                                                                                            },\n                                                                                            fontFamily: \"mono\",\n                                                                                            fontSize: \"sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 670,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 668,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                                    size: \"md\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                                            as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiUsers,\n                                                                                            mr: 2\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 690,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        \"Roles (\",\n                                                                                        roles.length,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 689,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPlus, {}, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 695,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        colorScheme: \"green\",\n                                                                                        onClick: handleRoleCreate,\n                                                                                        isDisabled: isRateLimited,\n                                                                                        size: \"sm\",\n                                                                                        children: \"Create Role\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 694,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 693,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            p: 3,\n                                                                            bg: \"blue.50\",\n                                                                            _dark: {\n                                                                                bg: 'blue.900'\n                                                                            },\n                                                                            borderRadius: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    fontWeight: \"medium\",\n                                                                                    children: [\n                                                                                        selectedRoles.length,\n                                                                                        \" role(s) selected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 708,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            variant: \"outline\",\n                                                                                            onClick: ()=>setSelectedRoles([]),\n                                                                                            children: \"Clear Selection\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 712,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            colorScheme: \"red\",\n                                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 722,\n                                                                                                columnNumber: 43\n                                                                                            }, void 0),\n                                                                                            onClick: handleBulkDeleteRoles,\n                                                                                            isLoading: bulkDeleting,\n                                                                                            isDisabled: isBulkRateLimited,\n                                                                                            children: \"Delete Selected\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 719,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 711,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 3,\n                                                                    children: [\n                                                                        ...Array(3)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                                            height: \"60px\"\n                                                                        }, i, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 738,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 736,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                                    overflowX: \"auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                                                        variant: \"simple\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Thead, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                isChecked: selectedRoles.length === roles.filter((r)=>r.name !== '@everyone').length && roles.length > 1,\n                                                                                                isIndeterminate: selectedRoles.length > 0 && selectedRoles.length < roles.filter((r)=>r.name !== '@everyone').length,\n                                                                                                onChange: selectAllRoles\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 747,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 746,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Role\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 753,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Members\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 754,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Permissions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 755,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Actions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 756,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 745,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 744,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tbody, {\n                                                                                children: (roles || []).map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                    isChecked: selectedRoles.includes(role.id),\n                                                                                                    onChange: ()=>toggleRoleSelection(role.id),\n                                                                                                    isDisabled: role.name === '@everyone'\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 763,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 762,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                                                                            w: 4,\n                                                                                                            h: 4,\n                                                                                                            rounded: \"full\",\n                                                                                                            bg: role.color ? \"#\".concat(role.color.toString(16).padStart(6, '0')) : 'gray.500'\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 771,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                            children: role.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 777,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 770,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 769,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                    colorScheme: \"blue\",\n                                                                                                    children: \"0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 781,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 780,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    wrap: \"wrap\",\n                                                                                                    spacing: 1,\n                                                                                                    children: [\n                                                                                                        (decodePermissions(role.permissions) || []).slice(0, 3).map((perm)=>{\n                                                                                                            var _PERMISSION_BADGES_perm, _PERMISSION_BADGES_perm1;\n                                                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                                colorScheme: ((_PERMISSION_BADGES_perm = PERMISSION_BADGES[perm]) === null || _PERMISSION_BADGES_perm === void 0 ? void 0 : _PERMISSION_BADGES_perm.color) || 'gray',\n                                                                                                                size: \"sm\",\n                                                                                                                children: ((_PERMISSION_BADGES_perm1 = PERMISSION_BADGES[perm]) === null || _PERMISSION_BADGES_perm1 === void 0 ? void 0 : _PERMISSION_BADGES_perm1.label) || perm\n                                                                                                            }, perm, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 786,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this);\n                                                                                                        }),\n                                                                                                        decodePermissions(role.permissions).length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                            colorScheme: \"gray\",\n                                                                                                            size: \"sm\",\n                                                                                                            children: [\n                                                                                                                \"+\",\n                                                                                                                decodePermissions(role.permissions).length - 3\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 795,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 784,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 783,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Edit Role\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Edit role\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiEdit2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 806,\n                                                                                                                    columnNumber: 49\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"blue\",\n                                                                                                                onClick: ()=>handleRoleEdit(role),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 804,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 803,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Delete Role\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Delete role\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 817,\n                                                                                                                    columnNumber: 49\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"red\",\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 815,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 814,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 802,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 801,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, role.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 761,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 759,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 743,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 742,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                                    size: \"md\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                                            as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHash,\n                                                                                            mr: 2\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 841,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        \"Channels (\",\n                                                                                        channels.length,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 840,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPlus, {}, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 846,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        colorScheme: \"blue\",\n                                                                                        onClick: onCreateChannelOpen,\n                                                                                        size: \"sm\",\n                                                                                        children: \"Create Channel\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 845,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 844,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 839,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedChannels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            p: 3,\n                                                                            bg: \"blue.50\",\n                                                                            _dark: {\n                                                                                bg: 'blue.900'\n                                                                            },\n                                                                            borderRadius: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    fontWeight: \"medium\",\n                                                                                    children: [\n                                                                                        selectedChannels.length,\n                                                                                        \" channel(s) selected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 858,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            variant: \"outline\",\n                                                                                            onClick: ()=>setSelectedChannels([]),\n                                                                                            children: \"Clear Selection\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 862,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            colorScheme: \"red\",\n                                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 872,\n                                                                                                columnNumber: 43\n                                                                                            }, void 0),\n                                                                                            onClick: handleBulkDeleteChannels,\n                                                                                            isLoading: bulkDeleting,\n                                                                                            isDisabled: isBulkRateLimited,\n                                                                                            children: \"Delete Selected\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 869,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 861,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 857,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 838,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 837,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                children: channelsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 3,\n                                                                    children: [\n                                                                        ...Array(5)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                                            height: \"50px\"\n                                                                        }, i, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 888,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 25\n                                                                }, this) : channels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                    color: \"gray.500\",\n                                                                    textAlign: \"center\",\n                                                                    py: 8,\n                                                                    children: \"No channels found\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 892,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                                    overflowX: \"auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                                                        variant: \"simple\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Thead, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                isChecked: selectedChannels.length === channels.length && channels.length > 0,\n                                                                                                isIndeterminate: selectedChannels.length > 0 && selectedChannels.length < channels.length,\n                                                                                                onChange: selectAllChannels\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 901,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 900,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 907,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Type\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 908,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Category\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 909,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Position\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 910,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Actions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 911,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 899,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 898,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tbody, {\n                                                                                children: (channels || []).map((channel)=>{\n                                                                                    const typeConfig = CHANNEL_TYPE_CONFIG[channel.type] || {\n                                                                                        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare,\n                                                                                        color: 'gray',\n                                                                                        label: 'Other'\n                                                                                    };\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                    isChecked: selectedChannels.includes(channel.id),\n                                                                                                    onChange: ()=>toggleChannelSelection(channel.id)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 920,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 919,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                                                            as: typeConfig.icon,\n                                                                                                            color: \"\".concat(typeConfig.color, \".400\")\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 927,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                            children: channel.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 931,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 926,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 925,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                    colorScheme: typeConfig.color,\n                                                                                                    children: typeConfig.label\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 935,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 934,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                    color: \"gray.500\",\n                                                                                                    children: getParentName(channel.parent_id)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 938,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 937,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                    color: \"gray.500\",\n                                                                                                    children: channel.position\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 941,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 940,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Edit Channel\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Edit channel\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiEdit2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 948,\n                                                                                                                    columnNumber: 51\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"blue\",\n                                                                                                                onClick: ()=>handleChannelEdit(channel),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 946,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 945,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Delete Channel\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Delete channel\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 959,\n                                                                                                                    columnNumber: 51\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"red\",\n                                                                                                                onClick: ()=>handleChannelDelete(channel.id),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 957,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 956,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 944,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 943,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, channel.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 918,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 914,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 897,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 896,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 836,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"\\uD83C\\uDFA8 Theme Builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Create and customize your own themes with the advanced color builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 986,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Custom Theme Builder\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 994,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 993,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Create your own custom themes with full color control\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 998,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPalette, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1002,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"purple\",\n                                                                                    onClick: onColorBuilderOpen,\n                                                                                    size: \"lg\",\n                                                                                    children: \"Open Color Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1001,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 997,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 996,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 992,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Theme Presets\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1015,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1014,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 3,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    mb: 2,\n                                                                                    children: \"Quick theme options available in the navigation bar\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1019,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    wrap: \"wrap\",\n                                                                                    spacing: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"blue\",\n                                                                                            children: \"Dark\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1023,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"purple\",\n                                                                                            children: \"Midnight\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1024,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"green\",\n                                                                                            children: \"Forest\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1025,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"orange\",\n                                                                                            children: \"Sunset\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1026,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"pink\",\n                                                                                            children: \"Rose\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1027,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1022,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1018,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1017,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1013,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 991,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 983,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"\\uD83D\\uDEE0️ Builders & Tools\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1040,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Create custom content and manage server features with powerful builders\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1041,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Content Builders\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1049,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1048,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1054,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"green\",\n                                                                                    onClick: ()=>window.open('/admin/experimental/addon-builder', '_blank'),\n                                                                                    children: \"Addon Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1053,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1061,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"blue\",\n                                                                                    onClick: ()=>window.open('/admin/applications-builder', '_blank'),\n                                                                                    children: \"Applications Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1060,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1068,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"purple\",\n                                                                                    onClick: ()=>window.open('/admin/embed-builder', '_blank'),\n                                                                                    isDisabled: true,\n                                                                                    children: \"Message Builder (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1067,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1052,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1051,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1047,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Management Tools\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1081,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1080,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1086,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"orange\",\n                                                                                    onClick: ()=>window.open('/admin/addons', '_blank'),\n                                                                                    children: \"Manage Addons\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1085,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1093,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"teal\",\n                                                                                    onClick: ()=>window.open('/admin/commands', '_blank'),\n                                                                                    children: \"Command Manager\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1092,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1100,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"cyan\",\n                                                                                    onClick: ()=>window.open('/admin/applications', '_blank'),\n                                                                                    children: \"Application Manager\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1099,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1084,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1083,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1079,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 1037,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"⚡ Automation & Activities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1117,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Set up automated features and server activities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1118,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Activity Templates\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1126,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1125,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                color: \"gray.500\",\n                                                                                fontSize: \"sm\",\n                                                                                mb: 4,\n                                                                                children: \"Pre-built activity templates to get you started quickly:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 1129,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                                spacing: 2,\n                                                                                align: \"stretch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Event Management System\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1133,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Welcome & Onboarding Flow\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1134,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Moderation Automation\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1135,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Custom Commands\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1136,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Auto-Role Assignment\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1137,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Scheduled Messages\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1138,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 1132,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1128,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1124,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Automation Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1145,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1144,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Configure automated server features\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1149,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1153,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"yellow\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Auto-Moderation (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1152,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1161,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"green\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Welcome System (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1160,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1169,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"blue\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Event Scheduler (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1168,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1148,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1147,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1143,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 1115,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 1114,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1187,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateChannelDialog, {\n                        isOpen: isCreateChannelOpen,\n                        onClose: onCreateChannelClose,\n                        onSuccess: fetchChannels\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1188,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1187,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1195,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditChannelDialog, {\n                        isOpen: isEditChannelOpen,\n                        onClose: onEditChannelClose,\n                        channel: selectedChannel,\n                        categories: channels.filter((c)=>c.type === 4),\n                        onSuccess: fetchChannels\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1196,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1205,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateRoleDialog, {\n                        isOpen: isCreateRoleOpen,\n                        onClose: onCreateRoleClose,\n                        onSuccess: fetchGuildData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1206,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1205,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1213,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditRoleDialog, {\n                        isOpen: isEditRoleOpen,\n                        onClose: onEditRoleClose,\n                        role: selectedRole,\n                        onSuccess: fetchGuildData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1214,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1213,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1222,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColorBuilder, {\n                        isOpen: isColorBuilderOpen,\n                        onClose: onColorBuilderClose\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1223,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1222,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 569,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n        lineNumber: 568,\n        columnNumber: 5\n    }, this);\n}\n_s1(ServerManagement, \"dC2cBLbdfeKlXY+f/bPPmk9+OmY=\", false, function() {\n    return [\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        useRateLimit,\n        useRateLimit,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure\n    ];\n});\n_c5 = ServerManagement;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"CreateChannelDialog\");\n$RefreshReg$(_c1, \"EditChannelDialog\");\n$RefreshReg$(_c2, \"EditRoleDialog\");\n$RefreshReg$(_c3, \"ColorBuilder\");\n$RefreshReg$(_c4, \"CreateRoleDialog\");\n$RefreshReg$(_c5, \"ServerManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/admin/guilds.tsx\n"));

/***/ })

});