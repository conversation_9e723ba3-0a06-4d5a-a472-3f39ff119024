"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9196],{39196:(e,s,n)=>{n.r(s),n.d(s,{default:()=>w});var t=n(94513),i=n(64349),r=n(31862),a=n(15975),l=n(59220),c=n(46949),d=n(81139),h=n(95066),o=n(53083),p=n(24490),x=n(31840),j=n(29607),m=n(35624),u=n(35339),C=n(55206),g=n(3037),_=n(7836),f=n(94285);let L={GUILD_TEXT:0,GUILD_VOICE:2,GUILD_CATEGORY:4};function w(e){let{isOpen:s,onClose:n,onSuccess:w,channel:y,categories:v}=e,E=(0,_.d)(),[I,b]=(0,f.useState)(!1),[U,O]=(0,f.useState)({name:"",type:0,topic:"",nsfw:!1,bitrate:64e3,userLimit:0,parent:"",rateLimitPerUser:0});(0,f.useEffect)(()=>{y&&O({name:y.name||"",type:y.raw_type||0,topic:y.topic||"",nsfw:y.nsfw||!1,bitrate:y.bitrate||64e3,userLimit:y.user_limit||0,parent:y.parent_id||"",rateLimitPerUser:y.rate_limit_per_user||0})},[y]);let T=async()=>{b(!0);try{let e=await fetch("/api/discord/channels/".concat(y.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:U.name,topic:U.topic,nsfw:U.nsfw,bitrate:U.type===L.GUILD_VOICE?U.bitrate:void 0,user_limit:U.type===L.GUILD_VOICE?U.userLimit:void 0,parent_id:U.parent||null,rate_limit_per_user:U.type===L.GUILD_TEXT?U.rateLimitPerUser:void 0})});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to update channel")}E({title:"Success",description:"Channel updated successfully",status:"success",duration:3e3}),w(),n()}catch(e){E({title:"Error",description:e.message||"Failed to update channel",status:"error",duration:5e3})}finally{b(!1)}},k=(e,s)=>{O(n=>({...n,[e]:s}))};return(0,t.jsxs)(c.aF,{isOpen:s,onClose:n,size:"xl",children:[(0,t.jsx)(j.m,{backdropFilter:"blur(10px)"}),(0,t.jsxs)(o.$,{bg:"gray.800",children:[(0,t.jsx)(x.r,{children:"Edit Channel"}),(0,t.jsx)(h.s,{}),(0,t.jsx)(d.c,{children:(0,t.jsxs)(g.T,{spacing:4,children:[(0,t.jsxs)(r.MJ,{children:[(0,t.jsx)(a.l,{children:"Channel Name"}),(0,t.jsx)(l.p,{placeholder:"Enter channel name",value:U.name,onChange:e=>k("name",e.target.value)})]}),U.type===L.GUILD_TEXT&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(r.MJ,{children:[(0,t.jsx)(a.l,{children:"Channel Topic"}),(0,t.jsx)(l.p,{placeholder:"Enter channel topic",value:U.topic,onChange:e=>k("topic",e.target.value)})]}),(0,t.jsxs)(r.MJ,{children:[(0,t.jsx)(a.l,{children:"Slowmode (seconds)"}),(0,t.jsxs)(m.Q7,{min:0,max:21600,value:U.rateLimitPerUser,onChange:e=>k("rateLimitPerUser",parseInt(e)),children:[(0,t.jsx)(m.OO,{}),(0,t.jsxs)(m.lw,{children:[(0,t.jsx)(m.Q0,{}),(0,t.jsx)(m.Sh,{})]})]})]}),(0,t.jsxs)(r.MJ,{display:"flex",alignItems:"center",children:[(0,t.jsx)(a.l,{htmlFor:"nsfw",mb:"0",children:"Age-Restricted (NSFW)"}),(0,t.jsx)(C.d,{id:"nsfw",isChecked:U.nsfw,onChange:e=>k("nsfw",e.target.checked)})]})]}),U.type===L.GUILD_VOICE&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(r.MJ,{children:[(0,t.jsx)(a.l,{children:"Bitrate (kbps)"}),(0,t.jsxs)(m.Q7,{min:8,max:96,value:U.bitrate/1e3,onChange:e=>k("bitrate",1e3*parseInt(e)),children:[(0,t.jsx)(m.OO,{}),(0,t.jsxs)(m.lw,{children:[(0,t.jsx)(m.Q0,{}),(0,t.jsx)(m.Sh,{})]})]})]}),(0,t.jsxs)(r.MJ,{children:[(0,t.jsx)(a.l,{children:"User Limit"}),(0,t.jsxs)(m.Q7,{min:0,max:99,value:U.userLimit,onChange:e=>k("userLimit",parseInt(e)),children:[(0,t.jsx)(m.OO,{}),(0,t.jsxs)(m.lw,{children:[(0,t.jsx)(m.Q0,{}),(0,t.jsx)(m.Sh,{})]})]})]})]}),U.type!==L.GUILD_CATEGORY&&(0,t.jsxs)(r.MJ,{children:[(0,t.jsx)(a.l,{children:"Parent Category"}),(0,t.jsxs)(u.l,{value:U.parent,onChange:e=>k("parent",e.target.value),children:[(0,t.jsx)("option",{value:"",children:"None"}),v.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]})}),(0,t.jsxs)(p.j,{children:[(0,t.jsx)(i.$,{variant:"ghost",mr:3,onClick:n,children:"Cancel"}),(0,t.jsx)(i.$,{colorScheme:"blue",onClick:T,isLoading:I,children:"Save Changes"})]})]})]})}}}]);