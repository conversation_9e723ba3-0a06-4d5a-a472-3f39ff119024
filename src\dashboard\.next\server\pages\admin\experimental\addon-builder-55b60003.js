"use strict";exports.id=2437,exports.ids=[2437],exports.modules={68490:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.d(r,{A:()=>h});var n=o(8732),i=o(82015),s=o(66909),a=o(29409),l=o(16589),c=o(3001),d=e([s,a,c]);[s,a,c]=d.then?(await d)():d;let m=(0,i.memo)(({data:e,selected:r})=>{let{currentScheme:o}=(0,c.DP)();return(0,n.jsxs)(a.az,{bg:o.colors.surface,border:`2px solid ${r?o.colors.primary:o.colors.border}`,borderRadius:"full",p:2,minW:"80px",minH:"80px",boxShadow:"lg",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",_hover:{boxShadow:"xl",transform:"scale(1.05)",borderColor:o.colors.primary},transition:"all 0.2s",children:[(0,n.jsxs)(a.Tk,{spacing:1,align:"center",children:[(0,n.jsx)(a.az,{bg:o.colors.primary,color:"white",borderRadius:"full",p:1,fontSize:"sm",boxShadow:"sm",children:(0,n.jsx)(l.aze,{})}),(0,n.jsx)(a.EY,{fontSize:"xs",fontWeight:"bold",color:o.colors.text,textAlign:"center",children:e.label})]}),(0,n.jsx)(s.Handle,{type:"source",position:s.Position.Bottom,style:{background:o.colors.background,border:`2px solid ${o.colors.primary}`,width:"16px",height:"16px",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",bottom:"-8px",left:"50%",transform:"translateX(-50%)"}})]})});m.displayName="TriggerNode";let h=m;t()}catch(e){t(e)}})},70318:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.d(r,{A:()=>g});var n=o(8732),i=o(82015),s=o(66909),a=o(64299),l=o(80169),c=o(3001),d=e([s,a,c]);[s,a,c]=d.then?(await d)():d;let m={event:[{name:"{event.type}",description:"Type of event that triggered",icon:"\uD83D\uDCE1"},{name:"{event.timestamp}",description:"When the event occurred",icon:"⏰"},{name:"{event.guild}",description:"Server where event occurred",icon:"\uD83C\uDFE0"},{name:"{event.channel}",description:"Channel where event occurred",icon:"\uD83D\uDCFA"},{name:"{event.user}",description:"User who triggered the event",icon:"\uD83D\uDC64"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83C\uDD94"},{name:"{message.content}",description:"Message content",icon:"\uD83D\uDCAC"},{name:"{message.author}",description:"Message author",icon:"\uD83D\uDC64"},{name:"{message.channel}",description:"Message channel",icon:"\uD83D\uDCFA"},{name:"{message.createdAt}",description:"Message creation time",icon:"\uD83D\uDCC5"},{name:"{message.editedAt}",description:"Message edit time",icon:"✏️"},{name:"{message.attachments}",description:"Message attachments",icon:"\uD83D\uDCCE"},{name:"{message.embeds}",description:"Message embeds",icon:"\uD83D\uDCCB"},{name:"{message.reactions}",description:"Message reactions",icon:"\uD83D\uDC4D"},{name:"{message.mentions}",description:"Message mentions",icon:"\uD83D\uDCE2"}],member:[{name:"{member.id}",description:"Member ID",icon:"\uD83C\uDD94"},{name:"{member.username}",description:"Member username",icon:"\uD83D\uDC64"},{name:"{member.displayName}",description:"Member display name",icon:"\uD83D\uDCDD"},{name:"{member.tag}",description:"Member tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{member.mention}",description:"Member mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{member.avatar}",description:"Member avatar URL",icon:"\uD83D\uDDBC️"},{name:"{member.joinedAt}",description:"Server join date",icon:"\uD83D\uDEAA"},{name:"{member.roles}",description:"Member roles",icon:"\uD83C\uDFAD"},{name:"{member.permissions}",description:"Member permissions",icon:"\uD83D\uDD10"},{name:"{member.isBot}",description:"Is member a bot",icon:"\uD83E\uDD16"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member count",icon:"\uD83D\uDC65"},{name:"{channel.position}",description:"Channel position",icon:"\uD83D\uDCCD"},{name:"{channel.nsfw}",description:"Is NSFW channel",icon:"\uD83D\uDD1E"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Total member count",icon:"\uD83D\uDC65"},{name:"{server.owner}",description:"Server owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server boost level",icon:"\uD83D\uDE80"},{name:"{server.boostCount}",description:"Server boost count",icon:"\uD83D\uDC8E"},{name:"{server.createdAt}",description:"Server creation date",icon:"\uD83D\uDCC5"}],reaction:[{name:"{reaction.emoji}",description:"Reaction emoji",icon:"\uD83D\uDE00"},{name:"{reaction.count}",description:"Reaction count",icon:"\uD83D\uDD22"},{name:"{reaction.users}",description:"Users who reacted",icon:"\uD83D\uDC65"},{name:"{reaction.me}",description:"Bot reacted",icon:"\uD83E\uDD16"}],voice:[{name:"{voice.channelId}",description:"Voice channel ID",icon:"\uD83D\uDD0A"},{name:"{voice.channelName}",description:"Voice channel name",icon:"\uD83D\uDD0A"},{name:"{voice.memberCount}",description:"Voice channel member count",icon:"\uD83D\uDC65"},{name:"{voice.muted}",description:"Is member muted",icon:"\uD83D\uDD07"},{name:"{voice.deafened}",description:"Is member deafened",icon:"\uD83D\uDD07"},{name:"{voice.streaming}",description:"Is member streaming",icon:"\uD83D\uDCFA"},{name:"{voice.camera}",description:"Is member using camera",icon:"\uD83D\uDCF9"}],role:[{name:"{role.id}",description:"Role ID",icon:"\uD83C\uDD94"},{name:"{role.name}",description:"Role name",icon:"\uD83C\uDFAD"},{name:"{role.mention}",description:"Role mention (<@&id>)",icon:"\uD83D\uDCE2"},{name:"{role.color}",description:"Role color",icon:"\uD83C\uDFA8"},{name:"{role.position}",description:"Role position",icon:"\uD83D\uDCCD"},{name:"{role.permissions}",description:"Role permissions",icon:"\uD83D\uDD10"},{name:"{role.mentionable}",description:"Is role mentionable",icon:"\uD83D\uDCE2"},{name:"{role.hoisted}",description:"Is role hoisted",icon:"\uD83D\uDCCC"}]},h=[{value:"messageCreate",label:"\uD83D\uDCAC Message Created",category:"Messages",description:"When a new message is sent"},{value:"messageUpdate",label:"✏️ Message Edited",category:"Messages",description:"When a message is edited"},{value:"messageDelete",label:"\uD83D\uDDD1️ Message Deleted",category:"Messages",description:"When a message is deleted"},{value:"messageReactionAdd",label:"\uD83D\uDC4D Reaction Added",category:"Messages",description:"When a reaction is added to a message"},{value:"messageReactionRemove",label:"\uD83D\uDC4E Reaction Removed",category:"Messages",description:"When a reaction is removed from a message"},{value:"messageReactionRemoveAll",label:"\uD83E\uDDF9 All Reactions Removed",category:"Messages",description:"When all reactions are removed from a message"},{value:"guildMemberAdd",label:"\uD83D\uDEAA Member Joined",category:"Members",description:"When a new member joins the server"},{value:"guildMemberRemove",label:"\uD83D\uDC4B Member Left",category:"Members",description:"When a member leaves the server"},{value:"guildMemberUpdate",label:"\uD83D\uDC64 Member Updated",category:"Members",description:"When member info changes (roles, nickname, etc.)"},{value:"userUpdate",label:"\uD83D\uDCDD User Updated",category:"Members",description:"When user profile changes (avatar, username, etc.)"},{value:"presenceUpdate",label:"\uD83D\uDFE2 Presence Updated",category:"Members",description:"When member status/activity changes"},{value:"guildBanAdd",label:"\uD83D\uDD28 Member Banned",category:"Moderation",description:"When a member is banned"},{value:"guildBanRemove",label:"\uD83D\uDD13 Member Unbanned",category:"Moderation",description:"When a member is unbanned"},{value:"messageDeleteBulk",label:"\uD83E\uDDF9 Bulk Message Delete",category:"Moderation",description:"When multiple messages are deleted at once"},{value:"voiceStateUpdate",label:"\uD83D\uDD0A Voice State Changed",category:"Voice",description:"When member joins/leaves/mutes in voice"},{value:"channelCreate",label:"\uD83D\uDCFA Channel Created",category:"Channels",description:"When a new channel is created"},{value:"channelDelete",label:"\uD83D\uDDD1️ Channel Deleted",category:"Channels",description:"When a channel is deleted"},{value:"channelUpdate",label:"⚙️ Channel Updated",category:"Channels",description:"When channel settings change"},{value:"channelPinsUpdate",label:"\uD83D\uDCCC Channel Pins Updated",category:"Channels",description:"When pinned messages change"},{value:"roleCreate",label:"\uD83C\uDFAD Role Created",category:"Roles",description:"When a new role is created"},{value:"roleDelete",label:"\uD83D\uDDD1️ Role Deleted",category:"Roles",description:"When a role is deleted"},{value:"roleUpdate",label:"⚙️ Role Updated",category:"Roles",description:"When role settings change"},{value:"threadCreate",label:"\uD83E\uDDF5 Thread Created",category:"Threads",description:"When a thread is created"},{value:"threadDelete",label:"\uD83D\uDDD1️ Thread Deleted",category:"Threads",description:"When a thread is deleted"},{value:"threadUpdate",label:"⚙️ Thread Updated",category:"Threads",description:"When thread settings change"},{value:"threadMemberUpdate",label:"\uD83D\uDC64 Thread Member Update",category:"Threads",description:"When someone joins/leaves a thread"},{value:"interactionCreate",label:"\uD83C\uDF9B️ Interaction Created",category:"Interactions",description:"When buttons/selects are used"},{value:"applicationCommandPermissionsUpdate",label:"\uD83D\uDD10 Command Permissions Updated",category:"Interactions",description:"When command permissions change"},{value:"guildUpdate",label:"\uD83C\uDFE0 Server Updated",category:"Server",description:"When server settings change"},{value:"guildUnavailable",label:"⚠️ Server Unavailable",category:"Server",description:"When server becomes unavailable"},{value:"guildIntegrationsUpdate",label:"\uD83D\uDD17 Integrations Updated",category:"Server",description:"When server integrations change"},{value:"inviteCreate",label:"\uD83D\uDD17 Invite Created",category:"Server",description:"When an invite is created"},{value:"inviteDelete",label:"\uD83D\uDDD1️ Invite Deleted",category:"Server",description:"When an invite is deleted"},{value:"emojiCreate",label:"\uD83D\uDE00 Emoji Created",category:"Server",description:"When a custom emoji is added"},{value:"emojiDelete",label:"\uD83D\uDDD1️ Emoji Deleted",category:"Server",description:"When a custom emoji is removed"},{value:"emojiUpdate",label:"⚙️ Emoji Updated",category:"Server",description:"When a custom emoji is modified"},{value:"stickerCreate",label:"\uD83C\uDFF7️ Sticker Created",category:"Server",description:"When a custom sticker is added"},{value:"stickerDelete",label:"\uD83D\uDDD1️ Sticker Deleted",category:"Server",description:"When a custom sticker is removed"},{value:"stickerUpdate",label:"⚙️ Sticker Updated",category:"Server",description:"When a custom sticker is modified"}],p=[{value:"channel",label:"\uD83D\uDCFA Channel Filter",description:"Filter by specific channels"},{value:"role",label:"\uD83C\uDFAD Role Filter",description:"Filter by user roles"},{value:"user",label:"\uD83D\uDC64 User Filter",description:"Filter by specific users"},{value:"regex",label:"\uD83D\uDD0D Regex Pattern",description:"Filter using regular expressions"},{value:"cooldown",label:"⏰ Cooldown",description:"Rate limit event triggers"},{value:"permission",label:"\uD83D\uDD10 Permission",description:"Filter by user permissions"},{value:"content",label:"\uD83D\uDCAC Content Filter",description:"Filter by message content"},{value:"custom",label:"⚙️ Custom",description:"Custom filter condition"}],x=(0,i.memo)(({data:e,selected:r,id:o,updateNodeData:t})=>{let{currentScheme:d}=(0,c.DP)(),{isOpen:x,onOpen:g,onClose:b}=(0,a.useDisclosure)(),[u,v]=(0,i.useState)(()=>({ignoreBot:!0,ignoreSystem:!0,rateLimited:!1,rateLimit:1e3,priority:1,async:!1,retryOnError:!1,maxRetries:3,filters:[],channelRestrictions:[],roleRestrictions:[],...e})),[j,S]=(0,i.useState)(!1),y=e=>{v(r=>({...r,...e}))},f=e=>{let r=h.find(r=>r.value===e);return r?r.label.split(" ").slice(1).join(" "):e},C=(e,r)=>{let o=[...u.filters||[]];o[e]={...o[e],...r},y({filters:o})},k=e=>{let r=(u.filters||[]).filter((r,o)=>o!==e);y({filters:r})},T=e=>{navigator.clipboard.writeText(e)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(a.Box,{bg:d.colors.surface,border:`2px solid ${r?"#10b981":d.colors.border}`,borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,n.jsx)(s.Handle,{type:"target",position:s.Position.Top,style:{background:"#10b981",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,n.jsxs)(a.VStack,{spacing:1,align:"stretch",children:[(0,n.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,n.jsxs)(a.HStack,{spacing:1,children:[(0,n.jsx)(a.Box,{bg:"green.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,n.jsx)(l.DQs,{})}),(0,n.jsx)(a.Text,{fontSize:"xs",fontWeight:"bold",color:d.colors.text,children:"Event"})]}),(0,n.jsx)(a.IconButton,{icon:(0,n.jsx)(l.VSk,{}),size:"xs",variant:"ghost",onClick:g,"aria-label":"Configure event"})]}),(0,n.jsx)(a.Box,{children:(0,n.jsxs)(a.HStack,{spacing:1,children:[u.eventType&&(0,n.jsx)(a.Text,{fontSize:"xs",children:(e=>{let r=h.find(r=>r.value===e);return r?r.label.split(" ")[0]:"\uD83D\uDCE1"})(u.eventType)}),(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.text,noOfLines:1,children:u.eventType?f(u.eventType):"Select Event"})]})}),u.description&&(0,n.jsx)(a.Box,{children:(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,noOfLines:1,children:u.description.length>25?u.description.substring(0,25)+"...":u.description})}),(0,n.jsxs)(a.HStack,{spacing:1,flexWrap:"wrap",children:[(u.filters?.length??0)>0&&(0,n.jsxs)(a.Badge,{size:"xs",colorScheme:"green",children:[u.filters?.length," filter",(u.filters?.length??0)!==1?"s":""]}),u.ignoreBot&&(0,n.jsx)(a.Badge,{size:"xs",colorScheme:"orange",children:"No Bots"}),u.rateLimited&&(0,n.jsx)(a.Badge,{size:"xs",colorScheme:"yellow",children:"Rate Limited"})]})]}),(0,n.jsx)(s.Handle,{type:"source",position:s.Position.Bottom,style:{background:"#10b981",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,n.jsxs)(a.Modal,{isOpen:x,onClose:()=>{t&&o&&t(o,u),b()},size:"4xl",children:[(0,n.jsx)(a.ModalOverlay,{bg:"blackAlpha.600"}),(0,n.jsxs)(a.ModalContent,{bg:d.colors.background,border:"2px solid",borderColor:"green.400",maxW:"1200px",children:[(0,n.jsx)(a.ModalHeader,{color:d.colors.text,children:"\uD83D\uDCE1 Configure Event"}),(0,n.jsx)(a.ModalCloseButton,{}),(0,n.jsx)(a.ModalBody,{pb:6,children:(0,n.jsxs)(a.VStack,{spacing:6,align:"stretch",children:[(0,n.jsxs)(a.Box,{children:[(0,n.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,n.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Available Variables"}),(0,n.jsxs)(a.Button,{size:"sm",variant:"ghost",leftIcon:j?(0,n.jsx)(l._NO,{}):(0,n.jsx)(l.Vap,{}),onClick:()=>S(!j),children:[j?"Hide":"Show"," Variables"]})]}),(0,n.jsxs)(a.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,n.jsx)(a.AlertIcon,{}),(0,n.jsx)(a.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your event responses! Click any variable below to copy it. Variables are replaced with actual values when your event triggers."})]}),(0,n.jsx)(a.Collapse,{in:j,animateOpacity:!0,children:(0,n.jsx)(a.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,n.jsx)(a.Accordion,{allowMultiple:!0,children:Object.entries(m).map(([e,r])=>(0,n.jsxs)(a.AccordionItem,{border:"none",children:[(0,n.jsxs)(a.AccordionButton,{px:0,py:2,children:[(0,n.jsx)(a.Box,{flex:"1",textAlign:"left",children:(0,n.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,textTransform:"capitalize",children:[e," Variables"]})}),(0,n.jsx)(a.AccordionIcon,{})]}),(0,n.jsx)(a.AccordionPanel,{px:0,py:2,children:(0,n.jsx)(a.VStack,{spacing:2,align:"stretch",children:r.map(e=>(0,n.jsxs)(a.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>T(e.name),children:[(0,n.jsx)(a.Text,{fontSize:"sm",children:e.icon}),(0,n.jsx)(a.Code,{fontSize:"xs",colorScheme:"green",children:e.name}),(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,flex:"1",children:e.description}),(0,n.jsx)(a.IconButton,{icon:(0,n.jsx)(l.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:r=>{r.stopPropagation(),T(e.name)}})]},e.name))})})]},e))})})})]}),(0,n.jsx)(a.Divider,{}),(0,n.jsxs)(a.Tabs,{variant:"enclosed",colorScheme:"green",children:[(0,n.jsxs)(a.TabList,{children:[(0,n.jsx)(a.Tab,{children:"Event Type"}),(0,n.jsx)(a.Tab,{children:"Filters"}),(0,n.jsx)(a.Tab,{children:"Settings"}),(0,n.jsx)(a.Tab,{children:"Advanced"})]}),(0,n.jsxs)(a.TabPanels,{children:[(0,n.jsx)(a.TabPanel,{children:(0,n.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(a.FormControl,{isRequired:!0,children:[(0,n.jsx)(a.FormLabel,{color:d.colors.text,children:"Event Type"}),(0,n.jsx)(a.Select,{value:u.eventType||"",onChange:e=>y({eventType:e.target.value}),placeholder:"Select an event type",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:Object.entries(h.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{})).map(([e,r])=>(0,n.jsx)("optgroup",{label:e,children:r.map(e=>(0,n.jsx)("option",{value:e.value,children:e.label},e.value))},e))})]}),u.eventType&&(0,n.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,n.jsx)(a.AlertIcon,{}),(0,n.jsxs)(a.Box,{children:[(0,n.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:h.find(e=>e.value===u.eventType)?.label}),(0,n.jsx)(a.Text,{fontSize:"sm",children:h.find(e=>e.value===u.eventType)?.description})]})]}),(0,n.jsxs)(a.FormControl,{children:[(0,n.jsx)(a.FormLabel,{color:d.colors.text,children:"Description"}),(0,n.jsx)(a.Textarea,{value:u.description||"",onChange:e=>y({description:e.target.value}),placeholder:"Describe when this event should trigger",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"})]})]})}),(0,n.jsx)(a.TabPanel,{children:(0,n.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,n.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Event Filters"}),(0,n.jsx)(a.Button,{leftIcon:(0,n.jsx)(l.GGD,{}),onClick:()=>{y({filters:[...u.filters||[],{type:"channel",value:"",operator:"equals"}]})},colorScheme:"green",size:"sm",children:"Add Filter"})]}),(0,n.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,n.jsx)(a.AlertIcon,{}),(0,n.jsx)(a.AlertDescription,{fontSize:"sm",children:"Filters determine when this event should trigger. Only events that pass all filters will execute the connected actions."})]}),(0,n.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[u.filters?.map((e,r)=>(0,n.jsxs)(a.Box,{p:4,bg:d.colors.surface,borderRadius:"md",border:"1px solid",borderColor:d.colors.border,children:[(0,n.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:3,children:[(0,n.jsxs)(a.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,children:["Filter ",r+1]}),(0,n.jsx)(a.IconButton,{icon:(0,n.jsx)(l.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>k(r),"aria-label":"Remove filter"})]}),(0,n.jsxs)(a.VStack,{spacing:3,align:"stretch",children:[(0,n.jsxs)(a.SimpleGrid,{columns:2,spacing:3,children:[(0,n.jsxs)(a.FormControl,{children:[(0,n.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Filter Type"}),(0,n.jsx)(a.Select,{value:e.type,onChange:e=>C(r,{type:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm",children:p.map(e=>(0,n.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,n.jsxs)(a.FormControl,{children:[(0,n.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Operator"}),(0,n.jsxs)(a.Select,{value:e.operator||"equals",onChange:e=>C(r,{operator:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm",children:[(0,n.jsx)("option",{value:"equals",children:"Equals"}),(0,n.jsx)("option",{value:"contains",children:"Contains"}),(0,n.jsx)("option",{value:"startsWith",children:"Starts With"}),(0,n.jsx)("option",{value:"endsWith",children:"Ends With"}),(0,n.jsx)("option",{value:"regex",children:"Regex"})]})]})]}),(0,n.jsxs)(a.FormControl,{children:[(0,n.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Filter Value"}),(0,n.jsx)(a.Input,{value:e.value,onChange:e=>C(r,{value:e.target.value}),placeholder:"channel"===e.type?"general or {channel.name}":"role"===e.type?"Member or {role.name}":"user"===e.type?"username or {user.id}":"regex"===e.type?"^Hello.*":"content"===e.type?"hello world":"Filter value",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]}),(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:p.find(r=>r.value===e.type)?.description})]})]},r)),(!u.filters||0===u.filters.length)&&(0,n.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,n.jsx)(a.AlertIcon,{}),(0,n.jsx)(a.AlertDescription,{children:"No filters configured. This event will trigger for all occurrences of the selected event type."})]})]})]})}),(0,n.jsx)(a.TabPanel,{children:(0,n.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,n.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Event Settings"}),(0,n.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(a.HStack,{spacing:4,children:[(0,n.jsx)(a.Switch,{isChecked:u.ignoreBot,onChange:e=>y({ignoreBot:e.target.checked}),colorScheme:"green"}),(0,n.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,n.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Ignore Bot Messages"}),(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Don't trigger on messages from bots (recommended)"})]})]}),(0,n.jsxs)(a.HStack,{spacing:4,children:[(0,n.jsx)(a.Switch,{isChecked:u.ignoreSystem,onChange:e=>y({ignoreSystem:e.target.checked}),colorScheme:"green"}),(0,n.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,n.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Ignore System Messages"}),(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Don't trigger on Discord system messages"})]})]}),(0,n.jsxs)(a.HStack,{spacing:4,children:[(0,n.jsx)(a.Switch,{isChecked:u.rateLimited,onChange:e=>y({rateLimited:e.target.checked}),colorScheme:"orange"}),(0,n.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,n.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Rate Limited"}),(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Limit how often this event can trigger"})]})]}),u.rateLimited&&(0,n.jsxs)(a.FormControl,{children:[(0,n.jsx)(a.FormLabel,{color:d.colors.text,children:"Rate Limit (milliseconds)"}),(0,n.jsxs)(a.NumberInput,{value:u.rateLimit||1e3,onChange:e=>y({rateLimit:parseInt(e)||1e3}),min:100,max:6e4,children:[(0,n.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,n.jsxs)(a.NumberInputStepper,{children:[(0,n.jsx)(a.NumberIncrementStepper,{}),(0,n.jsx)(a.NumberDecrementStepper,{})]})]}),(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"Minimum time between triggers (1000ms = 1 second)"})]})]})]})}),(0,n.jsx)(a.TabPanel,{children:(0,n.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,n.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Advanced Settings"}),(0,n.jsxs)(a.FormControl,{children:[(0,n.jsx)(a.FormLabel,{color:d.colors.text,children:"Event Priority"}),(0,n.jsxs)(a.NumberInput,{value:u.priority||1,onChange:e=>y({priority:parseInt(e)||1}),min:1,max:10,children:[(0,n.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,n.jsxs)(a.NumberInputStepper,{children:[(0,n.jsx)(a.NumberIncrementStepper,{}),(0,n.jsx)(a.NumberDecrementStepper,{})]})]}),(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"Higher priority events execute first (1 = highest, 10 = lowest)"})]}),(0,n.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(a.HStack,{spacing:4,children:[(0,n.jsx)(a.Switch,{isChecked:u.async,onChange:e=>y({async:e.target.checked}),colorScheme:"green"}),(0,n.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,n.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Async Processing"}),(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Don't wait for this event to complete before processing others"})]})]}),(0,n.jsxs)(a.HStack,{spacing:4,children:[(0,n.jsx)(a.Switch,{isChecked:u.retryOnError,onChange:e=>y({retryOnError:e.target.checked}),colorScheme:"red"}),(0,n.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,n.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Retry on Error"}),(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Automatically retry if event processing fails"})]})]}),u.retryOnError&&(0,n.jsxs)(a.FormControl,{children:[(0,n.jsx)(a.FormLabel,{color:d.colors.text,children:"Max Retries"}),(0,n.jsxs)(a.NumberInput,{value:u.maxRetries||3,onChange:e=>y({maxRetries:parseInt(e)||3}),min:1,max:10,children:[(0,n.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,n.jsxs)(a.NumberInputStepper,{children:[(0,n.jsx)(a.NumberIncrementStepper,{}),(0,n.jsx)(a.NumberDecrementStepper,{})]})]}),(0,n.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"Maximum number of retry attempts"})]})]})]})})]})]}),(0,n.jsx)(a.Button,{colorScheme:"green",onClick:()=>{e.eventType=u.eventType,e.description=u.description,e.filters=u.filters,e.ignoreBot=u.ignoreBot,e.ignoreSystem=u.ignoreSystem,e.rateLimited=u.rateLimited,e.rateLimit=u.rateLimit,e.priority=u.priority,e.async=u.async,e.retryOnError=u.retryOnError,e.maxRetries=u.maxRetries,e.channelRestrictions=u.channelRestrictions,e.roleRestrictions=u.roleRestrictions,e.label=u.eventType?f(u.eventType):"Event",b()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});x.displayName="EventNode";let g=x;t()}catch(e){t(e)}})}};