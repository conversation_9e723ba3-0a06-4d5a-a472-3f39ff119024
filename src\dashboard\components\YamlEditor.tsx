import React from 'react';
import { Box } from '@chakra-ui/react';
import Editor, { OnMount, loader } from '@monaco-editor/react';
import { configureMonacoYaml } from 'monaco-yaml';

interface YamlEditorProps {
  value: string;
  onChange: (value: string | undefined) => void;
  height?: string | number;
}

// URI for the model, which is used for validation and other language features
const modelUri = "file:///config.yaml";

// configure loader path to use CDN instead of local assets
loader.config({ paths: { vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs' } });

const YamlEditor: React.FC<YamlEditorProps> = ({ value, onChange, height = '60vh' }) => {

  const handleEditorDidMount: OnMount = (editor, monaco) => {
    // Provide custom workers to Monaco
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore – attach to global
    self.MonacoEnvironment = {
      getWorker: function (_: string, label: string) {
        if (label === 'yaml') {
          return new Worker('https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/base/worker/workerMain.js', { type: 'module' });
        }
        return new Worker('https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/base/worker/workerMain.js', { type: 'module' });
      },
    };

    // Basic setup
    monaco.languages.typescript.javascriptDefaults.setEagerModelSync(true);

    // Configure YAML language features
    configureMonacoYaml(monaco, {
      enableSchemaRequest: true,
      hover: true,
      completion: true,
      validate: true,
      format: true,
      schemas: [{
        // A dummy schema to get basic validation
        uri: 'http://json.schemastore.org/github-workflow',
        fileMatch: [modelUri]
      }]
    });
  };

  return (
    <Box
      borderWidth="1px"
      borderColor="purple.600"
      borderRadius="md"
      overflow="hidden"
      height={height}
    >
      <Editor
        height="100%"
        language="yaml"
        theme="vs-dark"
        path={modelUri}
        value={value}
        onChange={onChange}
        onMount={handleEditorDidMount}
        options={{
          minimap: { enabled: false },
          tabSize: 2,
          insertSpaces: true,
          wordWrap: 'on',
          automaticLayout: true,
          padding: {
            top: 10,
            bottom: 10
          },
        }}
      />
    </Box>
  );
};

export default YamlEditor; 