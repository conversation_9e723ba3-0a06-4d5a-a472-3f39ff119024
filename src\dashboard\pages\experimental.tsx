// @ts-nocheck
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Icon,
  SimpleGrid,
  Card,
  CardBody,
  Badge,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Divider,
} from '@chakra-ui/react';
import Layout from '../components/Layout';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './api/auth/[...nextauth]';
import { useRouter } from 'next/router';
import { FaFlask, FaRocket, FaCode, FaLock } from 'react-icons/fa';
import dynamic from 'next/dynamic';
import { Suspense } from 'react';

// Dynamic imports for heavy components
const ExperimentalFeatures = dynamic(() => import('../components/ExperimentalFeatures'), {
  loading: () => <Spinner size="lg" />,
  ssr: false
});

const useExperimentalFeatures = dynamic(() => import('../hooks/useExperimentalFeatures'), {
  ssr: false
});

interface ExperimentalFeature {
  id: string;
  name: string;
  description: string;
  icon: any;
  status: 'beta' | 'alpha' | 'stable';
  enabled: boolean;
  path: string;
}

const experimentalFeatures: ExperimentalFeature[] = [
  {
    id: 'addon-builder',
    name: 'Addon Builder',
    description: 'Create custom Discord bot addons with a visual interface',
    icon: FaCode,
    status: 'beta',
    enabled: true,
    path: '/experimental/addon-builder'
  }
];

function ExperimentalFeatureCard({ feature }: { feature: ExperimentalFeature }) {
  const router = useRouter();
  const { hasAccess } = useExperimentalFeatures();

  const statusColors = {
    alpha: 'red',
    beta: 'orange',
    stable: 'green'
  };

  const handleFeatureClick = () => {
    if (hasAccess && feature.enabled) {
      router.push(feature.path);
    }
  };

  return (
    <Card
      cursor={hasAccess && feature.enabled ? 'pointer' : 'not-allowed'}
      onClick={handleFeatureClick}
      opacity={hasAccess && feature.enabled ? 1 : 0.6}
      _hover={hasAccess && feature.enabled ? {
        transform: 'translateY(-2px)',
        shadow: 'xl'
      } : {}}
      transition="all 0.2s"
      border="1px solid"
      borderColor={hasAccess && feature.enabled ? 'transparent' : 'gray.300'}
      bg={hasAccess && feature.enabled ? 'white' : 'gray.50'}
    >
      <CardBody>
        <VStack align="start" spacing={3}>
          <HStack>
            <Icon as={feature.icon} boxSize={6} color="purple.500" />
            <VStack align="start" spacing={0} flex={1}>
              <HStack>
                <Text fontWeight="bold" fontSize="lg">
                  {feature.name}
                </Text>
                <Badge colorScheme={statusColors[feature.status]} size="sm">
                  {feature.status.toUpperCase()}
                </Badge>
              </HStack>
            </VStack>
            {!feature.enabled && (
              <Icon as={FaLock} boxSize={4} color="gray.400" />
            )}
          </HStack>
          
          <Text color="gray.600" fontSize="sm">
            {feature.description}
          </Text>
          
          <Button
            size="sm"
            colorScheme="purple"
            variant="outline"
            isDisabled={!hasAccess || !feature.enabled}
            leftIcon={<Icon as={FaRocket} />}
          >
            {!hasAccess ? 'Access Required' : !feature.enabled ? 'Coming Soon' : 'Try Now'}
          </Button>
        </VStack>
      </CardBody>
    </Card>
  );
}

export default function ExperimentalPage() {
  const { hasAccess, isLoading, reason } = useExperimentalFeatures();

  if (isLoading) {
    return (
      <Layout>
        <Container maxW="container.xl" py={8}>
          <VStack spacing={8}>
            <Spinner size="xl" />
            <Heading size="md">Loading experimental features...</Heading>
          </VStack>
        </Container>
      </Layout>
    );
  }

  if (!hasAccess) {
    return (
      <Layout>
        <Container maxW="container.xl" py={8}>
          <VStack spacing={8}>
            <Icon as={FaFlask} boxSize={16} color="purple.500" />
            <Heading size="lg" textAlign="center">
              ⚗️ Experimental Features
            </Heading>
            <Alert status="warning" borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertTitle>Access Required!</AlertTitle>
                <AlertDescription>
                  You need experimental features access to use these features.
                  {reason === 'no_access' && ' Please apply for experimental features access from the overview page.'}
                </AlertDescription>
              </Box>
            </Alert>
          </VStack>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxW="container.xl" py={8}>
        <VStack spacing={8} align="stretch">
          <Box textAlign="center">
            <Heading size="lg" mb={4}>
              ⚗️ Experimental Features
            </Heading>
            <Text color="gray.600" fontSize="lg">
              Cutting-edge features in development
            </Text>
          </Box>

          <Alert status="info" borderRadius="md">
            <AlertIcon />
            <Box>
              <AlertTitle>Beta Testing Program</AlertTitle>
              <AlertDescription>
                <strong>Welcome to Experimental Features!</strong> These features are in active development.
                Your feedback helps us improve them before general release.
              </AlertDescription>
            </Box>
          </Alert>

          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
            {experimentalFeatures.map((feature) => (
              <ExperimentalFeatureCard key={feature.id} feature={feature} />
            ))}
          </SimpleGrid>

          <Box>
            <Divider mb={4} />
            <Suspense fallback={<Spinner />}>
              <ExperimentalFeatures />
            </Suspense>
          </Box>

          <Alert status="warning" borderRadius="md">
            <AlertIcon />
            <AlertDescription>
              Experimental features may change or be removed without notice. Use at your own discretion.
            </AlertDescription>
          </Alert>
        </VStack>
      </Container>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);
  
  if (!session) {
    return {
      redirect: {
        destination: '/api/auth/signin?callbackUrl=%2Fexperimental',
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
}; 