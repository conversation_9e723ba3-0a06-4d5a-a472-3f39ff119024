"use strict";exports.id=823,exports.ids=[823],exports.modules={54984:(e,t,a)=>{a.a(e,async(e,o)=>{try{a.d(t,{a:()=>r.a});var r=a(45200),n=e([r]);r=(n.then?(await n)():n)[0],o()}catch(e){o(e)}})},70823:(e,t,a)=>{a.a(e,async(e,o)=>{try{a.r(t),a.d(t,{default:()=>h});var r=a(8732);a(82015);var n=a(54984),i=a(51859),s=a.n(i),d=a(54615),c=e([n,d]);[n,d]=c.then?(await c)():c;let l="file:///config.yaml";i.loader.config({paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}});let h=({value:e,onChange:t,height:a="60vh"})=>(0,r.jsx)(n.a,{borderWidth:"1px",borderColor:"purple.600",borderRadius:"md",overflow:"hidden",height:a,children:(0,r.jsx)(s(),{height:"100%",language:"yaml",theme:"vs-dark",path:l,value:e,onChange:t,onMount:(e,t)=>{self.MonacoEnvironment={getWorker:function(e,t){return new Worker("https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/base/worker/workerMain.js",{type:"module"})}},t.languages.typescript.javascriptDefaults.setEagerModelSync(!0),(0,d.configureMonacoYaml)(t,{enableSchemaRequest:!0,hover:!0,completion:!0,validate:!0,format:!0,schemas:[{uri:"http://json.schemastore.org/github-workflow",fileMatch:[l]}]})},options:{minimap:{enabled:!1},tabSize:2,insertSpaces:!0,wordWrap:"on",automaticLayout:!0,padding:{top:10,bottom:10}}})});o()}catch(e){o(e)}})}};