"use strict";(()=>{var e={};e.id=1298,e.ids=[1298],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},42125:(e,t,r)=>{r.r(t),r.d(t,{config:()=>x,default:()=>m,routeModule:()=>j});var a={};r.r(a),r.d(a,{default:()=>h});var n=r(93433),s=r(20264),i=r(20584),o=r(15806),d=r(94506),u=r(12518),l=r(98580);let p="933023999770918932",c=null,f=l.dashboardConfig.database?.url||"mongodb://localhost:27017",b=l.dashboardConfig.database?.name||"discord_bot";async function g(){return c||(c=await u.MongoClient.connect(f,{...l.dashboardConfig.database?.options||{}})),c.db(b)}async function h(e,t){try{let r=await (0,o.getServerSession)(e,t,d.authOptions);if(!r?.user?.id)return t.status(401).json({error:"Unauthorized"});if(!r.user?.isAdmin)return t.status(403).json({error:"Forbidden"});let a=await g();if("GET"===e.method){let{type:n}=e.query;if("experimental-settings"===n){if(r.user.id!==p)return t.status(403).json({error:"Forbidden"});let e=a.collection("experimental_settings"),n=await e.findOne({key:"applications_enabled"})||{enabled:!1};return t.status(200).json({enabled:n.enabled})}let s=a.collection("applications"),i=await s.find({}).toArray(),o={total:i.length,pending:i.filter(e=>"pending"===e.status).length,approved:i.filter(e=>"approved"===e.status).length,rejected:i.filter(e=>"rejected"===e.status).length,recentIncrease:0};return t.status(200).json({applications:i,stats:o})}if("POST"===e.method){let{type:n,...s}=e.body;if("experimental-settings"===n){if(r.user.id!==p)return t.status(403).json({error:"Forbidden"});let{enabled:e}=s;if("boolean"!=typeof e)return t.status(400).json({error:"Invalid request body"});let n=a.collection("experimental_settings");return await n.updateOne({key:"applications_enabled"},{$set:{enabled:e,updatedAt:new Date,updatedBy:r.user.id}},{upsert:!0}),t.status(200).json({enabled:e})}let i=a.collection("applications"),o={...s,createdAt:new Date,createdBy:r.user.id,status:"pending"},d=await i.insertOne(o);return t.status(201).json({id:d.insertedId,...o})}if("PATCH"===e.method){let{applicationId:n,action:s}=e.body;if(!n||!s)return t.status(400).json({error:"Missing applicationId or action"});let i=a.collection("applications"),o={status:"approve"===s?"approved":"rejected",updatedAt:new Date,updatedBy:r.user.id};return await i.updateOne({_id:n},{$set:o}),t.status(200).json({success:!0})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let m=(0,i.M)(a,"default"),x=(0,i.M)(a,"config"),j=new n.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/admin/applications",pathname:"/api/admin/applications",bundlePath:"",filename:""},userland:a})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(42125));module.exports=a})();