"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/applications";
exports.ids = ["pages/api/admin/applications"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications.ts&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications.ts&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_applications_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\applications.ts */ \"(api-node)/./pages/api/admin/applications.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_applications_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_applications_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/applications\",\n        pathname: \"/api/admin/applications\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_admin_applications_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/admin/applications.ts":
/*!*****************************************!*\
  !*** ./pages/api/admin/applications.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n\n\n\n\n// Hardcoded developer ID\nconst DEVELOPER_ID = '933023999770918932';\n// Reuse connection pattern\nlet cachedClient = null;\nconst mongoUrl = _core_config__WEBPACK_IMPORTED_MODULE_3__.dashboardConfig.database?.url || 'mongodb://localhost:27017';\nconst dbName = _core_config__WEBPACK_IMPORTED_MODULE_3__.dashboardConfig.database?.name || 'discord_bot';\nasync function getDb() {\n    if (!cachedClient) {\n        cachedClient = await mongodb__WEBPACK_IMPORTED_MODULE_2__.MongoClient.connect(mongoUrl, {\n            ..._core_config__WEBPACK_IMPORTED_MODULE_3__.dashboardConfig.database?.options || {}\n        });\n    }\n    return cachedClient.db(dbName);\n}\nasync function handler(req, res) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session?.user?.id) {\n            return res.status(401).json({\n                error: 'Unauthorized'\n            });\n        }\n        // Check if user is admin\n        const isAdmin = session.user?.isAdmin;\n        if (!isAdmin) {\n            return res.status(403).json({\n                error: 'Forbidden'\n            });\n        }\n        const db = await getDb();\n        if (req.method === 'GET') {\n            // Handle different query parameters\n            const { type } = req.query;\n            if (type === 'experimental-settings') {\n                // Only allow the developer to access experimental settings\n                if (session.user.id !== DEVELOPER_ID) {\n                    return res.status(403).json({\n                        error: 'Forbidden'\n                    });\n                }\n                const collection = db.collection('experimental_settings');\n                const settings = await collection.findOne({\n                    key: 'applications_enabled'\n                }) || {\n                    enabled: false\n                };\n                return res.status(200).json({\n                    enabled: settings.enabled\n                });\n            }\n            // Get regular applications\n            const applicationsCollection = db.collection('applications');\n            const applications = await applicationsCollection.find({}).toArray();\n            // Calculate stats\n            const stats = {\n                total: applications.length,\n                pending: applications.filter((app)=>app.status === 'pending').length,\n                approved: applications.filter((app)=>app.status === 'approved').length,\n                rejected: applications.filter((app)=>app.status === 'rejected').length,\n                recentIncrease: 0 // TODO: Calculate based on date comparison\n            };\n            return res.status(200).json({\n                applications,\n                stats\n            });\n        }\n        if (req.method === 'POST') {\n            const { type, ...data } = req.body;\n            if (type === 'experimental-settings') {\n                // Only allow the developer to modify experimental settings\n                if (session.user.id !== DEVELOPER_ID) {\n                    return res.status(403).json({\n                        error: 'Forbidden'\n                    });\n                }\n                const { enabled } = data;\n                if (typeof enabled !== 'boolean') {\n                    return res.status(400).json({\n                        error: 'Invalid request body'\n                    });\n                }\n                const collection = db.collection('experimental_settings');\n                await collection.updateOne({\n                    key: 'applications_enabled'\n                }, {\n                    $set: {\n                        enabled,\n                        updatedAt: new Date(),\n                        updatedBy: session.user.id\n                    }\n                }, {\n                    upsert: true\n                });\n                return res.status(200).json({\n                    enabled\n                });\n            }\n            // Handle regular application creation\n            const applicationsCollection = db.collection('applications');\n            const newApplication = {\n                ...data,\n                createdAt: new Date(),\n                createdBy: session.user.id,\n                status: 'pending'\n            };\n            const result = await applicationsCollection.insertOne(newApplication);\n            return res.status(201).json({\n                id: result.insertedId,\n                ...newApplication\n            });\n        }\n        if (req.method === 'PATCH') {\n            // Handle application status updates\n            const { applicationId, action } = req.body;\n            if (!applicationId || !action) {\n                return res.status(400).json({\n                    error: 'Missing applicationId or action'\n                });\n            }\n            const applicationsCollection = db.collection('applications');\n            const updateData = {\n                status: action === 'approve' ? 'approved' : 'rejected',\n                updatedAt: new Date(),\n                updatedBy: session.user.id\n            };\n            await applicationsCollection.updateOne({\n                _id: applicationId\n            }, {\n                $set: updateData\n            });\n            return res.status(200).json({\n                success: true\n            });\n        }\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    } catch (error) {\n        console.error('Error in applications API:', error);\n        return res.status(500).json({\n            error: 'Internal server error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/admin/applications.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();