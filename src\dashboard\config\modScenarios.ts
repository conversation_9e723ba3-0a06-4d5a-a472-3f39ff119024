import { ModScenario } from '../types/applications';

export const MOD_SCENARIOS: ModScenario[] = [
  {
    id: 1,
    scenario: "A user is repeatedly posting invite links to other Discord servers in general chat, despite being warned by other members.",
    context: "Server Rules: No advertising or self-promotion without permission.",
  },
  {
    id: 2,
    scenario: "Two users are having a heated argument about politics in the gaming channel, using increasingly hostile language.",
    context: "Server Rules: Keep discussions on-topic, no political discussions, maintain respectful communication.",
  },
  {
    id: 3,
    scenario: "A member reports that another user is sending them unwanted DMs with inappropriate content.",
    context: "Server Rules: No harassment, respect privacy, no NSFW content.",
  },
  {
    id: 4,
    scenario: "A user is spamming emojis and text across multiple channels simultaneously.",
    context: "Server Rules: No spamming, maintain channel cleanliness.",
  },
  {
    id: 5,
    scenario: "A well-known member is caught using racial slurs in voice chat.",
    context: "Server Rules: Zero tolerance for hate speech and discrimination.",
  },
  {
    id: 6,
    scenario: "A user is sharing what appears to be leaked personal information about another member.",
    context: "Server Rules: No doxxing, respect privacy, protect personal information.",
  },
  {
    id: 7,
    scenario: "Multiple users are organizing a raid on another Discord server.",
    context: "Server Rules: No organizing or participating in raids, maintain good relations with other communities.",
  },
  {
    id: 8,
    scenario: "A user is repeatedly asking for free items/currency in the trading channel.",
    context: "Server Rules: No begging, follow trading channel guidelines.",
  },
  {
    id: 9,
    scenario: "A member is posting links to suspicious websites claiming to offer free Discord Nitro.",
    context: "Server Rules: No scam links, protect community safety.",
  },
  {
    id: 10,
    scenario: "A user is using alternate accounts to bypass a temporary mute.",
    context: "Server Rules: No ban/mute evasion, respect moderator actions.",
  },
  {
    id: 11,
    scenario: "Several users are sharing memes with subtle but inappropriate sexual references in the general chat.",
    context: "Server Rules: Keep content family-friendly, no NSFW content or innuendos.",
  },
  {
    id: 12,
    scenario: "A user is repeatedly mentioning everyone in non-emergency situations.",
    context: "Server Rules: Don't abuse mentions, respect notification settings.",
  },
  {
    id: 13,
    scenario: "A member is threatening self-harm in a public channel.",
    context: "Server Rules: Take mental health concerns seriously, have protocol for crisis situations.",
  },
  {
    id: 14,
    scenario: "Users are sharing copyrighted content (movies/games) in the media channel.",
    context: "Server Rules: No piracy, respect intellectual property rights.",
  },
  {
    id: 15,
    scenario: "A user is roleplaying inappropriately in serious discussion channels.",
    context: "Server Rules: Keep roleplay in designated channels, respect channel purposes.",
  }
]; 