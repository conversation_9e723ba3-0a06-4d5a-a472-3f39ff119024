"use strict";exports.id=2457,exports.ids=[2457],exports.modules={479:(e,t,n)=>{n.d(t,{j:()=>u});var r=n(18337),o=n(82015);function u(e={}){let{onClose:t,onOpen:n,isOpen:a,id:i}=e,c=(0,r.c)(n),s=(0,r.c)(t),[l,f]=(0,o.useState)(e.defaultIsOpen||!1),d=void 0!==a?a:l,p=void 0!==a,m=(0,o.useId)(),b=i??`disclosure-${m}`,v=(0,o.useCallback)(()=>{p||f(!1),s?.()},[p,s]),E=(0,o.useCallback)(()=>{p||f(!0),c?.()},[p,c]),g=(0,o.useCallback)(()=>{d?v():E()},[d,E,v]);return{isOpen:d,onOpen:E,onClose:v,onToggle:g,isControlled:p,getButtonProps:function(e={}){return{...e,"aria-expanded":d,"aria-controls":b,onClick(t){e.onClick?.(t),g()}}},getDisclosureProps:function(e={}){return{...e,hidden:!d,id:b}}}}},12785:(e,t,n)=>{n.d(t,{Px:()=>T,vG:()=>i,c9:()=>u.c,ic:()=>c,I5:()=>s,j1:()=>p.j,ML:()=>a,Xb:()=>v,Sp:()=>S,wf:()=>g,cC:()=>x,$$:()=>L,SV:()=>j,jz:()=>M,ZC:()=>P,UQ:()=>m,Z3:()=>O,w5:()=>b});var r=n(13910),o=n(82015),u=n(18337);function a(e,t,n,r){let a=(0,u.c)(n);return(0,o.useEffect)(()=>{let o="function"==typeof e?e():e??document;if(n&&o)return o.addEventListener(t,a,r),()=>{o.removeEventListener(t,a,r)}},[t,e,r,a,n]),()=>{let n="function"==typeof e?e():e??document;n?.removeEventListener(t,a,r)}}function i(e){let{isOpen:t,ref:n}=e,[u,i]=(0,o.useState)(t),[c,s]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{c||(i(t),s(!0))},[t,c,u]),a(()=>n.current,"animationend",()=>{i(t)}),{present:!(!t&&!u),onComplete(){let e=new((0,r.mD)(n.current)).CustomEvent("animationend",{bubbles:!0});n.current?.dispatchEvent(e)}}}function c(e){let{value:t,defaultValue:n,onChange:r,shouldUpdate:a=(e,t)=>e!==t}=e,i=(0,u.c)(r),c=(0,u.c)(a),[s,l]=(0,o.useState)(n),f=void 0!==t,d=f?t:s,p=(0,u.c)(e=>{let t="function"==typeof e?e(d):e;c(d,t)&&(f||l(t),i(t))},[f,i,d,c]);return[d,p]}function s(e={}){let{onChange:t,precision:n,defaultValue:a,value:i,step:c=1,min:p=Number.MIN_SAFE_INTEGER,max:m=Number.MAX_SAFE_INTEGER,keepWithinRange:b=!0}=e,v=(0,u.c)(t),[E,g]=(0,o.useState)(()=>null==a?"":d(a,c,n)??""),w=void 0!==i,h=w?i:E,k=f(l(h),c),y=n??k,C=(0,o.useCallback)(e=>{e!==h&&(w||g(e.toString()),v?.(e.toString(),l(e)))},[v,w,h]),S=(0,o.useCallback)(e=>{let t=e;return b&&(t=(0,r.L3)(t,p,m)),(0,r.QX)(t,y)},[y,b,m,p]),x=(0,o.useCallback)((e=c)=>{let t;C(S(""===h?l(e):l(h)+e))},[S,c,C,h]),L=(0,o.useCallback)((e=c)=>{let t;C(S(""===h?l(-e):l(h)-e))},[S,c,C,h]),T=(0,o.useCallback)(()=>{let e;C(null==a?"":d(a,c,n)??p)},[a,n,c,C,p]),j=(0,o.useCallback)(e=>{C(d(e,c,y)??p)},[y,c,C,p]),M=l(h),D=M>m||M<p;return{isOutOfRange:D,isAtMax:M===m,isAtMin:M===p,precision:y,value:h,valueAsNumber:M,update:C,reset:T,increment:x,decrement:L,clamp:S,cast:j,setValue:g}}function l(e){return parseFloat(e.toString().replace(/[^\w.-]+/g,""))}function f(e,t){return Math.max((0,r.FZ)(t),(0,r.FZ)(e))}function d(e,t,n){let o=l(e);if(Number.isNaN(o))return;let u=f(o,t);return(0,r.QX)(o,n??u)}n(39354);var p=n(479);let m=globalThis?.document?o.useLayoutEffect:o.useEffect,b=(e,t)=>{let n=(0,o.useRef)(!1),r=(0,o.useRef)(!1);(0,o.useEffect)(()=>{if(n.current&&r.current)return e();r.current=!0},t),(0,o.useEffect)(()=>(n.current=!0,()=>{n.current=!1}),[])};function v(e,t){let{shouldFocus:n,visible:o,focusRef:u}=t,a=n&&!o;b(()=>{let t;if(!a||function(e){let t=e.current;if(!t)return!1;let n=(0,r.bq)(t);return!(!n||t.contains(n))&&!!(0,r.AO)(n)}(e))return;let n=u?.current||e.current;if(n)return t=requestAnimationFrame(()=>{n.focus({preventScroll:!0})}),()=>{cancelAnimationFrame(t)}},[a,e,u])}let E={preventScroll:!0,shouldFocus:!1};function g(e,t=E){let{focusRef:n,preventScroll:u,shouldFocus:i,visible:c}=t,s="current"in e?e.current:e,l=i&&c,f=(0,o.useRef)(l),d=(0,o.useRef)(c);m(()=>{!d.current&&c&&(f.current=l),d.current=c},[c,l]);let p=(0,o.useCallback)(()=>{if(c&&s&&f.current&&(f.current=!1,!s.contains(document.activeElement)))if(n?.current)requestAnimationFrame(()=>{n.current?.focus({preventScroll:u})});else{let e=(0,r.ep)(s);e.length>0&&requestAnimationFrame(()=>{e[0].focus({preventScroll:u})})}},[c,u,s,n]);b(()=>{p()},[p]),a(s,"transitionend",p)}let w=()=>"undefined"!=typeof window,h=e=>w()&&e.test(navigator.vendor),k=e=>w()&&e.test(function(){let e=navigator.userAgentData;return e?.platform??navigator.platform}()),y=()=>k(/mac|iphone|ipad|ipod/i),C=()=>y()&&h(/apple/i);function S(e){let{ref:t,elements:n,enabled:r}=e,o=()=>t.current?.ownerDocument??document;a(o,"pointerdown",e=>{if(!C()||!r)return;let u=e.composedPath?.()?.[0]??e.target,a=(n??[t]).some(e=>{let t="current"in e?e.current:e;return t?.contains(u)||t===u});o().activeElement!==u&&a&&(e.preventDefault(),u.focus())})}function x(e,...t){let n=function(e,t){let n=(0,o.useId)();return(0,o.useMemo)(()=>e||[void 0,n].filter(Boolean).join("-"),[e,void 0,n])}(e);return(0,o.useMemo)(()=>t.map(e=>`${e}-${n}`),[n,t])}function L(e,t){let n=(0,u.c)(e);(0,o.useEffect)(()=>{let e=null;return null!==t&&(e=window.setInterval(()=>n(),t)),()=>{e&&window.clearInterval(e)}},[t,n])}function T(...e){return t=>{e.forEach(e=>{!function(e,t){if(null!=e){if("function"==typeof e)return e(t);try{e.current=t}catch(n){throw Error(`Cannot assign value '${t}' to ref '${e}'`)}}}(e,t)})}}function j(...e){return(0,o.useMemo)(()=>T(...e),e)}function M(e){let{ref:t,handler:n,enabled:r=!0}=e,a=(0,u.c)(n),i=(0,o.useRef)({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}).current;(0,o.useEffect)(()=>{if(!r)return;let e=e=>{D(e,t)&&(i.isPointerDown=!0)},o=e=>{if(i.ignoreEmulatedMouseEvents){i.ignoreEmulatedMouseEvents=!1;return}i.isPointerDown&&n&&D(e,t)&&(i.isPointerDown=!1,a(e))},u=e=>{i.ignoreEmulatedMouseEvents=!0,n&&i.isPointerDown&&D(e,t)&&(i.isPointerDown=!1,a(e))},c=I(t.current);return c.addEventListener("mousedown",e,!0),c.addEventListener("mouseup",o,!0),c.addEventListener("touchstart",e,!0),c.addEventListener("touchend",u,!0),()=>{c.removeEventListener("mousedown",e,!0),c.removeEventListener("mouseup",o,!0),c.removeEventListener("touchstart",e,!0),c.removeEventListener("touchend",u,!0)}},[n,t,a,i,r])}function D(e,t){let n=e.composedPath?.()[0]??e.target;return(!n||!!I(n).contains(n))&&!t.current?.contains(n)}function I(e){return e?.ownerDocument??document}function P(e){let t=(0,o.useRef)(void 0);return(0,o.useEffect)(()=>{t.current=e},[e]),t.current}function O(e,t){let n=(0,u.c)(e);(0,o.useEffect)(()=>{if(null==t)return;let e=null;return e=window.setTimeout(()=>{n()},t),()=>{e&&window.clearTimeout(e)}},[t,n])}n(54903),globalThis?.document?o.useLayoutEffect:o.useEffect,n(94007);var A=Object.defineProperty},18337:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(82015);function o(e,t=[]){let n=(0,r.useRef)(e);return(0,r.useEffect)(()=>{n.current=e}),(0,r.useCallback)((...e)=>n.current?.(...e),t)}},39354:(e,t,n)=>{n.d(t,{i:()=>u});var r=n(82015),o=n(99897);function u(e,t={}){let[n,a]=(0,r.useState)(!1),[i,c]=(0,r.useState)(e);(0,r.useEffect)(()=>c(e),[e]);let{timeout:s=1500,...l}="number"==typeof t?{timeout:t}:t,f=(0,r.useCallback)(e=>{let t="string"==typeof e?e:i;"clipboard"in navigator?navigator.clipboard.writeText(t).then(()=>a(!0)).catch(()=>a(o(t,l))):a(o(t,l))},[i,l]);return(0,r.useEffect)(()=>{let e=null;return n&&(e=window.setTimeout(()=>{a(!1)},s)),()=>{e&&window.clearTimeout(e)}},[s,n]),{value:i,setValue:c,onCopy:f,hasCopied:n}}},79464:(e,t,n)=>{function r(e,t={}){let n=!1;function o(t){let n=(["container","root"].includes(t??"")?[e]:[e,t]).filter(Boolean).join("__"),r=`chakra-${n}`;return{className:r,selector:`.${r}`,toString:()=>t}}return{parts:function(...u){for(let e of(!function(){if(!n){n=!0;return}throw Error("[anatomy] .part(...) should only be called once. Did you mean to use .extend(...) ?")}(),u))t[e]=o(e);return r(e,t)},toPart:o,extend:function(...n){for(let e of n)e in t||(t[e]=o(e));return r(e,t)},selectors:function(){return Object.fromEntries(Object.entries(t).map(([e,t])=>[e,t.selector]))},classnames:function(){return Object.fromEntries(Object.entries(t).map(([e,t])=>[e,t.className]))},get keys(){return Object.keys(t)},__type:{}}}n.d(t,{aH:()=>o,Ov:()=>u,ZO:()=>a,RG:()=>i,M9:()=>j,fZ:()=>c,Lx:()=>s,Is:()=>l,Ip:()=>f,_8:()=>d,Gq:()=>p,yj:()=>m,Pe:()=>b,Zt:()=>v,zV:()=>E,vI:()=>g,j_:()=>w,Vg:()=>h,oc:()=>k,tC:()=>y,S4:()=>C,af:()=>S,e:()=>x,Us:()=>L,K_:()=>T});let o=r("accordion").parts("root","container","button","panel","icon"),u=r("alert").parts("title","description","container","icon","spinner"),a=r("avatar").parts("label","badge","container","excessLabel","group"),i=r("breadcrumb").parts("link","item","container","separator");r("button").parts();let c=r("checkbox").parts("control","icon","container","label");r("progress").parts("track","filledTrack","label");let s=r("drawer").parts("overlay","dialogContainer","dialog","header","closeButton","body","footer"),l=r("editable").parts("preview","input","textarea"),f=r("form").parts("container","requiredIndicator","helperText"),d=r("formError").parts("text","icon"),p=r("input").parts("addon","field","element","group"),m=r("list").parts("container","item","icon"),b=r("menu").parts("button","list","item","groupTitle","icon","command","divider"),v=r("modal").parts("overlay","dialogContainer","dialog","header","closeButton","body","footer"),E=r("numberinput").parts("root","field","stepperGroup","stepper");r("pininput").parts("field");let g=r("popover").parts("content","header","body","footer","popper","arrow","closeButton"),w=r("progress").parts("label","filledTrack","track"),h=r("radio").parts("container","control","label"),k=r("select").parts("field","icon"),y=r("slider").parts("container","track","thumb","filledTrack","mark"),C=r("stat").parts("container","label","helpText","number","icon"),S=r("switch").parts("container","track","thumb","label"),x=r("table").parts("table","thead","tbody","tr","th","td","tfoot","caption"),L=r("tabs").parts("root","tab","tablist","tabpanel","tabpanels","indicator"),T=r("tag").parts("container","label","closeButton"),j=r("card").parts("container","header","body","footer");r("stepper").parts("stepper","step","title","description","indicator","separator","icon","number")}};