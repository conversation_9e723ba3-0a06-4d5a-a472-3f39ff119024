{"c": ["pages/admin/guilds-pages_admin_guilds_tsx-d15b7b25", "webpack"], "r": ["pages/admin/applications"], "m": ["(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CUsers%5CPete%20Gaming%20PC%5CDesktop%5C404%20Bot%5Csrc%5Cdashboard%5Cpages%5Cadmin%5Capplications.tsx&page=%2Fadmin%2Fapplications!", "(pages-dir-browser)/./pages/admin/applications.tsx", "(pages-dir-browser)/__barrel_optimize__?names=Alert,AlertDescription,AlertIcon,Avatar,Badge,Box,Button,Card,CardBody,Divider,HStack,Heading,Icon,Modal,ModalBody,ModalCloseButton,ModalContent,ModalHeader,ModalOverlay,SimpleGrid,Spinner,Stat,StatArrow,StatHelpText,StatLabel,StatNumber,Tab,TabList,TabPanel,TabPanels,Table,Tabs,Tbody,Td,Text,Th,Thead,Tr,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs", "(pages-dir-browser)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/stat/stat-arrow.mjs", "(pages-dir-browser)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/stat/stat-help-text.mjs", "(pages-dir-browser)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/stat/stat-label.mjs", "(pages-dir-browser)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/stat/stat-number.mjs", "(pages-dir-browser)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/stat/stat.mjs"]}