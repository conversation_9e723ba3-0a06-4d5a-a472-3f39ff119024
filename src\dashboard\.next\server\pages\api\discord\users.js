"use strict";(()=>{var e={};e.id=8616,e.ids=[8616],e.modules={224:e=>{e.exports=import("@discordjs/rest")},4722:e=>{e.exports=require("next-auth/react")},6688:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{config:()=>l,default:()=>u,routeModule:()=>m});var s=r(93433),a=r(20264),n=r(20584),o=r(43882),d=e([o]);o=(d.then?(await d)():d)[0];let u=(0,n.M)(o,"default"),l=(0,n.M)(o,"config"),m=new s.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/discord/users",pathname:"/api/discord/users",bundlePath:"",filename:""},userland:o});i()}catch(e){i(e)}})},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},33915:e=>{e.exports=import("discord-api-types/v10")},43882:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{default:()=>u});var s=r(4722),a=r(224),n=r(33915),o=r(20381),d=e([a,n]);async function u(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{if(!await (0,s.getSession)({req:e}))return t.status(401).json({error:"Unauthorized"});let r=new a.REST({version:"10"}).setToken(o._.DISCORD_BOT_TOKEN),i=o._.DISCORD_GUILD_ID,d=await r.get(`${n.Routes.guildMembers(i)}?limit=1000`),u=await r.get(n.Routes.guildRoles(i)),l=d.map(e=>({id:e.user.id,tag:`${e.user.username}${"0"!==e.user.discriminator?`#${e.user.discriminator}`:""}`,username:e.user.username,discriminator:e.user.discriminator,avatar:e.user.avatar?`https://cdn.discordapp.com/avatars/${e.user.id}/${e.user.avatar}.${e.user.avatar.startsWith("a_")?"gif":"png"}`:null,roles:e.roles.map(e=>{let t=u.find(t=>t.id===e);return t?{id:t.id,name:t.name,color:t.color?`#${t.color.toString(16).padStart(6,"0")}`:null,position:t.position}:null}).filter(Boolean),joinedAt:e.joined_at,timeoutEnd:e.communication_disabled_until?new Date(e.communication_disabled_until).getTime():null,isTimedOut:e.communication_disabled_until&&new Date(e.communication_disabled_until).getTime()>Date.now(),isAdmin:e.roles.some(e=>{let t=u.find(t=>t.id===e);return t&&(BigInt(t.permissions)&BigInt(8))===BigInt(8)}),isModerator:e.roles.some(e=>{let t=u.find(t=>t.id===e);return t&&(BigInt(t.permissions)&BigInt(2))===BigInt(2)})}));t.status(200).json(l)}catch(e){t.status(500).json({error:"Failed to fetch users",details:e instanceof Error?e.message:"Unknown error"})}}[a,n]=d.then?(await d)():d,i()}catch(e){i(e)}})},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(6688));module.exports=i})();