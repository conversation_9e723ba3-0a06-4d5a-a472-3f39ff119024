"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171"],{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/VisualElement.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/VisualElement.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VisualElement: () => (/* binding */ VisualElement)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _motion_features_definitions_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../motion/features/definitions.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/definitions.mjs\");\n/* harmony import */ var _projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../projection/geometry/models.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/models.mjs\");\n/* harmony import */ var _utils_reduced_motion_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/reduced-motion/index.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/reduced-motion/index.mjs\");\n/* harmony import */ var _utils_reduced_motion_state_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/reduced-motion/state.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs\");\n/* harmony import */ var _store_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./store.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/store.mjs\");\n/* harmony import */ var _utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/is-controlling-variants.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs\");\n/* harmony import */ var _utils_motion_values_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/motion-values.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/motion-values.mjs\");\n/* harmony import */ var _utils_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/resolve-variants.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs\");\n\n\n\n\n\n\n\n\n\n\n\nconst propEventHandlers = [\n    \"AnimationStart\",\n    \"AnimationComplete\",\n    \"Update\",\n    \"BeforeLayoutMeasure\",\n    \"LayoutMeasure\",\n    \"LayoutAnimationStart\",\n    \"LayoutAnimationComplete\",\n];\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n    /**\n     * This method takes React props and returns found MotionValues. For example, HTML\n     * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n     *\n     * This isn't an abstract method as it needs calling in the constructor, but it is\n     * intended to be one.\n     */\n    scrapeMotionValuesFromProps(_props, _prevProps, _visualElement) {\n        return {};\n    }\n    constructor({ parent, props, presenceContext, reducedMotionConfig, blockInitialAnimation, visualState, }, options = {}) {\n        /**\n         * A reference to the current underlying Instance, e.g. a HTMLElement\n         * or Three.Mesh etc.\n         */\n        this.current = null;\n        /**\n         * A set containing references to this VisualElement's children.\n         */\n        this.children = new Set();\n        /**\n         * Determine what role this visual element should take in the variant tree.\n         */\n        this.isVariantNode = false;\n        this.isControllingVariants = false;\n        /**\n         * Decides whether this VisualElement should animate in reduced motion\n         * mode.\n         *\n         * TODO: This is currently set on every individual VisualElement but feels\n         * like it could be set globally.\n         */\n        this.shouldReduceMotion = null;\n        /**\n         * A map of all motion values attached to this visual element. Motion\n         * values are source of truth for any given animated value. A motion\n         * value might be provided externally by the component via props.\n         */\n        this.values = new Map();\n        this.KeyframeResolver = motion_dom__WEBPACK_IMPORTED_MODULE_0__.KeyframeResolver;\n        /**\n         * Cleanup functions for active features (hover/tap/exit etc)\n         */\n        this.features = {};\n        /**\n         * A map of every subscription that binds the provided or generated\n         * motion values onChange listeners to this visual element.\n         */\n        this.valueSubscriptions = new Map();\n        /**\n         * A reference to the previously-provided motion values as returned\n         * from scrapeMotionValuesFromProps. We use the keys in here to determine\n         * if any motion values need to be removed after props are updated.\n         */\n        this.prevMotionValues = {};\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        /**\n         * An object containing an unsubscribe function for each prop event subscription.\n         * For example, every \"Update\" event can have multiple subscribers via\n         * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n         */\n        this.propEventSubscriptions = {};\n        this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n        this.render = () => {\n            if (!this.current)\n                return;\n            this.triggerBuild();\n            this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n        };\n        this.renderScheduledAt = 0.0;\n        this.scheduleRender = () => {\n            const now = motion_dom__WEBPACK_IMPORTED_MODULE_0__.time.now();\n            if (this.renderScheduledAt < now) {\n                this.renderScheduledAt = now;\n                motion_dom__WEBPACK_IMPORTED_MODULE_0__.frame.render(this.render, false, true);\n            }\n        };\n        const { latestValues, renderState } = visualState;\n        this.latestValues = latestValues;\n        this.baseTarget = { ...latestValues };\n        this.initialValues = props.initial ? { ...latestValues } : {};\n        this.renderState = renderState;\n        this.parent = parent;\n        this.props = props;\n        this.presenceContext = presenceContext;\n        this.depth = parent ? parent.depth + 1 : 0;\n        this.reducedMotionConfig = reducedMotionConfig;\n        this.options = options;\n        this.blockInitialAnimation = Boolean(blockInitialAnimation);\n        this.isControllingVariants = (0,_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_1__.isControllingVariants)(props);\n        this.isVariantNode = (0,_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_1__.isVariantNode)(props);\n        if (this.isVariantNode) {\n            this.variantChildren = new Set();\n        }\n        this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n        /**\n         * Any motion values that are provided to the element when created\n         * aren't yet bound to the element, as this would technically be impure.\n         * However, we iterate through the motion values and set them to the\n         * initial values for this component.\n         *\n         * TODO: This is impure and we should look at changing this to run on mount.\n         * Doing so will break some tests but this isn't necessarily a breaking change,\n         * more a reflection of the test.\n         */\n        const { willChange, ...initialMotionValues } = this.scrapeMotionValuesFromProps(props, {}, this);\n        for (const key in initialMotionValues) {\n            const value = initialMotionValues[key];\n            if (latestValues[key] !== undefined && (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(value)) {\n                value.set(latestValues[key], false);\n            }\n        }\n    }\n    mount(instance) {\n        this.current = instance;\n        _store_mjs__WEBPACK_IMPORTED_MODULE_2__.visualElementStore.set(instance, this);\n        if (this.projection && !this.projection.instance) {\n            this.projection.mount(instance);\n        }\n        if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n            this.removeFromVariantTree = this.parent.addVariantChild(this);\n        }\n        this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n        if (!_utils_reduced_motion_state_mjs__WEBPACK_IMPORTED_MODULE_3__.hasReducedMotionListener.current) {\n            (0,_utils_reduced_motion_index_mjs__WEBPACK_IMPORTED_MODULE_4__.initPrefersReducedMotion)();\n        }\n        this.shouldReduceMotion =\n            this.reducedMotionConfig === \"never\"\n                ? false\n                : this.reducedMotionConfig === \"always\"\n                    ? true\n                    : _utils_reduced_motion_state_mjs__WEBPACK_IMPORTED_MODULE_3__.prefersReducedMotion.current;\n        if (true) {\n            (0,motion_utils__WEBPACK_IMPORTED_MODULE_5__.warnOnce)(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n        }\n        if (this.parent)\n            this.parent.children.add(this);\n        this.update(this.props, this.presenceContext);\n    }\n    unmount() {\n        this.projection && this.projection.unmount();\n        (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(this.notifyUpdate);\n        (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(this.render);\n        this.valueSubscriptions.forEach((remove) => remove());\n        this.valueSubscriptions.clear();\n        this.removeFromVariantTree && this.removeFromVariantTree();\n        this.parent && this.parent.children.delete(this);\n        for (const key in this.events) {\n            this.events[key].clear();\n        }\n        for (const key in this.features) {\n            const feature = this.features[key];\n            if (feature) {\n                feature.unmount();\n                feature.isMounted = false;\n            }\n        }\n        this.current = null;\n    }\n    bindToMotionValue(key, value) {\n        if (this.valueSubscriptions.has(key)) {\n            this.valueSubscriptions.get(key)();\n        }\n        const valueIsTransform = motion_dom__WEBPACK_IMPORTED_MODULE_0__.transformProps.has(key);\n        if (valueIsTransform && this.onBindTransform) {\n            this.onBindTransform();\n        }\n        const removeOnChange = value.on(\"change\", (latestValue) => {\n            this.latestValues[key] = latestValue;\n            this.props.onUpdate && motion_dom__WEBPACK_IMPORTED_MODULE_0__.frame.preRender(this.notifyUpdate);\n            if (valueIsTransform && this.projection) {\n                this.projection.isTransformDirty = true;\n            }\n        });\n        const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n        let removeSyncCheck;\n        if (window.MotionCheckAppearSync) {\n            removeSyncCheck = window.MotionCheckAppearSync(this, key, value);\n        }\n        this.valueSubscriptions.set(key, () => {\n            removeOnChange();\n            removeOnRenderRequest();\n            if (removeSyncCheck)\n                removeSyncCheck();\n            if (value.owner)\n                value.stop();\n        });\n    }\n    sortNodePosition(other) {\n        /**\n         * If these nodes aren't even of the same type we can't compare their depth.\n         */\n        if (!this.current ||\n            !this.sortInstanceNodePosition ||\n            this.type !== other.type) {\n            return 0;\n        }\n        return this.sortInstanceNodePosition(this.current, other.current);\n    }\n    updateFeatures() {\n        let key = \"animation\";\n        for (key in _motion_features_definitions_mjs__WEBPACK_IMPORTED_MODULE_6__.featureDefinitions) {\n            const featureDefinition = _motion_features_definitions_mjs__WEBPACK_IMPORTED_MODULE_6__.featureDefinitions[key];\n            if (!featureDefinition)\n                continue;\n            const { isEnabled, Feature: FeatureConstructor } = featureDefinition;\n            /**\n             * If this feature is enabled but not active, make a new instance.\n             */\n            if (!this.features[key] &&\n                FeatureConstructor &&\n                isEnabled(this.props)) {\n                this.features[key] = new FeatureConstructor(this);\n            }\n            /**\n             * If we have a feature, mount or update it.\n             */\n            if (this.features[key]) {\n                const feature = this.features[key];\n                if (feature.isMounted) {\n                    feature.update();\n                }\n                else {\n                    feature.mount();\n                    feature.isMounted = true;\n                }\n            }\n        }\n    }\n    triggerBuild() {\n        this.build(this.renderState, this.latestValues, this.props);\n    }\n    /**\n     * Measure the current viewport box with or without transforms.\n     * Only measures axis-aligned boxes, rotate and skew must be manually\n     * removed with a re-render to work.\n     */\n    measureViewportBox() {\n        return this.current\n            ? this.measureInstanceViewportBox(this.current, this.props)\n            : (0,_projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_7__.createBox)();\n    }\n    getStaticValue(key) {\n        return this.latestValues[key];\n    }\n    setStaticValue(key, value) {\n        this.latestValues[key] = value;\n    }\n    /**\n     * Update the provided props. Ensure any newly-added motion values are\n     * added to our map, old ones removed, and listeners updated.\n     */\n    update(props, presenceContext) {\n        if (props.transformTemplate || this.props.transformTemplate) {\n            this.scheduleRender();\n        }\n        this.prevProps = this.props;\n        this.props = props;\n        this.prevPresenceContext = this.presenceContext;\n        this.presenceContext = presenceContext;\n        /**\n         * Update prop event handlers ie onAnimationStart, onAnimationComplete\n         */\n        for (let i = 0; i < propEventHandlers.length; i++) {\n            const key = propEventHandlers[i];\n            if (this.propEventSubscriptions[key]) {\n                this.propEventSubscriptions[key]();\n                delete this.propEventSubscriptions[key];\n            }\n            const listenerName = (\"on\" + key);\n            const listener = props[listenerName];\n            if (listener) {\n                this.propEventSubscriptions[key] = this.on(key, listener);\n            }\n        }\n        this.prevMotionValues = (0,_utils_motion_values_mjs__WEBPACK_IMPORTED_MODULE_8__.updateMotionValuesFromProps)(this, this.scrapeMotionValuesFromProps(props, this.prevProps, this), this.prevMotionValues);\n        if (this.handleChildMotionValue) {\n            this.handleChildMotionValue();\n        }\n    }\n    getProps() {\n        return this.props;\n    }\n    /**\n     * Returns the variant definition with a given name.\n     */\n    getVariant(name) {\n        return this.props.variants ? this.props.variants[name] : undefined;\n    }\n    /**\n     * Returns the defined default transition on this component.\n     */\n    getDefaultTransition() {\n        return this.props.transition;\n    }\n    getTransformPagePoint() {\n        return this.props.transformPagePoint;\n    }\n    getClosestVariantNode() {\n        return this.isVariantNode\n            ? this\n            : this.parent\n                ? this.parent.getClosestVariantNode()\n                : undefined;\n    }\n    /**\n     * Add a child visual element to our set of children.\n     */\n    addVariantChild(child) {\n        const closestVariantNode = this.getClosestVariantNode();\n        if (closestVariantNode) {\n            closestVariantNode.variantChildren &&\n                closestVariantNode.variantChildren.add(child);\n            return () => closestVariantNode.variantChildren.delete(child);\n        }\n    }\n    /**\n     * Add a motion value and bind it to this visual element.\n     */\n    addValue(key, value) {\n        // Remove existing value if it exists\n        const existingValue = this.values.get(key);\n        if (value !== existingValue) {\n            if (existingValue)\n                this.removeValue(key);\n            this.bindToMotionValue(key, value);\n            this.values.set(key, value);\n            this.latestValues[key] = value.get();\n        }\n    }\n    /**\n     * Remove a motion value and unbind any active subscriptions.\n     */\n    removeValue(key) {\n        this.values.delete(key);\n        const unsubscribe = this.valueSubscriptions.get(key);\n        if (unsubscribe) {\n            unsubscribe();\n            this.valueSubscriptions.delete(key);\n        }\n        delete this.latestValues[key];\n        this.removeValueFromRenderState(key, this.renderState);\n    }\n    /**\n     * Check whether we have a motion value for this key\n     */\n    hasValue(key) {\n        return this.values.has(key);\n    }\n    getValue(key, defaultValue) {\n        if (this.props.values && this.props.values[key]) {\n            return this.props.values[key];\n        }\n        let value = this.values.get(key);\n        if (value === undefined && defaultValue !== undefined) {\n            value = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.motionValue)(defaultValue === null ? undefined : defaultValue, { owner: this });\n            this.addValue(key, value);\n        }\n        return value;\n    }\n    /**\n     * If we're trying to animate to a previously unencountered value,\n     * we need to check for it in our state and as a last resort read it\n     * directly from the instance (which might have performance implications).\n     */\n    readValue(key, target) {\n        let value = this.latestValues[key] !== undefined || !this.current\n            ? this.latestValues[key]\n            : this.getBaseTargetFromProps(this.props, key) ??\n                this.readValueFromInstance(this.current, key, this.options);\n        if (value !== undefined && value !== null) {\n            if (typeof value === \"string\" &&\n                ((0,motion_utils__WEBPACK_IMPORTED_MODULE_5__.isNumericalString)(value) || (0,motion_utils__WEBPACK_IMPORTED_MODULE_5__.isZeroValueString)(value))) {\n                // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n                value = parseFloat(value);\n            }\n            else if (!(0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.findValueType)(value) && motion_dom__WEBPACK_IMPORTED_MODULE_0__.complex.test(target)) {\n                value = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.getAnimatableNone)(key, target);\n            }\n            this.setBaseTarget(key, (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(value) ? value.get() : value);\n        }\n        return (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(value) ? value.get() : value;\n    }\n    /**\n     * Set the base target to later animate back to. This is currently\n     * only hydrated on creation and when we first read a value.\n     */\n    setBaseTarget(key, value) {\n        this.baseTarget[key] = value;\n    }\n    /**\n     * Find the base target for a value thats been removed from all animation\n     * props.\n     */\n    getBaseTarget(key) {\n        const { initial } = this.props;\n        let valueFromInitial;\n        if (typeof initial === \"string\" || typeof initial === \"object\") {\n            const variant = (0,_utils_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_9__.resolveVariantFromProps)(this.props, initial, this.presenceContext?.custom);\n            if (variant) {\n                valueFromInitial = variant[key];\n            }\n        }\n        /**\n         * If this value still exists in the current initial variant, read that.\n         */\n        if (initial && valueFromInitial !== undefined) {\n            return valueFromInitial;\n        }\n        /**\n         * Alternatively, if this VisualElement config has defined a getBaseTarget\n         * so we can read the value from an alternative source, try that.\n         */\n        const target = this.getBaseTargetFromProps(this.props, key);\n        if (target !== undefined && !(0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(target))\n            return target;\n        /**\n         * If the value was initially defined on initial, but it doesn't any more,\n         * return undefined. Otherwise return the value as initially read from the DOM.\n         */\n        return this.initialValues[key] !== undefined &&\n            valueFromInitial === undefined\n            ? undefined\n            : this.baseTarget[key];\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new motion_utils__WEBPACK_IMPORTED_MODULE_5__.SubscriptionManager();\n        }\n        return this.events[eventName].add(callback);\n    }\n    notify(eventName, ...args) {\n        if (this.events[eventName]) {\n            this.events[eventName].notify(...args);\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/VisualElement.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/create-factory.mjs":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/create-factory.mjs ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMotionComponentFactory: () => (/* binding */ createMotionComponentFactory)\n/* harmony export */ });\n/* harmony import */ var _motion_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../motion/index.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/index.mjs\");\n/* harmony import */ var _dom_use_render_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../dom/use-render.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/use-render.mjs\");\n/* harmony import */ var _dom_utils_is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dom/utils/is-svg-component.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs\");\n/* harmony import */ var _html_config_motion_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../html/config-motion.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/config-motion.mjs\");\n/* harmony import */ var _svg_config_motion_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../svg/config-motion.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs\");\n\n\n\n\n\n\nfunction createMotionComponentFactory(preloadedFeatures, createVisualElement) {\n    return function createMotionComponent(Component, { forwardMotionProps } = { forwardMotionProps: false }) {\n        const baseConfig = (0,_dom_utils_is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_0__.isSVGComponent)(Component)\n            ? _svg_config_motion_mjs__WEBPACK_IMPORTED_MODULE_1__.svgMotionConfig\n            : _html_config_motion_mjs__WEBPACK_IMPORTED_MODULE_2__.htmlMotionConfig;\n        const config = {\n            ...baseConfig,\n            preloadedFeatures,\n            useRender: (0,_dom_use_render_mjs__WEBPACK_IMPORTED_MODULE_3__.createUseRender)(forwardMotionProps),\n            createVisualElement,\n            Component,\n        };\n        return (0,_motion_index_mjs__WEBPACK_IMPORTED_MODULE_4__.createRendererMotionComponent)(config);\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/create-factory.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/create-proxy.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/create-proxy.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDOMMotionComponentProxy: () => (/* binding */ createDOMMotionComponentProxy)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n\n\nfunction createDOMMotionComponentProxy(componentFactory) {\n    if (typeof Proxy === \"undefined\") {\n        return componentFactory;\n    }\n    /**\n     * A cache of generated `motion` components, e.g `motion.div`, `motion.input` etc.\n     * Rather than generating them anew every render.\n     */\n    const componentCache = new Map();\n    const deprecatedFactoryFunction = (...args) => {\n        if (true) {\n            (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.warnOnce)(false, \"motion() is deprecated. Use motion.create() instead.\");\n        }\n        return componentFactory(...args);\n    };\n    return new Proxy(deprecatedFactoryFunction, {\n        /**\n         * Called when `motion` is referenced with a prop: `motion.div`, `motion.input` etc.\n         * The prop name is passed through as `key` and we can use that to generate a `motion`\n         * DOM component with that name.\n         */\n        get: (_target, key) => {\n            if (key === \"create\")\n                return componentFactory;\n            /**\n             * If this element doesn't exist in the component cache, create it and cache.\n             */\n            if (!componentCache.has(key)) {\n                componentCache.set(key, componentFactory(key));\n            }\n            return componentCache.get(key);\n        },\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/create-proxy.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/m/create.mjs":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/m/create.mjs ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMinimalMotionComponent: () => (/* binding */ createMinimalMotionComponent)\n/* harmony export */ });\n/* harmony import */ var _create_factory_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../create-factory.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/create-factory.mjs\");\n\n\nconst createMinimalMotionComponent = \n/*@__PURE__*/ (0,_create_factory_mjs__WEBPACK_IMPORTED_MODULE_0__.createMotionComponentFactory)();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2NvbXBvbmVudHMvbS9jcmVhdGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFFOztBQUVyRTtBQUNBLGNBQWMsaUZBQTRCOztBQUVGIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcY29tcG9uZW50c1xcbVxcY3JlYXRlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVNb3Rpb25Db21wb25lbnRGYWN0b3J5IH0gZnJvbSAnLi4vY3JlYXRlLWZhY3RvcnkubWpzJztcblxuY29uc3QgY3JlYXRlTWluaW1hbE1vdGlvbkNvbXBvbmVudCA9IFxuLypAX19QVVJFX18qLyBjcmVhdGVNb3Rpb25Db21wb25lbnRGYWN0b3J5KCk7XG5cbmV4cG9ydCB7IGNyZWF0ZU1pbmltYWxNb3Rpb25Db21wb25lbnQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/m/create.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/m/proxy.mjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/m/proxy.mjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   m: () => (/* binding */ m)\n/* harmony export */ });\n/* harmony import */ var _create_proxy_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../create-proxy.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/create-proxy.mjs\");\n/* harmony import */ var _create_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./create.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/m/create.mjs\");\n\n\n\nconst m = /*@__PURE__*/ (0,_create_proxy_mjs__WEBPACK_IMPORTED_MODULE_0__.createDOMMotionComponentProxy)(_create_mjs__WEBPACK_IMPORTED_MODULE_1__.createMinimalMotionComponent);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2NvbXBvbmVudHMvbS9wcm94eS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9FO0FBQ1I7O0FBRTVELHdCQUF3QixnRkFBNkIsQ0FBQyxxRUFBNEI7O0FBRXJFIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcY29tcG9uZW50c1xcbVxccHJveHkubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZURPTU1vdGlvbkNvbXBvbmVudFByb3h5IH0gZnJvbSAnLi4vY3JlYXRlLXByb3h5Lm1qcyc7XG5pbXBvcnQgeyBjcmVhdGVNaW5pbWFsTW90aW9uQ29tcG9uZW50IH0gZnJvbSAnLi9jcmVhdGUubWpzJztcblxuY29uc3QgbSA9IC8qQF9fUFVSRV9fKi8gY3JlYXRlRE9NTW90aW9uQ29tcG9uZW50UHJveHkoY3JlYXRlTWluaW1hbE1vdGlvbkNvbXBvbmVudCk7XG5cbmV4cG9ydCB7IG0gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/m/proxy.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/motion/create.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/motion/create.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMotionComponent: () => (/* binding */ createMotionComponent)\n/* harmony export */ });\n/* harmony import */ var _motion_features_animations_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../motion/features/animations.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animations.mjs\");\n/* harmony import */ var _motion_features_drag_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../motion/features/drag.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/drag.mjs\");\n/* harmony import */ var _motion_features_gestures_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../motion/features/gestures.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/gestures.mjs\");\n/* harmony import */ var _motion_features_layout_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../motion/features/layout.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout.mjs\");\n/* harmony import */ var _create_factory_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../create-factory.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/create-factory.mjs\");\n/* harmony import */ var _dom_create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../dom/create-visual-element.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs\");\n\n\n\n\n\n\n\nconst createMotionComponent = /*@__PURE__*/ (0,_create_factory_mjs__WEBPACK_IMPORTED_MODULE_0__.createMotionComponentFactory)({\n    ..._motion_features_animations_mjs__WEBPACK_IMPORTED_MODULE_1__.animations,\n    ..._motion_features_gestures_mjs__WEBPACK_IMPORTED_MODULE_2__.gestureAnimations,\n    ..._motion_features_drag_mjs__WEBPACK_IMPORTED_MODULE_3__.drag,\n    ..._motion_features_layout_mjs__WEBPACK_IMPORTED_MODULE_4__.layout,\n}, _dom_create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_5__.createDomVisualElement);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2NvbXBvbmVudHMvbW90aW9uL2NyZWF0ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFxRTtBQUNaO0FBQ2lCO0FBQ2I7QUFDUTtBQUNROztBQUU3RSw0Q0FBNEMsaUZBQTRCO0FBQ3hFLE9BQU8sdUVBQVU7QUFDakIsT0FBTyw0RUFBaUI7QUFDeEIsT0FBTywyREFBSTtBQUNYLE9BQU8sK0RBQU07QUFDYixDQUFDLEVBQUUsa0ZBQXNCOztBQUVRIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcY29tcG9uZW50c1xcbW90aW9uXFxjcmVhdGUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFuaW1hdGlvbnMgfSBmcm9tICcuLi8uLi8uLi9tb3Rpb24vZmVhdHVyZXMvYW5pbWF0aW9ucy5tanMnO1xuaW1wb3J0IHsgZHJhZyB9IGZyb20gJy4uLy4uLy4uL21vdGlvbi9mZWF0dXJlcy9kcmFnLm1qcyc7XG5pbXBvcnQgeyBnZXN0dXJlQW5pbWF0aW9ucyB9IGZyb20gJy4uLy4uLy4uL21vdGlvbi9mZWF0dXJlcy9nZXN0dXJlcy5tanMnO1xuaW1wb3J0IHsgbGF5b3V0IH0gZnJvbSAnLi4vLi4vLi4vbW90aW9uL2ZlYXR1cmVzL2xheW91dC5tanMnO1xuaW1wb3J0IHsgY3JlYXRlTW90aW9uQ29tcG9uZW50RmFjdG9yeSB9IGZyb20gJy4uL2NyZWF0ZS1mYWN0b3J5Lm1qcyc7XG5pbXBvcnQgeyBjcmVhdGVEb21WaXN1YWxFbGVtZW50IH0gZnJvbSAnLi4vLi4vZG9tL2NyZWF0ZS12aXN1YWwtZWxlbWVudC5tanMnO1xuXG5jb25zdCBjcmVhdGVNb3Rpb25Db21wb25lbnQgPSAvKkBfX1BVUkVfXyovIGNyZWF0ZU1vdGlvbkNvbXBvbmVudEZhY3Rvcnkoe1xuICAgIC4uLmFuaW1hdGlvbnMsXG4gICAgLi4uZ2VzdHVyZUFuaW1hdGlvbnMsXG4gICAgLi4uZHJhZyxcbiAgICAuLi5sYXlvdXQsXG59LCBjcmVhdGVEb21WaXN1YWxFbGVtZW50KTtcblxuZXhwb3J0IHsgY3JlYXRlTW90aW9uQ29tcG9uZW50IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/motion/create.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   motion: () => (/* binding */ motion)\n/* harmony export */ });\n/* harmony import */ var _create_proxy_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../create-proxy.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/create-proxy.mjs\");\n/* harmony import */ var _create_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./create.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/motion/create.mjs\");\n\n\n\nconst motion = /*@__PURE__*/ (0,_create_proxy_mjs__WEBPACK_IMPORTED_MODULE_0__.createDOMMotionComponentProxy)(_create_mjs__WEBPACK_IMPORTED_MODULE_1__.createMotionComponent);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2NvbXBvbmVudHMvbW90aW9uL3Byb3h5Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0U7QUFDZjs7QUFFckQsNkJBQTZCLGdGQUE2QixDQUFDLDhEQUFxQjs7QUFFOUQiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFxjb21wb25lbnRzXFxtb3Rpb25cXHByb3h5Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVET01Nb3Rpb25Db21wb25lbnRQcm94eSB9IGZyb20gJy4uL2NyZWF0ZS1wcm94eS5tanMnO1xuaW1wb3J0IHsgY3JlYXRlTW90aW9uQ29tcG9uZW50IH0gZnJvbSAnLi9jcmVhdGUubWpzJztcblxuY29uc3QgbW90aW9uID0gLypAX19QVVJFX18qLyBjcmVhdGVET01Nb3Rpb25Db21wb25lbnRQcm94eShjcmVhdGVNb3Rpb25Db21wb25lbnQpO1xuXG5leHBvcnQgeyBtb3Rpb24gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOMVisualElement: () => (/* binding */ DOMVisualElement)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _VisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../VisualElement.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/VisualElement.mjs\");\n\n\n\nclass DOMVisualElement extends _VisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__.VisualElement {\n    constructor() {\n        super(...arguments);\n        this.KeyframeResolver = motion_dom__WEBPACK_IMPORTED_MODULE_1__.DOMKeyframesResolver;\n    }\n    sortInstanceNodePosition(a, b) {\n        /**\n         * compareDocumentPosition returns a bitmask, by using the bitwise &\n         * we're returning true if 2 in that bitmask is set to true. 2 is set\n         * to true if b preceeds a.\n         */\n        return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props.style\n            ? props.style[key]\n            : undefined;\n    }\n    removeValueFromRenderState(key, { vars, style }) {\n        delete vars[key];\n        delete style[key];\n    }\n    handleChildMotionValue() {\n        if (this.childSubscription) {\n            this.childSubscription();\n            delete this.childSubscription;\n        }\n        const { children } = this.props;\n        if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.isMotionValue)(children)) {\n            this.childSubscription = children.on(\"change\", (latest) => {\n                if (this.current) {\n                    this.current.textContent = `${latest}`;\n                }\n            });\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDomVisualElement: () => (/* binding */ createDomVisualElement)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var _html_HTMLVisualElement_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../html/HTMLVisualElement.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs\");\n/* harmony import */ var _svg_SVGVisualElement_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../svg/SVGVisualElement.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs\");\n/* harmony import */ var _utils_is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/is-svg-component.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs\");\n\n\n\n\n\nconst createDomVisualElement = (Component, options) => {\n    return (0,_utils_is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_1__.isSVGComponent)(Component)\n        ? new _svg_SVGVisualElement_mjs__WEBPACK_IMPORTED_MODULE_2__.SVGVisualElement(options)\n        : new _html_HTMLVisualElement_mjs__WEBPACK_IMPORTED_MODULE_3__.HTMLVisualElement(options, {\n            allowProjection: Component !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n        });\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9jcmVhdGUtdmlzdWFsLWVsZW1lbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWlDO0FBQ2lDO0FBQ0g7QUFDRDs7QUFFOUQ7QUFDQSxXQUFXLDJFQUFjO0FBQ3pCLGNBQWMsdUVBQWdCO0FBQzlCLGNBQWMsMEVBQWlCO0FBQy9CLDJDQUEyQywyQ0FBUTtBQUNuRCxTQUFTO0FBQ1Q7O0FBRWtDIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFxjcmVhdGUtdmlzdWFsLWVsZW1lbnQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZyYWdtZW50IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgSFRNTFZpc3VhbEVsZW1lbnQgfSBmcm9tICcuLi9odG1sL0hUTUxWaXN1YWxFbGVtZW50Lm1qcyc7XG5pbXBvcnQgeyBTVkdWaXN1YWxFbGVtZW50IH0gZnJvbSAnLi4vc3ZnL1NWR1Zpc3VhbEVsZW1lbnQubWpzJztcbmltcG9ydCB7IGlzU1ZHQ29tcG9uZW50IH0gZnJvbSAnLi91dGlscy9pcy1zdmctY29tcG9uZW50Lm1qcyc7XG5cbmNvbnN0IGNyZWF0ZURvbVZpc3VhbEVsZW1lbnQgPSAoQ29tcG9uZW50LCBvcHRpb25zKSA9PiB7XG4gICAgcmV0dXJuIGlzU1ZHQ29tcG9uZW50KENvbXBvbmVudClcbiAgICAgICAgPyBuZXcgU1ZHVmlzdWFsRWxlbWVudChvcHRpb25zKVxuICAgICAgICA6IG5ldyBIVE1MVmlzdWFsRWxlbWVudChvcHRpb25zLCB7XG4gICAgICAgICAgICBhbGxvd1Byb2plY3Rpb246IENvbXBvbmVudCAhPT0gRnJhZ21lbnQsXG4gICAgICAgIH0pO1xufTtcblxuZXhwb3J0IHsgY3JlYXRlRG9tVmlzdWFsRWxlbWVudCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/features-animation.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/features-animation.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   domAnimation: () => (/* binding */ domAnimation)\n/* harmony export */ });\n/* harmony import */ var _motion_features_animations_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../motion/features/animations.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animations.mjs\");\n/* harmony import */ var _motion_features_gestures_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../motion/features/gestures.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/gestures.mjs\");\n/* harmony import */ var _create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./create-visual-element.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs\");\n\n\n\n\n/**\n * @public\n */\nconst domAnimation = {\n    renderer: _create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_0__.createDomVisualElement,\n    ..._motion_features_animations_mjs__WEBPACK_IMPORTED_MODULE_1__.animations,\n    ..._motion_features_gestures_mjs__WEBPACK_IMPORTED_MODULE_2__.gestureAnimations,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9mZWF0dXJlcy1hbmltYXRpb24ubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBa0U7QUFDSztBQUNGOztBQUVyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsOEVBQXNCO0FBQ3BDLE9BQU8sdUVBQVU7QUFDakIsT0FBTyw0RUFBaUI7QUFDeEI7O0FBRXdCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFxmZWF0dXJlcy1hbmltYXRpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFuaW1hdGlvbnMgfSBmcm9tICcuLi8uLi9tb3Rpb24vZmVhdHVyZXMvYW5pbWF0aW9ucy5tanMnO1xuaW1wb3J0IHsgZ2VzdHVyZUFuaW1hdGlvbnMgfSBmcm9tICcuLi8uLi9tb3Rpb24vZmVhdHVyZXMvZ2VzdHVyZXMubWpzJztcbmltcG9ydCB7IGNyZWF0ZURvbVZpc3VhbEVsZW1lbnQgfSBmcm9tICcuL2NyZWF0ZS12aXN1YWwtZWxlbWVudC5tanMnO1xuXG4vKipcbiAqIEBwdWJsaWNcbiAqL1xuY29uc3QgZG9tQW5pbWF0aW9uID0ge1xuICAgIHJlbmRlcmVyOiBjcmVhdGVEb21WaXN1YWxFbGVtZW50LFxuICAgIC4uLmFuaW1hdGlvbnMsXG4gICAgLi4uZ2VzdHVyZUFuaW1hdGlvbnMsXG59O1xuXG5leHBvcnQgeyBkb21BbmltYXRpb24gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/features-animation.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/features-max.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/features-max.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   domMax: () => (/* binding */ domMax)\n/* harmony export */ });\n/* harmony import */ var _motion_features_drag_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../motion/features/drag.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/drag.mjs\");\n/* harmony import */ var _motion_features_layout_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../motion/features/layout.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout.mjs\");\n/* harmony import */ var _features_animation_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./features-animation.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/features-animation.mjs\");\n\n\n\n\n/**\n * @public\n */\nconst domMax = {\n    ..._features_animation_mjs__WEBPACK_IMPORTED_MODULE_0__.domAnimation,\n    ..._motion_features_drag_mjs__WEBPACK_IMPORTED_MODULE_1__.drag,\n    ..._motion_features_layout_mjs__WEBPACK_IMPORTED_MODULE_2__.layout,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9mZWF0dXJlcy1tYXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBc0Q7QUFDSTtBQUNGOztBQUV4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU8saUVBQVk7QUFDbkIsT0FBTywyREFBSTtBQUNYLE9BQU8sK0RBQU07QUFDYjs7QUFFa0IiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFxkb21cXGZlYXR1cmVzLW1heC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZHJhZyB9IGZyb20gJy4uLy4uL21vdGlvbi9mZWF0dXJlcy9kcmFnLm1qcyc7XG5pbXBvcnQgeyBsYXlvdXQgfSBmcm9tICcuLi8uLi9tb3Rpb24vZmVhdHVyZXMvbGF5b3V0Lm1qcyc7XG5pbXBvcnQgeyBkb21BbmltYXRpb24gfSBmcm9tICcuL2ZlYXR1cmVzLWFuaW1hdGlvbi5tanMnO1xuXG4vKipcbiAqIEBwdWJsaWNcbiAqL1xuY29uc3QgZG9tTWF4ID0ge1xuICAgIC4uLmRvbUFuaW1hdGlvbixcbiAgICAuLi5kcmFnLFxuICAgIC4uLmxheW91dCxcbn07XG5cbmV4cG9ydCB7IGRvbU1heCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/features-max.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/features-min.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/features-min.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   domMin: () => (/* binding */ domMin)\n/* harmony export */ });\n/* harmony import */ var _motion_features_animations_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../motion/features/animations.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animations.mjs\");\n/* harmony import */ var _create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./create-visual-element.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs\");\n\n\n\n/**\n * @public\n */\nconst domMin = {\n    renderer: _create_visual_element_mjs__WEBPACK_IMPORTED_MODULE_0__.createDomVisualElement,\n    ..._motion_features_animations_mjs__WEBPACK_IMPORTED_MODULE_1__.animations,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9mZWF0dXJlcy1taW4ubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRTtBQUNHOztBQUVyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsOEVBQXNCO0FBQ3BDLE9BQU8sdUVBQVU7QUFDakI7O0FBRWtCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFxmZWF0dXJlcy1taW4ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFuaW1hdGlvbnMgfSBmcm9tICcuLi8uLi9tb3Rpb24vZmVhdHVyZXMvYW5pbWF0aW9ucy5tanMnO1xuaW1wb3J0IHsgY3JlYXRlRG9tVmlzdWFsRWxlbWVudCB9IGZyb20gJy4vY3JlYXRlLXZpc3VhbC1lbGVtZW50Lm1qcyc7XG5cbi8qKlxuICogQHB1YmxpY1xuICovXG5jb25zdCBkb21NaW4gPSB7XG4gICAgcmVuZGVyZXI6IGNyZWF0ZURvbVZpc3VhbEVsZW1lbnQsXG4gICAgLi4uYW5pbWF0aW9ucyxcbn07XG5cbmV4cG9ydCB7IGRvbU1pbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/features-min.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachToAnimation: () => (/* binding */ attachToAnimation)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/get-timeline.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\");\n\n\n\nfunction attachToAnimation(animation, options) {\n    const timeline = (0,_utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__.getTimeline)(options);\n    return animation.attachTimeline({\n        timeline: options.target ? undefined : timeline,\n        observe: (valueAnimation) => {\n            valueAnimation.pause();\n            return (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.observeTimeline)((progress) => {\n                valueAnimation.time = valueAnimation.duration * progress;\n            }, timeline);\n        },\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvYXR0YWNoLWFuaW1hdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBQ1U7O0FBRXZEO0FBQ0EscUJBQXFCLG9FQUFXO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDJEQUFlO0FBQ2xDO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVCxLQUFLO0FBQ0w7O0FBRTZCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFxzY3JvbGxcXGF0dGFjaC1hbmltYXRpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG9ic2VydmVUaW1lbGluZSB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgZ2V0VGltZWxpbmUgfSBmcm9tICcuL3V0aWxzL2dldC10aW1lbGluZS5tanMnO1xuXG5mdW5jdGlvbiBhdHRhY2hUb0FuaW1hdGlvbihhbmltYXRpb24sIG9wdGlvbnMpIHtcbiAgICBjb25zdCB0aW1lbGluZSA9IGdldFRpbWVsaW5lKG9wdGlvbnMpO1xuICAgIHJldHVybiBhbmltYXRpb24uYXR0YWNoVGltZWxpbmUoe1xuICAgICAgICB0aW1lbGluZTogb3B0aW9ucy50YXJnZXQgPyB1bmRlZmluZWQgOiB0aW1lbGluZSxcbiAgICAgICAgb2JzZXJ2ZTogKHZhbHVlQW5pbWF0aW9uKSA9PiB7XG4gICAgICAgICAgICB2YWx1ZUFuaW1hdGlvbi5wYXVzZSgpO1xuICAgICAgICAgICAgcmV0dXJuIG9ic2VydmVUaW1lbGluZSgocHJvZ3Jlc3MpID0+IHtcbiAgICAgICAgICAgICAgICB2YWx1ZUFuaW1hdGlvbi50aW1lID0gdmFsdWVBbmltYXRpb24uZHVyYXRpb24gKiBwcm9ncmVzcztcbiAgICAgICAgICAgIH0sIHRpbWVsaW5lKTtcbiAgICAgICAgfSxcbiAgICB9KTtcbn1cblxuZXhwb3J0IHsgYXR0YWNoVG9BbmltYXRpb24gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachToFunction: () => (/* binding */ attachToFunction)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _track_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./track.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n/* harmony import */ var _utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/get-timeline.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\");\n\n\n\n\n/**\n * If the onScroll function has two arguments, it's expecting\n * more specific information about the scroll from scrollInfo.\n */\nfunction isOnScrollWithInfo(onScroll) {\n    return onScroll.length === 2;\n}\nfunction attachToFunction(onScroll, options) {\n    if (isOnScrollWithInfo(onScroll)) {\n        return (0,_track_mjs__WEBPACK_IMPORTED_MODULE_0__.scrollInfo)((info) => {\n            onScroll(info[options.axis].progress, info);\n        }, options);\n    }\n    else {\n        return (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.observeTimeline)(onScroll, (0,_utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_2__.getTimeline)(options));\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvYXR0YWNoLWZ1bmN0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBQ0o7QUFDYzs7QUFFdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzREFBVTtBQUN6QjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZUFBZSwyREFBZSxXQUFXLG9FQUFXO0FBQ3BEO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFxzY3JvbGxcXGF0dGFjaC1mdW5jdGlvbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgb2JzZXJ2ZVRpbWVsaW5lIH0gZnJvbSAnbW90aW9uLWRvbSc7XG5pbXBvcnQgeyBzY3JvbGxJbmZvIH0gZnJvbSAnLi90cmFjay5tanMnO1xuaW1wb3J0IHsgZ2V0VGltZWxpbmUgfSBmcm9tICcuL3V0aWxzL2dldC10aW1lbGluZS5tanMnO1xuXG4vKipcbiAqIElmIHRoZSBvblNjcm9sbCBmdW5jdGlvbiBoYXMgdHdvIGFyZ3VtZW50cywgaXQncyBleHBlY3RpbmdcbiAqIG1vcmUgc3BlY2lmaWMgaW5mb3JtYXRpb24gYWJvdXQgdGhlIHNjcm9sbCBmcm9tIHNjcm9sbEluZm8uXG4gKi9cbmZ1bmN0aW9uIGlzT25TY3JvbGxXaXRoSW5mbyhvblNjcm9sbCkge1xuICAgIHJldHVybiBvblNjcm9sbC5sZW5ndGggPT09IDI7XG59XG5mdW5jdGlvbiBhdHRhY2hUb0Z1bmN0aW9uKG9uU2Nyb2xsLCBvcHRpb25zKSB7XG4gICAgaWYgKGlzT25TY3JvbGxXaXRoSW5mbyhvblNjcm9sbCkpIHtcbiAgICAgICAgcmV0dXJuIHNjcm9sbEluZm8oKGluZm8pID0+IHtcbiAgICAgICAgICAgIG9uU2Nyb2xsKGluZm9bb3B0aW9ucy5heGlzXS5wcm9ncmVzcywgaW5mbyk7XG4gICAgICAgIH0sIG9wdGlvbnMpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIG9ic2VydmVUaW1lbGluZShvblNjcm9sbCwgZ2V0VGltZWxpbmUob3B0aW9ucykpO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgYXR0YWNoVG9GdW5jdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scroll: () => (/* binding */ scroll)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _attach_animation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./attach-animation.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs\");\n/* harmony import */ var _attach_function_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./attach-function.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs\");\n\n\n\n\nfunction scroll(onScroll, { axis = \"y\", container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n    const optionsWithDefaults = { axis, container, ...options };\n    return typeof onScroll === \"function\"\n        ? (0,_attach_function_mjs__WEBPACK_IMPORTED_MODULE_1__.attachToFunction)(onScroll, optionsWithDefaults)\n        : (0,_attach_animation_mjs__WEBPACK_IMPORTED_MODULE_2__.attachToAnimation)(onScroll, optionsWithDefaults);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0M7QUFDdUI7QUFDRjs7QUFFekQsNEJBQTRCLGdFQUFnRSxJQUFJO0FBQ2hHO0FBQ0EsZUFBZSw4Q0FBSTtBQUNuQixrQ0FBa0M7QUFDbEM7QUFDQSxVQUFVLHNFQUFnQjtBQUMxQixVQUFVLHdFQUFpQjtBQUMzQjs7QUFFa0IiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFxkb21cXHNjcm9sbFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vb3AgfSBmcm9tICdtb3Rpb24tdXRpbHMnO1xuaW1wb3J0IHsgYXR0YWNoVG9BbmltYXRpb24gfSBmcm9tICcuL2F0dGFjaC1hbmltYXRpb24ubWpzJztcbmltcG9ydCB7IGF0dGFjaFRvRnVuY3Rpb24gfSBmcm9tICcuL2F0dGFjaC1mdW5jdGlvbi5tanMnO1xuXG5mdW5jdGlvbiBzY3JvbGwob25TY3JvbGwsIHsgYXhpcyA9IFwieVwiLCBjb250YWluZXIgPSBkb2N1bWVudC5zY3JvbGxpbmdFbGVtZW50LCAuLi5vcHRpb25zIH0gPSB7fSkge1xuICAgIGlmICghY29udGFpbmVyKVxuICAgICAgICByZXR1cm4gbm9vcDtcbiAgICBjb25zdCBvcHRpb25zV2l0aERlZmF1bHRzID0geyBheGlzLCBjb250YWluZXIsIC4uLm9wdGlvbnMgfTtcbiAgICByZXR1cm4gdHlwZW9mIG9uU2Nyb2xsID09PSBcImZ1bmN0aW9uXCJcbiAgICAgICAgPyBhdHRhY2hUb0Z1bmN0aW9uKG9uU2Nyb2xsLCBvcHRpb25zV2l0aERlZmF1bHRzKVxuICAgICAgICA6IGF0dGFjaFRvQW5pbWF0aW9uKG9uU2Nyb2xsLCBvcHRpb25zV2l0aERlZmF1bHRzKTtcbn1cblxuZXhwb3J0IHsgc2Nyb2xsIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createScrollInfo: () => (/* binding */ createScrollInfo),\n/* harmony export */   updateScrollInfo: () => (/* binding */ updateScrollInfo)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n    current: 0,\n    offset: [],\n    progress: 0,\n    scrollLength: 0,\n    targetOffset: 0,\n    targetLength: 0,\n    containerLength: 0,\n    velocity: 0,\n});\nconst createScrollInfo = () => ({\n    time: 0,\n    x: createAxisInfo(),\n    y: createAxisInfo(),\n});\nconst keys = {\n    x: {\n        length: \"Width\",\n        position: \"Left\",\n    },\n    y: {\n        length: \"Height\",\n        position: \"Top\",\n    },\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n    const axis = info[axisName];\n    const { length, position } = keys[axisName];\n    const prev = axis.current;\n    const prevTime = info.time;\n    axis.current = element[`scroll${position}`];\n    axis.scrollLength = element[`scroll${length}`] - element[`client${length}`];\n    axis.offset.length = 0;\n    axis.offset[0] = 0;\n    axis.offset[1] = axis.scrollLength;\n    axis.progress = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.progress)(0, axis.scrollLength, axis.current);\n    const elapsed = time - prevTime;\n    axis.velocity =\n        elapsed > maxElapsed\n            ? 0\n            : (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.velocityPerSecond)(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n    updateAxisInfo(element, \"x\", info, time);\n    updateAxisInfo(element, \"y\", info, time);\n    info.time = time;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   namedEdges: () => (/* binding */ namedEdges),\n/* harmony export */   resolveEdge: () => (/* binding */ resolveEdge)\n/* harmony export */ });\nconst namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (edge in namedEdges) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (typeof edge === \"string\") {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (typeof edge === \"number\") {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffsets: () => (/* binding */ resolveOffsets)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _inset_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./inset.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\");\n/* harmony import */ var _offset_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offset.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\");\n/* harmony import */ var _presets_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./presets.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\");\n\n\n\n\n\n\nconst point = { x: 0, y: 0 };\nfunction getTargetSize(target) {\n    return \"getBBox\" in target && target.tagName !== \"svg\"\n        ? target.getBBox()\n        : { width: target.clientWidth, height: target.clientHeight };\n}\nfunction resolveOffsets(container, info, options) {\n    const { offset: offsetDefinition = _presets_mjs__WEBPACK_IMPORTED_MODULE_0__.ScrollOffset.All } = options;\n    const { target = container, axis = \"y\" } = options;\n    const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n    const inset = target !== container ? (0,_inset_mjs__WEBPACK_IMPORTED_MODULE_1__.calcInset)(target, container) : point;\n    /**\n     * Measure the target and container. If they're the same thing then we\n     * use the container's scrollWidth/Height as the target, from there\n     * all other calculations can remain the same.\n     */\n    const targetSize = target === container\n        ? { width: container.scrollWidth, height: container.scrollHeight }\n        : getTargetSize(target);\n    const containerSize = {\n        width: container.clientWidth,\n        height: container.clientHeight,\n    };\n    /**\n     * Reset the length of the resolved offset array rather than creating a new one.\n     * TODO: More reusable data structures for targetSize/containerSize would also be good.\n     */\n    info[axis].offset.length = 0;\n    /**\n     * Populate the offset array by resolving the user's offset definition into\n     * a list of pixel scroll offets.\n     */\n    let hasChanged = !info[axis].interpolate;\n    const numOffsets = offsetDefinition.length;\n    for (let i = 0; i < numOffsets; i++) {\n        const offset = (0,_offset_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffset)(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset[axis]);\n        if (!hasChanged && offset !== info[axis].interpolatorOffsets[i]) {\n            hasChanged = true;\n        }\n        info[axis].offset[i] = offset;\n    }\n    /**\n     * If the pixel scroll offsets have changed, create a new interpolator function\n     * to map scroll value into a progress.\n     */\n    if (hasChanged) {\n        info[axis].interpolate = (0,motion_dom__WEBPACK_IMPORTED_MODULE_3__.interpolate)(info[axis].offset, (0,motion_dom__WEBPACK_IMPORTED_MODULE_3__.defaultOffset)(offsetDefinition), { clamp: false });\n        info[axis].interpolatorOffsets = [...info[axis].offset];\n    }\n    info[axis].progress = (0,motion_utils__WEBPACK_IMPORTED_MODULE_4__.clamp)(0, 1, info[axis].interpolate(info[axis].current));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcInset: () => (/* binding */ calcInset)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n\n\nfunction calcInset(element, container) {\n    const inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(current)) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current.tagName === \"svg\") {\n            /**\n             * This isn't an ideal approach to measuring the offset of <svg /> tags.\n             * It would be preferable, given they behave like HTMLElements in most ways\n             * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n             * can't use .getBBox() like most SVG elements as these provide the offset\n             * relative to the SVG itself, which for <svg /> is usually 0x0.\n             */\n            const svgBoundingBox = current.getBoundingClientRect();\n            current = current.parentElement;\n            const parentBoundingBox = current.getBoundingClientRect();\n            inset.x += svgBoundingBox.left - parentBoundingBox.left;\n            inset.y += svgBoundingBox.top - parentBoundingBox.top;\n        }\n        else if (current instanceof SVGGraphicsElement) {\n            const { x, y } = current.getBBox();\n            inset.x += x;\n            inset.y += y;\n            let svg = null;\n            let parent = current.parentNode;\n            while (!svg) {\n                if (parent.tagName === \"svg\") {\n                    svg = parent;\n                }\n                parent = current.parentNode;\n            }\n            current = svg;\n        }\n        else {\n            break;\n        }\n    }\n    return inset;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffset: () => (/* binding */ resolveOffset)\n/* harmony export */ });\n/* harmony import */ var _edge_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edge.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\");\n\n\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n    let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n    let targetPoint = 0;\n    let containerPoint = 0;\n    if (typeof offset === \"number\") {\n        /**\n         * If we're provided offset: [0, 0.5, 1] then each number x should become\n         * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n         * and container etc.\n         */\n        offsetDefinition = [offset, offset];\n    }\n    else if (typeof offset === \"string\") {\n        offset = offset.trim();\n        if (offset.includes(\" \")) {\n            offsetDefinition = offset.split(\" \");\n        }\n        else {\n            /**\n             * If we're provided a definition like \"100px\" then we want to apply\n             * that only to the top of the target point, leaving the container at 0.\n             * Whereas a named offset like \"end\" should be applied to both.\n             */\n            offsetDefinition = [offset, _edge_mjs__WEBPACK_IMPORTED_MODULE_0__.namedEdges[offset] ? offset : `0`];\n        }\n    }\n    targetPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[0], targetLength, targetInset);\n    containerPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[1], containerLength);\n    return targetPoint - containerPoint;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollOffset: () => (/* binding */ ScrollOffset)\n/* harmony export */ });\nconst ScrollOffset = {\n    Enter: [\n        [0, 1],\n        [1, 1],\n    ],\n    Exit: [\n        [0, 0],\n        [1, 0],\n    ],\n    Any: [\n        [1, 0],\n        [0, 1],\n    ],\n    All: [\n        [0, 0],\n        [1, 1],\n    ],\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb2Zmc2V0cy9wcmVzZXRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxcc2Nyb2xsXFxvZmZzZXRzXFxwcmVzZXRzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBTY3JvbGxPZmZzZXQgPSB7XG4gICAgRW50ZXI6IFtcbiAgICAgICAgWzAsIDFdLFxuICAgICAgICBbMSwgMV0sXG4gICAgXSxcbiAgICBFeGl0OiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDBdLFxuICAgIF0sXG4gICAgQW55OiBbXG4gICAgICAgIFsxLCAwXSxcbiAgICAgICAgWzAsIDFdLFxuICAgIF0sXG4gICAgQWxsOiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDFdLFxuICAgIF0sXG59O1xuXG5leHBvcnQgeyBTY3JvbGxPZmZzZXQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOnScrollHandler: () => (/* binding */ createOnScrollHandler)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offsets/index.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\");\n\n\n\n\nfunction measure(container, target = container, info) {\n    /**\n     * Find inset of target within scrollable container\n     */\n    info.x.targetOffset = 0;\n    info.y.targetOffset = 0;\n    if (target !== container) {\n        let node = target;\n        while (node && node !== container) {\n            info.x.targetOffset += node.offsetLeft;\n            info.y.targetOffset += node.offsetTop;\n            node = node.offsetParent;\n        }\n    }\n    info.x.targetLength =\n        target === container ? target.scrollWidth : target.clientWidth;\n    info.y.targetLength =\n        target === container ? target.scrollHeight : target.clientHeight;\n    info.x.containerLength = container.clientWidth;\n    info.y.containerLength = container.clientHeight;\n    /**\n     * In development mode ensure scroll containers aren't position: static as this makes\n     * it difficult to measure their relative positions.\n     */\n    if (true) {\n        if (container && target && target !== container) {\n            (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.warnOnce)(getComputedStyle(container).position !== \"static\", \"Please ensure that the container has a non-static position, like 'relative', 'fixed', or 'absolute' to ensure scroll offset is calculated correctly.\");\n        }\n    }\n}\nfunction createOnScrollHandler(element, onScroll, info, options = {}) {\n    return {\n        measure: (time) => {\n            measure(element, options.target, info);\n            (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.updateScrollInfo)(element, info, time);\n            if (options.offset || options.target) {\n                (0,_offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffsets)(element, info, options);\n            }\n        },\n        notify: () => onScroll(info),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrollInfo: () => (/* binding */ scrollInfo)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./on-scroll-handler.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\");\n\n\n\n\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.createScrollInfo)();\n    const containerHandler = (0,_on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_2__.createOnScrollHandler)(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers) {\n                handler.measure(motion_dom__WEBPACK_IMPORTED_MODULE_3__.frameData.timestamp);\n            }\n            motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.preUpdate(notifyAll);\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers) {\n                handler.notify();\n            }\n        };\n        const listener = () => motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.read(measureAll);\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, (0,motion_dom__WEBPACK_IMPORTED_MODULE_3__.resize)(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n        listener();\n    }\n    const listener = scrollListeners.get(container);\n    motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.read(listener, false, true);\n    return () => {\n        (0,motion_dom__WEBPACK_IMPORTED_MODULE_3__.cancelFrame)(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            resizeListeners.get(container)?.();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTimeline: () => (/* binding */ getTimeline)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _track_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../track.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n\n\n\nconst timelineCache = new Map();\nfunction scrollTimelineFallback(options) {\n    const currentTime = { value: 0 };\n    const cancel = (0,_track_mjs__WEBPACK_IMPORTED_MODULE_0__.scrollInfo)((info) => {\n        currentTime.value = info[options.axis].progress * 100;\n    }, options);\n    return { currentTime, cancel };\n}\nfunction getTimeline({ source, container, ...options }) {\n    const { axis } = options;\n    if (source)\n        container = source;\n    const containerCache = timelineCache.get(container) ?? new Map();\n    timelineCache.set(container, containerCache);\n    const targetKey = options.target ?? \"self\";\n    const targetCache = containerCache.get(targetKey) ?? {};\n    const axisKey = axis + (options.offset ?? []).join(\",\");\n    if (!targetCache[axisKey]) {\n        targetCache[axisKey] =\n            !options.target && (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.supportsScrollTimeline)()\n                ? new ScrollTimeline({ source: container, axis })\n                : scrollTimelineFallback({ container, ...options });\n    }\n    return targetCache[axisKey];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/use-render.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/use-render.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUseRender: () => (/* binding */ createUseRender)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var _html_use_props_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../html/use-props.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/use-props.mjs\");\n/* harmony import */ var _svg_use_props_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../svg/use-props.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/use-props.mjs\");\n/* harmony import */ var _utils_filter_props_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/filter-props.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs\");\n/* harmony import */ var _utils_is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/is-svg-component.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs\");\n\n\n\n\n\n\n\nfunction createUseRender(forwardMotionProps = false) {\n    const useRender = (Component, props, ref, { latestValues }, isStatic) => {\n        const useVisualProps = (0,_utils_is_svg_component_mjs__WEBPACK_IMPORTED_MODULE_1__.isSVGComponent)(Component)\n            ? _svg_use_props_mjs__WEBPACK_IMPORTED_MODULE_2__.useSVGProps\n            : _html_use_props_mjs__WEBPACK_IMPORTED_MODULE_3__.useHTMLProps;\n        const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n        const filteredProps = (0,_utils_filter_props_mjs__WEBPACK_IMPORTED_MODULE_4__.filterProps)(props, typeof Component === \"string\", forwardMotionProps);\n        const elementProps = Component !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment\n            ? { ...filteredProps, ...visualProps, ref }\n            : {};\n        /**\n         * If component has been handed a motion value as its child,\n         * memoise its initial value and render that. Subsequent updates\n         * will be handled by the onChange handler\n         */\n        const { children } = props;\n        const renderedChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ((0,motion_dom__WEBPACK_IMPORTED_MODULE_5__.isMotionValue)(children) ? children.get() : children), [children]);\n        return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Component, {\n            ...elementProps,\n            children: renderedChildren,\n        });\n    };\n    return useRender;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/use-render.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   camelToDash: () => (/* binding */ camelToDash)\n/* harmony export */ });\n/**\n * Convert camelCase to dash-case properties.\n */\nconst camelToDash = (str) => str.replace(/([a-z])([A-Z])/gu, \"$1-$2\").toLowerCase();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9jYW1lbC10by1kYXNoLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRXVCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFx1dGlsc1xcY2FtZWwtdG8tZGFzaC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb252ZXJ0IGNhbWVsQ2FzZSB0byBkYXNoLWNhc2UgcHJvcGVydGllcy5cbiAqL1xuY29uc3QgY2FtZWxUb0Rhc2ggPSAoc3RyKSA9PiBzdHIucmVwbGFjZSgvKFthLXpdKShbQS1aXSkvZ3UsIFwiJDEtJDJcIikudG9Mb3dlckNhc2UoKTtcblxuZXhwb3J0IHsgY2FtZWxUb0Rhc2ggfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filterProps: () => (/* binding */ filterProps),\n/* harmony export */   loadExternalIsValidProp: () => (/* binding */ loadExternalIsValidProp)\n/* harmony export */ });\n/* harmony import */ var _motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../motion/utils/valid-prop.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs\");\n\n\nlet shouldForward = (key) => !(0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key);\nfunction loadExternalIsValidProp(isValidProp) {\n    if (typeof isValidProp !== \"function\")\n        return;\n    // Explicitly filter our events\n    shouldForward = (key) => key.startsWith(\"on\") ? !(0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key) : isValidProp(key);\n}\n/**\n * Emotion and Styled Components both allow users to pass through arbitrary props to their components\n * to dynamically generate CSS. They both use the `@emotion/is-prop-valid` package to determine which\n * of these should be passed to the underlying DOM node.\n *\n * However, when styling a Motion component `styled(motion.div)`, both packages pass through *all* props\n * as it's seen as an arbitrary component rather than a DOM node. Motion only allows arbitrary props\n * passed through the `custom` prop so it doesn't *need* the payload or computational overhead of\n * `@emotion/is-prop-valid`, however to fix this problem we need to use it.\n *\n * By making it an optionalDependency we can offer this functionality only in the situations where it's\n * actually required.\n */\ntry {\n    /**\n     * We attempt to import this package but require won't be defined in esm environments, in that case\n     * isPropValid will have to be provided via `MotionContext`. In a 6.0.0 this should probably be removed\n     * in favour of explicit injection.\n     */\n    loadExternalIsValidProp(require(\"@emotion/is-prop-valid\").default);\n}\ncatch {\n    // We don't need to actually do anything here - the fallback is the existing `isPropValid`.\n}\nfunction filterProps(props, isDom, forwardMotionProps) {\n    const filteredProps = {};\n    for (const key in props) {\n        /**\n         * values is considered a valid prop by Emotion, so if it's present\n         * this will be rendered out to the DOM unless explicitly filtered.\n         *\n         * We check the type as it could be used with the `feColorMatrix`\n         * element, which we support.\n         */\n        if (key === \"values\" && typeof props.values === \"object\")\n            continue;\n        if (shouldForward(key) ||\n            (forwardMotionProps === true && (0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key)) ||\n            (!isDom && !(0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key)) ||\n            // If trying to use native HTML drag events, forward drag listeners\n            (props[\"draggable\"] &&\n                key.startsWith(\"onDrag\"))) {\n            filteredProps[key] =\n                props[key];\n        }\n    }\n    return filteredProps;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSVGComponent: () => (/* binding */ isSVGComponent)\n/* harmony export */ });\n/* harmony import */ var _svg_lowercase_elements_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../svg/lowercase-elements.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs\");\n\n\nfunction isSVGComponent(Component) {\n    if (\n    /**\n     * If it's not a string, it's a custom React component. Currently we only support\n     * HTML custom React components.\n     */\n    typeof Component !== \"string\" ||\n        /**\n         * If it contains a dash, the element is a custom HTML webcomponent.\n         */\n        Component.includes(\"-\")) {\n        return false;\n    }\n    else if (\n    /**\n     * If it's in our list of lowercase SVG tags, it's an SVG component\n     */\n    _svg_lowercase_elements_mjs__WEBPACK_IMPORTED_MODULE_0__.lowercaseSVGElements.indexOf(Component) > -1 ||\n        /**\n         * If it contains a capital letter, it's an SVG component\n         */\n        /[A-Z]/u.test(Component)) {\n        return true;\n    }\n    return false;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inView: () => (/* binding */ inView)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry.target, entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (typeof onEnd === \"function\") {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTMLVisualElement: () => (/* binding */ HTMLVisualElement),\n/* harmony export */   getComputedStyle: () => (/* binding */ getComputedStyle)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _projection_utils_measure_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../projection/utils/measure.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/utils/measure.mjs\");\n/* harmony import */ var _dom_DOMVisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dom/DOMVisualElement.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs\");\n/* harmony import */ var _utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/build-styles.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs\");\n/* harmony import */ var _utils_render_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/render.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/render.mjs\");\n/* harmony import */ var _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/scrape-motion-values.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs\");\n\n\n\n\n\n\n\nfunction getComputedStyle(element) {\n    return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends _dom_DOMVisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__.DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"html\";\n        this.renderInstance = _utils_render_mjs__WEBPACK_IMPORTED_MODULE_1__.renderHTML;\n    }\n    readValueFromInstance(instance, key) {\n        if (motion_dom__WEBPACK_IMPORTED_MODULE_2__.transformProps.has(key)) {\n            return this.projection?.isProjecting\n                ? (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.defaultTransformValue)(key)\n                : (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.readTransformValue)(instance, key);\n        }\n        else {\n            const computedStyle = getComputedStyle(instance);\n            const value = ((0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.isCSSVariableName)(key)\n                ? computedStyle.getPropertyValue(key)\n                : computedStyle[key]) || 0;\n            return typeof value === \"string\" ? value.trim() : value;\n        }\n    }\n    measureInstanceViewportBox(instance, { transformPagePoint }) {\n        return (0,_projection_utils_measure_mjs__WEBPACK_IMPORTED_MODULE_3__.measureViewportBox)(instance, transformPagePoint);\n    }\n    build(renderState, latestValues, props) {\n        (0,_utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_4__.buildHTMLStyles)(renderState, latestValues, props.transformTemplate);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return (0,_utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_5__.scrapeMotionValuesFromProps)(props, prevProps, visualElement);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/config-motion.mjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/config-motion.mjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   htmlMotionConfig: () => (/* binding */ htmlMotionConfig)\n/* harmony export */ });\n/* harmony import */ var _motion_utils_use_visual_state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../motion/utils/use-visual-state.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs\");\n/* harmony import */ var _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/scrape-motion-values.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs\");\n/* harmony import */ var _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/create-render-state.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs\");\n\n\n\n\nconst htmlMotionConfig = {\n    useVisualState: (0,_motion_utils_use_visual_state_mjs__WEBPACK_IMPORTED_MODULE_0__.makeUseVisualState)({\n        scrapeMotionValuesFromProps: _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_1__.scrapeMotionValuesFromProps,\n        createRenderState: _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_2__.createHtmlRenderState,\n    }),\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvY29uZmlnLW1vdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE2RTtBQUNFO0FBQ1A7O0FBRXhFO0FBQ0Esb0JBQW9CLHNGQUFrQjtBQUN0QyxtQ0FBbUM7QUFDbkMsMkJBQTJCLGlGQUFxQjtBQUNoRCxLQUFLO0FBQ0w7O0FBRTRCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcaHRtbFxcY29uZmlnLW1vdGlvbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbWFrZVVzZVZpc3VhbFN0YXRlIH0gZnJvbSAnLi4vLi4vbW90aW9uL3V0aWxzL3VzZS12aXN1YWwtc3RhdGUubWpzJztcbmltcG9ydCB7IHNjcmFwZU1vdGlvblZhbHVlc0Zyb21Qcm9wcyB9IGZyb20gJy4vdXRpbHMvc2NyYXBlLW1vdGlvbi12YWx1ZXMubWpzJztcbmltcG9ydCB7IGNyZWF0ZUh0bWxSZW5kZXJTdGF0ZSB9IGZyb20gJy4vdXRpbHMvY3JlYXRlLXJlbmRlci1zdGF0ZS5tanMnO1xuXG5jb25zdCBodG1sTW90aW9uQ29uZmlnID0ge1xuICAgIHVzZVZpc3VhbFN0YXRlOiBtYWtlVXNlVmlzdWFsU3RhdGUoe1xuICAgICAgICBzY3JhcGVNb3Rpb25WYWx1ZXNGcm9tUHJvcHMsXG4gICAgICAgIGNyZWF0ZVJlbmRlclN0YXRlOiBjcmVhdGVIdG1sUmVuZGVyU3RhdGUsXG4gICAgfSksXG59O1xuXG5leHBvcnQgeyBodG1sTW90aW9uQ29uZmlnIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/config-motion.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/use-props.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/use-props.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copyRawValuesOnly: () => (/* binding */ copyRawValuesOnly),\n/* harmony export */   useHTMLProps: () => (/* binding */ useHTMLProps)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var _motion_utils_is_forced_motion_value_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../motion/utils/is-forced-motion-value.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs\");\n/* harmony import */ var _utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/build-styles.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs\");\n/* harmony import */ var _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/create-render-state.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs\");\n\n\n\n\n\n\nfunction copyRawValuesOnly(target, source, props) {\n    for (const key in source) {\n        if (!(0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.isMotionValue)(source[key]) && !(0,_motion_utils_is_forced_motion_value_mjs__WEBPACK_IMPORTED_MODULE_2__.isForcedMotionValue)(key, props)) {\n            target[key] = source[key];\n        }\n    }\n}\nfunction useInitialMotionValues({ transformTemplate }, visualState) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        const state = (0,_utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_3__.createHtmlRenderState)();\n        (0,_utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_4__.buildHTMLStyles)(state, visualState, transformTemplate);\n        return Object.assign({}, state.vars, state.style);\n    }, [visualState]);\n}\nfunction useStyle(props, visualState) {\n    const styleProp = props.style || {};\n    const style = {};\n    /**\n     * Copy non-Motion Values straight into style\n     */\n    copyRawValuesOnly(style, styleProp, props);\n    Object.assign(style, useInitialMotionValues(props, visualState));\n    return style;\n}\nfunction useHTMLProps(props, visualState) {\n    // The `any` isn't ideal but it is the type of createElement props argument\n    const htmlProps = {};\n    const style = useStyle(props, visualState);\n    if (props.drag && props.dragListener !== false) {\n        // Disable the ghost element when a user drags\n        htmlProps.draggable = false;\n        // Disable text selection\n        style.userSelect =\n            style.WebkitUserSelect =\n                style.WebkitTouchCallout =\n                    \"none\";\n        // Disable scrolling on the draggable direction\n        style.touchAction =\n            props.drag === true\n                ? \"none\"\n                : `pan-${props.drag === \"x\" ? \"y\" : \"x\"}`;\n    }\n    if (props.tabIndex === undefined &&\n        (props.onTap || props.onTapStart || props.whileTap)) {\n        htmlProps.tabIndex = 0;\n    }\n    htmlProps.style = style;\n    return htmlProps;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXNlLXByb3BzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTJDO0FBQ1g7QUFDb0Q7QUFDekI7QUFDYTs7QUFFeEU7QUFDQTtBQUNBLGFBQWEseURBQWEsa0JBQWtCLDZGQUFtQjtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxtQkFBbUI7QUFDckQsV0FBVyw4Q0FBTztBQUNsQixzQkFBc0IscUZBQXFCO0FBQzNDLFFBQVEsd0VBQWU7QUFDdkIsK0JBQStCO0FBQy9CLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsK0JBQStCO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTJDIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcaHRtbFxcdXNlLXByb3BzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc01vdGlvblZhbHVlIH0gZnJvbSAnbW90aW9uLWRvbSc7XG5pbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgaXNGb3JjZWRNb3Rpb25WYWx1ZSB9IGZyb20gJy4uLy4uL21vdGlvbi91dGlscy9pcy1mb3JjZWQtbW90aW9uLXZhbHVlLm1qcyc7XG5pbXBvcnQgeyBidWlsZEhUTUxTdHlsZXMgfSBmcm9tICcuL3V0aWxzL2J1aWxkLXN0eWxlcy5tanMnO1xuaW1wb3J0IHsgY3JlYXRlSHRtbFJlbmRlclN0YXRlIH0gZnJvbSAnLi91dGlscy9jcmVhdGUtcmVuZGVyLXN0YXRlLm1qcyc7XG5cbmZ1bmN0aW9uIGNvcHlSYXdWYWx1ZXNPbmx5KHRhcmdldCwgc291cmNlLCBwcm9wcykge1xuICAgIGZvciAoY29uc3Qga2V5IGluIHNvdXJjZSkge1xuICAgICAgICBpZiAoIWlzTW90aW9uVmFsdWUoc291cmNlW2tleV0pICYmICFpc0ZvcmNlZE1vdGlvblZhbHVlKGtleSwgcHJvcHMpKSB7XG4gICAgICAgICAgICB0YXJnZXRba2V5XSA9IHNvdXJjZVtrZXldO1xuICAgICAgICB9XG4gICAgfVxufVxuZnVuY3Rpb24gdXNlSW5pdGlhbE1vdGlvblZhbHVlcyh7IHRyYW5zZm9ybVRlbXBsYXRlIH0sIHZpc3VhbFN0YXRlKSB7XG4gICAgcmV0dXJuIHVzZU1lbW8oKCkgPT4ge1xuICAgICAgICBjb25zdCBzdGF0ZSA9IGNyZWF0ZUh0bWxSZW5kZXJTdGF0ZSgpO1xuICAgICAgICBidWlsZEhUTUxTdHlsZXMoc3RhdGUsIHZpc3VhbFN0YXRlLCB0cmFuc2Zvcm1UZW1wbGF0ZSk7XG4gICAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKHt9LCBzdGF0ZS52YXJzLCBzdGF0ZS5zdHlsZSk7XG4gICAgfSwgW3Zpc3VhbFN0YXRlXSk7XG59XG5mdW5jdGlvbiB1c2VTdHlsZShwcm9wcywgdmlzdWFsU3RhdGUpIHtcbiAgICBjb25zdCBzdHlsZVByb3AgPSBwcm9wcy5zdHlsZSB8fCB7fTtcbiAgICBjb25zdCBzdHlsZSA9IHt9O1xuICAgIC8qKlxuICAgICAqIENvcHkgbm9uLU1vdGlvbiBWYWx1ZXMgc3RyYWlnaHQgaW50byBzdHlsZVxuICAgICAqL1xuICAgIGNvcHlSYXdWYWx1ZXNPbmx5KHN0eWxlLCBzdHlsZVByb3AsIHByb3BzKTtcbiAgICBPYmplY3QuYXNzaWduKHN0eWxlLCB1c2VJbml0aWFsTW90aW9uVmFsdWVzKHByb3BzLCB2aXN1YWxTdGF0ZSkpO1xuICAgIHJldHVybiBzdHlsZTtcbn1cbmZ1bmN0aW9uIHVzZUhUTUxQcm9wcyhwcm9wcywgdmlzdWFsU3RhdGUpIHtcbiAgICAvLyBUaGUgYGFueWAgaXNuJ3QgaWRlYWwgYnV0IGl0IGlzIHRoZSB0eXBlIG9mIGNyZWF0ZUVsZW1lbnQgcHJvcHMgYXJndW1lbnRcbiAgICBjb25zdCBodG1sUHJvcHMgPSB7fTtcbiAgICBjb25zdCBzdHlsZSA9IHVzZVN0eWxlKHByb3BzLCB2aXN1YWxTdGF0ZSk7XG4gICAgaWYgKHByb3BzLmRyYWcgJiYgcHJvcHMuZHJhZ0xpc3RlbmVyICE9PSBmYWxzZSkge1xuICAgICAgICAvLyBEaXNhYmxlIHRoZSBnaG9zdCBlbGVtZW50IHdoZW4gYSB1c2VyIGRyYWdzXG4gICAgICAgIGh0bWxQcm9wcy5kcmFnZ2FibGUgPSBmYWxzZTtcbiAgICAgICAgLy8gRGlzYWJsZSB0ZXh0IHNlbGVjdGlvblxuICAgICAgICBzdHlsZS51c2VyU2VsZWN0ID1cbiAgICAgICAgICAgIHN0eWxlLldlYmtpdFVzZXJTZWxlY3QgPVxuICAgICAgICAgICAgICAgIHN0eWxlLldlYmtpdFRvdWNoQ2FsbG91dCA9XG4gICAgICAgICAgICAgICAgICAgIFwibm9uZVwiO1xuICAgICAgICAvLyBEaXNhYmxlIHNjcm9sbGluZyBvbiB0aGUgZHJhZ2dhYmxlIGRpcmVjdGlvblxuICAgICAgICBzdHlsZS50b3VjaEFjdGlvbiA9XG4gICAgICAgICAgICBwcm9wcy5kcmFnID09PSB0cnVlXG4gICAgICAgICAgICAgICAgPyBcIm5vbmVcIlxuICAgICAgICAgICAgICAgIDogYHBhbi0ke3Byb3BzLmRyYWcgPT09IFwieFwiID8gXCJ5XCIgOiBcInhcIn1gO1xuICAgIH1cbiAgICBpZiAocHJvcHMudGFiSW5kZXggPT09IHVuZGVmaW5lZCAmJlxuICAgICAgICAocHJvcHMub25UYXAgfHwgcHJvcHMub25UYXBTdGFydCB8fCBwcm9wcy53aGlsZVRhcCkpIHtcbiAgICAgICAgaHRtbFByb3BzLnRhYkluZGV4ID0gMDtcbiAgICB9XG4gICAgaHRtbFByb3BzLnN0eWxlID0gc3R5bGU7XG4gICAgcmV0dXJuIGh0bWxQcm9wcztcbn1cblxuZXhwb3J0IHsgY29weVJhd1ZhbHVlc09ubHksIHVzZUhUTUxQcm9wcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/use-props.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildHTMLStyles: () => (/* binding */ buildHTMLStyles)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _build_transform_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./build-transform.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs\");\n\n\n\nfunction buildHTMLStyles(state, latestValues, transformTemplate) {\n    const { style, vars, transformOrigin } = state;\n    // Track whether we encounter any transform or transformOrigin values.\n    let hasTransform = false;\n    let hasTransformOrigin = false;\n    /**\n     * Loop over all our latest animated values and decide whether to handle them\n     * as a style or CSS variable.\n     *\n     * Transforms and transform origins are kept separately for further processing.\n     */\n    for (const key in latestValues) {\n        const value = latestValues[key];\n        if (motion_dom__WEBPACK_IMPORTED_MODULE_0__.transformProps.has(key)) {\n            // If this is a transform, flag to enable further transform processing\n            hasTransform = true;\n            continue;\n        }\n        else if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isCSSVariableName)(key)) {\n            vars[key] = value;\n            continue;\n        }\n        else {\n            // Convert the value to its default value type, ie 0 -> \"0px\"\n            const valueAsType = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.getValueAsType)(value, motion_dom__WEBPACK_IMPORTED_MODULE_0__.numberValueTypes[key]);\n            if (key.startsWith(\"origin\")) {\n                // If this is a transform origin, flag and enable further transform-origin processing\n                hasTransformOrigin = true;\n                transformOrigin[key] =\n                    valueAsType;\n            }\n            else {\n                style[key] = valueAsType;\n            }\n        }\n    }\n    if (!latestValues.transform) {\n        if (hasTransform || transformTemplate) {\n            style.transform = (0,_build_transform_mjs__WEBPACK_IMPORTED_MODULE_1__.buildTransform)(latestValues, state.transform, transformTemplate);\n        }\n        else if (style.transform) {\n            /**\n             * If we have previously created a transform but currently don't have any,\n             * reset transform style to none.\n             */\n            style.transform = \"none\";\n        }\n    }\n    /**\n     * Build a transformOrigin style. Uses the same defaults as the browser for\n     * undefined origins.\n     */\n    if (hasTransformOrigin) {\n        const { originX = \"50%\", originY = \"50%\", originZ = 0, } = transformOrigin;\n        style.transformOrigin = `${originX} ${originY} ${originZ}`;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXRpbHMvYnVpbGQtc3R5bGVzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUc7QUFDMUM7O0FBRXZEO0FBQ0EsWUFBWSwrQkFBK0I7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksc0RBQWM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsNkRBQWlCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsMERBQWMsUUFBUSx3REFBZ0I7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsb0VBQWM7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixpREFBaUQ7QUFDakUsbUNBQW1DLFNBQVMsRUFBRSxTQUFTLEVBQUUsUUFBUTtBQUNqRTtBQUNBOztBQUUyQiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGh0bWxcXHV0aWxzXFxidWlsZC1zdHlsZXMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHRyYW5zZm9ybVByb3BzLCBpc0NTU1ZhcmlhYmxlTmFtZSwgZ2V0VmFsdWVBc1R5cGUsIG51bWJlclZhbHVlVHlwZXMgfSBmcm9tICdtb3Rpb24tZG9tJztcbmltcG9ydCB7IGJ1aWxkVHJhbnNmb3JtIH0gZnJvbSAnLi9idWlsZC10cmFuc2Zvcm0ubWpzJztcblxuZnVuY3Rpb24gYnVpbGRIVE1MU3R5bGVzKHN0YXRlLCBsYXRlc3RWYWx1ZXMsIHRyYW5zZm9ybVRlbXBsYXRlKSB7XG4gICAgY29uc3QgeyBzdHlsZSwgdmFycywgdHJhbnNmb3JtT3JpZ2luIH0gPSBzdGF0ZTtcbiAgICAvLyBUcmFjayB3aGV0aGVyIHdlIGVuY291bnRlciBhbnkgdHJhbnNmb3JtIG9yIHRyYW5zZm9ybU9yaWdpbiB2YWx1ZXMuXG4gICAgbGV0IGhhc1RyYW5zZm9ybSA9IGZhbHNlO1xuICAgIGxldCBoYXNUcmFuc2Zvcm1PcmlnaW4gPSBmYWxzZTtcbiAgICAvKipcbiAgICAgKiBMb29wIG92ZXIgYWxsIG91ciBsYXRlc3QgYW5pbWF0ZWQgdmFsdWVzIGFuZCBkZWNpZGUgd2hldGhlciB0byBoYW5kbGUgdGhlbVxuICAgICAqIGFzIGEgc3R5bGUgb3IgQ1NTIHZhcmlhYmxlLlxuICAgICAqXG4gICAgICogVHJhbnNmb3JtcyBhbmQgdHJhbnNmb3JtIG9yaWdpbnMgYXJlIGtlcHQgc2VwYXJhdGVseSBmb3IgZnVydGhlciBwcm9jZXNzaW5nLlxuICAgICAqL1xuICAgIGZvciAoY29uc3Qga2V5IGluIGxhdGVzdFZhbHVlcykge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IGxhdGVzdFZhbHVlc1trZXldO1xuICAgICAgICBpZiAodHJhbnNmb3JtUHJvcHMuaGFzKGtleSkpIHtcbiAgICAgICAgICAgIC8vIElmIHRoaXMgaXMgYSB0cmFuc2Zvcm0sIGZsYWcgdG8gZW5hYmxlIGZ1cnRoZXIgdHJhbnNmb3JtIHByb2Nlc3NpbmdcbiAgICAgICAgICAgIGhhc1RyYW5zZm9ybSA9IHRydWU7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpc0NTU1ZhcmlhYmxlTmFtZShrZXkpKSB7XG4gICAgICAgICAgICB2YXJzW2tleV0gPSB2YWx1ZTtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgLy8gQ29udmVydCB0aGUgdmFsdWUgdG8gaXRzIGRlZmF1bHQgdmFsdWUgdHlwZSwgaWUgMCAtPiBcIjBweFwiXG4gICAgICAgICAgICBjb25zdCB2YWx1ZUFzVHlwZSA9IGdldFZhbHVlQXNUeXBlKHZhbHVlLCBudW1iZXJWYWx1ZVR5cGVzW2tleV0pO1xuICAgICAgICAgICAgaWYgKGtleS5zdGFydHNXaXRoKFwib3JpZ2luXCIpKSB7XG4gICAgICAgICAgICAgICAgLy8gSWYgdGhpcyBpcyBhIHRyYW5zZm9ybSBvcmlnaW4sIGZsYWcgYW5kIGVuYWJsZSBmdXJ0aGVyIHRyYW5zZm9ybS1vcmlnaW4gcHJvY2Vzc2luZ1xuICAgICAgICAgICAgICAgIGhhc1RyYW5zZm9ybU9yaWdpbiA9IHRydWU7XG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtT3JpZ2luW2tleV0gPVxuICAgICAgICAgICAgICAgICAgICB2YWx1ZUFzVHlwZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHN0eWxlW2tleV0gPSB2YWx1ZUFzVHlwZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoIWxhdGVzdFZhbHVlcy50cmFuc2Zvcm0pIHtcbiAgICAgICAgaWYgKGhhc1RyYW5zZm9ybSB8fCB0cmFuc2Zvcm1UZW1wbGF0ZSkge1xuICAgICAgICAgICAgc3R5bGUudHJhbnNmb3JtID0gYnVpbGRUcmFuc2Zvcm0obGF0ZXN0VmFsdWVzLCBzdGF0ZS50cmFuc2Zvcm0sIHRyYW5zZm9ybVRlbXBsYXRlKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChzdHlsZS50cmFuc2Zvcm0pIHtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogSWYgd2UgaGF2ZSBwcmV2aW91c2x5IGNyZWF0ZWQgYSB0cmFuc2Zvcm0gYnV0IGN1cnJlbnRseSBkb24ndCBoYXZlIGFueSxcbiAgICAgICAgICAgICAqIHJlc2V0IHRyYW5zZm9ybSBzdHlsZSB0byBub25lLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBzdHlsZS50cmFuc2Zvcm0gPSBcIm5vbmVcIjtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBCdWlsZCBhIHRyYW5zZm9ybU9yaWdpbiBzdHlsZS4gVXNlcyB0aGUgc2FtZSBkZWZhdWx0cyBhcyB0aGUgYnJvd3NlciBmb3JcbiAgICAgKiB1bmRlZmluZWQgb3JpZ2lucy5cbiAgICAgKi9cbiAgICBpZiAoaGFzVHJhbnNmb3JtT3JpZ2luKSB7XG4gICAgICAgIGNvbnN0IHsgb3JpZ2luWCA9IFwiNTAlXCIsIG9yaWdpblkgPSBcIjUwJVwiLCBvcmlnaW5aID0gMCwgfSA9IHRyYW5zZm9ybU9yaWdpbjtcbiAgICAgICAgc3R5bGUudHJhbnNmb3JtT3JpZ2luID0gYCR7b3JpZ2luWH0gJHtvcmlnaW5ZfSAke29yaWdpblp9YDtcbiAgICB9XG59XG5cbmV4cG9ydCB7IGJ1aWxkSFRNTFN0eWxlcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildTransform: () => (/* binding */ buildTransform)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n\n\nconst translateAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n    transformPerspective: \"perspective\",\n};\nconst numTransforms = motion_dom__WEBPACK_IMPORTED_MODULE_0__.transformPropOrder.length;\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform(latestValues, transform, transformTemplate) {\n    // The transform string we're going to build into.\n    let transformString = \"\";\n    let transformIsDefault = true;\n    /**\n     * Loop over all possible transforms in order, adding the ones that\n     * are present to the transform string.\n     */\n    for (let i = 0; i < numTransforms; i++) {\n        const key = motion_dom__WEBPACK_IMPORTED_MODULE_0__.transformPropOrder[i];\n        const value = latestValues[key];\n        if (value === undefined)\n            continue;\n        let valueIsDefault = true;\n        if (typeof value === \"number\") {\n            valueIsDefault = value === (key.startsWith(\"scale\") ? 1 : 0);\n        }\n        else {\n            valueIsDefault = parseFloat(value) === 0;\n        }\n        if (!valueIsDefault || transformTemplate) {\n            const valueAsType = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.getValueAsType)(value, motion_dom__WEBPACK_IMPORTED_MODULE_0__.numberValueTypes[key]);\n            if (!valueIsDefault) {\n                transformIsDefault = false;\n                const transformName = translateAlias[key] || key;\n                transformString += `${transformName}(${valueAsType}) `;\n            }\n            if (transformTemplate) {\n                transform[key] = valueAsType;\n            }\n        }\n    }\n    transformString = transformString.trim();\n    // If we have a custom `transform` template, pass our transform values and\n    // generated transformString to that before returning\n    if (transformTemplate) {\n        transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n    }\n    else if (transformIsDefault) {\n        transformString = \"none\";\n    }\n    return transformString;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHtmlRenderState: () => (/* binding */ createHtmlRenderState)\n/* harmony export */ });\nconst createHtmlRenderState = () => ({\n    style: {},\n    transform: {},\n    transformOrigin: {},\n    vars: {},\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXRpbHMvY3JlYXRlLXJlbmRlci1zdGF0ZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsYUFBYTtBQUNiLGlCQUFpQjtBQUNqQix1QkFBdUI7QUFDdkIsWUFBWTtBQUNaLENBQUM7O0FBRWdDIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcaHRtbFxcdXRpbHNcXGNyZWF0ZS1yZW5kZXItc3RhdGUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGNyZWF0ZUh0bWxSZW5kZXJTdGF0ZSA9ICgpID0+ICh7XG4gICAgc3R5bGU6IHt9LFxuICAgIHRyYW5zZm9ybToge30sXG4gICAgdHJhbnNmb3JtT3JpZ2luOiB7fSxcbiAgICB2YXJzOiB7fSxcbn0pO1xuXG5leHBvcnQgeyBjcmVhdGVIdG1sUmVuZGVyU3RhdGUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/render.mjs":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/render.mjs ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   renderHTML: () => (/* binding */ renderHTML)\n/* harmony export */ });\nfunction renderHTML(element, { style, vars }, styleProp, projection) {\n    const elementStyle = element.style;\n    let key;\n    for (key in style) {\n        // CSSStyleDeclaration has [index: number]: string; in the types, so we use that as key type.\n        elementStyle[key] = style[key];\n    }\n    // Write projection styles directly to element style\n    projection?.applyProjectionStyles(elementStyle, styleProp);\n    for (key in vars) {\n        // Loop over any CSS variables and assign those.\n        // They can only be assigned using `setProperty`.\n        elementStyle.setProperty(key, vars[key]);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXRpbHMvcmVuZGVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsK0JBQStCLGFBQWE7QUFDNUM7QUFDQTtBQUNBO0FBQ0EsNERBQTREO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzQiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGh0bWxcXHV0aWxzXFxyZW5kZXIubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHJlbmRlckhUTUwoZWxlbWVudCwgeyBzdHlsZSwgdmFycyB9LCBzdHlsZVByb3AsIHByb2plY3Rpb24pIHtcbiAgICBjb25zdCBlbGVtZW50U3R5bGUgPSBlbGVtZW50LnN0eWxlO1xuICAgIGxldCBrZXk7XG4gICAgZm9yIChrZXkgaW4gc3R5bGUpIHtcbiAgICAgICAgLy8gQ1NTU3R5bGVEZWNsYXJhdGlvbiBoYXMgW2luZGV4OiBudW1iZXJdOiBzdHJpbmc7IGluIHRoZSB0eXBlcywgc28gd2UgdXNlIHRoYXQgYXMga2V5IHR5cGUuXG4gICAgICAgIGVsZW1lbnRTdHlsZVtrZXldID0gc3R5bGVba2V5XTtcbiAgICB9XG4gICAgLy8gV3JpdGUgcHJvamVjdGlvbiBzdHlsZXMgZGlyZWN0bHkgdG8gZWxlbWVudCBzdHlsZVxuICAgIHByb2plY3Rpb24/LmFwcGx5UHJvamVjdGlvblN0eWxlcyhlbGVtZW50U3R5bGUsIHN0eWxlUHJvcCk7XG4gICAgZm9yIChrZXkgaW4gdmFycykge1xuICAgICAgICAvLyBMb29wIG92ZXIgYW55IENTUyB2YXJpYWJsZXMgYW5kIGFzc2lnbiB0aG9zZS5cbiAgICAgICAgLy8gVGhleSBjYW4gb25seSBiZSBhc3NpZ25lZCB1c2luZyBgc2V0UHJvcGVydHlgLlxuICAgICAgICBlbGVtZW50U3R5bGUuc2V0UHJvcGVydHkoa2V5LCB2YXJzW2tleV0pO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgcmVuZGVySFRNTCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/render.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrapeMotionValuesFromProps: () => (/* binding */ scrapeMotionValuesFromProps)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _motion_utils_is_forced_motion_value_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../motion/utils/is-forced-motion-value.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs\");\n\n\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const { style } = props;\n    const newValues = {};\n    for (const key in style) {\n        if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(style[key]) ||\n            (prevProps.style &&\n                (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(prevProps.style[key])) ||\n            (0,_motion_utils_is_forced_motion_value_mjs__WEBPACK_IMPORTED_MODULE_1__.isForcedMotionValue)(key, props) ||\n            visualElement?.getValue(key)?.liveStyle !== undefined) {\n            newValues[key] = style[key];\n        }\n    }\n    return newValues;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2h0bWwvdXRpbHMvc2NyYXBlLW1vdGlvbi12YWx1ZXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyQztBQUM0Qzs7QUFFdkY7QUFDQSxZQUFZLFFBQVE7QUFDcEI7QUFDQTtBQUNBLFlBQVkseURBQWE7QUFDekI7QUFDQSxnQkFBZ0IseURBQWE7QUFDN0IsWUFBWSw2RkFBbUI7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGh0bWxcXHV0aWxzXFxzY3JhcGUtbW90aW9uLXZhbHVlcy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNNb3Rpb25WYWx1ZSB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgaXNGb3JjZWRNb3Rpb25WYWx1ZSB9IGZyb20gJy4uLy4uLy4uL21vdGlvbi91dGlscy9pcy1mb3JjZWQtbW90aW9uLXZhbHVlLm1qcyc7XG5cbmZ1bmN0aW9uIHNjcmFwZU1vdGlvblZhbHVlc0Zyb21Qcm9wcyhwcm9wcywgcHJldlByb3BzLCB2aXN1YWxFbGVtZW50KSB7XG4gICAgY29uc3QgeyBzdHlsZSB9ID0gcHJvcHM7XG4gICAgY29uc3QgbmV3VmFsdWVzID0ge307XG4gICAgZm9yIChjb25zdCBrZXkgaW4gc3R5bGUpIHtcbiAgICAgICAgaWYgKGlzTW90aW9uVmFsdWUoc3R5bGVba2V5XSkgfHxcbiAgICAgICAgICAgIChwcmV2UHJvcHMuc3R5bGUgJiZcbiAgICAgICAgICAgICAgICBpc01vdGlvblZhbHVlKHByZXZQcm9wcy5zdHlsZVtrZXldKSkgfHxcbiAgICAgICAgICAgIGlzRm9yY2VkTW90aW9uVmFsdWUoa2V5LCBwcm9wcykgfHxcbiAgICAgICAgICAgIHZpc3VhbEVsZW1lbnQ/LmdldFZhbHVlKGtleSk/LmxpdmVTdHlsZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBuZXdWYWx1ZXNba2V5XSA9IHN0eWxlW2tleV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG5ld1ZhbHVlcztcbn1cblxuZXhwb3J0IHsgc2NyYXBlTW90aW9uVmFsdWVzRnJvbVByb3BzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/object/ObjectVisualElement.mjs":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/object/ObjectVisualElement.mjs ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ObjectVisualElement: () => (/* binding */ ObjectVisualElement)\n/* harmony export */ });\n/* harmony import */ var _projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../projection/geometry/models.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/models.mjs\");\n/* harmony import */ var _VisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../VisualElement.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/VisualElement.mjs\");\n\n\n\nfunction isObjectKey(key, object) {\n    return key in object;\n}\nclass ObjectVisualElement extends _VisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__.VisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"object\";\n    }\n    readValueFromInstance(instance, key) {\n        if (isObjectKey(key, instance)) {\n            const value = instance[key];\n            if (typeof value === \"string\" || typeof value === \"number\") {\n                return value;\n            }\n        }\n        return undefined;\n    }\n    getBaseTargetFromProps() {\n        return undefined;\n    }\n    removeValueFromRenderState(key, renderState) {\n        delete renderState.output[key];\n    }\n    measureInstanceViewportBox() {\n        return (0,_projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_1__.createBox)();\n    }\n    build(renderState, latestValues) {\n        Object.assign(renderState.output, latestValues);\n    }\n    renderInstance(instance, { output }) {\n        Object.assign(instance, output);\n    }\n    sortInstanceNodePosition() {\n        return 0;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/object/ObjectVisualElement.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/store.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/store.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   visualElementStore: () => (/* binding */ visualElementStore)\n/* harmony export */ });\nconst visualElementStore = new WeakMap();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N0b3JlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRThCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcc3RvcmUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHZpc3VhbEVsZW1lbnRTdG9yZSA9IG5ldyBXZWFrTWFwKCk7XG5cbmV4cG9ydCB7IHZpc3VhbEVsZW1lbnRTdG9yZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/store.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SVGVisualElement: () => (/* binding */ SVGVisualElement)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../projection/geometry/models.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/models.mjs\");\n/* harmony import */ var _dom_DOMVisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dom/DOMVisualElement.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs\");\n/* harmony import */ var _dom_utils_camel_to_dash_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../dom/utils/camel-to-dash.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs\");\n/* harmony import */ var _utils_build_attrs_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/build-attrs.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs\");\n/* harmony import */ var _utils_camel_case_attrs_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/camel-case-attrs.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs\");\n/* harmony import */ var _utils_is_svg_tag_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/is-svg-tag.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs\");\n/* harmony import */ var _utils_render_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/render.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/render.mjs\");\n/* harmony import */ var _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/scrape-motion-values.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs\");\n\n\n\n\n\n\n\n\n\n\nclass SVGVisualElement extends _dom_DOMVisualElement_mjs__WEBPACK_IMPORTED_MODULE_0__.DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"svg\";\n        this.isSVGTag = false;\n        this.measureInstanceViewportBox = _projection_geometry_models_mjs__WEBPACK_IMPORTED_MODULE_1__.createBox;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props[key];\n    }\n    readValueFromInstance(instance, key) {\n        if (motion_dom__WEBPACK_IMPORTED_MODULE_2__.transformProps.has(key)) {\n            const defaultType = (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.getDefaultValueType)(key);\n            return defaultType ? defaultType.default || 0 : 0;\n        }\n        key = !_utils_camel_case_attrs_mjs__WEBPACK_IMPORTED_MODULE_3__.camelCaseAttributes.has(key) ? (0,_dom_utils_camel_to_dash_mjs__WEBPACK_IMPORTED_MODULE_4__.camelToDash)(key) : key;\n        return instance.getAttribute(key);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return (0,_utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_5__.scrapeMotionValuesFromProps)(props, prevProps, visualElement);\n    }\n    build(renderState, latestValues, props) {\n        (0,_utils_build_attrs_mjs__WEBPACK_IMPORTED_MODULE_6__.buildSVGAttrs)(renderState, latestValues, this.isSVGTag, props.transformTemplate, props.style);\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        (0,_utils_render_mjs__WEBPACK_IMPORTED_MODULE_7__.renderSVG)(instance, renderState, styleProp, projection);\n    }\n    mount(instance) {\n        this.isSVGTag = (0,_utils_is_svg_tag_mjs__WEBPACK_IMPORTED_MODULE_8__.isSVGTag)(instance.tagName);\n        super.mount(instance);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svgMotionConfig: () => (/* binding */ svgMotionConfig)\n/* harmony export */ });\n/* harmony import */ var _motion_utils_use_visual_state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../motion/utils/use-visual-state.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs\");\n/* harmony import */ var _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/create-render-state.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs\");\n/* harmony import */ var _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/scrape-motion-values.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs\");\n\n\n\n\nconst svgMotionConfig = {\n    useVisualState: (0,_motion_utils_use_visual_state_mjs__WEBPACK_IMPORTED_MODULE_0__.makeUseVisualState)({\n        scrapeMotionValuesFromProps: _utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_1__.scrapeMotionValuesFromProps,\n        createRenderState: _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_2__.createSvgRenderState,\n    }),\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy9jb25maWctbW90aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZFO0FBQ047QUFDUTs7QUFFL0U7QUFDQSxvQkFBb0Isc0ZBQWtCO0FBQ3RDLHFDQUFxQyx3RkFBMkI7QUFDaEUsMkJBQTJCLGdGQUFvQjtBQUMvQyxLQUFLO0FBQ0w7O0FBRTJCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcc3ZnXFxjb25maWctbW90aW9uLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtYWtlVXNlVmlzdWFsU3RhdGUgfSBmcm9tICcuLi8uLi9tb3Rpb24vdXRpbHMvdXNlLXZpc3VhbC1zdGF0ZS5tanMnO1xuaW1wb3J0IHsgY3JlYXRlU3ZnUmVuZGVyU3RhdGUgfSBmcm9tICcuL3V0aWxzL2NyZWF0ZS1yZW5kZXItc3RhdGUubWpzJztcbmltcG9ydCB7IHNjcmFwZU1vdGlvblZhbHVlc0Zyb21Qcm9wcyB9IGZyb20gJy4vdXRpbHMvc2NyYXBlLW1vdGlvbi12YWx1ZXMubWpzJztcblxuY29uc3Qgc3ZnTW90aW9uQ29uZmlnID0ge1xuICAgIHVzZVZpc3VhbFN0YXRlOiBtYWtlVXNlVmlzdWFsU3RhdGUoe1xuICAgICAgICBzY3JhcGVNb3Rpb25WYWx1ZXNGcm9tUHJvcHM6IHNjcmFwZU1vdGlvblZhbHVlc0Zyb21Qcm9wcyxcbiAgICAgICAgY3JlYXRlUmVuZGVyU3RhdGU6IGNyZWF0ZVN2Z1JlbmRlclN0YXRlLFxuICAgIH0pLFxufTtcblxuZXhwb3J0IHsgc3ZnTW90aW9uQ29uZmlnIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   lowercaseSVGElements: () => (/* binding */ lowercaseSVGElements)\n/* harmony export */ });\n/**\n * We keep these listed separately as we use the lowercase tag names as part\n * of the runtime bundle to detect SVG components\n */\nconst lowercaseSVGElements = [\n    \"animate\",\n    \"circle\",\n    \"defs\",\n    \"desc\",\n    \"ellipse\",\n    \"g\",\n    \"image\",\n    \"line\",\n    \"filter\",\n    \"marker\",\n    \"mask\",\n    \"metadata\",\n    \"path\",\n    \"pattern\",\n    \"polygon\",\n    \"polyline\",\n    \"rect\",\n    \"stop\",\n    \"switch\",\n    \"symbol\",\n    \"svg\",\n    \"text\",\n    \"tspan\",\n    \"use\",\n    \"view\",\n];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy9sb3dlcmNhc2UtZWxlbWVudHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZ0MiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFxzdmdcXGxvd2VyY2FzZS1lbGVtZW50cy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBXZSBrZWVwIHRoZXNlIGxpc3RlZCBzZXBhcmF0ZWx5IGFzIHdlIHVzZSB0aGUgbG93ZXJjYXNlIHRhZyBuYW1lcyBhcyBwYXJ0XG4gKiBvZiB0aGUgcnVudGltZSBidW5kbGUgdG8gZGV0ZWN0IFNWRyBjb21wb25lbnRzXG4gKi9cbmNvbnN0IGxvd2VyY2FzZVNWR0VsZW1lbnRzID0gW1xuICAgIFwiYW5pbWF0ZVwiLFxuICAgIFwiY2lyY2xlXCIsXG4gICAgXCJkZWZzXCIsXG4gICAgXCJkZXNjXCIsXG4gICAgXCJlbGxpcHNlXCIsXG4gICAgXCJnXCIsXG4gICAgXCJpbWFnZVwiLFxuICAgIFwibGluZVwiLFxuICAgIFwiZmlsdGVyXCIsXG4gICAgXCJtYXJrZXJcIixcbiAgICBcIm1hc2tcIixcbiAgICBcIm1ldGFkYXRhXCIsXG4gICAgXCJwYXRoXCIsXG4gICAgXCJwYXR0ZXJuXCIsXG4gICAgXCJwb2x5Z29uXCIsXG4gICAgXCJwb2x5bGluZVwiLFxuICAgIFwicmVjdFwiLFxuICAgIFwic3RvcFwiLFxuICAgIFwic3dpdGNoXCIsXG4gICAgXCJzeW1ib2xcIixcbiAgICBcInN2Z1wiLFxuICAgIFwidGV4dFwiLFxuICAgIFwidHNwYW5cIixcbiAgICBcInVzZVwiLFxuICAgIFwidmlld1wiLFxuXTtcblxuZXhwb3J0IHsgbG93ZXJjYXNlU1ZHRWxlbWVudHMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/use-props.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/use-props.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSVGProps: () => (/* binding */ useSVGProps)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var _html_use_props_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../html/use-props.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/use-props.mjs\");\n/* harmony import */ var _utils_build_attrs_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/build-attrs.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs\");\n/* harmony import */ var _utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/create-render-state.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs\");\n/* harmony import */ var _utils_is_svg_tag_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/is-svg-tag.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs\");\n\n\n\n\n\n\nfunction useSVGProps(props, visualState, _isStatic, Component) {\n    const visualProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        const state = (0,_utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_1__.createSvgRenderState)();\n        (0,_utils_build_attrs_mjs__WEBPACK_IMPORTED_MODULE_2__.buildSVGAttrs)(state, visualState, (0,_utils_is_svg_tag_mjs__WEBPACK_IMPORTED_MODULE_3__.isSVGTag)(Component), props.transformTemplate, props.style);\n        return {\n            ...state.attrs,\n            style: { ...state.style },\n        };\n    }, [visualState]);\n    if (props.style) {\n        const rawStyles = {};\n        (0,_html_use_props_mjs__WEBPACK_IMPORTED_MODULE_4__.copyRawValuesOnly)(rawStyles, props.style, props);\n        visualProps.style = { ...rawStyles, ...visualProps.style };\n    }\n    return visualProps;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/use-props.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildSVGAttrs: () => (/* binding */ buildSVGAttrs)\n/* harmony export */ });\n/* harmony import */ var _html_utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../html/utils/build-styles.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs\");\n/* harmony import */ var _path_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./path.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/path.mjs\");\n\n\n\n/**\n * Build SVG visual attributes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, { attrX, attrY, attrScale, pathLength, pathSpacing = 1, pathOffset = 0, \n// This is object creation, which we try to avoid per-frame.\n...latest }, isSVGTag, transformTemplate, styleProp) {\n    (0,_html_utils_build_styles_mjs__WEBPACK_IMPORTED_MODULE_0__.buildHTMLStyles)(state, latest, transformTemplate);\n    /**\n     * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n     * as normal HTML tags.\n     */\n    if (isSVGTag) {\n        if (state.style.viewBox) {\n            state.attrs.viewBox = state.style.viewBox;\n        }\n        return;\n    }\n    state.attrs = state.style;\n    state.style = {};\n    const { attrs, style } = state;\n    /**\n     * However, we apply transforms as CSS transforms.\n     * So if we detect a transform, transformOrigin we take it from attrs and copy it into style.\n     */\n    if (attrs.transform) {\n        style.transform = attrs.transform;\n        delete attrs.transform;\n    }\n    if (style.transform || attrs.transformOrigin) {\n        style.transformOrigin = attrs.transformOrigin ?? \"50% 50%\";\n        delete attrs.transformOrigin;\n    }\n    if (style.transform) {\n        /**\n         * SVG's element transform-origin uses its own median as a reference.\n         * Therefore, transformBox becomes a fill-box\n         */\n        style.transformBox = styleProp?.transformBox ?? \"fill-box\";\n        delete attrs.transformBox;\n    }\n    // Render attrX/attrY/attrScale as attributes\n    if (attrX !== undefined)\n        attrs.x = attrX;\n    if (attrY !== undefined)\n        attrs.y = attrY;\n    if (attrScale !== undefined)\n        attrs.scale = attrScale;\n    // Build SVG path if one has been defined\n    if (pathLength !== undefined) {\n        (0,_path_mjs__WEBPACK_IMPORTED_MODULE_1__.buildSVGPath)(attrs, pathLength, pathSpacing, pathOffset, false);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   camelCaseAttributes: () => (/* binding */ camelCaseAttributes)\n/* harmony export */ });\n/**\n * A set of attribute names that are always read/written as camel case.\n */\nconst camelCaseAttributes = new Set([\n    \"baseFrequency\",\n    \"diffuseConstant\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerWidth\",\n    \"numOctaves\",\n    \"targetX\",\n    \"targetY\",\n    \"surfaceScale\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"stdDeviation\",\n    \"tableValues\",\n    \"viewBox\",\n    \"gradientTransform\",\n    \"pathLength\",\n    \"startOffset\",\n    \"textLength\",\n    \"lengthAdjust\",\n]);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9jYW1lbC1jYXNlLWF0dHJzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRStCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcc3ZnXFx1dGlsc1xcY2FtZWwtY2FzZS1hdHRycy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBIHNldCBvZiBhdHRyaWJ1dGUgbmFtZXMgdGhhdCBhcmUgYWx3YXlzIHJlYWQvd3JpdHRlbiBhcyBjYW1lbCBjYXNlLlxuICovXG5jb25zdCBjYW1lbENhc2VBdHRyaWJ1dGVzID0gbmV3IFNldChbXG4gICAgXCJiYXNlRnJlcXVlbmN5XCIsXG4gICAgXCJkaWZmdXNlQ29uc3RhbnRcIixcbiAgICBcImtlcm5lbE1hdHJpeFwiLFxuICAgIFwia2VybmVsVW5pdExlbmd0aFwiLFxuICAgIFwia2V5U3BsaW5lc1wiLFxuICAgIFwia2V5VGltZXNcIixcbiAgICBcImxpbWl0aW5nQ29uZUFuZ2xlXCIsXG4gICAgXCJtYXJrZXJIZWlnaHRcIixcbiAgICBcIm1hcmtlcldpZHRoXCIsXG4gICAgXCJudW1PY3RhdmVzXCIsXG4gICAgXCJ0YXJnZXRYXCIsXG4gICAgXCJ0YXJnZXRZXCIsXG4gICAgXCJzdXJmYWNlU2NhbGVcIixcbiAgICBcInNwZWN1bGFyQ29uc3RhbnRcIixcbiAgICBcInNwZWN1bGFyRXhwb25lbnRcIixcbiAgICBcInN0ZERldmlhdGlvblwiLFxuICAgIFwidGFibGVWYWx1ZXNcIixcbiAgICBcInZpZXdCb3hcIixcbiAgICBcImdyYWRpZW50VHJhbnNmb3JtXCIsXG4gICAgXCJwYXRoTGVuZ3RoXCIsXG4gICAgXCJzdGFydE9mZnNldFwiLFxuICAgIFwidGV4dExlbmd0aFwiLFxuICAgIFwibGVuZ3RoQWRqdXN0XCIsXG5dKTtcblxuZXhwb3J0IHsgY2FtZWxDYXNlQXR0cmlidXRlcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSvgRenderState: () => (/* binding */ createSvgRenderState)\n/* harmony export */ });\n/* harmony import */ var _html_utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../html/utils/create-render-state.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs\");\n\n\nconst createSvgRenderState = () => ({\n    ...(0,_html_utils_create_render_state_mjs__WEBPACK_IMPORTED_MODULE_0__.createHtmlRenderState)(),\n    attrs: {},\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9jcmVhdGUtcmVuZGVyLXN0YXRlLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRjs7QUFFakY7QUFDQSxPQUFPLDBGQUFxQjtBQUM1QixhQUFhO0FBQ2IsQ0FBQzs7QUFFK0IiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFxzdmdcXHV0aWxzXFxjcmVhdGUtcmVuZGVyLXN0YXRlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVIdG1sUmVuZGVyU3RhdGUgfSBmcm9tICcuLi8uLi9odG1sL3V0aWxzL2NyZWF0ZS1yZW5kZXItc3RhdGUubWpzJztcblxuY29uc3QgY3JlYXRlU3ZnUmVuZGVyU3RhdGUgPSAoKSA9PiAoe1xuICAgIC4uLmNyZWF0ZUh0bWxSZW5kZXJTdGF0ZSgpLFxuICAgIGF0dHJzOiB7fSxcbn0pO1xuXG5leHBvcnQgeyBjcmVhdGVTdmdSZW5kZXJTdGF0ZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSVGTag: () => (/* binding */ isSVGTag)\n/* harmony export */ });\nconst isSVGTag = (tag) => typeof tag === \"string\" && tag.toLowerCase() === \"svg\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9pcy1zdmctdGFnLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRW9CIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcc3ZnXFx1dGlsc1xcaXMtc3ZnLXRhZy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNTVkdUYWcgPSAodGFnKSA9PiB0eXBlb2YgdGFnID09PSBcInN0cmluZ1wiICYmIHRhZy50b0xvd2VyQ2FzZSgpID09PSBcInN2Z1wiO1xuXG5leHBvcnQgeyBpc1NWR1RhZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/path.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/path.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildSVGPath: () => (/* binding */ buildSVGPath)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n\n\nconst dashKeys = {\n    offset: \"stroke-dashoffset\",\n    array: \"stroke-dasharray\",\n};\nconst camelKeys = {\n    offset: \"strokeDashoffset\",\n    array: \"strokeDasharray\",\n};\n/**\n * Build SVG path properties. Uses the path's measured length to convert\n * our custom pathLength, pathSpacing and pathOffset into stroke-dashoffset\n * and stroke-dasharray attributes.\n *\n * This function is mutative to reduce per-frame GC.\n */\nfunction buildSVGPath(attrs, length, spacing = 1, offset = 0, useDashCase = true) {\n    // Normalise path length by setting SVG attribute pathLength to 1\n    attrs.pathLength = 1;\n    // We use dash case when setting attributes directly to the DOM node and camel case\n    // when defining props on a React component.\n    const keys = useDashCase ? dashKeys : camelKeys;\n    // Build the dash offset\n    attrs[keys.offset] = motion_dom__WEBPACK_IMPORTED_MODULE_0__.px.transform(-offset);\n    // Build the dash array\n    const pathLength = motion_dom__WEBPACK_IMPORTED_MODULE_0__.px.transform(length);\n    const pathSpacing = motion_dom__WEBPACK_IMPORTED_MODULE_0__.px.transform(spacing);\n    attrs[keys.array] = `${pathLength} ${pathSpacing}`;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/path.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/render.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/render.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   renderSVG: () => (/* binding */ renderSVG)\n/* harmony export */ });\n/* harmony import */ var _dom_utils_camel_to_dash_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../dom/utils/camel-to-dash.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs\");\n/* harmony import */ var _html_utils_render_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../html/utils/render.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/render.mjs\");\n/* harmony import */ var _camel_case_attrs_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./camel-case-attrs.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs\");\n\n\n\n\nfunction renderSVG(element, renderState, _styleProp, projection) {\n    (0,_html_utils_render_mjs__WEBPACK_IMPORTED_MODULE_0__.renderHTML)(element, renderState, undefined, projection);\n    for (const key in renderState.attrs) {\n        element.setAttribute(!_camel_case_attrs_mjs__WEBPACK_IMPORTED_MODULE_1__.camelCaseAttributes.has(key) ? (0,_dom_utils_camel_to_dash_mjs__WEBPACK_IMPORTED_MODULE_2__.camelToDash)(key) : key, renderState.attrs[key]);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9yZW5kZXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZ0U7QUFDUDtBQUNJOztBQUU3RDtBQUNBLElBQUksa0VBQVU7QUFDZDtBQUNBLDhCQUE4QixzRUFBbUIsWUFBWSx5RUFBVztBQUN4RTtBQUNBOztBQUVxQiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXHN2Z1xcdXRpbHNcXHJlbmRlci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FtZWxUb0Rhc2ggfSBmcm9tICcuLi8uLi9kb20vdXRpbHMvY2FtZWwtdG8tZGFzaC5tanMnO1xuaW1wb3J0IHsgcmVuZGVySFRNTCB9IGZyb20gJy4uLy4uL2h0bWwvdXRpbHMvcmVuZGVyLm1qcyc7XG5pbXBvcnQgeyBjYW1lbENhc2VBdHRyaWJ1dGVzIH0gZnJvbSAnLi9jYW1lbC1jYXNlLWF0dHJzLm1qcyc7XG5cbmZ1bmN0aW9uIHJlbmRlclNWRyhlbGVtZW50LCByZW5kZXJTdGF0ZSwgX3N0eWxlUHJvcCwgcHJvamVjdGlvbikge1xuICAgIHJlbmRlckhUTUwoZWxlbWVudCwgcmVuZGVyU3RhdGUsIHVuZGVmaW5lZCwgcHJvamVjdGlvbik7XG4gICAgZm9yIChjb25zdCBrZXkgaW4gcmVuZGVyU3RhdGUuYXR0cnMpIHtcbiAgICAgICAgZWxlbWVudC5zZXRBdHRyaWJ1dGUoIWNhbWVsQ2FzZUF0dHJpYnV0ZXMuaGFzKGtleSkgPyBjYW1lbFRvRGFzaChrZXkpIDoga2V5LCByZW5kZXJTdGF0ZS5hdHRyc1trZXldKTtcbiAgICB9XG59XG5cbmV4cG9ydCB7IHJlbmRlclNWRyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/render.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrapeMotionValuesFromProps: () => (/* binding */ scrapeMotionValuesFromProps)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _html_utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../html/utils/scrape-motion-values.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs\");\n\n\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const newValues = (0,_html_utils_scrape_motion_values_mjs__WEBPACK_IMPORTED_MODULE_0__.scrapeMotionValuesFromProps)(props, prevProps, visualElement);\n    for (const key in props) {\n        if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.isMotionValue)(props[key]) ||\n            (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.isMotionValue)(prevProps[key])) {\n            const targetKey = motion_dom__WEBPACK_IMPORTED_MODULE_1__.transformPropOrder.indexOf(key) !== -1\n                ? \"attr\" + key.charAt(0).toUpperCase() + key.substring(1)\n                : key;\n            newValues[targetKey] = props[key];\n        }\n    }\n    return newValues;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3N2Zy91dGlscy9zY3JhcGUtbW90aW9uLXZhbHVlcy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStEO0FBQzBEOztBQUV6SDtBQUNBLHNCQUFzQixpR0FBNkI7QUFDbkQ7QUFDQSxZQUFZLHlEQUFhO0FBQ3pCLFlBQVkseURBQWE7QUFDekIsOEJBQThCLDBEQUFrQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFdUMiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFxzdmdcXHV0aWxzXFxzY3JhcGUtbW90aW9uLXZhbHVlcy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNNb3Rpb25WYWx1ZSwgdHJhbnNmb3JtUHJvcE9yZGVyIH0gZnJvbSAnbW90aW9uLWRvbSc7XG5pbXBvcnQgeyBzY3JhcGVNb3Rpb25WYWx1ZXNGcm9tUHJvcHMgYXMgc2NyYXBlTW90aW9uVmFsdWVzRnJvbVByb3BzJDEgfSBmcm9tICcuLi8uLi9odG1sL3V0aWxzL3NjcmFwZS1tb3Rpb24tdmFsdWVzLm1qcyc7XG5cbmZ1bmN0aW9uIHNjcmFwZU1vdGlvblZhbHVlc0Zyb21Qcm9wcyhwcm9wcywgcHJldlByb3BzLCB2aXN1YWxFbGVtZW50KSB7XG4gICAgY29uc3QgbmV3VmFsdWVzID0gc2NyYXBlTW90aW9uVmFsdWVzRnJvbVByb3BzJDEocHJvcHMsIHByZXZQcm9wcywgdmlzdWFsRWxlbWVudCk7XG4gICAgZm9yIChjb25zdCBrZXkgaW4gcHJvcHMpIHtcbiAgICAgICAgaWYgKGlzTW90aW9uVmFsdWUocHJvcHNba2V5XSkgfHxcbiAgICAgICAgICAgIGlzTW90aW9uVmFsdWUocHJldlByb3BzW2tleV0pKSB7XG4gICAgICAgICAgICBjb25zdCB0YXJnZXRLZXkgPSB0cmFuc2Zvcm1Qcm9wT3JkZXIuaW5kZXhPZihrZXkpICE9PSAtMVxuICAgICAgICAgICAgICAgID8gXCJhdHRyXCIgKyBrZXkuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBrZXkuc3Vic3RyaW5nKDEpXG4gICAgICAgICAgICAgICAgOiBrZXk7XG4gICAgICAgICAgICBuZXdWYWx1ZXNbdGFyZ2V0S2V5XSA9IHByb3BzW2tleV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG5ld1ZhbHVlcztcbn1cblxuZXhwb3J0IHsgc2NyYXBlTW90aW9uVmFsdWVzRnJvbVByb3BzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/animation-state.mjs":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/animation-state.mjs ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkVariantsDidChange: () => (/* binding */ checkVariantsDidChange),\n/* harmony export */   createAnimationState: () => (/* binding */ createAnimationState)\n/* harmony export */ });\n/* harmony import */ var _animation_interfaces_visual_element_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../animation/interfaces/visual-element.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/interfaces/visual-element.mjs\");\n/* harmony import */ var _animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../animation/utils/is-animation-controls.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs\");\n/* harmony import */ var _animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../animation/utils/is-keyframes-target.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs\");\n/* harmony import */ var _utils_shallow_compare_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/shallow-compare.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/shallow-compare.mjs\");\n/* harmony import */ var _get_variant_context_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./get-variant-context.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/get-variant-context.mjs\");\n/* harmony import */ var _is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./is-variant-label.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs\");\n/* harmony import */ var _resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resolve-dynamic-variants.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs\");\n/* harmony import */ var _variant_props_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./variant-props.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/variant-props.mjs\");\n\n\n\n\n\n\n\n\n\nconst reversePriorityOrder = [..._variant_props_mjs__WEBPACK_IMPORTED_MODULE_0__.variantPriorityOrder].reverse();\nconst numAnimationTypes = _variant_props_mjs__WEBPACK_IMPORTED_MODULE_0__.variantPriorityOrder.length;\nfunction animateList(visualElement) {\n    return (animations) => Promise.all(animations.map(({ animation, options }) => (0,_animation_interfaces_visual_element_mjs__WEBPACK_IMPORTED_MODULE_1__.animateVisualElement)(visualElement, animation, options)));\n}\nfunction createAnimationState(visualElement) {\n    let animate = animateList(visualElement);\n    let state = createState();\n    let isInitialRender = true;\n    /**\n     * This function will be used to reduce the animation definitions for\n     * each active animation type into an object of resolved values for it.\n     */\n    const buildResolvedTypeValues = (type) => (acc, definition) => {\n        const resolved = (0,_resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveVariant)(visualElement, definition, type === \"exit\"\n            ? visualElement.presenceContext?.custom\n            : undefined);\n        if (resolved) {\n            const { transition, transitionEnd, ...target } = resolved;\n            acc = { ...acc, ...target, ...transitionEnd };\n        }\n        return acc;\n    };\n    /**\n     * This just allows us to inject mocked animation functions\n     * @internal\n     */\n    function setAnimateFunction(makeAnimator) {\n        animate = makeAnimator(visualElement);\n    }\n    /**\n     * When we receive new props, we need to:\n     * 1. Create a list of protected keys for each type. This is a directory of\n     *    value keys that are currently being \"handled\" by types of a higher priority\n     *    so that whenever an animation is played of a given type, these values are\n     *    protected from being animated.\n     * 2. Determine if an animation type needs animating.\n     * 3. Determine if any values have been removed from a type and figure out\n     *    what to animate those to.\n     */\n    function animateChanges(changedActiveType) {\n        const { props } = visualElement;\n        const context = (0,_get_variant_context_mjs__WEBPACK_IMPORTED_MODULE_3__.getVariantContext)(visualElement.parent) || {};\n        /**\n         * A list of animations that we'll build into as we iterate through the animation\n         * types. This will get executed at the end of the function.\n         */\n        const animations = [];\n        /**\n         * Keep track of which values have been removed. Then, as we hit lower priority\n         * animation types, we can check if they contain removed values and animate to that.\n         */\n        const removedKeys = new Set();\n        /**\n         * A dictionary of all encountered keys. This is an object to let us build into and\n         * copy it without iteration. Each time we hit an animation type we set its protected\n         * keys - the keys its not allowed to animate - to the latest version of this object.\n         */\n        let encounteredKeys = {};\n        /**\n         * If a variant has been removed at a given index, and this component is controlling\n         * variant animations, we want to ensure lower-priority variants are forced to animate.\n         */\n        let removedVariantIndex = Infinity;\n        /**\n         * Iterate through all animation types in reverse priority order. For each, we want to\n         * detect which values it's handling and whether or not they've changed (and therefore\n         * need to be animated). If any values have been removed, we want to detect those in\n         * lower priority props and flag for animation.\n         */\n        for (let i = 0; i < numAnimationTypes; i++) {\n            const type = reversePriorityOrder[i];\n            const typeState = state[type];\n            const prop = props[type] !== undefined\n                ? props[type]\n                : context[type];\n            const propIsVariant = (0,_is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_4__.isVariantLabel)(prop);\n            /**\n             * If this type has *just* changed isActive status, set activeDelta\n             * to that status. Otherwise set to null.\n             */\n            const activeDelta = type === changedActiveType ? typeState.isActive : null;\n            if (activeDelta === false)\n                removedVariantIndex = i;\n            /**\n             * If this prop is an inherited variant, rather than been set directly on the\n             * component itself, we want to make sure we allow the parent to trigger animations.\n             *\n             * TODO: Can probably change this to a !isControllingVariants check\n             */\n            let isInherited = prop === context[type] &&\n                prop !== props[type] &&\n                propIsVariant;\n            /**\n             *\n             */\n            if (isInherited &&\n                isInitialRender &&\n                visualElement.manuallyAnimateOnMount) {\n                isInherited = false;\n            }\n            /**\n             * Set all encountered keys so far as the protected keys for this type. This will\n             * be any key that has been animated or otherwise handled by active, higher-priortiy types.\n             */\n            typeState.protectedKeys = { ...encounteredKeys };\n            // Check if we can skip analysing this prop early\n            if (\n            // If it isn't active and hasn't *just* been set as inactive\n            (!typeState.isActive && activeDelta === null) ||\n                // If we didn't and don't have any defined prop for this animation type\n                (!prop && !typeState.prevProp) ||\n                // Or if the prop doesn't define an animation\n                (0,_animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_5__.isAnimationControls)(prop) ||\n                typeof prop === \"boolean\") {\n                continue;\n            }\n            /**\n             * As we go look through the values defined on this type, if we detect\n             * a changed value or a value that was removed in a higher priority, we set\n             * this to true and add this prop to the animation list.\n             */\n            const variantDidChange = checkVariantsDidChange(typeState.prevProp, prop);\n            let shouldAnimateType = variantDidChange ||\n                // If we're making this variant active, we want to always make it active\n                (type === changedActiveType &&\n                    typeState.isActive &&\n                    !isInherited &&\n                    propIsVariant) ||\n                // If we removed a higher-priority variant (i is in reverse order)\n                (i > removedVariantIndex && propIsVariant);\n            let handledRemovedValues = false;\n            /**\n             * As animations can be set as variant lists, variants or target objects, we\n             * coerce everything to an array if it isn't one already\n             */\n            const definitionList = Array.isArray(prop) ? prop : [prop];\n            /**\n             * Build an object of all the resolved values. We'll use this in the subsequent\n             * animateChanges calls to determine whether a value has changed.\n             */\n            let resolvedValues = definitionList.reduce(buildResolvedTypeValues(type), {});\n            if (activeDelta === false)\n                resolvedValues = {};\n            /**\n             * Now we need to loop through all the keys in the prev prop and this prop,\n             * and decide:\n             * 1. If the value has changed, and needs animating\n             * 2. If it has been removed, and needs adding to the removedKeys set\n             * 3. If it has been removed in a higher priority type and needs animating\n             * 4. If it hasn't been removed in a higher priority but hasn't changed, and\n             *    needs adding to the type's protectedKeys list.\n             */\n            const { prevResolvedValues = {} } = typeState;\n            const allKeys = {\n                ...prevResolvedValues,\n                ...resolvedValues,\n            };\n            const markToAnimate = (key) => {\n                shouldAnimateType = true;\n                if (removedKeys.has(key)) {\n                    handledRemovedValues = true;\n                    removedKeys.delete(key);\n                }\n                typeState.needsAnimating[key] = true;\n                const motionValue = visualElement.getValue(key);\n                if (motionValue)\n                    motionValue.liveStyle = false;\n            };\n            for (const key in allKeys) {\n                const next = resolvedValues[key];\n                const prev = prevResolvedValues[key];\n                // If we've already handled this we can just skip ahead\n                if (encounteredKeys.hasOwnProperty(key))\n                    continue;\n                /**\n                 * If the value has changed, we probably want to animate it.\n                 */\n                let valueHasChanged = false;\n                if ((0,_animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_6__.isKeyframesTarget)(next) && (0,_animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_6__.isKeyframesTarget)(prev)) {\n                    valueHasChanged = !(0,_utils_shallow_compare_mjs__WEBPACK_IMPORTED_MODULE_7__.shallowCompare)(next, prev);\n                }\n                else {\n                    valueHasChanged = next !== prev;\n                }\n                if (valueHasChanged) {\n                    if (next !== undefined && next !== null) {\n                        // If next is defined and doesn't equal prev, it needs animating\n                        markToAnimate(key);\n                    }\n                    else {\n                        // If it's undefined, it's been removed.\n                        removedKeys.add(key);\n                    }\n                }\n                else if (next !== undefined && removedKeys.has(key)) {\n                    /**\n                     * If next hasn't changed and it isn't undefined, we want to check if it's\n                     * been removed by a higher priority\n                     */\n                    markToAnimate(key);\n                }\n                else {\n                    /**\n                     * If it hasn't changed, we add it to the list of protected values\n                     * to ensure it doesn't get animated.\n                     */\n                    typeState.protectedKeys[key] = true;\n                }\n            }\n            /**\n             * Update the typeState so next time animateChanges is called we can compare the\n             * latest prop and resolvedValues to these.\n             */\n            typeState.prevProp = prop;\n            typeState.prevResolvedValues = resolvedValues;\n            /**\n             *\n             */\n            if (typeState.isActive) {\n                encounteredKeys = { ...encounteredKeys, ...resolvedValues };\n            }\n            if (isInitialRender && visualElement.blockInitialAnimation) {\n                shouldAnimateType = false;\n            }\n            /**\n             * If this is an inherited prop we want to skip this animation\n             * unless the inherited variants haven't changed on this render.\n             */\n            const willAnimateViaParent = isInherited && variantDidChange;\n            const needsAnimating = !willAnimateViaParent || handledRemovedValues;\n            if (shouldAnimateType && needsAnimating) {\n                animations.push(...definitionList.map((animation) => ({\n                    animation: animation,\n                    options: { type },\n                })));\n            }\n        }\n        /**\n         * If there are some removed value that haven't been dealt with,\n         * we need to create a new animation that falls back either to the value\n         * defined in the style prop, or the last read value.\n         */\n        if (removedKeys.size) {\n            const fallbackAnimation = {};\n            /**\n             * If the initial prop contains a transition we can use that, otherwise\n             * allow the animation function to use the visual element's default.\n             */\n            if (typeof props.initial !== \"boolean\") {\n                const initialTransition = (0,_resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveVariant)(visualElement, Array.isArray(props.initial)\n                    ? props.initial[0]\n                    : props.initial);\n                if (initialTransition && initialTransition.transition) {\n                    fallbackAnimation.transition = initialTransition.transition;\n                }\n            }\n            removedKeys.forEach((key) => {\n                const fallbackTarget = visualElement.getBaseTarget(key);\n                const motionValue = visualElement.getValue(key);\n                if (motionValue)\n                    motionValue.liveStyle = true;\n                // @ts-expect-error - @mattgperry to figure if we should do something here\n                fallbackAnimation[key] = fallbackTarget ?? null;\n            });\n            animations.push({ animation: fallbackAnimation });\n        }\n        let shouldAnimate = Boolean(animations.length);\n        if (isInitialRender &&\n            (props.initial === false || props.initial === props.animate) &&\n            !visualElement.manuallyAnimateOnMount) {\n            shouldAnimate = false;\n        }\n        isInitialRender = false;\n        return shouldAnimate ? animate(animations) : Promise.resolve();\n    }\n    /**\n     * Change whether a certain animation type is active.\n     */\n    function setActive(type, isActive) {\n        // If the active state hasn't changed, we can safely do nothing here\n        if (state[type].isActive === isActive)\n            return Promise.resolve();\n        // Propagate active change to children\n        visualElement.variantChildren?.forEach((child) => child.animationState?.setActive(type, isActive));\n        state[type].isActive = isActive;\n        const animations = animateChanges(type);\n        for (const key in state) {\n            state[key].protectedKeys = {};\n        }\n        return animations;\n    }\n    return {\n        animateChanges,\n        setActive,\n        setAnimateFunction,\n        getState: () => state,\n        reset: () => {\n            state = createState();\n            isInitialRender = true;\n        },\n    };\n}\nfunction checkVariantsDidChange(prev, next) {\n    if (typeof next === \"string\") {\n        return next !== prev;\n    }\n    else if (Array.isArray(next)) {\n        return !(0,_utils_shallow_compare_mjs__WEBPACK_IMPORTED_MODULE_7__.shallowCompare)(next, prev);\n    }\n    return false;\n}\nfunction createTypeState(isActive = false) {\n    return {\n        isActive,\n        protectedKeys: {},\n        needsAnimating: {},\n        prevResolvedValues: {},\n    };\n}\nfunction createState() {\n    return {\n        animate: createTypeState(true),\n        whileInView: createTypeState(),\n        whileHover: createTypeState(),\n        whileTap: createTypeState(),\n        whileDrag: createTypeState(),\n        whileFocus: createTypeState(),\n        exit: createTypeState(),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL2FuaW1hdGlvbi1zdGF0ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFxRjtBQUNDO0FBQ0o7QUFDakI7QUFDSDtBQUNOO0FBQ1E7QUFDTDs7QUFFM0QsaUNBQWlDLG9FQUFvQjtBQUNyRCwwQkFBMEIsb0VBQW9CO0FBQzlDO0FBQ0EseURBQXlELG9CQUFvQixLQUFLLDhGQUFvQjtBQUN0RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5Qiw2RUFBYztBQUN2QztBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsdUNBQXVDO0FBQzNELG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsUUFBUTtBQUN4Qix3QkFBd0IsMkVBQWlCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix1QkFBdUI7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxxRUFBYztBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiwrRkFBbUI7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0ZBQXdGO0FBQ3hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsMEJBQTBCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDJGQUFpQixVQUFVLDJGQUFpQjtBQUNoRSx1Q0FBdUMsMEVBQWM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLE1BQU07QUFDckMsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEMsNkVBQWM7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsOEJBQThCLDhCQUE4QjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiwwRUFBYztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekIsMEJBQTBCO0FBQzFCLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFd0QiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFx1dGlsc1xcYW5pbWF0aW9uLXN0YXRlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhbmltYXRlVmlzdWFsRWxlbWVudCB9IGZyb20gJy4uLy4uL2FuaW1hdGlvbi9pbnRlcmZhY2VzL3Zpc3VhbC1lbGVtZW50Lm1qcyc7XG5pbXBvcnQgeyBpc0FuaW1hdGlvbkNvbnRyb2xzIH0gZnJvbSAnLi4vLi4vYW5pbWF0aW9uL3V0aWxzL2lzLWFuaW1hdGlvbi1jb250cm9scy5tanMnO1xuaW1wb3J0IHsgaXNLZXlmcmFtZXNUYXJnZXQgfSBmcm9tICcuLi8uLi9hbmltYXRpb24vdXRpbHMvaXMta2V5ZnJhbWVzLXRhcmdldC5tanMnO1xuaW1wb3J0IHsgc2hhbGxvd0NvbXBhcmUgfSBmcm9tICcuLi8uLi91dGlscy9zaGFsbG93LWNvbXBhcmUubWpzJztcbmltcG9ydCB7IGdldFZhcmlhbnRDb250ZXh0IH0gZnJvbSAnLi9nZXQtdmFyaWFudC1jb250ZXh0Lm1qcyc7XG5pbXBvcnQgeyBpc1ZhcmlhbnRMYWJlbCB9IGZyb20gJy4vaXMtdmFyaWFudC1sYWJlbC5tanMnO1xuaW1wb3J0IHsgcmVzb2x2ZVZhcmlhbnQgfSBmcm9tICcuL3Jlc29sdmUtZHluYW1pYy12YXJpYW50cy5tanMnO1xuaW1wb3J0IHsgdmFyaWFudFByaW9yaXR5T3JkZXIgfSBmcm9tICcuL3ZhcmlhbnQtcHJvcHMubWpzJztcblxuY29uc3QgcmV2ZXJzZVByaW9yaXR5T3JkZXIgPSBbLi4udmFyaWFudFByaW9yaXR5T3JkZXJdLnJldmVyc2UoKTtcbmNvbnN0IG51bUFuaW1hdGlvblR5cGVzID0gdmFyaWFudFByaW9yaXR5T3JkZXIubGVuZ3RoO1xuZnVuY3Rpb24gYW5pbWF0ZUxpc3QodmlzdWFsRWxlbWVudCkge1xuICAgIHJldHVybiAoYW5pbWF0aW9ucykgPT4gUHJvbWlzZS5hbGwoYW5pbWF0aW9ucy5tYXAoKHsgYW5pbWF0aW9uLCBvcHRpb25zIH0pID0+IGFuaW1hdGVWaXN1YWxFbGVtZW50KHZpc3VhbEVsZW1lbnQsIGFuaW1hdGlvbiwgb3B0aW9ucykpKTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZUFuaW1hdGlvblN0YXRlKHZpc3VhbEVsZW1lbnQpIHtcbiAgICBsZXQgYW5pbWF0ZSA9IGFuaW1hdGVMaXN0KHZpc3VhbEVsZW1lbnQpO1xuICAgIGxldCBzdGF0ZSA9IGNyZWF0ZVN0YXRlKCk7XG4gICAgbGV0IGlzSW5pdGlhbFJlbmRlciA9IHRydWU7XG4gICAgLyoqXG4gICAgICogVGhpcyBmdW5jdGlvbiB3aWxsIGJlIHVzZWQgdG8gcmVkdWNlIHRoZSBhbmltYXRpb24gZGVmaW5pdGlvbnMgZm9yXG4gICAgICogZWFjaCBhY3RpdmUgYW5pbWF0aW9uIHR5cGUgaW50byBhbiBvYmplY3Qgb2YgcmVzb2x2ZWQgdmFsdWVzIGZvciBpdC5cbiAgICAgKi9cbiAgICBjb25zdCBidWlsZFJlc29sdmVkVHlwZVZhbHVlcyA9ICh0eXBlKSA9PiAoYWNjLCBkZWZpbml0aW9uKSA9PiB7XG4gICAgICAgIGNvbnN0IHJlc29sdmVkID0gcmVzb2x2ZVZhcmlhbnQodmlzdWFsRWxlbWVudCwgZGVmaW5pdGlvbiwgdHlwZSA9PT0gXCJleGl0XCJcbiAgICAgICAgICAgID8gdmlzdWFsRWxlbWVudC5wcmVzZW5jZUNvbnRleHQ/LmN1c3RvbVxuICAgICAgICAgICAgOiB1bmRlZmluZWQpO1xuICAgICAgICBpZiAocmVzb2x2ZWQpIHtcbiAgICAgICAgICAgIGNvbnN0IHsgdHJhbnNpdGlvbiwgdHJhbnNpdGlvbkVuZCwgLi4udGFyZ2V0IH0gPSByZXNvbHZlZDtcbiAgICAgICAgICAgIGFjYyA9IHsgLi4uYWNjLCAuLi50YXJnZXQsIC4uLnRyYW5zaXRpb25FbmQgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYWNjO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogVGhpcyBqdXN0IGFsbG93cyB1cyB0byBpbmplY3QgbW9ja2VkIGFuaW1hdGlvbiBmdW5jdGlvbnNcbiAgICAgKiBAaW50ZXJuYWxcbiAgICAgKi9cbiAgICBmdW5jdGlvbiBzZXRBbmltYXRlRnVuY3Rpb24obWFrZUFuaW1hdG9yKSB7XG4gICAgICAgIGFuaW1hdGUgPSBtYWtlQW5pbWF0b3IodmlzdWFsRWxlbWVudCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFdoZW4gd2UgcmVjZWl2ZSBuZXcgcHJvcHMsIHdlIG5lZWQgdG86XG4gICAgICogMS4gQ3JlYXRlIGEgbGlzdCBvZiBwcm90ZWN0ZWQga2V5cyBmb3IgZWFjaCB0eXBlLiBUaGlzIGlzIGEgZGlyZWN0b3J5IG9mXG4gICAgICogICAgdmFsdWUga2V5cyB0aGF0IGFyZSBjdXJyZW50bHkgYmVpbmcgXCJoYW5kbGVkXCIgYnkgdHlwZXMgb2YgYSBoaWdoZXIgcHJpb3JpdHlcbiAgICAgKiAgICBzbyB0aGF0IHdoZW5ldmVyIGFuIGFuaW1hdGlvbiBpcyBwbGF5ZWQgb2YgYSBnaXZlbiB0eXBlLCB0aGVzZSB2YWx1ZXMgYXJlXG4gICAgICogICAgcHJvdGVjdGVkIGZyb20gYmVpbmcgYW5pbWF0ZWQuXG4gICAgICogMi4gRGV0ZXJtaW5lIGlmIGFuIGFuaW1hdGlvbiB0eXBlIG5lZWRzIGFuaW1hdGluZy5cbiAgICAgKiAzLiBEZXRlcm1pbmUgaWYgYW55IHZhbHVlcyBoYXZlIGJlZW4gcmVtb3ZlZCBmcm9tIGEgdHlwZSBhbmQgZmlndXJlIG91dFxuICAgICAqICAgIHdoYXQgdG8gYW5pbWF0ZSB0aG9zZSB0by5cbiAgICAgKi9cbiAgICBmdW5jdGlvbiBhbmltYXRlQ2hhbmdlcyhjaGFuZ2VkQWN0aXZlVHlwZSkge1xuICAgICAgICBjb25zdCB7IHByb3BzIH0gPSB2aXN1YWxFbGVtZW50O1xuICAgICAgICBjb25zdCBjb250ZXh0ID0gZ2V0VmFyaWFudENvbnRleHQodmlzdWFsRWxlbWVudC5wYXJlbnQpIHx8IHt9O1xuICAgICAgICAvKipcbiAgICAgICAgICogQSBsaXN0IG9mIGFuaW1hdGlvbnMgdGhhdCB3ZSdsbCBidWlsZCBpbnRvIGFzIHdlIGl0ZXJhdGUgdGhyb3VnaCB0aGUgYW5pbWF0aW9uXG4gICAgICAgICAqIHR5cGVzLiBUaGlzIHdpbGwgZ2V0IGV4ZWN1dGVkIGF0IHRoZSBlbmQgb2YgdGhlIGZ1bmN0aW9uLlxuICAgICAgICAgKi9cbiAgICAgICAgY29uc3QgYW5pbWF0aW9ucyA9IFtdO1xuICAgICAgICAvKipcbiAgICAgICAgICogS2VlcCB0cmFjayBvZiB3aGljaCB2YWx1ZXMgaGF2ZSBiZWVuIHJlbW92ZWQuIFRoZW4sIGFzIHdlIGhpdCBsb3dlciBwcmlvcml0eVxuICAgICAgICAgKiBhbmltYXRpb24gdHlwZXMsIHdlIGNhbiBjaGVjayBpZiB0aGV5IGNvbnRhaW4gcmVtb3ZlZCB2YWx1ZXMgYW5kIGFuaW1hdGUgdG8gdGhhdC5cbiAgICAgICAgICovXG4gICAgICAgIGNvbnN0IHJlbW92ZWRLZXlzID0gbmV3IFNldCgpO1xuICAgICAgICAvKipcbiAgICAgICAgICogQSBkaWN0aW9uYXJ5IG9mIGFsbCBlbmNvdW50ZXJlZCBrZXlzLiBUaGlzIGlzIGFuIG9iamVjdCB0byBsZXQgdXMgYnVpbGQgaW50byBhbmRcbiAgICAgICAgICogY29weSBpdCB3aXRob3V0IGl0ZXJhdGlvbi4gRWFjaCB0aW1lIHdlIGhpdCBhbiBhbmltYXRpb24gdHlwZSB3ZSBzZXQgaXRzIHByb3RlY3RlZFxuICAgICAgICAgKiBrZXlzIC0gdGhlIGtleXMgaXRzIG5vdCBhbGxvd2VkIHRvIGFuaW1hdGUgLSB0byB0aGUgbGF0ZXN0IHZlcnNpb24gb2YgdGhpcyBvYmplY3QuXG4gICAgICAgICAqL1xuICAgICAgICBsZXQgZW5jb3VudGVyZWRLZXlzID0ge307XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBJZiBhIHZhcmlhbnQgaGFzIGJlZW4gcmVtb3ZlZCBhdCBhIGdpdmVuIGluZGV4LCBhbmQgdGhpcyBjb21wb25lbnQgaXMgY29udHJvbGxpbmdcbiAgICAgICAgICogdmFyaWFudCBhbmltYXRpb25zLCB3ZSB3YW50IHRvIGVuc3VyZSBsb3dlci1wcmlvcml0eSB2YXJpYW50cyBhcmUgZm9yY2VkIHRvIGFuaW1hdGUuXG4gICAgICAgICAqL1xuICAgICAgICBsZXQgcmVtb3ZlZFZhcmlhbnRJbmRleCA9IEluZmluaXR5O1xuICAgICAgICAvKipcbiAgICAgICAgICogSXRlcmF0ZSB0aHJvdWdoIGFsbCBhbmltYXRpb24gdHlwZXMgaW4gcmV2ZXJzZSBwcmlvcml0eSBvcmRlci4gRm9yIGVhY2gsIHdlIHdhbnQgdG9cbiAgICAgICAgICogZGV0ZWN0IHdoaWNoIHZhbHVlcyBpdCdzIGhhbmRsaW5nIGFuZCB3aGV0aGVyIG9yIG5vdCB0aGV5J3ZlIGNoYW5nZWQgKGFuZCB0aGVyZWZvcmVcbiAgICAgICAgICogbmVlZCB0byBiZSBhbmltYXRlZCkuIElmIGFueSB2YWx1ZXMgaGF2ZSBiZWVuIHJlbW92ZWQsIHdlIHdhbnQgdG8gZGV0ZWN0IHRob3NlIGluXG4gICAgICAgICAqIGxvd2VyIHByaW9yaXR5IHByb3BzIGFuZCBmbGFnIGZvciBhbmltYXRpb24uXG4gICAgICAgICAqL1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IG51bUFuaW1hdGlvblR5cGVzOyBpKyspIHtcbiAgICAgICAgICAgIGNvbnN0IHR5cGUgPSByZXZlcnNlUHJpb3JpdHlPcmRlcltpXTtcbiAgICAgICAgICAgIGNvbnN0IHR5cGVTdGF0ZSA9IHN0YXRlW3R5cGVdO1xuICAgICAgICAgICAgY29uc3QgcHJvcCA9IHByb3BzW3R5cGVdICE9PSB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICA/IHByb3BzW3R5cGVdXG4gICAgICAgICAgICAgICAgOiBjb250ZXh0W3R5cGVdO1xuICAgICAgICAgICAgY29uc3QgcHJvcElzVmFyaWFudCA9IGlzVmFyaWFudExhYmVsKHByb3ApO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBJZiB0aGlzIHR5cGUgaGFzICpqdXN0KiBjaGFuZ2VkIGlzQWN0aXZlIHN0YXR1cywgc2V0IGFjdGl2ZURlbHRhXG4gICAgICAgICAgICAgKiB0byB0aGF0IHN0YXR1cy4gT3RoZXJ3aXNlIHNldCB0byBudWxsLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBjb25zdCBhY3RpdmVEZWx0YSA9IHR5cGUgPT09IGNoYW5nZWRBY3RpdmVUeXBlID8gdHlwZVN0YXRlLmlzQWN0aXZlIDogbnVsbDtcbiAgICAgICAgICAgIGlmIChhY3RpdmVEZWx0YSA9PT0gZmFsc2UpXG4gICAgICAgICAgICAgICAgcmVtb3ZlZFZhcmlhbnRJbmRleCA9IGk7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIElmIHRoaXMgcHJvcCBpcyBhbiBpbmhlcml0ZWQgdmFyaWFudCwgcmF0aGVyIHRoYW4gYmVlbiBzZXQgZGlyZWN0bHkgb24gdGhlXG4gICAgICAgICAgICAgKiBjb21wb25lbnQgaXRzZWxmLCB3ZSB3YW50IHRvIG1ha2Ugc3VyZSB3ZSBhbGxvdyB0aGUgcGFyZW50IHRvIHRyaWdnZXIgYW5pbWF0aW9ucy5cbiAgICAgICAgICAgICAqXG4gICAgICAgICAgICAgKiBUT0RPOiBDYW4gcHJvYmFibHkgY2hhbmdlIHRoaXMgdG8gYSAhaXNDb250cm9sbGluZ1ZhcmlhbnRzIGNoZWNrXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGxldCBpc0luaGVyaXRlZCA9IHByb3AgPT09IGNvbnRleHRbdHlwZV0gJiZcbiAgICAgICAgICAgICAgICBwcm9wICE9PSBwcm9wc1t0eXBlXSAmJlxuICAgICAgICAgICAgICAgIHByb3BJc1ZhcmlhbnQ7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGlmIChpc0luaGVyaXRlZCAmJlxuICAgICAgICAgICAgICAgIGlzSW5pdGlhbFJlbmRlciAmJlxuICAgICAgICAgICAgICAgIHZpc3VhbEVsZW1lbnQubWFudWFsbHlBbmltYXRlT25Nb3VudCkge1xuICAgICAgICAgICAgICAgIGlzSW5oZXJpdGVkID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFNldCBhbGwgZW5jb3VudGVyZWQga2V5cyBzbyBmYXIgYXMgdGhlIHByb3RlY3RlZCBrZXlzIGZvciB0aGlzIHR5cGUuIFRoaXMgd2lsbFxuICAgICAgICAgICAgICogYmUgYW55IGtleSB0aGF0IGhhcyBiZWVuIGFuaW1hdGVkIG9yIG90aGVyd2lzZSBoYW5kbGVkIGJ5IGFjdGl2ZSwgaGlnaGVyLXByaW9ydGl5IHR5cGVzLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICB0eXBlU3RhdGUucHJvdGVjdGVkS2V5cyA9IHsgLi4uZW5jb3VudGVyZWRLZXlzIH07XG4gICAgICAgICAgICAvLyBDaGVjayBpZiB3ZSBjYW4gc2tpcCBhbmFseXNpbmcgdGhpcyBwcm9wIGVhcmx5XG4gICAgICAgICAgICBpZiAoXG4gICAgICAgICAgICAvLyBJZiBpdCBpc24ndCBhY3RpdmUgYW5kIGhhc24ndCAqanVzdCogYmVlbiBzZXQgYXMgaW5hY3RpdmVcbiAgICAgICAgICAgICghdHlwZVN0YXRlLmlzQWN0aXZlICYmIGFjdGl2ZURlbHRhID09PSBudWxsKSB8fFxuICAgICAgICAgICAgICAgIC8vIElmIHdlIGRpZG4ndCBhbmQgZG9uJ3QgaGF2ZSBhbnkgZGVmaW5lZCBwcm9wIGZvciB0aGlzIGFuaW1hdGlvbiB0eXBlXG4gICAgICAgICAgICAgICAgKCFwcm9wICYmICF0eXBlU3RhdGUucHJldlByb3ApIHx8XG4gICAgICAgICAgICAgICAgLy8gT3IgaWYgdGhlIHByb3AgZG9lc24ndCBkZWZpbmUgYW4gYW5pbWF0aW9uXG4gICAgICAgICAgICAgICAgaXNBbmltYXRpb25Db250cm9scyhwcm9wKSB8fFxuICAgICAgICAgICAgICAgIHR5cGVvZiBwcm9wID09PSBcImJvb2xlYW5cIikge1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBBcyB3ZSBnbyBsb29rIHRocm91Z2ggdGhlIHZhbHVlcyBkZWZpbmVkIG9uIHRoaXMgdHlwZSwgaWYgd2UgZGV0ZWN0XG4gICAgICAgICAgICAgKiBhIGNoYW5nZWQgdmFsdWUgb3IgYSB2YWx1ZSB0aGF0IHdhcyByZW1vdmVkIGluIGEgaGlnaGVyIHByaW9yaXR5LCB3ZSBzZXRcbiAgICAgICAgICAgICAqIHRoaXMgdG8gdHJ1ZSBhbmQgYWRkIHRoaXMgcHJvcCB0byB0aGUgYW5pbWF0aW9uIGxpc3QuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGNvbnN0IHZhcmlhbnREaWRDaGFuZ2UgPSBjaGVja1ZhcmlhbnRzRGlkQ2hhbmdlKHR5cGVTdGF0ZS5wcmV2UHJvcCwgcHJvcCk7XG4gICAgICAgICAgICBsZXQgc2hvdWxkQW5pbWF0ZVR5cGUgPSB2YXJpYW50RGlkQ2hhbmdlIHx8XG4gICAgICAgICAgICAgICAgLy8gSWYgd2UncmUgbWFraW5nIHRoaXMgdmFyaWFudCBhY3RpdmUsIHdlIHdhbnQgdG8gYWx3YXlzIG1ha2UgaXQgYWN0aXZlXG4gICAgICAgICAgICAgICAgKHR5cGUgPT09IGNoYW5nZWRBY3RpdmVUeXBlICYmXG4gICAgICAgICAgICAgICAgICAgIHR5cGVTdGF0ZS5pc0FjdGl2ZSAmJlxuICAgICAgICAgICAgICAgICAgICAhaXNJbmhlcml0ZWQgJiZcbiAgICAgICAgICAgICAgICAgICAgcHJvcElzVmFyaWFudCkgfHxcbiAgICAgICAgICAgICAgICAvLyBJZiB3ZSByZW1vdmVkIGEgaGlnaGVyLXByaW9yaXR5IHZhcmlhbnQgKGkgaXMgaW4gcmV2ZXJzZSBvcmRlcilcbiAgICAgICAgICAgICAgICAoaSA+IHJlbW92ZWRWYXJpYW50SW5kZXggJiYgcHJvcElzVmFyaWFudCk7XG4gICAgICAgICAgICBsZXQgaGFuZGxlZFJlbW92ZWRWYWx1ZXMgPSBmYWxzZTtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogQXMgYW5pbWF0aW9ucyBjYW4gYmUgc2V0IGFzIHZhcmlhbnQgbGlzdHMsIHZhcmlhbnRzIG9yIHRhcmdldCBvYmplY3RzLCB3ZVxuICAgICAgICAgICAgICogY29lcmNlIGV2ZXJ5dGhpbmcgdG8gYW4gYXJyYXkgaWYgaXQgaXNuJ3Qgb25lIGFscmVhZHlcbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgY29uc3QgZGVmaW5pdGlvbkxpc3QgPSBBcnJheS5pc0FycmF5KHByb3ApID8gcHJvcCA6IFtwcm9wXTtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogQnVpbGQgYW4gb2JqZWN0IG9mIGFsbCB0aGUgcmVzb2x2ZWQgdmFsdWVzLiBXZSdsbCB1c2UgdGhpcyBpbiB0aGUgc3Vic2VxdWVudFxuICAgICAgICAgICAgICogYW5pbWF0ZUNoYW5nZXMgY2FsbHMgdG8gZGV0ZXJtaW5lIHdoZXRoZXIgYSB2YWx1ZSBoYXMgY2hhbmdlZC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgbGV0IHJlc29sdmVkVmFsdWVzID0gZGVmaW5pdGlvbkxpc3QucmVkdWNlKGJ1aWxkUmVzb2x2ZWRUeXBlVmFsdWVzKHR5cGUpLCB7fSk7XG4gICAgICAgICAgICBpZiAoYWN0aXZlRGVsdGEgPT09IGZhbHNlKVxuICAgICAgICAgICAgICAgIHJlc29sdmVkVmFsdWVzID0ge307XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIE5vdyB3ZSBuZWVkIHRvIGxvb3AgdGhyb3VnaCBhbGwgdGhlIGtleXMgaW4gdGhlIHByZXYgcHJvcCBhbmQgdGhpcyBwcm9wLFxuICAgICAgICAgICAgICogYW5kIGRlY2lkZTpcbiAgICAgICAgICAgICAqIDEuIElmIHRoZSB2YWx1ZSBoYXMgY2hhbmdlZCwgYW5kIG5lZWRzIGFuaW1hdGluZ1xuICAgICAgICAgICAgICogMi4gSWYgaXQgaGFzIGJlZW4gcmVtb3ZlZCwgYW5kIG5lZWRzIGFkZGluZyB0byB0aGUgcmVtb3ZlZEtleXMgc2V0XG4gICAgICAgICAgICAgKiAzLiBJZiBpdCBoYXMgYmVlbiByZW1vdmVkIGluIGEgaGlnaGVyIHByaW9yaXR5IHR5cGUgYW5kIG5lZWRzIGFuaW1hdGluZ1xuICAgICAgICAgICAgICogNC4gSWYgaXQgaGFzbid0IGJlZW4gcmVtb3ZlZCBpbiBhIGhpZ2hlciBwcmlvcml0eSBidXQgaGFzbid0IGNoYW5nZWQsIGFuZFxuICAgICAgICAgICAgICogICAgbmVlZHMgYWRkaW5nIHRvIHRoZSB0eXBlJ3MgcHJvdGVjdGVkS2V5cyBsaXN0LlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBjb25zdCB7IHByZXZSZXNvbHZlZFZhbHVlcyA9IHt9IH0gPSB0eXBlU3RhdGU7XG4gICAgICAgICAgICBjb25zdCBhbGxLZXlzID0ge1xuICAgICAgICAgICAgICAgIC4uLnByZXZSZXNvbHZlZFZhbHVlcyxcbiAgICAgICAgICAgICAgICAuLi5yZXNvbHZlZFZhbHVlcyxcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBjb25zdCBtYXJrVG9BbmltYXRlID0gKGtleSkgPT4ge1xuICAgICAgICAgICAgICAgIHNob3VsZEFuaW1hdGVUeXBlID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICBpZiAocmVtb3ZlZEtleXMuaGFzKGtleSkpIHtcbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlZFJlbW92ZWRWYWx1ZXMgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICByZW1vdmVkS2V5cy5kZWxldGUoa2V5KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdHlwZVN0YXRlLm5lZWRzQW5pbWF0aW5nW2tleV0gPSB0cnVlO1xuICAgICAgICAgICAgICAgIGNvbnN0IG1vdGlvblZhbHVlID0gdmlzdWFsRWxlbWVudC5nZXRWYWx1ZShrZXkpO1xuICAgICAgICAgICAgICAgIGlmIChtb3Rpb25WYWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgbW90aW9uVmFsdWUubGl2ZVN0eWxlID0gZmFsc2U7XG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gYWxsS2V5cykge1xuICAgICAgICAgICAgICAgIGNvbnN0IG5leHQgPSByZXNvbHZlZFZhbHVlc1trZXldO1xuICAgICAgICAgICAgICAgIGNvbnN0IHByZXYgPSBwcmV2UmVzb2x2ZWRWYWx1ZXNba2V5XTtcbiAgICAgICAgICAgICAgICAvLyBJZiB3ZSd2ZSBhbHJlYWR5IGhhbmRsZWQgdGhpcyB3ZSBjYW4ganVzdCBza2lwIGFoZWFkXG4gICAgICAgICAgICAgICAgaWYgKGVuY291bnRlcmVkS2V5cy5oYXNPd25Qcm9wZXJ0eShrZXkpKVxuICAgICAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAgICAgKiBJZiB0aGUgdmFsdWUgaGFzIGNoYW5nZWQsIHdlIHByb2JhYmx5IHdhbnQgdG8gYW5pbWF0ZSBpdC5cbiAgICAgICAgICAgICAgICAgKi9cbiAgICAgICAgICAgICAgICBsZXQgdmFsdWVIYXNDaGFuZ2VkID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgaWYgKGlzS2V5ZnJhbWVzVGFyZ2V0KG5leHQpICYmIGlzS2V5ZnJhbWVzVGFyZ2V0KHByZXYpKSB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlSGFzQ2hhbmdlZCA9ICFzaGFsbG93Q29tcGFyZShuZXh0LCBwcmV2KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlSGFzQ2hhbmdlZCA9IG5leHQgIT09IHByZXY7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICh2YWx1ZUhhc0NoYW5nZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG5leHQgIT09IHVuZGVmaW5lZCAmJiBuZXh0ICE9PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBJZiBuZXh0IGlzIGRlZmluZWQgYW5kIGRvZXNuJ3QgZXF1YWwgcHJldiwgaXQgbmVlZHMgYW5pbWF0aW5nXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXJrVG9BbmltYXRlKGtleSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBJZiBpdCdzIHVuZGVmaW5lZCwgaXQncyBiZWVuIHJlbW92ZWQuXG4gICAgICAgICAgICAgICAgICAgICAgICByZW1vdmVkS2V5cy5hZGQoa2V5KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChuZXh0ICE9PSB1bmRlZmluZWQgJiYgcmVtb3ZlZEtleXMuaGFzKGtleSkpIHtcbiAgICAgICAgICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgICAgICAgICAqIElmIG5leHQgaGFzbid0IGNoYW5nZWQgYW5kIGl0IGlzbid0IHVuZGVmaW5lZCwgd2Ugd2FudCB0byBjaGVjayBpZiBpdCdzXG4gICAgICAgICAgICAgICAgICAgICAqIGJlZW4gcmVtb3ZlZCBieSBhIGhpZ2hlciBwcmlvcml0eVxuICAgICAgICAgICAgICAgICAgICAgKi9cbiAgICAgICAgICAgICAgICAgICAgbWFya1RvQW5pbWF0ZShrZXkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgICAgICAgICAqIElmIGl0IGhhc24ndCBjaGFuZ2VkLCB3ZSBhZGQgaXQgdG8gdGhlIGxpc3Qgb2YgcHJvdGVjdGVkIHZhbHVlc1xuICAgICAgICAgICAgICAgICAgICAgKiB0byBlbnN1cmUgaXQgZG9lc24ndCBnZXQgYW5pbWF0ZWQuXG4gICAgICAgICAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgICAgICAgICB0eXBlU3RhdGUucHJvdGVjdGVkS2V5c1trZXldID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFVwZGF0ZSB0aGUgdHlwZVN0YXRlIHNvIG5leHQgdGltZSBhbmltYXRlQ2hhbmdlcyBpcyBjYWxsZWQgd2UgY2FuIGNvbXBhcmUgdGhlXG4gICAgICAgICAgICAgKiBsYXRlc3QgcHJvcCBhbmQgcmVzb2x2ZWRWYWx1ZXMgdG8gdGhlc2UuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIHR5cGVTdGF0ZS5wcmV2UHJvcCA9IHByb3A7XG4gICAgICAgICAgICB0eXBlU3RhdGUucHJldlJlc29sdmVkVmFsdWVzID0gcmVzb2x2ZWRWYWx1ZXM7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGlmICh0eXBlU3RhdGUuaXNBY3RpdmUpIHtcbiAgICAgICAgICAgICAgICBlbmNvdW50ZXJlZEtleXMgPSB7IC4uLmVuY291bnRlcmVkS2V5cywgLi4ucmVzb2x2ZWRWYWx1ZXMgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpc0luaXRpYWxSZW5kZXIgJiYgdmlzdWFsRWxlbWVudC5ibG9ja0luaXRpYWxBbmltYXRpb24pIHtcbiAgICAgICAgICAgICAgICBzaG91bGRBbmltYXRlVHlwZSA9IGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBJZiB0aGlzIGlzIGFuIGluaGVyaXRlZCBwcm9wIHdlIHdhbnQgdG8gc2tpcCB0aGlzIGFuaW1hdGlvblxuICAgICAgICAgICAgICogdW5sZXNzIHRoZSBpbmhlcml0ZWQgdmFyaWFudHMgaGF2ZW4ndCBjaGFuZ2VkIG9uIHRoaXMgcmVuZGVyLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBjb25zdCB3aWxsQW5pbWF0ZVZpYVBhcmVudCA9IGlzSW5oZXJpdGVkICYmIHZhcmlhbnREaWRDaGFuZ2U7XG4gICAgICAgICAgICBjb25zdCBuZWVkc0FuaW1hdGluZyA9ICF3aWxsQW5pbWF0ZVZpYVBhcmVudCB8fCBoYW5kbGVkUmVtb3ZlZFZhbHVlcztcbiAgICAgICAgICAgIGlmIChzaG91bGRBbmltYXRlVHlwZSAmJiBuZWVkc0FuaW1hdGluZykge1xuICAgICAgICAgICAgICAgIGFuaW1hdGlvbnMucHVzaCguLi5kZWZpbml0aW9uTGlzdC5tYXAoKGFuaW1hdGlvbikgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiBhbmltYXRpb24sXG4gICAgICAgICAgICAgICAgICAgIG9wdGlvbnM6IHsgdHlwZSB9LFxuICAgICAgICAgICAgICAgIH0pKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgLyoqXG4gICAgICAgICAqIElmIHRoZXJlIGFyZSBzb21lIHJlbW92ZWQgdmFsdWUgdGhhdCBoYXZlbid0IGJlZW4gZGVhbHQgd2l0aCxcbiAgICAgICAgICogd2UgbmVlZCB0byBjcmVhdGUgYSBuZXcgYW5pbWF0aW9uIHRoYXQgZmFsbHMgYmFjayBlaXRoZXIgdG8gdGhlIHZhbHVlXG4gICAgICAgICAqIGRlZmluZWQgaW4gdGhlIHN0eWxlIHByb3AsIG9yIHRoZSBsYXN0IHJlYWQgdmFsdWUuXG4gICAgICAgICAqL1xuICAgICAgICBpZiAocmVtb3ZlZEtleXMuc2l6ZSkge1xuICAgICAgICAgICAgY29uc3QgZmFsbGJhY2tBbmltYXRpb24gPSB7fTtcbiAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICogSWYgdGhlIGluaXRpYWwgcHJvcCBjb250YWlucyBhIHRyYW5zaXRpb24gd2UgY2FuIHVzZSB0aGF0LCBvdGhlcndpc2VcbiAgICAgICAgICAgICAqIGFsbG93IHRoZSBhbmltYXRpb24gZnVuY3Rpb24gdG8gdXNlIHRoZSB2aXN1YWwgZWxlbWVudCdzIGRlZmF1bHQuXG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGlmICh0eXBlb2YgcHJvcHMuaW5pdGlhbCAhPT0gXCJib29sZWFuXCIpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBpbml0aWFsVHJhbnNpdGlvbiA9IHJlc29sdmVWYXJpYW50KHZpc3VhbEVsZW1lbnQsIEFycmF5LmlzQXJyYXkocHJvcHMuaW5pdGlhbClcbiAgICAgICAgICAgICAgICAgICAgPyBwcm9wcy5pbml0aWFsWzBdXG4gICAgICAgICAgICAgICAgICAgIDogcHJvcHMuaW5pdGlhbCk7XG4gICAgICAgICAgICAgICAgaWYgKGluaXRpYWxUcmFuc2l0aW9uICYmIGluaXRpYWxUcmFuc2l0aW9uLnRyYW5zaXRpb24pIHtcbiAgICAgICAgICAgICAgICAgICAgZmFsbGJhY2tBbmltYXRpb24udHJhbnNpdGlvbiA9IGluaXRpYWxUcmFuc2l0aW9uLnRyYW5zaXRpb247XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmVtb3ZlZEtleXMuZm9yRWFjaCgoa2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmFsbGJhY2tUYXJnZXQgPSB2aXN1YWxFbGVtZW50LmdldEJhc2VUYXJnZXQoa2V5KTtcbiAgICAgICAgICAgICAgICBjb25zdCBtb3Rpb25WYWx1ZSA9IHZpc3VhbEVsZW1lbnQuZ2V0VmFsdWUoa2V5KTtcbiAgICAgICAgICAgICAgICBpZiAobW90aW9uVmFsdWUpXG4gICAgICAgICAgICAgICAgICAgIG1vdGlvblZhbHVlLmxpdmVTdHlsZSA9IHRydWU7XG4gICAgICAgICAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciAtIEBtYXR0Z3BlcnJ5IHRvIGZpZ3VyZSBpZiB3ZSBzaG91bGQgZG8gc29tZXRoaW5nIGhlcmVcbiAgICAgICAgICAgICAgICBmYWxsYmFja0FuaW1hdGlvbltrZXldID0gZmFsbGJhY2tUYXJnZXQgPz8gbnVsbDtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgYW5pbWF0aW9ucy5wdXNoKHsgYW5pbWF0aW9uOiBmYWxsYmFja0FuaW1hdGlvbiB9KTtcbiAgICAgICAgfVxuICAgICAgICBsZXQgc2hvdWxkQW5pbWF0ZSA9IEJvb2xlYW4oYW5pbWF0aW9ucy5sZW5ndGgpO1xuICAgICAgICBpZiAoaXNJbml0aWFsUmVuZGVyICYmXG4gICAgICAgICAgICAocHJvcHMuaW5pdGlhbCA9PT0gZmFsc2UgfHwgcHJvcHMuaW5pdGlhbCA9PT0gcHJvcHMuYW5pbWF0ZSkgJiZcbiAgICAgICAgICAgICF2aXN1YWxFbGVtZW50Lm1hbnVhbGx5QW5pbWF0ZU9uTW91bnQpIHtcbiAgICAgICAgICAgIHNob3VsZEFuaW1hdGUgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpc0luaXRpYWxSZW5kZXIgPSBmYWxzZTtcbiAgICAgICAgcmV0dXJuIHNob3VsZEFuaW1hdGUgPyBhbmltYXRlKGFuaW1hdGlvbnMpIDogUHJvbWlzZS5yZXNvbHZlKCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENoYW5nZSB3aGV0aGVyIGEgY2VydGFpbiBhbmltYXRpb24gdHlwZSBpcyBhY3RpdmUuXG4gICAgICovXG4gICAgZnVuY3Rpb24gc2V0QWN0aXZlKHR5cGUsIGlzQWN0aXZlKSB7XG4gICAgICAgIC8vIElmIHRoZSBhY3RpdmUgc3RhdGUgaGFzbid0IGNoYW5nZWQsIHdlIGNhbiBzYWZlbHkgZG8gbm90aGluZyBoZXJlXG4gICAgICAgIGlmIChzdGF0ZVt0eXBlXS5pc0FjdGl2ZSA9PT0gaXNBY3RpdmUpXG4gICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCk7XG4gICAgICAgIC8vIFByb3BhZ2F0ZSBhY3RpdmUgY2hhbmdlIHRvIGNoaWxkcmVuXG4gICAgICAgIHZpc3VhbEVsZW1lbnQudmFyaWFudENoaWxkcmVuPy5mb3JFYWNoKChjaGlsZCkgPT4gY2hpbGQuYW5pbWF0aW9uU3RhdGU/LnNldEFjdGl2ZSh0eXBlLCBpc0FjdGl2ZSkpO1xuICAgICAgICBzdGF0ZVt0eXBlXS5pc0FjdGl2ZSA9IGlzQWN0aXZlO1xuICAgICAgICBjb25zdCBhbmltYXRpb25zID0gYW5pbWF0ZUNoYW5nZXModHlwZSk7XG4gICAgICAgIGZvciAoY29uc3Qga2V5IGluIHN0YXRlKSB7XG4gICAgICAgICAgICBzdGF0ZVtrZXldLnByb3RlY3RlZEtleXMgPSB7fTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYW5pbWF0aW9ucztcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgICAgYW5pbWF0ZUNoYW5nZXMsXG4gICAgICAgIHNldEFjdGl2ZSxcbiAgICAgICAgc2V0QW5pbWF0ZUZ1bmN0aW9uLFxuICAgICAgICBnZXRTdGF0ZTogKCkgPT4gc3RhdGUsXG4gICAgICAgIHJlc2V0OiAoKSA9PiB7XG4gICAgICAgICAgICBzdGF0ZSA9IGNyZWF0ZVN0YXRlKCk7XG4gICAgICAgICAgICBpc0luaXRpYWxSZW5kZXIgPSB0cnVlO1xuICAgICAgICB9LFxuICAgIH07XG59XG5mdW5jdGlvbiBjaGVja1ZhcmlhbnRzRGlkQ2hhbmdlKHByZXYsIG5leHQpIHtcbiAgICBpZiAodHlwZW9mIG5leHQgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuIG5leHQgIT09IHByZXY7XG4gICAgfVxuICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkobmV4dCkpIHtcbiAgICAgICAgcmV0dXJuICFzaGFsbG93Q29tcGFyZShuZXh0LCBwcmV2KTtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xufVxuZnVuY3Rpb24gY3JlYXRlVHlwZVN0YXRlKGlzQWN0aXZlID0gZmFsc2UpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBpc0FjdGl2ZSxcbiAgICAgICAgcHJvdGVjdGVkS2V5czoge30sXG4gICAgICAgIG5lZWRzQW5pbWF0aW5nOiB7fSxcbiAgICAgICAgcHJldlJlc29sdmVkVmFsdWVzOiB7fSxcbiAgICB9O1xufVxuZnVuY3Rpb24gY3JlYXRlU3RhdGUoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgYW5pbWF0ZTogY3JlYXRlVHlwZVN0YXRlKHRydWUpLFxuICAgICAgICB3aGlsZUluVmlldzogY3JlYXRlVHlwZVN0YXRlKCksXG4gICAgICAgIHdoaWxlSG92ZXI6IGNyZWF0ZVR5cGVTdGF0ZSgpLFxuICAgICAgICB3aGlsZVRhcDogY3JlYXRlVHlwZVN0YXRlKCksXG4gICAgICAgIHdoaWxlRHJhZzogY3JlYXRlVHlwZVN0YXRlKCksXG4gICAgICAgIHdoaWxlRm9jdXM6IGNyZWF0ZVR5cGVTdGF0ZSgpLFxuICAgICAgICBleGl0OiBjcmVhdGVUeXBlU3RhdGUoKSxcbiAgICB9O1xufVxuXG5leHBvcnQgeyBjaGVja1ZhcmlhbnRzRGlkQ2hhbmdlLCBjcmVhdGVBbmltYXRpb25TdGF0ZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/animation-state.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compareByDepth: () => (/* binding */ compareByDepth)\n/* harmony export */ });\nconst compareByDepth = (a, b) => a.depth - b.depth;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL2NvbXBhcmUtYnktZGVwdGgubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFMEIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFx1dGlsc1xcY29tcGFyZS1ieS1kZXB0aC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgY29tcGFyZUJ5RGVwdGggPSAoYSwgYikgPT4gYS5kZXB0aCAtIGIuZGVwdGg7XG5cbmV4cG9ydCB7IGNvbXBhcmVCeURlcHRoIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlatTree: () => (/* binding */ FlatTree)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _compare_by_depth_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./compare-by-depth.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs\");\n\n\n\nclass FlatTree {\n    constructor() {\n        this.children = [];\n        this.isDirty = false;\n    }\n    add(child) {\n        (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.addUniqueItem)(this.children, child);\n        this.isDirty = true;\n    }\n    remove(child) {\n        (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.removeItem)(this.children, child);\n        this.isDirty = true;\n    }\n    forEach(callback) {\n        this.isDirty && this.children.sort(_compare_by_depth_mjs__WEBPACK_IMPORTED_MODULE_1__.compareByDepth);\n        this.isDirty = false;\n        this.children.forEach(callback);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL2ZsYXQtdHJlZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXlEO0FBQ0Q7O0FBRXhEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsMkRBQWE7QUFDckI7QUFDQTtBQUNBO0FBQ0EsUUFBUSx3REFBVTtBQUNsQjtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsaUVBQWM7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7O0FBRW9CIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcdXRpbHNcXGZsYXQtdHJlZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYWRkVW5pcXVlSXRlbSwgcmVtb3ZlSXRlbSB9IGZyb20gJ21vdGlvbi11dGlscyc7XG5pbXBvcnQgeyBjb21wYXJlQnlEZXB0aCB9IGZyb20gJy4vY29tcGFyZS1ieS1kZXB0aC5tanMnO1xuXG5jbGFzcyBGbGF0VHJlZSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMuY2hpbGRyZW4gPSBbXTtcbiAgICAgICAgdGhpcy5pc0RpcnR5ID0gZmFsc2U7XG4gICAgfVxuICAgIGFkZChjaGlsZCkge1xuICAgICAgICBhZGRVbmlxdWVJdGVtKHRoaXMuY2hpbGRyZW4sIGNoaWxkKTtcbiAgICAgICAgdGhpcy5pc0RpcnR5ID0gdHJ1ZTtcbiAgICB9XG4gICAgcmVtb3ZlKGNoaWxkKSB7XG4gICAgICAgIHJlbW92ZUl0ZW0odGhpcy5jaGlsZHJlbiwgY2hpbGQpO1xuICAgICAgICB0aGlzLmlzRGlydHkgPSB0cnVlO1xuICAgIH1cbiAgICBmb3JFYWNoKGNhbGxiYWNrKSB7XG4gICAgICAgIHRoaXMuaXNEaXJ0eSAmJiB0aGlzLmNoaWxkcmVuLnNvcnQoY29tcGFyZUJ5RGVwdGgpO1xuICAgICAgICB0aGlzLmlzRGlydHkgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5jaGlsZHJlbi5mb3JFYWNoKGNhbGxiYWNrKTtcbiAgICB9XG59XG5cbmV4cG9ydCB7IEZsYXRUcmVlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/get-variant-context.mjs":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/get-variant-context.mjs ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVariantContext: () => (/* binding */ getVariantContext)\n/* harmony export */ });\n/* harmony import */ var _is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is-variant-label.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs\");\n/* harmony import */ var _variant_props_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./variant-props.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/variant-props.mjs\");\n\n\n\nconst numVariantProps = _variant_props_mjs__WEBPACK_IMPORTED_MODULE_0__.variantProps.length;\nfunction getVariantContext(visualElement) {\n    if (!visualElement)\n        return undefined;\n    if (!visualElement.isControllingVariants) {\n        const context = visualElement.parent\n            ? getVariantContext(visualElement.parent) || {}\n            : {};\n        if (visualElement.props.initial !== undefined) {\n            context.initial = visualElement.props.initial;\n        }\n        return context;\n    }\n    const context = {};\n    for (let i = 0; i < numVariantProps; i++) {\n        const name = _variant_props_mjs__WEBPACK_IMPORTED_MODULE_0__.variantProps[i];\n        const prop = visualElement.props[name];\n        if ((0,_is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_1__.isVariantLabel)(prop) || prop === false) {\n            context[name] = prop;\n        }\n    }\n    return context;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/get-variant-context.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isControllingVariants: () => (/* binding */ isControllingVariants),\n/* harmony export */   isVariantNode: () => (/* binding */ isVariantNode)\n/* harmony export */ });\n/* harmony import */ var _animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../animation/utils/is-animation-controls.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs\");\n/* harmony import */ var _is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is-variant-label.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs\");\n/* harmony import */ var _variant_props_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./variant-props.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/variant-props.mjs\");\n\n\n\n\nfunction isControllingVariants(props) {\n    return ((0,_animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_0__.isAnimationControls)(props.animate) ||\n        _variant_props_mjs__WEBPACK_IMPORTED_MODULE_1__.variantProps.some((name) => (0,_is_variant_label_mjs__WEBPACK_IMPORTED_MODULE_2__.isVariantLabel)(props[name])));\n}\nfunction isVariantNode(props) {\n    return Boolean(isControllingVariants(props) || props.variants);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL2lzLWNvbnRyb2xsaW5nLXZhcmlhbnRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzRjtBQUM5QjtBQUNMOztBQUVuRDtBQUNBLFlBQVksK0ZBQW1CO0FBQy9CLFFBQVEsNERBQVksZ0JBQWdCLHFFQUFjO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBOztBQUVnRCIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXHV0aWxzXFxpcy1jb250cm9sbGluZy12YXJpYW50cy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNBbmltYXRpb25Db250cm9scyB9IGZyb20gJy4uLy4uL2FuaW1hdGlvbi91dGlscy9pcy1hbmltYXRpb24tY29udHJvbHMubWpzJztcbmltcG9ydCB7IGlzVmFyaWFudExhYmVsIH0gZnJvbSAnLi9pcy12YXJpYW50LWxhYmVsLm1qcyc7XG5pbXBvcnQgeyB2YXJpYW50UHJvcHMgfSBmcm9tICcuL3ZhcmlhbnQtcHJvcHMubWpzJztcblxuZnVuY3Rpb24gaXNDb250cm9sbGluZ1ZhcmlhbnRzKHByb3BzKSB7XG4gICAgcmV0dXJuIChpc0FuaW1hdGlvbkNvbnRyb2xzKHByb3BzLmFuaW1hdGUpIHx8XG4gICAgICAgIHZhcmlhbnRQcm9wcy5zb21lKChuYW1lKSA9PiBpc1ZhcmlhbnRMYWJlbChwcm9wc1tuYW1lXSkpKTtcbn1cbmZ1bmN0aW9uIGlzVmFyaWFudE5vZGUocHJvcHMpIHtcbiAgICByZXR1cm4gQm9vbGVhbihpc0NvbnRyb2xsaW5nVmFyaWFudHMocHJvcHMpIHx8IHByb3BzLnZhcmlhbnRzKTtcbn1cblxuZXhwb3J0IHsgaXNDb250cm9sbGluZ1ZhcmlhbnRzLCBpc1ZhcmlhbnROb2RlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isVariantLabel: () => (/* binding */ isVariantLabel)\n/* harmony export */ });\n/**\n * Decides if the supplied variable is variant label\n */\nfunction isVariantLabel(v) {\n    return typeof v === \"string\" || Array.isArray(v);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL2lzLXZhcmlhbnQtbGFiZWwubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTBCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcdXRpbHNcXGlzLXZhcmlhbnQtbGFiZWwubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRGVjaWRlcyBpZiB0aGUgc3VwcGxpZWQgdmFyaWFibGUgaXMgdmFyaWFudCBsYWJlbFxuICovXG5mdW5jdGlvbiBpc1ZhcmlhbnRMYWJlbCh2KSB7XG4gICAgcmV0dXJuIHR5cGVvZiB2ID09PSBcInN0cmluZ1wiIHx8IEFycmF5LmlzQXJyYXkodik7XG59XG5cbmV4cG9ydCB7IGlzVmFyaWFudExhYmVsIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/motion-values.mjs":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/motion-values.mjs ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   updateMotionValuesFromProps: () => (/* binding */ updateMotionValuesFromProps)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n\n\nfunction updateMotionValuesFromProps(element, next, prev) {\n    for (const key in next) {\n        const nextValue = next[key];\n        const prevValue = prev[key];\n        if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(nextValue)) {\n            /**\n             * If this is a motion value found in props or style, we want to add it\n             * to our visual element's motion value map.\n             */\n            element.addValue(key, nextValue);\n        }\n        else if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isMotionValue)(prevValue)) {\n            /**\n             * If we're swapping from a motion value to a static value,\n             * create a new motion value from that\n             */\n            element.addValue(key, (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.motionValue)(nextValue, { owner: element }));\n        }\n        else if (prevValue !== nextValue) {\n            /**\n             * If this is a flat value that has changed, update the motion value\n             * or create one if it doesn't exist. We only want to do this if we're\n             * not handling the value with our animation state.\n             */\n            if (element.hasValue(key)) {\n                const existingValue = element.getValue(key);\n                if (existingValue.liveStyle === true) {\n                    existingValue.jump(nextValue);\n                }\n                else if (!existingValue.hasAnimated) {\n                    existingValue.set(nextValue);\n                }\n            }\n            else {\n                const latestValue = element.getStaticValue(key);\n                element.addValue(key, (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.motionValue)(latestValue !== undefined ? latestValue : nextValue, { owner: element }));\n            }\n        }\n    }\n    // Handle removed values\n    for (const key in prev) {\n        if (next[key] === undefined)\n            element.removeValue(key);\n    }\n    return next;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/motion-values.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveVariant: () => (/* binding */ resolveVariant)\n/* harmony export */ });\n/* harmony import */ var _resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./resolve-variants.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs\");\n\n\nfunction resolveVariant(visualElement, definition, custom) {\n    const props = visualElement.getProps();\n    return (0,_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveVariantFromProps)(props, definition, custom !== undefined ? custom : props.custom, visualElement);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL3Jlc29sdmUtZHluYW1pYy12YXJpYW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUU7O0FBRWpFO0FBQ0E7QUFDQSxXQUFXLDhFQUF1QjtBQUNsQzs7QUFFMEIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFx1dGlsc1xccmVzb2x2ZS1keW5hbWljLXZhcmlhbnRzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZXNvbHZlVmFyaWFudEZyb21Qcm9wcyB9IGZyb20gJy4vcmVzb2x2ZS12YXJpYW50cy5tanMnO1xuXG5mdW5jdGlvbiByZXNvbHZlVmFyaWFudCh2aXN1YWxFbGVtZW50LCBkZWZpbml0aW9uLCBjdXN0b20pIHtcbiAgICBjb25zdCBwcm9wcyA9IHZpc3VhbEVsZW1lbnQuZ2V0UHJvcHMoKTtcbiAgICByZXR1cm4gcmVzb2x2ZVZhcmlhbnRGcm9tUHJvcHMocHJvcHMsIGRlZmluaXRpb24sIGN1c3RvbSAhPT0gdW5kZWZpbmVkID8gY3VzdG9tIDogcHJvcHMuY3VzdG9tLCB2aXN1YWxFbGVtZW50KTtcbn1cblxuZXhwb3J0IHsgcmVzb2x2ZVZhcmlhbnQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveVariantFromProps: () => (/* binding */ resolveVariantFromProps)\n/* harmony export */ });\nfunction getValueState(visualElement) {\n    const state = [{}, {}];\n    visualElement?.values.forEach((value, key) => {\n        state[0][key] = value.get();\n        state[1][key] = value.getVelocity();\n    });\n    return state;\n}\nfunction resolveVariantFromProps(props, definition, custom, visualElement) {\n    /**\n     * If the variant definition is a function, resolve.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    /**\n     * If the variant definition is a variant label, or\n     * the function returned a variant label, resolve.\n     */\n    if (typeof definition === \"string\") {\n        definition = props.variants && props.variants[definition];\n    }\n    /**\n     * At this point we've resolved both functions and variant labels,\n     * but the resolved variant label might itself have been a function.\n     * If so, resolve. This can only have returned a valid target object.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    return definition;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/setters.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/setters.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setTarget: () => (/* binding */ setTarget)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-browser)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../animation/utils/is-keyframes-target.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs\");\n/* harmony import */ var _resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resolve-dynamic-variants.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs\");\n\n\n\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n    if (visualElement.hasValue(key)) {\n        visualElement.getValue(key).set(value);\n    }\n    else {\n        visualElement.addValue(key, (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.motionValue)(value));\n    }\n}\nfunction resolveFinalValueInKeyframes(v) {\n    // TODO maybe throw if v.length - 1 is placeholder token?\n    return (0,_animation_utils_is_keyframes_target_mjs__WEBPACK_IMPORTED_MODULE_1__.isKeyframesTarget)(v) ? v[v.length - 1] || 0 : v;\n}\nfunction setTarget(visualElement, definition) {\n    const resolved = (0,_resolve_dynamic_variants_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveVariant)(visualElement, definition);\n    let { transitionEnd = {}, transition = {}, ...target } = resolved || {};\n    target = { ...target, ...transitionEnd };\n    for (const key in target) {\n        const value = resolveFinalValueInKeyframes(target[key]);\n        setMotionValue(visualElement, key, value);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/setters.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/variant-props.mjs":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/variant-props.mjs ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   variantPriorityOrder: () => (/* binding */ variantPriorityOrder),\n/* harmony export */   variantProps: () => (/* binding */ variantProps)\n/* harmony export */ });\nconst variantPriorityOrder = [\n    \"animate\",\n    \"whileInView\",\n    \"whileFocus\",\n    \"whileHover\",\n    \"whileTap\",\n    \"whileDrag\",\n    \"exit\",\n];\nconst variantProps = [\"initial\", ...variantPriorityOrder];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL3V0aWxzL3ZhcmlhbnQtcHJvcHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRThDIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcdXRpbHNcXHZhcmlhbnQtcHJvcHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHZhcmlhbnRQcmlvcml0eU9yZGVyID0gW1xuICAgIFwiYW5pbWF0ZVwiLFxuICAgIFwid2hpbGVJblZpZXdcIixcbiAgICBcIndoaWxlRm9jdXNcIixcbiAgICBcIndoaWxlSG92ZXJcIixcbiAgICBcIndoaWxlVGFwXCIsXG4gICAgXCJ3aGlsZURyYWdcIixcbiAgICBcImV4aXRcIixcbl07XG5jb25zdCB2YXJpYW50UHJvcHMgPSBbXCJpbml0aWFsXCIsIC4uLnZhcmlhbnRQcmlvcml0eU9yZGVyXTtcblxuZXhwb3J0IHsgdmFyaWFudFByaW9yaXR5T3JkZXIsIHZhcmlhbnRQcm9wcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/variant-props.mjs\n"));

/***/ })

}]);