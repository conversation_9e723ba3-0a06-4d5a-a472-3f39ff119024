"use strict";exports.id=1516,exports.ids=[1516],exports.modules={487:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getGapWidth=t.zeroGap=void 0,t.zeroGap={left:0,top:0,right:0,gap:0};var n=function(e){return parseInt(e||"",10)||0},r=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],o=t["padding"===e?"paddingTop":"marginTop"],a=t["padding"===e?"paddingRight":"marginRight"];return[n(r),n(o),n(a)]};t.getGapWidth=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t.zeroGap;var n=r(e),o=document.documentElement.clientWidth,a=window.innerWidth;return{left:n[0],top:n[1],right:n[2],gap:Math.max(0,a-o+n[2]-n[0])}}},14032:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getGapWidth=t.removedBarSizeVariable=t.noScrollbarsClassName=t.fullWidthClassName=t.zeroRightClassName=t.RemoveScrollBar=void 0;var r=n(86637);Object.defineProperty(t,"RemoveScrollBar",{enumerable:!0,get:function(){return r.RemoveScrollBar}});var o=n(26811);Object.defineProperty(t,"zeroRightClassName",{enumerable:!0,get:function(){return o.zeroRightClassName}}),Object.defineProperty(t,"fullWidthClassName",{enumerable:!0,get:function(){return o.fullWidthClassName}}),Object.defineProperty(t,"noScrollbarsClassName",{enumerable:!0,get:function(){return o.noScrollbarsClassName}}),Object.defineProperty(t,"removedBarSizeVariable",{enumerable:!0,get:function(){return o.removedBarSizeVariable}});var a=n(487);Object.defineProperty(t,"getGapWidth",{enumerable:!0,get:function(){return a.getGapWidth}})},16568:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(5818),o=r.__importStar(n(82015)),a=n(71289),l=r.__importDefault(n(82172)),i=o.forwardRef(function(e,t){return o.createElement(a.RemoveScroll,r.__assign({},e,{ref:t,sideCar:l.default}))});i.classNames=a.RemoveScroll.classNames,t.default=i},17793:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveScrollSideCar=t.getDeltaXY=t.getTouchXY=void 0;var r=n(5818),o=r.__importStar(n(82015)),a=n(14032),l=n(13594),i=n(39384),c=n(59334);t.getTouchXY=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},t.getDeltaXY=function(e){return[e.deltaX,e.deltaY]};var u=function(e){return e&&"current"in e?e.current:e},s=0,d=[];t.RemoveScrollSideCar=function(e){var n=o.useRef([]),f=o.useRef([0,0]),v=o.useRef(),p=o.useState(s++)[0],m=o.useState(l.styleSingleton)[0],g=o.useRef(e);o.useEffect(function(){g.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(p));var t=r.__spreadArray([e.lockRef.current],(e.shards||[]).map(u),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(p))}),function(){document.body.classList.remove("block-interactivity-".concat(p)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(p))})}}},[e.inert,e.lockRef.current,e.shards]);var h=o.useCallback(function(e,n){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!g.current.allowPinchZoom;var r,o=(0,t.getTouchXY)(e),a=f.current,l="deltaX"in e?e.deltaX:a[0]-o[0],i="deltaY"in e?e.deltaY:a[1]-o[1],u=e.target,s=Math.abs(l)>Math.abs(i)?"h":"v";if("touches"in e&&"h"===s&&"range"===u.type)return!1;var d=(0,c.locationCouldBeScrolled)(s,u);if(!d)return!0;if(d?r=s:(r="v"===s?"h":"v",d=(0,c.locationCouldBeScrolled)(s,u)),!d)return!1;if(!v.current&&"changedTouches"in e&&(l||i)&&(v.current=r),!r)return!0;var p=v.current||r;return(0,c.handleScroll)(p,n,e,"h"===p?l:i,!0)},[]),b=o.useCallback(function(e){if(d.length&&d[d.length-1]===m){var r="deltaY"in e?(0,t.getDeltaXY)(e):(0,t.getTouchXY)(e),o=n.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(o&&o.should){e.cancelable&&e.preventDefault();return}if(!o){var a=(g.current.shards||[]).map(u).filter(Boolean).filter(function(t){return t.contains(e.target)});(a.length>0?h(e,a[0]):!g.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),y=o.useCallback(function(e,t,r,o){var a={name:e,delta:t,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};n.current.push(a),setTimeout(function(){n.current=n.current.filter(function(e){return e!==a})},1)},[]),S=o.useCallback(function(e){f.current=(0,t.getTouchXY)(e),v.current=void 0},[]),C=o.useCallback(function(n){y(n.type,(0,t.getDeltaXY)(n),n.target,h(n,e.lockRef.current))},[]),w=o.useCallback(function(n){y(n.type,(0,t.getTouchXY)(n),n.target,h(n,e.lockRef.current))},[]);o.useEffect(function(){return d.push(m),e.setCallbacks({onScrollCapture:C,onWheelCapture:C,onTouchMoveCapture:w}),document.addEventListener("wheel",b,i.nonPassive),document.addEventListener("touchmove",b,i.nonPassive),document.addEventListener("touchstart",S,i.nonPassive),function(){d=d.filter(function(e){return e!==m}),document.removeEventListener("wheel",b,i.nonPassive),document.removeEventListener("touchmove",b,i.nonPassive),document.removeEventListener("touchstart",S,i.nonPassive)}},[]);var _=e.removeScrollBar,R=e.inert;return o.createElement(o.Fragment,null,R?o.createElement(m,{styles:"\n  .block-interactivity-".concat(p," {pointer-events: none;}\n  .allow-interactivity-").concat(p," {pointer-events: all;}\n")}):null,_?o.createElement(a.RemoveScrollBar,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}},23711:(e,t,n)=>{n.d(t,{Kq:()=>N});var r=n(82015);n(97645);var o=Symbol.for("react.forward_ref"),a=Symbol.for("react.memo");function l(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}var i={notify(){},get:()=>[]},c=7311==n.j?"undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement:null,u=7311==n.j?"undefined"!=typeof navigator&&"ReactNative"===navigator.product:null,s=7311==n.j?c||u?r.useLayoutEffect:r.useEffect:null;function d(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}var f={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},v={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},p={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},m={[o]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[a]:p};function g(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case null:switch(e=e.type){case null:case null:case null:case null:case null:return e;default:switch(e=e&&e.$$typeof){case null:case o:case null:case a:case null:return e;default:return t}}case null:return t}}}(e)===a?p:m[e.$$typeof]||f}var h=Object.defineProperty,b=Object.getOwnPropertyNames,y=Object.getOwnPropertySymbols,S=Object.getOwnPropertyDescriptor,C=Object.getPrototypeOf,w=Object.prototype,_=7311==n.j?Symbol.for("react-redux-context"):null,R="undefined"!=typeof globalThis?globalThis:{},P=7311==n.j?function(){if(!r.createContext)return{};let e=R[_]??=new Map,t=e.get(r.createContext);return t||(t=r.createContext(null),e.set(r.createContext,t)),t}():null,N=7311==n.j?function(e){let{children:t,context:n,serverState:o,store:a}=e,l=r.useMemo(()=>{let e=function(e,t){let n,r=i,o=0,a=!1;function l(){s.onStateChange&&s.onStateChange()}function c(){if(o++,!n){let t,o;n=e.subscribe(l),t=null,o=null,r={clear(){t=null,o=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let n=!0,r=o={callback:e,next:null,prev:o};return r.prev?r.prev.next=r:t=r,function(){n&&null!==t&&(n=!1,r.next?r.next.prev=r.prev:o=r.prev,r.prev?r.prev.next=r.next:t=r.next)}}}}}function u(){o--,n&&0===o&&(n(),n=void 0,r.clear(),r=i)}let s={addNestedSub:function(e){c();let t=r.subscribe(e),n=!1;return()=>{n||(n=!0,t(),u())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:l,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,c())},tryUnsubscribe:function(){a&&(a=!1,u())},getListeners:()=>r};return s}(a);return{store:a,subscription:e,getServerState:o?()=>o:void 0}},[a,o]),c=r.useMemo(()=>a.getState(),[a]);return s(()=>{let{subscription:e}=l;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),c!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[l,c]),r.createElement((n||P).Provider,{value:l},t)}:null},26811:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.removedBarSizeVariable=t.noScrollbarsClassName=t.fullWidthClassName=t.zeroRightClassName=void 0,t.zeroRightClassName="right-scroll-bar-position",t.fullWidthClassName="width-before-scroll-bar",t.noScrollbarsClassName="with-scroll-bars-hidden",t.removedBarSizeVariable="--removed-body-scroll-bar-size"},39384:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.nonPassive=void 0;var n=!1;if("undefined"!=typeof window)try{var r=Object.defineProperty({},"passive",{get:function(){return n=!0,!0}});window.addEventListener("test",r,r),window.removeEventListener("test",r,r)}catch(e){n=!1}t.nonPassive=!!n&&{passive:!1}},59334:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.handleScroll=t.locationCouldBeScrolled=void 0;var n=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])};t.locationCouldBeScrolled=function(e,t){var n=t.ownerDocument,a=t;do{if("undefined"!=typeof ShadowRoot&&a instanceof ShadowRoot&&(a=a.host),r(e,a)){var l=o(e,a);if(l[1]>l[2])return!0}a=a.parentNode}while(a&&a!==n.body);return!1};var r=function(e,t){return"v"===e?n(t,"overflowY"):n(t,"overflowX")},o=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]};t.handleScroll=function(e,t,n,a,l){var i,c=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),u=c*a,s=n.target,d=t.contains(s),f=!1,v=u>0,p=0,m=0;do{if(!s)break;var g=o(e,s),h=g[0],b=g[1]-g[2]-c*h;(h||b)&&r(e,s)&&(p+=b,m+=h);var y=s.parentNode;s=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!d&&s!==document.body||d&&(t.contains(s)||t===s));return v&&(l&&1>Math.abs(p)||!l&&u>p)?f=!0:!v&&(l&&1>Math.abs(m)||!l&&-u>m)&&(f=!0),f}},71289:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveScroll=void 0;var r=n(5818),o=r.__importStar(n(82015)),a=n(26811),l=n(13658),i=n(89430),c=function(){},u=o.forwardRef(function(e,t){var n=o.useRef(null),a=o.useState({onScrollCapture:c,onWheelCapture:c,onTouchMoveCapture:c}),u=a[0],s=a[1],d=e.forwardProps,f=e.children,v=e.className,p=e.removeScrollBar,m=e.enabled,g=e.shards,h=e.sideCar,b=e.noRelative,y=e.noIsolation,S=e.inert,C=e.allowPinchZoom,w=e.as,_=e.gapMode,R=r.__rest(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(0,l.useMergeRefs)([n,t]),N=r.__assign(r.__assign({},R),u);return o.createElement(o.Fragment,null,m&&o.createElement(h,{sideCar:i.effectCar,removeScrollBar:p,shards:g,noRelative:b,noIsolation:y,inert:S,setCallbacks:s,allowPinchZoom:!!C,lockRef:n,gapMode:_}),d?o.cloneElement(o.Children.only(f),r.__assign(r.__assign({},N),{ref:P})):o.createElement(void 0===w?"div":w,r.__assign({},N,{className:v,ref:P}),f))});t.RemoveScroll=u,u.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},u.classNames={fullWidth:a.fullWidthClassName,zeroRight:a.zeroRightClassName}},77897:(e,t,n)=>{t.G=void 0,t.G=n(5818).__importDefault(n(16568)).default},82172:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(34960),o=n(17793),a=n(89430);t.default=(0,r.exportSidecar)(a.effectCar,o.RemoveScrollSideCar)},86637:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveScrollBar=t.useLockAttribute=t.lockAttribute=void 0;var r=n(5818).__importStar(n(82015)),o=n(13594),a=n(26811),l=n(487),i=(0,o.styleSingleton)();t.lockAttribute="data-scroll-locked";var c=function(e,n,r,o){var l=e.left,i=e.top,c=e.right,u=e.gap;return void 0===r&&(r="margin"),"\n  .".concat(a.noScrollbarsClassName," {\n   overflow: hidden ").concat(o,";\n   padding-right: ").concat(u,"px ").concat(o,";\n  }\n  body[").concat(t.lockAttribute,"] {\n    overflow: hidden ").concat(o,";\n    overscroll-behavior: contain;\n    ").concat([n&&"position: relative ".concat(o,";"),"margin"===r&&"\n    padding-left: ".concat(l,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(o,";\n    "),"padding"===r&&"padding-right: ".concat(u,"px ").concat(o,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a.zeroRightClassName," {\n    right: ").concat(u,"px ").concat(o,";\n  }\n  \n  .").concat(a.fullWidthClassName," {\n    margin-right: ").concat(u,"px ").concat(o,";\n  }\n  \n  .").concat(a.zeroRightClassName," .").concat(a.zeroRightClassName," {\n    right: 0 ").concat(o,";\n  }\n  \n  .").concat(a.fullWidthClassName," .").concat(a.fullWidthClassName," {\n    margin-right: 0 ").concat(o,";\n  }\n  \n  body[").concat(t.lockAttribute,"] {\n    ").concat(a.removedBarSizeVariable,": ").concat(u,"px;\n  }\n")},u=function(){var e=parseInt(document.body.getAttribute(t.lockAttribute)||"0",10);return isFinite(e)?e:0};t.useLockAttribute=function(){r.useEffect(function(){return document.body.setAttribute(t.lockAttribute,(u()+1).toString()),function(){var e=u()-1;e<=0?document.body.removeAttribute(t.lockAttribute):document.body.setAttribute(t.lockAttribute,e.toString())}},[])},t.RemoveScrollBar=function(e){var n=e.noRelative,o=e.noImportant,a=e.gapMode,u=void 0===a?"margin":a;(0,t.useLockAttribute)();var s=r.useMemo(function(){return(0,l.getGapWidth)(u)},[u]);return r.createElement(i,{styles:c(s,!n,u,o?"":"!important")})}},89430:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.effectCar=void 0,t.effectCar=(0,n(34960).createSidecarMedium)()}};