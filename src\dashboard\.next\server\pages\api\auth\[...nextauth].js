"use strict";(()=>{var e={};e.id=4651,e.ids=[4651],e.modules={20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},91863:(e,r,t)=>{t.r(r),t.d(r,{config:()=>d,default:()=>o,routeModule:()=>p});var a=t(93433),i=t(20264),u=t(20584),s=t(94506);let o=(0,u.M)(s,"default"),d=(0,u.M)(s,"config"),p=new a.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/auth/[...nextauth]",pathname:"/api/auth/[...nextauth]",bundlePath:"",filename:""},userland:s})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(91863));module.exports=a})();