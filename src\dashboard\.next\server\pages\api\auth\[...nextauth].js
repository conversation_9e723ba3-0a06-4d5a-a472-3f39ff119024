"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\auth\\[...nextauth].ts */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./core/config.ts":
/*!************************!*\
  !*** ./core/config.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dashboardConfig: () => (/* binding */ dashboardConfig),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n// @ts-nocheck\n\n\n\nlet config = {};\ntry {\n    // Locate config.yml by walking up directories (dashboard may run from nested cwd)\n    const possible = [\n        'config.yml',\n        '../config.yml',\n        '../../config.yml',\n        '../../../config.yml',\n        '../../../../config.yml'\n    ].map((rel)=>path__WEBPACK_IMPORTED_MODULE_2__.resolve(process.cwd(), rel));\n    let configPath = possible.find((p)=>fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(p));\n    if (!configPath) {\n        // fallback relative to file location\n        const dirBased = path__WEBPACK_IMPORTED_MODULE_2__.resolve(__dirname, '../../../config.yml');\n        if (fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(dirBased)) configPath = dirBased;\n    }\n    if (!configPath) {\n        throw new Error('config.yml not found');\n    }\n    const fileContents = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(configPath, 'utf8');\n    config = yaml__WEBPACK_IMPORTED_MODULE_1___default().parse(fileContents);\n} catch (error) {\n    console.error('Error: Failed to load config.yml:', error);\n    process.exit(1); // Exit if we can't load the config\n}\n// Export the dashboard-specific config\nconst dashboardConfig = {\n    bot: {\n        token: config.bot.token,\n        clientId: config.bot.clientId,\n        clientSecret: config.bot.clientSecret,\n        guildId: config.bot.guildId,\n        ticketCategoryId: config.bot.ticketCategoryId || null,\n        ticketLogChannelId: config.bot.ticketLogChannelId || null,\n        prefix: config.bot.prefix\n    },\n    dashboard: {\n        admins: config.dashboard?.admins || [],\n        adminRoleIds: config.dashboard?.adminRoleIds || [],\n        session: {\n            secret: config.dashboard?.session?.secret || config.bot.clientSecret\n        }\n    },\n    database: {\n        url: config.database.url,\n        name: config.database.name,\n        options: {\n            maxPoolSize: config.database.options?.maxPoolSize || 10,\n            minPoolSize: config.database.options?.minPoolSize || 1,\n            maxIdleTimeMS: config.database.options?.maxIdleTimeMS || 30000,\n            serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,\n            socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,\n            connectTimeoutMS: config.database.options?.connectTimeoutMS || 10000,\n            retryWrites: config.database.options?.retryWrites !== false,\n            retryReads: config.database.options?.retryReads !== false\n        }\n    }\n};\n// Validate required configuration\nif (!dashboardConfig.bot.token) {\n    console.error('Error: Discord bot token is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.clientId || !dashboardConfig.bot.clientSecret) {\n    console.error('Error: Discord OAuth2 credentials (clientId and clientSecret) are required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.guildId) {\n    console.error('Error: Guild ID is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.database.url || !dashboardConfig.database.name) {\n    console.error('Error: Database configuration (url and name) is required in config.yml');\n    process.exit(1);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dashboardConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./core/config.ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/discord */ \"next-auth/providers/discord\");\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nconst authOptions = {\n    providers: [\n        next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId,\n            clientSecret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n            authorization: {\n                params: {\n                    scope: 'identify email guilds'\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, account, profile }) {\n            if (account && profile) {\n                token.accessToken = account.access_token || null;\n                token.id = profile.id || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                // Ensure we have valid values for serialization\n                const userId = token.id || null;\n                const accessToken = token.accessToken || null;\n                // Attach Discord user ID to session\n                session.user.id = userId;\n                session.user.accessToken = accessToken;\n                // Default to false for admin status\n                let isAdmin = false;\n                if (userId) {\n                    console.log('Checking admin status for user:', userId);\n                    // Check explicit admin IDs\n                    const adminIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.admins || [];\n                    console.log('Configured admin IDs:', adminIds);\n                    if (adminIds.includes(userId)) {\n                        console.log('User is in admin list');\n                        isAdmin = true;\n                    } else {\n                        console.log('User not in admin list, checking roles...');\n                        // Check roles if configured\n                        const roleIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.adminRoleIds || [];\n                        console.log('Configured admin role IDs:', roleIds);\n                        if (roleIds.length && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId) {\n                            try {\n                                console.log('Fetching member roles from Discord API...');\n                                console.log('Bot token (first 20 chars):', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token.substring(0, 20) + '...');\n                                console.log('Guild ID:', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId);\n                                console.log('User ID:', userId);\n                                console.log('Admin role IDs to check:', roleIds);\n                                const res = await fetch(`https://discord.com/api/v10/guilds/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId}/members/${userId}`, {\n                                    headers: {\n                                        Authorization: `Bot ${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token}`\n                                    }\n                                });\n                                console.log('Discord API response status:', res.status);\n                                if (res.ok) {\n                                    const member = await res.json();\n                                    console.log('Member data:', JSON.stringify(member, null, 2));\n                                    console.log('Member roles:', member.roles);\n                                    isAdmin = roleIds.some((rid)=>member.roles?.includes(rid)) || false;\n                                    console.log('Has admin role:', isAdmin);\n                                } else {\n                                    const errorText = await res.text();\n                                    console.error('Failed to fetch member - Status:', res.status);\n                                    console.error('Error response:', errorText);\n                                }\n                            } catch (error) {\n                                console.error('Failed to fetch guild member:', error);\n                            }\n                        } else {\n                            console.log('No role IDs configured or missing bot token/guild ID');\n                        }\n                    }\n                } else {\n                    console.log('No user ID available');\n                }\n                // Set admin status\n                session.user.isAdmin = isAdmin;\n                console.log('Final admin status:', isAdmin);\n                // Ensure all session values are serializable\n                session.user = {\n                    ...session.user,\n                    id: session.user.id || null,\n                    accessToken: session.user.accessToken || null,\n                    isAdmin: session.user.isAdmin || false,\n                    name: session.user.name || null,\n                    email: session.user.email || null,\n                    image: session.user.image || null\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Dynamically derive equivalent localhost URL (same protocol & port)\n            const parsed = new URL(baseUrl);\n            const localhostBase = `${parsed.protocol}//localhost${parsed.port ? `:${parsed.port}` : ''}`;\n            if (url.startsWith(baseUrl) || url.startsWith(localhostBase)) {\n                return url;\n            }\n            return baseUrl;\n        }\n    },\n    secret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.session.secret || _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n    pages: {\n        signIn: '/signin'\n    },\n    // Always show auth errors, but only debug logs in development\n    debug: \"development\" === 'development',\n    logger: {\n        error: (code, metadata)=>{\n            console.error('[NextAuth Error]', code, metadata);\n        },\n        warn: (code)=>{\n            if (true) {\n                console.warn('[NextAuth Warn]', code);\n            }\n        },\n        debug: (code, metadata)=>{\n            if (true) {\n                console.debug('[NextAuth Debug]', code, metadata);\n            }\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();