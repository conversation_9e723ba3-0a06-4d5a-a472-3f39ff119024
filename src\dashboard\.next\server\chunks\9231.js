"use strict";exports.id=9231,exports.ids=[9231],exports.modules={23244:(e,o,s)=>{s.a(e,async(e,r)=>{try{s.d(o,{Badge:()=>i.E,Box:()=>t.a,Button:()=>l.$,HStack:()=>a.z,Icon:()=>c.I,List:()=>n.B8,ListIcon:()=>n.kp,ListItem:()=>n.ck,Modal:()=>d.aF,ModalBody:()=>x.c,ModalCloseButton:()=>p.s,ModalContent:()=>h.$,ModalFooter:()=>j.j,ModalHeader:()=>g.r,ModalOverlay:()=>m.m,Text:()=>u.E,Tooltip:()=>y.m,VStack:()=>b.T,useDisclosure:()=>v.j});var i=s(25392),t=s(45200),l=s(77502),a=s(55197),c=s(50792),n=s(36058),d=s(75460),x=s(42929),p=s(7394),h=s(89164),j=s(87346),g=s(95148),m=s(12725),u=s(87378),y=s(63792),b=s(17335),v=s(66646);s(9436),s(25035);var k=e([i,t,l,a,c,n,d,x,p,h,j,g,m,u,y,b]);[i,t,l,a,c,n,d,x,p,h,j,g,m,u,y,b]=k.then?(await k)():k,r()}catch(e){r(e)}})},61227:(e,o,s)=>{s.d(o,{A3x:()=>r.FiCheckCircle,Ohp:()=>r.FiClock,cfS:()=>r.FiUsers,wIk:()=>r.FiCalendar,y3G:()=>r.FiAlertCircle});var r=s(64960)},69231:(e,o,s)=>{s.a(e,async(e,r)=>{try{s.r(o),s.d(o,{ApplicationCard:()=>n});var i=s(8732);s(82015);var t=s(23244),l=s(61227),a=s(64960),c=e([t]);t=(c.then?(await c)():c)[0];let n=({application:e,onApply:o,hasApplied:s=!1})=>{let{isOpen:r,onOpen:c,onClose:n}=(0,t.useDisclosure)(),d=a[e.icon];return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(t.Box,{bg:e.gradient?`linear-gradient(135deg, ${e.gradient.from}, ${e.gradient.to})`:"gray.800",borderRadius:"xl",border:"1px solid",borderColor:e.isOpen?`${e.color}.400`:"gray.600",p:6,position:"relative",transition:"all 0.3s",opacity:e.isOpen?1:.7,_hover:{transform:e.isOpen?"translateY(-2px)":"none",boxShadow:e.isOpen?"0 4px 12px rgba(0,0,0,0.2)":"none"},children:(0,i.jsxs)(t.VStack,{align:"stretch",spacing:4,children:[(0,i.jsxs)(t.HStack,{justify:"space-between",children:[(0,i.jsxs)(t.HStack,{children:[(0,i.jsx)(t.Icon,{as:d,boxSize:6,color:`${e.color}.300`}),(0,i.jsx)(t.Text,{fontSize:"xl",fontWeight:"bold",color:"white",children:e.title})]}),(0,i.jsx)(t.Badge,{colorScheme:e.isOpen?"green":"gray",variant:"solid",px:3,py:1,borderRadius:"full",children:e.isOpen?"Open":"Closed"})]}),(0,i.jsx)(t.Text,{color:"gray.300",noOfLines:2,children:e.description}),(0,i.jsxs)(t.HStack,{spacing:4,color:"gray.400",fontSize:"sm",children:[(0,i.jsx)(t.Tooltip,{label:"Average response time",children:(0,i.jsxs)(t.HStack,{children:[(0,i.jsx)(t.Icon,{as:l.Ohp}),(0,i.jsx)(t.Text,{children:e.metadata.averageResponseTime})]})}),(0,i.jsx)(t.Tooltip,{label:"Total applications",children:(0,i.jsxs)(t.HStack,{children:[(0,i.jsx)(t.Icon,{as:l.cfS}),(0,i.jsx)(t.Text,{children:e.metadata.totalApplications})]})}),(0,i.jsx)(t.Tooltip,{label:"Acceptance rate",children:(0,i.jsxs)(t.HStack,{children:[(0,i.jsx)(t.Icon,{as:l.A3x}),(0,i.jsxs)(t.Text,{children:[e.metadata.acceptanceRate,"%"]})]})})]}),(0,i.jsxs)(t.HStack,{justify:"space-between",align:"center",children:[(0,i.jsx)(t.Button,{variant:"ghost",size:"sm",onClick:c,color:`${e.color}.300`,_hover:{bg:`${e.color}.900`,color:`${e.color}.200`},children:"View Details"}),(0,i.jsx)(t.Button,{colorScheme:e.color,isDisabled:!e.isOpen||s,onClick:()=>o(e),children:s?"Applied":"Apply Now"})]})]})}),(0,i.jsxs)(t.Modal,{isOpen:r,onClose:n,size:"lg",children:[(0,i.jsx)(t.ModalOverlay,{backdropFilter:"blur(10px)"}),(0,i.jsxs)(t.ModalContent,{bg:"gray.800",border:"1px",borderColor:"whiteAlpha.200",children:[(0,i.jsx)(t.ModalHeader,{color:"white",children:(0,i.jsxs)(t.HStack,{children:[(0,i.jsx)(t.Icon,{as:d,color:`${e.color}.300`}),(0,i.jsxs)(t.Text,{children:[e.title," Details"]})]})}),(0,i.jsx)(t.ModalCloseButton,{}),(0,i.jsx)(t.ModalBody,{children:(0,i.jsxs)(t.VStack,{align:"stretch",spacing:6,children:[(0,i.jsxs)(t.Box,{children:[(0,i.jsx)(t.Text,{fontWeight:"bold",color:"gray.300",mb:2,children:"Requirements"}),(0,i.jsxs)(t.List,{spacing:2,children:[e.requirements?.minAge&&(0,i.jsxs)(t.ListItem,{color:"gray.400",children:[(0,i.jsx)(t.ListIcon,{as:l.y3G,color:`${e.color}.300`}),"Must be ",e.requirements.minAge,"+ years old"]}),e.requirements?.minAccountAge&&(0,i.jsxs)(t.ListItem,{color:"gray.400",children:[(0,i.jsx)(t.ListIcon,{as:l.wIk,color:`${e.color}.300`}),"Account must be at least ",e.requirements.minAccountAge," days old"]}),e.requirements?.timezone&&(0,i.jsxs)(t.ListItem,{color:"gray.400",children:[(0,i.jsx)(t.ListIcon,{as:l.Ohp,color:`${e.color}.300`}),"Must provide timezone information"]}),e.requirements?.availability&&(0,i.jsxs)(t.ListItem,{color:"gray.400",children:[(0,i.jsx)(t.ListIcon,{as:l.cfS,color:`${e.color}.300`}),"Must specify availability hours"]})]})]}),(0,i.jsxs)(t.Box,{children:[(0,i.jsx)(t.Text,{fontWeight:"bold",color:"gray.300",mb:2,children:"Application Process"}),(0,i.jsxs)(t.List,{spacing:2,color:"gray.400",children:[(0,i.jsxs)(t.ListItem,{children:[(0,i.jsx)(t.ListIcon,{as:l.A3x,color:`${e.color}.300`}),"Fill out the application form"]}),e.requiresApproval&&(0,i.jsxs)(t.ListItem,{children:[(0,i.jsx)(t.ListIcon,{as:l.Ohp,color:`${e.color}.300`}),"Wait for review (",e.metadata.averageResponseTime,")"]})]})]}),!e.isOpen&&e.metadata.nextReviewDate&&(0,i.jsx)(t.Box,{bg:`${e.color}.900`,p:4,borderRadius:"md",children:(0,i.jsxs)(t.Text,{color:"white",children:["Next application review period starts: ",new Date(e.metadata.nextReviewDate).toLocaleDateString()]})})]})}),(0,i.jsxs)(t.ModalFooter,{children:[(0,i.jsx)(t.Button,{variant:"ghost",mr:3,onClick:n,children:"Close"}),(0,i.jsx)(t.Button,{colorScheme:e.color,isDisabled:!e.isOpen||s,onClick:()=>{n(),o(e)},children:s?"Already Applied":"Apply Now"})]})]})]})]})};r()}catch(e){r(e)}})}};