"use strict";(()=>{var e={};e.id=5268,e.ids=[5268],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},50333:(e,t,r)=>{r.r(t),r.d(t,{config:()=>c,default:()=>p,routeModule:()=>h});var o={};r.r(o),r.d(o,{default:()=>u});var s=r(93433),a=r(20264),i=r(20584),n=r(15806),d=r(94506);let l={name:"Easter Egg Hunter",color:"#FFD700",permissions:0n,hoist:!0,mentionable:!0};async function u(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let r=await (0,n.getServerSession)(e,t,d.authOptions);if(!r)return t.status(401).json({error:"Unauthorized"});let o=r.user.id;if(!o)return t.status(400).json({error:"User ID not found"});let s="1384248736980537417",a=await fetch(`https://discord.com/api/v10/guilds/${s}/roles`,{headers:{Authorization:"Bot MTM4NzUyODQ1NjEzNjU1NjYyNA.GTn4Xm.3EFu9GUY5CHUc85n8far5F2QAZX3m8KPHlZT90","Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch guild roles");let i=(await a.json()).find(e=>e.name===l.name);if(!i){let e=await fetch(`https://discord.com/api/v10/guilds/${s}/roles`,{method:"POST",headers:{Authorization:"Bot MTM4NzUyODQ1NjEzNjU1NjYyNA.GTn4Xm.3EFu9GUY5CHUc85n8far5F2QAZX3m8KPHlZT90","Content-Type":"application/json"},body:JSON.stringify({...l,color:parseInt(l.color.replace("#",""),16)})});if(!e.ok)throw Error("Failed to create easter egg role");i=await e.json()}if(!(await fetch(`https://discord.com/api/v10/guilds/${s}/members/${o}/roles/${i.id}`,{method:"PUT",headers:{Authorization:"Bot MTM4NzUyODQ1NjEzNjU1NjYyNA.GTn4Xm.3EFu9GUY5CHUc85n8far5F2QAZX3m8KPHlZT90"}})).ok)throw Error("Failed to add role to user");let u=process.env.DISCORD_SYSTEM_CHANNEL_ID;return u&&await fetch(`https://discord.com/api/v10/channels/${u}/messages`,{method:"POST",headers:{Authorization:"Bot MTM4NzUyODQ1NjEzNjU1NjYyNA.GTn4Xm.3EFu9GUY5CHUc85n8far5F2QAZX3m8KPHlZT90","Content-Type":"application/json"},body:JSON.stringify({embeds:[{title:"\uD83C\uDF89 Easter Egg Master!",description:`Congratulations <@${o}>! You've found all the dashboard easter eggs and earned the ${l.name} role!`,color:parseInt(l.color.replace("#",""),16),timestamp:new Date().toISOString()}]})}),t.status(200).json({success:!0})}catch(e){return t.status(500).json({error:e.message})}}let p=(0,i.M)(o,"default"),c=(0,i.M)(o,"config"),h=new s.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/discord/roles/easter-egg",pathname:"/api/discord/roles/easter-egg",bundlePath:"",filename:""},userland:o})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(50333));module.exports=o})();