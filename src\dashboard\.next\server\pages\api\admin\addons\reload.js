"use strict";(()=>{var e={};e.id=5838,e.ids=[5838],e.modules={15806:e=>{e.exports=require("next-auth/next")},19652:(e,r,t)=>{t.r(r),t.d(r,{config:()=>x,default:()=>f,routeModule:()=>g});var s={};t.r(s),t.d(s,{default:()=>m});var n=t(93433),i=t(20264),o=t(20584),a=t(15806),d=t(94506),u=t(29021),l=t.n(u),c=t(33873),p=t.n(c);async function m(e,r){if("POST"!==e.method)return r.status(405).json({error:"Method not allowed"});let t=await (0,a.getServerSession)(e,r,d.authOptions);if(!t)return r.status(401).json({error:"Unauthorized"});if(!t.user.isAdmin)return r.status(403).json({error:"Forbidden - Admin access required"});try{let e=function(){let e=["404-bot",".","..","../..","../../..","../../../.."].map(e=>p().resolve(process.cwd(),e)).find(e=>{try{return l().existsSync(p().join(e,"addon-reload.signal"))||l().existsSync(p().join(e,"config.yml"))}catch{return!1}});if(!e){let r=p().resolve(__dirname,"../../../../../../..");l().existsSync(p().join(r,"config.yml"))&&(e=r)}if(!e)throw Error("Project root not found");return e}(),s=p().join(e,"addon-reload.signal"),n={timestamp:new Date().toISOString(),requestedBy:t.user?.email||"dashboard"};return l().writeFileSync(s,JSON.stringify(n,null,2)),r.status(200).json({success:!0,message:"Addon reload signal sent",timestamp:n.timestamp,path:s})}catch(e){return r.status(500).json({error:"Internal server error",details:e.message,stack:void 0})}}let f=(0,o.M)(s,"default"),x=(0,o.M)(s,"config"),g=new n.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/admin/addons/reload",pathname:"/api/admin/addons/reload",bundlePath:"",filename:""},userland:s})},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(19652));module.exports=s})();