import fs from 'fs';
import path from 'path';
import YAML from 'yaml';

// Load config.yml
function loadConfig() {
  const attempts = [
    'config.yml',
    '../config.yml',
    '../../config.yml',
    '../../../config.yml',
  ].map(rel => path.resolve(process.cwd(), rel));
  
  const configPath = attempts.find(p => fs.existsSync(p));
  if (!configPath) throw new Error('config.yml not found');
  
  const config = YAML.parse(fs.readFileSync(configPath, 'utf8'));
  return {
    DISCORD_CLIENT_ID: config.bot.clientId,
    DISCORD_CLIENT_SECRET: config.bot.clientSecret,
    DISCORD_BOT_TOKEN: config.bot.token,
    DISCORD_GUILD_ID: config.bot.guildId,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL || process.env.DASHBOARD_URL || config.dashboard?.nextAuthUrl || config.dashboard?.url || 'http://localhost:3000',
  };
}

const env = loadConfig();

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable strict runtime checks
  reactStrictMode: true,

  // Optimizations for smaller build size
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Optimize bundle
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Reduce bundle size with aggressive splitting
    config.optimization.splitChunks = {
      chunks: 'all',
      minSize: 10000,
      maxSize: 200000,
      cacheGroups: {
        framework: {
          name: 'framework',
          test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
          priority: 40,
          chunks: 'all',
          enforce: true,
        },
        chakra: {
          name: 'chakra',
          test: /[\\/]node_modules[\\/]@chakra-ui[\\/]/,
          priority: 35,
          chunks: 'all',
          enforce: true,
        },
        icons: {
          name: 'icons',
          test: /[\\/]node_modules[\\/](react-icons|@chakra-ui\/icons)[\\/]/,
          priority: 30,
          chunks: 'all',
          enforce: true,
        },
        lib: {
          test: /[\\/]node_modules[\\/]/,
          name: 'lib',
          priority: 20,
          chunks: 'all',
          enforce: true,
        },
        commons: {
          name: 'commons',
          minChunks: 2,
          priority: 10,
          chunks: 'all',
        },
      },
    };

    // Tree shaking optimizations (only in production)
    if (!dev) {
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
    }

    // Minimize bundle size
    if (!dev) {
      config.resolve.alias = {
        ...config.resolve.alias,
        // Replace heavy libraries with lighter alternatives
        'lodash': 'lodash-es',
      };

      // Ignore large files that aren't needed in production
      config.module.rules.push({
        test: /\.(md|txt)$/,
        use: 'ignore-loader',
      });
    }

    // Add webpack plugins for optimization
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/locale$/,
        contextRegExp: /moment$/,
      })
    );
    
    return config;
  },

  // Experimental optimizations (removed problematic ones)
  experimental: {
    scrollRestoration: true,
    optimizePackageImports: [
      '@chakra-ui/react', 
      'react-icons',
      'framer-motion',
      'recharts',
      'react-chartjs-2'
    ],
    // Enable modern bundling
    esmExternals: true,
  },

  env,

  // Disable ESLint during production builds
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Skip type checking during production builds
  typescript: {
    ignoreBuildErrors: true,
  },

  // Compress output
  compress: true,

  // Optimize performance
  poweredByHeader: false,
  generateEtags: false,
};

export default nextConfig;
