{"c": ["pages/admin/guilds-h", "webpack", "pages/admin/guilds-_", "chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27"], "r": [], "m": ["(pages-dir-browser)/__barrel_optimize__?names=<PERSON>ge,Box,<PERSON>ton,Card,CardBody,CardHeader,Container,FormControl,FormLabel,HStack,Heading,Icon,Input,SimpleGrid,Skeleton,Spinner,Tab,TabList,TabPanel,TabPanels,Tabs,Text,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs", "(pages-dir-browser)/__barrel_optimize__?names=FiFolderPlus,FiHash,FiLock,FiMessageCircle,FiMessageSquare,FiPlus,FiRadio,FiSave,FiServer,FiSettings,FiTool,FiUsers,FiVolume2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs"]}