"use strict";(()=>{var t={};t.id=6236,t.ids=[6236],t.modules={12518:t=>{t.exports=require("mongodb")},15806:t=>{t.exports=require("next-auth/next")},20396:t=>{t.exports=require("next-auth/providers/discord")},29021:t=>{t.exports=require("fs")},33873:t=>{t.exports=require("path")},37479:(t,e,a)=>{a.r(e),a.d(e,{config:()=>v,default:()=>x,routeModule:()=>y});var r={};a.r(r),a.d(r,{default:()=>b});var i=a(93433),s=a(20264),n=a(20584),o=a(15806),d=a(94506),l=a(12518),c=a(98580);let{url:p,name:m}=c.dashboardConfig.database,g=null;async function u(){return g||(g=await l.MongoClient.connect(p)),g}async function b(t,e){let a,r;if("GET"!==t.method)return e.setHeader("Allow",["GET"]),e.status(405).json({error:"Method not allowed"});let i=await (0,o.getServerSession)(t,e,d.authOptions);if(!i)return e.status(401).json({error:"Unauthorized"});let{id:s}=t.query;if(!s||Array.isArray(s))return e.status(400).json({error:"Invalid ticket id"});let{token:n}=c.dashboardConfig.bot;try{a=await u()}catch{return e.status(500).json({error:"DB connect failed"})}let p=a.db(m).collection("tickets"),g=await p.findOne({_id:new l.ObjectId(s)});if(!g)return e.status(404).json({error:"Ticket not found"});let b=i.user.isAdmin,x=g.creatorId===i.user.id;if(!b&&!x)return e.status(403).json({error:"Forbidden"});if(g.transcriptHtml&&(g.transcriptHtml.includes("<style")||g.transcriptHtml.includes("<!DOCTYPE html>")))return e.setHeader("Content-Type","text/html"),e.status(200).send(g.transcriptHtml);let v=[];try{for(;;){let t=`https://discord.com/api/v10/channels/${g.channelId}/messages?limit=100${r?`&before=${r}`:""}`,e=await fetch(t,{headers:{Authorization:`Bot ${n}`}});if(!e.ok){if(404===e.status)throw Error("Channel not found - ticket channel may have been deleted");if(403===e.status)throw Error("Access denied - bot may not have permission to read channel history");throw Error(`Discord API error: ${e.status} ${e.statusText}`)}let a=await e.json();if(v=v.concat(a),a.length<100)break;r=a[a.length-1].id}}catch(t){return e.status(500).json({error:"Failed to fetch messages",details:t.message||"Unknown error occurred while fetching Discord messages",suggestion:"The ticket channel may have been deleted. Transcripts should be saved when tickets are closed."})}v.reverse();let y=[...new Set(v.map(t=>t.author.id))].map(t=>{let e=v.find(e=>e.author.id===t);return{id:t,username:e?.author.username||"Unknown User",avatar:e?.author.avatar?`https://cdn.discordapp.com/avatars/${t}/${e.author.avatar}.png?size=32`:`https://cdn.discordapp.com/embed/avatars/${parseInt(e.author.discriminator||"0")%5}.png`,role:t===g.creatorId?"Creator":"Staff"}}).map(t=>`
    <div class="participant">
      <img src="${t.avatar}" alt="${t.username}'s avatar"/>
      <div>
        <div class="participant-name">${t.username}</div>
        <div class="participant-role">${t.role}</div>
      </div>
    </div>
  `).join("");async function k(t){try{let e=await fetch(`https://discord.com/api/v10/users/${t}`,{headers:{Authorization:`Bot ${n}`}});if(!e.ok)return`<@${t}>`;let a=await e.json();return`@${a.username}`}catch{return`<@${t}>`}}async function w(t){if(!t.content_type?.startsWith("image/"))return`<a href="${t.url}" target="_blank">${t.filename||t.url}</a>`;if("image/gif"===t.content_type||t.size>1048576)return`<img class="attach" src="${t.url}" />`;try{let e=await (await fetch(t.url)).arrayBuffer(),a=Buffer.from(e).toString("base64");return`<img class="attach" src="data:${t.content_type};base64,${a}" />`}catch{return`<img class="attach" src="${t.url}" />`}}let $=v.map(async t=>{let e=new Date(t.timestamp).toLocaleString(),a=t.author.avatar?`https://cdn.discordapp.com/avatars/${t.author.id}/${t.author.avatar}.png?size=32`:`https://cdn.discordapp.com/embed/avatars/${parseInt(t.author.discriminator||"0")%5}.png`,r=(t.content||"").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\n/g,"<br/>"),i=r.match(/<@!?(\d+)>/g);if(i)for(let t of i){let e=t.match(/\d+/)?.[0];if(e){let a=await k(e);r=r.replace(t,a)}}if(t===v[0]&&t.embeds?.length>0){let e=t.embeds[0];r=`
<div class="ticket-embed">
  <div class="ticket-embed-header">
    <span class="ticket-embed-icon">📝</span>
    <div class="ticket-embed-title">${e.title||"Support Ticket"}</div>
  </div>
  ${e.fields?.map(t=>`
    <div class="ticket-embed-field">
      <div class="ticket-embed-label">${t.name}</div>
      <div class="ticket-embed-value">${t.value}</div>
    </div>
  `).join("")||""}
  ${e.description?`<div class="ticket-embed-description">${e.description}</div>`:""}
</div>`}r=await h(r,/<a?:([\w]+):(\d+)>/g,async(t,e,a)=>{let r=t.startsWith("<a:")?"gif":"png",i=`https://cdn.discordapp.com/emojis/${a}.${r}`;if("gif"===r)return`<img class="emoji" src="${i}" />`;let s=await f(i,`image/${r}`);return`<img class="emoji" src="${s}" />`}),r=await h(r,/(https?:\/\/tenor\.com\/view\/[^\s]+)/g,async t=>{let e=t.match(/-(\d+)(?:\?.*)?$/);if(!e)return"";let a=e[1];try{let t,e=await fetch(`https://g.tenor.com/v1/gifs?ids=${a}&key=LIVDSRZULELA`),r=await e.json();if(!r?.results?.length)throw 0;let i=r.results[0].media_formats||r.results[0].media?.[0];for(let e of["mediumgif","gif","tinygif","nanogif"])if(i?.[e]?.url){t=i[e].url;break}if(!t)throw 0;return t}catch{return""}}),r=await h(r,/(https?:\/\/\S+\.(?:png|jpe?g|gif|webp))/gi,async t=>t);let s="";return t.attachments?.length&&(s=(await Promise.all(t.attachments.map(w))).join("")),`<div class="msg">
      <img class="avatar" src="${a}"/>
      <div class="bubble">
        <div class="meta">
          <span class="name">${t.author.username}</span>
          <span class="time">${e}</span>
        </div>
        <div class="content">${r}${s}</div>
      </div>
    </div>`}),j=(await Promise.all($)).join("\n"),z=`<!DOCTYPE html>
<html lang="en"><head><meta charset="utf-8"/><title>Ticket ${s} Transcript</title>
<style>
/* Reset and base styles */
*{box-sizing:border-box;margin:0;padding:0}
:root {
  --bg-primary: #0f172a;
  --bg-secondary: rgba(255,255,255,0.08);
  --text-primary: #e2e8f0;
  --text-secondary: #94a3b8;
  --accent-blue: #3b82f6;
  --accent-purple: #a855f7;
  --accent-red: #ef4444;
}

/* Main layout */
body{
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
  display: grid;
  grid-template-columns: 280px 1fr;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(29,78,216,.15), transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(60,46,123,.15), transparent 50%);
}

/* Sidebar */
.sidebar {
  background: var(--bg-secondary);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255,255,255,0.1);
  padding: 24px;
  position: sticky;
  top: 0;
  height: 100vh;
  overflow-y: auto;
}

.sidebar-section {
  margin-bottom: 32px;
}

.sidebar-section h3 {
  color: var(--text-secondary);
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
}

.participant {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.participant img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.participant-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.participant-role {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Main content */
.main {
  padding: 40px;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

/* Header */
.header {
  margin-bottom: 32px;
  text-align: center;
}

h1 {
  font-size: 32px;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 16px;
}

/* Support details */
.support-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 32px;
  padding: 24px;
  background: var(--bg-secondary);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.1);
}

.detail-item {
  text-align: center;
  padding: 12px;
  background: rgba(255,255,255,0.02);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.detail-item:hover {
  background: rgba(255,255,255,0.04);
  transform: translateY(-2px);
}

.detail-label {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 6px;
  font-weight: 500;
}

.detail-value {
  font-size: 15px;
  color: var(--text-primary);
  font-weight: 600;
}

/* Messages */
.messages {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.1);
}

.msg {
  display: flex;
  gap: 16px;
  animation: fadeIn 0.3s ease;
  padding: 4px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.msg:hover {
  background: rgba(255,255,255,0.03);
}

.avatar {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.msg:hover .avatar {
  transform: scale(1.05);
}

.bubble {
  background: rgba(255,255,255,0.04);
  padding: 16px;
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.06);
  max-width: calc(100% - 58px);
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.msg:hover .bubble {
  border-color: rgba(255,255,255,0.1);
  background: rgba(255,255,255,0.05);
}

.meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.name {
  font-size: 15px;
  font-weight: 500;
  color: #60a5fa;
}

.time {
  font-size: 12px;
  color: var(--text-secondary);
}

.content {
  font-size: 15px;
  line-height: 1.5;
  color: var(--text-primary);
  word-break: break-word;
}

/* Attachments */
.attach {
  display: block;
  margin-top: 12px;
  max-width: 400px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  transition: transform 0.2s ease;
}

.attach:hover {
  transform: scale(1.02);
}

.emoji {
  width: 32px;
  height: 32px;
  vertical-align: middle;
  margin: 0 2px;
  transition: transform 0.15s ease;
  cursor: pointer;
}

.emoji:hover {
  transform: scale(1.2);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Category badge */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 99px;
  font-size: 13px;
  font-weight: 500;
  background: var(--badge-bg);
  color: white;
  margin-bottom: 24px;
}

/* Ticket embed */
.ticket-embed {
  background: rgba(255,255,255,0.03);
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 16px;
  padding: 24px;
  margin-top: 12px;
  transition: all 0.2s ease;
}

.ticket-embed:hover {
  background: rgba(255,255,255,0.04);
  border-color: rgba(255,255,255,0.15);
  transform: translateY(-2px);
}

.ticket-embed-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.ticket-embed-icon {
  font-size: 24px;
}

.ticket-embed-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--accent-blue);
  background: linear-gradient(135deg, #60a5fa 0%, #818cf8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.ticket-embed-field {
  margin-bottom: 12px;
  padding-left: 28px;
}

.ticket-embed-label {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 4px;
  font-weight: 500;
}

.ticket-embed-value {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.5;
}

.ticket-embed-description {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255,255,255,0.1);
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.5;
  padding-left: 28px;
}

/* Participants */
.participant {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.participant img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.participant-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.participant-role {
  font-size: 12px;
  color: var(--text-secondary);
}
</style></head><body>
  <div class="sidebar">
    <div class="sidebar-section">
      <h3>Participants</h3>
      ${y}
    </div>
    <div class="sidebar-section">
      <h3>Support Info</h3>
      <div class="detail-item">
        <div class="detail-label">Status</div>
        <div class="detail-value">Closed</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Category</div>
        <div class="detail-value">${g.category||"Support"}</div>
      </div>
    </div>
  </div>

  <div class="main">
    <div class="header">
      <h1>Ticket Transcript</h1>
      <span class="badge" style="--badge-bg:${{support:"#3b82f6","18plus":"#ef4444",other:"#a855f7"}[g.category||"other"]||"#6b7280"}">${g.category||"Support"}</span>
    </div>

    <div class="support-details">
      <div class="detail-item">
        <div class="detail-label">Ticket ID</div>
        <div class="detail-value">${s}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Created</div>
        <div class="detail-value">${new Date(g.createdAt).toLocaleString()}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Closed</div>
        <div class="detail-value">${g.closedAt?new Date(g.closedAt).toLocaleString():"N/A"}</div>
      </div>
    </div>

    <div class="messages">
      ${j}
    </div>
  </div>
</body></html>`;try{await p.updateOne({_id:new l.ObjectId(s)},{$set:{transcriptHtml:z}})}catch{}e.setHeader("Content-Type","text/html"),e.status(200).send(z)}function h(t,e,a){let r=[];return t.replace(e,(...t)=>{let e=a(...t);return r.push(e),t[0]}),Promise.all(r).then(a=>t.replace(e,()=>a.shift()))}async function f(t,e){try{let a=await fetch(t),r=await a.arrayBuffer();if(r.byteLength>1048576)return t;let i=Buffer.from(r).toString("base64");return`data:${e};base64,${i}`}catch{return t}}let x=(0,n.M)(r,"default"),v=(0,n.M)(r,"config"),y=new i.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/discord/tickets/[id]/transcript",pathname:"/api/discord/tickets/[id]/transcript",bundlePath:"",filename:""},userland:r})},65542:t=>{t.exports=require("next-auth")},72115:t=>{t.exports=require("yaml")},75600:t=>{t.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var e=require("../../../../../webpack-api-runtime.js");e.C(t);var a=t=>e(e.s=t),r=e.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>a(37479));module.exports=r})();