exports.id=873,exports.ids=[873],exports.modules={14963:function(t,e,r){var i;!function(n){"use strict";var s,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},h=!0,u="[DecimalError] ",f=u+"Invalid argument: ",c=u+"Exponent out of range: ",l=Math.floor,a=Math.pow,d=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,g=l(1286742750677284.5),p={};function w(t,e){var r,i,n,s,o,u,f,c,l=t.constructor,a=l.precision;if(!t.s||!e.s)return e.s||(e=new l(t)),h?q(e,a):e;if(f=t.d,c=e.d,o=t.e,n=e.e,f=f.slice(),s=o-n){for(s<0?(i=f,s=-s,u=c.length):(i=c,n=o,u=f.length),s>(u=(o=Math.ceil(a/7))>u?o+1:u+1)&&(s=u,i.length=1),i.reverse();s--;)i.push(0);i.reverse()}for((u=f.length)-(s=c.length)<0&&(s=u,i=c,c=f,f=i),r=0;s;)r=(f[--s]=f[s]+c[s]+r)/1e7|0,f[s]%=1e7;for(r&&(f.unshift(r),++n),u=f.length;0==f[--u];)f.pop();return e.d=f,e.e=n,h?q(e,a):e}function v(t,e,r){if(t!==~~t||t<e||t>r)throw Error(f+t)}function m(t){var e,r,i,n=t.length-1,s="",o=t[0];if(n>0){for(s+=o,e=1;e<n;e++)(r=7-(i=t[e]+"").length)&&(s+=b(r)),s+=i;(r=7-(i=(o=t[e])+"").length)&&(s+=b(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return s+o}p.absoluteValue=p.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},p.comparedTo=p.cmp=function(t){var e,r,i,n;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(i=this.d.length)<(n=t.d.length)?i:n;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return i===n?0:i>n^this.s<0?1:-1},p.decimalPlaces=p.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},p.dividedBy=p.div=function(t){return E(this,new this.constructor(t))},p.dividedToIntegerBy=p.idiv=function(t){var e=this.constructor;return q(E(this,new e(t),0,1),e.precision)},p.equals=p.eq=function(t){return!this.cmp(t)},p.exponent=function(){return x(this)},p.greaterThan=p.gt=function(t){return this.cmp(t)>0},p.greaterThanOrEqualTo=p.gte=function(t){return this.cmp(t)>=0},p.isInteger=p.isint=function(){return this.e>this.d.length-2},p.isNegative=p.isneg=function(){return this.s<0},p.isPositive=p.ispos=function(){return this.s>0},p.isZero=function(){return 0===this.s},p.lessThan=p.lt=function(t){return 0>this.cmp(t)},p.lessThanOrEqualTo=p.lte=function(t){return 1>this.cmp(t)},p.logarithm=p.log=function(t){var e,r=this.constructor,i=r.precision,n=i+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(s))throw Error(u+"NaN");if(this.s<1)throw Error(u+(this.s?"NaN":"-Infinity"));return this.eq(s)?new r(0):(h=!1,e=E(L(this,n),L(t,n),n),h=!0,q(e,i))},p.minus=p.sub=function(t){return t=new this.constructor(t),this.s==t.s?M(this,t):w(this,(t.s=-t.s,t))},p.modulo=p.mod=function(t){var e,r=this.constructor,i=r.precision;if(!(t=new r(t)).s)throw Error(u+"NaN");return this.s?(h=!1,e=E(this,t,0,1).times(t),h=!0,this.minus(e)):q(new r(this),i)},p.naturalExponential=p.exp=function(){return N(this)},p.naturalLogarithm=p.ln=function(){return L(this)},p.negated=p.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},p.plus=p.add=function(t){return t=new this.constructor(t),this.s==t.s?w(this,t):M(this,(t.s=-t.s,t))},p.precision=p.sd=function(t){var e,r,i;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(f+t);if(e=x(this)+1,r=7*(i=this.d.length-1)+1,i=this.d[i]){for(;i%10==0;i/=10)r--;for(i=this.d[0];i>=10;i/=10)r++}return t&&e>r?e:r},p.squareRoot=p.sqrt=function(){var t,e,r,i,n,s,o,f=this.constructor;if(this.s<1){if(!this.s)return new f(0);throw Error(u+"NaN")}for(t=x(this),h=!1,0==(n=Math.sqrt(+this))||n==1/0?(((e=m(this.d)).length+t)%2==0&&(e+="0"),n=Math.sqrt(e),t=l((t+1)/2)-(t<0||t%2),i=new f(e=n==1/0?"5e"+t:(e=n.toExponential()).slice(0,e.indexOf("e")+1)+t)):i=new f(n.toString()),n=o=(r=f.precision)+3;;)if(i=(s=i).plus(E(this,s,o+2)).times(.5),m(s.d).slice(0,o)===(e=m(i.d)).slice(0,o)){if(e=e.slice(o-3,o+1),n==o&&"4999"==e){if(q(s,r+1,0),s.times(s).eq(this)){i=s;break}}else if("9999"!=e)break;o+=4}return h=!0,q(i,r)},p.times=p.mul=function(t){var e,r,i,n,s,o,u,f,c,l=this.constructor,a=this.d,d=(t=new l(t)).d;if(!this.s||!t.s)return new l(0);for(t.s*=this.s,r=this.e+t.e,(f=a.length)<(c=d.length)&&(s=a,a=d,d=s,o=f,f=c,c=o),s=[],i=o=f+c;i--;)s.push(0);for(i=c;--i>=0;){for(e=0,n=f+i;n>i;)u=s[n]+d[i]*a[n-i-1]+e,s[n--]=u%1e7|0,e=u/1e7|0;s[n]=(s[n]+e)%1e7|0}for(;!s[--o];)s.pop();return e?++r:s.shift(),t.d=s,t.e=r,h?q(t,l.precision):t},p.toDecimalPlaces=p.todp=function(t,e){var r=this,i=r.constructor;return(r=new i(r),void 0===t)?r:(v(t,0,1e9),void 0===e?e=i.rounding:v(e,0,8),q(r,t+x(r)+1,e))},p.toExponential=function(t,e){var r,i=this,n=i.constructor;return void 0===t?r=_(i,!0):(v(t,0,1e9),void 0===e?e=n.rounding:v(e,0,8),r=_(i=q(new n(i),t+1,e),!0,t+1)),r},p.toFixed=function(t,e){var r,i,n=this.constructor;return void 0===t?_(this):(v(t,0,1e9),void 0===e?e=n.rounding:v(e,0,8),r=_((i=q(new n(this),t+x(this)+1,e)).abs(),!1,t+x(i)+1),this.isneg()&&!this.isZero()?"-"+r:r)},p.toInteger=p.toint=function(){var t=this.constructor;return q(new t(this),x(this)+1,t.rounding)},p.toNumber=function(){return+this},p.toPower=p.pow=function(t){var e,r,i,n,o,f,c=this,a=c.constructor,d=+(t=new a(t));if(!t.s)return new a(s);if(!(c=new a(c)).s){if(t.s<1)throw Error(u+"Infinity");return c}if(c.eq(s))return c;if(i=a.precision,t.eq(s))return q(c,i);if(f=(e=t.e)>=(r=t.d.length-1),o=c.s,f){if((r=d<0?-d:d)<=0x1fffffffffffff){for(n=new a(s),e=Math.ceil(i/7+4),h=!1;r%2&&y((n=n.times(c)).d,e),0!==(r=l(r/2));)y((c=c.times(c)).d,e);return h=!0,t.s<0?new a(s).div(n):q(n,i)}}else if(o<0)throw Error(u+"NaN");return o=o<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,h=!1,n=t.times(L(c,i+12)),h=!0,(n=N(n)).s=o,n},p.toPrecision=function(t,e){var r,i,n=this,s=n.constructor;return void 0===t?(r=x(n),i=_(n,r<=s.toExpNeg||r>=s.toExpPos)):(v(t,1,1e9),void 0===e?e=s.rounding:v(e,0,8),r=x(n=q(new s(n),t,e)),i=_(n,t<=r||r<=s.toExpNeg,t)),i},p.toSignificantDigits=p.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(v(t,1,1e9),void 0===e?e=r.rounding:v(e,0,8)),q(new r(this),t,e)},p.toString=p.valueOf=p.val=p.toJSON=function(){var t=x(this),e=this.constructor;return _(this,t<=e.toExpNeg||t>=e.toExpPos)};var E=function(){function t(t,e){var r,i=0,n=t.length;for(t=t.slice();n--;)r=t[n]*e+i,t[n]=r%1e7|0,i=r/1e7|0;return i&&t.unshift(i),t}function e(t,e,r,i){var n,s;if(r!=i)s=r>i?1:-1;else for(n=s=0;n<r;n++)if(t[n]!=e[n]){s=t[n]>e[n]?1:-1;break}return s}function r(t,e,r){for(var i=0;r--;)t[r]-=i,i=+(t[r]<e[r]),t[r]=1e7*i+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(i,n,s,o){var h,f,c,l,a,d,g,p,w,v,m,E,N,O,b,L,D,M,_=i.constructor,y=i.s==n.s?1:-1,A=i.d,P=n.d;if(!i.s)return new _(i);if(!n.s)throw Error(u+"Division by zero");for(c=0,f=i.e-n.e,D=P.length,b=A.length,p=(g=new _(y)).d=[];P[c]==(A[c]||0);)++c;if(P[c]>(A[c]||0)&&--f,(E=null==s?s=_.precision:o?s+(x(i)-x(n))+1:s)<0)return new _(0);if(E=E/7+2|0,c=0,1==D)for(l=0,P=P[0],E++;(c<b||l)&&E--;c++)N=1e7*l+(A[c]||0),p[c]=N/P|0,l=N%P|0;else{for((l=1e7/(P[0]+1)|0)>1&&(P=t(P,l),A=t(A,l),D=P.length,b=A.length),O=D,v=(w=A.slice(0,D)).length;v<D;)w[v++]=0;(M=P.slice()).unshift(0),L=P[0],P[1]>=1e7/2&&++L;do l=0,(h=e(P,w,D,v))<0?(m=w[0],D!=v&&(m=1e7*m+(w[1]||0)),(l=m/L|0)>1?(l>=1e7&&(l=1e7-1),d=(a=t(P,l)).length,v=w.length,1==(h=e(a,w,d,v))&&(l--,r(a,D<d?M:P,d))):(0==l&&(h=l=1),a=P.slice()),(d=a.length)<v&&a.unshift(0),r(w,a,v),-1==h&&(v=w.length,(h=e(P,w,D,v))<1&&(l++,r(w,D<v?M:P,v))),v=w.length):0===h&&(l++,w=[0]),p[c++]=l,h&&w[0]?w[v++]=A[O]||0:(w=[A[O]],v=1);while((O++<b||void 0!==w[0])&&E--)}return p[0]||p.shift(),g.e=f,q(g,o?s+x(g)+1:s)}}();function N(t,e){var r,i,n,o,u,f=0,l=0,d=t.constructor,g=d.precision;if(x(t)>16)throw Error(c+x(t));if(!t.s)return new d(s);for(null==e?(h=!1,u=g):u=e,o=new d(.03125);t.abs().gte(.1);)t=t.times(o),l+=5;for(u+=Math.log(a(2,l))/Math.LN10*2+5|0,r=i=n=new d(s),d.precision=u;;){if(i=q(i.times(t),u),r=r.times(++f),m((o=n.plus(E(i,r,u))).d).slice(0,u)===m(n.d).slice(0,u)){for(;l--;)n=q(n.times(n),u);return d.precision=g,null==e?(h=!0,q(n,g)):n}n=o}}function x(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function O(t,e,r){if(e>t.LN10.sd())throw h=!0,r&&(t.precision=r),Error(u+"LN10 precision limit exceeded");return q(new t(t.LN10),e)}function b(t){for(var e="";t--;)e+="0";return e}function L(t,e){var r,i,n,o,f,c,l,a,d,g=1,p=t,w=p.d,v=p.constructor,N=v.precision;if(p.s<1)throw Error(u+(p.s?"NaN":"-Infinity"));if(p.eq(s))return new v(0);if(null==e?(h=!1,a=N):a=e,p.eq(10))return null==e&&(h=!0),O(v,a);if(v.precision=a+=10,i=(r=m(w)).charAt(0),!(15e14>Math.abs(o=x(p))))return l=O(v,a+2,N).times(o+""),p=L(new v(i+"."+r.slice(1)),a-10).plus(l),v.precision=N,null==e?(h=!0,q(p,N)):p;for(;i<7&&1!=i||1==i&&r.charAt(1)>3;)i=(r=m((p=p.times(t)).d)).charAt(0),g++;for(o=x(p),i>1?(p=new v("0."+r),o++):p=new v(i+"."+r.slice(1)),c=f=p=E(p.minus(s),p.plus(s),a),d=q(p.times(p),a),n=3;;){if(f=q(f.times(d),a),m((l=c.plus(E(f,new v(n),a))).d).slice(0,a)===m(c.d).slice(0,a))return c=c.times(2),0!==o&&(c=c.plus(O(v,a+2,N).times(o+""))),c=E(c,new v(g),a),v.precision=N,null==e?(h=!0,q(c,N)):c;c=l,n+=2}}function D(t,e){var r,i,n;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(i=e.search(/e/i))>0?(r<0&&(r=i),r+=+e.slice(i+1),e=e.substring(0,i)):r<0&&(r=e.length),i=0;48===e.charCodeAt(i);)++i;for(n=e.length;48===e.charCodeAt(n-1);)--n;if(e=e.slice(i,n)){if(n-=i,t.e=l((r=r-i-1)/7),t.d=[],i=(r+1)%7,r<0&&(i+=7),i<n){for(i&&t.d.push(+e.slice(0,i)),n-=7;i<n;)t.d.push(+e.slice(i,i+=7));i=7-(e=e.slice(i)).length}else i-=n;for(;i--;)e+="0";if(t.d.push(+e),h&&(t.e>g||t.e<-g))throw Error(c+r)}else t.s=0,t.e=0,t.d=[0];return t}function q(t,e,r){var i,n,s,o,u,f,d,p,w=t.d;for(o=1,s=w[0];s>=10;s/=10)o++;if((i=e-o)<0)i+=7,n=e,d=w[p=0];else{if((p=Math.ceil((i+1)/7))>=(s=w.length))return t;for(o=1,d=s=w[p];s>=10;s/=10)o++;i%=7,n=i-7+o}if(void 0!==r&&(u=d/(s=a(10,o-n-1))%10|0,f=e<0||void 0!==w[p+1]||d%s,f=r<4?(u||f)&&(0==r||r==(t.s<0?3:2)):u>5||5==u&&(4==r||f||6==r&&(i>0?n>0?d/a(10,o-n):0:w[p-1])%10&1||r==(t.s<0?8:7))),e<1||!w[0])return f?(s=x(t),w.length=1,e=e-s-1,w[0]=a(10,(7-e%7)%7),t.e=l(-e/7)||0):(w.length=1,w[0]=t.e=t.s=0),t;if(0==i?(w.length=p,s=1,p--):(w.length=p+1,s=a(10,7-i),w[p]=n>0?(d/a(10,o-n)%a(10,n)|0)*s:0),f)for(;;)if(0==p){1e7==(w[0]+=s)&&(w[0]=1,++t.e);break}else{if(w[p]+=s,1e7!=w[p])break;w[p--]=0,s=1}for(i=w.length;0===w[--i];)w.pop();if(h&&(t.e>g||t.e<-g))throw Error(c+x(t));return t}function M(t,e){var r,i,n,s,o,u,f,c,l,a,d=t.constructor,g=d.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new d(t),h?q(e,g):e;if(f=t.d,a=e.d,i=e.e,c=t.e,f=f.slice(),o=c-i){for((l=o<0)?(r=f,o=-o,u=a.length):(r=a,i=c,u=f.length),o>(n=Math.max(Math.ceil(g/7),u)+2)&&(o=n,r.length=1),r.reverse(),n=o;n--;)r.push(0);r.reverse()}else{for((l=(n=f.length)<(u=a.length))&&(u=n),n=0;n<u;n++)if(f[n]!=a[n]){l=f[n]<a[n];break}o=0}for(l&&(r=f,f=a,a=r,e.s=-e.s),u=f.length,n=a.length-u;n>0;--n)f[u++]=0;for(n=a.length;n>o;){if(f[--n]<a[n]){for(s=n;s&&0===f[--s];)f[s]=1e7-1;--f[s],f[n]+=1e7}f[n]-=a[n]}for(;0===f[--u];)f.pop();for(;0===f[0];f.shift())--i;return f[0]?(e.d=f,e.e=i,h?q(e,g):e):new d(0)}function _(t,e,r){var i,n=x(t),s=m(t.d),o=s.length;return e?(r&&(i=r-o)>0?s=s.charAt(0)+"."+s.slice(1)+b(i):o>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(n<0?"e":"e+")+n):n<0?(s="0."+b(-n-1)+s,r&&(i=r-o)>0&&(s+=b(i))):n>=o?(s+=b(n+1-o),r&&(i=r-n-1)>0&&(s=s+"."+b(i))):((i=n+1)<o&&(s=s.slice(0,i)+"."+s.slice(i)),r&&(i=r-o)>0&&(n+1===o&&(s+="."),s+=b(i))),t.s<0?"-"+s:s}function y(t,e){if(t.length>e)return t.length=e,!0}function A(t){if(!t||"object"!=typeof t)throw Error(u+"Object expected");var e,r,i,n=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<n.length;e+=3)if(void 0!==(i=t[r=n[e]]))if(l(i)===i&&i>=n[e+1]&&i<=n[e+2])this[r]=i;else throw Error(f+r+": "+i);if(void 0!==(i=t[r="LN10"]))if(i==Math.LN10)this[r]=new this(i);else throw Error(f+r+": "+i);return this}(o=function t(e){var r,i,n;function s(t){if(!(this instanceof s))return new s(t);if(this.constructor=s,t instanceof s){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(f+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return D(this,t.toString())}if("string"!=typeof t)throw Error(f+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,d.test(t))D(this,t);else throw Error(f+t)}if(s.prototype=p,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.clone=t,s.config=s.set=A,void 0===e&&(e={}),e)for(r=0,n=["precision","rounding","toExpNeg","toExpPos","LN10"];r<n.length;)e.hasOwnProperty(i=n[r++])||(e[i]=this[i]);return s.config(e),s}(o)).default=o.Decimal=o,s=new o(1),void 0===(i=(function(){return o}).call(e,r,e,t))||(t.exports=i)}(0)},41108:t=>{t.exports.isNode="[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0)}};