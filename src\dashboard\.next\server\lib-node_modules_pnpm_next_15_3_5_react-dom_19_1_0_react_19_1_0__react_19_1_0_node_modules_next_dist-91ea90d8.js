"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8";
exports.ids = ["lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_document.js":
/*!**********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_document.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/// <reference types=\"webpack/module.d.ts\" />\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Head: function() {\n        return Head;\n    },\n    Html: function() {\n        return Html;\n    },\n    Main: function() {\n        return Main;\n    },\n    NextScript: function() {\n        return NextScript;\n    },\n    /**\n * `Document` component handles the initial `document` markup and renders only on the server side.\n * Commonly used for implementing server side rendering for `css-in-js` libraries.\n */ default: function() {\n        return Document;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard(__webpack_require__(/*! react */ \"react\"));\nconst _constants = __webpack_require__(/*! ../shared/lib/constants */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/constants.js\");\nconst _getpagefiles = __webpack_require__(/*! ../server/get-page-files */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/get-page-files.js\");\nconst _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/htmlescape.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! ../lib/is-error */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/is-error.js\"));\nconst _htmlcontextsharedruntime = __webpack_require__(/*! ../shared/lib/html-context.shared-runtime */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages/vendored/contexts/html-context.js\");\nconst _encodeuripath = __webpack_require__(/*! ../shared/lib/encode-uri-path */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/encode-uri-path.js\");\nconst _tracer = __webpack_require__(/*! ../server/lib/trace/tracer */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/trace/tracer.js\");\nconst _utils = __webpack_require__(/*! ../server/lib/trace/utils */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/trace/utils.js\");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {\n        __proto__: null\n    };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\n/** Set of pages that have triggered a large data warning on production mode. */ const largePageDataWarnings = new Set();\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getpagefiles.getPageFiles)(buildManifest, '/_app');\n    const pageFiles =  true && inAmpMode ? [] : (0, _getpagefiles.getPageFiles)(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix, buildManifest, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')).map((polyfill)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(polyfill)}${assetQueryString}`\n        }, polyfill));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var _el_props_dangerouslySetInnerHTML, _el_props;\n            return el == null ? void 0 : (_el_props = el.props) == null ? void 0 : (_el_props_dangerouslySetInnerHTML = _el_props.dangerouslySetInnerHTML) == null ? void 0 : _el_props_dangerouslySetInnerHTML.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join('').replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, '').replace(/\\/\\*@ sourceURL=.*?\\*\\//g, '')\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports, assetPrefix, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith('.js') || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        }, file);\n    });\n}\nfunction getScripts(context, props, files) {\n    var _buildManifest_lowPriorityFiles;\n    const { assetPrefix, buildManifest, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith('.js'));\n    const lowPriorityScripts = (_buildManifest_lowPriorityFiles = buildManifest.lowPriorityFiles) == null ? void 0 : _buildManifest_lowPriorityFiles.filter((file)=>file.endsWith('.js'));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        }, file);\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix, scriptLoader, crossOrigin, nextScriptWorkers } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === 'edge') return null;\n    try {\n        // @ts-expect-error: Prevent webpack from processing this require\n        let { partytownSnippet } = require('@builder.io/partytown/integration');\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var _child_props_dangerouslySetInnerHTML, _child_props;\n            return hasComponentProps(child) && (child == null ? void 0 : (_child_props = child.props) == null ? void 0 : (_child_props_dangerouslySetInnerHTML = _child_props.dangerouslySetInnerHTML) == null ? void 0 : _child_props_dangerouslySetInnerHTML.__html.length) && 'data-partytown-config' in child.props;\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                !userDefinedConfig && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    \"data-partytown-config\": \"\",\n                    dangerouslySetInnerHTML: {\n                        __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n                    }\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    \"data-partytown\": \"\",\n                    dangerouslySetInnerHTML: {\n                        __html: partytownSnippet()\n                    }\n                }),\n                (scriptLoader.worker || []).map((file, index)=>{\n                    const { strategy, src, children: scriptChildren, dangerouslySetInnerHTML, ...scriptProps } = file;\n                    let srcProps = {};\n                    if (src) {\n                        // Use external src if provided\n                        srcProps.src = src;\n                    } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                        // Embed inline script if provided with dangerouslySetInnerHTML\n                        srcProps.dangerouslySetInnerHTML = {\n                            __html: dangerouslySetInnerHTML.__html\n                        };\n                    } else if (scriptChildren) {\n                        // Embed inline script if provided with children\n                        srcProps.dangerouslySetInnerHTML = {\n                            __html: typeof scriptChildren === 'string' ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join('') : ''\n                        };\n                    } else {\n                        throw Object.defineProperty(new Error('Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E82\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return /*#__PURE__*/ (0, _react.createElement)(\"script\", {\n                        ...srcProps,\n                        ...scriptProps,\n                        type: \"text/partytown\",\n                        key: src || index,\n                        nonce: props.nonce,\n                        \"data-nscript\": \"worker\",\n                        crossOrigin: props.crossOrigin || crossOrigin\n                    });\n                })\n            ]\n        });\n    } catch (err) {\n        if ((0, _iserror.default)(err) && err.code !== 'MODULE_NOT_FOUND') {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader, disableOptimizedLoading, crossOrigin } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy, ...scriptProps } = file;\n        return /*#__PURE__*/ (0, _react.createElement)(\"script\", {\n            ...scriptProps,\n            key: scriptProps.src || index,\n            defer: scriptProps.defer ?? !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            webWorkerScripts,\n            beforeInteractiveScripts\n        ]\n    });\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin, nonce, ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes('?') ? '&' : '?'}amp=1`;\n}\nfunction getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix = '') {\n    if (!nextFontManifest) {\n        return {\n            preconnect: null,\n            preload: null\n        };\n    }\n    const appFontsEntry = nextFontManifest.pages['/_app'];\n    const pageFontsEntry = nextFontManifest.pages[dangerousAsPath];\n    const preloadedFontFiles = Array.from(new Set([\n        ...appFontsEntry ?? [],\n        ...pageFontsEntry ?? []\n    ]));\n    // If no font files should preload but there's an entry for the path, add a preconnect tag.\n    const preconnectToSelf = !!(preloadedFontFiles.length === 0 && (appFontsEntry || pageFontsEntry));\n    return {\n        preconnect: preconnectToSelf ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            \"data-next-font\": nextFontManifest.pagesUsingSizeAdjust ? 'size-adjust' : '',\n            rel: \"preconnect\",\n            href: \"/\",\n            crossOrigin: \"anonymous\"\n        }) : null,\n        preload: preloadedFontFiles ? preloadedFontFiles.map((fontFile)=>{\n            const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)[1];\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                rel: \"preload\",\n                href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(fontFile)}`,\n                as: \"font\",\n                type: `font/${ext}`,\n                crossOrigin: \"anonymous\",\n                \"data-next-font\": fontFile.includes('-s') ? 'size-adjust' : ''\n            }, fontFile);\n        }) : null\n    };\n}\nclass Head extends _react.default.Component {\n    static #_ = this.contextType = _htmlcontextsharedruntime.HtmlContext;\n    getCssLinks(files) {\n        const { assetPrefix, assetQueryString, dynamicImports, dynamicCssManifest, crossOrigin, optimizeCss } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith('.css'));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmanagedFiles = new Set([]);\n        let localDynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith('.css'))));\n        if (localDynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            localDynamicCssFiles = localDynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmanagedFiles = new Set(localDynamicCssFiles);\n            cssFiles.push(...localDynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            const isUnmanagedFile = unmanagedFiles.has(file);\n            const isFileInDynamicCssManifest = dynamicCssManifest.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }, `${file}-preload`));\n            }\n            cssLinkElements.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? '' : undefined,\n                \"data-n-p\": isSharedFile || isUnmanagedFile || isFileInDynamicCssManifest ? undefined : ''\n            }, file));\n        });\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports, assetPrefix, assetQueryString, crossOrigin } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith('.js')) {\n                return null;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                rel: \"preload\",\n                href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            }, file);\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix, assetQueryString, scriptLoader, crossOrigin } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith('.js');\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }, file.src)),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }, file))\n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader } = this.context;\n        const { nonce, crossOrigin } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy, children, dangerouslySetInnerHTML, src, ...scriptProps } = file;\n            let html = '';\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n            }\n            return /*#__PURE__*/ (0, _react.createElement)(\"script\", {\n                ...scriptProps,\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            });\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    render() {\n        const { styles, ampPath, inAmpMode, hybridAmp, canonicalBase, __NEXT_DATA__, dangerousAsPath, headTags, unstable_runtimeJS, unstable_JsPreload, disableOptimizedLoading, optimizeCss, assetPrefix, nextFontManifest } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((child)=>{\n                if (child && child.type === 'link' && child.props['rel'] === 'preload' && child.props['as'] === 'style') {\n                    if (this.context.strictNextHead) {\n                        cssPreloads.push(/*#__PURE__*/ _react.default.cloneElement(child, {\n                            'data-next-head': ''\n                        }));\n                    } else {\n                        cssPreloads.push(child);\n                    }\n                } else {\n                    if (child) {\n                        if (this.context.strictNextHead) {\n                            otherHeadElements.push(/*#__PURE__*/ _react.default.cloneElement(child, {\n                                'data-next-head': ''\n                            }));\n                        } else {\n                            otherHeadElements.push(child);\n                        }\n                    }\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var _child_props;\n                const isReactHelmet = child == null ? void 0 : (_child_props = child.props) == null ? void 0 : _child_props['data-react-helmet'];\n                if (!isReactHelmet) {\n                    var _child_props1;\n                    if ((child == null ? void 0 : child.type) === 'title') {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === 'meta' && (child == null ? void 0 : (_child_props1 = child.props) == null ? void 0 : _child_props1.name) === 'viewport') {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n            });\n            if (this.props.crossOrigin) console.warn('Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated');\n        }\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type, props } = child;\n            if ( true && inAmpMode) {\n                let badProp = '';\n                if (type === 'meta' && props.name === 'viewport') {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === 'link' && props.rel === 'canonical') {\n                    hasCanonicalRel = true;\n                } else if (type === 'script') {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf('ampproject') < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === 'text/javascript')) {\n                        badProp = '<script';\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += '/>';\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === 'link' && props.rel === 'amphtml') {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        const nextFontLinkTags = getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix);\n        const tracingMetadata = (0, _utils.getTracedMetadata)((0, _tracer.getTracer)().getTracePropagationData(), this.context.experimentalClientTraceMetadata);\n        const traceMetaTags = (tracingMetadata || []).map(({ key, value }, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                name: key,\n                content: value\n            }, `next-trace-data-${index}`));\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"head\", {\n            ...getHeadHTMLProps(this.props),\n            children: [\n                this.context.isDevelopment && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            \"data-next-hide-fouc\": true,\n                            \"data-ampdevmode\":  true && inAmpMode ? 'true' : undefined,\n                            dangerouslySetInnerHTML: {\n                                __html: `body{display:none}`\n                            }\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            \"data-next-hide-fouc\": true,\n                            \"data-ampdevmode\":  true && inAmpMode ? 'true' : undefined,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                                dangerouslySetInnerHTML: {\n                                    __html: `body{display:block}`\n                                }\n                            })\n                        })\n                    ]\n                }),\n                head,\n                this.context.strictNextHead ? null : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                    name: \"next-head-count\",\n                    content: _react.default.Children.count(head || []).toString()\n                }),\n                children,\n                nextFontLinkTags.preconnect,\n                nextFontLinkTags.preload,\n                 true && inAmpMode && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n                        }),\n                        !hasCanonicalRel && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                            rel: \"canonical\",\n                            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/utils.js\").cleanAmpPath)(dangerousAsPath)\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                            rel: \"preload\",\n                            as: \"script\",\n                            href: \"https://cdn.ampproject.org/v0.js\"\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AmpStyles, {\n                            styles: styles\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            \"amp-boilerplate\": \"\",\n                            dangerouslySetInnerHTML: {\n                                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n                            }\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                                \"amp-boilerplate\": \"\",\n                                dangerouslySetInnerHTML: {\n                                    __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n                                }\n                            })\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                            async: true,\n                            src: \"https://cdn.ampproject.org/v0.js\"\n                        })\n                    ]\n                }),\n                !( true && inAmpMode) && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                    children: [\n                        !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                            rel: \"amphtml\",\n                            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n                        }),\n                        this.getBeforeInteractiveInlineScripts(),\n                        !optimizeCss && this.getCssLinks(files),\n                        !optimizeCss && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            \"data-n-css\": this.props.nonce ?? ''\n                        }),\n                        !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(),\n                        !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files),\n                        optimizeCss && this.getCssLinks(files),\n                        optimizeCss && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            \"data-n-css\": this.props.nonce ?? ''\n                        }),\n                        this.context.isDevelopment && // this element is used to mount development styles so the\n                        // ordering matches production\n                        // (by default, style-loader injects at the bottom of <head />)\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            id: \"__next_css__DO_NOT_USE__\"\n                        }),\n                        traceMetaTags,\n                        styles || null\n                    ]\n                }),\n                /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || [])\n            ]\n        });\n    }\n}\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var _children_find_props, _children_find, _children_find_props1, _children_find1;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (_children_find = children.find((child)=>child.type === Head)) == null ? void 0 : (_children_find_props = _children_find.props) == null ? void 0 : _children_find_props.children;\n    const bodyChildren = (_children_find1 = children.find((child)=>child.type === 'body')) == null ? void 0 : (_children_find_props1 = _children_find1.props) == null ? void 0 : _children_find_props1.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ]\n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var _child_type;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((_child_type = child.type) == null ? void 0 : _child_type.__nextScript) {\n            if (child.props.strategy === 'beforeInteractive') {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }\n                ]);\n                return;\n            } else if ([\n                'lazyOnload',\n                'afterInteractive',\n                'worker'\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            } else if (typeof child.props.strategy === 'undefined') {\n                scriptLoaderItems.push({\n                    ...child.props,\n                    strategy: 'afterInteractive'\n                });\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static #_ = this.contextType = _htmlcontextsharedruntime.HtmlContext;\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__, largePageDataBytes } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n                return (0, _htmlescape.htmlEscapeJsonString)(data);\n            }\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                if (false) {}\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? '' : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape.htmlEscapeJsonString)(data);\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.message.indexOf('circular structure') !== -1) {\n                throw Object.defineProperty(new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E490\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix, inAmpMode, buildManifest, unstable_runtimeJS, docComponentsRendered, assetQueryString, disableOptimizedLoading, crossOrigin } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles\n            ];\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    disableRuntimeJS ? null : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                        id: \"__NEXT_DATA__\",\n                        type: \"application/json\",\n                        nonce: this.props.nonce,\n                        crossOrigin: this.props.crossOrigin || crossOrigin,\n                        dangerouslySetInnerHTML: {\n                            __html: NextScript.getInlineScriptSource(this.context)\n                        },\n                        \"data-ampdevmode\": true\n                    }),\n                    ampDevFiles.map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                            nonce: this.props.nonce,\n                            crossOrigin: this.props.crossOrigin || crossOrigin,\n                            \"data-ampdevmode\": true\n                        }, file))\n                ]\n            });\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn('Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated');\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                        src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                        nonce: this.props.nonce,\n                        crossOrigin: this.props.crossOrigin || crossOrigin\n                    }, file)) : null,\n                disableRuntimeJS ? null : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    id: \"__NEXT_DATA__\",\n                    type: \"application/json\",\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    dangerouslySetInnerHTML: {\n                        __html: NextScript.getInlineScriptSource(this.context)\n                    }\n                }),\n                disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(),\n                disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(),\n                disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files),\n                disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files)\n            ]\n        });\n    }\n}\nfunction Html(props) {\n    const { inAmpMode, docComponentsRendered, locale, scriptLoader, __NEXT_DATA__ } = (0, _htmlcontextsharedruntime.useHtmlContext)();\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"html\", {\n        ...props,\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? '' : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== 'production' ? '' : undefined\n    });\n}\nfunction Main() {\n    const { docComponentsRendered } = (0, _htmlcontextsharedruntime.useHtmlContext)();\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"next-js-internal-body-render-target\", {});\n}\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(Html, {\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {}),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(Main, {}),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(NextScript, {})\n                    ]\n                })\n            ]\n        });\n    }\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(Html, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(Main, {}),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(NextScript, {})\n                ]\n            })\n        ]\n    });\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_error.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_error.js ***!
  \*******************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return Error;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-node)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/cjs/_interop_require_default.cjs\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"react\"));\nconst _head = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/head */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/head.js\"));\nconst statusCodes = {\n    400: 'Bad Request',\n    404: 'This page could not be found',\n    405: 'Method Not Allowed',\n    500: 'Internal Server Error'\n};\nfunction _getInitialProps(param) {\n    let { req, res, err } = param;\n    const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n    let hostname;\n    if (false) {} else if (req) {\n        const { getRequestMeta } = __webpack_require__(/*! ../server/request-meta */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/request-meta.js\");\n        const initUrl = getRequestMeta(req, 'initURL');\n        if (initUrl) {\n            const url = new URL(initUrl);\n            hostname = url.hostname;\n        }\n    }\n    return {\n        statusCode,\n        hostname\n    };\n}\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: '100vh',\n        textAlign: 'center',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center'\n    },\n    desc: {\n        lineHeight: '48px'\n    },\n    h1: {\n        display: 'inline-block',\n        margin: '0 20px 0 0',\n        paddingRight: 23,\n        fontSize: 24,\n        fontWeight: 500,\n        verticalAlign: 'top'\n    },\n    h2: {\n        fontSize: 14,\n        fontWeight: 400,\n        lineHeight: '28px'\n    },\n    wrap: {\n        display: 'inline-block'\n    }\n};\nclass Error extends _react.default.Component {\n    render() {\n        const { statusCode, withDarkMode = true } = this.props;\n        const title = this.props.title || statusCodes[statusCode] || 'An unexpected error has occurred';\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            style: styles.error,\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(_head.default, {\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"title\", {\n                        children: statusCode ? statusCode + \": \" + title : 'Application error: a client-side exception has occurred'\n                    })\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    style: styles.desc,\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            dangerouslySetInnerHTML: {\n                                /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */ __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}\" + (withDarkMode ? '@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}' : '')\n                            }\n                        }),\n                        statusCode ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                            className: \"next-error-h1\",\n                            style: styles.h1,\n                            children: statusCode\n                        }) : null,\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            style: styles.wrap,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"h2\", {\n                                style: styles.h2,\n                                children: [\n                                    this.props.title || statusCode ? title : /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                                        children: [\n                                            \"Application error: a client-side exception has occurred\",\n                                            ' ',\n                                            Boolean(this.props.hostname) && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                                                children: [\n                                                    \"while loading \",\n                                                    this.props.hostname\n                                                ]\n                                            }),\n                                            ' ',\n                                            \"(see the browser console for more information)\"\n                                        ]\n                                    }),\n                                    \".\"\n                                ]\n                            })\n                        })\n                    ]\n                })\n            ]\n        });\n    }\n}\nError.displayName = 'ErrorPage';\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=_error.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_error.js\n");

/***/ }),

/***/ "../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_document.js":
/*!**********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_document.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/// <reference types=\"webpack/module.d.ts\" />\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Head: function() {\n        return Head;\n    },\n    Html: function() {\n        return Html;\n    },\n    Main: function() {\n        return Main;\n    },\n    NextScript: function() {\n        return NextScript;\n    },\n    /**\n * `Document` component handles the initial `document` markup and renders only on the server side.\n * Commonly used for implementing server side rendering for `css-in-js` libraries.\n */ default: function() {\n        return Document;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard(__webpack_require__(/*! react */ \"react\"));\nconst _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/constants.js\");\nconst _getpagefiles = __webpack_require__(/*! ../server/get-page-files */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/get-page-files.js\");\nconst _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/htmlescape.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default(__webpack_require__(/*! ../lib/is-error */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/is-error.js\"));\nconst _htmlcontextsharedruntime = __webpack_require__(/*! ../shared/lib/html-context.shared-runtime */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages/vendored/contexts/html-context.js\");\nconst _encodeuripath = __webpack_require__(/*! ../shared/lib/encode-uri-path */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/encode-uri-path.js\");\nconst _tracer = __webpack_require__(/*! ../server/lib/trace/tracer */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/trace/tracer.js\");\nconst _utils = __webpack_require__(/*! ../server/lib/trace/utils */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/trace/utils.js\");\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {\n        __proto__: null\n    };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\n/** Set of pages that have triggered a large data warning on production mode. */ const largePageDataWarnings = new Set();\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getpagefiles.getPageFiles)(buildManifest, '/_app');\n    const pageFiles =  true && inAmpMode ? [] : (0, _getpagefiles.getPageFiles)(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix, buildManifest, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')).map((polyfill)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(polyfill)}${assetQueryString}`\n        }, polyfill));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var _el_props_dangerouslySetInnerHTML, _el_props;\n            return el == null ? void 0 : (_el_props = el.props) == null ? void 0 : (_el_props_dangerouslySetInnerHTML = _el_props.dangerouslySetInnerHTML) == null ? void 0 : _el_props_dangerouslySetInnerHTML.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join('').replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, '').replace(/\\/\\*@ sourceURL=.*?\\*\\//g, '')\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports, assetPrefix, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith('.js') || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        }, file);\n    });\n}\nfunction getScripts(context, props, files) {\n    var _buildManifest_lowPriorityFiles;\n    const { assetPrefix, buildManifest, isDevelopment, assetQueryString, disableOptimizedLoading, crossOrigin } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith('.js'));\n    const lowPriorityScripts = (_buildManifest_lowPriorityFiles = buildManifest.lowPriorityFiles) == null ? void 0 : _buildManifest_lowPriorityFiles.filter((file)=>file.endsWith('.js'));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        }, file);\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix, scriptLoader, crossOrigin, nextScriptWorkers } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === 'edge') return null;\n    try {\n        // @ts-expect-error: Prevent webpack from processing this require\n        let { partytownSnippet } = require('@builder.io/partytown/integration');\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var _child_props_dangerouslySetInnerHTML, _child_props;\n            return hasComponentProps(child) && (child == null ? void 0 : (_child_props = child.props) == null ? void 0 : (_child_props_dangerouslySetInnerHTML = _child_props.dangerouslySetInnerHTML) == null ? void 0 : _child_props_dangerouslySetInnerHTML.__html.length) && 'data-partytown-config' in child.props;\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                !userDefinedConfig && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    \"data-partytown-config\": \"\",\n                    dangerouslySetInnerHTML: {\n                        __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n                    }\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    \"data-partytown\": \"\",\n                    dangerouslySetInnerHTML: {\n                        __html: partytownSnippet()\n                    }\n                }),\n                (scriptLoader.worker || []).map((file, index)=>{\n                    const { strategy, src, children: scriptChildren, dangerouslySetInnerHTML, ...scriptProps } = file;\n                    let srcProps = {};\n                    if (src) {\n                        // Use external src if provided\n                        srcProps.src = src;\n                    } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                        // Embed inline script if provided with dangerouslySetInnerHTML\n                        srcProps.dangerouslySetInnerHTML = {\n                            __html: dangerouslySetInnerHTML.__html\n                        };\n                    } else if (scriptChildren) {\n                        // Embed inline script if provided with children\n                        srcProps.dangerouslySetInnerHTML = {\n                            __html: typeof scriptChildren === 'string' ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join('') : ''\n                        };\n                    } else {\n                        throw Object.defineProperty(new Error('Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E82\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return /*#__PURE__*/ (0, _react.createElement)(\"script\", {\n                        ...srcProps,\n                        ...scriptProps,\n                        type: \"text/partytown\",\n                        key: src || index,\n                        nonce: props.nonce,\n                        \"data-nscript\": \"worker\",\n                        crossOrigin: props.crossOrigin || crossOrigin\n                    });\n                })\n            ]\n        });\n    } catch (err) {\n        if ((0, _iserror.default)(err) && err.code !== 'MODULE_NOT_FOUND') {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader, disableOptimizedLoading, crossOrigin } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy, ...scriptProps } = file;\n        return /*#__PURE__*/ (0, _react.createElement)(\"script\", {\n            ...scriptProps,\n            key: scriptProps.src || index,\n            defer: scriptProps.defer ?? !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            webWorkerScripts,\n            beforeInteractiveScripts\n        ]\n    });\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin, nonce, ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes('?') ? '&' : '?'}amp=1`;\n}\nfunction getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix = '') {\n    if (!nextFontManifest) {\n        return {\n            preconnect: null,\n            preload: null\n        };\n    }\n    const appFontsEntry = nextFontManifest.pages['/_app'];\n    const pageFontsEntry = nextFontManifest.pages[dangerousAsPath];\n    const preloadedFontFiles = Array.from(new Set([\n        ...appFontsEntry ?? [],\n        ...pageFontsEntry ?? []\n    ]));\n    // If no font files should preload but there's an entry for the path, add a preconnect tag.\n    const preconnectToSelf = !!(preloadedFontFiles.length === 0 && (appFontsEntry || pageFontsEntry));\n    return {\n        preconnect: preconnectToSelf ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            \"data-next-font\": nextFontManifest.pagesUsingSizeAdjust ? 'size-adjust' : '',\n            rel: \"preconnect\",\n            href: \"/\",\n            crossOrigin: \"anonymous\"\n        }) : null,\n        preload: preloadedFontFiles ? preloadedFontFiles.map((fontFile)=>{\n            const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)[1];\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                rel: \"preload\",\n                href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(fontFile)}`,\n                as: \"font\",\n                type: `font/${ext}`,\n                crossOrigin: \"anonymous\",\n                \"data-next-font\": fontFile.includes('-s') ? 'size-adjust' : ''\n            }, fontFile);\n        }) : null\n    };\n}\nclass Head extends _react.default.Component {\n    static #_ = this.contextType = _htmlcontextsharedruntime.HtmlContext;\n    getCssLinks(files) {\n        const { assetPrefix, assetQueryString, dynamicImports, dynamicCssManifest, crossOrigin, optimizeCss } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith('.css'));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmanagedFiles = new Set([]);\n        let localDynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith('.css'))));\n        if (localDynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            localDynamicCssFiles = localDynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmanagedFiles = new Set(localDynamicCssFiles);\n            cssFiles.push(...localDynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            const isUnmanagedFile = unmanagedFiles.has(file);\n            const isFileInDynamicCssManifest = dynamicCssManifest.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }, `${file}-preload`));\n            }\n            cssLinkElements.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? '' : undefined,\n                \"data-n-p\": isSharedFile || isUnmanagedFile || isFileInDynamicCssManifest ? undefined : ''\n            }, file));\n        });\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports, assetPrefix, assetQueryString, crossOrigin } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith('.js')) {\n                return null;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                rel: \"preload\",\n                href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            }, file);\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix, assetQueryString, scriptLoader, crossOrigin } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith('.js');\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }, file.src)),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }, file))\n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader } = this.context;\n        const { nonce, crossOrigin } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy, children, dangerouslySetInnerHTML, src, ...scriptProps } = file;\n            let html = '';\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n            }\n            return /*#__PURE__*/ (0, _react.createElement)(\"script\", {\n                ...scriptProps,\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            });\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    render() {\n        const { styles, ampPath, inAmpMode, hybridAmp, canonicalBase, __NEXT_DATA__, dangerousAsPath, headTags, unstable_runtimeJS, unstable_JsPreload, disableOptimizedLoading, optimizeCss, assetPrefix, nextFontManifest } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((child)=>{\n                if (child && child.type === 'link' && child.props['rel'] === 'preload' && child.props['as'] === 'style') {\n                    if (this.context.strictNextHead) {\n                        cssPreloads.push(/*#__PURE__*/ _react.default.cloneElement(child, {\n                            'data-next-head': ''\n                        }));\n                    } else {\n                        cssPreloads.push(child);\n                    }\n                } else {\n                    if (child) {\n                        if (this.context.strictNextHead) {\n                            otherHeadElements.push(/*#__PURE__*/ _react.default.cloneElement(child, {\n                                'data-next-head': ''\n                            }));\n                        } else {\n                            otherHeadElements.push(child);\n                        }\n                    }\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var _child_props;\n                const isReactHelmet = child == null ? void 0 : (_child_props = child.props) == null ? void 0 : _child_props['data-react-helmet'];\n                if (!isReactHelmet) {\n                    var _child_props1;\n                    if ((child == null ? void 0 : child.type) === 'title') {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === 'meta' && (child == null ? void 0 : (_child_props1 = child.props) == null ? void 0 : _child_props1.name) === 'viewport') {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n            });\n            if (this.props.crossOrigin) console.warn('Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated');\n        }\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type, props } = child;\n            if ( true && inAmpMode) {\n                let badProp = '';\n                if (type === 'meta' && props.name === 'viewport') {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === 'link' && props.rel === 'canonical') {\n                    hasCanonicalRel = true;\n                } else if (type === 'script') {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf('ampproject') < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === 'text/javascript')) {\n                        badProp = '<script';\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += '/>';\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === 'link' && props.rel === 'amphtml') {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        const nextFontLinkTags = getNextFontLinkTags(nextFontManifest, dangerousAsPath, assetPrefix);\n        const tracingMetadata = (0, _utils.getTracedMetadata)((0, _tracer.getTracer)().getTracePropagationData(), this.context.experimentalClientTraceMetadata);\n        const traceMetaTags = (tracingMetadata || []).map(({ key, value }, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                name: key,\n                content: value\n            }, `next-trace-data-${index}`));\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"head\", {\n            ...getHeadHTMLProps(this.props),\n            children: [\n                this.context.isDevelopment && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            \"data-next-hide-fouc\": true,\n                            \"data-ampdevmode\":  true && inAmpMode ? 'true' : undefined,\n                            dangerouslySetInnerHTML: {\n                                __html: `body{display:none}`\n                            }\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            \"data-next-hide-fouc\": true,\n                            \"data-ampdevmode\":  true && inAmpMode ? 'true' : undefined,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                                dangerouslySetInnerHTML: {\n                                    __html: `body{display:block}`\n                                }\n                            })\n                        })\n                    ]\n                }),\n                head,\n                this.context.strictNextHead ? null : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                    name: \"next-head-count\",\n                    content: _react.default.Children.count(head || []).toString()\n                }),\n                children,\n                nextFontLinkTags.preconnect,\n                nextFontLinkTags.preload,\n                 true && inAmpMode && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n                        }),\n                        !hasCanonicalRel && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                            rel: \"canonical\",\n                            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/utils.js\").cleanAmpPath)(dangerousAsPath)\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                            rel: \"preload\",\n                            as: \"script\",\n                            href: \"https://cdn.ampproject.org/v0.js\"\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AmpStyles, {\n                            styles: styles\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            \"amp-boilerplate\": \"\",\n                            dangerouslySetInnerHTML: {\n                                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n                            }\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                                \"amp-boilerplate\": \"\",\n                                dangerouslySetInnerHTML: {\n                                    __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n                                }\n                            })\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                            async: true,\n                            src: \"https://cdn.ampproject.org/v0.js\"\n                        })\n                    ]\n                }),\n                !( true && inAmpMode) && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                    children: [\n                        !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                            rel: \"amphtml\",\n                            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n                        }),\n                        this.getBeforeInteractiveInlineScripts(),\n                        !optimizeCss && this.getCssLinks(files),\n                        !optimizeCss && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            \"data-n-css\": this.props.nonce ?? ''\n                        }),\n                        !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(),\n                        !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files),\n                        !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files),\n                        optimizeCss && this.getCssLinks(files),\n                        optimizeCss && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            \"data-n-css\": this.props.nonce ?? ''\n                        }),\n                        this.context.isDevelopment && // this element is used to mount development styles so the\n                        // ordering matches production\n                        // (by default, style-loader injects at the bottom of <head />)\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"noscript\", {\n                            id: \"__next_css__DO_NOT_USE__\"\n                        }),\n                        traceMetaTags,\n                        styles || null\n                    ]\n                }),\n                /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || [])\n            ]\n        });\n    }\n}\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var _children_find_props, _children_find, _children_find_props1, _children_find1;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (_children_find = children.find((child)=>child.type === Head)) == null ? void 0 : (_children_find_props = _children_find.props) == null ? void 0 : _children_find_props.children;\n    const bodyChildren = (_children_find1 = children.find((child)=>child.type === 'body')) == null ? void 0 : (_children_find_props1 = _children_find1.props) == null ? void 0 : _children_find_props1.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ]\n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var _child_type;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((_child_type = child.type) == null ? void 0 : _child_type.__nextScript) {\n            if (child.props.strategy === 'beforeInteractive') {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }\n                ]);\n                return;\n            } else if ([\n                'lazyOnload',\n                'afterInteractive',\n                'worker'\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            } else if (typeof child.props.strategy === 'undefined') {\n                scriptLoaderItems.push({\n                    ...child.props,\n                    strategy: 'afterInteractive'\n                });\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static #_ = this.contextType = _htmlcontextsharedruntime.HtmlContext;\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__, largePageDataBytes } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n                return (0, _htmlescape.htmlEscapeJsonString)(data);\n            }\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                if (false) {}\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? '' : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape.htmlEscapeJsonString)(data);\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.message.indexOf('circular structure') !== -1) {\n                throw Object.defineProperty(new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E490\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix, inAmpMode, buildManifest, unstable_runtimeJS, docComponentsRendered, assetQueryString, disableOptimizedLoading, crossOrigin } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles\n            ];\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    disableRuntimeJS ? null : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                        id: \"__NEXT_DATA__\",\n                        type: \"application/json\",\n                        nonce: this.props.nonce,\n                        crossOrigin: this.props.crossOrigin || crossOrigin,\n                        dangerouslySetInnerHTML: {\n                            __html: NextScript.getInlineScriptSource(this.context)\n                        },\n                        \"data-ampdevmode\": true\n                    }),\n                    ampDevFiles.map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                            src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                            nonce: this.props.nonce,\n                            crossOrigin: this.props.crossOrigin || crossOrigin,\n                            \"data-ampdevmode\": true\n                        }, file))\n                ]\n            });\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn('Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated');\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                        src: `${assetPrefix}/_next/${(0, _encodeuripath.encodeURIPath)(file)}${assetQueryString}`,\n                        nonce: this.props.nonce,\n                        crossOrigin: this.props.crossOrigin || crossOrigin\n                    }, file)) : null,\n                disableRuntimeJS ? null : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    id: \"__NEXT_DATA__\",\n                    type: \"application/json\",\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    dangerouslySetInnerHTML: {\n                        __html: NextScript.getInlineScriptSource(this.context)\n                    }\n                }),\n                disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(),\n                disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(),\n                disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files),\n                disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files)\n            ]\n        });\n    }\n}\nfunction Html(props) {\n    const { inAmpMode, docComponentsRendered, locale, scriptLoader, __NEXT_DATA__ } = (0, _htmlcontextsharedruntime.useHtmlContext)();\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"html\", {\n        ...props,\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? '' : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== 'production' ? '' : undefined\n    });\n}\nfunction Main() {\n    const { docComponentsRendered } = (0, _htmlcontextsharedruntime.useHtmlContext)();\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"next-js-internal-body-render-target\", {});\n}\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(Html, {\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {}),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(Main, {}),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(NextScript, {})\n                    ]\n                })\n            ]\n        });\n    }\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(Html, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(Main, {}),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(NextScript, {})\n                ]\n            })\n        ]\n    });\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_error.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_error.js ***!
  \*******************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return Error;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/cjs/_interop_require_default.cjs\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"react\"));\nconst _head = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/head */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/head.js\"));\nconst statusCodes = {\n    400: 'Bad Request',\n    404: 'This page could not be found',\n    405: 'Method Not Allowed',\n    500: 'Internal Server Error'\n};\nfunction _getInitialProps(param) {\n    let { req, res, err } = param;\n    const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n    let hostname;\n    if (false) {} else if (req) {\n        const { getRequestMeta } = __webpack_require__(/*! ../server/request-meta */ \"../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/request-meta.js\");\n        const initUrl = getRequestMeta(req, 'initURL');\n        if (initUrl) {\n            const url = new URL(initUrl);\n            hostname = url.hostname;\n        }\n    }\n    return {\n        statusCode,\n        hostname\n    };\n}\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: '100vh',\n        textAlign: 'center',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center'\n    },\n    desc: {\n        lineHeight: '48px'\n    },\n    h1: {\n        display: 'inline-block',\n        margin: '0 20px 0 0',\n        paddingRight: 23,\n        fontSize: 24,\n        fontWeight: 500,\n        verticalAlign: 'top'\n    },\n    h2: {\n        fontSize: 14,\n        fontWeight: 400,\n        lineHeight: '28px'\n    },\n    wrap: {\n        display: 'inline-block'\n    }\n};\nclass Error extends _react.default.Component {\n    render() {\n        const { statusCode, withDarkMode = true } = this.props;\n        const title = this.props.title || statusCodes[statusCode] || 'An unexpected error has occurred';\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            style: styles.error,\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(_head.default, {\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"title\", {\n                        children: statusCode ? statusCode + \": \" + title : 'Application error: a client-side exception has occurred'\n                    })\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    style: styles.desc,\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            dangerouslySetInnerHTML: {\n                                /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */ __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}\" + (withDarkMode ? '@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}' : '')\n                            }\n                        }),\n                        statusCode ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                            className: \"next-error-h1\",\n                            style: styles.h1,\n                            children: statusCode\n                        }) : null,\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            style: styles.wrap,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"h2\", {\n                                style: styles.h2,\n                                children: [\n                                    this.props.title || statusCode ? title : /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                                        children: [\n                                            \"Application error: a client-side exception has occurred\",\n                                            ' ',\n                                            Boolean(this.props.hostname) && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                                                children: [\n                                                    \"while loading \",\n                                                    this.props.hostname\n                                                ]\n                                            }),\n                                            ' ',\n                                            \"(see the browser console for more information)\"\n                                        ]\n                                    }),\n                                    \".\"\n                                ]\n                            })\n                        })\n                    ]\n                })\n            ]\n        });\n    }\n}\nError.displayName = 'ErrorPage';\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=_error.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/pages/_error.js\n");

/***/ })

};
;