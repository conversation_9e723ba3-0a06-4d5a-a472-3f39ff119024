"use strict";(()=>{var e={};e.id=5127,e.ids=[5127],e.modules={7158:(e,r,t)=>{t.r(r),t.d(r,{config:()=>j,default:()=>v,routeModule:()=>m});var s={};t.r(s),t.d(s,{default:()=>h});var o=t(93433),n=t(20264),a=t(20584),i=t(15806),u=t(94506),d=t(12518);let{url:l,name:p}=t(98580).dashboardConfig.database,f=null;async function c(){if(f)return f;let e=await d.MongoClient.connect(l);return f=e,e}async function h(e,r){if(!await (0,i.getServerSession)(e,r,u.authOptions))return r.status(401).json({error:"Unauthorized"});try{let t=(await c()).db(p).collection("gameservers");switch(e.method){case"GET":let s=await t.find({}).toArray();return r.status(200).json(s);case"POST":let o=e.body;if(!o.type||!o.host||!o.port)return r.status(400).json({error:"Missing required fields"});if(o.port<1||o.port>65535)return r.status(400).json({error:"Invalid port number"});if(await t.findOne({host:o.host,port:o.port}))return r.status(409).json({error:"Server already exists"});let n=await t.insertOne(o);return r.status(201).json({...o,_id:n.insertedId});case"PUT":let{id:a,...i}=e.body;if(!i.type||!i.host||!i.port)return r.status(400).json({error:"Missing required fields"});if(i.port<1||i.port>65535)return r.status(400).json({error:"Invalid port number"});try{let e=null;if(a)try{e={_id:new d.ObjectId(a)}}catch(e){}e||(e={host:i.host,port:i.port});let s=await t.findOne(e);if(!s)return r.status(404).json({error:"Server not found"});if((s.host!==i.host||s.port!==i.port)&&await t.findOne({_id:{$ne:s._id},host:i.host,port:i.port}))return r.status(409).json({error:"Another server with this host and port already exists"});let o=await t.updateOne({_id:s._id},{$set:i});if(0===o.matchedCount)return r.status(404).json({error:"Server not found after update"});let n=await t.findOne({_id:s._id});return r.status(200).json(n)}catch(e){if(e instanceof Error&&e.message.includes("ObjectId"))return r.status(400).json({error:"Invalid server ID format"});return r.status(500).json({error:"Failed to update server"})}case"DELETE":let u,l;try{u="string"==typeof e.body?JSON.parse(e.body):e.body}catch(e){return r.status(400).json({error:"Invalid request body format"})}if(u.id)try{l={_id:new d.ObjectId(u.id)}}catch(e){return r.status(400).json({error:"Invalid server ID format"})}else{if(!u.host||void 0===u.port)return r.status(400).json({error:"Missing server identification (id or host/port)"});l={host:u.host.toString(),port:parseInt(u.port.toString())}}try{let e=await t.findOneAndDelete(l);if(!e?.value)return r.status(404).json({error:"Server not found"});return r.status(200).json({message:"Server deleted successfully",deletedServer:e.value})}catch(e){return r.status(500).json({error:"Failed to delete server from database"})}default:return r.setHeader("Allow",["GET","POST","PUT","DELETE"]),r.status(405).json({error:`Method ${e.method} not allowed`})}}catch(e){return r.status(500).json({error:"Internal server error"})}}let v=(0,a.M)(s,"default"),j=(0,a.M)(s,"config"),m=new o.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/gameservers/manage",pathname:"/api/gameservers/manage",bundlePath:"",filename:""},userland:s})},12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(7158));module.exports=s})();