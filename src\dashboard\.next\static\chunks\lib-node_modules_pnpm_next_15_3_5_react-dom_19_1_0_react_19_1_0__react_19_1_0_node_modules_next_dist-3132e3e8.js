"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-3132e3e8"],{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js ***!
  \*************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    CALL_STACK_STYLES: function() {\n        return CALL_STACK_STYLES;\n    },\n    CallStack: function() {\n        return CallStack;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nconst _callstackframe = __webpack_require__(/*! ../../call-stack-frame/call-stack-frame */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/call-stack-frame/call-stack-frame.js\");\nfunction CallStack(param) {\n    let { frames, dialogResizerRef } = param;\n    const initialDialogHeight = (0, _react.useRef)(NaN);\n    const [isIgnoreListOpen, setIsIgnoreListOpen] = (0, _react.useState)(false);\n    const ignoredFramesTally = (0, _react.useMemo)(()=>{\n        return frames.reduce((tally, frame)=>tally + (frame.ignored ? 1 : 0), 0);\n    }, [\n        frames\n    ]);\n    function onToggleIgnoreList() {\n        const dialog = dialogResizerRef == null ? void 0 : dialogResizerRef.current;\n        if (!dialog) {\n            return;\n        }\n        const { height: currentHeight } = dialog == null ? void 0 : dialog.getBoundingClientRect();\n        if (!initialDialogHeight.current) {\n            initialDialogHeight.current = currentHeight;\n        }\n        if (isIgnoreListOpen) {\n            function onTransitionEnd() {\n                dialog.removeEventListener('transitionend', onTransitionEnd);\n                setIsIgnoreListOpen(false);\n            }\n            dialog.style.height = \"\" + initialDialogHeight.current + \"px\";\n            dialog.addEventListener('transitionend', onTransitionEnd);\n        } else {\n            setIsIgnoreListOpen(true);\n        }\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        className: \"error-overlay-call-stack-container\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                className: \"error-overlay-call-stack-header\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        className: \"error-overlay-call-stack-title\",\n                        children: [\n                            \"Call Stack\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                className: \"error-overlay-call-stack-count\",\n                                children: frames.length\n                            })\n                        ]\n                    }),\n                    ignoredFramesTally > 0 && /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"button\", {\n                        \"data-expand-ignore-button\": isIgnoreListOpen,\n                        className: \"error-overlay-call-stack-ignored-list-toggle-button\",\n                        onClick: onToggleIgnoreList,\n                        children: [\n                            (isIgnoreListOpen ? 'Hide' : 'Show') + \" \" + ignoredFramesTally + \" ignore-listed frame(s)\",\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(ChevronUpDown, {})\n                        ]\n                    })\n                ]\n            }),\n            frames.map((frame, frameIndex)=>{\n                return !frame.ignored || isIgnoreListOpen ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_callstackframe.CallStackFrame, {\n                    frame: frame\n                }, frameIndex) : null;\n            })\n        ]\n    });\n}\n_c = CallStack;\nfunction ChevronUpDown() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M8.70722 2.39641C8.3167 2.00588 7.68353 2.00588 7.29301 2.39641L4.46978 5.21963L3.93945 5.74996L5.00011 6.81062L5.53044 6.28029L8.00011 3.81062L10.4698 6.28029L11.0001 6.81062L12.0608 5.74996L11.5304 5.21963L8.70722 2.39641ZM5.53044 9.71963L5.00011 9.1893L3.93945 10.25L4.46978 10.7803L7.29301 13.6035C7.68353 13.994 8.3167 13.994 8.70722 13.6035L11.5304 10.7803L12.0608 10.25L11.0001 9.1893L10.4698 9.71963L8.00011 12.1893L5.53044 9.71963Z\",\n            fill: \"currentColor\"\n        })\n    });\n}\n_c1 = ChevronUpDown;\nconst CALL_STACK_STYLES = \"\\n  .error-overlay-call-stack-container {\\n    position: relative;\\n    margin-top: 8px;\\n  }\\n\\n  .error-overlay-call-stack-header {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    min-height: var(--size-28);\\n    padding: 8px 8px 12px 4px;\\n    width: 100%;\\n  }\\n\\n  .error-overlay-call-stack-title {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    gap: 8px;\\n\\n    margin: 0;\\n\\n    color: var(--color-gray-1000);\\n    font-size: var(--size-16);\\n    font-weight: 500;\\n  }\\n\\n  .error-overlay-call-stack-count {\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n\\n    width: var(--size-20);\\n    height: var(--size-20);\\n    gap: 4px;\\n\\n    color: var(--color-gray-1000);\\n    text-align: center;\\n    font-size: var(--size-11);\\n    font-weight: 500;\\n    line-height: var(--size-16);\\n\\n    border-radius: var(--rounded-full);\\n    background: var(--color-gray-300);\\n  }\\n\\n  .error-overlay-call-stack-ignored-list-toggle-button {\\n    all: unset;\\n    display: flex;\\n    align-items: center;\\n    gap: 6px;\\n    color: var(--color-gray-900);\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    border-radius: 6px;\\n    padding: 4px 6px;\\n    margin-right: -6px;\\n    transition: background 150ms ease;\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n\\n    &:focus {\\n      outline: var(--focus-ring);\\n    }\\n\\n    svg {\\n      width: var(--size-16);\\n      height: var(--size-16);\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=call-stack.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"CallStack\");\n$RefreshReg$(_c1, \"ChevronUpDown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js":
/*!*******************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js ***!
  \*******************************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INDICATOR_STYLES: function() {\n        return DEV_TOOLS_INDICATOR_STYLES;\n    },\n    DevToolsIndicator: function() {\n        return DevToolsIndicator;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nconst _toast = __webpack_require__(/*! ../../toast */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/toast/index.js\");\nconst _nextlogo = __webpack_require__(/*! ./next-logo */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js\");\nconst _initialize = __webpack_require__(/*! ../../../../../../dev/dev-build-indicator/internal/initialize */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/dev/dev-build-indicator/internal/initialize.js\");\nconst _devrenderindicator = __webpack_require__(/*! ../../../../utils/dev-indicator/dev-render-indicator */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/dev-render-indicator.js\");\nconst _usedelayedrender = __webpack_require__(/*! ../../../hooks/use-delayed-render */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-delayed-render.js\");\nconst _turbopackinfo = __webpack_require__(/*! ./dev-tools-info/turbopack-info */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js\");\nconst _routeinfo = __webpack_require__(/*! ./dev-tools-info/route-info */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js\");\nconst _gearicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../icons/gear-icon */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/gear-icon.js\"));\nconst _userpreferences = __webpack_require__(/*! ./dev-tools-info/user-preferences */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js\");\nconst _utils = __webpack_require__(/*! ./utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js\");\nconst _preferences = __webpack_require__(/*! ./dev-tools-info/preferences */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/preferences.js\");\nfunction DevToolsIndicator(param) {\n    let { state, errorCount, isBuildError, setIsErrorOverlayOpen, ...props } = param;\n    const [isDevToolsIndicatorVisible, setIsDevToolsIndicatorVisible] = (0, _react.useState)(true);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(DevToolsPopover, {\n        routerType: state.routerType,\n        semver: state.versionInfo.installed,\n        issueCount: errorCount,\n        isStaticRoute: state.staticIndicator,\n        hide: ()=>{\n            setIsDevToolsIndicatorVisible(false);\n            fetch('/__nextjs_disable_dev_indicator', {\n                method: 'POST'\n            });\n        },\n        setIsErrorOverlayOpen: setIsErrorOverlayOpen,\n        isTurbopack: !!false,\n        disabled: state.disableDevIndicator || !isDevToolsIndicatorVisible,\n        isBuildError: isBuildError,\n        ...props\n    });\n}\n_c = DevToolsIndicator;\nconst Context = /*#__PURE__*/ (0, _react.createContext)({});\nconst OVERLAYS = {\n    Root: 'root',\n    Turbo: 'turbo',\n    Route: 'route',\n    Preferences: 'preferences'\n};\nfunction DevToolsPopover(param) {\n    let { routerType, disabled, issueCount, isStaticRoute, isTurbopack, isBuildError, hide, setIsErrorOverlayOpen, scale, setScale } = param;\n    const menuRef = (0, _react.useRef)(null);\n    const triggerRef = (0, _react.useRef)(null);\n    const [open, setOpen] = (0, _react.useState)(null);\n    const [position, setPosition] = (0, _react.useState)((0, _preferences.getInitialPosition)());\n    const [selectedIndex, setSelectedIndex] = (0, _react.useState)(-1);\n    const isMenuOpen = open === OVERLAYS.Root;\n    const isTurbopackInfoOpen = open === OVERLAYS.Turbo;\n    const isRouteInfoOpen = open === OVERLAYS.Route;\n    const isPreferencesOpen = open === OVERLAYS.Preferences;\n    const { mounted: menuMounted, rendered: menuRendered } = (0, _usedelayedrender.useDelayedRender)(isMenuOpen, {\n        // Intentionally no fade in, makes the UI feel more immediate\n        enterDelay: 0,\n        // Graceful fade out to confirm that the UI did not break\n        exitDelay: _utils.MENU_DURATION_MS\n    });\n    // Features to make the menu accessible\n    (0, _utils.useFocusTrap)(menuRef, triggerRef, isMenuOpen);\n    (0, _utils.useClickOutside)(menuRef, triggerRef, isMenuOpen, closeMenu);\n    (0, _react.useEffect)(()=>{\n        if (open === null) {\n            // Avoid flashing selected state\n            const id = setTimeout(()=>{\n                setSelectedIndex(-1);\n            }, _utils.MENU_DURATION_MS);\n            return ()=>clearTimeout(id);\n        }\n    }, [\n        open\n    ]);\n    function select(index) {\n        var _menuRef_current;\n        if (index === 'first') {\n            setTimeout(()=>{\n                var _menuRef_current;\n                const all = (_menuRef_current = menuRef.current) == null ? void 0 : _menuRef_current.querySelectorAll('[role=\"menuitem\"]');\n                if (all) {\n                    const firstIndex = all[0].getAttribute('data-index');\n                    select(Number(firstIndex));\n                }\n            });\n            return;\n        }\n        if (index === 'last') {\n            setTimeout(()=>{\n                var _menuRef_current;\n                const all = (_menuRef_current = menuRef.current) == null ? void 0 : _menuRef_current.querySelectorAll('[role=\"menuitem\"]');\n                if (all) {\n                    const lastIndex = all.length - 1;\n                    select(lastIndex);\n                }\n            });\n            return;\n        }\n        const el = (_menuRef_current = menuRef.current) == null ? void 0 : _menuRef_current.querySelector('[data-index=\"' + index + '\"]');\n        if (el) {\n            setSelectedIndex(index);\n            el == null ? void 0 : el.focus();\n        }\n    }\n    function onMenuKeydown(e) {\n        e.preventDefault();\n        switch(e.key){\n            case 'ArrowDown':\n                const next = selectedIndex + 1;\n                select(next);\n                break;\n            case 'ArrowUp':\n                const prev = selectedIndex - 1;\n                select(prev);\n                break;\n            case 'Home':\n                select('first');\n                break;\n            case 'End':\n                select('last');\n                break;\n            default:\n                break;\n        }\n    }\n    function openErrorOverlay() {\n        setOpen(null);\n        if (issueCount > 0) {\n            setIsErrorOverlayOpen(true);\n        }\n    }\n    function toggleErrorOverlay() {\n        setIsErrorOverlayOpen((prev)=>!prev);\n    }\n    function openRootMenu() {\n        setOpen((prevOpen)=>{\n            if (prevOpen === null) select('first');\n            return OVERLAYS.Root;\n        });\n    }\n    function onTriggerClick() {\n        if (open === OVERLAYS.Root) {\n            setOpen(null);\n        } else {\n            openRootMenu();\n            setTimeout(()=>{\n                select('first');\n            });\n        }\n    }\n    function closeMenu() {\n        // Only close when we were on `Root`,\n        // otherwise it will close other overlays\n        setOpen((prevOpen)=>{\n            if (prevOpen === OVERLAYS.Root) {\n                return null;\n            }\n            return prevOpen;\n        });\n    }\n    function handleHideDevtools() {\n        setOpen(null);\n        hide();\n    }\n    const [vertical, horizontal] = position.split('-', 2);\n    const popover = {\n        [vertical]: 'calc(100% + 8px)',\n        [horizontal]: 0\n    };\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_toast.Toast, {\n        \"data-nextjs-toast\": true,\n        style: {\n            '--animate-out-duration-ms': \"\" + _utils.MENU_DURATION_MS + \"ms\",\n            '--animate-out-timing-function': _utils.MENU_CURVE,\n            boxShadow: 'none',\n            zIndex: 2147483647,\n            // Reset the toast component's default positions.\n            bottom: 'initial',\n            left: 'initial',\n            [vertical]: '20px',\n            [horizontal]: '20px'\n        },\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_nextlogo.NextLogo, {\n                ref: triggerRef,\n                \"aria-haspopup\": \"menu\",\n                \"aria-expanded\": isMenuOpen,\n                \"aria-controls\": \"nextjs-dev-tools-menu\",\n                \"aria-label\": \"\" + (isMenuOpen ? 'Close' : 'Open') + \" Next.js Dev Tools\",\n                \"data-nextjs-dev-tools-button\": true,\n                disabled: disabled,\n                issueCount: issueCount,\n                onTriggerClick: onTriggerClick,\n                toggleErrorOverlay: toggleErrorOverlay,\n                isDevBuilding: (0, _initialize.useIsDevBuilding)(),\n                isDevRendering: (0, _devrenderindicator.useIsDevRendering)(),\n                isBuildError: isBuildError,\n                scale: scale\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_routeinfo.RouteInfo, {\n                isOpen: isRouteInfoOpen,\n                close: openRootMenu,\n                triggerRef: triggerRef,\n                style: popover,\n                routerType: routerType,\n                routeType: isStaticRoute ? 'Static' : 'Dynamic'\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_turbopackinfo.TurbopackInfo, {\n                isOpen: isTurbopackInfoOpen,\n                close: openRootMenu,\n                triggerRef: triggerRef,\n                style: popover\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_userpreferences.UserPreferences, {\n                isOpen: isPreferencesOpen,\n                close: openRootMenu,\n                triggerRef: triggerRef,\n                style: popover,\n                hide: handleHideDevtools,\n                setPosition: setPosition,\n                position: position,\n                scale: scale,\n                setScale: setScale\n            }),\n            menuMounted && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                ref: menuRef,\n                id: \"nextjs-dev-tools-menu\",\n                role: \"menu\",\n                dir: \"ltr\",\n                \"aria-orientation\": \"vertical\",\n                \"aria-label\": \"Next.js Dev Tools Items\",\n                tabIndex: -1,\n                className: \"dev-tools-indicator-menu\",\n                onKeyDown: onMenuKeydown,\n                \"data-rendered\": menuRendered,\n                style: popover,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(Context.Provider, {\n                    value: {\n                        closeMenu,\n                        selectedIndex,\n                        setSelectedIndex\n                    },\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"dev-tools-indicator-inner\",\n                            children: [\n                                issueCount > 0 && /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    title: issueCount + \" \" + (issueCount === 1 ? 'issue' : 'issues') + \" found. Click to view details in the dev overlay.\",\n                                    index: 0,\n                                    label: \"Issues\",\n                                    value: /*#__PURE__*/ (0, _jsxruntime.jsx)(IssueCount, {\n                                        children: issueCount\n                                    }),\n                                    onClick: openErrorOverlay\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    title: \"Current route is \" + (isStaticRoute ? 'static' : 'dynamic') + \".\",\n                                    label: \"Route\",\n                                    index: 1,\n                                    value: isStaticRoute ? 'Static' : 'Dynamic',\n                                    onClick: ()=>setOpen(OVERLAYS.Route),\n                                    \"data-nextjs-route-type\": isStaticRoute ? 'static' : 'dynamic'\n                                }),\n                                isTurbopack ? /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    title: \"Turbopack is enabled.\",\n                                    label: \"Turbopack\",\n                                    value: \"Enabled\"\n                                }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                    index: 2,\n                                    title: \"Learn about Turbopack and how to enable it in your application.\",\n                                    label: \"Try Turbopack\",\n                                    value: /*#__PURE__*/ (0, _jsxruntime.jsx)(ChevronRight, {}),\n                                    onClick: ()=>setOpen(OVERLAYS.Turbo)\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            className: \"dev-tools-indicator-footer\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(MenuItem, {\n                                \"data-preferences\": true,\n                                label: \"Preferences\",\n                                value: /*#__PURE__*/ (0, _jsxruntime.jsx)(_gearicon.default, {}),\n                                onClick: ()=>setOpen(OVERLAYS.Preferences),\n                                index: isTurbopack ? 2 : 3\n                            })\n                        })\n                    ]\n                })\n            })\n        ]\n    });\n}\n_c1 = DevToolsPopover;\nfunction ChevronRight() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fill: \"#666\",\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M5.50011 1.93945L6.03044 2.46978L10.8537 7.293C11.2442 7.68353 11.2442 8.31669 10.8537 8.70722L6.03044 13.5304L5.50011 14.0608L4.43945 13.0001L4.96978 12.4698L9.43945 8.00011L4.96978 3.53044L4.43945 3.00011L5.50011 1.93945Z\"\n        })\n    });\n}\n_c2 = ChevronRight;\nfunction MenuItem(param) {\n    let { index, label, value, onClick, href, ...props } = param;\n    const isInteractive = typeof onClick === 'function' || typeof href === 'string';\n    const { closeMenu, selectedIndex, setSelectedIndex } = (0, _react.useContext)(Context);\n    const selected = selectedIndex === index;\n    function click() {\n        if (isInteractive) {\n            onClick == null ? void 0 : onClick();\n            closeMenu();\n            if (href) {\n                window.open(href, '_blank', 'noopener, noreferrer');\n            }\n        }\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        className: \"dev-tools-indicator-item\",\n        \"data-index\": index,\n        \"data-selected\": selected,\n        onClick: click,\n        // Needs `onMouseMove` instead of enter to work together\n        // with keyboard and mouse input\n        onMouseMove: ()=>{\n            if (isInteractive && index !== undefined && selectedIndex !== index) {\n                setSelectedIndex(index);\n            }\n        },\n        onMouseLeave: ()=>setSelectedIndex(-1),\n        onKeyDown: (e)=>{\n            if (e.key === 'Enter' || e.key === ' ') {\n                click();\n            }\n        },\n        role: isInteractive ? 'menuitem' : undefined,\n        tabIndex: selected ? 0 : -1,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: \"dev-tools-indicator-label\",\n                children: label\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: \"dev-tools-indicator-value\",\n                children: value\n            })\n        ]\n    });\n}\n_c3 = MenuItem;\nfunction IssueCount(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"span\", {\n        className: \"dev-tools-indicator-issue-count\",\n        \"data-has-issues\": children > 0,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                className: \"dev-tools-indicator-issue-count-indicator\"\n            }),\n            children\n        ]\n    });\n}\n_c4 = IssueCount;\nconst DEV_TOOLS_INDICATOR_STYLES = \"\\n  .dev-tools-indicator-menu {\\n    -webkit-font-smoothing: antialiased;\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-start;\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background-clip: padding-box;\\n    box-shadow: var(--shadow-menu);\\n    border-radius: var(--rounded-xl);\\n    position: absolute;\\n    font-family: var(--font-stack-sans);\\n    z-index: 1000;\\n    overflow: hidden;\\n    opacity: 0;\\n    outline: 0;\\n    min-width: 248px;\\n    transition: opacity var(--animate-out-duration-ms)\\n      var(--animate-out-timing-function);\\n\\n    &[data-rendered='true'] {\\n      opacity: 1;\\n      scale: 1;\\n    }\\n  }\\n\\n  .dev-tools-indicator-inner {\\n    padding: 6px;\\n    width: 100%;\\n  }\\n\\n  .dev-tools-indicator-item {\\n    display: flex;\\n    align-items: center;\\n    padding: 8px 6px;\\n    height: var(--size-36);\\n    border-radius: 6px;\\n    text-decoration: none !important;\\n    user-select: none;\\n    white-space: nowrap;\\n\\n    svg {\\n      width: var(--size-16);\\n      height: var(--size-16);\\n    }\\n\\n    &:focus-visible {\\n      outline: 0;\\n    }\\n  }\\n\\n  .dev-tools-indicator-footer {\\n    background: var(--color-background-200);\\n    padding: 6px;\\n    border-top: 1px solid var(--color-gray-400);\\n    width: 100%;\\n  }\\n\\n  .dev-tools-indicator-item[data-selected='true'] {\\n    cursor: pointer;\\n    background-color: var(--color-gray-200);\\n  }\\n\\n  .dev-tools-indicator-label {\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    color: var(--color-gray-1000);\\n  }\\n\\n  .dev-tools-indicator-value {\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    color: var(--color-gray-900);\\n    margin-left: auto;\\n  }\\n\\n  .dev-tools-indicator-issue-count {\\n    --color-primary: var(--color-gray-800);\\n    --color-secondary: var(--color-gray-100);\\n    display: flex;\\n    flex-direction: row;\\n    align-items: center;\\n    justify-content: center;\\n    gap: 8px;\\n    min-width: var(--size-40);\\n    height: var(--size-24);\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background-clip: padding-box;\\n    box-shadow: var(--shadow-small);\\n    padding: 2px;\\n    color: var(--color-gray-1000);\\n    border-radius: 128px;\\n    font-weight: 500;\\n    font-size: var(--size-13);\\n    font-variant-numeric: tabular-nums;\\n\\n    &[data-has-issues='true'] {\\n      --color-primary: var(--color-red-800);\\n      --color-secondary: var(--color-red-100);\\n    }\\n\\n    .dev-tools-indicator-issue-count-indicator {\\n      width: var(--size-8);\\n      height: var(--size-8);\\n      background: var(--color-primary);\\n      box-shadow: 0 0 0 2px var(--color-secondary);\\n      border-radius: 50%;\\n    }\\n  }\\n\\n  .dev-tools-indicator-shortcut {\\n    display: flex;\\n    gap: 4px;\\n\\n    kbd {\\n      width: var(--size-20);\\n      height: var(--size-20);\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      border-radius: var(--rounded-md);\\n      border: 1px solid var(--color-gray-400);\\n      font-family: var(--font-stack-sans);\\n      background: var(--color-background-100);\\n      color: var(--color-gray-1000);\\n      text-align: center;\\n      font-size: var(--size-12);\\n      line-height: var(--size-16);\\n    }\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-tools-indicator.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"DevToolsIndicator\");\n$RefreshReg$(_c1, \"DevToolsPopover\");\n$RefreshReg$(_c2, \"ChevronRight\");\n$RefreshReg$(_c3, \"MenuItem\");\n$RefreshReg$(_c4, \"IssueCount\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_STYLES: function() {\n        return DEV_TOOLS_INFO_STYLES;\n    },\n    DevToolsInfo: function() {\n        return DevToolsInfo;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nconst _utils = __webpack_require__(/*! ../utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js\");\nconst _usedelayedrender = __webpack_require__(/*! ../../../../hooks/use-delayed-render */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/hooks/use-delayed-render.js\");\nfunction DevToolsInfo(param) {\n    let { title, children, learnMoreLink, isOpen, triggerRef, close, ...props } = param;\n    const ref = (0, _react.useRef)(null);\n    const closeButtonRef = (0, _react.useRef)(null);\n    const { mounted, rendered } = (0, _usedelayedrender.useDelayedRender)(isOpen, {\n        // Intentionally no fade in, makes the UI feel more immediate\n        enterDelay: 0,\n        // Graceful fade out to confirm that the UI did not break\n        exitDelay: _utils.MENU_DURATION_MS\n    });\n    (0, _utils.useFocusTrap)(ref, triggerRef, isOpen, ()=>{\n        var _closeButtonRef_current;\n        (_closeButtonRef_current = closeButtonRef.current) == null ? void 0 : _closeButtonRef_current.focus();\n    });\n    (0, _utils.useClickOutside)(ref, triggerRef, isOpen, close);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n        tabIndex: -1,\n        role: \"dialog\",\n        ref: ref,\n        \"data-info-popover\": true,\n        ...props,\n        \"data-rendered\": rendered,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            className: \"dev-tools-info-container\",\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                    className: \"dev-tools-info-title\",\n                    children: title\n                }),\n                children,\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"dev-tools-info-button-container\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                            ref: closeButtonRef,\n                            className: \"dev-tools-info-close-button\",\n                            onClick: close,\n                            children: \"Close\"\n                        }),\n                        learnMoreLink && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                            className: \"dev-tools-info-learn-more-button\",\n                            href: learnMoreLink,\n                            target: \"_blank\",\n                            rel: \"noreferrer noopener\",\n                            children: \"Learn More\"\n                        })\n                    ]\n                })\n            ]\n        })\n    });\n}\n_c = DevToolsInfo;\nconst DEV_TOOLS_INFO_STYLES = \"\\n  [data-info-popover] {\\n    -webkit-font-smoothing: antialiased;\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-start;\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background-clip: padding-box;\\n    box-shadow: var(--shadow-menu);\\n    border-radius: var(--rounded-xl);\\n    position: absolute;\\n    font-family: var(--font-stack-sans);\\n    z-index: 1000;\\n    overflow: hidden;\\n    opacity: 0;\\n    outline: 0;\\n    min-width: 350px;\\n    transition: opacity var(--animate-out-duration-ms)\\n      var(--animate-out-timing-function);\\n\\n    &[data-rendered='true'] {\\n      opacity: 1;\\n      scale: 1;\\n    }\\n\\n    button:focus-visible {\\n      outline: var(--focus-ring);\\n    }\\n  }\\n\\n  .dev-tools-info-container {\\n    padding: 12px;\\n  }\\n\\n  .dev-tools-info-title {\\n    padding: 8px 6px;\\n    color: var(--color-gray-1000);\\n    font-size: var(--size-16);\\n    font-weight: 600;\\n    line-height: var(--size-20);\\n    margin: 0;\\n  }\\n\\n  .dev-tools-info-article {\\n    padding: 8px 6px;\\n    color: var(--color-gray-1000);\\n    font-size: var(--size-14);\\n    line-height: var(--size-20);\\n    margin: 0;\\n  }\\n  .dev-tools-info-paragraph {\\n    &:last-child {\\n      margin-bottom: 0;\\n    }\\n  }\\n\\n  .dev-tools-info-button-container {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    padding: 8px 6px;\\n  }\\n\\n  .dev-tools-info-close-button {\\n    padding: 0 8px;\\n    height: var(--size-28);\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    line-height: var(--size-20);\\n    transition: background var(--duration-short) ease;\\n    color: var(--color-gray-1000);\\n    border-radius: var(--rounded-md-2);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background: var(--color-background-200);\\n  }\\n\\n  .dev-tools-info-close-button:hover {\\n    background: var(--color-gray-400);\\n  }\\n\\n  .dev-tools-info-learn-more-button {\\n    align-content: center;\\n    padding: 0 8px;\\n    height: var(--size-28);\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    line-height: var(--size-20);\\n    transition: background var(--duration-short) ease;\\n    color: var(--color-background-100);\\n    border-radius: var(--rounded-md-2);\\n    background: var(--color-gray-1000);\\n  }\\n\\n  .dev-tools-info-learn-more-button:hover {\\n    text-decoration: none;\\n    color: var(--color-background-100);\\n    opacity: 0.9;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dev-tools-info.js.map\nvar _c;\n$RefreshReg$(_c, \"DevToolsInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/preferences.js":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/preferences.js ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    NEXT_DEV_TOOLS_SCALE: function() {\n        return NEXT_DEV_TOOLS_SCALE;\n    },\n    getInitialPosition: function() {\n        return getInitialPosition;\n    },\n    getInitialTheme: function() {\n        return getInitialTheme;\n    },\n    useDevToolsScale: function() {\n        return useDevToolsScale;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nconst _shared = __webpack_require__(/*! ../../../../../shared */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst INDICATOR_POSITION = \"bottom-left\" || 0;\nfunction getInitialPosition() {\n    if (typeof localStorage !== 'undefined' && localStorage.getItem(_shared.STORAGE_KEY_POSITION)) {\n        return localStorage.getItem(_shared.STORAGE_KEY_POSITION);\n    }\n    return INDICATOR_POSITION;\n}\n//////////////////////////////////////////////////////////////////////////////////////\nconst BASE_SIZE = 16;\nconst NEXT_DEV_TOOLS_SCALE = {\n    Small: BASE_SIZE / 14,\n    Medium: BASE_SIZE / 16,\n    Large: BASE_SIZE / 18\n};\nfunction getInitialScale() {\n    if (typeof localStorage !== 'undefined' && localStorage.getItem(_shared.STORAGE_KEY_SCALE)) {\n        return Number(localStorage.getItem(_shared.STORAGE_KEY_SCALE));\n    }\n    return NEXT_DEV_TOOLS_SCALE.Medium;\n}\nfunction useDevToolsScale() {\n    const [scale, setScale] = (0, _react.useState)(getInitialScale());\n    function set(value) {\n        setScale(value);\n        localStorage.setItem(_shared.STORAGE_KEY_SCALE, String(value));\n    }\n    return [\n        scale,\n        set\n    ];\n}\nfunction getInitialTheme() {\n    if (typeof localStorage === 'undefined') {\n        return 'system';\n    }\n    const theme = localStorage.getItem(_shared.STORAGE_KEY_THEME);\n    return theme === 'dark' || theme === 'light' ? theme : 'system';\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=preferences.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/preferences.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js":
/*!*************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js ***!
  \*************************************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_ROUTE_INFO_STYLES: function() {\n        return DEV_TOOLS_INFO_ROUTE_INFO_STYLES;\n    },\n    RouteInfo: function() {\n        return RouteInfo;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _devtoolsinfo = __webpack_require__(/*! ./dev-tools-info */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\");\nfunction StaticRouteContent(param) {\n    let { routerType } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"article\", {\n        className: \"dev-tools-info-article\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"The path\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                        className: \"dev-tools-info-code\",\n                        children: window.location.pathname\n                    }),\n                    ' ',\n                    'is marked as \"static\" since it will be prerendered during the build time.'\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"With Static Rendering, routes are rendered at build time, or in the background after\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: routerType === 'pages' ? 'https://nextjs.org/docs/pages/building-your-application/data-fetching/incremental-static-regeneration' : \"https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"data revalidation\"\n                    }),\n                    \".\"\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: \"Static rendering is useful when a route has data that is not personalized to the user and can be known at build time, such as a static blog post or a product page.\"\n            })\n        ]\n    });\n}\n_c = StaticRouteContent;\nfunction DynamicRouteContent(param) {\n    let { routerType } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"article\", {\n        className: \"dev-tools-info-article\",\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"The path\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                        className: \"dev-tools-info-code\",\n                        children: window.location.pathname\n                    }),\n                    ' ',\n                    'is marked as \"dynamic\" since it will be rendered for each user at',\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"strong\", {\n                        children: \"request time\"\n                    }),\n                    \".\"\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: \"Dynamic rendering is useful when a route has data that is personalized to the user or has information that can only be known at request time, such as cookies or the URL's search params.\"\n            }),\n            routerType === 'pages' ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-pagraph\",\n                children: [\n                    \"Exporting the\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: \"https://nextjs.org/docs/pages/building-your-application/data-fetching/get-server-side-props\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"getServerSideProps\"\n                    }),\n                    ' ',\n                    \"function will opt the route into dynamic rendering. This function will be called by the server on every request.\"\n                ]\n            }) : /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                className: \"dev-tools-info-paragraph\",\n                children: [\n                    \"During rendering, if a\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: \"https://nextjs.org/docs/app/building-your-application/rendering/server-components#dynamic-apis\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"Dynamic API\"\n                    }),\n                    ' ',\n                    \"or a\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n                        className: \"dev-tools-info-link\",\n                        href: \"https://nextjs.org/docs/app/api-reference/functions/fetch\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: \"fetch\"\n                    }),\n                    ' ',\n                    \"option of\",\n                    ' ',\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                        className: \"dev-tools-info-code\",\n                        children: \"{ cache: 'no-store' }\"\n                    }),\n                    ' ',\n                    \"is discovered, Next.js will switch to dynamically rendering the whole route.\"\n                ]\n            })\n        ]\n    });\n}\n_c1 = DynamicRouteContent;\nconst learnMoreLink = {\n    pages: {\n        static: 'https://nextjs.org/docs/pages/building-your-application/rendering/static-site-generation',\n        dynamic: 'https://nextjs.org/docs/pages/building-your-application/rendering/server-side-rendering'\n    },\n    app: {\n        static: 'https://nextjs.org/docs/app/building-your-application/rendering/server-components#static-rendering-default',\n        dynamic: 'https://nextjs.org/docs/app/building-your-application/rendering/server-components#dynamic-rendering'\n    }\n};\nfunction RouteInfo(param) {\n    let { routeType, routerType, ...props } = param;\n    const isStaticRoute = routeType === 'Static';\n    const learnMore = isStaticRoute ? learnMoreLink[routerType].static : learnMoreLink[routerType].dynamic;\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_devtoolsinfo.DevToolsInfo, {\n        title: \"\" + routeType + \" Route\",\n        learnMoreLink: learnMore,\n        ...props,\n        children: isStaticRoute ? /*#__PURE__*/ (0, _jsxruntime.jsx)(StaticRouteContent, {\n            routerType: routerType\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(DynamicRouteContent, {\n            routerType: routerType\n        })\n    });\n}\n_c2 = RouteInfo;\nconst DEV_TOOLS_INFO_ROUTE_INFO_STYLES = \"\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=route-info.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"StaticRouteContent\");\n$RefreshReg$(_c1, \"DynamicRouteContent\");\n$RefreshReg$(_c2, \"RouteInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES: function() {\n        return DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES;\n    },\n    TurbopackInfo: function() {\n        return TurbopackInfo;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _devtoolsinfo = __webpack_require__(/*! ./dev-tools-info */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\");\nconst _copybutton = __webpack_require__(/*! ../../../copy-button */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/copy-button/index.js\");\nfunction TurbopackInfo(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_devtoolsinfo.DevToolsInfo, {\n        title: \"Turbopack\",\n        learnMoreLink: \"https://nextjs.org/docs/app/api-reference/turbopack\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"article\", {\n                className: \"dev-tools-info-article\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        className: \"dev-tools-info-paragraph\",\n                        children: [\n                            \"Turbopack is an incremental bundler optimized for JavaScript and TypeScript, written in Rust, and built into Next.js. Turbopack can be used in Next.js in both the\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                className: \"dev-tools-info-code\",\n                                children: \"pages\"\n                            }),\n                            \" and\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                className: \"dev-tools-info-code\",\n                                children: \"app\"\n                            }),\n                            \" directories for faster local development.\"\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        className: \"dev-tools-info-paragraph\",\n                        children: [\n                            \"To enable Turbopack, use the\",\n                            ' ',\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                className: \"dev-tools-info-code\",\n                                children: \"--turbopack\"\n                            }),\n                            \" flag when running the Next.js development server.\"\n                        ]\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                className: \"dev-tools-info-code-block-container\",\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"dev-tools-info-code-block\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_copybutton.CopyButton, {\n                            actionLabel: \"Copy Next.js Turbopack Command\",\n                            successLabel: \"Next.js Turbopack Command Copied\",\n                            content: '--turbopack',\n                            className: \"dev-tools-info-copy-button\"\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"pre\", {\n                            className: \"dev-tools-info-code-block-pre\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"code\", {\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '  '\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '{'\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '  ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"scripts\"'\n                                            }),\n                                            \": \",\n                                            '{'\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line dev-tools-info-highlight\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"dev\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next dev --turbopack\"'\n                                            }),\n                                            \",\"\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"build\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next build\"'\n                                            }),\n                                            \",\"\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"start\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next start\"'\n                                            }),\n                                            \",\"\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: [\n                                            '    ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-key\",\n                                                children: '\"lint\"'\n                                            }),\n                                            \":\",\n                                            ' ',\n                                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                className: \"dev-tools-info-code-block-json-value\",\n                                                children: '\"next lint\"'\n                                            })\n                                        ]\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '  }'\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '}'\n                                    }),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                        className: \"dev-tools-info-code-block-line\",\n                                        children: '  '\n                                    })\n                                ]\n                            })\n                        })\n                    ]\n                })\n            })\n        ]\n    });\n}\n_c = TurbopackInfo;\nconst DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES = \"\\n  .dev-tools-info-code {\\n    background: var(--color-gray-400);\\n    color: var(--color-gray-1000);\\n    font-family: var(--font-stack-monospace);\\n    padding: 2px 4px;\\n    margin: 0;\\n    font-size: var(--size-13);\\n    white-space: break-spaces;\\n    border-radius: var(--rounded-md-2);\\n  }\\n\\n  .dev-tools-info-code-block-container {\\n    padding: 6px;\\n  }\\n\\n  .dev-tools-info-code-block {\\n    position: relative;\\n    background: var(--color-background-200);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    border-radius: var(--rounded-md-2);\\n    min-width: 326px;\\n  }\\n\\n  .dev-tools-info-code-block-pre {\\n    margin: 0;\\n    font-family: var(--font-stack-monospace);\\n    font-size: var(--size-12);\\n  }\\n\\n  .dev-tools-info-copy-button {\\n    position: absolute;\\n\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    right: 8px;\\n    top: 8px;\\n    padding: 4px;\\n    height: var(--size-24);\\n    width: var(--size-24);\\n    border-radius: var(--rounded-md-2);\\n    border: 1px solid var(--color-gray-alpha-400);\\n    background: var(--color-background-100);\\n  }\\n\\n  .dev-tools-info-code-block-line {\\n    display: block;\\n    line-height: 1.5;\\n    padding: 0 16px;\\n  }\\n\\n  .dev-tools-info-code-block-line.dev-tools-info-highlight {\\n    border-left: 2px solid var(--color-blue-900);\\n    background: var(--color-blue-400);\\n  }\\n\\n  .dev-tools-info-code-block-json-key {\\n    color: var(--color-syntax-keyword);\\n  }\\n\\n  .dev-tools-info-code-block-json-value {\\n    color: var(--color-syntax-link);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=turbopack-info.js.map\nvar _c;\n$RefreshReg$(_c, \"TurbopackInfo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/turbopack-info.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js":
/*!*******************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js ***!
  \*******************************************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DEV_TOOLS_INFO_USER_PREFERENCES_STYLES: function() {\n        return DEV_TOOLS_INFO_USER_PREFERENCES_STYLES;\n    },\n    UserPreferences: function() {\n        return UserPreferences;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nconst _css = __webpack_require__(/*! ../../../../../utils/css */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nconst _eyeicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/eye-icon */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/eye-icon.js\"));\nconst _shared = __webpack_require__(/*! ../../../../../shared */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _lighticon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/light-icon */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/light-icon.js\"));\nconst _darkicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/dark-icon */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/dark-icon.js\"));\nconst _systemicon = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../../icons/system-icon */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/icons/system-icon.js\"));\nconst _devtoolsinfo = __webpack_require__(/*! ./dev-tools-info */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/dev-tools-info.js\");\nconst _preferences = __webpack_require__(/*! ./preferences */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/preferences.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n  .preferences-container {\\n    padding: 8px 6px;\\n    width: 100%;\\n  }\\n\\n  @media (min-width: 576px) {\\n    .preferences-container {\\n      width: 480px;\\n    }\\n  }\\n\\n  .preference-section:first-child {\\n    padding-top: 0;\\n  }\\n\\n  .preference-section {\\n    padding: 12px 0;\\n    border-bottom: 1px solid var(--color-gray-400);\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    gap: 24px;\\n  }\\n\\n  .preference-section:last-child {\\n    border-bottom: none;\\n  }\\n\\n  .preference-header {\\n    margin-bottom: 0;\\n    flex: 1;\\n  }\\n\\n  .preference-header label {\\n    font-size: var(--size-14);\\n    font-weight: 500;\\n    color: var(--color-gray-1000);\\n    margin: 0;\\n  }\\n\\n  .preference-description {\\n    color: var(--color-gray-900);\\n    font-size: var(--size-14);\\n    margin: 0;\\n  }\\n\\n  .select-button,\\n  .action-button {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n    background: var(--color-background-100);\\n    border: 1px solid var(--color-gray-400);\\n    border-radius: var(--rounded-lg);\\n    font-weight: 400;\\n    font-size: var(--size-14);\\n    color: var(--color-gray-1000);\\n    padding: 6px 8px;\\n\\n    &:hover {\\n      background: var(--color-gray-100);\\n    }\\n  }\\n\\n  .select-button {\\n    &:focus-within {\\n      outline: var(--focus-ring);\\n    }\\n\\n    select {\\n      all: unset;\\n    }\\n  }\\n\\n  :global(.icon) {\\n    width: 18px;\\n    height: 18px;\\n    color: #666;\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction UserPreferences(param) {\n    let { setPosition, position, hide, scale, setScale, ...props } = param;\n    // derive initial theme from system preference\n    const [theme, setTheme] = (0, _react.useState)((0, _preferences.getInitialTheme)());\n    const handleThemeChange = (e)=>{\n        const portal = document.querySelector('nextjs-portal');\n        if (!portal) return;\n        setTheme(e.target.value);\n        if (e.target.value === 'system') {\n            portal.classList.remove('dark');\n            portal.classList.remove('light');\n            localStorage.removeItem(_shared.STORAGE_KEY_THEME);\n            return;\n        }\n        if (e.target.value === 'dark') {\n            portal.classList.add('dark');\n            portal.classList.remove('light');\n            localStorage.setItem(_shared.STORAGE_KEY_THEME, 'dark');\n        } else {\n            portal.classList.remove('dark');\n            portal.classList.add('light');\n            localStorage.setItem(_shared.STORAGE_KEY_THEME, 'light');\n        }\n    };\n    function handlePositionChange(e) {\n        setPosition(e.target.value);\n        localStorage.setItem(_shared.STORAGE_KEY_POSITION, e.target.value);\n    }\n    function handleSizeChange(param) {\n        let { target } = param;\n        const value = Number(target.value);\n        setScale(value);\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_devtoolsinfo.DevToolsInfo, {\n        title: \"Preferences\",\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            className: \"preferences-container\",\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"preference-section\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-header\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                    htmlFor: \"theme\",\n                                    children: \"Theme\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    className: \"preference-description\",\n                                    children: \"Select your theme preference.\"\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(Select, {\n                            id: \"theme\",\n                            name: \"theme\",\n                            prefix: /*#__PURE__*/ (0, _jsxruntime.jsx)(ThemeIcon, {\n                                theme: theme\n                            }),\n                            value: theme,\n                            onChange: handleThemeChange,\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                    value: \"system\",\n                                    children: \"System\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                    value: \"light\",\n                                    children: \"Light\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                    value: \"dark\",\n                                    children: \"Dark\"\n                                })\n                            ]\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"preference-section\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-header\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                    htmlFor: \"position\",\n                                    children: \"Position\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    className: \"preference-description\",\n                                    children: \"Adjust the placement of your dev tools.\"\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(Select, {\n                            id: \"position\",\n                            name: \"position\",\n                            value: position,\n                            onChange: handlePositionChange,\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                    value: \"bottom-left\",\n                                    children: \"Bottom Left\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                    value: \"bottom-right\",\n                                    children: \"Bottom Right\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                    value: \"top-left\",\n                                    children: \"Top Left\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                    value: \"top-right\",\n                                    children: \"Top Right\"\n                                })\n                            ]\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"preference-section\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-header\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                    htmlFor: \"size\",\n                                    children: \"Size\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    className: \"preference-description\",\n                                    children: \"Adjust the size of your dev tools.\"\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(Select, {\n                            id: \"size\",\n                            name: \"size\",\n                            value: scale,\n                            onChange: handleSizeChange,\n                            children: Object.entries(_preferences.NEXT_DEV_TOOLS_SCALE).map((param)=>{\n                                let [key, value] = param;\n                                return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"option\", {\n                                    value: value,\n                                    children: key\n                                }, key);\n                            })\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    className: \"preference-section\",\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            className: \"preference-header\",\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                    id: \"hide-dev-tools\",\n                                    children: \"Hide Dev Tools for this session\"\n                                }),\n                                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                                    className: \"preference-description\",\n                                    children: \"Hide Dev Tools until you restart your dev server, or 1 day.\"\n                                })\n                            ]\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            className: \"preference-control\",\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"button\", {\n                                \"aria-describedby\": \"hide-dev-tools\",\n                                name: \"hide-dev-tools\",\n                                \"data-hide-dev-tools\": true,\n                                className: \"action-button\",\n                                onClick: hide,\n                                children: [\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_eyeicon.default, {}),\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                        children: \"Hide\"\n                                    })\n                                ]\n                            })\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                    className: \"preference-section\",\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                        className: \"preference-header\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"label\", {\n                                children: \"Disable Dev Tools for this project\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                                className: \"preference-description\",\n                                children: [\n                                    \"To disable this UI completely, set\",\n                                    ' ',\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                        className: \"dev-tools-info-code\",\n                                        children: \"devIndicators: false\"\n                                    }),\n                                    ' ',\n                                    \"in your \",\n                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"code\", {\n                                        className: \"dev-tools-info-code\",\n                                        children: \"next.config\"\n                                    }),\n                                    ' ',\n                                    \"file.\"\n                                ]\n                            })\n                        ]\n                    })\n                })\n            ]\n        })\n    });\n}\n_c = UserPreferences;\nfunction Select(param) {\n    let { children, prefix, ...props } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        className: \"select-button\",\n        children: [\n            prefix,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"select\", {\n                ...props,\n                children: children\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(ChevronDownIcon, {})\n        ]\n    });\n}\n_c1 = Select;\nfunction ThemeIcon(param) {\n    let { theme } = param;\n    switch(theme){\n        case 'system':\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_systemicon.default, {});\n        case 'dark':\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_darkicon.default, {});\n        case 'light':\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lighticon.default, {});\n        default:\n            return null;\n    }\n}\n_c2 = ThemeIcon;\nconst DEV_TOOLS_INFO_USER_PREFERENCES_STYLES = (0, _css.css)(_templateObject());\nfunction ChevronDownIcon() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        \"aria-hidden\": true,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M14.0607 5.49999L13.5303 6.03032L8.7071 10.8535C8.31658 11.2441 7.68341 11.2441 7.29289 10.8535L2.46966 6.03032L1.93933 5.49999L2.99999 4.43933L3.53032 4.96966L7.99999 9.43933L12.4697 4.96966L13 4.43933L14.0607 5.49999Z\",\n            fill: \"currentColor\"\n        })\n    });\n}\n_c3 = ChevronDownIcon;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=user-preferences.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"UserPreferences\");\n$RefreshReg$(_c1, \"Select\");\n$RefreshReg$(_c2, \"ThemeIcon\");\n$RefreshReg$(_c3, \"ChevronDownIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Cross: function() {\n        return Cross;\n    },\n    NextLogo: function() {\n        return NextLogo;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _tagged_template_literal_loose = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal_loose */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_tagged_template_literal_loose.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nconst _css = __webpack_require__(/*! ../../../../utils/css */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/utils/css.js\");\nconst _mergerefs = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../utils/merge-refs */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/utils/merge-refs.js\"));\nconst _useminimumloadingtimemultiple = __webpack_require__(/*! ./use-minimum-loading-time-multiple */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js\");\nfunction _templateObject() {\n    const data = _tagged_template_literal_loose._([\n        \"\\n          [data-next-badge-root] {\\n            --timing: cubic-bezier(0.23, 0.88, 0.26, 0.92);\\n            --duration-long: 250ms;\\n            --color-outer-border: #171717;\\n            --color-inner-border: hsla(0, 0%, 100%, 0.14);\\n            --color-hover-alpha-subtle: hsla(0, 0%, 100%, 0.13);\\n            --color-hover-alpha-error: hsla(0, 0%, 100%, 0.2);\\n            --color-hover-alpha-error-2: hsla(0, 0%, 100%, 0.25);\\n            --mark-size: calc(var(--size) - var(--size-2) * 2);\\n\\n            --focus-color: var(--color-blue-800);\\n            --focus-ring: 2px solid var(--focus-color);\\n\\n            &:has([data-next-badge][data-error='true']) {\\n              --focus-color: #fff;\\n            }\\n          }\\n\\n          [data-disabled-icon] {\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n            padding-right: 4px;\\n          }\\n\\n          [data-next-badge] {\\n            -webkit-font-smoothing: antialiased;\\n            width: var(--size);\\n            height: var(--size);\\n            display: flex;\\n            align-items: center;\\n            position: relative;\\n            background: rgba(0, 0, 0, 0.8);\\n            box-shadow:\\n              0 0 0 1px var(--color-outer-border),\\n              inset 0 0 0 1px var(--color-inner-border),\\n              0px 16px 32px -8px rgba(0, 0, 0, 0.24);\\n            backdrop-filter: blur(48px);\\n            border-radius: var(--rounded-full);\\n            user-select: none;\\n            cursor: pointer;\\n            scale: 1;\\n            overflow: hidden;\\n            will-change: scale, box-shadow, width, background;\\n            transition:\\n              scale var(--duration-short) var(--timing),\\n              width var(--duration-long) var(--timing),\\n              box-shadow var(--duration-long) var(--timing),\\n              background var(--duration-short) ease;\\n\\n            &:active[data-error='false'] {\\n              scale: 0.95;\\n            }\\n\\n            &[data-animate='true']:not(:hover) {\\n              scale: 1.02;\\n            }\\n\\n            &[data-error='false']:has([data-next-mark]:focus-visible) {\\n              outline: var(--focus-ring);\\n              outline-offset: 3px;\\n            }\\n\\n            &[data-error='true'] {\\n              background: #ca2a30;\\n              --color-inner-border: #e5484d;\\n\\n              [data-next-mark] {\\n                background: var(--color-hover-alpha-error);\\n                outline-offset: 0px;\\n\\n                &:focus-visible {\\n                  outline: var(--focus-ring);\\n                  outline-offset: -1px;\\n                }\\n\\n                &:hover {\\n                  background: var(--color-hover-alpha-error-2);\\n                }\\n              }\\n            }\\n\\n            &[data-error-expanded='false'][data-error='true'] ~ [data-dot] {\\n              scale: 1;\\n            }\\n\\n            > div {\\n              display: flex;\\n            }\\n          }\\n\\n          [data-issues-collapse]:focus-visible {\\n            outline: var(--focus-ring);\\n          }\\n\\n          [data-issues]:has([data-issues-open]:focus-visible) {\\n            outline: var(--focus-ring);\\n            outline-offset: -1px;\\n          }\\n\\n          [data-dot] {\\n            content: '';\\n            width: var(--size-8);\\n            height: var(--size-8);\\n            background: #fff;\\n            box-shadow: 0 0 0 1px var(--color-outer-border);\\n            border-radius: 50%;\\n            position: absolute;\\n            top: 2px;\\n            right: 0px;\\n            scale: 0;\\n            pointer-events: none;\\n            transition: scale 200ms var(--timing);\\n            transition-delay: var(--duration-short);\\n          }\\n\\n          [data-issues] {\\n            --padding-left: 8px;\\n            display: flex;\\n            gap: 2px;\\n            align-items: center;\\n            padding-left: 8px;\\n            padding-right: 8px;\\n            height: var(--size-32);\\n            margin: 0 2px;\\n            border-radius: var(--rounded-full);\\n            transition: background var(--duration-short) ease;\\n\\n            &:has([data-issues-open]:hover) {\\n              background: var(--color-hover-alpha-error);\\n            }\\n\\n            &:has([data-issues-collapse]) {\\n              padding-right: calc(var(--padding-left) / 2);\\n            }\\n\\n            [data-cross] {\\n              translate: 0px -1px;\\n            }\\n          }\\n\\n          [data-issues-open] {\\n            font-size: var(--size-13);\\n            color: white;\\n            width: fit-content;\\n            height: 100%;\\n            display: flex;\\n            gap: 2px;\\n            align-items: center;\\n            margin: 0;\\n            line-height: var(--size-36);\\n            font-weight: 500;\\n            z-index: 2;\\n            white-space: nowrap;\\n\\n            &:focus-visible {\\n              outline: 0;\\n            }\\n          }\\n\\n          [data-issues-collapse] {\\n            width: var(--size-24);\\n            height: var(--size-24);\\n            border-radius: var(--rounded-full);\\n            transition: background var(--duration-short) ease;\\n\\n            &:hover {\\n              background: var(--color-hover-alpha-error);\\n            }\\n          }\\n\\n          [data-cross] {\\n            color: #fff;\\n            width: var(--size-12);\\n            height: var(--size-12);\\n          }\\n\\n          [data-next-mark] {\\n            width: var(--mark-size);\\n            height: var(--mark-size);\\n            margin-left: 2px;\\n            display: flex;\\n            align-items: center;\\n            border-radius: var(--rounded-full);\\n            transition: background var(--duration-long) var(--timing);\\n\\n            &:focus-visible {\\n              outline: 0;\\n            }\\n\\n            &:hover {\\n              background: var(--color-hover-alpha-subtle);\\n            }\\n\\n            svg {\\n              flex-shrink: 0;\\n              width: var(--size-40);\\n              height: var(--size-40);\\n            }\\n          }\\n\\n          [data-issues-count-animation] {\\n            display: grid;\\n            place-items: center center;\\n            font-variant-numeric: tabular-nums;\\n\\n            &[data-animate='false'] {\\n              [data-issues-count-exit],\\n              [data-issues-count-enter] {\\n                animation-duration: 0ms;\\n              }\\n            }\\n\\n            > * {\\n              grid-area: 1 / 1;\\n            }\\n\\n            [data-issues-count-exit] {\\n              animation: fadeOut 300ms var(--timing) forwards;\\n            }\\n\\n            [data-issues-count-enter] {\\n              animation: fadeIn 300ms var(--timing) forwards;\\n            }\\n          }\\n\\n          [data-issues-count-plural] {\\n            display: inline-block;\\n            &[data-animate='true'] {\\n              animation: fadeIn 300ms var(--timing) forwards;\\n            }\\n          }\\n\\n          .path0 {\\n            animation: draw0 1.5s ease-in-out infinite;\\n          }\\n\\n          .path1 {\\n            animation: draw1 1.5s ease-out infinite;\\n            animation-delay: 0.3s;\\n          }\\n\\n          .paused {\\n            stroke-dashoffset: 0;\\n          }\\n\\n          @keyframes fadeIn {\\n            0% {\\n              opacity: 0;\\n              filter: blur(2px);\\n              transform: translateY(8px);\\n            }\\n            100% {\\n              opacity: 1;\\n              filter: blur(0px);\\n              transform: translateY(0);\\n            }\\n          }\\n\\n          @keyframes fadeOut {\\n            0% {\\n              opacity: 1;\\n              filter: blur(0px);\\n              transform: translateY(0);\\n            }\\n            100% {\\n              opacity: 0;\\n              transform: translateY(-12px);\\n              filter: blur(2px);\\n            }\\n          }\\n\\n          @keyframes draw0 {\\n            0%,\\n            25% {\\n              stroke-dashoffset: -29.6;\\n            }\\n            25%,\\n            50% {\\n              stroke-dashoffset: 0;\\n            }\\n            50%,\\n            75% {\\n              stroke-dashoffset: 0;\\n            }\\n            75%,\\n            100% {\\n              stroke-dashoffset: 29.6;\\n            }\\n          }\\n\\n          @keyframes draw1 {\\n            0%,\\n            20% {\\n              stroke-dashoffset: -11.6;\\n            }\\n            20%,\\n            50% {\\n              stroke-dashoffset: 0;\\n            }\\n            50%,\\n            75% {\\n              stroke-dashoffset: 0;\\n            }\\n            75%,\\n            100% {\\n              stroke-dashoffset: 11.6;\\n            }\\n          }\\n\\n          @media (prefers-reduced-motion) {\\n            [data-issues-count-exit],\\n            [data-issues-count-enter],\\n            [data-issues-count-plural] {\\n              animation-duration: 0ms !important;\\n            }\\n          }\\n        \"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nconst SHORT_DURATION_MS = 150;\nconst NextLogo = /*#__PURE__*/ (0, _react.forwardRef)(_s(function NextLogo(param, propRef) {\n    _s();\n    let { disabled, issueCount, isDevBuilding, isDevRendering, isBuildError, onTriggerClick, toggleErrorOverlay, scale = 1, ...props } = param;\n    const SIZE = 36 / scale;\n    const hasError = issueCount > 0;\n    const [isErrorExpanded, setIsErrorExpanded] = (0, _react.useState)(hasError);\n    const [dismissed, setDismissed] = (0, _react.useState)(false);\n    const newErrorDetected = useUpdateAnimation(issueCount, SHORT_DURATION_MS);\n    const triggerRef = (0, _react.useRef)(null);\n    const ref = (0, _react.useRef)(null);\n    const [measuredWidth, pristine] = useMeasureWidth(ref);\n    const isLoading = (0, _useminimumloadingtimemultiple.useMinimumLoadingTimeMultiple)(isDevBuilding || isDevRendering);\n    const isExpanded = isErrorExpanded || disabled;\n    const style = (0, _react.useMemo)(()=>{\n        let width = SIZE;\n        // Animates the badge, if expanded\n        if (measuredWidth > SIZE) width = measuredWidth;\n        // No animations on page load, assume the intrinsic width immediately\n        if (pristine && hasError) width = 'auto';\n        // Default state, collapsed\n        return {\n            width\n        };\n    }, [\n        measuredWidth,\n        pristine,\n        hasError,\n        SIZE\n    ]);\n    (0, _react.useEffect)(()=>{\n        setIsErrorExpanded(hasError);\n    }, [\n        hasError\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        \"data-next-badge-root\": true,\n        style: {\n            '--size': \"\" + SIZE + \"px\",\n            '--duration-short': \"\" + SHORT_DURATION_MS + \"ms\",\n            // if the indicator is disabled, hide the badge\n            // also allow the \"disabled\" state be dismissed, as long as there are no build errors\n            display: disabled && (!hasError || dismissed) ? 'none' : 'block'\n        },\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                children: (0, _css.css)(_templateObject())\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-next-badge\": true,\n                \"data-error\": hasError,\n                \"data-error-expanded\": isExpanded,\n                \"data-animate\": newErrorDetected,\n                style: style,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    ref: ref,\n                    children: [\n                        !disabled && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                            ref: (0, _mergerefs.default)(triggerRef, propRef),\n                            \"data-next-mark\": true,\n                            \"data-next-mark-loading\": isLoading,\n                            onClick: onTriggerClick,\n                            ...props,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(NextMark, {\n                                isLoading: isLoading,\n                                isDevBuilding: isDevBuilding\n                            })\n                        }),\n                        isExpanded && /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                            \"data-issues\": true,\n                            children: [\n                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"button\", {\n                                    \"data-issues-open\": true,\n                                    \"aria-label\": \"Open issues overlay\",\n                                    onClick: toggleErrorOverlay,\n                                    children: [\n                                        disabled && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                                            \"data-disabled-icon\": true,\n                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Warning, {})\n                                        }),\n                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(AnimateCount, {\n                                            animate: newErrorDetected,\n                                            \"data-issues-count-animation\": true,\n                                            children: issueCount\n                                        }, issueCount),\n                                        ' ',\n                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                                            children: [\n                                                \"Issue\",\n                                                issueCount > 1 && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"span\", {\n                                                    \"aria-hidden\": true,\n                                                    \"data-issues-count-plural\": true,\n                                                    // This only needs to animate once the count changes from 1 -> 2,\n                                                    // otherwise it should stay static between re-renders.\n                                                    \"data-animate\": newErrorDetected && issueCount === 2,\n                                                    children: \"s\"\n                                                })\n                                            ]\n                                        })\n                                    ]\n                                }),\n                                !isBuildError && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"button\", {\n                                    \"data-issues-collapse\": true,\n                                    \"aria-label\": \"Collapse issues badge\",\n                                    onClick: ()=>{\n                                        var _triggerRef_current;\n                                        if (disabled) {\n                                            setDismissed(true);\n                                        } else {\n                                            setIsErrorExpanded(false);\n                                        }\n                                        (_triggerRef_current = triggerRef.current) == null ? void 0 : _triggerRef_current.focus();\n                                    },\n                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Cross, {\n                                        \"data-cross\": true\n                                    })\n                                })\n                            ]\n                        })\n                    ]\n                })\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"aria-hidden\": true,\n                \"data-dot\": true\n            })\n        ]\n    });\n}, \"jkUnATqmh1rTMIYx3BahGx7X0Qc=\", false, function() {\n    return [\n        useUpdateAnimation,\n        useMeasureWidth\n    ];\n}));\nfunction AnimateCount(param) {\n    let { children: count, animate = true, ...props } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n        ...props,\n        \"data-animate\": animate,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"aria-hidden\": true,\n                \"data-issues-count-exit\": true,\n                children: count - 1\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                \"data-issues-count\": true,\n                \"data-issues-count-enter\": true,\n                children: count\n            })\n        ]\n    });\n}\n_c = AnimateCount;\nfunction useMeasureWidth(ref) {\n    const [width, setWidth] = (0, _react.useState)(0);\n    const [pristine, setPristine] = (0, _react.useState)(true);\n    (0, _react.useEffect)(()=>{\n        const el = ref.current;\n        if (!el) {\n            return;\n        }\n        const observer = new ResizeObserver(()=>{\n            const { width: w } = el.getBoundingClientRect();\n            setWidth((prevWidth)=>{\n                if (prevWidth !== 0) {\n                    setPristine(false);\n                }\n                return w;\n            });\n        });\n        observer.observe(el);\n        return ()=>observer.disconnect();\n    }, [\n        ref\n    ]);\n    return [\n        width,\n        pristine\n    ];\n}\nfunction useUpdateAnimation(issueCount, animationDurationMs) {\n    if (animationDurationMs === void 0) animationDurationMs = 0;\n    const lastUpdatedTimeStamp = (0, _react.useRef)(null);\n    const [animate, setAnimate] = (0, _react.useState)(false);\n    (0, _react.useEffect)(()=>{\n        if (issueCount > 0) {\n            const deltaMs = lastUpdatedTimeStamp.current ? Date.now() - lastUpdatedTimeStamp.current : -1;\n            lastUpdatedTimeStamp.current = Date.now();\n            // We don't animate if `issueCount` changes too quickly\n            if (deltaMs <= animationDurationMs) {\n                return;\n            }\n            setAnimate(true);\n            // It is important to use a CSS transitioned state, not a CSS keyframed animation\n            // because if the issue count increases faster than the animation duration, it\n            // will abruptly stop and not transition smoothly back to its original state.\n            const timeoutId = window.setTimeout(()=>{\n                setAnimate(false);\n            }, animationDurationMs);\n            return ()=>{\n                clearTimeout(timeoutId);\n            };\n        }\n    }, [\n        issueCount,\n        animationDurationMs\n    ]);\n    return animate;\n}\nfunction NextMark(param) {\n    let { isLoading, isDevBuilding } = param;\n    const strokeColor = isDevBuilding ? 'rgba(255,255,255,0.7)' : 'white';\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"svg\", {\n        width: \"40\",\n        height: \"40\",\n        viewBox: \"0 0 40 40\",\n        fill: \"none\",\n        \"data-next-mark-loading\": isLoading,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"g\", {\n                transform: \"translate(8.5, 13)\",\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        className: isLoading ? 'path0' : 'paused',\n                        d: \"M13.3 15.2 L2.34 1 V12.6\",\n                        fill: \"none\",\n                        stroke: \"url(#next_logo_paint0_linear_1357_10853)\",\n                        strokeWidth: \"1.86\",\n                        mask: \"url(#next_logo_mask0)\",\n                        strokeDasharray: \"29.6\",\n                        strokeDashoffset: \"29.6\"\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n                        className: isLoading ? 'path1' : 'paused',\n                        d: \"M11.825 1.5 V13.1\",\n                        strokeWidth: \"1.86\",\n                        stroke: \"url(#next_logo_paint1_linear_1357_10853)\",\n                        strokeDasharray: \"11.6\",\n                        strokeDashoffset: \"11.6\"\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"defs\", {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"next_logo_paint0_linear_1357_10853\",\n                        x1: \"9.95555\",\n                        y1: \"11.1226\",\n                        x2: \"15.4778\",\n                        y2: \"17.9671\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                stopColor: strokeColor\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"0.604072\",\n                                stopColor: strokeColor,\n                                stopOpacity: \"0\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: strokeColor,\n                                stopOpacity: \"0\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"linearGradient\", {\n                        id: \"next_logo_paint1_linear_1357_10853\",\n                        x1: \"11.8222\",\n                        y1: \"1.40039\",\n                        x2: \"11.791\",\n                        y2: \"9.62542\",\n                        gradientUnits: \"userSpaceOnUse\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                stopColor: strokeColor\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"stop\", {\n                                offset: \"1\",\n                                stopColor: strokeColor,\n                                stopOpacity: \"0\"\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"mask\", {\n                        id: \"next_logo_mask0\",\n                        children: [\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"rect\", {\n                                width: \"100%\",\n                                height: \"100%\",\n                                fill: \"white\"\n                            }),\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"rect\", {\n                                width: \"5\",\n                                height: \"1.5\",\n                                fill: \"black\"\n                            })\n                        ]\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = NextMark;\nfunction Warning() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"12\",\n        height: \"12\",\n        viewBox: \"0 0 12 12\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M3.98071 1.125L1.125 3.98071L1.125 8.01929L3.98071 10.875H8.01929L10.875 8.01929V3.98071L8.01929 1.125H3.98071ZM3.82538 0C3.62647 0 3.4357 0.0790176 3.29505 0.21967L0.21967 3.29505C0.0790176 3.4357 0 3.62647 0 3.82538V8.17462C0 8.37353 0.0790178 8.5643 0.21967 8.70495L3.29505 11.7803C3.4357 11.921 3.62647 12 3.82538 12H8.17462C8.37353 12 8.5643 11.921 8.70495 11.7803L11.7803 8.70495C11.921 8.5643 12 8.37353 12 8.17462V3.82538C12 3.62647 11.921 3.4357 11.7803 3.29505L8.70495 0.21967C8.5643 0.0790177 8.37353 0 8.17462 0H3.82538ZM6.5625 2.8125V3.375V6V6.5625H5.4375V6V3.375V2.8125H6.5625ZM6 9C6.41421 9 6.75 8.66421 6.75 8.25C6.75 7.83579 6.41421 7.5 6 7.5C5.58579 7.5 5.25 7.83579 5.25 8.25C5.25 8.66421 5.58579 9 6 9Z\",\n            fill: \"#EAEAEA\"\n        })\n    });\n}\n_c2 = Warning;\nfunction Cross(props) {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"svg\", {\n        width: \"12\",\n        height: \"12\",\n        viewBox: \"0 0 14 14\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"path\", {\n            fillRule: \"evenodd\",\n            clipRule: \"evenodd\",\n            d: \"M3.08889 11.8384L2.62486 12.3024L1.69678 11.3744L2.16082 10.9103L6.07178 6.99937L2.16082 3.08841L1.69678 2.62437L2.62486 1.69629L3.08889 2.16033L6.99986 6.07129L10.9108 2.16033L11.3749 1.69629L12.3029 2.62437L11.8389 3.08841L7.92793 6.99937L11.8389 10.9103L12.3029 11.3744L11.3749 12.3024L10.9108 11.8384L6.99986 7.92744L3.08889 11.8384Z\",\n            fill: \"currentColor\"\n        })\n    });\n}\n_c3 = Cross;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=next-logo.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"AnimateCount\");\n$RefreshReg$(_c1, \"NextMark\");\n$RefreshReg$(_c2, \"Warning\");\n$RefreshReg$(_c3, \"Cross\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/next-logo.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js":
/*!*********************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js ***!
  \*********************************************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMinimumLoadingTimeMultiple\", ({\n    enumerable: true,\n    get: function() {\n        return useMinimumLoadingTimeMultiple;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nfunction useMinimumLoadingTimeMultiple(isLoadingTrigger, interval) {\n    if (interval === void 0) interval = 750;\n    const [isLoading, setIsLoading] = (0, _react.useState)(false);\n    const loadStartTimeRef = (0, _react.useRef)(null);\n    const timeoutIdRef = (0, _react.useRef)(null);\n    (0, _react.useEffect)(()=>{\n        // Clear any pending timeout to avoid overlap\n        if (timeoutIdRef.current) {\n            clearTimeout(timeoutIdRef.current);\n            timeoutIdRef.current = null;\n        }\n        if (isLoadingTrigger) {\n            // If we enter \"loading\" state, record start time if not already\n            if (loadStartTimeRef.current === null) {\n                loadStartTimeRef.current = Date.now();\n            }\n            setIsLoading(true);\n        } else {\n            // If we're exiting the \"loading\" state:\n            if (loadStartTimeRef.current === null) {\n                // No start time was recorded, so just stop loading immediately\n                setIsLoading(false);\n            } else {\n                // How long we've been \"loading\"\n                const timeDiff = Date.now() - loadStartTimeRef.current;\n                // Next multiple of `interval` after `timeDiff`\n                const nextMultiple = interval * Math.ceil(timeDiff / interval);\n                // Remaining time needed to reach that multiple\n                const remainingTime = nextMultiple - timeDiff;\n                if (remainingTime > 0) {\n                    // If not yet at that multiple, schedule the final step\n                    timeoutIdRef.current = setTimeout(()=>{\n                        setIsLoading(false);\n                        loadStartTimeRef.current = null;\n                    }, remainingTime);\n                } else {\n                    // We're already past the multiple boundary\n                    setIsLoading(false);\n                    loadStartTimeRef.current = null;\n                }\n            }\n        }\n        // Cleanup when effect is about to re-run or component unmounts\n        return ()=>{\n            if (timeoutIdRef.current) {\n                clearTimeout(timeoutIdRef.current);\n            }\n        };\n    }, [\n        isLoadingTrigger,\n        interval\n    ]);\n    return isLoading;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-minimum-loading-time-multiple.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/use-minimum-loading-time-multiple.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js":
/*!*****************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js ***!
  \*****************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    MENU_CURVE: function() {\n        return MENU_CURVE;\n    },\n    MENU_DURATION_MS: function() {\n        return MENU_DURATION_MS;\n    },\n    useClickOutside: function() {\n        return useClickOutside;\n    },\n    useFocusTrap: function() {\n        return useFocusTrap;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nfunction useFocusTrap(rootRef, triggerRef, active, onOpenFocus) {\n    (0, _react.useEffect)(()=>{\n        let rootNode = null;\n        function onTab(e) {\n            if (e.key !== 'Tab' || rootNode === null) {\n                return;\n            }\n            const [firstFocusableNode, lastFocusableNode] = getFocusableNodes(rootNode);\n            const activeElement = getActiveElement(rootNode);\n            if (e.shiftKey) {\n                if (activeElement === firstFocusableNode) {\n                    lastFocusableNode == null ? void 0 : lastFocusableNode.focus();\n                    e.preventDefault();\n                }\n            } else {\n                if (activeElement === lastFocusableNode) {\n                    firstFocusableNode == null ? void 0 : firstFocusableNode.focus();\n                    e.preventDefault();\n                }\n            }\n        }\n        const id = setTimeout(()=>{\n            // Grab this on next tick to ensure the content is mounted\n            rootNode = rootRef.current;\n            if (active) {\n                if (onOpenFocus) {\n                    onOpenFocus();\n                } else {\n                    rootNode == null ? void 0 : rootNode.focus();\n                }\n                rootNode == null ? void 0 : rootNode.addEventListener('keydown', onTab);\n            } else {\n                const activeElement = getActiveElement(rootNode);\n                // Only restore focus if the focus was previously on the content.\n                // This avoids us accidentally focusing on mount when the\n                // user could want to interact with their own app instead.\n                if (triggerRef && (rootNode == null ? void 0 : rootNode.contains(activeElement))) {\n                    var _triggerRef_current;\n                    (_triggerRef_current = triggerRef.current) == null ? void 0 : _triggerRef_current.focus();\n                }\n            }\n        });\n        return ()=>{\n            clearTimeout(id);\n            rootNode == null ? void 0 : rootNode.removeEventListener('keydown', onTab);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        active\n    ]);\n}\nfunction getActiveElement(node) {\n    const root = node == null ? void 0 : node.getRootNode();\n    return root instanceof ShadowRoot ? root == null ? void 0 : root.activeElement : null;\n}\nfunction getFocusableNodes(node) {\n    const focusableElements = node.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n    if (!focusableElements) return [];\n    return [\n        focusableElements[0],\n        focusableElements[focusableElements.length - 1]\n    ];\n}\nfunction useClickOutside(rootRef, triggerRef, active, close) {\n    (0, _react.useEffect)(()=>{\n        if (!active) {\n            return;\n        }\n        function handleClickOutside(event) {\n            var _rootRef_current, _triggerRef_current;\n            if (!(((_rootRef_current = rootRef.current) == null ? void 0 : _rootRef_current.getBoundingClientRect()) ? event.clientX >= rootRef.current.getBoundingClientRect().left && event.clientX <= rootRef.current.getBoundingClientRect().right && event.clientY >= rootRef.current.getBoundingClientRect().top && event.clientY <= rootRef.current.getBoundingClientRect().bottom : false) && !(((_triggerRef_current = triggerRef.current) == null ? void 0 : _triggerRef_current.getBoundingClientRect()) ? event.clientX >= triggerRef.current.getBoundingClientRect().left && event.clientX <= triggerRef.current.getBoundingClientRect().right && event.clientY >= triggerRef.current.getBoundingClientRect().top && event.clientY <= triggerRef.current.getBoundingClientRect().bottom : false)) {\n                close();\n            }\n        }\n        function handleKeyDown(event) {\n            if (event.key === 'Escape') {\n                close();\n            }\n        }\n        document.addEventListener('mousedown', handleClickOutside);\n        document.addEventListener('keydown', handleKeyDown);\n        return ()=>{\n            document.removeEventListener('mousedown', handleClickOutside);\n            document.removeEventListener('keydown', handleKeyDown);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        active\n    ]);\n}\nconst MENU_DURATION_MS = 200;\nconst MENU_CURVE = 'cubic-bezier(0.175, 0.885, 0.32, 1.1)';\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjVfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlYWN0LWRldi1vdmVybGF5L3VpL2NvbXBvbmVudHMvZXJyb3JzL2Rldi10b29scy1pbmRpY2F0b3IvdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0lBcUlhQSxVQUFVO2VBQVZBOztJQURBQyxnQkFBZ0I7ZUFBaEJBOztJQWxER0MsZUFBZTtlQUFmQTs7SUFoRkFDLFlBQVk7ZUFBWkE7OzttQ0FGVTtBQUVuQixTQUFTQSxhQUNkQyxPQUE0QyxFQUM1Q0MsVUFBNEQsRUFDNURDLE1BQWUsRUFDZkMsV0FBd0I7SUFFeEJDLENBQUFBLEdBQUFBLE9BQUFBLFNBQUFBLEVBQVU7UUFDUixJQUFJQyxXQUErQjtRQUVuQyxTQUFTQyxNQUFNQyxDQUFnQjtZQUM3QixJQUFJQSxFQUFFQyxHQUFHLEtBQUssU0FBU0gsYUFBYSxNQUFNO2dCQUN4QztZQUNGO1lBRUEsTUFBTSxDQUFDSSxvQkFBb0JDLGtCQUFrQixHQUMzQ0Msa0JBQWtCTjtZQUNwQixNQUFNTyxnQkFBZ0JDLGlCQUFpQlI7WUFFdkMsSUFBSUUsRUFBRU8sUUFBUSxFQUFFO2dCQUNkLElBQUlGLGtCQUFrQkgsb0JBQW9CO29CQUN4Q0MscUJBQUFBLE9BQUFBLEtBQUFBLElBQUFBLGtCQUFtQkssS0FBSztvQkFDeEJSLEVBQUVTLGNBQWM7Z0JBQ2xCO1lBQ0YsT0FBTztnQkFDTCxJQUFJSixrQkFBa0JGLG1CQUFtQjtvQkFDdkNELHNCQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxtQkFBb0JNLEtBQUs7b0JBQ3pCUixFQUFFUyxjQUFjO2dCQUNsQjtZQUNGO1FBQ0Y7UUFFQSxNQUFNQyxLQUFLQyxXQUFXO1lBQ3BCLDBEQUEwRDtZQUMxRGIsV0FBV0wsUUFBUW1CLE9BQU87WUFDMUIsSUFBSWpCLFFBQVE7Z0JBQ1YsSUFBSUMsYUFBYTtvQkFDZkE7Z0JBQ0YsT0FBTztvQkFDTEUsWUFBQUEsT0FBQUEsS0FBQUEsSUFBQUEsU0FBVVUsS0FBSztnQkFDakI7Z0JBQ0FWLFlBQUFBLE9BQUFBLEtBQUFBLElBQUFBLFNBQVVlLGdCQUFnQixDQUFDLFdBQVdkO1lBQ3hDLE9BQU87Z0JBQ0wsTUFBTU0sZ0JBQWdCQyxpQkFBaUJSO2dCQUN2QyxpRUFBaUU7Z0JBQ2pFLHlEQUF5RDtnQkFDekQsMERBQTBEO2dCQUMxRCxJQUFJSixlQUFjSSxZQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxTQUFVZ0IsUUFBUSxDQUFDVCxjQUFBQSxHQUFnQjt3QkFDbkRYO29CQUFBQSx1QkFBQUEsV0FBV2tCLE9BQUFBLEtBQU8sZ0JBQWxCbEIsb0JBQW9CYyxLQUFLO2dCQUMzQjtZQUNGO1FBQ0Y7UUFFQSxPQUFPO1lBQ0xPLGFBQWFMO1lBQ2JaLFlBQUFBLE9BQUFBLEtBQUFBLElBQUFBLFNBQVVrQixtQkFBbUIsQ0FBQyxXQUFXakI7UUFDM0M7SUFDQSx1REFBdUQ7SUFDekQsR0FBRztRQUFDSjtLQUFPO0FBQ2I7QUFFQSxTQUFTVyxpQkFBaUJXLElBQXdCO0lBQ2hELE1BQU1DLE9BQU9ELFFBQUFBLE9BQUFBLEtBQUFBLElBQUFBLEtBQU1FLFdBQVc7SUFDOUIsT0FBT0QsZ0JBQWdCRSxhQUNsQkYsUUFBQUEsT0FBQUEsS0FBQUEsSUFBQUEsS0FBTWIsYUFBYSxHQUNwQjtBQUNOO0FBRUEsU0FBU0Qsa0JBQWtCYSxJQUFpQjtJQUMxQyxNQUFNSSxvQkFBb0JKLEtBQUtLLGdCQUFnQixDQUM3QztJQUVGLElBQUksQ0FBQ0QsbUJBQW1CLE9BQU8sRUFBRTtJQUNqQyxPQUFPO1FBQ0xBLGlCQUFrQixDQUFDLEVBQUU7UUFDckJBLGlCQUFrQixDQUFDQSxrQkFBbUJFLE1BQU0sR0FBRyxFQUFFO0tBQ2xEO0FBQ0g7QUFJTyxTQUFTaEMsZ0JBQ2RFLE9BQTRDLEVBQzVDQyxVQUFxRCxFQUNyREMsTUFBZSxFQUNmNkIsS0FBaUI7SUFFakIzQixDQUFBQSxHQUFBQSxPQUFBQSxTQUFBQSxFQUFVO1FBQ1IsSUFBSSxDQUFDRixRQUFRO1lBQ1g7UUFDRjtRQUVBLFNBQVM4QixtQkFBbUJDLEtBQWlCO2dCQUV2Q2pDLGtCQU1BQztZQVBKLElBQ0UsQ0FBRUQsQ0FBQUEsQ0FBQUEsQ0FBQUEsbUJBQUFBLFFBQVFtQixPQUFBQSxLQUFPLGdCQUFmbkIsaUJBQWlCa0MscUJBQXFCLE1BQ3BDRCxNQUFNRSxPQUFPLElBQUluQyxRQUFRbUIsT0FBTyxDQUFDZSxxQkFBcUIsR0FBSUUsSUFBSSxJQUM5REgsTUFBTUUsT0FBTyxJQUFJbkMsUUFBUW1CLE9BQU8sQ0FBQ2UscUJBQXFCLEdBQUlHLEtBQUssSUFDL0RKLE1BQU1LLE9BQU8sSUFBSXRDLFFBQVFtQixPQUFPLENBQUNlLHFCQUFxQixHQUFJSyxHQUFHLElBQzdETixNQUFNSyxPQUFPLElBQUl0QyxRQUFRbUIsT0FBTyxDQUFDZSxxQkFBcUIsR0FBSU0sTUFBTSxHQUNoRSxNQUFJLElBQ1IsQ0FBRXZDLENBQUFBLENBQUFBLENBQUFBLHNCQUFBQSxXQUFXa0IsT0FBQUEsS0FBTyxnQkFBbEJsQixvQkFBb0JpQyxxQkFBcUIsTUFDdkNELE1BQU1FLE9BQU8sSUFBSWxDLFdBQVdrQixPQUFPLENBQUNlLHFCQUFxQixHQUFJRSxJQUFJLElBQ2pFSCxNQUFNRSxPQUFPLElBQ1hsQyxXQUFXa0IsT0FBTyxDQUFDZSxxQkFBcUIsR0FBSUcsS0FBSyxJQUNuREosTUFBTUssT0FBTyxJQUFJckMsV0FBV2tCLE9BQU8sQ0FBQ2UscUJBQXFCLEdBQUlLLEdBQUcsSUFDaEVOLE1BQU1LLE9BQU8sSUFBSXJDLFdBQVdrQixPQUFPLENBQUNlLHFCQUFxQixHQUFJTSxNQUFNLEdBQ25FLE1BQUksRUFDUjtnQkFDQVQ7WUFDRjtRQUNGO1FBRUEsU0FBU1UsY0FBY1IsS0FBb0I7WUFDekMsSUFBSUEsTUFBTXpCLEdBQUcsS0FBSyxVQUFVO2dCQUMxQnVCO1lBQ0Y7UUFDRjtRQUVBVyxTQUFTdEIsZ0JBQWdCLENBQUMsYUFBYVk7UUFDdkNVLFNBQVN0QixnQkFBZ0IsQ0FBQyxXQUFXcUI7UUFFckMsT0FBTztZQUNMQyxTQUFTbkIsbUJBQW1CLENBQUMsYUFBYVM7WUFDMUNVLFNBQVNuQixtQkFBbUIsQ0FBQyxXQUFXa0I7UUFDMUM7SUFDQSx1REFBdUQ7SUFDekQsR0FBRztRQUFDdkM7S0FBTztBQUNiO0FBSU8sTUFBTUwsbUJBQW1CO0FBQ3pCLE1BQU1ELGFBQWEiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXGNsaWVudFxcY29tcG9uZW50c1xccmVhY3QtZGV2LW92ZXJsYXlcXHVpXFxjb21wb25lbnRzXFxlcnJvcnNcXGRldi10b29scy1pbmRpY2F0b3JcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuXG5leHBvcnQgZnVuY3Rpb24gdXNlRm9jdXNUcmFwKFxuICByb290UmVmOiBSZWFjdC5SZWZPYmplY3Q8SFRNTEVsZW1lbnQgfCBudWxsPixcbiAgdHJpZ2dlclJlZjogUmVhY3QuUmVmT2JqZWN0PEhUTUxCdXR0b25FbGVtZW50IHwgbnVsbD4gfCBudWxsLFxuICBhY3RpdmU6IGJvb2xlYW4sXG4gIG9uT3BlbkZvY3VzPzogKCkgPT4gdm9pZFxuKSB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbGV0IHJvb3ROb2RlOiBIVE1MRWxlbWVudCB8IG51bGwgPSBudWxsXG5cbiAgICBmdW5jdGlvbiBvblRhYihlOiBLZXlib2FyZEV2ZW50KSB7XG4gICAgICBpZiAoZS5rZXkgIT09ICdUYWInIHx8IHJvb3ROb2RlID09PSBudWxsKSB7XG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICBjb25zdCBbZmlyc3RGb2N1c2FibGVOb2RlLCBsYXN0Rm9jdXNhYmxlTm9kZV0gPVxuICAgICAgICBnZXRGb2N1c2FibGVOb2Rlcyhyb290Tm9kZSlcbiAgICAgIGNvbnN0IGFjdGl2ZUVsZW1lbnQgPSBnZXRBY3RpdmVFbGVtZW50KHJvb3ROb2RlKVxuXG4gICAgICBpZiAoZS5zaGlmdEtleSkge1xuICAgICAgICBpZiAoYWN0aXZlRWxlbWVudCA9PT0gZmlyc3RGb2N1c2FibGVOb2RlKSB7XG4gICAgICAgICAgbGFzdEZvY3VzYWJsZU5vZGU/LmZvY3VzKClcbiAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWYgKGFjdGl2ZUVsZW1lbnQgPT09IGxhc3RGb2N1c2FibGVOb2RlKSB7XG4gICAgICAgICAgZmlyc3RGb2N1c2FibGVOb2RlPy5mb2N1cygpXG4gICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBpZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgLy8gR3JhYiB0aGlzIG9uIG5leHQgdGljayB0byBlbnN1cmUgdGhlIGNvbnRlbnQgaXMgbW91bnRlZFxuICAgICAgcm9vdE5vZGUgPSByb290UmVmLmN1cnJlbnRcbiAgICAgIGlmIChhY3RpdmUpIHtcbiAgICAgICAgaWYgKG9uT3BlbkZvY3VzKSB7XG4gICAgICAgICAgb25PcGVuRm9jdXMoKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJvb3ROb2RlPy5mb2N1cygpXG4gICAgICAgIH1cbiAgICAgICAgcm9vdE5vZGU/LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBvblRhYilcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGFjdGl2ZUVsZW1lbnQgPSBnZXRBY3RpdmVFbGVtZW50KHJvb3ROb2RlKVxuICAgICAgICAvLyBPbmx5IHJlc3RvcmUgZm9jdXMgaWYgdGhlIGZvY3VzIHdhcyBwcmV2aW91c2x5IG9uIHRoZSBjb250ZW50LlxuICAgICAgICAvLyBUaGlzIGF2b2lkcyB1cyBhY2NpZGVudGFsbHkgZm9jdXNpbmcgb24gbW91bnQgd2hlbiB0aGVcbiAgICAgICAgLy8gdXNlciBjb3VsZCB3YW50IHRvIGludGVyYWN0IHdpdGggdGhlaXIgb3duIGFwcCBpbnN0ZWFkLlxuICAgICAgICBpZiAodHJpZ2dlclJlZiAmJiByb290Tm9kZT8uY29udGFpbnMoYWN0aXZlRWxlbWVudCkpIHtcbiAgICAgICAgICB0cmlnZ2VyUmVmLmN1cnJlbnQ/LmZvY3VzKClcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgY2xlYXJUaW1lb3V0KGlkKVxuICAgICAgcm9vdE5vZGU/LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBvblRhYilcbiAgICB9XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICB9LCBbYWN0aXZlXSlcbn1cblxuZnVuY3Rpb24gZ2V0QWN0aXZlRWxlbWVudChub2RlOiBIVE1MRWxlbWVudCB8IG51bGwpIHtcbiAgY29uc3Qgcm9vdCA9IG5vZGU/LmdldFJvb3ROb2RlKClcbiAgcmV0dXJuIHJvb3QgaW5zdGFuY2VvZiBTaGFkb3dSb290XG4gICAgPyAocm9vdD8uYWN0aXZlRWxlbWVudCBhcyBIVE1MRWxlbWVudClcbiAgICA6IG51bGxcbn1cblxuZnVuY3Rpb24gZ2V0Rm9jdXNhYmxlTm9kZXMobm9kZTogSFRNTEVsZW1lbnQpOiBbSFRNTEVsZW1lbnQsIEhUTUxFbGVtZW50XSB8IFtdIHtcbiAgY29uc3QgZm9jdXNhYmxlRWxlbWVudHMgPSBub2RlLnF1ZXJ5U2VsZWN0b3JBbGwoXG4gICAgJ2J1dHRvbiwgW2hyZWZdLCBpbnB1dCwgc2VsZWN0LCB0ZXh0YXJlYSwgW3RhYmluZGV4XTpub3QoW3RhYmluZGV4PVwiLTFcIl0pJ1xuICApXG4gIGlmICghZm9jdXNhYmxlRWxlbWVudHMpIHJldHVybiBbXVxuICByZXR1cm4gW1xuICAgIGZvY3VzYWJsZUVsZW1lbnRzIVswXSBhcyBIVE1MRWxlbWVudCxcbiAgICBmb2N1c2FibGVFbGVtZW50cyFbZm9jdXNhYmxlRWxlbWVudHMhLmxlbmd0aCAtIDFdIGFzIEhUTUxFbGVtZW50LFxuICBdXG59XG5cbi8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VDbGlja091dHNpZGUoXG4gIHJvb3RSZWY6IFJlYWN0LlJlZk9iamVjdDxIVE1MRWxlbWVudCB8IG51bGw+LFxuICB0cmlnZ2VyUmVmOiBSZWFjdC5SZWZPYmplY3Q8SFRNTEJ1dHRvbkVsZW1lbnQgfCBudWxsPixcbiAgYWN0aXZlOiBib29sZWFuLFxuICBjbG9zZTogKCkgPT4gdm9pZFxuKSB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFhY3RpdmUpIHtcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGZ1bmN0aW9uIGhhbmRsZUNsaWNrT3V0c2lkZShldmVudDogTW91c2VFdmVudCkge1xuICAgICAgaWYgKFxuICAgICAgICAhKHJvb3RSZWYuY3VycmVudD8uZ2V0Qm91bmRpbmdDbGllbnRSZWN0KClcbiAgICAgICAgICA/IGV2ZW50LmNsaWVudFggPj0gcm9vdFJlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpIS5sZWZ0ICYmXG4gICAgICAgICAgICBldmVudC5jbGllbnRYIDw9IHJvb3RSZWYuY3VycmVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKSEucmlnaHQgJiZcbiAgICAgICAgICAgIGV2ZW50LmNsaWVudFkgPj0gcm9vdFJlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpIS50b3AgJiZcbiAgICAgICAgICAgIGV2ZW50LmNsaWVudFkgPD0gcm9vdFJlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpIS5ib3R0b21cbiAgICAgICAgICA6IGZhbHNlKSAmJlxuICAgICAgICAhKHRyaWdnZXJSZWYuY3VycmVudD8uZ2V0Qm91bmRpbmdDbGllbnRSZWN0KClcbiAgICAgICAgICA/IGV2ZW50LmNsaWVudFggPj0gdHJpZ2dlclJlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpIS5sZWZ0ICYmXG4gICAgICAgICAgICBldmVudC5jbGllbnRYIDw9XG4gICAgICAgICAgICAgIHRyaWdnZXJSZWYuY3VycmVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKSEucmlnaHQgJiZcbiAgICAgICAgICAgIGV2ZW50LmNsaWVudFkgPj0gdHJpZ2dlclJlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpIS50b3AgJiZcbiAgICAgICAgICAgIGV2ZW50LmNsaWVudFkgPD0gdHJpZ2dlclJlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpIS5ib3R0b21cbiAgICAgICAgICA6IGZhbHNlKVxuICAgICAgKSB7XG4gICAgICAgIGNsb3NlKClcbiAgICAgIH1cbiAgICB9XG5cbiAgICBmdW5jdGlvbiBoYW5kbGVLZXlEb3duKGV2ZW50OiBLZXlib2FyZEV2ZW50KSB7XG4gICAgICBpZiAoZXZlbnQua2V5ID09PSAnRXNjYXBlJykge1xuICAgICAgICBjbG9zZSgpXG4gICAgICB9XG4gICAgfVxuXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKVxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlEb3duKVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSlcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlEb3duKVxuICAgIH1cbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gIH0sIFthY3RpdmVdKVxufVxuXG4vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xuXG5leHBvcnQgY29uc3QgTUVOVV9EVVJBVElPTl9NUyA9IDIwMFxuZXhwb3J0IGNvbnN0IE1FTlVfQ1VSVkUgPSAnY3ViaWMtYmV6aWVyKDAuMTc1LCAwLjg4NSwgMC4zMiwgMS4xKSdcbiJdLCJuYW1lcyI6WyJNRU5VX0NVUlZFIiwiTUVOVV9EVVJBVElPTl9NUyIsInVzZUNsaWNrT3V0c2lkZSIsInVzZUZvY3VzVHJhcCIsInJvb3RSZWYiLCJ0cmlnZ2VyUmVmIiwiYWN0aXZlIiwib25PcGVuRm9jdXMiLCJ1c2VFZmZlY3QiLCJyb290Tm9kZSIsIm9uVGFiIiwiZSIsImtleSIsImZpcnN0Rm9jdXNhYmxlTm9kZSIsImxhc3RGb2N1c2FibGVOb2RlIiwiZ2V0Rm9jdXNhYmxlTm9kZXMiLCJhY3RpdmVFbGVtZW50IiwiZ2V0QWN0aXZlRWxlbWVudCIsInNoaWZ0S2V5IiwiZm9jdXMiLCJwcmV2ZW50RGVmYXVsdCIsImlkIiwic2V0VGltZW91dCIsImN1cnJlbnQiLCJhZGRFdmVudExpc3RlbmVyIiwiY29udGFpbnMiLCJjbGVhclRpbWVvdXQiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwibm9kZSIsInJvb3QiLCJnZXRSb290Tm9kZSIsIlNoYWRvd1Jvb3QiLCJmb2N1c2FibGVFbGVtZW50cyIsInF1ZXJ5U2VsZWN0b3JBbGwiLCJsZW5ndGgiLCJjbG9zZSIsImhhbmRsZUNsaWNrT3V0c2lkZSIsImV2ZW50IiwiZ2V0Qm91bmRpbmdDbGllbnRSZWN0IiwiY2xpZW50WCIsImxlZnQiLCJyaWdodCIsImNsaWVudFkiLCJ0b3AiLCJib3R0b20iLCJoYW5kbGVLZXlEb3duIiwiZG9jdW1lbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/utils.js\n"));

/***/ })

}]);