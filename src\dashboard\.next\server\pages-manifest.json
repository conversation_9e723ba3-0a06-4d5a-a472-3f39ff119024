{"/_app": "pages/_app.js", "/_document": "pages/_document.js", "/admin/addons": "pages/admin/addons.html", "/_error": "pages/_error.js", "/admin/commands": "pages/admin/commands.html", "/admin/experimental/addon-builder": "pages/admin/experimental/addon-builder.html", "/admin/applications-builder": "pages/admin/applications-builder.html", "/api/admin/addons/[name]": "pages/api/admin/addons/[name].js", "/api/admin/addons/[name]/config": "pages/api/admin/addons/[name]/config.js", "/api/admin/addons/[name]/flow": "pages/api/admin/addons/[name]/flow.js", "/api/admin/addons": "pages/api/admin/addons.js", "/api/admin/addons/reload": "pages/api/admin/addons/reload.js", "/api/admin/applications-builder/[id]": "pages/api/admin/applications-builder/[id].js", "/api/admin/applications-builder": "pages/api/admin/applications-builder.js", "/api/admin/applications": "pages/api/admin/applications.js", "/api/admin/commands": "pages/api/admin/commands.js", "/api/admin/commands/refresh": "pages/api/admin/commands/refresh.js", "/api/admin/errors/[id]": "pages/api/admin/errors/[id].js", "/api/admin/errors": "pages/api/admin/errors.js", "/api/admin/errors/clear": "pages/api/admin/errors/clear.js", "/api/admin/experimental/addon-builder/guild-data": "pages/api/admin/experimental/addon-builder/guild-data.js", "/api/admin/experimental/addon-builder/templates": "pages/api/admin/experimental/addon-builder/templates.js", "/api/analytics/bot": "pages/api/analytics/bot.js", "/api/analytics/server": "pages/api/analytics/server.js", "/api/applications/config": "pages/api/applications/config.js", "/api/applications/moderation": "pages/api/applications/moderation.js", "/api/auth/permissions": "pages/api/auth/permissions.js", "/api/database/collections": "pages/api/database/collections.js", "/api/database/documents": "pages/api/database/documents.js", "/api/database/logs": "pages/api/database/logs.js", "/api/database/middleware": "pages/api/database/middleware.js", "/api/database/stats": "pages/api/database/stats.js", "/api/discord/audit-logs": "pages/api/discord/audit-logs.js", "/api/discord/channels": "pages/api/discord/channels.js", "/api/discord/channels/[channelId]": "pages/api/discord/channels/[channelId].js", "/api/discord/channels/[channelId]/messages": "pages/api/discord/channels/[channelId]/messages.js", "/api/discord/channels/bulk-delete": "pages/api/discord/channels/bulk-delete.js", "/api/discord/guild": "pages/api/discord/guild.js", "/api/discord/roles/[roleId]": "pages/api/discord/roles/[roleId].js", "/api/discord/roles/bulk-delete": "pages/api/discord/roles/bulk-delete.js", "/api/discord/roles/easter-egg": "pages/api/discord/roles/easter-egg.js", "/api/discord/presence": "pages/api/discord/presence.js", "/api/discord/roles": "pages/api/discord/roles.js", "/api/discord/settings": "pages/api/discord/settings.js", "/api/discord/tickets/[id]": "pages/api/discord/tickets/[id].js", "/api/discord/tickets/[id]/transcript": "pages/api/discord/tickets/[id]/transcript.js", "/api/discord/tickets": "pages/api/discord/tickets.js", "/api/discord/user/experimental": "pages/api/discord/user/experimental.js", "/api/discord/users/ban": "pages/api/discord/users/ban.js", "/api/discord/users": "pages/api/discord/users.js", "/api/discord/users/kick": "pages/api/discord/users/kick.js", "/api/discord/users/role": "pages/api/discord/users/role.js", "/api/discord/users/untimeout": "pages/api/discord/users/untimeout.js", "/api/discord/users/timeout": "pages/api/discord/users/timeout.js", "/api/experimental/addon-builder/templates": "pages/api/experimental/addon-builder/templates.js", "/api/experimental/flags/[feature]": "pages/api/experimental/flags/[feature].js", "/api/experimental/flags": "pages/api/experimental/flags.js", "/api/gameservers/games": "pages/api/gameservers/games.js", "/api/gameservers/manage": "pages/api/gameservers/manage.js", "/api/gameservers/query": "pages/api/gameservers/query.js", "/api/notifications": "pages/api/notifications.js", "/signin": "pages/signin.html", "/unauthorized": "pages/unauthorized.html", "/api/admin/experimental/addon-builder/build": "pages/api/admin/experimental/addon-builder/build.js", "/admin/guilds": "pages/admin/guilds.js", "/api/auth/[...nextauth]": "pages/api/auth/[...nextauth].js", "/admin/applications": "pages/admin/applications.js", "/experimental/addon-builder": "pages/experimental/addon-builder.js", "/experimental": "pages/experimental.js", "/": "pages/index.js", "/gameservers": "pages/gameservers.js", "/api/experimental/addon-builder/create": "pages/api/experimental/addon-builder/create.js", "/tickets": "pages/tickets.js", "/applications": "pages/applications.js", "/overview": "pages/overview.js", "/404": "pages/404.html"}