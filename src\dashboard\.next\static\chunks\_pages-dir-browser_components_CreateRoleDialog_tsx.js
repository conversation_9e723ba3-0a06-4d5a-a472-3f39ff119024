"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_components_CreateRoleDialog_tsx"],{

/***/ "(pages-dir-browser)/./components/CreateRoleDialog.tsx":
/*!*****************************************!*\
  !*** ./components/CreateRoleDialog.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateRoleDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Checkbox,Divider,FormControl,FormLabel,HStack,Icon,Input,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,SimpleGrid,Text,VStack,useToast!=!@chakra-ui/react */ \"(pages-dir-browser)/__barrel_optimize__?names=Box,Button,Checkbox,Divider,FormControl,FormLabel,HStack,Icon,Input,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,SimpleGrid,Text,VStack,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!react-icons/fi */ \"(pages-dir-browser)/__barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n// @ts-nocheck\n\nvar _s = $RefreshSig$();\n\n\n\nconst PERMISSION_GROUPS = {\n    General: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiShield,\n        permissions: [\n            'ADMINISTRATOR',\n            'VIEW_AUDIT_LOG',\n            'MANAGE_GUILD',\n            'MANAGE_ROLES',\n            'MANAGE_CHANNELS',\n            'MANAGE_EMOJIS_AND_STICKERS',\n            'MANAGE_WEBHOOKS',\n            'VIEW_CHANNEL'\n        ]\n    },\n    Text: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiMessageSquare,\n        permissions: [\n            'SEND_MESSAGES',\n            'EMBED_LINKS',\n            'ATTACH_FILES',\n            'ADD_REACTIONS',\n            'USE_EXTERNAL_EMOJIS',\n            'MENTION_EVERYONE',\n            'MANAGE_MESSAGES',\n            'READ_MESSAGE_HISTORY'\n        ]\n    },\n    Voice: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiVolume2,\n        permissions: [\n            'CONNECT',\n            'SPEAK',\n            'STREAM',\n            'USE_VAD',\n            'PRIORITY_SPEAKER',\n            'MUTE_MEMBERS',\n            'DEAFEN_MEMBERS',\n            'MOVE_MEMBERS'\n        ]\n    },\n    Members: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiUsers,\n        permissions: [\n            'KICK_MEMBERS',\n            'BAN_MEMBERS',\n            'CHANGE_NICKNAME',\n            'MANAGE_NICKNAMES',\n            'CREATE_INSTANT_INVITE'\n        ]\n    }\n};\nfunction CreateRoleDialog(param) {\n    let { isOpen, onClose, onSuccess } = param;\n    _s();\n    const toast = (0,_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        color: '#99AAB5',\n        permissions: [],\n        hoist: false,\n        mentionable: false\n    });\n    const handleSubmit = async ()=>{\n        if (!formData.name.trim()) {\n            toast({\n                title: 'Error',\n                description: 'Role name is required',\n                status: 'error',\n                duration: 3000\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/discord/roles', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.message || 'Failed to create role');\n            }\n            toast({\n                title: 'Success',\n                description: 'Role created successfully',\n                status: 'success',\n                duration: 3000\n            });\n            // Reset form\n            setFormData({\n                name: '',\n                color: '#99AAB5',\n                permissions: [],\n                hoist: false,\n                mentionable: false\n            });\n            onSuccess();\n            onClose();\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to create role',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePermissionChange = (permission)=>{\n        setFormData((prev)=>({\n                ...prev,\n                permissions: prev.permissions.includes(permission) ? prev.permissions.filter((p)=>p !== permission) : [\n                    ...prev.permissions,\n                    permission\n                ]\n            }));\n    };\n    const handleChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"xl\",\n        scrollBehavior: \"inside\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalOverlay, {\n                backdropFilter: \"blur(10px)\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalContent, {\n                bg: \"gray.800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalHeader, {\n                        children: \"Create New Role\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalCloseButton, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalBody, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                            spacing: 6,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                    isRequired: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                            children: \"Role Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            placeholder: \"Enter role name\",\n                                            value: formData.name,\n                                            onChange: (e)=>handleChange('name', e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                            children: \"Role Color\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            type: \"color\",\n                                            value: formData.color,\n                                            onChange: (e)=>handleChange('color', e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                        spacing: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                isChecked: formData.hoist,\n                                                onChange: (e)=>handleChange('hoist', e.target.checked),\n                                                children: \"Display role separately\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                isChecked: formData.mentionable,\n                                                onChange: (e)=>handleChange('mentionable', e.target.checked),\n                                                children: \"Allow anyone to @mention\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Divider, {}, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    fontSize: \"lg\",\n                                    fontWeight: \"bold\",\n                                    alignSelf: \"flex-start\",\n                                    children: \"Permissions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                Object.entries(PERMISSION_GROUPS).map((param)=>{\n                                    let [groupName, group] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                        w: \"full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                mb: 2,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                                        as: group.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                        fontWeight: \"semibold\",\n                                                        children: groupName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.SimpleGrid, {\n                                                columns: 2,\n                                                spacing: 2,\n                                                children: group.permissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                        isChecked: formData.permissions.includes(permission),\n                                                        onChange: ()=>handlePermissionChange(permission),\n                                                        children: permission.split('_').map((word)=>word.charAt(0) + word.slice(1).toLowerCase()).join(' ')\n                                                    }, permission, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, groupName, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalFooter, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                mr: 3,\n                                onClick: onClose,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                colorScheme: \"blue\",\n                                onClick: handleSubmit,\n                                isLoading: isLoading,\n                                children: \"Create Role\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\CreateRoleDialog.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateRoleDialog, \"b11Y34giKxIaIh/nQ2Fqb5hy/K0=\", false, function() {\n    return [\n        _barrel_optimize_names_Box_Button_Checkbox_Divider_FormControl_FormLabel_HStack_Icon_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_SimpleGrid_Text_VStack_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = CreateRoleDialog;\nvar _c;\n$RefreshReg$(_c, \"CreateRoleDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/CreateRoleDialog.tsx\n"));

/***/ })

}]);