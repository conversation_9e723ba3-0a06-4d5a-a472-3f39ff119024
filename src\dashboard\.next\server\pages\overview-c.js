"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/overview-c";
exports.ids = ["pages/overview-c"];
exports.modules = {

/***/ "(pages-dir-node)/./config/cards.ts":
/*!*************************!*\
  !*** ./config/cards.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CARD_CONFIGS: () => (/* binding */ CARD_CONFIGS)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=FiActivity,FiHelpCircle,FiLock,FiMonitor,FiPackage,FiSettings!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiActivity,FiHelpCircle,FiLock,FiMonitor,FiPackage,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n\nconst CARD_CONFIGS = [\n    {\n        id: 'overview',\n        title: 'Overview',\n        description: 'View server statistics and general information.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiActivity,\n        href: '/overview',\n        color: 'blue',\n        gradient: {\n            from: 'rgba(49, 130, 206, 0.4)',\n            to: 'rgba(49, 130, 206, 0.1)'\n        },\n        accentColor: '#63B3ED'\n    },\n    {\n        id: 'gameservers',\n        title: 'Game Servers',\n        description: 'Manage and monitor your game servers. View status, add or edit server configurations.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiMonitor,\n        href: '/gameservers',\n        color: 'green',\n        gradient: {\n            from: 'rgba(72, 187, 120, 0.4)',\n            to: 'rgba(72, 187, 120, 0.1)'\n        },\n        accentColor: '#68D391'\n    },\n    {\n        id: 'applications',\n        title: 'Applications',\n        description: 'Review and manage guild applications. Process new members and handle requests.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiPackage,\n        href: '/applications',\n        color: 'purple',\n        gradient: {\n            from: 'rgba(159, 122, 234, 0.4)',\n            to: 'rgba(159, 122, 234, 0.1)'\n        },\n        accentColor: '#B794F4'\n    },\n    {\n        id: 'tickets',\n        title: 'Support Tickets',\n        description: 'Track and manage support tickets. Respond to user inquiries and resolve issues.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiHelpCircle,\n        href: '/tickets',\n        color: 'orange',\n        gradient: {\n            from: 'rgba(237, 137, 54, 0.4)',\n            to: 'rgba(237, 137, 54, 0.1)'\n        },\n        accentColor: '#F6AD55'\n    },\n    {\n        id: 'moderation',\n        title: 'Moderation',\n        description: 'Tools and features for server moderators.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiLock,\n        href: '/moderation',\n        color: 'teal',\n        gradient: {\n            from: 'rgba(49, 151, 149, 0.4)',\n            to: 'rgba(49, 151, 149, 0.1)'\n        },\n        accentColor: '#4FD1C5',\n        requiredRole: 'moderator'\n    },\n    {\n        id: 'experimental',\n        title: 'Experimental Features',\n        description: 'Try out new features that are still in development. These may not work as expected.',\n        icon: _barrel_optimize_names_FiActivity_FiHelpCircle_FiLock_FiMonitor_FiPackage_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_0__.FiSettings,\n        href: '#',\n        color: 'yellow',\n        gradient: {\n            from: 'rgba(236, 201, 75, 0.4)',\n            to: 'rgba(236, 201, 75, 0.1)'\n        },\n        accentColor: '#F6E05E',\n        experimental: true,\n        disabled: true\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./config/cards.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./core/config.ts":
/*!************************!*\
  !*** ./core/config.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dashboardConfig: () => (/* binding */ dashboardConfig),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n// @ts-nocheck\n\n\n\nlet config = {};\ntry {\n    // Locate config.yml by walking up directories (dashboard may run from nested cwd)\n    const possible = [\n        'config.yml',\n        '../config.yml',\n        '../../config.yml',\n        '../../../config.yml',\n        '../../../../config.yml'\n    ].map((rel)=>path__WEBPACK_IMPORTED_MODULE_2__.resolve(process.cwd(), rel));\n    let configPath = possible.find((p)=>fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(p));\n    if (!configPath) {\n        // fallback relative to file location\n        const dirBased = path__WEBPACK_IMPORTED_MODULE_2__.resolve(__dirname, '../../../config.yml');\n        if (fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(dirBased)) configPath = dirBased;\n    }\n    if (!configPath) {\n        throw new Error('config.yml not found');\n    }\n    const fileContents = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(configPath, 'utf8');\n    config = yaml__WEBPACK_IMPORTED_MODULE_1___default().parse(fileContents);\n} catch (error) {\n    console.error('Error: Failed to load config.yml:', error);\n    process.exit(1); // Exit if we can't load the config\n}\n// Export the dashboard-specific config\nconst dashboardConfig = {\n    bot: {\n        token: config.bot.token,\n        clientId: config.bot.clientId,\n        clientSecret: config.bot.clientSecret,\n        guildId: config.bot.guildId,\n        ticketCategoryId: config.bot.ticketCategoryId || null,\n        ticketLogChannelId: config.bot.ticketLogChannelId || null,\n        prefix: config.bot.prefix\n    },\n    dashboard: {\n        admins: config.dashboard?.admins || [],\n        adminRoleIds: config.dashboard?.adminRoleIds || [],\n        session: {\n            secret: config.dashboard?.session?.secret || config.bot.clientSecret\n        }\n    },\n    database: {\n        url: config.database.url,\n        name: config.database.name,\n        options: {\n            maxPoolSize: config.database.options?.maxPoolSize || 10,\n            minPoolSize: config.database.options?.minPoolSize || 1,\n            maxIdleTimeMS: config.database.options?.maxIdleTimeMS || 30000,\n            serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,\n            socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,\n            connectTimeoutMS: config.database.options?.connectTimeoutMS || 10000,\n            retryWrites: config.database.options?.retryWrites !== false,\n            retryReads: config.database.options?.retryReads !== false\n        }\n    }\n};\n// Validate required configuration\nif (!dashboardConfig.bot.token) {\n    console.error('Error: Discord bot token is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.clientId || !dashboardConfig.bot.clientSecret) {\n    console.error('Error: Discord OAuth2 credentials (clientId and clientSecret) are required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.guildId) {\n    console.error('Error: Guild ID is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.database.url || !dashboardConfig.database.name) {\n    console.error('Error: Database configuration (url and name) is required in config.yml');\n    process.exit(1);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dashboardConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./core/config.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useExperimentalFeatures.ts":
/*!******************************************!*\
  !*** ./hooks/useExperimentalFeatures.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useExperimentalFeatures)\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction useExperimentalFeatures() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.useSession)();\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reason, setReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useExperimentalFeatures.useEffect\": ()=>{\n            async function checkAccess() {\n                if (!session?.user) {\n                    setHasAccess(false);\n                    setReason('unauthenticated');\n                    setIsLoading(false);\n                    return;\n                }\n                try {\n                    const response = await fetch('/api/discord/user/experimental');\n                    const data = await response.json();\n                    setHasAccess(data.hasAccess);\n                    setReason(data.reason);\n                } catch (error) {\n                    console.error('Error checking experimental features access:', error);\n                    setHasAccess(false);\n                    setReason('error');\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n            checkAccess();\n        }\n    }[\"useExperimentalFeatures.useEffect\"], [\n        session\n    ]);\n    return {\n        hasAccess,\n        reason,\n        isLoading,\n        isDeveloper: reason === 'developer',\n        isTester: reason === 'tester'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useExperimentalFeatures.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useGuildInfo.ts":
/*!*******************************!*\
  !*** ./hooks/useGuildInfo.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useGuildInfo)\n/* harmony export */ });\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swr */ \"swr\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swr__WEBPACK_IMPORTED_MODULE_0__]);\nswr__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst fetcher = async (url)=>{\n    const res = await fetch(url);\n    if (!res.ok) {\n        if (res.status === 401) {\n            // Return default data for unauthorized state\n            return {\n                name: '404 Bot',\n                botName: '404 Bot'\n            };\n        }\n        throw new Error('Failed to fetch guild info');\n    }\n    return res.json();\n};\nfunction useGuildInfo() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    // Only fetch if we're authenticated\n    const shouldFetch = status === 'authenticated';\n    const { data, error } = (0,swr__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(shouldFetch ? '/api/discord/guild' : null, fetcher, {\n        revalidateOnFocus: false,\n        revalidateOnReconnect: false\n    });\n    // Local preference state (guild vs bot)\n    const [pref, setPref] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"useGuildInfo.useState\": ()=>{\n            if (true) return 'guild';\n            return localStorage.getItem('dashboardDisplayNamePref') || 'guild';\n        }\n    }[\"useGuildInfo.useState\"]);\n    // Function to update preference and broadcast change\n    const updatePreference = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useGuildInfo.useCallback[updatePreference]\": (newPref)=>{\n            setPref(newPref);\n            if (false) {}\n        }\n    }[\"useGuildInfo.useCallback[updatePreference]\"], []);\n    // Listen for preference changes in this tab (custom event) or other tabs (storage event)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useGuildInfo.useEffect\": ()=>{\n            if (true) return;\n            const handleCustom = {\n                \"useGuildInfo.useEffect.handleCustom\": (e)=>{\n                    if (e?.detail) setPref(e.detail);\n                }\n            }[\"useGuildInfo.useEffect.handleCustom\"];\n            const handleStorage = {\n                \"useGuildInfo.useEffect.handleStorage\": (e)=>{\n                    if (e.key === 'dashboardDisplayNamePref') {\n                        setPref(e.newValue || 'guild');\n                    }\n                }\n            }[\"useGuildInfo.useEffect.handleStorage\"];\n            window.addEventListener('displayNamePrefChanged', handleCustom);\n            window.addEventListener('storage', handleStorage);\n            return ({\n                \"useGuildInfo.useEffect\": ()=>{\n                    window.removeEventListener('displayNamePrefChanged', handleCustom);\n                    window.removeEventListener('storage', handleStorage);\n                }\n            })[\"useGuildInfo.useEffect\"];\n        }\n    }[\"useGuildInfo.useEffect\"], []);\n    // Default display name when not authenticated\n    const defaultName = '404 Bot Dashboard';\n    // Determine displayName\n    let displayName = defaultName;\n    if (data) {\n        if (pref === 'bot' && data.botName) {\n            displayName = data.botName;\n        } else {\n            displayName = data.name || defaultName;\n        }\n    }\n    return {\n        guild: data,\n        displayName,\n        pref,\n        updatePreference,\n        isLoading: shouldFetch && !error && !data,\n        isError: !!error\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useGuildInfo.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/discord */ \"next-auth/providers/discord\");\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(pages-dir-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nconst authOptions = {\n    providers: [\n        next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId,\n            clientSecret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n            authorization: {\n                params: {\n                    scope: 'identify email guilds'\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, account, profile }) {\n            if (account && profile) {\n                token.accessToken = account.access_token || null;\n                token.id = profile.id || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                // Ensure we have valid values for serialization\n                const userId = token.id || null;\n                const accessToken = token.accessToken || null;\n                // Attach Discord user ID to session\n                session.user.id = userId;\n                session.user.accessToken = accessToken;\n                // Default to false for admin status\n                let isAdmin = false;\n                if (userId) {\n                    console.log('Checking admin status for user:', userId);\n                    // Check explicit admin IDs\n                    const adminIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.admins || [];\n                    console.log('Configured admin IDs:', adminIds);\n                    if (adminIds.includes(userId)) {\n                        console.log('User is in admin list');\n                        isAdmin = true;\n                    } else {\n                        console.log('User not in admin list, checking roles...');\n                        // Check roles if configured\n                        const roleIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.adminRoleIds || [];\n                        console.log('Configured admin role IDs:', roleIds);\n                        if (roleIds.length && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId) {\n                            try {\n                                console.log('Fetching member roles from Discord API...');\n                                console.log('Bot token (first 20 chars):', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token.substring(0, 20) + '...');\n                                console.log('Guild ID:', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId);\n                                console.log('User ID:', userId);\n                                console.log('Admin role IDs to check:', roleIds);\n                                const res = await fetch(`https://discord.com/api/v10/guilds/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId}/members/${userId}`, {\n                                    headers: {\n                                        Authorization: `Bot ${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token}`\n                                    }\n                                });\n                                console.log('Discord API response status:', res.status);\n                                if (res.ok) {\n                                    const member = await res.json();\n                                    console.log('Member data:', JSON.stringify(member, null, 2));\n                                    console.log('Member roles:', member.roles);\n                                    isAdmin = roleIds.some((rid)=>member.roles?.includes(rid)) || false;\n                                    console.log('Has admin role:', isAdmin);\n                                } else {\n                                    const errorText = await res.text();\n                                    console.error('Failed to fetch member - Status:', res.status);\n                                    console.error('Error response:', errorText);\n                                }\n                            } catch (error) {\n                                console.error('Failed to fetch guild member:', error);\n                            }\n                        } else {\n                            console.log('No role IDs configured or missing bot token/guild ID');\n                        }\n                    }\n                } else {\n                    console.log('No user ID available');\n                }\n                // Set admin status\n                session.user.isAdmin = isAdmin;\n                console.log('Final admin status:', isAdmin);\n                // Ensure all session values are serializable\n                session.user = {\n                    ...session.user,\n                    id: session.user.id || null,\n                    accessToken: session.user.accessToken || null,\n                    isAdmin: session.user.isAdmin || false,\n                    name: session.user.name || null,\n                    email: session.user.email || null,\n                    image: session.user.image || null\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Dynamically derive equivalent localhost URL (same protocol & port)\n            const parsed = new URL(baseUrl);\n            const localhostBase = `${parsed.protocol}//localhost${parsed.port ? `:${parsed.port}` : ''}`;\n            if (url.startsWith(baseUrl) || url.startsWith(localhostBase)) {\n                return url;\n            }\n            return baseUrl;\n        }\n    },\n    secret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.session.secret || _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n    pages: {\n        signIn: '/signin'\n    },\n    // Always show auth errors, but only debug logs in development\n    debug: \"development\" === 'development',\n    logger: {\n        error: (code, metadata)=>{\n            console.error('[NextAuth Error]', code, metadata);\n        },\n        warn: (code)=>{\n            if (true) {\n                console.warn('[NextAuth Warn]', code);\n            }\n        },\n        debug: (code, metadata)=>{\n            if (true) {\n                console.debug('[NextAuth Debug]', code, metadata);\n            }\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/overview.tsx":
/*!****************************!*\
  !*** ./pages/overview.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Overview),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardBody,HStack,Heading,Icon,SimpleGrid,Skeleton,Stat,StatHelpText,StatLabel,StatNumber,Text,VStack,Wrap,WrapItem,useToast!=!@chakra-ui/react */ \"(pages-dir-node)/__barrel_optimize__?names=Box,Card,CardBody,HStack,Heading,Icon,SimpleGrid,Skeleton,Stat,StatHelpText,StatLabel,StatNumber,Text,VStack,Wrap,WrapItem,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=FiActivity,FiAlertCircle,FiMessageSquare,FiServer,FiTrendingUp,FiUsers!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiActivity,FiAlertCircle,FiMessageSquare,FiServer,FiTrendingUp,FiUsers!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-node)/./components/Layout.tsx\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _api_auth_nextauth___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./api/auth/[...nextauth] */ \"(pages-dir-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _config_cards__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../config/cards */ \"(pages-dir-node)/./config/cards.ts\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @emotion/react */ \"@emotion/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(pages-dir-node)/__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_OverviewCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/OverviewCard */ \"(pages-dir-node)/./components/OverviewCard.tsx\");\n/* harmony import */ var _hooks_useExperimentalFeatures__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../hooks/useExperimentalFeatures */ \"(pages-dir-node)/./hooks/useExperimentalFeatures.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _barrel_optimize_names_Link_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Link!=!@chakra-ui/react */ \"(pages-dir-node)/__barrel_optimize__?names=Link!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Layout__WEBPACK_IMPORTED_MODULE_1__, _emotion_react__WEBPACK_IMPORTED_MODULE_5__, _components_OverviewCard__WEBPACK_IMPORTED_MODULE_8__, _barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__, _barrel_optimize_names_Link_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__]);\n([_components_Layout__WEBPACK_IMPORTED_MODULE_1__, _emotion_react__WEBPACK_IMPORTED_MODULE_5__, _components_OverviewCard__WEBPACK_IMPORTED_MODULE_8__, _barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__, _barrel_optimize_names_Link_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// @ts-nocheck\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Hidden message animation\nconst glowKeyframes = (0,_emotion_react__WEBPACK_IMPORTED_MODULE_5__.keyframes)`\n  0% { opacity: 0.3; }\n  50% { opacity: 0.7; }\n  100% { opacity: 0.3; }\n`;\nfunction Overview() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_7__.useSession)();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(true);\n    const toast = (0,_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    // Experimental feature access state\n    const { hasAccess: experimentalAccess, isDeveloper: isExperimentalDeveloper, isLoading: isExperimentalLoading } = (0,_hooks_useExperimentalFeatures__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"Overview.useEffect\": ()=>{\n            const fetchAnalytics = {\n                \"Overview.useEffect.fetchAnalytics\": async ()=>{\n                    try {\n                        const [serverRes, botRes] = await Promise.all([\n                            fetch('/api/analytics/server'),\n                            fetch('/api/analytics/bot')\n                        ]);\n                        if (!serverRes.ok || !botRes.ok) {\n                            throw new Error('Failed to fetch analytics');\n                        }\n                        const [serverData, botData] = await Promise.all([\n                            serverRes.json(),\n                            botRes.json()\n                        ]);\n                        setAnalyticsData({\n                            serverStats: serverData.serverStats,\n                            botStats: botData.botStats\n                        });\n                    } catch (error) {\n                        console.error('Error fetching analytics:', error);\n                        toast({\n                            title: 'Error',\n                            description: 'Failed to load analytics data',\n                            status: 'error',\n                            duration: 5000\n                        });\n                        // Fallback to mock data\n                        setAnalyticsData({\n                            serverStats: {\n                                totalMembers: 0,\n                                onlineMembers: 0,\n                                totalChannels: 0,\n                                totalRoles: 0\n                            },\n                            botStats: {\n                                commandsToday: 0,\n                                uptime: 'Unknown',\n                                responseTime: '0ms',\n                                activeAddons: 0,\n                                inactiveAddons: 0\n                            }\n                        });\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Overview.useEffect.fetchAnalytics\"];\n            fetchAnalytics();\n        }\n    }[\"Overview.useEffect\"], [\n        toast\n    ]);\n    const quotes = [\n        '\"Talk is cheap. Show me the code.\" – Linus Torvalds',\n        '\"Programs must be written for people to read, and only incidentally for machines to execute.\" – Harold Abelson',\n        '\"Any fool can write code that a computer can understand. Good programmers write code that humans can understand.\" – Martin Fowler',\n        '\"First, solve the problem. Then, write the code.\" – John Johnson',\n        '\"404 Chill Not Found? Keep calm and debug on.\" – Unknown',\n        \"It's not a bug – it's an undocumented feature.\",\n        '\"The best error message is the one that never shows up.\" – Thomas Fuchs',\n        \"Code is like humor. When you have to explain it, it's bad.\",\n        '\"Experience is the name everyone gives to their mistakes.\" – Oscar Wilde',\n        '\"In order to be irreplaceable, one must always be different.\" – Coco Chanel'\n    ];\n    // Use a stable quote selection based on the day of the month\n    const getQuoteOfTheDay = ()=>{\n        const today = new Date();\n        const dayOfMonth = today.getDate(); // 1-31\n        return quotes[dayOfMonth % quotes.length];\n    };\n    const quoteOfTheDay = getQuoteOfTheDay();\n    // Filter cards based on user role (excluding experimental features)\n    const filteredCards = _config_cards__WEBPACK_IMPORTED_MODULE_4__.CARD_CONFIGS.filter((card)=>{\n        if (card.requiredRole === 'admin') {\n            return session?.user?.isAdmin;\n        }\n        if (card.requiredRole === 'moderator') {\n            return session?.user?.isModerator;\n        }\n        // Exclude both overview and experimental cards\n        return card.id !== 'overview' && card.id !== 'experimental';\n    });\n    // Chart data\n    const channelDistribution = analyticsData ? [\n        {\n            name: 'Text',\n            value: analyticsData.serverStats.textChannels || 0,\n            color: '#4299E1'\n        },\n        {\n            name: 'Voice',\n            value: analyticsData.serverStats.voiceChannels || 0,\n            color: '#48BB78'\n        },\n        {\n            name: 'Categories',\n            value: analyticsData.serverStats.categories || 0,\n            color: '#9F7AEA'\n        }\n    ] : [];\n    const orderedDays = [\n        'Mon',\n        'Tue',\n        'Wed',\n        'Thu',\n        'Fri',\n        'Sat',\n        'Sun'\n    ];\n    const defaultWeekly = orderedDays.map((day)=>({\n            day,\n            commands: 0,\n            joins: 0,\n            leaves: 0\n        }));\n    const weeklyActivity = analyticsData ? orderedDays.map((day)=>{\n        const botEntry = analyticsData.botStats?.weeklyActivity?.find((e)=>e.day === day) || {};\n        const memberEntry = analyticsData.serverStats?.weeklyMembers?.find((e)=>e.day === day) || {};\n        return {\n            day,\n            commands: botEntry.commands || 0,\n            joins: memberEntry.joins || 0,\n            leaves: memberEntry.leaves || 0\n        };\n    }) : defaultWeekly;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Box, {\n            p: 8,\n            position: \"relative\",\n            _before: {\n                content: '\"\"',\n                position: 'absolute',\n                top: '50%',\n                left: '50%',\n                transform: 'translate(-50%, -50%)',\n                width: '100%',\n                height: '100%',\n                background: 'radial-gradient(circle at center, rgba(66, 153, 225, 0.1) 0%, transparent 70%)',\n                pointerEvents: 'none'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Box, {\n                    position: \"absolute\",\n                    top: 0,\n                    left: 0,\n                    width: \"100%\",\n                    height: \"100%\",\n                    pointerEvents: \"none\",\n                    opacity: 0.05,\n                    sx: {\n                        '@keyframes glow': {\n                            '0%': {\n                                opacity: 0.03\n                            },\n                            '50%': {\n                                opacity: 0.07\n                            },\n                            '100%': {\n                                opacity: 0.03\n                            }\n                        },\n                        animation: 'glow 4s infinite'\n                    },\n                    fontSize: \"3xl\",\n                    fontFamily: \"monospace\",\n                    color: \"blue.200\",\n                    textAlign: \"center\",\n                    pt: 20,\n                    children: \"ORACLE\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.VStack, {\n                    spacing: 8,\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Heading, {\n                            size: \"lg\",\n                            textAlign: \"center\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        marginRight: '0.5rem'\n                                    },\n                                    role: \"img\",\n                                    \"aria-label\": \"chart\",\n                                    children: \"\\uD83D\\uDCCA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Box, {\n                                    as: \"span\",\n                                    bgGradient: \"linear(to-r, blue.300, purple.400)\",\n                                    bgClip: \"text\",\n                                    children: \"Server Analytics\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.SimpleGrid, {\n                            columns: {\n                                base: 1,\n                                md: 2,\n                                lg: 4\n                            },\n                            spacing: 6,\n                            w: \"full\",\n                            children: isLoading ? // Loading skeletons\n                            Array.from({\n                                length: 4\n                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                    bg: \"whiteAlpha.100\",\n                                    backdropFilter: \"blur(10px)\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.CardBody, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Skeleton, {\n                                            height: \"80px\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 19\n                                    }, this)\n                                }, i, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                        bg: \"whiteAlpha.100\",\n                                        backdropFilter: \"blur(10px)\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.CardBody, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Stat, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.HStack, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Icon, {\n                                                                as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_12__.FiUsers,\n                                                                color: \"blue.400\",\n                                                                boxSize: 6\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatLabel, {\n                                                                color: \"gray.300\",\n                                                                children: \"Total Members\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatNumber, {\n                                                        color: \"white\",\n                                                        fontSize: \"2xl\",\n                                                        children: analyticsData?.serverStats.totalMembers.toLocaleString() || '0'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatHelpText, {\n                                                        color: \"green.400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Icon, {\n                                                                as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_12__.FiTrendingUp,\n                                                                mr: 1\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            analyticsData?.serverStats.onlineMembers || '0',\n                                                            \" online\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatHelpText, {\n                                                        color: \"green.300\",\n                                                        children: [\n                                                            \"+\",\n                                                            analyticsData?.serverStats.newMembersToday || 0,\n                                                            \" joined\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatHelpText, {\n                                                        color: \"red.400\",\n                                                        children: [\n                                                            \"-\",\n                                                            analyticsData?.serverStats.leftMembersToday || 0,\n                                                            \" left\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                        bg: \"whiteAlpha.100\",\n                                        backdropFilter: \"blur(10px)\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.CardBody, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Stat, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.HStack, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Icon, {\n                                                                as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_12__.FiMessageSquare,\n                                                                color: \"green.400\",\n                                                                boxSize: 6\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatLabel, {\n                                                                color: \"gray.300\",\n                                                                children: \"Channels\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatNumber, {\n                                                        color: \"white\",\n                                                        fontSize: \"2xl\",\n                                                        children: analyticsData?.serverStats.totalChannels || '0'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatHelpText, {\n                                                        color: \"gray.400\",\n                                                        children: [\n                                                            analyticsData?.serverStats.totalRoles || '0',\n                                                            \" roles\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                        bg: \"whiteAlpha.100\",\n                                        backdropFilter: \"blur(10px)\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.CardBody, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Stat, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.HStack, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Icon, {\n                                                                as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_12__.FiActivity,\n                                                                color: \"purple.400\",\n                                                                boxSize: 6\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatLabel, {\n                                                                color: \"gray.300\",\n                                                                children: \"Commands Today\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatNumber, {\n                                                        color: \"white\",\n                                                        fontSize: \"2xl\",\n                                                        children: analyticsData?.botStats.commandsToday || '0'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatHelpText, {\n                                                        color: \"gray.400\",\n                                                        children: [\n                                                            analyticsData?.botStats.responseTime || '0ms',\n                                                            \" avg response\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                        bg: \"whiteAlpha.100\",\n                                        backdropFilter: \"blur(10px)\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.CardBody, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Stat, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.HStack, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Icon, {\n                                                                as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_12__.FiServer,\n                                                                color: \"orange.400\",\n                                                                boxSize: 6\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatLabel, {\n                                                                color: \"gray.300\",\n                                                                children: \"Bot Uptime\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatNumber, {\n                                                        color: \"white\",\n                                                        fontSize: \"xl\",\n                                                        children: analyticsData?.botStats.uptime || 'Unknown'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatHelpText, {\n                                                        color: \"green.400\",\n                                                        children: [\n                                                            analyticsData?.botStats.activeAddons || '0',\n                                                            \" addons active\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatHelpText, {\n                                                        color: \"red.400\",\n                                                        children: [\n                                                            analyticsData?.botStats.inactiveAddons || '0',\n                                                            \" addons inactive\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this),\n                                    analyticsData?.botStats.errorsToday > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Link_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Link, {\n                                        as: (next_link__WEBPACK_IMPORTED_MODULE_10___default()),\n                                        href: \"/admin/errors\",\n                                        _hover: {\n                                            textDecoration: 'none'\n                                        },\n                                        w: \"full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                            bg: \"whiteAlpha.100\",\n                                            backdropFilter: \"blur(10px)\",\n                                            borderColor: \"red.400\",\n                                            borderWidth: \"1px\",\n                                            cursor: \"pointer\",\n                                            _hover: {\n                                                transform: 'translateY(-4px)',\n                                                boxShadow: '0 4px 12px rgba(0,0,0,0.2)',\n                                                borderColor: 'red.500'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.CardBody, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Stat, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.HStack, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Icon, {\n                                                                    as: _barrel_optimize_names_FiActivity_FiAlertCircle_FiMessageSquare_FiServer_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_12__.FiAlertCircle,\n                                                                    color: \"red.400\",\n                                                                    boxSize: 6\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatLabel, {\n                                                                    color: \"gray.300\",\n                                                                    children: \"Errors Today\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatNumber, {\n                                                            color: \"red.400\",\n                                                            fontSize: \"2xl\",\n                                                            children: analyticsData.botStats.errorsToday\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.StatHelpText, {\n                                                            color: \"red.300\",\n                                                            children: \"Needs attention\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this),\n                !isLoading && analyticsData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.VStack, {\n                    spacing: 8,\n                    mb: 8,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Heading, {\n                            size: \"lg\",\n                            textAlign: \"center\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        marginRight: '0.5rem'\n                                    },\n                                    role: \"img\",\n                                    \"aria-label\": \"graph\",\n                                    children: \"\\uD83D\\uDCC8\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Box, {\n                                    as: \"span\",\n                                    bgGradient: \"linear(to-r, blue.300, purple.400)\",\n                                    bgClip: \"text\",\n                                    children: \"Activity Overview\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.SimpleGrid, {\n                            columns: {\n                                base: 1,\n                                lg: 2\n                            },\n                            spacing: 8,\n                            w: \"full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                    bg: \"whiteAlpha.100\",\n                                    backdropFilter: \"blur(10px)\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.CardBody, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.VStack, {\n                                            spacing: 4,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Heading, {\n                                                    size: \"md\",\n                                                    color: \"white\",\n                                                    children: \"Channel Distribution\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Box, {\n                                                    h: \"200px\",\n                                                    w: \"full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.ResponsiveContainer, {\n                                                        width: \"100%\",\n                                                        height: \"100%\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.PieChart, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Pie, {\n                                                                    data: channelDistribution,\n                                                                    cx: \"50%\",\n                                                                    cy: \"50%\",\n                                                                    innerRadius: 40,\n                                                                    outerRadius: 80,\n                                                                    paddingAngle: 5,\n                                                                    dataKey: \"value\",\n                                                                    children: channelDistribution.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Cell, {\n                                                                            fill: entry.color\n                                                                        }, `cell-${index}`, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Tooltip, {\n                                                                    wrapperStyle: {\n                                                                        backgroundColor: 'transparent'\n                                                                    },\n                                                                    contentStyle: {\n                                                                        backgroundColor: 'rgba(26, 32, 44, 0.9)',\n                                                                        border: '1px solid rgba(255,255,255,0.2)',\n                                                                        borderRadius: '8px',\n                                                                        color: '#fff'\n                                                                    },\n                                                                    itemStyle: {\n                                                                        color: '#fff'\n                                                                    },\n                                                                    labelStyle: {\n                                                                        color: '#fff'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.HStack, {\n                                                    spacing: 4,\n                                                    justify: \"center\",\n                                                    children: channelDistribution.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.HStack, {\n                                                            spacing: 2,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Box, {\n                                                                    w: \"3\",\n                                                                    h: \"3\",\n                                                                    bg: item.color,\n                                                                    rounded: \"full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fontSize: \"sm\",\n                                                                    color: \"gray.300\",\n                                                                    children: [\n                                                                        item.name,\n                                                                        \": \",\n                                                                        item.value\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                    bg: \"whiteAlpha.100\",\n                                    backdropFilter: \"blur(10px)\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.CardBody, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.VStack, {\n                                            spacing: 4,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Heading, {\n                                                    size: \"md\",\n                                                    color: \"white\",\n                                                    children: \"Weekly Activity\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Box, {\n                                                    h: \"200px\",\n                                                    w: \"full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.ResponsiveContainer, {\n                                                        width: \"100%\",\n                                                        height: \"100%\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.BarChart, {\n                                                            data: weeklyActivity,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.CartesianGrid, {\n                                                                    strokeDasharray: \"3 3\",\n                                                                    stroke: \"rgba(255,255,255,0.1)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.XAxis, {\n                                                                    dataKey: \"day\",\n                                                                    axisLine: false,\n                                                                    tickLine: false,\n                                                                    tick: {\n                                                                        fill: '#A0AEC0',\n                                                                        fontSize: 12\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.YAxis, {\n                                                                    axisLine: false,\n                                                                    tickLine: false,\n                                                                    tick: {\n                                                                        fill: '#A0AEC0',\n                                                                        fontSize: 12\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Tooltip, {\n                                                                    wrapperStyle: {\n                                                                        backgroundColor: 'transparent'\n                                                                    },\n                                                                    contentStyle: {\n                                                                        backgroundColor: 'rgba(26, 32, 44, 0.9)',\n                                                                        border: '1px solid rgba(255,255,255,0.2)',\n                                                                        borderRadius: '8px',\n                                                                        color: '#fff'\n                                                                    },\n                                                                    itemStyle: {\n                                                                        color: '#fff'\n                                                                    },\n                                                                    labelStyle: {\n                                                                        color: '#fff'\n                                                                    },\n                                                                    cursor: {\n                                                                        fill: 'rgba(255,255,255,0.08)'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Bar, {\n                                                                    dataKey: \"commands\",\n                                                                    fill: \"#4299E1\",\n                                                                    name: \"Commands\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Bar, {\n                                                                    dataKey: \"joins\",\n                                                                    fill: \"#48BB78\",\n                                                                    name: \"Joins\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Bar, {\n                                                                    dataKey: \"leaves\",\n                                                                    fill: \"#E53E3E\",\n                                                                    name: \"Leaves\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Wrap, {\n                    spacing: \"24px\",\n                    justify: \"start\",\n                    children: filteredCards.map((card)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.WrapItem, {\n                            flex: \"1 0 260px\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardBody_HStack_Heading_Icon_SimpleGrid_Skeleton_Stat_StatHelpText_StatLabel_StatNumber_Text_VStack_Wrap_WrapItem_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Box, {\n                                onClick: ()=>window.dispatchEvent(new CustomEvent('colorClick', {\n                                        detail: card.color\n                                    })),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OverviewCard__WEBPACK_IMPORTED_MODULE_8__.OverviewCard, {\n                                    ...card\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 15\n                            }, this)\n                        }, card.id, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\overview.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\nconst getServerSideProps = async (ctx)=>{\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(ctx.req, ctx.res, _api_auth_nextauth___WEBPACK_IMPORTED_MODULE_3__.authOptions);\n    if (!session) {\n        return {\n            redirect: {\n                destination: '/signin',\n                permanent: false\n            }\n        };\n    }\n    return {\n        props: {}\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/overview.tsx\n");

/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/react");;

/***/ }),

/***/ "@emotion/styled":
/*!**********************************!*\
  !*** external "@emotion/styled" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("@emotion/styled");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "swr":
/*!**********************!*\
  !*** external "swr" ***!
  \**********************/
/***/ ((module) => {

module.exports = import("swr");;

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c","chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-cedb36cc","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27","chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9","chakra-node_modules_pnpm_chakra-ui_theme-","chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_fa_index_mjs-b1cc012-b4687165","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_f","lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_d3-sc","lib-node_modules_pnpm_d3-time-","lib-node_modules_pnpm_dec","lib-node_modules_pnpm_e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_immer_10_1_1_node_modules_immer_dist_immer_mjs-806fdd73","lib-node_modules_pnpm_i","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_react-c","lib-node_modules_pnpm_react-red","lib-node_modules_pnpm_rea","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-5c215c87","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-bf697780","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-334e6784","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-d89a0eeb","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-4c6b09db","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-d29869f5","lib-node_modules_pnpm_rec","lib-node_modules_pnpm_redux_","lib-node_modules_pnpm_r","commons","pages/overview-_"], () => (__webpack_exec__("(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Foverview&preferredRegion=&absolutePagePath=.%2Fpages%5Coverview.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();