// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { MongoClient } from 'mongodb';
import { dashboardConfig } from '../../../core/config';
import { MOD_SCENARIOS } from '../../../config/modScenarios';

// MongoDB connection
const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = await MongoClient.connect(mongoUrl);
  cachedClient = client;
  return client;
}

// Default application config
const defaultConfig = {
  isOpen: true,
  type: 'applications',
  questions: [
    "Why do you want to be a moderator?",
    "What experience do you have with moderation?",
    "How would you handle a difficult situation with a user?"
  ],
  scenarios: MOD_SCENARIOS,
  open: true // Whether applications are currently being accepted
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (req.method === 'GET') {
    let client;
    try {
      client = await connectToDatabase();
      const db = client.db(dbName);
      let config = await db.collection('config').findOne({ type: 'applications' });
      
      // If no config exists, use default
      if (!config) {
        config = defaultConfig;
      } else {
        // Merge with default config to ensure all fields exist
        config = {
          ...defaultConfig,
          ...config,
          // Ensure scenarios and questions exist
          scenarios: defaultConfig.scenarios,
          questions: defaultConfig.questions
        };

        // Ensure both isOpen and open fields are present and synchronized
        if (typeof config.isOpen === 'boolean' && config.open !== config.isOpen) {
          await db.collection('config').updateOne(
            { type: 'applications' },
            { $set: { open: config.isOpen } }
          );
          config.open = config.isOpen;
        } else if (typeof config.open === 'boolean' && config.isOpen !== config.open) {
          await db.collection('config').updateOne(
            { type: 'applications' },
            { $set: { isOpen: config.open } }
          );
          config.isOpen = config.open;
        }
      }

      return res.status(200).json(config);
    } catch (error) {
      console.error('Database error:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }

  if (req.method === 'POST') {
    const { isOpen } = req.body;

    if (typeof isOpen !== 'boolean') {
      return res.status(400).json({ error: 'Missing or invalid isOpen field' });
    }

    let client;
    try {
      client = await connectToDatabase();
      const db = client.db(dbName);

      // Get existing config or use default
      let existingConfig = await db.collection('config').findOne({ type: 'applications' }) || defaultConfig;
      
      // Update both isOpen and open fields while preserving other config
      // Ensure scenarios and questions are always from defaultConfig
      await db.collection('config').updateOne(
        { type: 'applications' },
        { 
          $set: { 
            ...existingConfig,
            type: 'applications',
            isOpen,
            open: isOpen,
            scenarios: defaultConfig.scenarios,
            questions: defaultConfig.questions
          }
        },
        { upsert: true }
      );

      const updatedConfig = await db.collection('config').findOne({ type: 'applications' });
      return res.status(200).json(updatedConfig);
    } catch (error) {
      console.error('Database error:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }

  res.setHeader('Allow', ['GET', 'POST']);
  return res.status(405).json({ error: 'Method not allowed' });
} 