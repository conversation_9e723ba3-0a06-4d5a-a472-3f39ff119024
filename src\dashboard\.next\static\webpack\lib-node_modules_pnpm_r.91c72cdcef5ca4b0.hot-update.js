"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("lib-node_modules_pnpm_r",{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-clientside-effect@1.2.8_react@19.1.0/node_modules/react-clientside-effect/lib/index.es.js":
/*!********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-clientside-effect@1.2.8_react@19.1.0/node_modules/react-clientside-effect/lib/index.es.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(pages-dir-browser)/../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(pages-dir-browser)/../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction withSideEffect(reducePropsToState, handleStateChangeOnClient) {\n  if (true) {\n    if (typeof reducePropsToState !== 'function') {\n      throw new Error('Expected reducePropsToState to be a function.');\n    }\n\n    if (typeof handleStateChangeOnClient !== 'function') {\n      throw new Error('Expected handleStateChangeOnClient to be a function.');\n    }\n  }\n\n  function getDisplayName(WrappedComponent) {\n    return WrappedComponent.displayName || WrappedComponent.name || 'Component';\n  }\n\n  return function wrap(WrappedComponent) {\n    if (true) {\n      if (typeof WrappedComponent !== 'function') {\n        throw new Error('Expected WrappedComponent to be a React component.');\n      }\n    }\n\n    var mountedInstances = [];\n    var state;\n\n    function emitChange() {\n      state = reducePropsToState(mountedInstances.map(function (instance) {\n        return instance.props;\n      }));\n      handleStateChangeOnClient(state);\n    }\n\n    var SideEffect = /*#__PURE__*/function (_PureComponent) {\n      (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(SideEffect, _PureComponent);\n\n      function SideEffect() {\n        return _PureComponent.apply(this, arguments) || this;\n      }\n\n      // Try to use displayName of wrapped component\n      SideEffect.peek = function peek() {\n        return state;\n      };\n\n      var _proto = SideEffect.prototype;\n\n      _proto.componentDidMount = function componentDidMount() {\n        mountedInstances.push(this);\n        emitChange();\n      };\n\n      _proto.componentDidUpdate = function componentDidUpdate() {\n        emitChange();\n      };\n\n      _proto.componentWillUnmount = function componentWillUnmount() {\n        var index = mountedInstances.indexOf(this);\n        mountedInstances.splice(index, 1);\n        emitChange();\n      };\n\n      _proto.render = function render() {\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().createElement(WrappedComponent, this.props);\n      };\n\n      return SideEffect;\n    }(react__WEBPACK_IMPORTED_MODULE_2__.PureComponent);\n\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(SideEffect, \"displayName\", \"SideEffect(\" + getDisplayName(WrappedComponent) + \")\");\n\n    return SideEffect;\n  };\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (withSideEffect);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-clientside-effect@1.2.8_react@19.1.0/node_modules/react-clientside-effect/lib/index.es.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(pages-dir-browser)/../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prop-types */ \"(pages-dir-browser)/../../node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! focus-lock/constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/util.js\");\n\n\n\n\n\nvar AutoFocusInside = function AutoFocusInside(_ref) {\n  var _ref$disabled = _ref.disabled,\n    disabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    children = _ref.children,\n    _ref$className = _ref.className,\n    className = _ref$className === void 0 ? undefined : _ref$className;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (0,_util__WEBPACK_IMPORTED_MODULE_2__.inlineProp)(focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_AUTO, !disabled), {\n    className: className\n  }), children);\n};\nAutoFocusInside.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().node).isRequired,\n  disabled: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),\n  className: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().string)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AutoFocusInside);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L0F1dG9Gb2N1c0luc2lkZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwRDtBQUNoQztBQUNTO0FBQ2U7QUFDZDtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMERBQW1CLFFBQVEsOEVBQVEsR0FBRyxFQUFFLGlEQUFVLENBQUMsNERBQVU7QUFDbkY7QUFDQSxHQUFHO0FBQ0g7QUFDQSw0QkFBNEIsS0FBcUM7QUFDakUsWUFBWSx3REFBYztBQUMxQixZQUFZLHdEQUFjO0FBQzFCLGFBQWEsMERBQWdCO0FBQzdCLEVBQUUsRUFBRSxDQUFFO0FBQ04saUVBQWUsZUFBZSIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWZvY3VzLWxvY2tcXGRpc3RcXGVzMjAxNVxcQXV0b0ZvY3VzSW5zaWRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBQcm9wVHlwZXMgZnJvbSAncHJvcC10eXBlcyc7XG5pbXBvcnQgeyBGT0NVU19BVVRPIH0gZnJvbSAnZm9jdXMtbG9jay9jb25zdGFudHMnO1xuaW1wb3J0IHsgaW5saW5lUHJvcCB9IGZyb20gJy4vdXRpbCc7XG52YXIgQXV0b0ZvY3VzSW5zaWRlID0gZnVuY3Rpb24gQXV0b0ZvY3VzSW5zaWRlKF9yZWYpIHtcbiAgdmFyIF9yZWYkZGlzYWJsZWQgPSBfcmVmLmRpc2FibGVkLFxuICAgIGRpc2FibGVkID0gX3JlZiRkaXNhYmxlZCA9PT0gdm9pZCAwID8gZmFsc2UgOiBfcmVmJGRpc2FibGVkLFxuICAgIGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbixcbiAgICBfcmVmJGNsYXNzTmFtZSA9IF9yZWYuY2xhc3NOYW1lLFxuICAgIGNsYXNzTmFtZSA9IF9yZWYkY2xhc3NOYW1lID09PSB2b2lkIDAgPyB1bmRlZmluZWQgOiBfcmVmJGNsYXNzTmFtZTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIF9leHRlbmRzKHt9LCBpbmxpbmVQcm9wKEZPQ1VTX0FVVE8sICFkaXNhYmxlZCksIHtcbiAgICBjbGFzc05hbWU6IGNsYXNzTmFtZVxuICB9KSwgY2hpbGRyZW4pO1xufTtcbkF1dG9Gb2N1c0luc2lkZS5wcm9wVHlwZXMgPSBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIgPyB7XG4gIGNoaWxkcmVuOiBQcm9wVHlwZXMubm9kZS5pc1JlcXVpcmVkLFxuICBkaXNhYmxlZDogUHJvcFR5cGVzLmJvb2wsXG4gIGNsYXNzTmFtZTogUHJvcFR5cGVzLnN0cmluZ1xufSA6IHt9O1xuZXhwb3J0IGRlZmF1bHQgQXV0b0ZvY3VzSW5zaWRlOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Combination.js":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Combination.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(pages-dir-browser)/../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(pages-dir-browser)/../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Lock__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Lock */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Lock.js\");\n/* harmony import */ var _Trap__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Trap */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Trap.js\");\n\n\n\n\n\nvar FocusLockCombination = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(function FocusLockUICombination(props, ref) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().createElement(_Lock__WEBPACK_IMPORTED_MODULE_3__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    sideCar: _Trap__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    ref: ref\n  }, props));\n});\nvar _ref = _Lock__WEBPACK_IMPORTED_MODULE_3__[\"default\"].propTypes || {},\n  sideCar = _ref.sideCar,\n  propTypes = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref, [\"sideCar\"]);\nFocusLockCombination.propTypes =  true ? propTypes : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FocusLockCombination);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L0NvbWJpbmF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBb0c7QUFDMUM7QUFDaEI7QUFDVDtBQUNGO0FBQy9CLHdDQUF3QyxpREFBVTtBQUNsRCxzQkFBc0IsMERBQW1CLENBQUMsNkNBQVcsRUFBRSw4RUFBUTtBQUMvRCxhQUFhLDZDQUFTO0FBQ3RCO0FBQ0EsR0FBRztBQUNILENBQUM7QUFDRCxXQUFXLHVEQUFxQixNQUFNO0FBQ3RDO0FBQ0EsY0FBYyxtR0FBNkI7QUFDM0MsaUNBQWlDLEtBQXFDLGVBQWUsQ0FBRTtBQUN2RixpRUFBZSxvQkFBb0IiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LWZvY3VzLWxvY2tAMi4xMy42X0B0eXBlcytyZWFjdEAxOC4zLjIzX3JlYWN0QDE5LjEuMFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1mb2N1cy1sb2NrXFxkaXN0XFxlczIwMTVcXENvbWJpbmF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZVwiO1xuaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzXCI7XG5pbXBvcnQgUmVhY3QsIHsgZm9yd2FyZFJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBGb2N1c0xvY2tVSSBmcm9tICcuL0xvY2snO1xuaW1wb3J0IEZvY3VzVHJhcCBmcm9tICcuL1RyYXAnO1xudmFyIEZvY3VzTG9ja0NvbWJpbmF0aW9uID0gLyojX19QVVJFX18qL2ZvcndhcmRSZWYoZnVuY3Rpb24gRm9jdXNMb2NrVUlDb21iaW5hdGlvbihwcm9wcywgcmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChGb2N1c0xvY2tVSSwgX2V4dGVuZHMoe1xuICAgIHNpZGVDYXI6IEZvY3VzVHJhcCxcbiAgICByZWY6IHJlZlxuICB9LCBwcm9wcykpO1xufSk7XG52YXIgX3JlZiA9IEZvY3VzTG9ja1VJLnByb3BUeXBlcyB8fCB7fSxcbiAgc2lkZUNhciA9IF9yZWYuc2lkZUNhcixcbiAgcHJvcFR5cGVzID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UoX3JlZiwgW1wic2lkZUNhclwiXSk7XG5Gb2N1c0xvY2tDb21iaW5hdGlvbi5wcm9wVHlwZXMgPSBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIgPyBwcm9wVHlwZXMgOiB7fTtcbmV4cG9ydCBkZWZhdWx0IEZvY3VzTG9ja0NvbWJpbmF0aW9uOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Combination.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/FocusGuard.js":
/*!**************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/FocusGuard.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hiddenGuard: () => (/* binding */ hiddenGuard)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prop-types */ \"(pages-dir-browser)/../../node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar hiddenGuard = {\n  width: '1px',\n  height: '0px',\n  padding: 0,\n  overflow: 'hidden',\n  position: 'fixed',\n  top: '1px',\n  left: '1px'\n};\nvar InFocusGuard = function InFocusGuard(_ref) {\n  var _ref$children = _ref.children,\n    children = _ref$children === void 0 ? null : _ref$children;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    key: \"guard-first\",\n    \"data-focus-guard\": true,\n    \"data-focus-auto-guard\": true,\n    style: hiddenGuard\n  }), children, children && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    key: \"guard-last\",\n    \"data-focus-guard\": true,\n    \"data-focus-auto-guard\": true,\n    style: hiddenGuard\n  }));\n};\nInFocusGuard.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().node)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InFocusGuard);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/FocusGuard.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(pages-dir-browser)/../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prop-types */ \"(pages-dir-browser)/../../node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! focus-lock/constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/util.js\");\n\n\n\n\n\nvar FreeFocusInside = function FreeFocusInside(_ref) {\n  var children = _ref.children,\n    className = _ref.className;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (0,_util__WEBPACK_IMPORTED_MODULE_2__.inlineProp)(focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_ALLOW, true), {\n    className: className\n  }), children);\n};\nFreeFocusInside.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().node).isRequired,\n  className: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().string)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FreeFocusInside);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L0ZyZWVGb2N1c0luc2lkZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwRDtBQUNoQztBQUNTO0FBQ2dCO0FBQ2Y7QUFDcEM7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDBEQUFtQixRQUFRLDhFQUFRLEdBQUcsRUFBRSxpREFBVSxDQUFDLDZEQUFXO0FBQ3BGO0FBQ0EsR0FBRztBQUNIO0FBQ0EsNEJBQTRCLEtBQXFDO0FBQ2pFLFlBQVksd0RBQWM7QUFDMUIsYUFBYSwwREFBZ0I7QUFDN0IsRUFBRSxFQUFFLENBQUU7QUFDTixpRUFBZSxlQUFlIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC1mb2N1cy1sb2NrQDIuMTMuNl9AdHlwZXMrcmVhY3RAMTguMy4yM19yZWFjdEAxOS4xLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtZm9jdXMtbG9ja1xcZGlzdFxcZXMyMDE1XFxGcmVlRm9jdXNJbnNpZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzXCI7XG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IFByb3BUeXBlcyBmcm9tICdwcm9wLXR5cGVzJztcbmltcG9ydCB7IEZPQ1VTX0FMTE9XIH0gZnJvbSAnZm9jdXMtbG9jay9jb25zdGFudHMnO1xuaW1wb3J0IHsgaW5saW5lUHJvcCB9IGZyb20gJy4vdXRpbCc7XG52YXIgRnJlZUZvY3VzSW5zaWRlID0gZnVuY3Rpb24gRnJlZUZvY3VzSW5zaWRlKF9yZWYpIHtcbiAgdmFyIGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbixcbiAgICBjbGFzc05hbWUgPSBfcmVmLmNsYXNzTmFtZTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIF9leHRlbmRzKHt9LCBpbmxpbmVQcm9wKEZPQ1VTX0FMTE9XLCB0cnVlKSwge1xuICAgIGNsYXNzTmFtZTogY2xhc3NOYW1lXG4gIH0pLCBjaGlsZHJlbik7XG59O1xuRnJlZUZvY3VzSW5zaWRlLnByb3BUeXBlcyA9IHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIiA/IHtcbiAgY2hpbGRyZW46IFByb3BUeXBlcy5ub2RlLmlzUmVxdWlyZWQsXG4gIGNsYXNzTmFtZTogUHJvcFR5cGVzLnN0cmluZ1xufSA6IHt9O1xuZXhwb3J0IGRlZmF1bHQgRnJlZUZvY3VzSW5zaWRlOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Lock.js":
/*!********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Lock.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(pages-dir-browser)/../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! prop-types */ \"(pages-dir-browser)/../../node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! focus-lock/constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! use-callback-ref */ \"(pages-dir-browser)/../../node_modules/.pnpm/use-callback-ref@1.3.3_@types+react@18.3.23_react@19.1.0/node_modules/use-callback-ref/dist/es2015/index.js\");\n/* harmony import */ var _FocusGuard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FocusGuard */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/FocusGuard.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./medium */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/medium.js\");\n/* harmony import */ var _scope__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./scope */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/scope.js\");\n\n\n\n\n\n\n\n\nvar emptyArray = [];\nvar FocusLock = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function FocusLockUI(props, parentRef) {\n  var _extends2;\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(),\n    realObserved = _useState[0],\n    setObserved = _useState[1];\n  var observed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n  var isActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n  var originalFocusedElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}),\n    update = _useState2[1];\n  var children = props.children,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled,\n    _props$noFocusGuards = props.noFocusGuards,\n    noFocusGuards = _props$noFocusGuards === void 0 ? false : _props$noFocusGuards,\n    _props$persistentFocu = props.persistentFocus,\n    persistentFocus = _props$persistentFocu === void 0 ? false : _props$persistentFocu,\n    _props$crossFrame = props.crossFrame,\n    crossFrame = _props$crossFrame === void 0 ? true : _props$crossFrame,\n    _props$autoFocus = props.autoFocus,\n    autoFocus = _props$autoFocus === void 0 ? true : _props$autoFocus,\n    allowTextSelection = props.allowTextSelection,\n    group = props.group,\n    className = props.className,\n    whiteList = props.whiteList,\n    hasPositiveIndices = props.hasPositiveIndices,\n    _props$shards = props.shards,\n    shards = _props$shards === void 0 ? emptyArray : _props$shards,\n    _props$as = props.as,\n    Container = _props$as === void 0 ? 'div' : _props$as,\n    _props$lockProps = props.lockProps,\n    containerProps = _props$lockProps === void 0 ? {} : _props$lockProps,\n    SideCar = props.sideCar,\n    _props$returnFocus = props.returnFocus,\n    shouldReturnFocus = _props$returnFocus === void 0 ? false : _props$returnFocus,\n    focusOptions = props.focusOptions,\n    onActivationCallback = props.onActivation,\n    onDeactivationCallback = props.onDeactivation;\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}),\n    id = _useState3[0];\n  var onActivation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (_ref) {\n    var captureFocusRestore = _ref.captureFocusRestore;\n    if (!originalFocusedElement.current) {\n      var _document;\n      var activeElement = (_document = document) == null ? void 0 : _document.activeElement;\n      originalFocusedElement.current = activeElement;\n      if (activeElement !== document.body) {\n        originalFocusedElement.current = captureFocusRestore(activeElement);\n      }\n    }\n    if (observed.current && onActivationCallback) {\n      onActivationCallback(observed.current);\n    }\n    isActive.current = true;\n    update();\n  }, [onActivationCallback]);\n  var onDeactivation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n    isActive.current = false;\n    if (onDeactivationCallback) {\n      onDeactivationCallback(observed.current);\n    }\n    update();\n  }, [onDeactivationCallback]);\n  var returnFocus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (allowDefer) {\n    var focusRestore = originalFocusedElement.current;\n    if (focusRestore) {\n      var returnFocusTo = (typeof focusRestore === 'function' ? focusRestore() : focusRestore) || document.body;\n      var howToReturnFocus = typeof shouldReturnFocus === 'function' ? shouldReturnFocus(returnFocusTo) : shouldReturnFocus;\n      if (howToReturnFocus) {\n        var returnFocusOptions = typeof howToReturnFocus === 'object' ? howToReturnFocus : undefined;\n        originalFocusedElement.current = null;\n        if (allowDefer) {\n          Promise.resolve().then(function () {\n            return returnFocusTo.focus(returnFocusOptions);\n          });\n        } else {\n          returnFocusTo.focus(returnFocusOptions);\n        }\n      }\n    }\n  }, [shouldReturnFocus]);\n  var onFocus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (event) {\n    if (isActive.current) {\n      _medium__WEBPACK_IMPORTED_MODULE_2__.mediumFocus.useMedium(event);\n    }\n  }, []);\n  var onBlur = _medium__WEBPACK_IMPORTED_MODULE_2__.mediumBlur.useMedium;\n  var setObserveNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (newObserved) {\n    if (observed.current !== newObserved) {\n      observed.current = newObserved;\n      setObserved(newObserved);\n    }\n  }, []);\n  if (true) {\n    if (typeof allowTextSelection !== 'undefined') {\n      console.warn('React-Focus-Lock: allowTextSelection is deprecated and enabled by default');\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      if (!observed.current && typeof Container !== 'string') {\n        console.error('FocusLock: could not obtain ref to internal node');\n      }\n    }, []);\n  }\n  var lockProps = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((_extends2 = {}, _extends2[focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_DISABLED] = disabled && 'disabled', _extends2[focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_GROUP] = group, _extends2), containerProps);\n  var hasLeadingGuards = noFocusGuards !== true;\n  var hasTailingGuards = hasLeadingGuards && noFocusGuards !== 'tail';\n  var mergedRef = (0,use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useMergeRefs)([parentRef, setObserveNode]);\n  var focusScopeValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return {\n      observed: observed,\n      shards: shards,\n      enabled: !disabled,\n      active: isActive.current\n    };\n  }, [disabled, isActive.current, shards, realObserved]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, hasLeadingGuards && [\n  /*#__PURE__*/\n  react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n    key: \"guard-first\",\n    \"data-focus-guard\": true,\n    tabIndex: disabled ? -1 : 0,\n    style: _FocusGuard__WEBPACK_IMPORTED_MODULE_5__.hiddenGuard\n  }), hasPositiveIndices ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n    key: \"guard-nearest\",\n    \"data-focus-guard\": true,\n    tabIndex: disabled ? -1 : 1,\n    style: _FocusGuard__WEBPACK_IMPORTED_MODULE_5__.hiddenGuard\n  }) : null], !disabled && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(SideCar, {\n    id: id,\n    sideCar: _medium__WEBPACK_IMPORTED_MODULE_2__.mediumSidecar,\n    observed: realObserved,\n    disabled: disabled,\n    persistentFocus: persistentFocus,\n    crossFrame: crossFrame,\n    autoFocus: autoFocus,\n    whiteList: whiteList,\n    shards: shards,\n    onActivation: onActivation,\n    onDeactivation: onDeactivation,\n    returnFocus: returnFocus,\n    focusOptions: focusOptions,\n    noFocusGuards: noFocusGuards\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(Container, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: mergedRef\n  }, lockProps, {\n    className: className,\n    onBlur: onBlur,\n    onFocus: onFocus\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_scope__WEBPACK_IMPORTED_MODULE_6__.focusScope.Provider, {\n    value: focusScopeValue\n  }, children)), hasTailingGuards && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n    \"data-focus-guard\": true,\n    tabIndex: disabled ? -1 : 0,\n    style: _FocusGuard__WEBPACK_IMPORTED_MODULE_5__.hiddenGuard\n  }));\n});\nFocusLock.propTypes =  true ? {\n  children: prop_types__WEBPACK_IMPORTED_MODULE_7__.node,\n  disabled: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  returnFocus: (0,prop_types__WEBPACK_IMPORTED_MODULE_7__.oneOfType)([prop_types__WEBPACK_IMPORTED_MODULE_7__.bool, prop_types__WEBPACK_IMPORTED_MODULE_7__.object, prop_types__WEBPACK_IMPORTED_MODULE_7__.func]),\n  focusOptions: prop_types__WEBPACK_IMPORTED_MODULE_7__.object,\n  noFocusGuards: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  hasPositiveIndices: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  allowTextSelection: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  autoFocus: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  persistentFocus: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  crossFrame: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  group: prop_types__WEBPACK_IMPORTED_MODULE_7__.string,\n  className: prop_types__WEBPACK_IMPORTED_MODULE_7__.string,\n  whiteList: prop_types__WEBPACK_IMPORTED_MODULE_7__.func,\n  shards: (0,prop_types__WEBPACK_IMPORTED_MODULE_7__.arrayOf)(prop_types__WEBPACK_IMPORTED_MODULE_7__.any),\n  as: (0,prop_types__WEBPACK_IMPORTED_MODULE_7__.oneOfType)([prop_types__WEBPACK_IMPORTED_MODULE_7__.string, prop_types__WEBPACK_IMPORTED_MODULE_7__.func, prop_types__WEBPACK_IMPORTED_MODULE_7__.object]),\n  lockProps: prop_types__WEBPACK_IMPORTED_MODULE_7__.object,\n  onActivation: prop_types__WEBPACK_IMPORTED_MODULE_7__.func,\n  onDeactivation: prop_types__WEBPACK_IMPORTED_MODULE_7__.func,\n  sideCar: prop_types__WEBPACK_IMPORTED_MODULE_7__.any.isRequired\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FocusLock);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Lock.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useFocusInside: () => (/* binding */ useFocusInside)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(pages-dir-browser)/../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! prop-types */ \"(pages-dir-browser)/../../node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! focus-lock/constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/util.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./medium */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/medium.js\");\n\n\n\n\n\n\nvar useFocusInside = function useFocusInside(observedRef) {\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var enabled = true;\n    _medium__WEBPACK_IMPORTED_MODULE_2__.mediumEffect.useMedium(function (car) {\n      var observed = observedRef && observedRef.current;\n      if (enabled && observed) {\n        if (!car.focusInside(observed)) {\n          car.moveFocusInside(observed, null);\n        }\n      }\n    });\n    return function () {\n      enabled = false;\n    };\n  }, [observedRef]);\n};\nfunction MoveFocusInside(_ref) {\n  var _ref$disabled = _ref.disabled,\n    isDisabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    className = _ref.className,\n    children = _ref.children;\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  useFocusInside(isDisabled ? undefined : ref);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (0,_util__WEBPACK_IMPORTED_MODULE_3__.inlineProp)(focus_lock_constants__WEBPACK_IMPORTED_MODULE_4__.FOCUS_AUTO, !isDisabled), {\n    ref: ref,\n    className: className\n  }), children);\n}\nMoveFocusInside.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().node).isRequired,\n  disabled: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().bool),\n  className: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MoveFocusInside);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L01vdmVGb2N1c0luc2lkZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQTBEO0FBQ1Q7QUFDZDtBQUNlO0FBQ2Q7QUFDSTtBQUNqQztBQUNQLEVBQUUsZ0RBQVM7QUFDWDtBQUNBLElBQUksaURBQVk7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLDZDQUFNO0FBQ2xCO0FBQ0Esc0JBQXNCLDBEQUFtQixRQUFRLDhFQUFRLEdBQUcsRUFBRSxpREFBVSxDQUFDLDREQUFVO0FBQ25GO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSw0QkFBNEIsS0FBcUM7QUFDakUsWUFBWSx3REFBYztBQUMxQixZQUFZLHdEQUFjO0FBQzFCLGFBQWEsMERBQWdCO0FBQzdCLEVBQUUsRUFBRSxDQUFFO0FBQ04saUVBQWUsZUFBZSIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWZvY3VzLWxvY2tcXGRpc3RcXGVzMjAxNVxcTW92ZUZvY3VzSW5zaWRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IFByb3BUeXBlcyBmcm9tICdwcm9wLXR5cGVzJztcbmltcG9ydCB7IEZPQ1VTX0FVVE8gfSBmcm9tICdmb2N1cy1sb2NrL2NvbnN0YW50cyc7XG5pbXBvcnQgeyBpbmxpbmVQcm9wIH0gZnJvbSAnLi91dGlsJztcbmltcG9ydCB7IG1lZGl1bUVmZmVjdCB9IGZyb20gJy4vbWVkaXVtJztcbmV4cG9ydCB2YXIgdXNlRm9jdXNJbnNpZGUgPSBmdW5jdGlvbiB1c2VGb2N1c0luc2lkZShvYnNlcnZlZFJlZikge1xuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHZhciBlbmFibGVkID0gdHJ1ZTtcbiAgICBtZWRpdW1FZmZlY3QudXNlTWVkaXVtKGZ1bmN0aW9uIChjYXIpIHtcbiAgICAgIHZhciBvYnNlcnZlZCA9IG9ic2VydmVkUmVmICYmIG9ic2VydmVkUmVmLmN1cnJlbnQ7XG4gICAgICBpZiAoZW5hYmxlZCAmJiBvYnNlcnZlZCkge1xuICAgICAgICBpZiAoIWNhci5mb2N1c0luc2lkZShvYnNlcnZlZCkpIHtcbiAgICAgICAgICBjYXIubW92ZUZvY3VzSW5zaWRlKG9ic2VydmVkLCBudWxsKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICBlbmFibGVkID0gZmFsc2U7XG4gICAgfTtcbiAgfSwgW29ic2VydmVkUmVmXSk7XG59O1xuZnVuY3Rpb24gTW92ZUZvY3VzSW5zaWRlKF9yZWYpIHtcbiAgdmFyIF9yZWYkZGlzYWJsZWQgPSBfcmVmLmRpc2FibGVkLFxuICAgIGlzRGlzYWJsZWQgPSBfcmVmJGRpc2FibGVkID09PSB2b2lkIDAgPyBmYWxzZSA6IF9yZWYkZGlzYWJsZWQsXG4gICAgY2xhc3NOYW1lID0gX3JlZi5jbGFzc05hbWUsXG4gICAgY2hpbGRyZW4gPSBfcmVmLmNoaWxkcmVuO1xuICB2YXIgcmVmID0gdXNlUmVmKG51bGwpO1xuICB1c2VGb2N1c0luc2lkZShpc0Rpc2FibGVkID8gdW5kZWZpbmVkIDogcmVmKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIF9leHRlbmRzKHt9LCBpbmxpbmVQcm9wKEZPQ1VTX0FVVE8sICFpc0Rpc2FibGVkKSwge1xuICAgIHJlZjogcmVmLFxuICAgIGNsYXNzTmFtZTogY2xhc3NOYW1lXG4gIH0pLCBjaGlsZHJlbik7XG59XG5Nb3ZlRm9jdXNJbnNpZGUucHJvcFR5cGVzID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiID8ge1xuICBjaGlsZHJlbjogUHJvcFR5cGVzLm5vZGUuaXNSZXF1aXJlZCxcbiAgZGlzYWJsZWQ6IFByb3BUeXBlcy5ib29sLFxuICBjbGFzc05hbWU6IFByb3BUeXBlcy5zdHJpbmdcbn0gOiB7fTtcbmV4cG9ydCBkZWZhdWx0IE1vdmVGb2N1c0luc2lkZTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Trap.js":
/*!********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Trap.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"(pages-dir-browser)/../../node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_clientside_effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-clientside-effect */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-clientside-effect@1.2.8_react@19.1.0/node_modules/react-clientside-effect/lib/index.es.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! focus-lock */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/index.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/util.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./medium */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/medium.js\");\n\n\n\n\n\n\nvar focusOnBody = function focusOnBody() {\n  return document && document.activeElement === document.body;\n};\nvar isFreeFocus = function isFreeFocus() {\n  return focusOnBody() || (0,focus_lock__WEBPACK_IMPORTED_MODULE_1__.focusIsHidden)();\n};\nvar lastActiveTrap = null;\nvar lastActiveFocus = null;\nvar tryRestoreFocus = function tryRestoreFocus() {\n  return null;\n};\nvar lastPortaledElement = null;\nvar focusWasOutsideWindow = false;\nvar windowFocused = false;\nvar defaultWhitelist = function defaultWhitelist() {\n  return true;\n};\nvar focusWhitelisted = function focusWhitelisted(activeElement) {\n  return (lastActiveTrap.whiteList || defaultWhitelist)(activeElement);\n};\nvar recordPortal = function recordPortal(observerNode, portaledElement) {\n  lastPortaledElement = {\n    observerNode: observerNode,\n    portaledElement: portaledElement\n  };\n};\nvar focusIsPortaledPair = function focusIsPortaledPair(element) {\n  return lastPortaledElement && lastPortaledElement.portaledElement === element;\n};\nfunction autoGuard(startIndex, end, step, allNodes) {\n  var lastGuard = null;\n  var i = startIndex;\n  do {\n    var item = allNodes[i];\n    if (item.guard) {\n      if (item.node.dataset.focusAutoGuard) {\n        lastGuard = item;\n      }\n    } else if (item.lockItem) {\n      if (i !== startIndex) {\n        return;\n      }\n      lastGuard = null;\n    } else {\n      break;\n    }\n  } while ((i += step) !== end);\n  if (lastGuard) {\n    lastGuard.node.tabIndex = 0;\n  }\n}\nvar focusWasOutside = function focusWasOutside(crossFrameOption) {\n  if (crossFrameOption) {\n    return Boolean(focusWasOutsideWindow);\n  }\n  return focusWasOutsideWindow === 'meanwhile';\n};\nvar checkInHost = function checkInHost(check, el, boundary) {\n  return el && (el.host === check && (!el.activeElement || boundary.contains(el.activeElement)) || el.parentNode && checkInHost(check, el.parentNode, boundary));\n};\nvar withinHost = function withinHost(activeElement, workingArea) {\n  return workingArea.some(function (area) {\n    return checkInHost(activeElement, area, area);\n  });\n};\nvar getNodeFocusables = function getNodeFocusables(nodes) {\n  return (0,focus_lock__WEBPACK_IMPORTED_MODULE_1__.getFocusableNodes)(nodes, new Map());\n};\nvar isNotFocusable = function isNotFocusable(node) {\n  return !getNodeFocusables([node.parentNode]).some(function (el) {\n    return el.node === node;\n  });\n};\nvar activateTrap = function activateTrap() {\n  var result = false;\n  if (lastActiveTrap) {\n    var _lastActiveTrap = lastActiveTrap,\n      observed = _lastActiveTrap.observed,\n      persistentFocus = _lastActiveTrap.persistentFocus,\n      autoFocus = _lastActiveTrap.autoFocus,\n      shards = _lastActiveTrap.shards,\n      crossFrame = _lastActiveTrap.crossFrame,\n      focusOptions = _lastActiveTrap.focusOptions,\n      noFocusGuards = _lastActiveTrap.noFocusGuards;\n    var workingNode = observed || lastPortaledElement && lastPortaledElement.portaledElement;\n    if (focusOnBody() && lastActiveFocus && lastActiveFocus !== document.body) {\n      if (!document.body.contains(lastActiveFocus) || isNotFocusable(lastActiveFocus)) {\n        var newTarget = tryRestoreFocus();\n        if (newTarget) {\n          newTarget.focus();\n        }\n      }\n    }\n    var activeElement = document && document.activeElement;\n    if (workingNode) {\n      var workingArea = [workingNode].concat(shards.map(_util__WEBPACK_IMPORTED_MODULE_2__.extractRef).filter(Boolean));\n      var shouldForceRestoreFocus = function shouldForceRestoreFocus() {\n        if (!focusWasOutside(crossFrame) || !noFocusGuards || !lastActiveFocus || windowFocused) {\n          return false;\n        }\n        var nodes = getNodeFocusables(workingArea);\n        var lastIndex = nodes.findIndex(function (_ref) {\n          var node = _ref.node;\n          return node === lastActiveFocus;\n        });\n        return lastIndex === 0 || lastIndex === nodes.length - 1;\n      };\n      if (!activeElement || focusWhitelisted(activeElement)) {\n        if (persistentFocus || shouldForceRestoreFocus() || !isFreeFocus() || !lastActiveFocus && autoFocus) {\n          if (workingNode && !((0,focus_lock__WEBPACK_IMPORTED_MODULE_1__.focusInside)(workingArea) || activeElement && withinHost(activeElement, workingArea) || focusIsPortaledPair(activeElement, workingNode))) {\n            if (document && !lastActiveFocus && activeElement && !autoFocus) {\n              if (activeElement.blur) {\n                activeElement.blur();\n              }\n              document.body.focus();\n            } else {\n              result = (0,focus_lock__WEBPACK_IMPORTED_MODULE_1__.moveFocusInside)(workingArea, lastActiveFocus, {\n                focusOptions: focusOptions\n              });\n              lastPortaledElement = {};\n            }\n          }\n          lastActiveFocus = document && document.activeElement;\n          if (lastActiveFocus !== document.body) {\n            tryRestoreFocus = (0,focus_lock__WEBPACK_IMPORTED_MODULE_1__.captureFocusRestore)(lastActiveFocus);\n          }\n          focusWasOutsideWindow = false;\n        }\n      }\n      if (document && activeElement !== document.activeElement && document.querySelector('[data-focus-auto-guard]')) {\n        var newActiveElement = document && document.activeElement;\n        var allNodes = (0,focus_lock__WEBPACK_IMPORTED_MODULE_1__.expandFocusableNodes)(workingArea);\n        var focusedIndex = allNodes.map(function (_ref2) {\n          var node = _ref2.node;\n          return node;\n        }).indexOf(newActiveElement);\n        if (focusedIndex > -1) {\n          allNodes.filter(function (_ref3) {\n            var guard = _ref3.guard,\n              node = _ref3.node;\n            return guard && node.dataset.focusAutoGuard;\n          }).forEach(function (_ref4) {\n            var node = _ref4.node;\n            return node.removeAttribute('tabIndex');\n          });\n          autoGuard(focusedIndex, allNodes.length, +1, allNodes);\n          autoGuard(focusedIndex, -1, -1, allNodes);\n        }\n      }\n    }\n  }\n  return result;\n};\nvar onTrap = function onTrap(event) {\n  if (activateTrap() && event) {\n    event.stopPropagation();\n    event.preventDefault();\n  }\n};\nvar onBlur = function onBlur() {\n  return (0,_util__WEBPACK_IMPORTED_MODULE_2__.deferAction)(activateTrap);\n};\nvar onFocus = function onFocus(event) {\n  var source = event.target;\n  var currentNode = event.currentTarget;\n  if (!currentNode.contains(source)) {\n    recordPortal(currentNode, source);\n  }\n};\nvar FocusWatcher = function FocusWatcher() {\n  return null;\n};\nvar FocusTrap = function FocusTrap(_ref5) {\n  var children = _ref5.children;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    onBlur: onBlur,\n    onFocus: onFocus\n  }, children);\n};\nFocusTrap.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().node).isRequired\n} : 0;\nvar onWindowFocus = function onWindowFocus() {\n  windowFocused = true;\n};\nvar onWindowBlur = function onWindowBlur() {\n  windowFocused = false;\n  focusWasOutsideWindow = 'just';\n  (0,_util__WEBPACK_IMPORTED_MODULE_2__.deferAction)(function () {\n    focusWasOutsideWindow = 'meanwhile';\n  });\n};\nvar attachHandler = function attachHandler() {\n  document.addEventListener('focusin', onTrap);\n  document.addEventListener('focusout', onBlur);\n  window.addEventListener('focus', onWindowFocus);\n  window.addEventListener('blur', onWindowBlur);\n};\nvar detachHandler = function detachHandler() {\n  document.removeEventListener('focusin', onTrap);\n  document.removeEventListener('focusout', onBlur);\n  window.removeEventListener('focus', onWindowFocus);\n  window.removeEventListener('blur', onWindowBlur);\n};\nfunction reducePropsToState(propsList) {\n  return propsList.filter(function (_ref6) {\n    var disabled = _ref6.disabled;\n    return !disabled;\n  });\n}\nvar focusLockAPI = {\n  moveFocusInside: focus_lock__WEBPACK_IMPORTED_MODULE_1__.moveFocusInside,\n  focusInside: focus_lock__WEBPACK_IMPORTED_MODULE_1__.focusInside,\n  focusNextElement: focus_lock__WEBPACK_IMPORTED_MODULE_1__.focusNextElement,\n  focusPrevElement: focus_lock__WEBPACK_IMPORTED_MODULE_1__.focusPrevElement,\n  focusFirstElement: focus_lock__WEBPACK_IMPORTED_MODULE_1__.focusFirstElement,\n  focusLastElement: focus_lock__WEBPACK_IMPORTED_MODULE_1__.focusLastElement,\n  captureFocusRestore: focus_lock__WEBPACK_IMPORTED_MODULE_1__.captureFocusRestore\n};\nfunction handleStateChangeOnClient(traps) {\n  var trap = traps.slice(-1)[0];\n  if (trap && !lastActiveTrap) {\n    attachHandler();\n  }\n  var lastTrap = lastActiveTrap;\n  var sameTrap = lastTrap && trap && trap.id === lastTrap.id;\n  lastActiveTrap = trap;\n  if (lastTrap && !sameTrap) {\n    lastTrap.onDeactivation();\n    if (!traps.filter(function (_ref7) {\n      var id = _ref7.id;\n      return id === lastTrap.id;\n    }).length) {\n      lastTrap.returnFocus(!trap);\n    }\n  }\n  if (trap) {\n    lastActiveFocus = null;\n    if (!sameTrap || lastTrap.observed !== trap.observed) {\n      trap.onActivation(focusLockAPI);\n    }\n    activateTrap(true);\n    (0,_util__WEBPACK_IMPORTED_MODULE_2__.deferAction)(activateTrap);\n  } else {\n    detachHandler();\n    lastActiveFocus = null;\n  }\n}\n_medium__WEBPACK_IMPORTED_MODULE_4__.mediumFocus.assignSyncMedium(onFocus);\n_medium__WEBPACK_IMPORTED_MODULE_4__.mediumBlur.assignMedium(onBlur);\n_medium__WEBPACK_IMPORTED_MODULE_4__.mediumEffect.assignMedium(function (cb) {\n  return cb(focusLockAPI);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_clientside_effect__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(reducePropsToState, handleStateChangeOnClient)(FocusWatcher));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Trap.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/UI.js":
/*!******************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/UI.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoFocusInside: () => (/* reexport safe */ _AutoFocusInside__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   FocusLockUI: () => (/* reexport safe */ _Lock__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   FreeFocusInside: () => (/* reexport safe */ _FreeFocusInside__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   InFocusGuard: () => (/* reexport safe */ _FocusGuard__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MoveFocusInside: () => (/* reexport safe */ _MoveFocusInside__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useFocusController: () => (/* reexport safe */ _use_focus_scope__WEBPACK_IMPORTED_MODULE_5__.useFocusController),\n/* harmony export */   useFocusInside: () => (/* reexport safe */ _MoveFocusInside__WEBPACK_IMPORTED_MODULE_1__.useFocusInside),\n/* harmony export */   useFocusScope: () => (/* reexport safe */ _use_focus_scope__WEBPACK_IMPORTED_MODULE_5__.useFocusScope),\n/* harmony export */   useFocusState: () => (/* reexport safe */ _use_focus_state__WEBPACK_IMPORTED_MODULE_6__.useFocusState)\n/* harmony export */ });\n/* harmony import */ var _Lock__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Lock */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Lock.js\");\n/* harmony import */ var _AutoFocusInside__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AutoFocusInside */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js\");\n/* harmony import */ var _MoveFocusInside__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MoveFocusInside */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js\");\n/* harmony import */ var _FreeFocusInside__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FreeFocusInside */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js\");\n/* harmony import */ var _FocusGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FocusGuard */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/FocusGuard.js\");\n/* harmony import */ var _use_focus_scope__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./use-focus-scope */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/use-focus-scope.js\");\n/* harmony import */ var _use_focus_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./use-focus-state */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/use-focus-state.js\");\n\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Lock__WEBPACK_IMPORTED_MODULE_4__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L1VJLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWlDO0FBQ2U7QUFDb0I7QUFDcEI7QUFDUjtBQUM4QjtBQUNwQjtBQUN3RztBQUMxSixpRUFBZSw2Q0FBVyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWZvY3VzLWxvY2tcXGRpc3RcXGVzMjAxNVxcVUkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEZvY3VzTG9ja1VJIGZyb20gJy4vTG9jayc7XG5pbXBvcnQgQXV0b0ZvY3VzSW5zaWRlIGZyb20gJy4vQXV0b0ZvY3VzSW5zaWRlJztcbmltcG9ydCBNb3ZlRm9jdXNJbnNpZGUsIHsgdXNlRm9jdXNJbnNpZGUgfSBmcm9tICcuL01vdmVGb2N1c0luc2lkZSc7XG5pbXBvcnQgRnJlZUZvY3VzSW5zaWRlIGZyb20gJy4vRnJlZUZvY3VzSW5zaWRlJztcbmltcG9ydCBJbkZvY3VzR3VhcmQgZnJvbSAnLi9Gb2N1c0d1YXJkJztcbmltcG9ydCB7IHVzZUZvY3VzQ29udHJvbGxlciwgdXNlRm9jdXNTY29wZSB9IGZyb20gJy4vdXNlLWZvY3VzLXNjb3BlJztcbmltcG9ydCB7IHVzZUZvY3VzU3RhdGUgfSBmcm9tICcuL3VzZS1mb2N1cy1zdGF0ZSc7XG5leHBvcnQgeyBBdXRvRm9jdXNJbnNpZGUsIE1vdmVGb2N1c0luc2lkZSwgRnJlZUZvY3VzSW5zaWRlLCBJbkZvY3VzR3VhcmQsIEZvY3VzTG9ja1VJLCB1c2VGb2N1c0luc2lkZSwgdXNlRm9jdXNDb250cm9sbGVyLCB1c2VGb2N1c1Njb3BlLCB1c2VGb2N1c1N0YXRlIH07XG5leHBvcnQgZGVmYXVsdCBGb2N1c0xvY2tVSTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/UI.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/index.js":
/*!*********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/index.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Combination__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Combination */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/Combination.js\");\n/* harmony import */ var _UI__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./UI */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/UI.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _UI__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _UI__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Combination__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFzQztBQUNqQjtBQUNyQixpRUFBZSxvREFBUyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWZvY3VzLWxvY2tcXGRpc3RcXGVzMjAxNVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEZvY3VzTG9jayBmcm9tICcuL0NvbWJpbmF0aW9uJztcbmV4cG9ydCAqIGZyb20gJy4vVUknO1xuZXhwb3J0IGRlZmF1bHQgRm9jdXNMb2NrOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/medium.js":
/*!**********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/medium.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mediumBlur: () => (/* binding */ mediumBlur),\n/* harmony export */   mediumEffect: () => (/* binding */ mediumEffect),\n/* harmony export */   mediumFocus: () => (/* binding */ mediumFocus),\n/* harmony export */   mediumSidecar: () => (/* binding */ mediumSidecar)\n/* harmony export */ });\n/* harmony import */ var use_sidecar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sidecar */ \"(pages-dir-browser)/../../node_modules/.pnpm/use-sidecar@1.1.3_@types+react@18.3.23_react@19.1.0/node_modules/use-sidecar/dist/es2015/index.js\");\n\nvar mediumFocus = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createMedium)({}, function (_ref) {\n  var target = _ref.target,\n    currentTarget = _ref.currentTarget;\n  return {\n    target: target,\n    currentTarget: currentTarget\n  };\n});\nvar mediumBlur = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createMedium)();\nvar mediumEffect = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createMedium)();\nvar mediumSidecar = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createSidecarMedium)({\n  async: true,\n  ssr: typeof document !== 'undefined'\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L21lZGl1bS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFnRTtBQUN6RCxrQkFBa0IseURBQVksR0FBRztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ00saUJBQWlCLHlEQUFZO0FBQzdCLG1CQUFtQix5REFBWTtBQUMvQixvQkFBb0IsZ0VBQW1CO0FBQzlDO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC1mb2N1cy1sb2NrQDIuMTMuNl9AdHlwZXMrcmVhY3RAMTguMy4yM19yZWFjdEAxOS4xLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtZm9jdXMtbG9ja1xcZGlzdFxcZXMyMDE1XFxtZWRpdW0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlTWVkaXVtLCBjcmVhdGVTaWRlY2FyTWVkaXVtIH0gZnJvbSAndXNlLXNpZGVjYXInO1xuZXhwb3J0IHZhciBtZWRpdW1Gb2N1cyA9IGNyZWF0ZU1lZGl1bSh7fSwgZnVuY3Rpb24gKF9yZWYpIHtcbiAgdmFyIHRhcmdldCA9IF9yZWYudGFyZ2V0LFxuICAgIGN1cnJlbnRUYXJnZXQgPSBfcmVmLmN1cnJlbnRUYXJnZXQ7XG4gIHJldHVybiB7XG4gICAgdGFyZ2V0OiB0YXJnZXQsXG4gICAgY3VycmVudFRhcmdldDogY3VycmVudFRhcmdldFxuICB9O1xufSk7XG5leHBvcnQgdmFyIG1lZGl1bUJsdXIgPSBjcmVhdGVNZWRpdW0oKTtcbmV4cG9ydCB2YXIgbWVkaXVtRWZmZWN0ID0gY3JlYXRlTWVkaXVtKCk7XG5leHBvcnQgdmFyIG1lZGl1bVNpZGVjYXIgPSBjcmVhdGVTaWRlY2FyTWVkaXVtKHtcbiAgYXN5bmM6IHRydWUsXG4gIHNzcjogdHlwZW9mIGRvY3VtZW50ICE9PSAndW5kZWZpbmVkJ1xufSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/medium.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/nano-events.js":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/nano-events.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNanoEvents: () => (/* binding */ createNanoEvents)\n/* harmony export */ });\nvar createNanoEvents = function createNanoEvents() {\n  return {\n    emit: function emit(event) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      for (var i = 0, callbacks = this.events[event] || [], length = callbacks.length; i < length; i++) {\n        callbacks[i].apply(callbacks, args);\n      }\n    },\n    events: {},\n    on: function on(event, cb) {\n      var _this$events,\n        _this = this;\n      ((_this$events = this.events)[event] || (_this$events[event] = [])).push(cb);\n      return function () {\n        var _this$events$event;\n        _this.events[event] = (_this$events$event = _this.events[event]) == null ? void 0 : _this$events$event.filter(function (i) {\n          return cb !== i;\n        });\n      };\n    }\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/nano-events.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/scope.js":
/*!*********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/scope.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusScope: () => (/* binding */ focusScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar focusScope = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L3Njb3BlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzQztBQUMvQiw4QkFBOEIsb0RBQWEiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LWZvY3VzLWxvY2tAMi4xMy42X0B0eXBlcytyZWFjdEAxOC4zLjIzX3JlYWN0QDE5LjEuMFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1mb2N1cy1sb2NrXFxkaXN0XFxlczIwMTVcXHNjb3BlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5leHBvcnQgdmFyIGZvY3VzU2NvcGUgPSAvKiNfX1BVUkVfXyovY3JlYXRlQ29udGV4dCh1bmRlZmluZWQpOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/scope.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/use-focus-scope.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/use-focus-scope.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFocusController: () => (/* binding */ useFocusController),\n/* harmony export */   useFocusScope: () => (/* binding */ useFocusScope)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(pages-dir-browser)/../../node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _scope__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./scope */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/scope.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./medium */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/medium.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/util.js\");\n\n\n\n\n\nvar collapseRefs = function collapseRefs(shards) {\n  return shards.map(_util__WEBPACK_IMPORTED_MODULE_2__.extractRef).filter(Boolean);\n};\nvar withMedium = function withMedium(fn) {\n  return new Promise(function (resolve) {\n    return _medium__WEBPACK_IMPORTED_MODULE_3__.mediumEffect.useMedium(function () {\n      resolve(fn.apply(void 0, arguments));\n    });\n  });\n};\nvar useFocusController = function useFocusController() {\n  for (var _len = arguments.length, shards = new Array(_len), _key = 0; _key < _len; _key++) {\n    shards[_key] = arguments[_key];\n  }\n  if (!shards.length) {\n    throw new Error('useFocusController requires at least one target element');\n  }\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(shards);\n  ref.current = shards;\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return {\n      autoFocus: function autoFocus(focusOptions) {\n        if (focusOptions === void 0) {\n          focusOptions = {};\n        }\n        return withMedium(function (car) {\n          return car.moveFocusInside(collapseRefs(ref.current), null, focusOptions);\n        });\n      },\n      focusNext: function focusNext(options) {\n        return withMedium(function (car) {\n          car.moveFocusInside(collapseRefs(ref.current), null);\n          car.focusNextElement(document.activeElement, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            scope: collapseRefs(ref.current)\n          }, options));\n        });\n      },\n      focusPrev: function focusPrev(options) {\n        return withMedium(function (car) {\n          car.moveFocusInside(collapseRefs(ref.current), null);\n          car.focusPrevElement(document.activeElement, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            scope: collapseRefs(ref.current)\n          }, options));\n        });\n      },\n      focusFirst: function focusFirst(options) {\n        return withMedium(function (car) {\n          car.focusFirstElement(collapseRefs(ref.current), options);\n        });\n      },\n      focusLast: function focusLast(options) {\n        return withMedium(function (car) {\n          car.focusLastElement(collapseRefs(ref.current), options);\n        });\n      }\n    };\n  }, []);\n};\nvar useFocusScope = function useFocusScope() {\n  var scope = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_scope__WEBPACK_IMPORTED_MODULE_4__.focusScope);\n  if (!scope) {\n    throw new Error('FocusLock is required to operate with FocusScope');\n  }\n  return useFocusController.apply(void 0, [scope.observed].concat(scope.shards));\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/use-focus-scope.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/use-focus-state.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/use-focus-state.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFocusState: () => (/* binding */ useFocusState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _nano_events__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./nano-events */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/nano-events.js\");\n\n\nvar mainbus = (0,_nano_events__WEBPACK_IMPORTED_MODULE_1__.createNanoEvents)();\nvar subscribeCounter = 0;\nvar onFocusIn = function onFocusIn(event) {\n  return mainbus.emit('assign', event.target);\n};\nvar onFocusOut = function onFocusOut(event) {\n  return mainbus.emit('reset', event.target);\n};\nvar useDocumentFocusSubscribe = function useDocumentFocusSubscribe() {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (!subscribeCounter) {\n      document.addEventListener('focusin', onFocusIn);\n      document.addEventListener('focusout', onFocusOut);\n    }\n    subscribeCounter += 1;\n    return function () {\n      subscribeCounter -= 1;\n      if (!subscribeCounter) {\n        document.removeEventListener('focusin', onFocusIn);\n        document.removeEventListener('focusout', onFocusOut);\n      }\n    };\n  }, []);\n};\nvar getFocusState = function getFocusState(target, current) {\n  if (target === current) {\n    return 'self';\n  }\n  if (current.contains(target)) {\n    return 'within';\n  }\n  return 'within-boundary';\n};\nvar useFocusState = function useFocusState(callbacks) {\n  if (callbacks === void 0) {\n    callbacks = {};\n  }\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),\n    active = _useState[0],\n    setActive = _useState[1];\n  var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(''),\n    state = _useState2[0],\n    setState = _useState2[1];\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  var focusState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n  var stateTracker = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (ref.current) {\n      var isAlreadyFocused = ref.current === document.activeElement || ref.current.contains(document.activeElement);\n      setActive(isAlreadyFocused);\n      setState(getFocusState(document.activeElement, ref.current));\n      if (isAlreadyFocused && callbacks.onFocus) {\n        callbacks.onFocus();\n      }\n    }\n  }, []);\n  var onFocus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (e) {\n    focusState.current = {\n      focused: true,\n      state: getFocusState(e.target, e.currentTarget)\n    };\n  }, []);\n  useDocumentFocusSubscribe();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    var fout = mainbus.on('reset', function () {\n      focusState.current = {};\n    });\n    var fin = mainbus.on('assign', function () {\n      var newState = focusState.current.focused || false;\n      setActive(newState);\n      setState(focusState.current.state || '');\n      if (newState !== stateTracker.current) {\n        stateTracker.current = newState;\n        if (newState) {\n          callbacks.onFocus && callbacks.onFocus();\n        } else {\n          callbacks.onBlur && callbacks.onBlur();\n        }\n      }\n    });\n    return function () {\n      fout();\n      fin();\n    };\n  }, []);\n  return {\n    active: active,\n    state: state,\n    onFocus: onFocus,\n    ref: ref\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/use-focus-state.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/util.js":
/*!********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/util.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deferAction: () => (/* binding */ deferAction),\n/* harmony export */   extractRef: () => (/* binding */ extractRef),\n/* harmony export */   inlineProp: () => (/* binding */ inlineProp)\n/* harmony export */ });\nfunction deferAction(action) {\n  setTimeout(action, 1);\n}\nvar inlineProp = function inlineProp(name, value) {\n  var obj = {};\n  obj[name] = value;\n  return obj;\n};\nvar extractRef = function extractRef(ref) {\n  return ref && 'current' in ref ? ref.current : ref;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L3V0aWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtZm9jdXMtbG9ja0AyLjEzLjZfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWZvY3VzLWxvY2tcXGRpc3RcXGVzMjAxNVxcdXRpbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZGVmZXJBY3Rpb24oYWN0aW9uKSB7XG4gIHNldFRpbWVvdXQoYWN0aW9uLCAxKTtcbn1cbmV4cG9ydCB2YXIgaW5saW5lUHJvcCA9IGZ1bmN0aW9uIGlubGluZVByb3AobmFtZSwgdmFsdWUpIHtcbiAgdmFyIG9iaiA9IHt9O1xuICBvYmpbbmFtZV0gPSB2YWx1ZTtcbiAgcmV0dXJuIG9iajtcbn07XG5leHBvcnQgdmFyIGV4dHJhY3RSZWYgPSBmdW5jdGlvbiBleHRyYWN0UmVmKHJlZikge1xuICByZXR1cm4gcmVmICYmICdjdXJyZW50JyBpbiByZWYgPyByZWYuY3VycmVudCA6IHJlZjtcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-focus-lock@2.13.6_@types+react@18.3.23_react@19.1.0/node_modules/react-focus-lock/dist/es2015/util.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/component.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/component.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveScrollBar: () => (/* binding */ RemoveScrollBar),\n/* harmony export */   lockAttribute: () => (/* binding */ lockAttribute),\n/* harmony export */   useLockAttribute: () => (/* binding */ useLockAttribute)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_style_singleton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-style-singleton */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/constants.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/utils.js\");\n\n\n\n\nvar Style = (0,react_style_singleton__WEBPACK_IMPORTED_MODULE_1__.styleSingleton)();\nvar lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(_constants__WEBPACK_IMPORTED_MODULE_2__.noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.zeroRightClassName, \" .\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.fullWidthClassName, \" .\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nvar useLockAttribute = function () {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nvar RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () { return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.getGapWidth)(gapMode); }, [gapMode]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/component.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/constants.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/constants.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fullWidthClassName: () => (/* binding */ fullWidthClassName),\n/* harmony export */   noScrollbarsClassName: () => (/* binding */ noScrollbarsClassName),\n/* harmony export */   removedBarSizeVariable: () => (/* binding */ removedBarSizeVariable),\n/* harmony export */   zeroRightClassName: () => (/* binding */ zeroRightClassName)\n/* harmony export */ });\nvar zeroRightClassName = 'right-scroll-bar-position';\nvar fullWidthClassName = 'width-before-scroll-bar';\nvar noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nvar removedBarSizeVariable = '--removed-body-scroll-bar-size';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtcmVtb3ZlLXNjcm9sbC1iYXJAMi4zXzdiNDY3NWYyNGM2NGNmNGU4YjM1MzVhYjY5OGRkYTczL25vZGVfbW9kdWxlcy9yZWFjdC1yZW1vdmUtc2Nyb2xsLWJhci9kaXN0L2VzMjAxNS9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFPO0FBQ0E7QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ08iLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXJlbW92ZS1zY3JvbGwtYmFyQDIuM183YjQ2NzVmMjRjNjRjZjRlOGIzNTM1YWI2OThkZGE3M1xcbm9kZV9tb2R1bGVzXFxyZWFjdC1yZW1vdmUtc2Nyb2xsLWJhclxcZGlzdFxcZXMyMDE1XFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciB6ZXJvUmlnaHRDbGFzc05hbWUgPSAncmlnaHQtc2Nyb2xsLWJhci1wb3NpdGlvbic7XG5leHBvcnQgdmFyIGZ1bGxXaWR0aENsYXNzTmFtZSA9ICd3aWR0aC1iZWZvcmUtc2Nyb2xsLWJhcic7XG5leHBvcnQgdmFyIG5vU2Nyb2xsYmFyc0NsYXNzTmFtZSA9ICd3aXRoLXNjcm9sbC1iYXJzLWhpZGRlbic7XG4vKipcbiAqIE5hbWUgb2YgYSBDU1MgdmFyaWFibGUgY29udGFpbmluZyB0aGUgYW1vdW50IG9mIFwiaGlkZGVuXCIgc2Nyb2xsYmFyXG4gKiAhIG1pZ2h0IGJlIHVuZGVmaW5lZCAhIHVzZSB3aWxsIGZhbGxiYWNrIVxuICovXG5leHBvcnQgdmFyIHJlbW92ZWRCYXJTaXplVmFyaWFibGUgPSAnLS1yZW1vdmVkLWJvZHktc2Nyb2xsLWJhci1zaXplJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/constants.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/index.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/index.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveScrollBar: () => (/* reexport safe */ _component__WEBPACK_IMPORTED_MODULE_0__.RemoveScrollBar),\n/* harmony export */   fullWidthClassName: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.fullWidthClassName),\n/* harmony export */   getGapWidth: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_2__.getGapWidth),\n/* harmony export */   noScrollbarsClassName: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.noScrollbarsClassName),\n/* harmony export */   removedBarSizeVariable: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.removedBarSizeVariable),\n/* harmony export */   zeroRightClassName: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.zeroRightClassName)\n/* harmony export */ });\n/* harmony import */ var _component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./component */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/component.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/constants.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/utils.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtcmVtb3ZlLXNjcm9sbC1iYXJAMi4zXzdiNDY3NWYyNGM2NGNmNGU4YjM1MzVhYjY5OGRkYTczL25vZGVfbW9kdWxlcy9yZWFjdC1yZW1vdmUtc2Nyb2xsLWJhci9kaXN0L2VzMjAxNS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBOEM7QUFDc0U7QUFDOUU7QUFDMEYiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXJlbW92ZS1zY3JvbGwtYmFyQDIuM183YjQ2NzVmMjRjNjRjZjRlOGIzNTM1YWI2OThkZGE3M1xcbm9kZV9tb2R1bGVzXFxyZWFjdC1yZW1vdmUtc2Nyb2xsLWJhclxcZGlzdFxcZXMyMDE1XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBSZW1vdmVTY3JvbGxCYXIgfSBmcm9tICcuL2NvbXBvbmVudCc7XG5pbXBvcnQgeyB6ZXJvUmlnaHRDbGFzc05hbWUsIGZ1bGxXaWR0aENsYXNzTmFtZSwgbm9TY3JvbGxiYXJzQ2xhc3NOYW1lLCByZW1vdmVkQmFyU2l6ZVZhcmlhYmxlIH0gZnJvbSAnLi9jb25zdGFudHMnO1xuaW1wb3J0IHsgZ2V0R2FwV2lkdGggfSBmcm9tICcuL3V0aWxzJztcbmV4cG9ydCB7IFJlbW92ZVNjcm9sbEJhciwgemVyb1JpZ2h0Q2xhc3NOYW1lLCBmdWxsV2lkdGhDbGFzc05hbWUsIG5vU2Nyb2xsYmFyc0NsYXNzTmFtZSwgcmVtb3ZlZEJhclNpemVWYXJpYWJsZSwgZ2V0R2FwV2lkdGgsIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/utils.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/utils.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getGapWidth: () => (/* binding */ getGapWidth),\n/* harmony export */   zeroGap: () => (/* binding */ zeroGap)\n/* harmony export */ });\nvar zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nvar getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/utils.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/Combination.js":
/*!********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/Combination.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(pages-dir-browser)/../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _UI__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UI */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/UI.js\");\n/* harmony import */ var _sidecar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sidecar */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/sidecar.js\");\n\n\n\n\nvar ReactRemoveScroll = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function (props, ref) { return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_UI__WEBPACK_IMPORTED_MODULE_2__.RemoveScroll, (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__assign)({}, props, { ref: ref, sideCar: _sidecar__WEBPACK_IMPORTED_MODULE_1__[\"default\"] }))); });\nReactRemoveScroll.classNames = _UI__WEBPACK_IMPORTED_MODULE_2__.RemoveScroll.classNames;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactRemoveScroll);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtcmVtb3ZlLXNjcm9sbEAyLjcuMV9AdHlwZXMrcmVhY3RAMTguMy4yM19yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LXJlbW92ZS1zY3JvbGwvZGlzdC9lczIwMTUvQ29tYmluYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWlDO0FBQ0Y7QUFDSztBQUNKO0FBQ2hDLHdCQUF3Qiw2Q0FBZ0IseUJBQXlCLFFBQVEsZ0RBQW1CLENBQUMsNkNBQVksRUFBRSwrQ0FBUSxHQUFHLFdBQVcsbUJBQW1CLGdEQUFPLEVBQUUsTUFBTTtBQUNuSywrQkFBK0IsNkNBQVk7QUFDM0MsaUVBQWUsaUJBQWlCLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXJlbW92ZS1zY3JvbGxAMi43LjFfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LXJlbW92ZS1zY3JvbGxcXGRpc3RcXGVzMjAxNVxcQ29tYmluYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgX19hc3NpZ24gfSBmcm9tIFwidHNsaWJcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFJlbW92ZVNjcm9sbCB9IGZyb20gJy4vVUknO1xuaW1wb3J0IFNpZGVDYXIgZnJvbSAnLi9zaWRlY2FyJztcbnZhciBSZWFjdFJlbW92ZVNjcm9sbCA9IFJlYWN0LmZvcndhcmRSZWYoZnVuY3Rpb24gKHByb3BzLCByZWYpIHsgcmV0dXJuIChSZWFjdC5jcmVhdGVFbGVtZW50KFJlbW92ZVNjcm9sbCwgX19hc3NpZ24oe30sIHByb3BzLCB7IHJlZjogcmVmLCBzaWRlQ2FyOiBTaWRlQ2FyIH0pKSk7IH0pO1xuUmVhY3RSZW1vdmVTY3JvbGwuY2xhc3NOYW1lcyA9IFJlbW92ZVNjcm9sbC5jbGFzc05hbWVzO1xuZXhwb3J0IGRlZmF1bHQgUmVhY3RSZW1vdmVTY3JvbGw7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/Combination.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/SideEffect.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/SideEffect.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveScrollSideCar: () => (/* binding */ RemoveScrollSideCar),\n/* harmony export */   getDeltaXY: () => (/* binding */ getDeltaXY),\n/* harmony export */   getTouchXY: () => (/* binding */ getTouchXY)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(pages-dir-browser)/../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_remove_scroll_bar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-remove-scroll-bar */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/index.js\");\n/* harmony import */ var react_style_singleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-style-singleton */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/index.js\");\n/* harmony import */ var _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./aggresiveCapture */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js\");\n/* harmony import */ var _handleScroll__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./handleScroll */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/handleScroll.js\");\n\n\n\n\n\n\nvar getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nvar getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nfunction RemoveScrollSideCar(props) {\n    var shouldPreventQueue = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    var touchStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef([0, 0]);\n    var activeAxis = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    var id = react__WEBPACK_IMPORTED_MODULE_0__.useState(idCounter++)[0];\n    var Style = react__WEBPACK_IMPORTED_MODULE_0__.useState(react_style_singleton__WEBPACK_IMPORTED_MODULE_2__.styleSingleton)[0];\n    var lastProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__spreadArray)([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = (0,_handleScroll__WEBPACK_IMPORTED_MODULE_4__.locationCouldBeScrolled)(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = (0,_handleScroll__WEBPACK_IMPORTED_MODULE_4__.locationCouldBeScrolled)(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return (0,_handleScroll__WEBPACK_IMPORTED_MODULE_4__.handleScroll)(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n        inert ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(react_remove_scroll_bar__WEBPACK_IMPORTED_MODULE_1__.RemoveScrollBar, { noRelative: props.noRelative, gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/SideEffect.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/UI.js":
/*!***********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/UI.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveScroll: () => (/* binding */ RemoveScroll)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(pages-dir-browser)/../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_remove_scroll_bar_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-remove-scroll-bar/constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3_7b4675f24c64cf4e8b3535ab698dda73/node_modules/react-remove-scroll-bar/dist/es2015/constants.js\");\n/* harmony import */ var use_callback_ref__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-callback-ref */ \"(pages-dir-browser)/../../node_modules/.pnpm/use-callback-ref@1.3.3_@types+react@18.3.23_react@19.1.0/node_modules/use-callback-ref/dist/es2015/index.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./medium */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/medium.js\");\n\n\n\n\n\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function (props, parentRef) {\n    var ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    var _a = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noRelative = props.noRelative, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noRelative\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = (0,use_callback_ref__WEBPACK_IMPORTED_MODULE_3__.useMergeRefs)([ref, parentRef]);\n    var containerProps = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, rest), callbacks);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n        enabled && (react__WEBPACK_IMPORTED_MODULE_0__.createElement(SideCar, { sideCar: _medium__WEBPACK_IMPORTED_MODULE_4__.effectCar, removeScrollBar: removeScrollBar, shards: shards, noRelative: noRelative, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children), (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, containerProps), { ref: containerRef }))) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(Container, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: react_remove_scroll_bar_constants__WEBPACK_IMPORTED_MODULE_1__.fullWidthClassName,\n    zeroRight: react_remove_scroll_bar_constants__WEBPACK_IMPORTED_MODULE_1__.zeroRightClassName,\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/UI.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   nonPassive: () => (/* binding */ nonPassive)\n/* harmony export */ });\nvar passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nvar nonPassive = passiveSupported ? { passive: false } : false;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtcmVtb3ZlLXNjcm9sbEAyLjcuMV9AdHlwZXMrcmVhY3RAMTguMy4yM19yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LXJlbW92ZS1zY3JvbGwvZGlzdC9lczIwMTUvYWdncmVzaXZlQ2FwdHVyZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EsOENBQThDO0FBQzlDO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sc0NBQXNDLGlCQUFpQiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtcmVtb3ZlLXNjcm9sbEAyLjcuMV9AdHlwZXMrcmVhY3RAMTguMy4yM19yZWFjdEAxOS4xLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtcmVtb3ZlLXNjcm9sbFxcZGlzdFxcZXMyMDE1XFxhZ2dyZXNpdmVDYXB0dXJlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBwYXNzaXZlU3VwcG9ydGVkID0gZmFsc2U7XG5pZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICB0cnkge1xuICAgICAgICB2YXIgb3B0aW9ucyA9IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh7fSwgJ3Bhc3NpdmUnLCB7XG4gICAgICAgICAgICBnZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgICAgICBwYXNzaXZlU3VwcG9ydGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuICAgICAgICAvLyBAdHMtaWdub3JlXG4gICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCd0ZXN0Jywgb3B0aW9ucywgb3B0aW9ucyk7XG4gICAgICAgIC8vIEB0cy1pZ25vcmVcbiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Rlc3QnLCBvcHRpb25zLCBvcHRpb25zKTtcbiAgICB9XG4gICAgY2F0Y2ggKGVycikge1xuICAgICAgICBwYXNzaXZlU3VwcG9ydGVkID0gZmFsc2U7XG4gICAgfVxufVxuZXhwb3J0IHZhciBub25QYXNzaXZlID0gcGFzc2l2ZVN1cHBvcnRlZCA/IHsgcGFzc2l2ZTogZmFsc2UgfSA6IGZhbHNlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/handleScroll.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/handleScroll.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleScroll: () => (/* binding */ handleScroll),\n/* harmony export */   locationCouldBeScrolled: () => (/* binding */ locationCouldBeScrolled)\n/* harmony export */ });\nvar alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nvar locationCouldBeScrolled = function (axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nvar handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        if (!target) {\n            break;\n        }\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        var parent_1 = target.parentNode;\n        // we will \"bubble\" from ShadowDom in case we are, or just to the parent in normal case\n        // this is the same logic used in focus-lock\n        target = (parent_1 && parent_1.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? parent_1.host : parent_1);\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScroll) < 1) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScrollTop) < 1) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/handleScroll.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/index.js":
/*!**************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/index.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveScroll: () => (/* reexport safe */ _Combination__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Combination__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Combination */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtcmVtb3ZlLXNjcm9sbEAyLjcuMV9AdHlwZXMrcmVhY3RAMTguMy4yM19yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LXJlbW92ZS1zY3JvbGwvZGlzdC9lczIwMTUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7QUFDakIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXJlbW92ZS1zY3JvbGxAMi43LjFfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LXJlbW92ZS1zY3JvbGxcXGRpc3RcXGVzMjAxNVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlbW92ZVNjcm9sbCBmcm9tICcuL0NvbWJpbmF0aW9uJztcbmV4cG9ydCB7IFJlbW92ZVNjcm9sbCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/medium.js":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/medium.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   effectCar: () => (/* binding */ effectCar)\n/* harmony export */ });\n/* harmony import */ var use_sidecar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sidecar */ \"(pages-dir-browser)/../../node_modules/.pnpm/use-sidecar@1.1.3_@types+react@18.3.23_react@19.1.0/node_modules/use-sidecar/dist/es2015/index.js\");\n\nvar effectCar = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createSidecarMedium)();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtcmVtb3ZlLXNjcm9sbEAyLjcuMV9AdHlwZXMrcmVhY3RAMTguMy4yM19yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LXJlbW92ZS1zY3JvbGwvZGlzdC9lczIwMTUvbWVkaXVtLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtEO0FBQzNDLGdCQUFnQixnRUFBbUIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXJlbW92ZS1zY3JvbGxAMi43LjFfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LXJlbW92ZS1zY3JvbGxcXGRpc3RcXGVzMjAxNVxcbWVkaXVtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVNpZGVjYXJNZWRpdW0gfSBmcm9tICd1c2Utc2lkZWNhcic7XG5leHBvcnQgdmFyIGVmZmVjdENhciA9IGNyZWF0ZVNpZGVjYXJNZWRpdW0oKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/medium.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/sidecar.js":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/sidecar.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var use_sidecar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sidecar */ \"(pages-dir-browser)/../../node_modules/.pnpm/use-sidecar@1.1.3_@types+react@18.3.23_react@19.1.0/node_modules/use-sidecar/dist/es2015/index.js\");\n/* harmony import */ var _SideEffect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SideEffect */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/SideEffect.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./medium */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/medium.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.exportSidecar)(_medium__WEBPACK_IMPORTED_MODULE_1__.effectCar, _SideEffect__WEBPACK_IMPORTED_MODULE_2__.RemoveScrollSideCar));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtcmVtb3ZlLXNjcm9sbEAyLjcuMV9AdHlwZXMrcmVhY3RAMTguMy4yM19yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LXJlbW92ZS1zY3JvbGwvZGlzdC9lczIwMTUvc2lkZWNhci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTRDO0FBQ087QUFDZDtBQUNyQyxpRUFBZSwwREFBYSxDQUFDLDhDQUFTLEVBQUUsNERBQW1CLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtcmVtb3ZlLXNjcm9sbEAyLjcuMV9AdHlwZXMrcmVhY3RAMTguMy4yM19yZWFjdEAxOS4xLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtcmVtb3ZlLXNjcm9sbFxcZGlzdFxcZXMyMDE1XFxzaWRlY2FyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGV4cG9ydFNpZGVjYXIgfSBmcm9tICd1c2Utc2lkZWNhcic7XG5pbXBvcnQgeyBSZW1vdmVTY3JvbGxTaWRlQ2FyIH0gZnJvbSAnLi9TaWRlRWZmZWN0JztcbmltcG9ydCB7IGVmZmVjdENhciB9IGZyb20gJy4vbWVkaXVtJztcbmV4cG9ydCBkZWZhdWx0IGV4cG9ydFNpZGVjYXIoZWZmZWN0Q2FyLCBSZW1vdmVTY3JvbGxTaWRlQ2FyKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/sidecar.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/component.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/component.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   styleSingleton: () => (/* binding */ styleSingleton)\n/* harmony export */ });\n/* harmony import */ var _hook__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hook */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/hook.js\");\n\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nvar styleSingleton = function () {\n    var useStyle = (0,_hook__WEBPACK_IMPORTED_MODULE_0__.styleHookSingleton)();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3Qtc3R5bGUtc2luZ2xldG9uQDIuMi4zXzEzNDIxNTBhODE1YTJlODM4YTA0Zjc0Nzg2MDk4ZWMxL25vZGVfbW9kdWxlcy9yZWFjdC1zdHlsZS1zaW5nbGV0b24vZGlzdC9lczIwMTUvY29tcG9uZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRDO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsbUJBQW1CLHlEQUFrQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3Qtc3R5bGUtc2luZ2xldG9uQDIuMi4zXzEzNDIxNTBhODE1YTJlODM4YTA0Zjc0Nzg2MDk4ZWMxXFxub2RlX21vZHVsZXNcXHJlYWN0LXN0eWxlLXNpbmdsZXRvblxcZGlzdFxcZXMyMDE1XFxjb21wb25lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc3R5bGVIb29rU2luZ2xldG9uIH0gZnJvbSAnLi9ob29rJztcbi8qKlxuICogY3JlYXRlIGEgQ29tcG9uZW50IHRvIGFkZCBzdHlsZXMgb24gZGVtYW5kXG4gKiAtIHN0eWxlcyBhcmUgYWRkZWQgd2hlbiBmaXJzdCBpbnN0YW5jZSBpcyBtb3VudGVkXG4gKiAtIHN0eWxlcyBhcmUgcmVtb3ZlZCB3aGVuIHRoZSBsYXN0IGluc3RhbmNlIGlzIHVubW91bnRlZFxuICogLSBjaGFuZ2luZyBzdHlsZXMgaW4gcnVudGltZSBkb2VzIG5vdGhpbmcgdW5sZXNzIGR5bmFtaWMgaXMgc2V0LiBCdXQgd2l0aCBtdWx0aXBsZSBjb21wb25lbnRzIHRoYXQgY2FuIGxlYWQgdG8gdGhlIHVuZGVmaW5lZCBiZWhhdmlvclxuICovXG5leHBvcnQgdmFyIHN0eWxlU2luZ2xldG9uID0gZnVuY3Rpb24gKCkge1xuICAgIHZhciB1c2VTdHlsZSA9IHN0eWxlSG9va1NpbmdsZXRvbigpO1xuICAgIHZhciBTaGVldCA9IGZ1bmN0aW9uIChfYSkge1xuICAgICAgICB2YXIgc3R5bGVzID0gX2Euc3R5bGVzLCBkeW5hbWljID0gX2EuZHluYW1pYztcbiAgICAgICAgdXNlU3R5bGUoc3R5bGVzLCBkeW5hbWljKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfTtcbiAgICByZXR1cm4gU2hlZXQ7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/component.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/hook.js":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/hook.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   styleHookSingleton: () => (/* binding */ styleHookSingleton)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _singleton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./singleton */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/singleton.js\");\n\n\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nvar styleHookSingleton = function () {\n    var sheet = (0,_singleton__WEBPACK_IMPORTED_MODULE_1__.stylesheetSingleton)();\n    return function (styles, isDynamic) {\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3Qtc3R5bGUtc2luZ2xldG9uQDIuMi4zXzEzNDIxNTBhODE1YTJlODM4YTA0Zjc0Nzg2MDk4ZWMxL25vZGVfbW9kdWxlcy9yZWFjdC1zdHlsZS1zaW5nbGV0b24vZGlzdC9lczIwMTUvaG9vay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStCO0FBQ21CO0FBQ2xEO0FBQ0E7QUFDQSxTQUFTLHNCQUFzQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixpQkFBaUI7QUFDckM7QUFDTztBQUNQLGdCQUFnQiwrREFBbUI7QUFDbkM7QUFDQSxRQUFRLDRDQUFlO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXN0eWxlLXNpbmdsZXRvbkAyLjIuM18xMzQyMTUwYTgxNWEyZTgzOGEwNGY3NDc4NjA5OGVjMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC1zdHlsZS1zaW5nbGV0b25cXGRpc3RcXGVzMjAxNVxcaG9vay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBzdHlsZXNoZWV0U2luZ2xldG9uIH0gZnJvbSAnLi9zaW5nbGV0b24nO1xuLyoqXG4gKiBjcmVhdGVzIGEgaG9vayB0byBjb250cm9sIHN0eWxlIHNpbmdsZXRvblxuICogQHNlZSB7QGxpbmsgc3R5bGVTaW5nbGV0b259IGZvciBhIHNhZmVyIGNvbXBvbmVudCB2ZXJzaW9uXG4gKiBAZXhhbXBsZVxuICogYGBgdHN4XG4gKiBjb25zdCB1c2VTdHlsZSA9IHN0eWxlSG9va1NpbmdsZXRvbigpO1xuICogLy8vXG4gKiB1c2VTdHlsZSgnYm9keSB7IG92ZXJmbG93OiBoaWRkZW59Jyk7XG4gKi9cbmV4cG9ydCB2YXIgc3R5bGVIb29rU2luZ2xldG9uID0gZnVuY3Rpb24gKCkge1xuICAgIHZhciBzaGVldCA9IHN0eWxlc2hlZXRTaW5nbGV0b24oKTtcbiAgICByZXR1cm4gZnVuY3Rpb24gKHN0eWxlcywgaXNEeW5hbWljKSB7XG4gICAgICAgIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICBzaGVldC5hZGQoc3R5bGVzKTtcbiAgICAgICAgICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgc2hlZXQucmVtb3ZlKCk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9LCBbc3R5bGVzICYmIGlzRHluYW1pY10pO1xuICAgIH07XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/hook.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/index.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/index.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   styleHookSingleton: () => (/* reexport safe */ _hook__WEBPACK_IMPORTED_MODULE_2__.styleHookSingleton),\n/* harmony export */   styleSingleton: () => (/* reexport safe */ _component__WEBPACK_IMPORTED_MODULE_0__.styleSingleton),\n/* harmony export */   stylesheetSingleton: () => (/* reexport safe */ _singleton__WEBPACK_IMPORTED_MODULE_1__.stylesheetSingleton)\n/* harmony export */ });\n/* harmony import */ var _component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./component */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/component.js\");\n/* harmony import */ var _singleton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./singleton */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/singleton.js\");\n/* harmony import */ var _hook__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hook */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/hook.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3Qtc3R5bGUtc2luZ2xldG9uQDIuMi4zXzEzNDIxNTBhODE1YTJlODM4YTA0Zjc0Nzg2MDk4ZWMxL25vZGVfbW9kdWxlcy9yZWFjdC1zdHlsZS1zaW5nbGV0b24vZGlzdC9lczIwMTUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZDO0FBQ0s7QUFDTiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3Qtc3R5bGUtc2luZ2xldG9uQDIuMi4zXzEzNDIxNTBhODE1YTJlODM4YTA0Zjc0Nzg2MDk4ZWMxXFxub2RlX21vZHVsZXNcXHJlYWN0LXN0eWxlLXNpbmdsZXRvblxcZGlzdFxcZXMyMDE1XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBzdHlsZVNpbmdsZXRvbiB9IGZyb20gJy4vY29tcG9uZW50JztcbmV4cG9ydCB7IHN0eWxlc2hlZXRTaW5nbGV0b24gfSBmcm9tICcuL3NpbmdsZXRvbic7XG5leHBvcnQgeyBzdHlsZUhvb2tTaW5nbGV0b24gfSBmcm9tICcuL2hvb2snO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/singleton.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/singleton.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stylesheetSingleton: () => (/* binding */ stylesheetSingleton)\n/* harmony export */ });\n/* harmony import */ var get_nonce__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! get-nonce */ \"(pages-dir-browser)/../../node_modules/.pnpm/get-nonce@1.0.1/node_modules/get-nonce/dist/es2015/index.js\");\n\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = (0,get_nonce__WEBPACK_IMPORTED_MODULE_0__.getNonce)();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nvar stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/react-style-singleton@2.2.3_1342150a815a2e838a04f74786098ec1/node_modules/react-style-singleton/dist/es2015/singleton.js\n"));

/***/ })

});