"use strict";(()=>{var e={};e.id=4143,e.ids=[4143],e.modules={5695:(e,a,r)=>{r.r(a),r.d(a,{config:()=>p,default:()=>c,routeModule:()=>y});var t={};r.r(t),r.d(t,{default:()=>f});var n=r(93433),i=r(20264),d=r(20584),o=r(15806),m=r(94506);r(71970);let u={minecraft:{name:"Minecraft",defaultPort:25565,fields:[{name:"port",type:"number",required:!0,default:25565,description:"Port number of the server"}]},minecraftbe:{name:"Minecraft: Bedrock Edition",defaultPort:19132,fields:[{name:"port",type:"number",required:!0,default:19132,description:"Port number of the server"}]},minecraftpe:{name:"Minecraft: Pocket Edition",defaultPort:19132,fields:[{name:"port",type:"number",required:!0,default:19132,description:"Port number of the server"}]},fivem:{name:"FiveM (GTA V)",defaultPort:30120,fields:[{name:"port",type:"number",required:!0,default:30120,description:"Port number of the server"}]},csgo:{name:"Counter-Strike: Global Offensive",defaultPort:27015,fields:[{name:"port",type:"number",required:!0,default:27015,description:"Port number of the server"}]},cs2:{name:"Counter-Strike 2",defaultPort:27015,fields:[{name:"port",type:"number",required:!0,default:27015,description:"Port number of the server"}]},valheim:{name:"Valheim",defaultPort:2456,fields:[{name:"port",type:"number",required:!0,default:2456,description:"Port number of the server"},{name:"queryPort",type:"number",required:!1,description:"Query port (if different from server port)"}]},sdtd:{name:"7 Days to Die",defaultPort:26900,fields:[{name:"port",type:"number",required:!0,default:26900,description:"Game port (default: 26900)"},{name:"queryPort",type:"number",required:!1,description:"Query port (if different from game port)"}]},teamspeak3:{name:"TeamSpeak 3",defaultPort:9987,fields:[{name:"port",type:"number",required:!0,default:9987,description:"UDP port (default: 9987)"},{name:"queryPort",type:"number",required:!1,default:10011,description:"TCP query port (default: 10011)"}]},palworld:{name:"Palworld",defaultPort:8211,fields:[{name:"port",type:"number",required:!0,default:8211,description:"Game port (default: 8211)"},{name:"queryPort",type:"number",required:!1,description:"Query port (if different from game port)"}]},rust:{name:"Rust",defaultPort:28015,fields:[{name:"port",type:"number",required:!0,default:28015,description:"Game port (default: 28015)"},{name:"queryPort",type:"number",required:!1,description:"Query port (if different from game port)"},{name:"rconPort",type:"number",required:!1,default:28016,description:"RCON port (default: 28016)"},{name:"rconPassword",type:"string",required:!1,description:"RCON password (if RCON is enabled)"}]},arkse:{name:"ARK: Survival Evolved",defaultPort:7777,fields:[{name:"port",type:"number",required:!0,default:7777,description:"Game port (default: 7777)"},{name:"queryPort",type:"number",required:!1,default:27015,description:"Query port (default: 27015)"}]}};function s(){return[{id:"arkse",name:"ARK: Survival Evolved"},{id:"arma2",name:"ARMA 2"},{id:"arma3",name:"ARMA 3"},{id:"armar",name:"ARMA Reforger"},{id:"assettocorsa",name:"Assetto Corsa"},{id:"atlas",name:"Atlas"},{id:"avp2",name:"Aliens vs. Predator 2"},{id:"avp2010",name:"Aliens vs. Predator 2010"},{id:"baldursgate",name:"Baldur's Gate"},{id:"battalion1944",name:"Battalion 1944"},{id:"bf1942",name:"Battlefield 1942"},{id:"bf2",name:"Battlefield 2"},{id:"bf2142",name:"Battlefield 2142"},{id:"bf3",name:"Battlefield 3"},{id:"bf4",name:"Battlefield 4"},{id:"bfh",name:"Battlefield Hardline"},{id:"bfv",name:"Battlefield Vietnam"},{id:"breach",name:"Breach"},{id:"breed",name:"Breed"},{id:"brink",name:"Brink"},{id:"buildandshoot",name:"Build and Shoot"},{id:"cod",name:"Call of Duty"},{id:"cod2",name:"Call of Duty 2"},{id:"cod3",name:"Call of Duty 3"},{id:"cod4",name:"Call of Duty 4: Modern Warfare"},{id:"codmw2",name:"Call of Duty: Modern Warfare 2"},{id:"codmw3",name:"Call of Duty: Modern Warfare 3"},{id:"coduo",name:"Call of Duty: United Offensive"},{id:"codwaw",name:"Call of Duty: World at War"},{id:"conanexiles",name:"Conan Exiles"},{id:"cs2",name:"Counter-Strike 2"},{id:"csgo",name:"Counter-Strike: Global Offensive"},{id:"css",name:"Counter-Strike: Source"},{id:"creativerse",name:"Creativerse"},{id:"crysis",name:"Crysis"},{id:"crysiswars",name:"Crysis Wars"},{id:"dayz",name:"DayZ"},{id:"dayzmod",name:"DayZ Mod"},{id:"dod",name:"Day of Defeat"},{id:"dods",name:"Day of Defeat: Source"},{id:"doi",name:"Day of Infamy"},{id:"doom3",name:"Doom 3"},{id:"dota2",name:"Dota 2"},{id:"eco",name:"Eco"},{id:"empyrion",name:"Empyrion - Galactic Survival"},{id:"enshrouded",name:"Enshrouded"},{id:"factorio",name:"Factorio"},{id:"farcry",name:"Far Cry"},{id:"farcry2",name:"Far Cry 2"},{id:"fear",name:"F.E.A.R."},{id:"fivem",name:"FiveM (GTA V)"},{id:"forrest",name:"The Forrest"},{id:"fs22",name:"Farming Simulator 22"},{id:"garrysmod",name:"Garry's Mod"},{id:"grav",name:"GRAV Online"},{id:"gta5m",name:"GTA V Multiplayer"},{id:"hl2dm",name:"Half-Life 2: Deathmatch"},{id:"hldm",name:"Half-Life Deathmatch"},{id:"hldms",name:"Half-Life Deathmatch: Source"},{id:"insurgency",name:"Insurgency"},{id:"insurgencysandstorm",name:"Insurgency: Sandstorm"},{id:"jc2mp",name:"Just Cause 2 Multiplayer"},{id:"jc3mp",name:"Just Cause 3 Multiplayer"},{id:"killingfloor",name:"Killing Floor"},{id:"killingfloor2",name:"Killing Floor 2"},{id:"l4d",name:"Left 4 Dead"},{id:"l4d2",name:"Left 4 Dead 2"},{id:"minecraft",name:"Minecraft"},{id:"minecraftbe",name:"Minecraft: Bedrock Edition"},{id:"minecraftpe",name:"Minecraft: Pocket Edition"},{id:"mordhau",name:"Mordhau"},{id:"mtasa",name:"Multi Theft Auto: San Andreas"},{id:"mumble",name:"Mumble"},{id:"nascarthunder2004",name:"NASCAR Thunder 2004"},{id:"naturalselection",name:"Natural Selection"},{id:"naturalselection2",name:"Natural Selection 2"},{id:"nwn",name:"Neverwinter Nights"},{id:"nwn2",name:"Neverwinter Nights 2"},{id:"onset",name:"Onset"},{id:"openttd",name:"OpenTTD"},{id:"palworld",name:"Palworld"},{id:"payday2",name:"PAYDAY 2"},{id:"pixark",name:"PixARK"},{id:"projectcars",name:"Project CARS"},{id:"projectzomboid",name:"Project Zomboid"},{id:"quake1",name:"Quake 1"},{id:"quake2",name:"Quake 2"},{id:"quake3",name:"Quake 3"},{id:"quake4",name:"Quake 4"},{id:"ragnarok",name:"Ragnarok Online"},{id:"redm",name:"RedM (Red Dead Redemption 2)"},{id:"rfactor",name:"rFactor"},{id:"ragemp",name:"RAGE:MP"},{id:"redorchestra",name:"Red Orchestra"},{id:"redorchestra2",name:"Red Orchestra 2"},{id:"redorchestraost",name:"Red Orchestra: Ostfront 41-45"},{id:"rust",name:"Rust"},{id:"samp",name:"San Andreas Multiplayer"},{id:"satisfactory",name:"Satisfactory"},{id:"sdtd",name:"7 Days to Die"},{id:"ship",name:"The Ship"},{id:"sniperelitev2",name:"Sniper Elite V2"},{id:"soldat",name:"Soldat"},{id:"source",name:"Source Engine Game"},{id:"squad",name:"Squad"},{id:"starbound",name:"Starbound"},{id:"starmade",name:"StarMade"},{id:"stationeers",name:"Stationeers"},{id:"steamquery",name:"Generic Steam Query"},{id:"subnautica",name:"Subnautica"},{id:"swat4",name:"SWAT 4"},{id:"teamfactor",name:"Team Factor"},{id:"teamfortress2",name:"Team Fortress 2"},{id:"teamspeak2",name:"TeamSpeak 2"},{id:"teamspeak3",name:"TeamSpeak 3"},{id:"terminus",name:"Terminus"},{id:"terraria",name:"Terraria"},{id:"tf2",name:"Team Fortress 2"},{id:"tfc",name:"Team Fortress Classic"},{id:"tibia",name:"Tibia"},{id:"tshock",name:"Terraria (TShock)"},{id:"unreal",name:"Unreal"},{id:"unreal2",name:"Unreal 2"},{id:"unreal3",name:"Unreal 3"},{id:"unrealtournament",name:"Unreal Tournament"},{id:"unrealtournament2003",name:"Unreal Tournament 2003"},{id:"unrealtournament2004",name:"Unreal Tournament 2004"},{id:"unrealtournament3",name:"Unreal Tournament 3"},{id:"unturned",name:"Unturned"},{id:"ut",name:"Unreal Tournament"},{id:"ut2003",name:"Unreal Tournament 2003"},{id:"ut2004",name:"Unreal Tournament 2004"},{id:"ut3",name:"Unreal Tournament 3"},{id:"vrising",name:"V Rising"},{id:"warsow",name:"Warsow"},{id:"won",name:"World Opponent Network"},{id:"wurm",name:"Wurm Unlimited"},{id:"zomboid",name:"Project Zomboid"}].sort((e,a)=>e.name.localeCompare(a.name))}function l(e){let a={"7d2d":"sdtd","7days":"sdtd","7daystodie":"sdtd",minecraft:"minecraft",mc:"minecraft",mcbe:"minecraftbe",mcpe:"minecraftpe",csgo:"csgo",cs2:"cs2",counterstrike:"cs2",rust:"rust",gmod:"garrysmod",garrysmod:"garrysmod",tf2:"tf2",teamfortress2:"tf2",ark:"arkse",arkse:"arkse",valheim:"valheim",fivem:"fivem",gtav:"fivem",gtamp:"gta5m",l4d:"l4d",l4d2:"l4d2",left4dead:"l4d",left4dead2:"l4d2",projectzomboid:"zomboid",pz:"zomboid",palworld:"palworld",pal:"palworld"};if(s().some(a=>a.id===e))return e;let r=e.toLowerCase().replace(/[^a-z0-9]/g,"");return a[r]?a[r]:null}async function f(e,a){if(!await (0,o.getServerSession)(e,a,m.authOptions))return a.status(401).json({error:"Unauthorized"});if("GET"!==e.method)return a.status(405).json({error:"Method not allowed"});try{let{search:r,type:t}=e.query;if(t){let e=l(t);if(!e)return a.status(404).json({error:"Game type not found"});return a.status(200).json({type:e})}if(r){let e=function(e){let a=s(),r=e.toLowerCase();return a.filter(e=>e.id.toLowerCase().includes(r)||e.name.toLowerCase().includes(r))}(r);return a.status(200).json(e)}let n=s();return a.status(200).json(n)}catch(e){return a.status(500).json({error:"Internal server error"})}}let c=(0,d.M)(t,"default"),p=(0,d.M)(t,"config"),y=new n.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/gameservers/games",pathname:"/api/gameservers/games",bundlePath:"",filename:""},userland:t})},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},71970:e=>{e.exports=require("gamedig")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var a=require("../../../webpack-api-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(5695));module.exports=t})();