import {
  Box,
  VStack,
  HStack,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Button,
  Text,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Progress,
  Icon,
  Alert,
  AlertIcon,
  Select,
} from '@chakra-ui/react';
import { useState } from 'react';
import { FaRobot, FaCheckCircle } from 'react-icons/fa';
import { useSession } from 'next-auth/react';

interface ExperimentalApplicationFormProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ExperimentalApplicationForm({ isOpen, onClose }: ExperimentalApplicationFormProps) {
  const { data: session } = useSession();
  const toast = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [formData, setFormData] = useState({
    motivation: '',
    experience: '',
    hoursPerWeek: '',
    feedback: '',
    contact: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const isFormValid = () => {
    return formData.motivation.length > 10 && formData.hoursPerWeek && formData.contact;
  };

  const handleSubmit = async () => {
    if (!isFormValid()) {
      toast({
        title: 'Form Incomplete',
        description: 'Please fill in all required fields.',
        status: 'error',
        duration: 3000,
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/admin/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'experimental',
          feature: 'experimental-access',
          reason: JSON.stringify({
            motivation: formData.motivation,
            experience: formData.experience,
            hoursPerWeek: formData.hoursPerWeek,
            feedback: formData.feedback,
            contact: formData.contact,
            username: session?.user?.name || 'Unknown',
            userId: session?.user?.id,
            submittedAt: new Date().toISOString(),
          }),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit application');
      }

      toast({
        title: 'Application Submitted!',
        description: 'Your application has been submitted and will be reviewed by OnedEyePete.',
        status: 'success',
        duration: 5000,
      });
      
      onClose();
      
      // Reset form
      setFormData({
        motivation: '',
        experience: '',
        hoursPerWeek: '',
        feedback: '',
        contact: '',
      });
    } catch (error) {
      console.error('Error submitting application:', error);
      toast({
        title: 'Submission Failed',
        description: 'There was an error submitting your application. Please try again.',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" scrollBehavior="inside">
      <ModalOverlay bg="blackAlpha.700" backdropFilter="blur(10px)" />
      <ModalContent bg="gray.800" border="1px solid" borderColor="whiteAlpha.200">
        <ModalHeader borderBottom="1px solid" borderColor="whiteAlpha.200">
          <VStack align="start" spacing={2}>
            <Text fontSize="xl" fontWeight="bold">
              Experimental Features Application
            </Text>
            <Text fontSize="sm" color="gray.400">
              Apply to test cutting-edge features and help improve the bot
            </Text>
          </VStack>
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody p={6}>
          <VStack spacing={6} align="stretch">
            <Alert status="info" bg="blue.900" border="1px solid" borderColor="blue.700">
              <AlertIcon />
              <Text fontSize="sm">
                Your application will be submitted to OnedEyePete's dashboard for review. Response time can take up to one week. Only serious testers who can provide valuable feedback will be accepted.
              </Text>
            </Alert>

            <FormControl isRequired>
              <FormLabel color="white">
                Why do you want to test experimental features? *
              </FormLabel>
              <Textarea
                placeholder="Tell us about your motivation and what you hope to contribute..."
                value={formData.motivation}
                onChange={(e) => handleInputChange('motivation', e.target.value)}
                bg="gray.700"
                border="1px solid"
                borderColor="whiteAlpha.300"
                rows={4}
                _focus={{
                  borderColor: 'yellow.400',
                  boxShadow: '0 0 0 1px #F6E05E',
                }}
              />
              <Text fontSize="xs" color="gray.400" mt={1}>
                {formData.motivation.length}/500 characters (minimum 10)
              </Text>
            </FormControl>

            <FormControl>
              <FormLabel color="white">
                Previous testing or beta experience
              </FormLabel>
              <Textarea
                placeholder="Describe any previous experience with beta testing, bug reporting, or feedback..."
                value={formData.experience}
                onChange={(e) => handleInputChange('experience', e.target.value)}
                bg="gray.700"
                border="1px solid"
                borderColor="whiteAlpha.300"
                rows={3}
                _focus={{
                  borderColor: 'yellow.400',
                  boxShadow: '0 0 0 1px #F6E05E',
                }}
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel color="white">
                How many hours per week can you dedicate to testing? *
              </FormLabel>
              <Select
                placeholder="Select hours per week"
                value={formData.hoursPerWeek}
                onChange={(e) => handleInputChange('hoursPerWeek', e.target.value)}
                bg="gray.700"
                border="1px solid"
                borderColor="whiteAlpha.300"
                _focus={{
                  borderColor: 'yellow.400',
                  boxShadow: '0 0 0 1px #F6E05E',
                }}
              >
                <option value="1-2">1-2 hours</option>
                <option value="3-5">3-5 hours</option>
                <option value="6-10">6-10 hours</option>
                <option value="10+">10+ hours</option>
              </Select>
            </FormControl>

            <FormControl>
              <FormLabel color="white">
                What kind of feedback can you provide?
              </FormLabel>
              <Textarea
                placeholder="Describe your ability to provide detailed bug reports, suggestions, or usability feedback..."
                value={formData.feedback}
                onChange={(e) => handleInputChange('feedback', e.target.value)}
                bg="gray.700"
                border="1px solid"
                borderColor="whiteAlpha.300"
                rows={3}
                _focus={{
                  borderColor: 'yellow.400',
                  boxShadow: '0 0 0 1px #F6E05E',
                }}
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel color="white">
                Best way to contact you for follow-up *
              </FormLabel>
              <Input
                placeholder="Discord username, email, or other contact method"
                value={formData.contact}
                onChange={(e) => handleInputChange('contact', e.target.value)}
                bg="gray.700"
                border="1px solid"
                borderColor="whiteAlpha.300"
                _focus={{
                  borderColor: 'yellow.400',
                  boxShadow: '0 0 0 1px #F6E05E',
                }}
              />
            </FormControl>
          </VStack>
        </ModalBody>

        <ModalFooter borderTop="1px solid" borderColor="whiteAlpha.200">
          <HStack spacing={4} width="full" justify="space-between">
            <HStack spacing={4}>
              <Icon as={FaRobot} color="yellow.300" boxSize={6} />
              <Text color="gray.400" fontSize="sm">
                Submitted to OnedEyePete's dashboard • Response within 1 week
              </Text>
            </HStack>
            
            <HStack spacing={4}>
              <Button variant="ghost" onClick={onClose}>
                Cancel
              </Button>
              <Button
                colorScheme="yellow"
                onClick={handleSubmit}
                isLoading={isSubmitting}
                loadingText="Submitting..."
                leftIcon={<Icon as={FaCheckCircle} />}
                isDisabled={!isFormValid()}
              >
                Submit Application
              </Button>
            </HStack>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
} 