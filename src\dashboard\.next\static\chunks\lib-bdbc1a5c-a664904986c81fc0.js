(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9284],{6179:(e,t,n)=>{"use strict";n.d(t,{VS:()=>T,h7:()=>a.h7,TG:()=>a.TG,Zk:()=>a.Zk,yX:()=>a.yX,Ln:()=>a.Ln,rN:()=>a.rN,Ay:()=>a.Gc,oN:()=>a.oN,fM:()=>a.fM,ck:()=>a.ck});var r,o,a=n(69792),i=n(94285),l=n(30662),c=n(88671),s=n(10903),u=n(96472);let d=({id:e,x:t,y:n,width:r,height:o,style:a,color:c,strokeColor:s,strokeWidth:u,className:d,borderRadius:f,shapeRendering:m,onClick:h,selected:p})=>{let{background:g,backgroundColor:v}=a||{};return i.createElement("rect",{className:(0,l.A)(["react-flow__minimap-node",{selected:p},d]),x:t,y:n,rx:f,ry:f,width:r,height:o,fill:c||g||v,stroke:s,strokeWidth:u,shapeRendering:m,onClick:h?t=>h(t,e):void 0})};d.displayName="MiniMapNode";var f=(0,i.memo)(d);let m=e=>e.nodeOrigin,h=e=>e.getNodes().filter(e=>!e.hidden&&e.width&&e.height),p=e=>e instanceof Function?e:()=>e;var g=(0,i.memo)(function({nodeStrokeColor:e="transparent",nodeColor:t="#e2e2e2",nodeClassName:n="",nodeBorderRadius:r=5,nodeStrokeWidth:o=2,nodeComponent:l=f,onClick:s}){let u=(0,a.Pj)(h,c.x),d=(0,a.Pj)(m),g=p(t),v=p(e),w=p(n),y="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return i.createElement(i.Fragment,null,u.map(e=>{let{x:t,y:n}=(0,a.Cz)(e,d).positionAbsolute;return i.createElement(l,{key:e.id,x:t,y:n,width:e.width,height:e.height,style:e.style,selected:e.selected,className:w(e),color:g(e),borderRadius:r,strokeColor:v(e),strokeWidth:o,shapeRendering:y,onClick:s,id:e.id})}))});let v=e=>{let t=e.getNodes(),n={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:n,boundingRect:t.length>0?(0,a.Mi)((0,a.Jo)(t,e.nodeOrigin),n):n,rfId:e.rfId}};function w({style:e,className:t,nodeStrokeColor:n="transparent",nodeColor:r="#e2e2e2",nodeClassName:o="",nodeBorderRadius:d=5,nodeStrokeWidth:f=2,nodeComponent:m,maskColor:h="rgb(240, 240, 240, 0.6)",maskStrokeColor:p="none",maskStrokeWidth:w=1,position:y="bottom-right",onClick:b,onNodeClick:E,pannable:x=!1,zoomable:C=!1,ariaLabel:S="React Flow mini map",inversePan:N=!1,zoomStep:k=10,offsetScale:M=5}){let R=(0,a.PI)(),_=(0,i.useRef)(null),{boundingRect:P,viewBB:$,rfId:A}=(0,a.Pj)(v,c.x),T=e?.width??200,z=e?.height??150,L=Math.max(P.width/T,P.height/z),O=L*T,H=L*z,B=M*L,V=P.x-(O-P.width)/2-B,I=P.y-(H-P.height)/2-B,D=O+2*B,X=H+2*B,j=`react-flow__minimap-desc-${A}`,W=(0,i.useRef)(0);W.current=L,(0,i.useEffect)(()=>{if(_.current){let e=(0,u.Lt)(_.current),t=(0,s.s_)().on("zoom",x?e=>{let{transform:t,d3Selection:n,d3Zoom:r,translateExtent:o,width:a,height:i}=R.getState();if("mousemove"!==e.sourceEvent.type||!n||!r)return;let l=W.current*Math.max(1,t[2])*(N?-1:1),c={x:t[0]-e.sourceEvent.movementX*l,y:t[1]-e.sourceEvent.movementY*l},u=s.GS.translate(c.x,c.y).scale(t[2]),d=r.constrain()(u,[[0,0],[a,i]],o);r.transform(n,d)}:null).on("zoom.wheel",C?e=>{let{transform:t,d3Selection:n,d3Zoom:r}=R.getState();if("wheel"!==e.sourceEvent.type||!n||!r)return;let o=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*k,a=t[2]*Math.pow(2,o);r.scaleTo(n,a)}:null);return e.call(t),()=>{e.on("zoom",null)}}},[x,C,N,k]);let Y=b?e=>{let t=(0,u.Wn)(e);b(e,{x:t[0],y:t[1]})}:void 0,Z=E?(e,t)=>{E(e,R.getState().nodeInternals.get(t))}:void 0;return i.createElement(a.Zk,{position:y,style:e,className:(0,l.A)(["react-flow__minimap",t]),"data-testid":"rf__minimap"},i.createElement("svg",{width:T,height:z,viewBox:`${V} ${I} ${D} ${X}`,role:"img","aria-labelledby":j,ref:_,onClick:Y},S&&i.createElement("title",{id:j},S),i.createElement(g,{onClick:Z,nodeColor:r,nodeStrokeColor:n,nodeBorderRadius:d,nodeClassName:o,nodeStrokeWidth:f,nodeComponent:m}),i.createElement("path",{className:"react-flow__minimap-mask",d:`M${V-B},${I-B}h${D+2*B}v${X+2*B}h${-D-2*B}z
        M${$.x},${$.y}h${$.width}v${$.height}h${-$.width}z`,fill:h,fillRule:"evenodd",stroke:p,strokeWidth:w,pointerEvents:"none"})))}function y(){return i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},i.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function b(){return i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},i.createElement("path",{d:"M0 0h32v4.2H0z"}))}function E(){return i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},i.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function x(){return i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},i.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function C(){return i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},i.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}w.displayName="MiniMap",(0,i.memo)(w);let S=({children:e,className:t,...n})=>i.createElement("button",{type:"button",className:(0,l.A)(["react-flow__controls-button",t]),...n},e);S.displayName="ControlButton";let N=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom}),k=({style:e,showZoom:t=!0,showFitView:n=!0,showInteractive:r=!0,fitViewOptions:o,onZoomIn:s,onZoomOut:u,onFitView:d,onInteractiveChange:f,className:m,children:h,position:p="bottom-left"})=>{let g=(0,a.PI)(),[v,w]=(0,i.useState)(!1),{isInteractive:k,minZoomReached:M,maxZoomReached:R}=(0,a.Pj)(N,c.x),{zoomIn:_,zoomOut:P,fitView:$}=(0,a.VH)();return((0,i.useEffect)(()=>{w(!0)},[]),v)?i.createElement(a.Zk,{className:(0,l.A)(["react-flow__controls",m]),position:p,style:e,"data-testid":"rf__controls"},t&&i.createElement(i.Fragment,null,i.createElement(S,{onClick:()=>{_(),s?.()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:R},i.createElement(y,null)),i.createElement(S,{onClick:()=>{P(),u?.()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:M},i.createElement(b,null))),n&&i.createElement(S,{className:"react-flow__controls-fitview",onClick:()=>{$(o),d?.()},title:"fit view","aria-label":"fit view"},i.createElement(E,null)),r&&i.createElement(S,{className:"react-flow__controls-interactive",onClick:()=>{g.setState({nodesDraggable:!k,nodesConnectable:!k,elementsSelectable:!k}),f?.(!k)},title:"toggle interactivity","aria-label":"toggle interactivity"},k?i.createElement(C,null):i.createElement(x,null)),h):null};function M({color:e,dimensions:t,lineWidth:n}){return i.createElement("path",{stroke:e,strokeWidth:n,d:`M${t[0]/2} 0 V${t[1]} M0 ${t[1]/2} H${t[0]}`})}function R({color:e,radius:t}){return i.createElement("circle",{cx:t,cy:t,r:t,fill:e})}k.displayName="Controls",(0,i.memo)(k),function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"}(r||(r={}));let _={[r.Dots]:"#91919a",[r.Lines]:"#eee",[r.Cross]:"#e2e2e2"},P={[r.Dots]:1,[r.Lines]:1,[r.Cross]:6},$=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function A({id:e,variant:t=r.Dots,gap:n=20,size:o,lineWidth:s=1,offset:u=2,color:d,style:f,className:m}){let h=(0,i.useRef)(null),{transform:p,patternId:g}=(0,a.Pj)($,c.x),v=d||_[t],w=o||P[t],y=t===r.Dots,b=t===r.Cross,E=Array.isArray(n)?n:[n,n],x=[E[0]*p[2]||1,E[1]*p[2]||1],C=w*p[2],S=b?[C,C]:x,N=y?[C/u,C/u]:[S[0]/u,S[1]/u];return i.createElement("svg",{className:(0,l.A)(["react-flow__background",m]),style:{...f,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:h,"data-testid":"rf__background"},i.createElement("pattern",{id:g+e,x:p[0]%x[0],y:p[1]%x[1],width:x[0],height:x[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${N[0]},-${N[1]})`},y?i.createElement(R,{color:v,radius:C/u}):i.createElement(M,{dimensions:S,color:v,lineWidth:s})),i.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${g+e})`}))}A.displayName="Background";var T=(0,i.memo)(A);n(24518);let z=e=>e.domNode?.querySelector(".react-flow__renderer"),L=(e,t)=>e?.positionAbsolute?.x===t?.positionAbsolute?.x&&e?.positionAbsolute?.y===t?.positionAbsolute?.y&&e?.width===t?.width&&e?.height===t?.height&&e?.selected===t?.selected&&e?.[internalsSymbol]?.z===t?.[internalsSymbol]?.z;var O=n(86423);!function(e){e.Line="line",e.Handle="handle"}(o||(o={}));let H={width:0,height:0,x:0,y:0},B={...H,pointerX:0,pointerY:0,aspectRatio:1};(0,i.memo)(function({nodeId:e,position:t,variant:n=o.Handle,className:r,style:c={},children:s,color:d,minWidth:f=10,minHeight:m=10,maxWidth:h=Number.MAX_VALUE,maxHeight:p=Number.MAX_VALUE,keepAspectRatio:g=!1,shouldResize:v,onResizeStart:w,onResize:y,onResizeEnd:b}){let E=(0,a.FC)(),x="string"==typeof e?e:E,C=(0,a.PI)(),S=(0,i.useRef)(null),N=(0,i.useRef)(B),k=(0,i.useRef)(H),M=(0,a.E_)(),R=n===o.Line?"right":"bottom-right",_=t??R;(0,i.useEffect)(()=>{if(!S.current||!x)return;let e=(0,u.Lt)(S.current),t=_.includes("right")||_.includes("left"),n=_.includes("bottom")||_.includes("top"),r=_.includes("left"),o=_.includes("top"),i=(0,O.$E)().on("start",e=>{let t=C.getState().nodeInternals.get(x),{xSnapped:n,ySnapped:r}=M(e);k.current={width:t?.width??0,height:t?.height??0,x:t?.position.x??0,y:t?.position.y??0},N.current={...k.current,pointerX:n,pointerY:r,aspectRatio:k.current.width/k.current.height},w?.(e,{...k.current})}).on("drag",e=>{let{nodeInternals:i,triggerNodeChanges:l}=C.getState(),{xSnapped:c,ySnapped:s}=M(e),u=i.get(x);if(u){let i=[],{pointerX:d,pointerY:w,width:b,height:E,x:C,y:S,aspectRatio:M}=N.current,{x:R,y:_,width:P,height:$}=k.current,A=Math.floor(t?c-d:0),T=Math.floor(n?s-w:0),z=(0,a.qE)(b+(r?-A:A),f,h),L=(0,a.qE)(E+(o?-T:T),m,p);if(g){let e=z/L,r=t&&n;z=e<=M&&r||n&&!t?L*M:z,L=e>M&&r||t&&!n?z/M:L,z>=h?(z=h,L=h/M):z<=f&&(z=f,L=f/M),L>=p?(L=p,z=p*M):L<=m&&(L=m,z=m*M)}let O=z!==P,H=L!==$;if(r||o){let e=r?C-(z-b):C,t=o?S-(L-E):S,n=e!==R&&O,a=t!==_&&H;if(n||a){let r={id:u.id,type:"position",position:{x:n?e:R,y:a?t:_}};i.push(r),k.current.x=r.position.x,k.current.y=r.position.y}}if(O||H){let e={id:x,type:"dimensions",updateStyle:!0,resizing:!0,dimensions:{width:z,height:L}};i.push(e),k.current.width=z,k.current.height=L}if(0===i.length)return;let B=function({width:e,prevWidth:t,height:n,prevHeight:r,invertX:o,invertY:a}){let i=e-t,l=n-r,c=[i>0?1:i<0?-1:0,l>0?1:l<0?-1:0];return i&&o&&(c[0]=-1*c[0]),l&&a&&(c[1]=-1*c[1]),c}({width:k.current.width,prevWidth:P,height:k.current.height,prevHeight:$,invertX:r,invertY:o}),V={...k.current,direction:B};if(!1===v?.(e,V))return;y?.(e,V),l(i)}}).on("end",e=>{b?.(e,{...k.current}),C.getState().triggerNodeChanges([{id:x,type:"dimensions",resizing:!1}])});return e.call(i),()=>{e.on(".drag",null)}},[x,_,f,m,h,p,g,M,w,y,b]);let P=_.split("-"),$=n===o.Line?"borderColor":"backgroundColor",A=d?{...c,[$]:d}:c;return i.createElement("div",{className:(0,l.A)(["react-flow__resize-control","nodrag",...P,n,r]),ref:S,style:A},s)})},20282:(e,t,n)=>{"use strict";n.d(t,{G:()=>D});var r=n(80594),o=n(94285),a="right-scroll-bar-position",i="width-before-scroll-bar",l=n(40012),c=n(58766),s=(0,c.fi)(),u=function(){},d=o.forwardRef(function(e,t){var n=o.useRef(null),a=o.useState({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:u}),i=a[0],c=a[1],d=e.forwardProps,f=e.children,m=e.className,h=e.removeScrollBar,p=e.enabled,g=e.shards,v=e.sideCar,w=e.noRelative,y=e.noIsolation,b=e.inert,E=e.allowPinchZoom,x=e.as,C=e.gapMode,S=(0,r.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(0,l.SV)([n,t]),k=(0,r.Cl)((0,r.Cl)({},S),i);return o.createElement(o.Fragment,null,p&&o.createElement(v,{sideCar:s,removeScrollBar:h,shards:g,noRelative:w,noIsolation:y,inert:b,setCallbacks:c,allowPinchZoom:!!E,lockRef:n,gapMode:C}),d?o.cloneElement(o.Children.only(f),(0,r.Cl)((0,r.Cl)({},k),{ref:N})):o.createElement(void 0===x?"div":x,(0,r.Cl)({},k,{className:m,ref:N}),f))});d.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},d.classNames={fullWidth:i,zeroRight:a};var f=n(79362),m=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=(0,f.m)();return t&&e.setAttribute("nonce",t),e}())){var r,o;(r=t).styleSheet?r.styleSheet.cssText=n:r.appendChild(document.createTextNode(n)),o=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(o)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},h=function(){var e=m();return function(t,n){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},p=function(){var e=h();return function(t){return e(t.styles,t.dynamic),null}},g={left:0,top:0,right:0,gap:0},v=function(e){return parseInt(e||"",10)||0},w=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[v(n),v(r),v(o)]},y=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return g;var t=w(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},b=p(),E="data-scroll-locked",x=function(e,t,n,r){var o=e.left,l=e.top,c=e.right,s=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(i," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(i," .").concat(i," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},C=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},S=function(){o.useEffect(function(){return document.body.setAttribute(E,(C()+1).toString()),function(){var e=C()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},N=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,a=void 0===r?"margin":r;S();var i=o.useMemo(function(){return y(a)},[a]);return o.createElement(b,{styles:x(i,!t,a,n?"":"!important")})},k=!1;if("undefined"!=typeof window)try{var M=Object.defineProperty({},"passive",{get:function(){return k=!0,!0}});window.addEventListener("test",M,M),window.removeEventListener("test",M,M)}catch(e){k=!1}var R=!!k&&{passive:!1},_=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},P=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),$(e,r)){var o=A(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},$=function(e,t){return"v"===e?_(t,"overflowY"):_(t,"overflowX")},A=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},T=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=i*r,c=n.target,s=t.contains(c),u=!1,d=l>0,f=0,m=0;do{if(!c)break;var h=A(e,c),p=h[0],g=h[1]-h[2]-i*p;(p||g)&&$(e,c)&&(f+=g,m+=p);var v=c.parentNode;c=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!s&&c!==document.body||s&&(t.contains(c)||t===c));return d&&(o&&1>Math.abs(f)||!o&&l>f)?u=!0:!d&&(o&&1>Math.abs(m)||!o&&-l>m)&&(u=!0),u},z=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},L=function(e){return[e.deltaX,e.deltaY]},O=function(e){return e&&"current"in e?e.current:e},H=0,B=[];let V=(0,c.mb)(s,function(e){var t=o.useRef([]),n=o.useRef([0,0]),a=o.useRef(),i=o.useState(H++)[0],l=o.useState(p)[0],c=o.useRef(e);o.useEffect(function(){c.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(0,r.fX)([e.lockRef.current],(e.shards||[]).map(O),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var s=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var r,o=z(e),i=n.current,l="deltaX"in e?e.deltaX:i[0]-o[0],s="deltaY"in e?e.deltaY:i[1]-o[1],u=e.target,d=Math.abs(l)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=P(d,u);if(!f)return!0;if(f?r=d:(r="v"===d?"h":"v",f=P(d,u)),!f)return!1;if(!a.current&&"changedTouches"in e&&(l||s)&&(a.current=r),!r)return!0;var m=a.current||r;return T(m,t,e,"h"===m?l:s,!0)},[]),u=o.useCallback(function(e){if(B.length&&B[B.length-1]===l){var n="deltaY"in e?L(e):z(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(c.current.shards||[]).map(O).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=o.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),f=o.useCallback(function(e){n.current=z(e),a.current=void 0},[]),m=o.useCallback(function(t){d(t.type,L(t),t.target,s(t,e.lockRef.current))},[]),h=o.useCallback(function(t){d(t.type,z(t),t.target,s(t,e.lockRef.current))},[]);o.useEffect(function(){return B.push(l),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:h}),document.addEventListener("wheel",u,R),document.addEventListener("touchmove",u,R),document.addEventListener("touchstart",f,R),function(){B=B.filter(function(e){return e!==l}),document.removeEventListener("wheel",u,R),document.removeEventListener("touchmove",u,R),document.removeEventListener("touchstart",f,R)}},[]);var g=e.removeScrollBar,v=e.inert;return o.createElement(o.Fragment,null,v?o.createElement(l,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,g?o.createElement(N,{noRelative:e.noRelative,gapMode:e.gapMode}):null)});var I=o.forwardRef(function(e,t){return o.createElement(d,(0,r.Cl)({},e,{ref:t,sideCar:V}))});I.classNames=d.classNames;let D=I},24472:()=>{},31365:(e,t,n)=>{"use strict";n.d(t,{Kq:()=>k});var r=n(94285);n(16863);var o=Symbol.for("react.forward_ref"),a=Symbol.for("react.memo");function i(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}var l={notify(){},get:()=>[]},c="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,s="undefined"!=typeof navigator&&"ReactNative"===navigator.product,u=c||s?r.useLayoutEffect:r.useEffect;function d(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}var f={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},m={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},h={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},p={[o]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[a]:h};function g(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case null:switch(e=e.type){case null:case null:case null:case null:case null:return e;default:switch(e=e&&e.$$typeof){case null:case o:case null:case a:case null:return e;default:return t}}case null:return t}}}(e)===a?h:p[e.$$typeof]||f}var v=Object.defineProperty,w=Object.getOwnPropertyNames,y=Object.getOwnPropertySymbols,b=Object.getOwnPropertyDescriptor,E=Object.getPrototypeOf,x=Object.prototype,C=Symbol.for("react-redux-context"),S="undefined"!=typeof globalThis?globalThis:{},N=function(){if(!r.createContext)return{};let e=S[C]??=new Map,t=e.get(r.createContext);return t||(t=r.createContext(null),e.set(r.createContext,t)),t}(),k=function(e){let{children:t,context:n,serverState:o,store:a}=e,i=r.useMemo(()=>{let e=function(e,t){let n,r=l,o=0,a=!1;function i(){u.onStateChange&&u.onStateChange()}function c(){if(o++,!n){let t,o;n=e.subscribe(i),t=null,o=null,r={clear(){t=null,o=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let n=!0,r=o={callback:e,next:null,prev:o};return r.prev?r.prev.next=r:t=r,function(){n&&null!==t&&(n=!1,r.next?r.next.prev=r.prev:o=r.prev,r.prev?r.prev.next=r.next:t=r.next)}}}}}function s(){o--,n&&0===o&&(n(),n=void 0,r.clear(),r=l)}let u={addNestedSub:function(e){c();let t=r.subscribe(e),n=!1;return()=>{n||(n=!0,t(),s())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:i,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,c())},tryUnsubscribe:function(){a&&(a=!1,s())},getListeners:()=>r};return u}(a);return{store:a,subscription:e,getServerState:o?()=>o:void 0}},[a,o]),c=r.useMemo(()=>a.getState(),[a]);return u(()=>{let{subscription:e}=i;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),c!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[i,c]),r.createElement((n||N).Provider,{value:i},t)}}}]);