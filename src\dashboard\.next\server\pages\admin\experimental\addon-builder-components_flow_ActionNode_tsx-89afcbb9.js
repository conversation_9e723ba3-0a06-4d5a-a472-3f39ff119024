"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "pages/admin/experimental/addon-builder-components_flow_ActionNode_tsx-89afcbb9";
exports.ids = ["pages/admin/experimental/addon-builder-components_flow_ActionNode_tsx-89afcbb9"];
exports.modules = {

/***/ "(pages-dir-node)/./components/flow/ActionNode.tsx":
/*!****************************************!*\
  !*** ./components/flow/ActionNode.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var reactflow__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactflow */ \"reactflow\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionButton,AccordionIcon,AccordionItem,AccordionPanel,Alert,AlertDescription,AlertIcon,Badge,Box,Button,Code,Collapse,Divider,FormControl,FormLabel,HStack,IconButton,Input,Modal,ModalBody,ModalCloseButton,ModalContent,ModalHeader,ModalOverlay,NumberDecrementStepper,NumberIncrementStepper,NumberInput,NumberInputField,NumberInputStepper,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure!=!@chakra-ui/react */ \"(pages-dir-node)/__barrel_optimize__?names=Accordion,AccordionButton,AccordionIcon,AccordionItem,AccordionPanel,Alert,AlertDescription,AlertIcon,Badge,Box,Button,Code,Collapse,Divider,FormControl,FormLabel,HStack,IconButton,Input,Modal,ModalBody,ModalCloseButton,ModalContent,ModalHeader,ModalOverlay,NumberDecrementStepper,NumberIncrementStepper,NumberInput,NumberInputField,NumberInputStepper,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiCopy,FiEye,FiEyeOff,FiPlus,FiSettings,FiTarget,FiTrash2!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiCopy,FiEye,FiEyeOff,FiPlus,FiSettings,FiTarget,FiTrash2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/ThemeContext */ \"(pages-dir-node)/./contexts/ThemeContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([reactflow__WEBPACK_IMPORTED_MODULE_2__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__, _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__]);\n([reactflow__WEBPACK_IMPORTED_MODULE_2__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__, _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst actionTypes = [\n    {\n        value: 'sendMessage',\n        label: '💬 Send Message',\n        category: 'Message'\n    },\n    {\n        value: 'sendEmbed',\n        label: '📋 Send Embed',\n        category: 'Message'\n    },\n    {\n        value: 'editMessage',\n        label: '✏️ Edit Message',\n        category: 'Message'\n    },\n    {\n        value: 'deleteMessage',\n        label: '🗑️ Delete Message',\n        category: 'Message'\n    },\n    {\n        value: 'addReaction',\n        label: '👍 Add Reaction',\n        category: 'Message'\n    },\n    {\n        value: 'removeReaction',\n        label: '👎 Remove Reaction',\n        category: 'Message'\n    },\n    {\n        value: 'addRole',\n        label: '🎭 Add Role',\n        category: 'Roles'\n    },\n    {\n        value: 'removeRole',\n        label: '🎭 Remove Role',\n        category: 'Roles'\n    },\n    {\n        value: 'kickUser',\n        label: '👢 Kick User',\n        category: 'Moderation'\n    },\n    {\n        value: 'banUser',\n        label: '🔨 Ban User',\n        category: 'Moderation'\n    },\n    {\n        value: 'timeoutUser',\n        label: '⏰ Timeout User',\n        category: 'Moderation'\n    },\n    {\n        value: 'unbanUser',\n        label: '🔓 Unban User',\n        category: 'Moderation'\n    },\n    {\n        value: 'createChannel',\n        label: '📺 Create Channel',\n        category: 'Channel'\n    },\n    {\n        value: 'deleteChannel',\n        label: '🗑️ Delete Channel',\n        category: 'Channel'\n    },\n    {\n        value: 'lockChannel',\n        label: '🔒 Lock Channel',\n        category: 'Channel'\n    },\n    {\n        value: 'unlockChannel',\n        label: '🔓 Unlock Channel',\n        category: 'Channel'\n    },\n    {\n        value: 'sendDM',\n        label: '📬 Send DM',\n        category: 'Message'\n    },\n    {\n        value: 'createThread',\n        label: '🧵 Create Thread',\n        category: 'Channel'\n    },\n    {\n        value: 'pinMessage',\n        label: '📌 Pin Message',\n        category: 'Message'\n    },\n    {\n        value: 'unpinMessage',\n        label: '📌 Unpin Message',\n        category: 'Message'\n    }\n];\n// Available variables organized by category\nconst availableVariables = {\n    user: [\n        {\n            name: '{user.id}',\n            description: 'User ID',\n            icon: '🆔'\n        },\n        {\n            name: '{user.username}',\n            description: 'Username',\n            icon: '👤'\n        },\n        {\n            name: '{user.displayName}',\n            description: 'Display Name',\n            icon: '📝'\n        },\n        {\n            name: '{user.tag}',\n            description: 'User Tag (username#0000)',\n            icon: '🏷️'\n        },\n        {\n            name: '{user.mention}',\n            description: 'User Mention (<@id>)',\n            icon: '📢'\n        },\n        {\n            name: '{user.avatar}',\n            description: 'Avatar URL',\n            icon: '🖼️'\n        },\n        {\n            name: '{user.createdAt}',\n            description: 'Account Creation Date',\n            icon: '📅'\n        },\n        {\n            name: '{user.joinedAt}',\n            description: 'Server Join Date',\n            icon: '🚪'\n        },\n        {\n            name: '{user.roles}',\n            description: 'User Roles',\n            icon: '🎭'\n        },\n        {\n            name: '{user.permissions}',\n            description: 'User Permissions',\n            icon: '🔐'\n        }\n    ],\n    channel: [\n        {\n            name: '{channel.id}',\n            description: 'Channel ID',\n            icon: '🆔'\n        },\n        {\n            name: '{channel.name}',\n            description: 'Channel Name',\n            icon: '📺'\n        },\n        {\n            name: '{channel.mention}',\n            description: 'Channel Mention (<#id>)',\n            icon: '📢'\n        },\n        {\n            name: '{channel.type}',\n            description: 'Channel Type',\n            icon: '📋'\n        },\n        {\n            name: '{channel.topic}',\n            description: 'Channel Topic',\n            icon: '💬'\n        },\n        {\n            name: '{channel.memberCount}',\n            description: 'Member Count',\n            icon: '👥'\n        },\n        {\n            name: '{channel.createdAt}',\n            description: 'Channel Creation Date',\n            icon: '📅'\n        }\n    ],\n    server: [\n        {\n            name: '{server.id}',\n            description: 'Server ID',\n            icon: '🆔'\n        },\n        {\n            name: '{server.name}',\n            description: 'Server Name',\n            icon: '🏠'\n        },\n        {\n            name: '{server.icon}',\n            description: 'Server Icon URL',\n            icon: '🖼️'\n        },\n        {\n            name: '{server.memberCount}',\n            description: 'Member Count',\n            icon: '👥'\n        },\n        {\n            name: '{server.createdAt}',\n            description: 'Server Creation Date',\n            icon: '📅'\n        },\n        {\n            name: '{server.owner}',\n            description: 'Server Owner',\n            icon: '👑'\n        },\n        {\n            name: '{server.boostLevel}',\n            description: 'Server Boost Level',\n            icon: '🚀'\n        },\n        {\n            name: '{server.boostCount}',\n            description: 'Server Boost Count',\n            icon: '💎'\n        }\n    ],\n    message: [\n        {\n            name: '{message.id}',\n            description: 'Message ID',\n            icon: '🆔'\n        },\n        {\n            name: '{message.content}',\n            description: 'Message Content',\n            icon: '💬'\n        },\n        {\n            name: '{message.author}',\n            description: 'Message Author',\n            icon: '👤'\n        },\n        {\n            name: '{message.channel}',\n            description: 'Message Channel',\n            icon: '📺'\n        },\n        {\n            name: '{message.createdAt}',\n            description: 'Message Creation Date',\n            icon: '📅'\n        },\n        {\n            name: '{message.editedAt}',\n            description: 'Message Edit Date',\n            icon: '✏️'\n        },\n        {\n            name: '{message.reactions}',\n            description: 'Message Reactions',\n            icon: '👍'\n        },\n        {\n            name: '{message.attachments}',\n            description: 'Message Attachments',\n            icon: '📎'\n        }\n    ],\n    api: [\n        {\n            name: '{response.data}',\n            description: 'API Response Data',\n            icon: '📊'\n        },\n        {\n            name: '{response.status}',\n            description: 'HTTP Status Code',\n            icon: '🔢'\n        },\n        {\n            name: '{response.headers}',\n            description: 'Response Headers',\n            icon: '📋'\n        },\n        {\n            name: '{response.message}',\n            description: 'Response Message',\n            icon: '💬'\n        },\n        {\n            name: '{response.error}',\n            description: 'Error Message',\n            icon: '❌'\n        }\n    ],\n    random: [\n        {\n            name: '{random.number}',\n            description: 'Random Number (1-100)',\n            icon: '🎲'\n        },\n        {\n            name: '{random.uuid}',\n            description: 'Random UUID',\n            icon: '🆔'\n        },\n        {\n            name: '{random.choice}',\n            description: 'Random Choice from Array',\n            icon: '🎯'\n        },\n        {\n            name: '{random.color}',\n            description: 'Random Hex Color',\n            icon: '🎨'\n        }\n    ],\n    date: [\n        {\n            name: '{date.now}',\n            description: 'Current Date/Time',\n            icon: '⏰'\n        },\n        {\n            name: '{date.today}',\n            description: 'Today\\'s Date',\n            icon: '📅'\n        },\n        {\n            name: '{date.timestamp}',\n            description: 'Unix Timestamp',\n            icon: '🕐'\n        },\n        {\n            name: '{date.iso}',\n            description: 'ISO Date String',\n            icon: '📝'\n        }\n    ]\n};\nconst ActionNode = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(({ data, selected, id, updateNodeData: updateParentNodeData })=>{\n    const { currentScheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const { isOpen, onOpen, onClose } = (0,_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useDisclosure)();\n    const [nodeData, setNodeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ActionNode.useState\": ()=>({\n                embed: {\n                    fields: [],\n                    author: {\n                        name: ''\n                    },\n                    footer: {\n                        text: ''\n                    }\n                },\n                ...data\n            })\n    }[\"ActionNode.useState\"]);\n    const [showVariables, setShowVariables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [embedPreview, setEmbedPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guildData, setGuildData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingGuildData, setLoadingGuildData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateNodeData = (updates)=>{\n        setNodeData((prev)=>({\n                ...prev,\n                ...updates\n            }));\n    };\n    const handleModalClose = ()=>{\n        // Update parent nodes array when modal closes\n        if (updateParentNodeData && id) {\n            updateParentNodeData(id, nodeData);\n        }\n        onClose();\n    };\n    const getActionLabel = (actionType)=>{\n        return actionTypes.find((a)=>a.value === actionType)?.label || actionType;\n    };\n    const getActionIcon = (actionType)=>{\n        const action = actionTypes.find((a)=>a.value === actionType);\n        return action?.label.split(' ')[0] || '🎯';\n    };\n    const copyVariable = (variable)=>{\n        navigator.clipboard.writeText(variable);\n    };\n    const fetchGuildData = async ()=>{\n        if (guildData || loadingGuildData) return; // Don't fetch if already loaded or loading\n        setLoadingGuildData(true);\n        try {\n            const response = await fetch('/api/admin/experimental/addon-builder/guild-data');\n            if (response.ok) {\n                const data = await response.json();\n                setGuildData({\n                    channels: data.channels,\n                    roles: data.roles,\n                    members: data.members\n                });\n            } else {\n                console.error('Failed to fetch guild data:', await response.text());\n            }\n        } catch (error) {\n            console.error('Error fetching guild data:', error);\n        } finally{\n            setLoadingGuildData(false);\n        }\n    };\n    // Fetch guild data when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActionNode.useEffect\": ()=>{\n            if (isOpen) {\n                fetchGuildData();\n            }\n        }\n    }[\"ActionNode.useEffect\"], [\n        isOpen\n    ]);\n    const addEmbedField = ()=>{\n        const currentFields = nodeData.embed?.fields || [];\n        updateNodeData({\n            embed: {\n                ...nodeData.embed,\n                fields: [\n                    ...currentFields,\n                    {\n                        name: '',\n                        value: '',\n                        inline: false\n                    }\n                ]\n            }\n        });\n    };\n    const updateEmbedField = (index, field, value)=>{\n        const currentFields = nodeData.embed?.fields || [];\n        const newFields = [\n            ...currentFields\n        ];\n        newFields[index] = {\n            ...newFields[index],\n            [field]: value\n        };\n        updateNodeData({\n            embed: {\n                ...nodeData.embed,\n                fields: newFields\n            }\n        });\n    };\n    const removeEmbedField = (index)=>{\n        const currentFields = nodeData.embed?.fields || [];\n        const newFields = currentFields.filter((_, i)=>i !== index);\n        updateNodeData({\n            embed: {\n                ...nodeData.embed,\n                fields: newFields\n            }\n        });\n    };\n    const renderEmbedPreview = ()=>{\n        const embed = nodeData.embed || {};\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n            spacing: 3,\n            align: \"stretch\",\n            maxW: \"500px\",\n            children: [\n                nodeData.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                    bg: currentScheme.colors.surface,\n                    border: \"1px solid\",\n                    borderColor: currentScheme.colors.border,\n                    borderRadius: \"md\",\n                    p: 3,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                            fontSize: \"sm\",\n                            color: currentScheme.colors.text,\n                            fontWeight: \"medium\",\n                            children: \"\\uD83D\\uDCE9 Message Content:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                            fontSize: \"sm\",\n                            color: currentScheme.colors.text,\n                            mt: 1,\n                            children: nodeData.message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                    bg: currentScheme.colors.surface,\n                    border: \"1px solid\",\n                    borderColor: currentScheme.colors.border,\n                    borderRadius: \"md\",\n                    p: 4,\n                    borderLeft: `4px solid ${embed.color || '#5865F2'}`,\n                    children: [\n                        embed.author?.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                            spacing: 2,\n                            mb: 2,\n                            children: [\n                                embed.author.iconUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                    w: 6,\n                                    h: 6,\n                                    borderRadius: \"full\",\n                                    bg: \"gray.300\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    fontSize: \"xs\",\n                                    children: \"\\uD83D\\uDC64\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    fontSize: \"sm\",\n                                    fontWeight: \"bold\",\n                                    color: currentScheme.colors.text,\n                                    children: embed.author.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, undefined),\n                        embed.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                            fontSize: \"md\",\n                            fontWeight: \"bold\",\n                            color: currentScheme.colors.text,\n                            mb: 2,\n                            children: embed.title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, undefined),\n                        embed.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                            fontSize: \"sm\",\n                            color: currentScheme.colors.textSecondary,\n                            mb: 3,\n                            children: embed.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, undefined),\n                        embed.fields && embed.fields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                            spacing: 2,\n                            align: \"stretch\",\n                            mb: 3,\n                            children: embed.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                            fontSize: \"sm\",\n                                            fontWeight: \"bold\",\n                                            color: currentScheme.colors.text,\n                                            children: field.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                            fontSize: \"sm\",\n                                            color: currentScheme.colors.textSecondary,\n                                            children: field.value\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, undefined),\n                        embed.footer?.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                            spacing: 2,\n                            mt: 3,\n                            pt: 2,\n                            borderTop: \"1px solid\",\n                            borderColor: currentScheme.colors.border,\n                            children: [\n                                embed.footer.iconUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                    w: 4,\n                                    h: 4,\n                                    borderRadius: \"full\",\n                                    bg: \"gray.300\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    fontSize: \"xs\",\n                                    children: \"ℹ️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    fontSize: \"xs\",\n                                    color: currentScheme.colors.textSecondary,\n                                    children: embed.footer.text\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 13\n                        }, undefined),\n                        embed.timestamp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                            fontSize: \"xs\",\n                            color: currentScheme.colors.textSecondary,\n                            mt: 2,\n                            children: [\n                                \"\\uD83D\\uDD52 \",\n                                new Date().toLocaleString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n            lineNumber: 300,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderVariablesList = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Collapse, {\n            in: showVariables,\n            animateOpacity: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                bg: currentScheme.colors.surface,\n                border: \"1px solid\",\n                borderColor: currentScheme.colors.border,\n                borderRadius: \"md\",\n                p: 4,\n                mt: 3,\n                maxH: \"400px\",\n                overflowY: \"auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Accordion, {\n                    allowMultiple: true,\n                    children: Object.entries(availableVariables).map(([category, variables])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AccordionItem, {\n                            border: \"none\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AccordionButton, {\n                                    px: 0,\n                                    py: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                            flex: \"1\",\n                                            textAlign: \"left\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                fontSize: \"sm\",\n                                                fontWeight: \"bold\",\n                                                color: currentScheme.colors.text,\n                                                textTransform: \"capitalize\",\n                                                children: [\n                                                    category,\n                                                    \" Variables\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AccordionIcon, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AccordionPanel, {\n                                    px: 0,\n                                    py: 2,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                        spacing: 2,\n                                        align: \"stretch\",\n                                        children: variables.map((variable)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                spacing: 2,\n                                                p: 2,\n                                                bg: currentScheme.colors.background,\n                                                borderRadius: \"md\",\n                                                cursor: \"pointer\",\n                                                _hover: {\n                                                    bg: currentScheme.colors.surface\n                                                },\n                                                onClick: ()=>copyVariable(variable.name),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                        fontSize: \"sm\",\n                                                        children: variable.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Code, {\n                                                        fontSize: \"xs\",\n                                                        colorScheme: \"blue\",\n                                                        children: variable.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                        fontSize: \"xs\",\n                                                        color: currentScheme.colors.textSecondary,\n                                                        flex: \"1\",\n                                                        children: variable.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCopy, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        size: \"xs\",\n                                                        variant: \"ghost\",\n                                                        \"aria-label\": \"Copy variable\",\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            copyVariable(variable.name);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, variable.name, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n            lineNumber: 410,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                bg: currentScheme.colors.surface,\n                border: `2px solid ${selected ? '#a855f7' : currentScheme.colors.border}`,\n                borderRadius: \"md\",\n                p: 2,\n                minW: \"140px\",\n                maxW: \"180px\",\n                boxShadow: \"sm\",\n                position: \"relative\",\n                _hover: {\n                    boxShadow: 'md',\n                    transform: 'translateY(-1px)'\n                },\n                transition: \"all 0.2s\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_2__.Handle, {\n                        type: \"target\",\n                        position: reactflow__WEBPACK_IMPORTED_MODULE_2__.Position.Top,\n                        style: {\n                            background: '#a855f7',\n                            border: `2px solid ${currentScheme.colors.surface}`,\n                            width: '12px',\n                            height: '12px',\n                            top: '-6px',\n                            left: '50%',\n                            transform: 'translateX(-50%)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                        spacing: 1,\n                        align: \"stretch\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                justify: \"space-between\",\n                                align: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                        spacing: 1,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                                bg: \"purple.500\",\n                                                color: \"white\",\n                                                borderRadius: \"full\",\n                                                p: 0.5,\n                                                fontSize: \"xs\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTarget, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                fontSize: \"xs\",\n                                                fontWeight: \"bold\",\n                                                color: currentScheme.colors.text,\n                                                children: \"Action\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSettings, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        size: \"xs\",\n                                        variant: \"ghost\",\n                                        onClick: onOpen,\n                                        \"aria-label\": \"Configure action\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                    spacing: 1,\n                                    children: [\n                                        nodeData.actionType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                            fontSize: \"xs\",\n                                            children: getActionIcon(nodeData.actionType)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                            fontSize: \"xs\",\n                                            color: currentScheme.colors.text,\n                                            noOfLines: 1,\n                                            children: nodeData.actionType ? getActionLabel(nodeData.actionType).split(' ').slice(1).join(' ') : 'Select Action'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 11\n                            }, undefined),\n                            nodeData.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    fontSize: \"xs\",\n                                    color: currentScheme.colors.textSecondary,\n                                    noOfLines: 1,\n                                    children: nodeData.message.length > 20 ? nodeData.message.substring(0, 20) + '...' : nodeData.message\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                spacing: 1,\n                                flexWrap: \"wrap\",\n                                children: [\n                                    nodeData.channel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"purple\",\n                                        children: [\n                                            \"#\",\n                                            nodeData.channel\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    nodeData.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"purple\",\n                                        children: [\n                                            \"@\",\n                                            nodeData.role\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    nodeData.embed?.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"blue\",\n                                        children: \"\\uD83D\\uDCCB Embed\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                lineNumber: 475,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Modal, {\n                isOpen: isOpen,\n                onClose: handleModalClose,\n                size: \"4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalOverlay, {\n                        bg: \"blackAlpha.600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                        lineNumber: 572,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalContent, {\n                        bg: currentScheme.colors.background,\n                        border: \"2px solid\",\n                        borderColor: \"purple.400\",\n                        maxW: \"1200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalHeader, {\n                                color: currentScheme.colors.text,\n                                children: \"\\uD83C\\uDFAF Configure Action\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalCloseButton, {}, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalBody, {\n                                pb: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                    spacing: 6,\n                                    align: \"stretch\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                    justify: \"space-between\",\n                                                    align: \"center\",\n                                                    mb: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                            fontSize: \"sm\",\n                                                            fontWeight: \"bold\",\n                                                            color: currentScheme.colors.text,\n                                                            children: \"Available Variables\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"ghost\",\n                                                            leftIcon: showVariables ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiEyeOff, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 47\n                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiEye, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 62\n                                                            }, void 0),\n                                                            onClick: ()=>setShowVariables(!showVariables),\n                                                            children: [\n                                                                showVariables ? 'Hide' : 'Show',\n                                                                \" Variables\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                                                    status: \"info\",\n                                                    borderRadius: \"md\",\n                                                    mb: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertIcon, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                                            fontSize: \"sm\",\n                                                            children: \"\\uD83D\\uDCA1 Use variables in your actions! Click any variable below to copy it. Variables are replaced with actual values when your addon runs.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                renderVariablesList()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Divider, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                    color: currentScheme.colors.text,\n                                                    children: \"Action Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                    value: nodeData.actionType || '',\n                                                    onChange: (e)=>updateNodeData({\n                                                            actionType: e.target.value\n                                                        }),\n                                                    placeholder: \"Select an action type\",\n                                                    bg: currentScheme.colors.background,\n                                                    color: currentScheme.colors.text,\n                                                    borderColor: currentScheme.colors.border,\n                                                    children: Object.entries(actionTypes.reduce((acc, action)=>{\n                                                        if (!acc[action.category]) acc[action.category] = [];\n                                                        acc[action.category].push(action);\n                                                        return acc;\n                                                    }, {})).map(([category, actions])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"optgroup\", {\n                                                            label: category,\n                                                            children: actions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: action.value,\n                                                                    children: action.label\n                                                                }, action.value, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 21\n                                                                }, undefined))\n                                                        }, category, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        nodeData.actionType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: nodeData.actionType === 'sendEmbed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                                                variant: \"enclosed\",\n                                                colorScheme: \"purple\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.TabList, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                                                children: \"Embed Builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                                                children: \"Preview\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.TabPanels, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Channel\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 650,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                    spacing: 2,\n                                                                                    align: \"stretch\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                                                            value: nodeData.channel || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    channel: e.target.value\n                                                                                                }),\n                                                                                            placeholder: loadingGuildData ? 'Loading channels...' : 'Select a channel',\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border,\n                                                                                            isDisabled: loadingGuildData,\n                                                                                            children: guildData?.channels.filter((channel)=>channel.type === 'text').map((channel)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: channel.name,\n                                                                                                    children: [\n                                                                                                        \"#\",\n                                                                                                        channel.name\n                                                                                                    ]\n                                                                                                }, channel.id, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 664,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 652,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            value: nodeData.channel || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    channel: e.target.value\n                                                                                                }),\n                                                                                            placeholder: \"Or type: general or {channel.name}\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border,\n                                                                                            size: \"sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 669,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 651,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Message Content (appears above embed)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 683,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                                                    value: nodeData.message || '',\n                                                                                    onChange: (e)=>updateNodeData({\n                                                                                            message: e.target.value\n                                                                                        }),\n                                                                                    placeholder: \"Hello {user.username}! This text appears above the embed...\",\n                                                                                    bg: currentScheme.colors.background,\n                                                                                    color: currentScheme.colors.text,\n                                                                                    borderColor: currentScheme.colors.border,\n                                                                                    minH: \"100px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 684,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.SimpleGrid, {\n                                                                            columns: 2,\n                                                                            spacing: 4,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                            color: currentScheme.colors.text,\n                                                                                            children: \"Embed Title\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 697,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            value: nodeData.embed?.title || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        title: e.target.value\n                                                                                                    }\n                                                                                                }),\n                                                                                            placeholder: \"Welcome to {server.name}!\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 698,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 696,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                            color: currentScheme.colors.text,\n                                                                                            children: \"Embed Color\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 711,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                            spacing: 2,\n                                                                                            align: \"stretch\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                            type: \"color\",\n                                                                                                            value: nodeData.embed?.color || '#5865F2',\n                                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                                    embed: {\n                                                                                                                        ...nodeData.embed,\n                                                                                                                        color: e.target.value\n                                                                                                                    }\n                                                                                                                }),\n                                                                                                            w: \"60px\",\n                                                                                                            h: \"40px\",\n                                                                                                            p: 1,\n                                                                                                            bg: currentScheme.colors.background,\n                                                                                                            borderColor: currentScheme.colors.border\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 714,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                            value: nodeData.embed?.color || '',\n                                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                                    embed: {\n                                                                                                                        ...nodeData.embed,\n                                                                                                                        color: e.target.value\n                                                                                                                    }\n                                                                                                                }),\n                                                                                                            placeholder: \"#5865F2\",\n                                                                                                            bg: currentScheme.colors.background,\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            borderColor: currentScheme.colors.border,\n                                                                                                            flex: \"1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 726,\n                                                                                                            columnNumber: 37\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 713,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                                    spacing: 1,\n                                                                                                    flexWrap: \"wrap\",\n                                                                                                    children: [\n                                                                                                        '#5865F2',\n                                                                                                        '#57F287',\n                                                                                                        '#FEE75C',\n                                                                                                        '#EB459E',\n                                                                                                        '#ED4245',\n                                                                                                        '#FF6B35',\n                                                                                                        '#00ADB5',\n                                                                                                        '#9B59B6'\n                                                                                                    ].map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                                            size: \"xs\",\n                                                                                                            bg: color,\n                                                                                                            w: \"30px\",\n                                                                                                            h: \"20px\",\n                                                                                                            minW: \"30px\",\n                                                                                                            p: 0,\n                                                                                                            onClick: ()=>updateNodeData({\n                                                                                                                    embed: {\n                                                                                                                        ...nodeData.embed,\n                                                                                                                        color\n                                                                                                                    }\n                                                                                                                }),\n                                                                                                            _hover: {\n                                                                                                                transform: 'scale(1.1)'\n                                                                                                            },\n                                                                                                            border: \"1px solid\",\n                                                                                                            borderColor: currentScheme.colors.border\n                                                                                                        }, color, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 740,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 738,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 712,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 710,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 695,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Embed Description\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 763,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                                                    value: nodeData.embed?.description || '',\n                                                                                    onChange: (e)=>updateNodeData({\n                                                                                            embed: {\n                                                                                                ...nodeData.embed,\n                                                                                                description: e.target.value\n                                                                                            }\n                                                                                        }),\n                                                                                    placeholder: \"This is the description that appears inside the embed...\",\n                                                                                    bg: currentScheme.colors.background,\n                                                                                    color: currentScheme.colors.text,\n                                                                                    borderColor: currentScheme.colors.border,\n                                                                                    minH: \"100px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 764,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 762,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.SimpleGrid, {\n                                                                            columns: 2,\n                                                                            spacing: 4,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                            color: currentScheme.colors.text,\n                                                                                            children: \"Thumbnail URL\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 779,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            value: nodeData.embed?.thumbnail || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        thumbnail: e.target.value\n                                                                                                    }\n                                                                                                }),\n                                                                                            placeholder: \"https://example.com/image.png\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 780,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 778,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                            color: currentScheme.colors.text,\n                                                                                            children: \"Image URL\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 793,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            value: nodeData.embed?.image || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        image: e.target.value\n                                                                                                    }\n                                                                                                }),\n                                                                                            placeholder: \"https://example.com/image.png\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 794,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 792,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 777,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Author\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 809,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                    spacing: 2,\n                                                                                    align: \"stretch\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            value: nodeData.embed?.author?.name || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        author: {\n                                                                                                            ...nodeData.embed?.author,\n                                                                                                            name: e.target.value\n                                                                                                        }\n                                                                                                    }\n                                                                                                }),\n                                                                                            placeholder: \"Author name\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 811,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.SimpleGrid, {\n                                                                                            columns: 2,\n                                                                                            spacing: 2,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                    value: nodeData.embed?.author?.url || '',\n                                                                                                    onChange: (e)=>updateNodeData({\n                                                                                                            embed: {\n                                                                                                                ...nodeData.embed,\n                                                                                                                author: {\n                                                                                                                    ...nodeData.embed?.author,\n                                                                                                                    url: e.target.value\n                                                                                                                }\n                                                                                                            }\n                                                                                                        }),\n                                                                                                    placeholder: \"Author URL\",\n                                                                                                    bg: currentScheme.colors.background,\n                                                                                                    color: currentScheme.colors.text,\n                                                                                                    borderColor: currentScheme.colors.border\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 825,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                    value: nodeData.embed?.author?.iconUrl || '',\n                                                                                                    onChange: (e)=>updateNodeData({\n                                                                                                            embed: {\n                                                                                                                ...nodeData.embed,\n                                                                                                                author: {\n                                                                                                                    ...nodeData.embed?.author,\n                                                                                                                    iconUrl: e.target.value\n                                                                                                                }\n                                                                                                            }\n                                                                                                        }),\n                                                                                                    placeholder: \"Author icon URL\",\n                                                                                                    bg: currentScheme.colors.background,\n                                                                                                    color: currentScheme.colors.text,\n                                                                                                    borderColor: currentScheme.colors.border\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 838,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 824,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 810,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 808,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Footer\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 857,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                    spacing: 2,\n                                                                                    align: \"stretch\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            value: nodeData.embed?.footer?.text || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        footer: {\n                                                                                                            ...nodeData.embed?.footer,\n                                                                                                            text: e.target.value\n                                                                                                        }\n                                                                                                    }\n                                                                                                }),\n                                                                                            placeholder: \"Footer text\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 859,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            value: nodeData.embed?.footer?.iconUrl || '',\n                                                                                            onChange: (e)=>updateNodeData({\n                                                                                                    embed: {\n                                                                                                        ...nodeData.embed,\n                                                                                                        footer: {\n                                                                                                            ...nodeData.embed?.footer,\n                                                                                                            iconUrl: e.target.value\n                                                                                                        }\n                                                                                                    }\n                                                                                                }),\n                                                                                            placeholder: \"Footer icon URL\",\n                                                                                            bg: currentScheme.colors.background,\n                                                                                            color: currentScheme.colors.text,\n                                                                                            borderColor: currentScheme.colors.border\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 872,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 858,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                    justify: \"space-between\",\n                                                                                    align: \"center\",\n                                                                                    mb: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                            color: currentScheme.colors.text,\n                                                                                            mb: 0,\n                                                                                            children: \"Embed Fields\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 891,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPlus, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                lineNumber: 894,\n                                                                                                columnNumber: 45\n                                                                                            }, void 0),\n                                                                                            onClick: addEmbedField,\n                                                                                            colorScheme: \"blue\",\n                                                                                            children: \"Add Field\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 892,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 890,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                    spacing: 3,\n                                                                                    align: \"stretch\",\n                                                                                    children: nodeData.embed?.fields?.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                                                                            p: 3,\n                                                                                            bg: currentScheme.colors.surface,\n                                                                                            borderRadius: \"md\",\n                                                                                            border: \"1px solid\",\n                                                                                            borderColor: currentScheme.colors.border,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                                    justify: \"space-between\",\n                                                                                                    align: \"center\",\n                                                                                                    mb: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                            fontSize: \"sm\",\n                                                                                                            fontWeight: \"bold\",\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            children: [\n                                                                                                                \"Field \",\n                                                                                                                index + 1\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 912,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                                                                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiPlus_FiSettings_FiTarget_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTrash2, {}, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                                lineNumber: 916,\n                                                                                                                columnNumber: 47\n                                                                                                            }, void 0),\n                                                                                                            size: \"xs\",\n                                                                                                            colorScheme: \"red\",\n                                                                                                            variant: \"ghost\",\n                                                                                                            onClick: ()=>removeEmbedField(index),\n                                                                                                            \"aria-label\": \"Remove field\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 915,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 911,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                                    spacing: 2,\n                                                                                                    align: \"stretch\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                            value: field.name,\n                                                                                                            onChange: (e)=>updateEmbedField(index, 'name', e.target.value),\n                                                                                                            placeholder: \"Field name\",\n                                                                                                            bg: currentScheme.colors.background,\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            borderColor: currentScheme.colors.border\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 925,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                                                                            value: field.value,\n                                                                                                            onChange: (e)=>updateEmbedField(index, 'value', e.target.value),\n                                                                                                            placeholder: \"Field value\",\n                                                                                                            bg: currentScheme.colors.background,\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            borderColor: currentScheme.colors.border,\n                                                                                                            minH: \"80px\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 933,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                                                    isChecked: field.inline || false,\n                                                                                                                    onChange: (e)=>updateEmbedField(index, 'inline', e.target.checked),\n                                                                                                                    colorScheme: \"purple\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                                    lineNumber: 943,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                                    fontSize: \"sm\",\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    children: \"Display inline\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                                    lineNumber: 948,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                            lineNumber: 942,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                                    lineNumber: 924,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, index, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                            lineNumber: 903,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 901,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 889,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                    isChecked: nodeData.embed?.timestamp || false,\n                                                                                    onChange: (e)=>updateNodeData({\n                                                                                            embed: {\n                                                                                                ...nodeData.embed,\n                                                                                                timestamp: e.target.checked\n                                                                                            }\n                                                                                        }),\n                                                                                    colorScheme: \"purple\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 959,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: currentScheme.colors.text,\n                                                                                    children: \"Show current timestamp\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 966,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 958,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                            fontSize: \"lg\",\n                                                                            fontWeight: \"bold\",\n                                                                            color: currentScheme.colors.text,\n                                                                            children: \"Embed Preview\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 976,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                                                                            status: \"info\",\n                                                                            borderRadius: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertIcon, {}, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 980,\n                                                                                    columnNumber: 21\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                                                                    fontSize: \"sm\",\n                                                                                    children: \"This shows your message content (above) and embed (below) as they will appear in Discord. Variables will be replaced with actual values when sent.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                    lineNumber: 981,\n                                                                                    columnNumber: 21\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                            lineNumber: 979,\n                                                                            columnNumber: 19\n                                                                        }, undefined),\n                                                                        renderEmbedPreview()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                    lineNumber: 975,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                spacing: 4,\n                                                align: \"stretch\",\n                                                children: [\n                                                    (nodeData.actionType === 'sendMessage' || nodeData.actionType === 'sendDM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Message Content\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 996,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                                        value: nodeData.message || '',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                message: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Hello {user.username}! Welcome to {server.name}!\",\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border,\n                                                                        minH: \"100px\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 997,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            nodeData.actionType !== 'sendDM' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Channel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1010,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                        spacing: 2,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                                                value: nodeData.channel || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        channel: e.target.value\n                                                                                    }),\n                                                                                placeholder: loadingGuildData ? 'Loading channels...' : 'Select a channel',\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                isDisabled: loadingGuildData,\n                                                                                children: guildData?.channels.filter((channel)=>channel.type === 'text').map((channel)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: channel.name,\n                                                                                        children: [\n                                                                                            \"#\",\n                                                                                            channel.name\n                                                                                        ]\n                                                                                    }, channel.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1024,\n                                                                                        columnNumber: 39\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1012,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                value: nodeData.channel || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        channel: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"Or type: general or {channel.name}\",\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                size: \"sm\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1029,\n                                                                                columnNumber: 21\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1011,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1009,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    (nodeData.actionType === 'addRole' || nodeData.actionType === 'removeRole') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Role Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1048,\n                                                                        columnNumber: 19\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                        spacing: 2,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                                                value: nodeData.role || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        role: e.target.value\n                                                                                    }),\n                                                                                placeholder: loadingGuildData ? 'Loading roles...' : 'Select a role',\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                isDisabled: loadingGuildData,\n                                                                                children: guildData?.roles.map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: role.name,\n                                                                                        children: [\n                                                                                            \"@\",\n                                                                                            role.name\n                                                                                        ]\n                                                                                    }, role.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1060,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1050,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                value: nodeData.role || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        role: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"Or type: Member or {user.role}\",\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                size: \"sm\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1065,\n                                                                                columnNumber: 19\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1049,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1047,\n                                                                columnNumber: 17\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Reason\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1077,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        value: nodeData.reason || '',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                reason: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Role updated by bot\",\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1078,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1076,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    (nodeData.actionType === 'kickUser' || nodeData.actionType === 'banUser') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"User\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1094,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                        spacing: 2,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                                                value: nodeData.user || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        user: e.target.value\n                                                                                    }),\n                                                                                placeholder: loadingGuildData ? 'Loading members...' : 'Select a user',\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                isDisabled: loadingGuildData,\n                                                                                children: guildData?.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: member.username,\n                                                                                        children: [\n                                                                                            member.displayName,\n                                                                                            \" (@\",\n                                                                                            member.username,\n                                                                                            \")\"\n                                                                                        ]\n                                                                                    }, member.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1106,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1096,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                value: nodeData.user || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        user: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"Or type: username or {user.id}\",\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                size: \"sm\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1111,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1095,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1093,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Reason\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1123,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        value: nodeData.reason || '',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                reason: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Violation of server rules\",\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1124,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1122,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            nodeData.actionType === 'banUser' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Delete Message History\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1135,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                        isChecked: nodeData.deleteMessages || false,\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                deleteMessages: e.target.checked\n                                                                            }),\n                                                                        colorScheme: \"purple\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1136,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1134,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    nodeData.actionType === 'timeoutUser' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"User\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1150,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                        spacing: 2,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                                                value: nodeData.user || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        user: e.target.value\n                                                                                    }),\n                                                                                placeholder: loadingGuildData ? 'Loading members...' : 'Select a user',\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                isDisabled: loadingGuildData,\n                                                                                children: guildData?.members.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: member.username,\n                                                                                        children: [\n                                                                                            member.displayName,\n                                                                                            \" (@\",\n                                                                                            member.username,\n                                                                                            \")\"\n                                                                                        ]\n                                                                                    }, member.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1162,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1152,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                value: nodeData.user || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        user: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"Or type: username or {user.id}\",\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                size: \"sm\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1167,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1151,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1149,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Duration (minutes)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1179,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.NumberInput, {\n                                                                        value: nodeData.duration || 10,\n                                                                        onChange: (valueString)=>updateNodeData({\n                                                                                duration: parseInt(valueString) || 10\n                                                                            }),\n                                                                        min: 1,\n                                                                        max: 40320,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.NumberInputField, {\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1186,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.NumberInputStepper, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.NumberIncrementStepper, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1192,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.NumberDecrementStepper, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                        lineNumber: 1193,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1191,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1180,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1178,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Reason\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1198,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        value: nodeData.reason || '',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                reason: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Timeout for spam\",\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1199,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1197,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    nodeData.actionType === 'addReaction' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                    color: currentScheme.colors.text,\n                                                                    children: \"Reaction (emoji)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                    lineNumber: 1215,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: nodeData.reaction || '',\n                                                                    onChange: (e)=>updateNodeData({\n                                                                            reaction: e.target.value\n                                                                        }),\n                                                                    placeholder: \"\\uD83D\\uDC4D or :thumbsup:\",\n                                                                    bg: currentScheme.colors.background,\n                                                                    color: currentScheme.colors.text,\n                                                                    borderColor: currentScheme.colors.border\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                    lineNumber: 1216,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                            lineNumber: 1214,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 1213,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    nodeData.actionType === 'createChannel' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                        spacing: 4,\n                                                        align: \"stretch\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Channel Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1232,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        value: nodeData.channelName || '',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                channelName: e.target.value\n                                                                            }),\n                                                                        placeholder: \"new-channel\",\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1233,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1231,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Channel Type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1243,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                                        value: nodeData.channelType || 'text',\n                                                                        onChange: (e)=>updateNodeData({\n                                                                                channelType: e.target.value\n                                                                            }),\n                                                                        bg: currentScheme.colors.background,\n                                                                        color: currentScheme.colors.text,\n                                                                        borderColor: currentScheme.colors.border,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"text\",\n                                                                                children: \"Text Channel\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1251,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"voice\",\n                                                                                children: \"Voice Channel\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1252,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"category\",\n                                                                                children: \"Category\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                                lineNumber: 1253,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                        lineNumber: 1244,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                                lineNumber: 1242,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                        lineNumber: 1230,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                                lineNumber: 991,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            colorScheme: \"purple\",\n                                            onClick: ()=>{\n                                                // Save the configuration\n                                                data.actionType = nodeData.actionType;\n                                                data.message = nodeData.message;\n                                                data.channel = nodeData.channel;\n                                                data.role = nodeData.role;\n                                                data.user = nodeData.user;\n                                                data.embed = nodeData.embed;\n                                                data.reason = nodeData.reason;\n                                                data.duration = nodeData.duration;\n                                                data.deleteMessages = nodeData.deleteMessages;\n                                                data.reaction = nodeData.reaction;\n                                                data.channelName = nodeData.channelName;\n                                                data.channelType = nodeData.channelType;\n                                                data.label = nodeData.actionType ? getActionLabel(nodeData.actionType) : 'Action';\n                                                onClose();\n                                            },\n                                            size: \"lg\",\n                                            width: \"full\",\n                                            children: \"Save Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                            lineNumber: 1263,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\ActionNode.tsx\",\n                lineNumber: 571,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n});\nActionNode.displayName = 'ActionNode';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ActionNode);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/flow/ActionNode.tsx\n");

/***/ })

};
;