"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8255],{6498:(t,n,r)=>{r.d(n,{M:()=>f});var e=r(94285),c=r(3638),a=r(45096),o=r(14901),i=r(47655);function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var e in r)({}).hasOwnProperty.call(r,e)&&(t[e]=r[e])}return t}).apply(null,arguments)}var s=(t,n,r,e,c)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(e)/2),i=e>=0?1:-1,u=r>=0?1:-1,s=+(e>=0&&r>=0||e<0&&r<0);if(o>0&&c instanceof Array){for(var l=[0,0,0,0],f=0;f<4;f++)l[f]=c[f]>o?o:c[f];a="M".concat(t,",").concat(n+i*l[0]),l[0]>0&&(a+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+u*l[0],",").concat(n)),a+="L ".concat(t+r-u*l[1],",").concat(n),l[1]>0&&(a+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,",\n        ").concat(t+r,",").concat(n+i*l[1])),a+="L ".concat(t+r,",").concat(n+e-i*l[2]),l[2]>0&&(a+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,",\n        ").concat(t+r-u*l[2],",").concat(n+e)),a+="L ".concat(t+u*l[3],",").concat(n+e),l[3]>0&&(a+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,",\n        ").concat(t,",").concat(n+e-i*l[3])),a+="Z"}else if(o>0&&c===+c&&c>0){var y=Math.min(o,c);a="M ".concat(t,",").concat(n+i*y,"\n            A ").concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+u*y,",").concat(n,"\n            L ").concat(t+r-u*y,",").concat(n,"\n            A ").concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+r,",").concat(n+i*y,"\n            L ").concat(t+r,",").concat(n+e-i*y,"\n            A ").concat(y,",").concat(y,",0,0,").concat(s,",").concat(t+r-u*y,",").concat(n+e,"\n            L ").concat(t+u*y,",").concat(n+e,"\n            A ").concat(y,",").concat(y,",0,0,").concat(s,",").concat(t,",").concat(n+e-i*y," Z")}else a="M ".concat(t,",").concat(n," h ").concat(r," v ").concat(e," h ").concat(-r," Z");return a},l={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=t=>{var n=(0,o.e)(t,l),r=(0,e.useRef)(null),[f,y]=(0,e.useState)(-1);(0,e.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&y(t)}catch(t){}},[]);var{x:v,y:b,width:p,height:g,radius:h,className:m}=n,{animationEasing:O,animationDuration:d,animationBegin:j,isAnimationActive:P,isUpdateAnimationActive:w}=n;if(v!==+v||b!==+b||p!==+p||g!==+g||0===p||0===g)return null;var x=(0,c.$)("recharts-rectangle",m);return w?e.createElement(i.i,{canBegin:f>0,from:{width:p,height:g,x:v,y:b},to:{width:p,height:g,x:v,y:b},duration:d,animationEasing:O,isActive:w},t=>{var{width:c,height:o,x:l,y:y}=t;return e.createElement(i.i,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:j,duration:d,isActive:P,easing:O},e.createElement("path",u({},(0,a.J9)(n,!0),{className:x,d:s(l,y,c,o,h),ref:r})))}):e.createElement("path",u({},(0,a.J9)(n,!0),{className:x,d:s(v,b,p,g,h)}))}},45387:(t,n,r)=>{r.d(n,{F:()=>f});var e=r(94285),c=r(3638),a=r(93833),o=r(45096),i=["x","y","top","left","width","height","className"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var e in r)({}).hasOwnProperty.call(r,e)&&(t[e]=r[e])}return t}).apply(null,arguments)}function s(t,n){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(t);n&&(e=e.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),r.push.apply(r,e)}return r}var l=(t,n,r,e,c,a)=>"M".concat(t,",").concat(c,"v").concat(e,"M").concat(a,",").concat(n,"h").concat(r),f=t=>{var{x:n=0,y:r=0,top:f=0,left:y=0,width:v=0,height:b=0,className:p}=t,g=function(t){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?s(Object(r),!0).forEach(function(n){var e,c,a;e=t,c=n,a=r[n],(c=function(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,n||"default");if("object"!=typeof e)return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}(c))in e?Object.defineProperty(e,c,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[c]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}({x:n,y:r,top:f,left:y,width:v,height:b},function(t,n){if(null==t)return{};var r,e,c=function(t,n){if(null==t)return{};var r={};for(var e in t)if(({}).hasOwnProperty.call(t,e)){if(-1!==n.indexOf(e))continue;r[e]=t[e]}return r}(t,n);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(e=0;e<a.length;e++)r=a[e],-1===n.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(c[r]=t[r])}return c}(t,i));return(0,a.Et)(n)&&(0,a.Et)(r)&&(0,a.Et)(v)&&(0,a.Et)(b)&&(0,a.Et)(f)&&(0,a.Et)(y)?e.createElement("path",u({},(0,o.J9)(g,!0),{className:(0,c.$)("recharts-cross",p),d:l(n,r,v,b,f,y)})):null}},81090:(t,n,r)=>{r.d(n,{i:()=>g});var e=r(94285),c=r(78869),a=r(3638),o=r(45096),i=r(93833),u=["type","size","sizeType"];function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var e in r)({}).hasOwnProperty.call(r,e)&&(t[e]=r[e])}return t}).apply(null,arguments)}function l(t,n){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(t);n&&(e=e.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),r.push.apply(r,e)}return r}function f(t){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(n){var e,c,a;e=t,c=n,a=r[n],(c=function(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,n||"default");if("object"!=typeof e)return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}(c))in e?Object.defineProperty(e,c,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[c]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}var y={symbolCircle:c.hK,symbolCross:c.BV,symbolDiamond:c.j,symbolSquare:c.yD,symbolStar:c.N8,symbolTriangle:c.ZK,symbolWye:c.IJ},v=Math.PI/180,b=t=>y["symbol".concat((0,i.Zb)(t))]||c.hK,p=(t,n,r)=>{if("area"===n)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var e=18*v;return 1.25*t*t*(Math.tan(e)-Math.tan(2*e)*Math.tan(e)**2);case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},g=t=>{var{type:n="circle",size:r=64,sizeType:i="area"}=t,l=f(f({},function(t,n){if(null==t)return{};var r,e,c=function(t,n){if(null==t)return{};var r={};for(var e in t)if(({}).hasOwnProperty.call(t,e)){if(-1!==n.indexOf(e))continue;r[e]=t[e]}return r}(t,n);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(e=0;e<a.length;e++)r=a[e],-1===n.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(c[r]=t[r])}return c}(t,u)),{},{type:n,size:r,sizeType:i}),{className:y,cx:v,cy:g}=l,h=(0,o.J9)(l,!0);return v===+v&&g===+g&&r===+r?e.createElement("path",s({},h,{className:(0,a.$)("recharts-symbols",y),transform:"translate(".concat(v,", ").concat(g,")"),d:(()=>{var t=b(n);return(0,c.HR)().type(t).size(p(r,i,n))()})()})):null};g.registerSymbol=(t,n)=>{y["symbol".concat((0,i.Zb)(t))]=n}},87964:(t,n,r)=>{r.d(n,{I:()=>O});var e=r(94285),c=r(78869),a=r(3638),o=r(83733),i=r(45096),u=r(93833),s=r(6944);function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var e in r)({}).hasOwnProperty.call(r,e)&&(t[e]=r[e])}return t}).apply(null,arguments)}function f(t,n){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(t);n&&(e=e.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),r.push.apply(r,e)}return r}function y(t){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?f(Object(r),!0).forEach(function(n){var e,c,a;e=t,c=n,a=r[n],(c=function(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,n||"default");if("object"!=typeof e)return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}(c))in e?Object.defineProperty(e,c,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[c]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}var v={curveBasisClosed:c.Yu,curveBasisOpen:c.IA,curveBasis:c.qr,curveBumpX:c.Wi,curveBumpY:c.PG,curveLinearClosed:c.Lx,curveLinear:c.lU,curveMonotoneX:c.nV,curveMonotoneY:c.ux,curveNatural:c.Xf,curveStep:c.GZ,curveStepAfter:c.UP,curveStepBefore:c.dy},b=t=>(0,s.H)(t.x)&&(0,s.H)(t.y),p=t=>t.x,g=t=>t.y,h=(t,n)=>{if("function"==typeof t)return t;var r="curve".concat((0,u.Zb)(t));return("curveMonotone"===r||"curveBump"===r)&&n?v["".concat(r).concat("vertical"===n?"Y":"X")]:v[r]||c.lU},m=t=>{var n,{type:r="linear",points:e=[],baseLine:a,layout:o,connectNulls:i=!1}=t,s=h(r,o),l=i?e.filter(b):e;if(Array.isArray(a)){var f=i?a.filter(t=>b(t)):a,v=l.map((t,n)=>y(y({},t),{},{base:f[n]}));return(n="vertical"===o?(0,c.Wc)().y(g).x1(p).x0(t=>t.base.x):(0,c.Wc)().x(p).y1(g).y0(t=>t.base.y)).defined(b).curve(s),n(v)}return(n="vertical"===o&&(0,u.Et)(a)?(0,c.Wc)().y(g).x1(p).x0(a):(0,u.Et)(a)?(0,c.Wc)().x(p).y1(g).y0(a):(0,c.n8)().x(p).y(g)).defined(b).curve(s),n(l)},O=t=>{var{className:n,points:r,path:c,pathRef:u}=t;if((!r||!r.length)&&!c)return null;var s=r&&r.length?m(t):c;return e.createElement("path",l({},(0,i.J9)(t,!1),(0,o._U)(t),{className:(0,a.$)("recharts-curve",n),d:null===s?void 0:s,ref:u}))}},94863:(t,n,r)=>{r.d(n,{h:()=>p});var e=r(94285),c=r(3638),a=r(45096),o=r(41529),i=r(93833),u=r(14901);function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var e in r)({}).hasOwnProperty.call(r,e)&&(t[e]=r[e])}return t}).apply(null,arguments)}var l=(t,n)=>(0,i.sA)(n-t)*Math.min(Math.abs(n-t),359.999),f=t=>{var{cx:n,cy:r,radius:e,angle:c,sign:a,isExternal:i,cornerRadius:u,cornerIsExternal:s}=t,l=u*(i?1:-1)+e,f=Math.asin(u/l)/o.Kg,y=s?c:c+a*f,v=(0,o.IZ)(n,r,l,y);return{center:v,circleTangency:(0,o.IZ)(n,r,e,y),lineTangency:(0,o.IZ)(n,r,l*Math.cos(f*o.Kg),s?c-a*f:c),theta:f}},y=t=>{var{cx:n,cy:r,innerRadius:e,outerRadius:c,startAngle:a,endAngle:i}=t,u=l(a,i),s=a+u,f=(0,o.IZ)(n,r,c,a),y=(0,o.IZ)(n,r,c,s),v="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(c,",").concat(c,",0,\n    ").concat(+(Math.abs(u)>180),",").concat(+(a>s),",\n    ").concat(y.x,",").concat(y.y,"\n  ");if(e>0){var b=(0,o.IZ)(n,r,e,a),p=(0,o.IZ)(n,r,e,s);v+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(e,",").concat(e,",0,\n            ").concat(+(Math.abs(u)>180),",").concat(+(a<=s),",\n            ").concat(b.x,",").concat(b.y," Z")}else v+="L ".concat(n,",").concat(r," Z");return v},v=t=>{var{cx:n,cy:r,innerRadius:e,outerRadius:c,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:u,startAngle:s,endAngle:l}=t,v=(0,i.sA)(l-s),{circleTangency:b,lineTangency:p,theta:g}=f({cx:n,cy:r,radius:c,angle:s,sign:v,cornerRadius:a,cornerIsExternal:u}),{circleTangency:h,lineTangency:m,theta:O}=f({cx:n,cy:r,radius:c,angle:l,sign:-v,cornerRadius:a,cornerIsExternal:u}),d=u?Math.abs(s-l):Math.abs(s-l)-g-O;if(d<0)return o?"M ".concat(p.x,",").concat(p.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):y({cx:n,cy:r,innerRadius:e,outerRadius:c,startAngle:s,endAngle:l});var j="M ".concat(p.x,",").concat(p.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(v<0),",").concat(b.x,",").concat(b.y,"\n    A").concat(c,",").concat(c,",0,").concat(+(d>180),",").concat(+(v<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(v<0),",").concat(m.x,",").concat(m.y,"\n  ");if(e>0){var{circleTangency:P,lineTangency:w,theta:x}=f({cx:n,cy:r,radius:e,angle:s,sign:v,isExternal:!0,cornerRadius:a,cornerIsExternal:u}),{circleTangency:M,lineTangency:E,theta:A}=f({cx:n,cy:r,radius:e,angle:l,sign:-v,isExternal:!0,cornerRadius:a,cornerIsExternal:u}),S=u?Math.abs(s-l):Math.abs(s-l)-x-A;if(S<0&&0===a)return"".concat(j,"L").concat(n,",").concat(r,"Z");j+="L".concat(E.x,",").concat(E.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(v<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(e,",").concat(e,",0,").concat(+(S>180),",").concat(+(v>0),",").concat(P.x,",").concat(P.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(v<0),",").concat(w.x,",").concat(w.y,"Z")}else j+="L".concat(n,",").concat(r,"Z");return j},b={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},p=t=>{var n,r=(0,u.e)(t,b),{cx:o,cy:l,innerRadius:f,outerRadius:p,cornerRadius:g,forceCornerRadius:h,cornerIsExternal:m,startAngle:O,endAngle:d,className:j}=r;if(p<f||O===d)return null;var P=(0,c.$)("recharts-sector",j),w=p-f,x=(0,i.F4)(g,w,0,!0);return n=x>0&&360>Math.abs(O-d)?v({cx:o,cy:l,innerRadius:f,outerRadius:p,cornerRadius:Math.min(x,w/2),forceCornerRadius:h,cornerIsExternal:m,startAngle:O,endAngle:d}):y({cx:o,cy:l,innerRadius:f,outerRadius:p,startAngle:O,endAngle:d}),e.createElement("path",s({},(0,a.J9)(r,!0),{className:P,d:n}))}}}]);