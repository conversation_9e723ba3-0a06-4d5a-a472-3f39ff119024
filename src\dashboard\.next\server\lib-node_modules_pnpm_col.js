"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "lib-node_modules_pnpm_col";
exports.ids = ["lib-node_modules_pnpm_col"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/dist/index.exports.import.es.mjs":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/dist/index.exports.import.es.mjs ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorError: () => (/* binding */ ColorError$1),\n/* harmony export */   adjustHue: () => (/* binding */ adjustHue),\n/* harmony export */   darken: () => (/* binding */ darken),\n/* harmony export */   desaturate: () => (/* binding */ desaturate),\n/* harmony export */   getContrast: () => (/* binding */ getContrast),\n/* harmony export */   getLuminance: () => (/* binding */ getLuminance),\n/* harmony export */   getScale: () => (/* binding */ getScale),\n/* harmony export */   guard: () => (/* binding */ guard),\n/* harmony export */   hasBadContrast: () => (/* binding */ hasBadContrast),\n/* harmony export */   hsla: () => (/* binding */ hsla),\n/* harmony export */   lighten: () => (/* binding */ lighten),\n/* harmony export */   mix: () => (/* binding */ mix),\n/* harmony export */   opacify: () => (/* binding */ opacify),\n/* harmony export */   parseToHsla: () => (/* binding */ parseToHsla),\n/* harmony export */   parseToRgba: () => (/* binding */ parseToRgba),\n/* harmony export */   readableColor: () => (/* binding */ readableColor),\n/* harmony export */   readableColorIsBlack: () => (/* binding */ readableColorIsBlack),\n/* harmony export */   rgba: () => (/* binding */ rgba),\n/* harmony export */   saturate: () => (/* binding */ saturate),\n/* harmony export */   toHex: () => (/* binding */ toHex),\n/* harmony export */   toHsla: () => (/* binding */ toHsla),\n/* harmony export */   toRgba: () => (/* binding */ toRgba),\n/* harmony export */   transparentize: () => (/* binding */ transparentize)\n/* harmony export */ });\n/**\n * A simple guard function:\n *\n * ```js\n * Math.min(Math.max(low, value), high)\n * ```\n */\nfunction guard(low, high, value) {\n  return Math.min(Math.max(low, value), high);\n}\n\nclass ColorError extends Error {\n  constructor(color) {\n    super(`Failed to parse color: \"${color}\"`);\n  }\n}\nvar ColorError$1 = ColorError;\n\n/**\n * Parses a color into red, gree, blue, alpha parts\n *\n * @param color the input color. Can be a RGB, RBGA, HSL, HSLA, or named color\n */\nfunction parseToRgba(color) {\n  if (typeof color !== 'string') throw new ColorError$1(color);\n  if (color.trim().toLowerCase() === 'transparent') return [0, 0, 0, 0];\n  let normalizedColor = color.trim();\n  normalizedColor = namedColorRegex.test(color) ? nameToHex(color) : color;\n  const reducedHexMatch = reducedHexRegex.exec(normalizedColor);\n  if (reducedHexMatch) {\n    const arr = Array.from(reducedHexMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(r(x, 2), 16)), parseInt(r(arr[3] || 'f', 2), 16) / 255];\n  }\n  const hexMatch = hexRegex.exec(normalizedColor);\n  if (hexMatch) {\n    const arr = Array.from(hexMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(x, 16)), parseInt(arr[3] || 'ff', 16) / 255];\n  }\n  const rgbaMatch = rgbaRegex.exec(normalizedColor);\n  if (rgbaMatch) {\n    const arr = Array.from(rgbaMatch).slice(1);\n    return [...arr.slice(0, 3).map(x => parseInt(x, 10)), parseFloat(arr[3] || '1')];\n  }\n  const hslaMatch = hslaRegex.exec(normalizedColor);\n  if (hslaMatch) {\n    const [h, s, l, a] = Array.from(hslaMatch).slice(1).map(parseFloat);\n    if (guard(0, 100, s) !== s) throw new ColorError$1(color);\n    if (guard(0, 100, l) !== l) throw new ColorError$1(color);\n    return [...hslToRgb(h, s, l), Number.isNaN(a) ? 1 : a];\n  }\n  throw new ColorError$1(color);\n}\nfunction hash(str) {\n  let hash = 5381;\n  let i = str.length;\n  while (i) {\n    hash = hash * 33 ^ str.charCodeAt(--i);\n  }\n\n  /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */\n  return (hash >>> 0) % 2341;\n}\nconst colorToInt = x => parseInt(x.replace(/_/g, ''), 36);\nconst compressedColorMap = '1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm'.split(' ').reduce((acc, next) => {\n  const key = colorToInt(next.substring(0, 3));\n  const hex = colorToInt(next.substring(3)).toString(16);\n\n  // NOTE: padStart could be used here but it breaks Node 6 compat\n  // https://github.com/ricokahler/color2k/issues/351\n  let prefix = '';\n  for (let i = 0; i < 6 - hex.length; i++) {\n    prefix += '0';\n  }\n  acc[key] = `${prefix}${hex}`;\n  return acc;\n}, {});\n\n/**\n * Checks if a string is a CSS named color and returns its equivalent hex value, otherwise returns the original color.\n */\nfunction nameToHex(color) {\n  const normalizedColorName = color.toLowerCase().trim();\n  const result = compressedColorMap[hash(normalizedColorName)];\n  if (!result) throw new ColorError$1(color);\n  return `#${result}`;\n}\nconst r = (str, amount) => Array.from(Array(amount)).map(() => str).join('');\nconst reducedHexRegex = new RegExp(`^#${r('([a-f0-9])', 3)}([a-f0-9])?$`, 'i');\nconst hexRegex = new RegExp(`^#${r('([a-f0-9]{2})', 3)}([a-f0-9]{2})?$`, 'i');\nconst rgbaRegex = new RegExp(`^rgba?\\\\(\\\\s*(\\\\d+)\\\\s*${r(',\\\\s*(\\\\d+)\\\\s*', 2)}(?:,\\\\s*([\\\\d.]+))?\\\\s*\\\\)$`, 'i');\nconst hslaRegex = /^hsla?\\(\\s*([\\d.]+)\\s*,\\s*([\\d.]+)%\\s*,\\s*([\\d.]+)%(?:\\s*,\\s*([\\d.]+))?\\s*\\)$/i;\nconst namedColorRegex = /^[a-z]+$/i;\nconst roundColor = color => {\n  return Math.round(color * 255);\n};\nconst hslToRgb = (hue, saturation, lightness) => {\n  let l = lightness / 100;\n  if (saturation === 0) {\n    // achromatic\n    return [l, l, l].map(roundColor);\n  }\n\n  // formulae from https://en.wikipedia.org/wiki/HSL_and_HSV\n  const huePrime = (hue % 360 + 360) % 360 / 60;\n  const chroma = (1 - Math.abs(2 * l - 1)) * (saturation / 100);\n  const secondComponent = chroma * (1 - Math.abs(huePrime % 2 - 1));\n  let red = 0;\n  let green = 0;\n  let blue = 0;\n  if (huePrime >= 0 && huePrime < 1) {\n    red = chroma;\n    green = secondComponent;\n  } else if (huePrime >= 1 && huePrime < 2) {\n    red = secondComponent;\n    green = chroma;\n  } else if (huePrime >= 2 && huePrime < 3) {\n    green = chroma;\n    blue = secondComponent;\n  } else if (huePrime >= 3 && huePrime < 4) {\n    green = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 4 && huePrime < 5) {\n    red = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 5 && huePrime < 6) {\n    red = chroma;\n    blue = secondComponent;\n  }\n  const lightnessModification = l - chroma / 2;\n  const finalRed = red + lightnessModification;\n  const finalGreen = green + lightnessModification;\n  const finalBlue = blue + lightnessModification;\n  return [finalRed, finalGreen, finalBlue].map(roundColor);\n};\n\n// taken from:\n// https://github.com/styled-components/polished/blob/a23a6a2bb26802b3d922d9c3b67bac3f3a54a310/src/internalHelpers/_rgbToHsl.js\n\n/**\n * Parses a color in hue, saturation, lightness, and the alpha channel.\n *\n * Hue is a number between 0 and 360, saturation, lightness, and alpha are\n * decimal percentages between 0 and 1\n */\nfunction parseToHsla(color) {\n  const [red, green, blue, alpha] = parseToRgba(color).map((value, index) =>\n  // 3rd index is alpha channel which is already normalized\n  index === 3 ? value : value / 255);\n  const max = Math.max(red, green, blue);\n  const min = Math.min(red, green, blue);\n  const lightness = (max + min) / 2;\n\n  // achromatic\n  if (max === min) return [0, 0, lightness, alpha];\n  const delta = max - min;\n  const saturation = lightness > 0.5 ? delta / (2 - max - min) : delta / (max + min);\n  const hue = 60 * (red === max ? (green - blue) / delta + (green < blue ? 6 : 0) : green === max ? (blue - red) / delta + 2 : (red - green) / delta + 4);\n  return [hue, saturation, lightness, alpha];\n}\n\n/**\n * Takes in hsla parts and constructs an hsla string\n *\n * @param hue The color circle (from 0 to 360) - 0 (or 360) is red, 120 is green, 240 is blue\n * @param saturation Percentage of saturation, given as a decimal between 0 and 1\n * @param lightness Percentage of lightness, given as a decimal between 0 and 1\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction hsla(hue, saturation, lightness, alpha) {\n  return `hsla(${(hue % 360).toFixed()}, ${guard(0, 100, saturation * 100).toFixed()}%, ${guard(0, 100, lightness * 100).toFixed()}%, ${parseFloat(guard(0, 1, alpha).toFixed(3))})`;\n}\n\n/**\n * Adjusts the current hue of the color by the given degrees. Wraps around when\n * over 360.\n *\n * @param color input color\n * @param degrees degrees to adjust the input color, accepts degree integers\n * (0 - 360) and wraps around on overflow\n */\nfunction adjustHue(color, degrees) {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h + degrees, s, l, a);\n}\n\n/**\n * Darkens using lightness. This is equivalent to subtracting the lightness\n * from the L in HSL.\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction darken(color, amount) {\n  const [hue, saturation, lightness, alpha] = parseToHsla(color);\n  return hsla(hue, saturation, lightness - amount, alpha);\n}\n\n/**\n * Desaturates the input color by the given amount via subtracting from the `s`\n * in `hsla`.\n *\n * @param amount The amount to desaturate, given as a decimal between 0 and 1\n */\nfunction desaturate(color, amount) {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h, s - amount, l, a);\n}\n\n// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getLuminance.js\n\n/**\n * Returns a number (float) representing the luminance of a color.\n */\nfunction getLuminance(color) {\n  if (color === 'transparent') return 0;\n  function f(x) {\n    const channel = x / 255;\n    return channel <= 0.04045 ? channel / 12.92 : Math.pow((channel + 0.055) / 1.055, 2.4);\n  }\n  const [r, g, b] = parseToRgba(color);\n  return 0.2126 * f(r) + 0.7152 * f(g) + 0.0722 * f(b);\n}\n\n// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getContrast.js\n\n/**\n * Returns the contrast ratio between two colors based on\n * [W3's recommended equation for calculating contrast](http://www.w3.org/TR/WCAG20/#contrast-ratiodef).\n */\nfunction getContrast(color1, color2) {\n  const luminance1 = getLuminance(color1);\n  const luminance2 = getLuminance(color2);\n  return luminance1 > luminance2 ? (luminance1 + 0.05) / (luminance2 + 0.05) : (luminance2 + 0.05) / (luminance1 + 0.05);\n}\n\n/**\n * Takes in rgba parts and returns an rgba string\n *\n * @param red The amount of red in the red channel, given in a number between 0 and 255 inclusive\n * @param green The amount of green in the red channel, given in a number between 0 and 255 inclusive\n * @param blue The amount of blue in the red channel, given in a number between 0 and 255 inclusive\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction rgba(red, green, blue, alpha) {\n  return `rgba(${guard(0, 255, red).toFixed()}, ${guard(0, 255, green).toFixed()}, ${guard(0, 255, blue).toFixed()}, ${parseFloat(guard(0, 1, alpha).toFixed(3))})`;\n}\n\n/**\n * Mixes two colors together. Taken from sass's implementation.\n */\nfunction mix(color1, color2, weight) {\n  const normalize = (n, index) =>\n  // 3rd index is alpha channel which is already normalized\n  index === 3 ? n : n / 255;\n  const [r1, g1, b1, a1] = parseToRgba(color1).map(normalize);\n  const [r2, g2, b2, a2] = parseToRgba(color2).map(normalize);\n\n  // The formula is copied from the original Sass implementation:\n  // http://sass-lang.com/documentation/Sass/Script/Functions.html#mix-instance_method\n  const alphaDelta = a2 - a1;\n  const normalizedWeight = weight * 2 - 1;\n  const combinedWeight = normalizedWeight * alphaDelta === -1 ? normalizedWeight : normalizedWeight + alphaDelta / (1 + normalizedWeight * alphaDelta);\n  const weight2 = (combinedWeight + 1) / 2;\n  const weight1 = 1 - weight2;\n  const r = (r1 * weight1 + r2 * weight2) * 255;\n  const g = (g1 * weight1 + g2 * weight2) * 255;\n  const b = (b1 * weight1 + b2 * weight2) * 255;\n  const a = a2 * weight + a1 * (1 - weight);\n  return rgba(r, g, b, a);\n}\n\n/**\n * Given a series colors, this function will return a `scale(x)` function that\n * accepts a percentage as a decimal between 0 and 1 and returns the color at\n * that percentage in the scale.\n *\n * ```js\n * const scale = getScale('red', 'yellow', 'green');\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(0.5)); // rgba(255, 255, 0, 1)\n * console.log(scale(1)); // rgba(0, 128, 0, 1)\n * ```\n *\n * If you'd like to limit the domain and range like chroma-js, we recommend\n * wrapping scale again.\n *\n * ```js\n * const _scale = getScale('red', 'yellow', 'green');\n * const scale = x => _scale(x / 100);\n *\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(50)); // rgba(255, 255, 0, 1)\n * console.log(scale(100)); // rgba(0, 128, 0, 1)\n * ```\n */\nfunction getScale(...colors) {\n  return n => {\n    const lastIndex = colors.length - 1;\n    const lowIndex = guard(0, lastIndex, Math.floor(n * lastIndex));\n    const highIndex = guard(0, lastIndex, Math.ceil(n * lastIndex));\n    const color1 = colors[lowIndex];\n    const color2 = colors[highIndex];\n    const unit = 1 / lastIndex;\n    const weight = (n - unit * lowIndex) / unit;\n    return mix(color1, color2, weight);\n  };\n}\n\nconst guidelines = {\n  decorative: 1.5,\n  readable: 3,\n  aa: 4.5,\n  aaa: 7\n};\n\n/**\n * Returns whether or not a color has bad contrast against a background\n * according to a given standard.\n */\nfunction hasBadContrast(color, standard = 'aa', background = '#fff') {\n  return getContrast(color, background) < guidelines[standard];\n}\n\n/**\n * Lightens a color by a given amount. This is equivalent to\n * `darken(color, -amount)`\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction lighten(color, amount) {\n  return darken(color, -amount);\n}\n\n/**\n * Takes in a color and makes it more transparent by convert to `rgba` and\n * decreasing the amount in the alpha channel.\n *\n * @param amount The amount to increase the transparency by, given as a decimal between 0 and 1\n */\nfunction transparentize(color, amount) {\n  const [r, g, b, a] = parseToRgba(color);\n  return rgba(r, g, b, a - amount);\n}\n\n/**\n * Takes a color and un-transparentizes it. Equivalent to\n * `transparentize(color, -amount)`\n *\n * @param amount The amount to increase the opacity by, given as a decimal between 0 and 1\n */\nfunction opacify(color, amount) {\n  return transparentize(color, -amount);\n}\n\n/**\n * An alternative function to `readableColor`. Returns whether or not the \n * readable color (i.e. the color to be place on top the input color) should be\n * black.\n */\nfunction readableColorIsBlack(color) {\n  return getLuminance(color) > 0.179;\n}\n\n/**\n * Returns black or white for best contrast depending on the luminosity of the\n * given color.\n */\nfunction readableColor(color) {\n  return readableColorIsBlack(color) ? '#000' : '#fff';\n}\n\n/**\n * Saturates a color by converting it to `hsl` and increasing the saturation\n * amount. Equivalent to `desaturate(color, -amount)`\n * \n * @param color Input color\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction saturate(color, amount) {\n  return desaturate(color, -amount);\n}\n\n/**\n * Takes in any color and returns it as a hex code.\n */\nfunction toHex(color) {\n  const [r, g, b, a] = parseToRgba(color);\n  let hex = x => {\n    const h = guard(0, 255, x).toString(16);\n    // NOTE: padStart could be used here but it breaks Node 6 compat\n    // https://github.com/ricokahler/color2k/issues/351\n    return h.length === 1 ? `0${h}` : h;\n  };\n  return `#${hex(r)}${hex(g)}${hex(b)}${a < 1 ? hex(Math.round(a * 255)) : ''}`;\n}\n\n/**\n * Takes in any color and returns it as an rgba string.\n */\nfunction toRgba(color) {\n  return rgba(...parseToRgba(color));\n}\n\n/**\n * Takes in any color and returns it as an hsla string.\n */\nfunction toHsla(color) {\n  return hsla(...parseToHsla(color));\n}\n\n\n//# sourceMappingURL=index.exports.import.es.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/dist/index.exports.import.es.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/copy-to-clipboard@3.3.3/node_modules/copy-to-clipboard/index.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/copy-to-clipboard@3.3.3/node_modules/copy-to-clipboard/index.js ***!
  \************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar deselectCurrent = __webpack_require__(/*! toggle-selection */ \"(pages-dir-node)/../../node_modules/.pnpm/toggle-selection@1.0.6/node_modules/toggle-selection/index.js\");\n\nvar clipboardToIE11Formatting = {\n  \"text/plain\": \"Text\",\n  \"text/html\": \"Url\",\n  \"default\": \"Text\"\n}\n\nvar defaultMessage = \"Copy to clipboard: #{key}, Enter\";\n\nfunction format(message) {\n  var copyKey = (/mac os x/i.test(navigator.userAgent) ? \"⌘\" : \"Ctrl\") + \"+C\";\n  return message.replace(/#{\\s*key\\s*}/g, copyKey);\n}\n\nfunction copy(text, options) {\n  var debug,\n    message,\n    reselectPrevious,\n    range,\n    selection,\n    mark,\n    success = false;\n  if (!options) {\n    options = {};\n  }\n  debug = options.debug || false;\n  try {\n    reselectPrevious = deselectCurrent();\n\n    range = document.createRange();\n    selection = document.getSelection();\n\n    mark = document.createElement(\"span\");\n    mark.textContent = text;\n    // avoid screen readers from reading out loud the text\n    mark.ariaHidden = \"true\"\n    // reset user styles for span element\n    mark.style.all = \"unset\";\n    // prevents scrolling to the end of the page\n    mark.style.position = \"fixed\";\n    mark.style.top = 0;\n    mark.style.clip = \"rect(0, 0, 0, 0)\";\n    // used to preserve spaces and line breaks\n    mark.style.whiteSpace = \"pre\";\n    // do not inherit user-select (it may be `none`)\n    mark.style.webkitUserSelect = \"text\";\n    mark.style.MozUserSelect = \"text\";\n    mark.style.msUserSelect = \"text\";\n    mark.style.userSelect = \"text\";\n    mark.addEventListener(\"copy\", function(e) {\n      e.stopPropagation();\n      if (options.format) {\n        e.preventDefault();\n        if (typeof e.clipboardData === \"undefined\") { // IE 11\n          debug && console.warn(\"unable to use e.clipboardData\");\n          debug && console.warn(\"trying IE specific stuff\");\n          window.clipboardData.clearData();\n          var format = clipboardToIE11Formatting[options.format] || clipboardToIE11Formatting[\"default\"]\n          window.clipboardData.setData(format, text);\n        } else { // all other browsers\n          e.clipboardData.clearData();\n          e.clipboardData.setData(options.format, text);\n        }\n      }\n      if (options.onCopy) {\n        e.preventDefault();\n        options.onCopy(e.clipboardData);\n      }\n    });\n\n    document.body.appendChild(mark);\n\n    range.selectNodeContents(mark);\n    selection.addRange(range);\n\n    var successful = document.execCommand(\"copy\");\n    if (!successful) {\n      throw new Error(\"copy command was unsuccessful\");\n    }\n    success = true;\n  } catch (err) {\n    debug && console.error(\"unable to copy using execCommand: \", err);\n    debug && console.warn(\"trying IE specific stuff\");\n    try {\n      window.clipboardData.setData(options.format || \"text\", text);\n      options.onCopy && options.onCopy(window.clipboardData);\n      success = true;\n    } catch (err) {\n      debug && console.error(\"unable to copy using clipboardData: \", err);\n      debug && console.error(\"falling back to prompt\");\n      message = format(\"message\" in options ? options.message : defaultMessage);\n      window.prompt(message, text);\n    }\n  } finally {\n    if (selection) {\n      if (typeof selection.removeRange == \"function\") {\n        selection.removeRange(range);\n      } else {\n        selection.removeAllRanges();\n      }\n    }\n\n    if (mark) {\n      document.body.removeChild(mark);\n    }\n    reselectPrevious();\n  }\n\n  return success;\n}\n\nmodule.exports = copy;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/copy-to-clipboard@3.3.3/node_modules/copy-to-clipboard/index.js\n");

/***/ })

};
;