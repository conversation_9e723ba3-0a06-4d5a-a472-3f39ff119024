"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/discord/guild";
exports.ids = ["pages/api/discord/guild"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fguild&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cguild.ts&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fguild&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cguild.ts&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_discord_guild_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\discord\\guild.ts */ \"(api-node)/./pages/api/discord/guild.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_guild_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_guild_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/discord/guild\",\n        pathname: \"/api/discord/guild\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_discord_guild_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fguild&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cguild.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/discord/guild.ts":
/*!************************************!*\
  !*** ./pages/api/discord/guild.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nasync function handler(req, res) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session?.user) {\n            return res.status(401).json({\n                error: 'Unauthorized'\n            });\n        }\n        const { guildId, token } = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot;\n        if (!token || !guildId) {\n            console.error('Missing bot configuration');\n            return res.status(500).json({\n                error: 'Bot configuration missing'\n            });\n        }\n        if (req.method === 'GET') {\n            try {\n                // Fetch guild data from Discord API\n                const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}`, {\n                    headers: {\n                        Authorization: `Bot ${token}`\n                    }\n                });\n                if (!response.ok) {\n                    const errorData = await response.text();\n                    console.error('Discord API error fetching guild:', errorData);\n                    throw new Error(`Failed to fetch guild data: ${response.status}`);\n                }\n                const guildData = await response.json();\n                // Fetch bot's nickname in the guild\n                const botResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}/members/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId}`, {\n                    headers: {\n                        Authorization: `Bot ${token}`\n                    }\n                });\n                let botName = '';\n                if (botResponse.ok) {\n                    const botData = await botResponse.json();\n                    botName = botData.nick || botData.user?.username || '';\n                } else {\n                    console.warn('Could not fetch bot nickname:', await botResponse.text());\n                }\n                return res.status(200).json({\n                    id: guildData.id,\n                    name: guildData.name,\n                    icon: guildData.icon,\n                    botName: botName,\n                    features: guildData.features,\n                    description: guildData.description,\n                    preferred_locale: guildData.preferred_locale\n                });\n            } catch (error) {\n                console.error('Error fetching guild data:', error);\n                return res.status(500).json({\n                    error: 'Failed to fetch guild data',\n                    details: error.message\n                });\n            }\n        }\n        if (req.method === 'PATCH') {\n            // Check if user has admin permissions\n            if (!session.user.isAdmin) {\n                return res.status(403).json({\n                    error: 'Forbidden - Admin access required'\n                });\n            }\n            const { guildName, botName, icon } = req.body;\n            console.log('Updating guild settings:', {\n                guildName,\n                botName,\n                hasIcon: Boolean(icon)\n            });\n            try {\n                const updates = [];\n                let guildUpdateError = null;\n                let botUpdateError = null;\n                // Update guild name or icon\n                if (guildName || icon) {\n                    try {\n                        const payload = {};\n                        if (guildName) payload.name = guildName;\n                        if (icon) payload.icon = icon; // data URI string e.g., data:image/png;base64,...\n                        const guildResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}`, {\n                            method: 'PATCH',\n                            headers: {\n                                Authorization: `Bot ${token}`,\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify(payload)\n                        });\n                        if (!guildResponse.ok) {\n                            const errorData = await guildResponse.text();\n                            console.error('Discord API error updating guild settings:', errorData);\n                            guildUpdateError = `Failed to update guild settings: ${guildResponse.status}`;\n                        } else {\n                            updates.push(icon ? 'guild icon' : 'guild name');\n                        }\n                    } catch (error) {\n                        console.error('Error updating guild name/icon:', error);\n                        guildUpdateError = error.message;\n                    }\n                }\n                // Update bot nickname if provided\n                if (botName) {\n                    try {\n                        // Prefer the @me endpoint which is recommended for bots\n                        let botResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}/members/@me`, {\n                            method: 'PATCH',\n                            headers: {\n                                Authorization: `Bot ${token}`,\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                nick: botName\n                            })\n                        });\n                        // Some older API behaviours require the /@me/nick endpoint\n                        if (botResponse.status === 404 || botResponse.status === 405) {\n                            botResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}/members/@me/nick`, {\n                                method: 'PATCH',\n                                headers: {\n                                    Authorization: `Bot ${token}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    nick: botName\n                                })\n                            });\n                        }\n                        // Fallback to explicit user-id endpoint if still not ok\n                        if (!botResponse.ok) {\n                            botResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}/members/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId}`, {\n                                method: 'PATCH',\n                                headers: {\n                                    Authorization: `Bot ${token}`,\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    nick: botName\n                                })\n                            });\n                        }\n                        if (!botResponse.ok) {\n                            const errorData = await botResponse.text();\n                            console.error('Discord API error updating bot nickname:', errorData);\n                            botUpdateError = `Failed to update bot nickname: ${botResponse.status}`;\n                        } else {\n                            updates.push('bot nickname');\n                        }\n                    } catch (error) {\n                        console.error('Error updating bot nickname:', error);\n                        botUpdateError = error.message;\n                    }\n                }\n                // Handle errors and responses\n                if (guildUpdateError || botUpdateError) {\n                    const errors = [];\n                    if (guildUpdateError) errors.push(guildUpdateError);\n                    if (botUpdateError) errors.push(botUpdateError);\n                    const statusCode = updates.length > 0 ? 207 : 500; // 207 = Multi-Status (partial success)\n                    return res.status(statusCode).json({\n                        error: updates.length > 0 ? 'Partial success' : 'Failed to update settings',\n                        details: errors.join('; '),\n                        partialSuccess: updates.length > 0,\n                        succeeded: updates\n                    });\n                }\n                if (updates.length === 0) {\n                    return res.status(400).json({\n                        error: 'No valid updates provided'\n                    });\n                }\n                return res.status(200).json({\n                    message: `Successfully updated ${updates.join(' and ')}`,\n                    updated: updates\n                });\n            } catch (error) {\n                console.error('Error updating guild:', error);\n                return res.status(500).json({\n                    error: 'Failed to update guild settings',\n                    details: error.message\n                });\n            }\n        }\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    } catch (error) {\n        console.error('Error in guild handler:', error);\n        return res.status(500).json({\n            error: 'Internal server error',\n            details: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/discord/guild.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_d3-sc","lib-node_modules_pnpm_d3-time-","lib-node_modules_pnpm_dec","lib-node_modules_pnpm_e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_immer_10_1_1_node_modules_immer_dist_immer_mjs-806fdd73","lib-node_modules_pnpm_i","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_react-c","lib-node_modules_pnpm_react-red","lib-node_modules_pnpm_rea","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-5c215c87","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-bf697780","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-334e6784","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-d89a0eeb","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-4c6b09db","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-d29869f5","lib-node_modules_pnpm_rec","lib-node_modules_pnpm_redux_","lib-node_modules_pnpm_r","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fguild&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cguild.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();