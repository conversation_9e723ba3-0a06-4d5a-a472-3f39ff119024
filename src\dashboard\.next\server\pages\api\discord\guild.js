"use strict";(()=>{var t={};t.id=2913,t.ids=[2913],t.modules={15806:t=>{t.exports=require("next-auth/next")},20396:t=>{t.exports=require("next-auth/providers/discord")},29021:t=>{t.exports=require("fs")},33873:t=>{t.exports=require("path")},65542:t=>{t.exports=require("next-auth")},72115:t=>{t.exports=require("yaml")},72235:(t,e,s)=>{s.r(e),s.d(e,{config:()=>p,default:()=>c,routeModule:()=>h});var i={};s.r(i),s.d(i,{default:()=>l});var r=s(93433),a=s(20264),o=s(20584),n=s(15806),d=s(94506),u=s(98580);async function l(t,e){try{let s=await (0,n.getServerSession)(t,e,d.authOptions);if(!s?.user)return e.status(401).json({error:"Unauthorized"});let{guildId:i,token:r}=u.dashboardConfig.bot;if(!r||!i)return e.status(500).json({error:"Bot configuration missing"});if("GET"===t.method)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i}`,{headers:{Authorization:`Bot ${r}`}});if(!t.ok)throw await t.text(),Error(`Failed to fetch guild data: ${t.status}`);let s=await t.json(),a=await fetch(`https://discord.com/api/v10/guilds/${i}/members/${u.dashboardConfig.bot.clientId}`,{headers:{Authorization:`Bot ${r}`}}),o="";if(a.ok){let t=await a.json();o=t.nick||t.user?.username||""}return e.status(200).json({id:s.id,name:s.name,icon:s.icon,botName:o,features:s.features,description:s.description,preferred_locale:s.preferred_locale})}catch(t){return e.status(500).json({error:"Failed to fetch guild data",details:t.message})}if("PATCH"===t.method){if(!s.user.isAdmin)return e.status(403).json({error:"Forbidden - Admin access required"});let{guildName:a,botName:o,icon:n}=t.body;try{let t=[],s=null,d=null;if(a||n)try{let e={};a&&(e.name=a),n&&(e.icon=n);let o=await fetch(`https://discord.com/api/v10/guilds/${i}`,{method:"PATCH",headers:{Authorization:`Bot ${r}`,"Content-Type":"application/json"},body:JSON.stringify(e)});o.ok?t.push(n?"guild icon":"guild name"):(await o.text(),s=`Failed to update guild settings: ${o.status}`)}catch(t){s=t.message}if(o)try{let e=await fetch(`https://discord.com/api/v10/guilds/${i}/members/@me`,{method:"PATCH",headers:{Authorization:`Bot ${r}`,"Content-Type":"application/json"},body:JSON.stringify({nick:o})});(404===e.status||405===e.status)&&(e=await fetch(`https://discord.com/api/v10/guilds/${i}/members/@me/nick`,{method:"PATCH",headers:{Authorization:`Bot ${r}`,"Content-Type":"application/json"},body:JSON.stringify({nick:o})})),e.ok||(e=await fetch(`https://discord.com/api/v10/guilds/${i}/members/${u.dashboardConfig.bot.clientId}`,{method:"PATCH",headers:{Authorization:`Bot ${r}`,"Content-Type":"application/json"},body:JSON.stringify({nick:o})})),e.ok?t.push("bot nickname"):(await e.text(),d=`Failed to update bot nickname: ${e.status}`)}catch(t){d=t.message}if(s||d){let i=[];s&&i.push(s),d&&i.push(d);let r=t.length>0?207:500;return e.status(r).json({error:t.length>0?"Partial success":"Failed to update settings",details:i.join("; "),partialSuccess:t.length>0,succeeded:t})}if(0===t.length)return e.status(400).json({error:"No valid updates provided"});return e.status(200).json({message:`Successfully updated ${t.join(" and ")}`,updated:t})}catch(t){return e.status(500).json({error:"Failed to update guild settings",details:t.message})}}return e.status(405).json({error:"Method not allowed"})}catch(t){return e.status(500).json({error:"Internal server error",details:t.message})}}let c=(0,o.M)(i,"default"),p=(0,o.M)(i,"config"),h=new r.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/discord/guild",pathname:"/api/discord/guild",bundlePath:"",filename:""},userland:i})},75600:t=>{t.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var e=require("../../../webpack-api-runtime.js");e.C(t);var s=t=>e(e.s=t),i=e.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>s(72235));module.exports=i})();