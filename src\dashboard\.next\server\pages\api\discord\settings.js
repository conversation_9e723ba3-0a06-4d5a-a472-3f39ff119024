"use strict";(()=>{var e={};e.id=6223,e.ids=[6223],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},42295:(e,t,r)=>{r.r(t),r.d(t,{config:()=>x,default:()=>h,routeModule:()=>v});var s={};r.r(s),r.d(s,{default:()=>g});var n=r(93433),i=r(20264),o=r(20584),a=r(15806),u=r(94506),l=r(72115),c=r.n(l),d=r(29021),p=r.n(d),f=r(33873),y=r.n(f);let m={};try{let{dashboardConfig:e}=r(98580);m=e??{}}catch{}async function g(e,t){if(!await (0,a.getServerSession)(e,t,u.authOptions))return t.status(401).json({error:"Unauthorized"});if("GET"===e.method)try{let e={prefix:m.bot?.prefix||"!",presence:{status:m.bot?.presence?.status||"online",activityType:m.bot?.presence?.activity?.type||"PLAYING",activityName:m.bot?.presence?.activity?.name||""},welcomeChannel:"",moderationChannel:"",logChannel:"",autoRole:"",enableWelcome:!1,enableModLog:!1,enableAutoRole:!1};return t.status(200).json(e)}catch(e){return t.status(500).json({error:"Failed to fetch settings"})}if("POST"===e.method)try{let{prefix:r,presence:s}=e.body;if(r&&("string"!=typeof r||0===r.length))return t.status(400).json({error:"Invalid prefix"});let n=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>y().resolve(process.cwd(),e)).find(e=>p().existsSync(e));if(n||(n=y().resolve(__dirname,"../../../../config.yml")),!p().existsSync(n))return t.status(500).json({error:"config.yml not found on server"});try{let e=p().readFileSync(n,"utf8"),i=c().parse(e);return r&&(i.bot.prefix=r),s&&(i.bot.presence={status:s.status||"online",activity:{type:s.activityType||"PLAYING",name:s.activityName||""}}),p().writeFileSync(n,c().stringify(i)),t.status(200).json({message:"Settings updated successfully"})}catch(e){return t.status(500).json({error:"Failed to update settings"})}}catch(e){return t.status(500).json({error:"Failed to update settings"})}return t.status(405).json({error:"Method not allowed"})}let h=(0,o.M)(s,"default"),x=(0,o.M)(s,"config"),v=new n.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/discord/settings",pathname:"/api/discord/settings",bundlePath:"",filename:""},userland:s})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(42295));module.exports=s})();