"use strict";(()=>{var e={};e.id=7783,e.ids=[7783],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},32720:(e,t,r)=>{r.r(t),r.d(t,{config:()=>g,default:()=>y,routeModule:()=>w});var a={};r.r(a),r.d(a,{default:()=>f});var o=r(93433),i=r(20264),s=r(20584),n=r(15806),d=r(94506),c=r(12518),l=r(98580);let{url:u,name:p}=l.dashboardConfig.database,h=null;async function m(){return h||(h=await c.MongoClient.connect(u))}async function f(e,t){let r,a=await (0,n.getServerSession)(e,t,d.authOptions);if(!a)return t.status(401).json({error:"Unauthorized"});let{token:o,guildId:i,ticketCategoryId:s,ticketLogChannelId:c}=l.dashboardConfig.bot;if(!o||!i)return t.status(500).json({error:"Bot configuration missing (token/guildId)"});try{r=await m()}catch(e){return t.status(500).json({error:"Database connection failed"})}let u=r.db(p).collection("tickets");switch(e.method){case"GET":try{let e=a.user.isAdmin?{}:{creatorId:a.user.id},r=(await u.find(e).sort({createdAt:-1}).toArray()).map(e=>({...e,discordLink:`https://discord.com/channels/${i}/${e.channelId}`}));return t.status(200).json(r)}catch(e){return t.status(500).json({error:"Failed to fetch tickets"})}case"POST":{let{reason:r="",category:n="support"}=e.body??{},d=Math.floor(9e3*Math.random()+1e3),p="18plus"===n?"18plus":n,h=`ticket-${p}-${d}`.slice(0,100),m=[{id:i,type:0,deny:"1024"},{id:a.user.id,type:1,allow:"68608"}];l.dashboardConfig.dashboard?.adminRoleIds?.length&&l.dashboardConfig.dashboard.adminRoleIds.forEach(e=>{m.push({id:e,type:0,allow:"68608"})});try{let e=await fetch(`https://discord.com/api/v10/guilds/${i}/channels`,{method:"POST",headers:{Authorization:`Bot ${o}`,"Content-Type":"application/json"},body:JSON.stringify({name:h,type:0,parent_id:s??void 0,permission_overwrites:m,topic:`Ticket created by ${a.user.name} | Reason: ${r}`.slice(0,1024)})});if(!e.ok){let r;try{r=await e.json()}catch{r=await e.text()}return t.status(e.status).json({error:"Failed to create Discord channel",details:r})}let d=await e.json(),l={creatorId:a.user.id,creatorTag:a.user.name,channelId:d.id,status:"open",reason:r,category:n,createdAt:new Date},p=await u.insertOne(l);try{let e=p.insertedId.toString();await fetch(`https://discord.com/api/v10/channels/${d.id}/messages`,{method:"POST",headers:{Authorization:`Bot ${o}`,"Content-Type":"application/json"},body:JSON.stringify({embeds:[{title:"\uD83C\uDFAB Support Ticket",description:`Hello <@${l.creatorId}>! A staff member will be with you shortly.

**Category:** ${n}
**Reason:** ${r||"No reason provided"}`,color:2845872,timestamp:new Date().toISOString(),footer:{text:`Ticket ID: ${e}`}}],components:[{type:1,components:[{type:2,style:1,label:"Claim",custom_id:`ticket_claim_${e}`},{type:2,style:4,label:"Close",custom_id:`ticket_close_${e}`}]}]})})}catch(e){}if(c)try{await fetch(`https://discord.com/api/v10/channels/${c}/messages`,{method:"POST",headers:{Authorization:`Bot ${o}`,"Content-Type":"application/json"},body:JSON.stringify({embeds:[{title:"\uD83C\uDFAB New Ticket Created",color:3900150,fields:[{name:"Ticket ID",value:p.insertedId.toString(),inline:!0},{name:"Category",value:n,inline:!0},{name:"User",value:`<@${l.creatorId}>`,inline:!1},{name:"Reason",value:r||"None",inline:!1}],timestamp:new Date().toISOString()}]})})}catch(e){}return t.status(201).json({...l,_id:p.insertedId,discordLink:`https://discord.com/channels/${i}/${l.channelId}`})}catch(e){return t.status(500).json({error:"Failed to create ticket"})}}default:return t.setHeader("Allow",["GET","POST"]),t.status(405).json({error:`Method ${e.method} not allowed`})}}let y=(0,s.M)(a,"default"),g=(0,s.M)(a,"config"),w=new o.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/discord/tickets",pathname:"/api/discord/tickets",bundlePath:"",filename:""},userland:a})},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(32720));module.exports=a})();