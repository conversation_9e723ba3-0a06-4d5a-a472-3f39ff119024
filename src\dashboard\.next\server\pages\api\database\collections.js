"use strict";(()=>{var e={};e.id=1290,e.ids=[1290],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},27831:(e,r,t)=>{t.r(r),t.d(r,{config:()=>h,default:()=>m,routeModule:()=>g});var a={};t.r(a),t.d(a,{default:()=>f});var o=t(93433),n=t(20264),s=t(20584),i=t(15806),u=t(94506),d=t(12518);let{url:l,name:p}=t(98580).dashboardConfig.database,c=null;async function x(){if(c)return c;let e=await d.MongoClient.connect(l);return c=e,e}async function f(e,r){if(!await (0,i.getServerSession)(e,r,u.authOptions))return r.status(401).json({error:"Unauthorized"});if("GET"!==e.method)return r.status(405).json({error:"Method not allowed"});try{let e=(await x()).db(p),t=(await e.listCollections().toArray()).map(e=>e.name);return r.status(200).json({collections:t})}catch(e){return r.status(500).json({error:"Internal server error"})}}let m=(0,s.M)(a,"default"),h=(0,s.M)(a,"config"),g=new o.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/database/collections",pathname:"/api/database/collections",bundlePath:"",filename:""},userland:a})},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(27831));module.exports=a})();