// runtime can't be in strict mode because a global variable is assign and maybe created.
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/gameservers-h"],{

/***/ "(pages-dir-browser)/./hooks/useExperimentalFeatures.ts":
/*!******************************************!*\
  !*** ./hooks/useExperimentalFeatures.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useExperimentalFeatures)\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"(pages-dir-browser)/../../node_modules/.pnpm/next-auth@4.24.11_next@15.3_2aafdeb2717e2285cf78486a4e62992f/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction useExperimentalFeatures() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.useSession)();\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reason, setReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useExperimentalFeatures.useEffect\": ()=>{\n            async function checkAccess() {\n                if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                    setHasAccess(false);\n                    setReason('unauthenticated');\n                    setIsLoading(false);\n                    return;\n                }\n                try {\n                    const response = await fetch('/api/discord/user/experimental');\n                    const data = await response.json();\n                    setHasAccess(data.hasAccess);\n                    setReason(data.reason);\n                } catch (error) {\n                    console.error('Error checking experimental features access:', error);\n                    setHasAccess(false);\n                    setReason('error');\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n            checkAccess();\n        }\n    }[\"useExperimentalFeatures.useEffect\"], [\n        session\n    ]);\n    return {\n        hasAccess,\n        reason,\n        isLoading,\n        isDeveloper: reason === 'developer',\n        isTester: reason === 'tester'\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./hooks/useExperimentalFeatures.ts\n"));

/***/ }),

/***/ "(pages-dir-browser)/./hooks/useGuildInfo.ts":
/*!*******************************!*\
  !*** ./hooks/useGuildInfo.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useGuildInfo)\n/* harmony export */ });\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swr */ \"(pages-dir-browser)/../../node_modules/.pnpm/swr@2.3.4_react@19.1.0/node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(pages-dir-browser)/../../node_modules/.pnpm/next-auth@4.24.11_next@15.3_2aafdeb2717e2285cf78486a4e62992f/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nconst fetcher = async (url)=>{\n    const res = await fetch(url);\n    if (!res.ok) {\n        if (res.status === 401) {\n            // Return default data for unauthorized state\n            return {\n                name: '404 Bot',\n                botName: '404 Bot'\n            };\n        }\n        throw new Error('Failed to fetch guild info');\n    }\n    return res.json();\n};\nfunction useGuildInfo() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    // Only fetch if we're authenticated\n    const shouldFetch = status === 'authenticated';\n    const { data, error } = (0,swr__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(shouldFetch ? '/api/discord/guild' : null, fetcher, {\n        revalidateOnFocus: false,\n        revalidateOnReconnect: false\n    });\n    // Local preference state (guild vs bot)\n    const [pref, setPref] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useGuildInfo.useState\": ()=>{\n            if (false) {}\n            return localStorage.getItem('dashboardDisplayNamePref') || 'guild';\n        }\n    }[\"useGuildInfo.useState\"]);\n    // Function to update preference and broadcast change\n    const updatePreference = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGuildInfo.useCallback[updatePreference]\": (newPref)=>{\n            setPref(newPref);\n            if (true) {\n                localStorage.setItem('dashboardDisplayNamePref', newPref);\n                window.dispatchEvent(new CustomEvent('displayNamePrefChanged', {\n                    detail: newPref\n                }));\n            }\n        }\n    }[\"useGuildInfo.useCallback[updatePreference]\"], []);\n    // Listen for preference changes in this tab (custom event) or other tabs (storage event)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useGuildInfo.useEffect\": ()=>{\n            if (false) {}\n            const handleCustom = {\n                \"useGuildInfo.useEffect.handleCustom\": (e)=>{\n                    if (e === null || e === void 0 ? void 0 : e.detail) setPref(e.detail);\n                }\n            }[\"useGuildInfo.useEffect.handleCustom\"];\n            const handleStorage = {\n                \"useGuildInfo.useEffect.handleStorage\": (e)=>{\n                    if (e.key === 'dashboardDisplayNamePref') {\n                        setPref(e.newValue || 'guild');\n                    }\n                }\n            }[\"useGuildInfo.useEffect.handleStorage\"];\n            window.addEventListener('displayNamePrefChanged', handleCustom);\n            window.addEventListener('storage', handleStorage);\n            return ({\n                \"useGuildInfo.useEffect\": ()=>{\n                    window.removeEventListener('displayNamePrefChanged', handleCustom);\n                    window.removeEventListener('storage', handleStorage);\n                }\n            })[\"useGuildInfo.useEffect\"];\n        }\n    }[\"useGuildInfo.useEffect\"], []);\n    // Default display name when not authenticated\n    const defaultName = '404 Bot Dashboard';\n    // Determine displayName\n    let displayName = defaultName;\n    if (data) {\n        if (pref === 'bot' && data.botName) {\n            displayName = data.botName;\n        } else {\n            displayName = data.name || defaultName;\n        }\n    }\n    return {\n        guild: data,\n        displayName,\n        pref,\n        updatePreference,\n        isLoading: shouldFetch && !error && !data,\n        isError: !!error\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./hooks/useGuildInfo.ts\n"));

/***/ }),

/***/ "(pages-dir-browser)/./pages/gameservers.tsx":
/*!*******************************!*\
  !*** ./pages/gameservers.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: () => (/* binding */ __N_SSP),\n/* harmony export */   \"default\": () => (/* binding */ GameServers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertDialog,AlertDialogBody,AlertDialogContent,AlertDialogFooter,AlertDialogHeader,AlertDialogOverlay,Box,Button,HStack,Heading,Icon,IconButton,SimpleGrid,Spinner,Text,VStack,useDisclosure,useToast!=!@chakra-ui/react */ \"(pages-dir-browser)/__barrel_optimize__?names=AlertDialog,AlertDialogBody,AlertDialogContent,AlertDialogFooter,AlertDialogHeader,AlertDialogOverlay,Box,Button,HStack,Heading,Icon,IconButton,SimpleGrid,Spinner,Text,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaEdit,FaGamepad,FaPlus,FaSync,FaTrash!=!react-icons/fa */ \"(pages-dir-browser)/__barrel_optimize__?names=FaEdit,FaGamepad,FaPlus,FaSync,FaTrash!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Layout */ \"(pages-dir-browser)/./components/Layout.tsx\");\n/* harmony import */ var _components_GameServerCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/GameServerCard */ \"(pages-dir-browser)/./components/GameServerCard.tsx\");\n/* harmony import */ var _components_GameServerDialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/GameServerDialog */ \"(pages-dir-browser)/./components/GameServerDialog.tsx\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-auth/react */ \"(pages-dir-browser)/../../node_modules/.pnpm/next-auth@4.24.11_next@15.3_2aafdeb2717e2285cf78486a4e62992f/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar __N_SSP = true;\nfunction GameServers() {\n    var _session_user;\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.useSession)();\n    const isAdmin = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.isAdmin) === true;\n    const [servers, setServers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedServer, setSelectedServer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [serverToDelete, setServerToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const { isOpen: isAddEditOpen, onOpen: onAddEditOpen, onClose: onAddEditClose } = (0,_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useDisclosure)();\n    const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = (0,_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useDisclosure)();\n    const cancelRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const toast = (0,_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const fetchServers = async ()=>{\n        try {\n            setRefreshing(true);\n            const response = await fetch('/api/gameservers/query');\n            if (!response.ok) {\n                throw new Error('Failed to fetch server status');\n            }\n            const data = await response.json();\n            console.log('API Response:', data, 'Type:', typeof data, 'Is Array:', Array.isArray(data));\n            // Ensure data is always an array\n            const serversArray = Array.isArray(data) ? data : [];\n            setServers(serversArray);\n        } catch (error) {\n            // Set servers to empty array to prevent map() error\n            setServers([]);\n            toast({\n                title: 'Error',\n                description: error instanceof Error ? error.message : 'Failed to fetch server status',\n                status: 'error',\n                duration: 5000,\n                isClosable: true\n            });\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GameServers.useEffect\": ()=>{\n            fetchServers();\n            // Set up auto-refresh every 30 seconds\n            const interval = setInterval(fetchServers, 30000);\n            return ({\n                \"GameServers.useEffect\": ()=>clearInterval(interval)\n            })[\"GameServers.useEffect\"];\n        }\n    }[\"GameServers.useEffect\"], []);\n    const handleAddEdit = async (server)=>{\n        try {\n            // First validate the game type\n            const validateResponse = await fetch(\"/api/gameservers/games?type=\".concat(encodeURIComponent(server.type)));\n            if (!validateResponse.ok) {\n                throw new Error('Invalid game type');\n            }\n            const { type: validatedType } = await validateResponse.json();\n            server.type = validatedType;\n            // Then save the server\n            const url = '/api/gameservers/manage';\n            const method = server._id ? 'PUT' : 'POST';\n            console.log('Server operation:', method, 'Server data:', server); // Enhanced debug log\n            let requestBody;\n            if (method === 'PUT') {\n                // For PUT requests, we need the id separate from the data\n                const { _id, ...serverData } = server;\n                requestBody = {\n                    id: _id,\n                    ...serverData\n                };\n            } else {\n                // For POST requests, we don't want the _id field at all\n                const { _id, ...serverData } = server;\n                requestBody = serverData;\n            }\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to save server');\n            }\n            fetchServers();\n            toast({\n                title: server._id ? 'Server Updated' : 'Server Added',\n                description: server._id ? 'The server has been updated successfully' : 'The server has been added successfully',\n                status: 'success',\n                duration: 3000,\n                isClosable: true\n            });\n        } catch (error) {\n            console.error('Save error:', error);\n            toast({\n                title: 'Error',\n                description: error instanceof Error ? error.message : 'Failed to save server',\n                status: 'error',\n                duration: 5000,\n                isClosable: true\n            });\n            throw error;\n        }\n    };\n    const handleDelete = async ()=>{\n        if (!(serverToDelete === null || serverToDelete === void 0 ? void 0 : serverToDelete._id)) {\n            console.error('No server selected for deletion', {\n                serverToDelete\n            });\n            toast({\n                title: 'Error',\n                description: 'No server selected for deletion',\n                status: 'error',\n                duration: 5000,\n                isClosable: true\n            });\n            return;\n        }\n        try {\n            console.log('Attempting to delete server:', serverToDelete);\n            const response = await fetch('/api/gameservers/manage', {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    id: serverToDelete._id\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to delete server');\n            }\n            // Remove the server from the local state\n            setServers((prev)=>prev.filter((s)=>s._id !== serverToDelete._id));\n            toast({\n                title: 'Server Deleted',\n                description: 'The server has been successfully deleted',\n                status: 'success',\n                duration: 3000,\n                isClosable: true\n            });\n            setServerToDelete(undefined);\n            onDeleteClose();\n        } catch (error) {\n            console.error('Delete error:', error);\n            toast({\n                title: 'Error',\n                description: error instanceof Error ? error.message : 'Failed to delete server',\n                status: 'error',\n                duration: 5000,\n                isClosable: true\n            });\n        }\n    };\n    const openEditDialog = (server)=>{\n        setSelectedServer({\n            _id: server._id,\n            name: server.name,\n            type: server.type,\n            host: server.host,\n            port: server.port,\n            description: server.description,\n            hasPassword: server.hasPassword,\n            password: server.password\n        });\n        onAddEditOpen();\n    };\n    const openDeleteDialog = (server)=>{\n        console.log('Opening delete dialog for server:', server);\n        setSelectedServer(server);\n        onDeleteOpen();\n    };\n    const handleDeleteServer = async ()=>{\n        if (!selectedServer) return;\n        try {\n            // Always use the server's ID if available\n            const deleteBody = {\n                id: selectedServer._id\n            };\n            console.log('Sending delete request with body:', deleteBody);\n            const response = await fetch('/api/gameservers/manage', {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(deleteBody)\n            });\n            const responseData = await response.json();\n            if (!response.ok) {\n                throw new Error(responseData.error || 'Failed to delete server');\n            }\n            // Remove the server from the local state\n            setServers((prev)=>prev.filter((s)=>s._id !== selectedServer._id));\n            setSelectedServer(undefined);\n            onDeleteClose();\n            toast({\n                title: 'Success',\n                description: 'Server deleted successfully',\n                status: 'success',\n                duration: 5000,\n                isClosable: true\n            });\n        } catch (error) {\n            console.error('Error deleting server:', error);\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to delete server',\n                status: 'error',\n                duration: 5000,\n                isClosable: true\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n            w: \"full\",\n            p: 4,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                    maxW: \"4xl\",\n                    mx: \"auto\",\n                    mb: 8,\n                    mt: 8,\n                    bg: \"rgba(255,255,255,0.08)\",\n                    p: 8,\n                    rounded: \"2xl\",\n                    backdropFilter: \"blur(10px)\",\n                    border: \"2px solid\",\n                    borderColor: \"green.400\",\n                    boxShadow: \"0 0 15px rgba(72, 187, 120, 0.4)\",\n                    textAlign: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                            size: \"2xl\",\n                            bgGradient: \"linear(to-r, green.300, teal.400)\",\n                            bgClip: \"text\",\n                            mb: 4,\n                            children: \"Game Servers\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            color: \"gray.300\",\n                            fontSize: \"lg\",\n                            mb: 6,\n                            children: \"Monitor and manage your game servers in real-time\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                            spacing: 4,\n                            justify: \"center\",\n                            children: [\n                                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPlus, {}, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    colorScheme: \"green\",\n                                    onClick: ()=>{\n                                        setSelectedServer(undefined);\n                                        onAddEditOpen();\n                                    },\n                                    size: \"md\",\n                                    variant: \"solid\",\n                                    _hover: {\n                                        transform: 'translateY(-2px)',\n                                        shadow: 'lg'\n                                    },\n                                    children: \"Add Server\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaSync, {}, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    onClick: fetchServers,\n                                    isLoading: refreshing,\n                                    loadingText: \"Refreshing\",\n                                    size: \"md\",\n                                    variant: \"outline\",\n                                    colorScheme: \"green\",\n                                    _hover: {\n                                        transform: 'translateY(-2px)',\n                                        shadow: 'lg'\n                                    },\n                                    children: \"Refresh Status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                    maxW: \"7xl\",\n                    mx: \"auto\",\n                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                        py: 8,\n                        bg: \"rgba(255,255,255,0.08)\",\n                        rounded: \"2xl\",\n                        backdropFilter: \"blur(10px)\",\n                        border: \"1px solid\",\n                        borderColor: \"whiteAlpha.200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Spinner, {\n                                size: \"xl\",\n                                color: \"green.400\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                color: \"gray.400\",\n                                children: \"Loading servers...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 13\n                    }, this) : !servers || servers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                        spacing: 4,\n                        p: 8,\n                        bg: \"rgba(255,255,255,0.08)\",\n                        rounded: \"2xl\",\n                        backdropFilter: \"blur(10px)\",\n                        border: \"1px solid\",\n                        borderColor: \"whiteAlpha.200\",\n                        textAlign: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Icon, {\n                                as: _barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaGamepad,\n                                boxSize: 12,\n                                color: \"green.400\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                color: \"gray.300\",\n                                fontSize: \"lg\",\n                                children: \"No game servers found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                fontSize: \"md\",\n                                color: \"gray.500\",\n                                children: \"Add your first game server to start monitoring\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 15\n                            }, this),\n                            isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPlus, {}, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 27\n                                }, void 0),\n                                colorScheme: \"green\",\n                                onClick: ()=>{\n                                    setSelectedServer(undefined);\n                                    onAddEditOpen();\n                                },\n                                size: \"md\",\n                                variant: \"outline\",\n                                _hover: {\n                                    transform: 'translateY(-2px)',\n                                    shadow: 'lg'\n                                },\n                                children: \"Add Your First Server\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.SimpleGrid, {\n                        columns: {\n                            base: 1,\n                            md: 2,\n                            lg: 3\n                        },\n                        spacing: 6,\n                        children: (()=>{\n                            console.log('Rendering servers:', servers, 'Type:', typeof servers, 'Is Array:', Array.isArray(servers));\n                            return (servers || []).map((server, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                    position: \"relative\",\n                                    transition: \"all 0.2s\",\n                                    _hover: isAdmin ? {\n                                        transform: 'translateY(-4px)',\n                                        '& > .server-actions': {\n                                            opacity: 1,\n                                            transform: 'translateY(0)'\n                                        }\n                                    } : undefined,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GameServerCard__WEBPACK_IMPORTED_MODULE_3__.GameServerCard, {\n                                            server: server\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, this),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                            className: \"server-actions\",\n                                            position: \"absolute\",\n                                            top: 2,\n                                            right: 2,\n                                            spacing: 1,\n                                            bg: \"blackAlpha.800\",\n                                            p: 1,\n                                            borderRadius: \"md\",\n                                            opacity: 0,\n                                            transform: \"translateY(-4px)\",\n                                            transition: \"all 0.2s\",\n                                            zIndex: 2,\n                                            backdropFilter: \"blur(8px)\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                                    \"aria-label\": \"Edit server\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaEdit, {}, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    size: \"sm\",\n                                                    variant: \"ghost\",\n                                                    colorScheme: \"green\",\n                                                    onClick: ()=>openEditDialog(server),\n                                                    _hover: {\n                                                        bg: 'green.700'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                                    \"aria-label\": \"Delete server\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaGamepad_FaPlus_FaSync_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaTrash, {}, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    size: \"sm\",\n                                                    variant: \"ghost\",\n                                                    colorScheme: \"red\",\n                                                    onClick: ()=>openDeleteDialog(server),\n                                                    _hover: {\n                                                        bg: 'red.700'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, \"\".concat(server._id || \"\".concat(server.host, \":\").concat(server.port, \"-\").concat(index)), true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 17\n                                }, this));\n                        })()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, this),\n                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GameServerDialog__WEBPACK_IMPORTED_MODULE_4__.GameServerDialog, {\n                    isOpen: isAddEditOpen,\n                    onClose: onAddEditClose,\n                    server: selectedServer,\n                    onSave: handleAddEdit\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.AlertDialog, {\n                    isOpen: isDeleteOpen,\n                    leastDestructiveRef: cancelRef,\n                    onClose: onDeleteClose,\n                    isCentered: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.AlertDialogOverlay, {\n                        backdropFilter: \"blur(10px)\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.AlertDialogContent, {\n                            bg: \"gray.800\",\n                            border: \"1px\",\n                            borderColor: \"whiteAlpha.200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.AlertDialogHeader, {\n                                    fontSize: \"lg\",\n                                    fontWeight: \"bold\",\n                                    color: \"white\",\n                                    children: \"Delete Server\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.AlertDialogBody, {\n                                    color: \"gray.300\",\n                                    children: [\n                                        \"Are you sure you want to delete \",\n                                        (selectedServer === null || selectedServer === void 0 ? void 0 : selectedServer.name) || \"\".concat(selectedServer === null || selectedServer === void 0 ? void 0 : selectedServer.host, \":\").concat(selectedServer === null || selectedServer === void 0 ? void 0 : selectedServer.port),\n                                        \"? This action cannot be undone.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.AlertDialogFooter, {\n                                    gap: 3,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            ref: cancelRef,\n                                            onClick: onDeleteClose,\n                                            variant: \"ghost\",\n                                            color: \"gray.300\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            colorScheme: \"red\",\n                                            onClick: handleDeleteServer,\n                                            _hover: {\n                                                bg: 'red.600'\n                                            },\n                                            _active: {\n                                                bg: 'red.700'\n                                            },\n                                            children: \"Delete\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n            lineNumber: 295,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\gameservers.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\n_s(GameServers, \"KvDOlrb3uI5B/kuqgcB43eq3oIU=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_5__.useSession,\n        _barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useDisclosure,\n        _barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useDisclosure,\n        _barrel_optimize_names_AlertDialog_AlertDialogBody_AlertDialogContent_AlertDialogFooter_AlertDialogHeader_AlertDialogOverlay_Box_Button_HStack_Heading_Icon_IconButton_SimpleGrid_Spinner_Text_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = GameServers;\nvar _c;\n$RefreshReg$(_c, \"GameServers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/gameservers.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["chakra-node_modules_pnpm_chakra-ui_a","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-4215ff4a","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27","chakra-node_modules_pnpm_chakra-ui_s","chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_fa_index_mjs-537c58d-60f44d42","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_f","lib-node_modules_pnpm_a","lib-node_modules_pnpm_d","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es2015_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-516eb045","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_h","lib-node_modules_pnpm_motion-d","lib-node_modules_pnpm_next-auth_4_24_11_next_15_3_2aafdeb2717e2285cf78486a4e62992f_node_modules_next-e83df605","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-65360dba","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-041c6906","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-5bb80607","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-b08bee20","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-3132e3e8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-1dd8b864","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-c1f28c14","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-f15ba28f","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-7974549f","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-15c79965","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-b81043bf","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-34be4b23","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-72590865","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-c2581ded","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-619c5d36","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-04138191","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-8b1a74d9","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-f459d541","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-77f57188","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-7eb5f54d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-387463bd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0ac67067","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_po","lib-node_modules_pnpm_r","lib-node_modules_pnpm_s","pages/gameservers-_","pages/gameservers-components_L","framework-node_modules_pnpm_react-dom_19_1_0_react_19_1_0_node_modules_react-dom_cjs_react-dom-clien-cf490416","framework-node_modules_pnpm_react-","pages/_app","main"], () => (__webpack_exec__("(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CUsers%5CPete%20Gaming%20PC%5CDesktop%5C404%20Bot%5Csrc%5Cdashboard%5Cpages%5Cgameservers.tsx&page=%2Fgameservers!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);