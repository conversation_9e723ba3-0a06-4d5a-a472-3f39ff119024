"use strict";(()=>{var e={};e.id=2637,e.ids=[2637],e.modules={224:e=>{e.exports=import("@discordjs/rest")},15806:e=>{e.exports=require("next-auth/next")},19311:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>p,default:()=>d,routeModule:()=>c});var a=t(93433),i=t(20264),o=t(20584),n=t(79272),u=e([n]);n=(u.then?(await u)():u)[0];let d=(0,o.M)(n,"default"),p=(0,o.M)(n,"config"),c=new a.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/discord/users/kick",pathname:"/api/discord/users/kick",bundlePath:"",filename:""},userland:n});s()}catch(e){s(e)}})},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},33915:e=>{e.exports=import("discord-api-types/v10")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},79272:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>p});var a=t(15806),i=t(94506),o=t(224),n=t(33915),u=t(20381),d=e([o,n]);async function p(e,r){if("POST"!==e.method)return r.status(405).json({error:"Method not allowed"});try{if(!await (0,a.getServerSession)(e,r,i.authOptions)){let t=e.headers.authorization;if(!t||!t.startsWith("Bearer "))return r.status(401).json({error:"Unauthorized"})}let{userId:t,reason:s}=e.body;if(!t)return r.status(400).json({error:"Missing userId"});let d=new o.REST({version:"10"}).setToken(u._.DISCORD_BOT_TOKEN),p=u._.DISCORD_GUILD_ID;await d.delete(n.Routes.guildMember(p,t),{body:{reason:s||"No reason provided"}}),r.status(200).json({success:!0})}catch(e){r.status(500).json({error:"Failed to kick user"})}}[o,n]=d.then?(await d)():d,s()}catch(e){s(e)}})}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(19311));module.exports=s})();