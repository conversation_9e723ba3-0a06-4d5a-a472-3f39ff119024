// @ts-nocheck
import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  VStack,
  HStack,
  Icon,
  useToast,
  Card,
  CardHeader,
  CardBody,
  Button,
  Input,
  Select,
  Switch,
  FormControl,
  FormLabel,
  Badge,
  Divider,
  Skeleton,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  Tooltip,
  Checkbox,
  Flex,
  RadioGroup,
  Radio,
  Spinner,
} from '@chakra-ui/react';
import Layout from '../../components/Layout';
import useGuildInfo from '../../hooks/useGuildInfo';
import { useState, useEffect, useCallback, useRef, Suspense } from 'react';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../api/auth/[...nextauth]';
import { FiSettings, FiUsers, FiHash, FiEdit2, FiSave, FiTrash2, FiPlus, FiMessageSquare, FiVolume2, FiFolderPlus, FiRadio, FiLock, FiMessageCircle, FiTrash, FiServer, FiZap } from 'react-icons/fi';
import dynamic from 'next/dynamic';

// Dynamic imports for heavy components
const CreateChannelDialog = dynamic(() => import('../../components/CreateChannelDialog'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

const EditChannelDialog = dynamic(() => import('../../components/EditChannelDialog'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

const EditRoleDialog = dynamic(() => import('../../components/EditRoleDialog'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

const CreateRoleDialog = dynamic(() => import('../../components/CreateRoleDialog'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

// Rate limiting constants
const RATE_LIMIT_MS = 2000; // 2 seconds between operations
const BULK_RATE_LIMIT_MS = 5000; // 5 seconds between bulk operations

// Custom hook for rate limiting
function useRateLimit(delay = RATE_LIMIT_MS) {
  const [isRateLimited, setIsRateLimited] = useState(false);
  const timeoutRef = useRef(null);

  const resetRateLimit = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsRateLimited(true);
    timeoutRef.current = setTimeout(() => {
      setIsRateLimited(false);
    }, delay);
  }, [delay]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [isRateLimited, resetRateLimit];
}

interface Role {
  id: string;
  name: string;
  color: number;
  position: number;
  permissions: string;
}

interface Channel {
  id: string;
  name: string;
  type: number;
  parent_id?: string;
  position: number;
  topic?: string;
  nsfw?: boolean;
  rate_limit_per_user?: number;
  bitrate?: number;
  user_limit?: number;
}

interface GuildSettings {
  prefix: string;
  botName: string;
  guildName: string;
  guildIcon: string | null;
  activities: { type: 'PLAYING' | 'STREAMING' | 'LISTENING' | 'WATCHING' | 'COMPETING'; name: string; }[];
  activityRotationInterval: number; // in seconds
}

const CHANNEL_TYPE_CONFIG = {
  0:  { icon: FiMessageSquare, color: 'blue',   label: 'Text' },
  2:  { icon: FiVolume2,      color: 'green',  label: 'Voice' },
  4:  { icon: FiFolderPlus,   color: 'purple', label: 'Category' },
  5:  { icon: FiRadio,        color: 'orange', label: 'Announcement' },
  11: { icon: FiMessageCircle,color: 'cyan',   label: 'Public Thread' },
  12: { icon: FiLock,         color: 'pink',   label: 'Private Thread' },
  13: { icon: FiHash,         color: 'teal',   label: 'Stage Voice'},
  15: { icon: FiHash,         color: 'gray',   label: 'Forum'}
};

const PERMISSION_BADGES = {
  ADMINISTRATOR: { color: 'red', label: 'Admin' },
  MANAGE_GUILD: { color: 'orange', label: 'Manage Server' },
  MANAGE_ROLES: { color: 'yellow', label: 'Manage Roles' },
  MANAGE_CHANNELS: { color: 'green', label: 'Manage Channels' },
  KICK_MEMBERS: { color: 'purple', label: 'Kick' },
  BAN_MEMBERS: { color: 'pink', label: 'Ban' },
  MANAGE_MESSAGES: { color: 'blue', label: 'Manage Messages' },
  MENTION_EVERYONE: { color: 'cyan', label: 'Mention @everyone' },
};

// Add this helper map and function after PERMISSION_BADGES constant
const PERMISSION_FLAG_BITS: Record<keyof typeof PERMISSION_BADGES, bigint> = {
  ADMINISTRATOR: 1n << 3n,
  MANAGE_GUILD: 1n << 5n,
  MANAGE_ROLES: 1n << 28n,
  MANAGE_CHANNELS: 1n << 4n,
  KICK_MEMBERS: 1n << 1n,
  BAN_MEMBERS: 1n << 2n,
  MANAGE_MESSAGES: 1n << 13n,
  MENTION_EVERYONE: 1n << 17n,
};

function decodePermissions(bitfield: string | number): (keyof typeof PERMISSION_BADGES)[] {
  if (!bitfield) return [];
  if (Array.isArray(bitfield)) return bitfield as any;
  try {
    const permissions: (keyof typeof PERMISSION_BADGES)[] = [];
    const bits = BigInt(bitfield);
    
    for (const [permission, bit] of Object.entries(PERMISSION_FLAG_BITS)) {
      if ((bits & bit) === bit) {
        permissions.push(permission as keyof typeof PERMISSION_BADGES);
      }
    }
    
    return permissions;
  } catch (error) {
    console.error('Error decoding permissions:', error);
    return [];
  }
}

export default function ServerManagement() {
  const toast = useToast();
  const { displayName } = useGuildInfo();
  const [isRateLimited, resetRateLimit] = useRateLimit();
  const [isBulkRateLimited, resetBulkRateLimit] = useRateLimit(BULK_RATE_LIMIT_MS);
  
  // State for guild settings
  const [guildData, setGuildData] = useState<GuildSettings>({
    prefix: '!',
    botName: 'Bot',
    guildName: '',
    guildIcon: null,
    activities: [{ type: 'PLAYING', name: 'with Discord.js' }],
    activityRotationInterval: 60
  });
  
  // State for roles and channels
  const [roles, setRoles] = useState<Role[]>([]);
  const [channels, setChannels] = useState<Channel[]>([]);
  const [loading, setLoading] = useState(true);
  const [channelsLoading, setChannelsLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  // Modal states
  const { isOpen: isCreateChannelOpen, onOpen: onCreateChannelOpen, onClose: onCreateChannelClose } = useDisclosure();
  const { isOpen: isEditChannelOpen, onOpen: onEditChannelOpen, onClose: onEditChannelClose } = useDisclosure();
  const { isOpen: isCreateRoleOpen, onOpen: onCreateRoleOpen, onClose: onCreateRoleClose } = useDisclosure();
  const { isOpen: isEditRoleOpen, onOpen: onEditRoleOpen, onClose: onEditRoleClose } = useDisclosure();
  
  // Selected items for editing
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<Channel | null>(null);
  
  // File upload state
  const [iconFile, setIconFile] = useState<File | null>(null);
  const [iconPreview, setIconPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleIconFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setIconFile(file);
      const reader = new FileReader();
      reader.onload = (e) => setIconPreview(e.target?.result as string);
      reader.readAsDataURL(file);
    }
  };

  const uploadIcon = async () => {
    if (!iconFile) return;
    
    const formData = new FormData();
    formData.append('icon', iconFile);
    
    try {
      const response = await fetch('/api/discord/settings', {
        method: 'POST',
        body: formData
      });
      
      if (response.ok) {
        const data = await response.json();
        setGuildData(prev => ({ ...prev, guildIcon: data.iconUrl }));
        setIconFile(null);
        setIconPreview(null);
        toast({
          title: 'Success',
          description: 'Guild icon updated successfully',
          status: 'success',
          duration: 3000
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to upload icon',
        status: 'error',
        duration: 3000
      });
    }
  };

  const fetchGuildData = async () => {
    try {
      const [guildResponse, rolesResponse] = await Promise.all([
        fetch('/api/discord/guild'),
        fetch('/api/discord/roles')
      ]);
      
      if (guildResponse.ok) {
        const guild = await guildResponse.json();
        setGuildData(prev => ({
          ...prev,
          guildName: guild.name,
          guildIcon: guild.icon
        }));
      }
      
      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json();
        const arr = Array.isArray(rolesData) ? rolesData : rolesData.roles || [];
        setRoles(arr.sort((a: Role, b: Role) => b.position - a.position));
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch guild data',
        status: 'error',
        duration: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchChannels = async () => {
    try {
      const response = await fetch('/api/discord/channels');
      if (!response.ok) {
        throw new Error('Failed to fetch channels');
      }
      const data = await response.json();
      const sortedChannels = (data || []).sort((a: Channel, b: Channel) => {
        if (a.type === 4 && b.type !== 4) return -1;
        if (a.type !== 4 && b.type === 4) return 1;
        return a.position - b.position;
      });
      setChannels(sortedChannels);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch channels',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setChannelsLoading(false);
    }
  };

  useEffect(() => {
    fetchGuildData();
    fetchChannels();
  }, []);

  const handleSettingChange = (setting: keyof GuildSettings, value: any) => {
    setGuildData(prev => ({ ...prev, [setting]: value }));
  };

  const saveSettings = async () => {
    if (saving || isRateLimited) return;
    
    setSaving(true);
    resetRateLimit();
    
    try {
      const response = await fetch('/api/discord/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(guildData)
      });
      
      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Settings saved successfully',
          status: 'success',
          duration: 3000
        });
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save settings',
        status: 'error',
        duration: 3000
      });
    } finally {
      setSaving(false);
    }
  };

  const handleRoleEdit = (role: Role) => {
    setSelectedRole(role);
    onEditRoleOpen();
  };

  const handleChannelEdit = (channel: Channel) => {
    setSelectedChannel(channel);
    onEditChannelOpen();
  };

  const handleChannelCreate = () => {
    onCreateChannelOpen();
  };

  const handleRoleCreate = () => {
    onCreateRoleOpen();
  };

  const handleChannelDelete = async (channelId: string) => {
    if (isRateLimited) return;
    
    try {
      resetRateLimit();
      const response = await fetch(`/api/discord/channels/${channelId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        await fetchChannels();
        toast({
          title: 'Success',
          description: 'Channel deleted successfully',
          status: 'success',
          duration: 3000
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete channel',
        status: 'error',
        duration: 3000
      });
    }
  };

  const getParentName = (parentId: string | undefined) => {
    if (!parentId || !channels) return '-';
    const parent = channels.find(c => c.id === parentId);
    return parent ? parent.name : '-';
  };

  if (loading) {
    return (
      <Layout>
        <Container maxW="container.xl" py={8}>
          <VStack spacing={6}>
            <Skeleton height="60px" />
            <Skeleton height="400px" />
          </VStack>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxW="container.xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Box
            bg="rgba(255,255,255,0.08)"
            p={8}
            rounded="2xl"
            backdropFilter="blur(10px)"
            border="2px solid"
            borderColor="blue.400"
            boxShadow="0 0 15px rgba(66, 153, 225, 0.4)"
          >
            <HStack justify="space-between" align="center">
              <Box>
                <Heading
                  size="xl"
                  bgGradient="linear(to-r, blue.300, purple.400)"
                  bgClip="text"
                >
                  <Icon as={FiServer} mr={3} />
                  Server Management
                </Heading>
                <Text color="gray.300" mt={2}>
                  Comprehensive management for {displayName || guildData.guildName}
                </Text>
              </Box>
              <Button
                leftIcon={<FiSave />}
                colorScheme="blue"
                onClick={saveSettings}
                isLoading={saving}
                isDisabled={isRateLimited}
                size="lg"
              >
                Save Settings
              </Button>
            </HStack>
          </Box>

          {/* Main Content Tabs */}
          <Tabs colorScheme="blue" isLazy>
            <TabList>
              <Tab>
                <Icon as={FiSettings} mr={2} />
                Guild Settings
              </Tab>
              <Tab>
                <Icon as={FiUsers} mr={2} />
                Roles ({roles.length})
              </Tab>
              <Tab>
                <Icon as={FiHash} mr={2} />
                Channels ({channels.length})
              </Tab>
              <Tab>
                <Icon as={FiSettings} mr={2} />
                Theme Builder
              </Tab>
              <Tab>
                <Icon as={FiZap} mr={2} />
                Activity Builder
              </Tab>
            </TabList>

            <TabPanels>
              {/* Guild Settings Tab */}
              <TabPanel>
                <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                  {/* Basic Settings */}
                  <Card>
                    <CardHeader>
                      <Heading size="md">Basic Settings</Heading>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={4}>
                        <FormControl>
                          <FormLabel>Bot Name</FormLabel>
                          <Input
                            value={guildData.botName}
                            onChange={(e) => handleSettingChange('botName', e.target.value)}
                            placeholder="Enter bot name"
                          />
                        </FormControl>
                        <FormControl>
                          <FormLabel>Command Prefix</FormLabel>
                          <Input
                            value={guildData.prefix}
                            onChange={(e) => handleSettingChange('prefix', e.target.value)}
                            placeholder="Enter command prefix"
                          />
                        </FormControl>
                      </VStack>
                    </CardBody>
                  </Card>

                  {/* Activity Settings */}
                  <Card>
                    <CardHeader>
                      <Heading size="md">Bot Activity</Heading>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={4}>
                        <FormControl>
                          <FormLabel>Activity Type</FormLabel>
                          <Select
                            value={guildData.activities[0]?.type || 'PLAYING'}
                            onChange={(e) => {
                              const newActivities = [...guildData.activities];
                              newActivities[0] = { ...newActivities[0], type: e.target.value as any };
                              handleSettingChange('activities', newActivities);
                            }}
                          >
                            <option value="PLAYING">Playing</option>
                            <option value="STREAMING">Streaming</option>
                            <option value="LISTENING">Listening to</option>
                            <option value="WATCHING">Watching</option>
                            <option value="COMPETING">Competing in</option>
                          </Select>
                        </FormControl>
                        <FormControl>
                          <FormLabel>Activity Name</FormLabel>
                          <Input
                            value={guildData.activities[0]?.name || ''}
                            onChange={(e) => {
                              const newActivities = [...guildData.activities];
                              newActivities[0] = { ...newActivities[0], name: e.target.value };
                              handleSettingChange('activities', newActivities);
                            }}
                            placeholder="Enter activity name"
                          />
                        </FormControl>
                      </VStack>
                    </CardBody>
                  </Card>
                </SimpleGrid>
              </TabPanel>

              {/* Roles Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="bold">
                      Server Roles
                    </Text>
                    <Button
                      leftIcon={<FiPlus />}
                      colorScheme="green"
                      onClick={handleRoleCreate}
                      isDisabled={isRateLimited}
                    >
                      Create Role
                    </Button>
                  </HStack>

                  <Box bg="gray.800" rounded="xl" p={6} border="1px" borderColor="gray.700">
                    <Table variant="simple">
                      <Thead>
                        <Tr>
                          <Th>Role</Th>
                          <Th>Members</Th>
                          <Th>Permissions</Th>
                          <Th>Actions</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {(roles || []).map((role) => (
                          <Tr key={role.id}>
                            <Td>
                              <HStack>
                                <Box
                                  w={4}
                                  h={4}
                                  rounded="full"
                                  bg={role.color ? `#${role.color.toString(16).padStart(6, '0')}` : 'gray.500'}
                                />
                                <Text color="white">{role.name}</Text>
                              </HStack>
                            </Td>
                            <Td>
                              <Badge colorScheme="blue">0</Badge>
                            </Td>
                            <Td>
                              <HStack wrap="wrap" spacing={1}>
                                {(decodePermissions(role.permissions) || []).slice(0, 3).map((perm) => (
                                  <Badge
                                    key={perm}
                                    colorScheme={PERMISSION_BADGES[perm]?.color || 'gray'}
                                    size="sm"
                                  >
                                    {PERMISSION_BADGES[perm]?.label || perm}
                                  </Badge>
                                ))}
                                {decodePermissions(role.permissions).length > 3 && (
                                  <Badge colorScheme="gray" size="sm">
                                    +{decodePermissions(role.permissions).length - 3}
                                  </Badge>
                                )}
                              </HStack>
                            </Td>
                            <Td>
                              <HStack spacing={2}>
                                <Tooltip label="Edit Role">
                                  <IconButton
                                    aria-label="Edit role"
                                    icon={<FiEdit2 />}
                                    size="sm"
                                    variant="ghost"
                                    colorScheme="blue"
                                    onClick={() => handleRoleEdit(role)}
                                    isDisabled={isRateLimited}
                                  />
                                </Tooltip>
                                <Tooltip label="Delete Role">
                                  <IconButton
                                    aria-label="Delete role"
                                    icon={<FiTrash2 />}
                                    size="sm"
                                    variant="ghost"
                                    colorScheme="red"
                                    isDisabled={isRateLimited}
                                  />
                                </Tooltip>
                              </HStack>
                            </Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </Box>
                </VStack>
              </TabPanel>

              {/* Channels Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="bold">
                      Server Channels
                    </Text>
                    <Button
                      leftIcon={<FiPlus />}
                      colorScheme="green"
                      onClick={handleChannelCreate}
                      isDisabled={isRateLimited}
                    >
                      Create Channel
                    </Button>
                  </HStack>

                  <Box bg="gray.800" rounded="xl" p={6} border="1px" borderColor="gray.700">
                    {channelsLoading ? (
                      <VStack spacing={4}>
                        <Skeleton height="40px" />
                        <Skeleton height="40px" />
                        <Skeleton height="40px" />
                      </VStack>
                    ) : (
                      <Table variant="simple">
                        <Thead>
                          <Tr>
                            <Th>Name</Th>
                            <Th>Type</Th>
                            <Th>Category</Th>
                            <Th>Position</Th>
                            <Th>Actions</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {(channels || []).map((channel) => {
                            const typeConfig = CHANNEL_TYPE_CONFIG[channel.type] || { icon: FiMessageSquare, color: 'gray', label: 'Other' };
                            return (
                              <Tr key={channel.id}>
                                <Td>
                                  <HStack>
                                    <Icon
                                      as={typeConfig.icon}
                                      color={`${typeConfig.color}.400`}
                                    />
                                    <Text color="white">{channel.name}</Text>
                                  </HStack>
                                </Td>
                                <Td>
                                  <Badge colorScheme={typeConfig.color}>{typeConfig.label}</Badge>
                                </Td>
                                <Td>
                                  <Text color="gray.300">{getParentName(channel.parent_id)}</Text>
                                </Td>
                                <Td>
                                  <Text color="gray.300">{channel.position}</Text>
                                </Td>
                                <Td>
                                  <HStack spacing={2}>
                                    <Tooltip label="Edit Channel">
                                      <IconButton
                                        aria-label="Edit channel"
                                        icon={<FiEdit2 />}
                                        size="sm"
                                        variant="ghost"
                                        colorScheme="blue"
                                        onClick={() => handleChannelEdit(channel)}
                                        isDisabled={isRateLimited}
                                      />
                                    </Tooltip>
                                    <Tooltip label="Delete Channel">
                                      <IconButton
                                        aria-label="Delete channel"
                                        icon={<FiTrash2 />}
                                        size="sm"
                                        variant="ghost"
                                        colorScheme="red"
                                        onClick={() => handleChannelDelete(channel.id)}
                                        isDisabled={isRateLimited}
                                      />
                                    </Tooltip>
                                  </HStack>
                                </Td>
                              </Tr>
                            );
                          })}
                        </Tbody>
                      </Table>
                    )}
                  </Box>
                </VStack>
              </TabPanel>

              {/* Theme Builder Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <Box>
                    <Heading size="md" mb={4}>Theme Builder</Heading>
                    <Text color="gray.600" _dark={{ color: 'gray.300' }} mb={6}>
                      Customize your server's visual theme and appearance
                    </Text>
                  </Box>

                  <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                    <Card>
                      <CardHeader>
                        <Heading size="sm">Quick Actions</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={4} align="stretch">
                          <Button
                            leftIcon={<FiSettings />}
                            colorScheme="purple"
                            onClick={() => window.open('/admin/applications-builder', '_blank')}
                          >
                            Open Applications Builder
                          </Button>
                          <Button
                            leftIcon={<FiMessageSquare />}
                            colorScheme="blue"
                            onClick={() => window.open('/admin/embed-builder', '_blank')}
                            isDisabled
                          >
                            Message Builder (Coming Soon)
                          </Button>
                        </VStack>
                      </CardBody>
                    </Card>

                    <Card>
                      <CardHeader>
                        <Heading size="sm">Theme Settings</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text color="gray.500" fontSize="sm">
                          Advanced theme customization features will be available here soon.
                          For now, you can use the theme selector in the top navigation.
                        </Text>
                      </CardBody>
                    </Card>
                  </SimpleGrid>
                </VStack>
              </TabPanel>

              {/* Activity Builder Tab */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  <Box>
                    <Heading size="md" mb={4}>Activity Builder</Heading>
                    <Text color="gray.600" _dark={{ color: 'gray.300' }} mb={6}>
                      Create and manage server activities, events, and automated features
                    </Text>
                  </Box>

                  <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                    <Card>
                      <CardHeader>
                        <Heading size="sm">Builders & Tools</Heading>
                      </CardHeader>
                      <CardBody>
                        <VStack spacing={4} align="stretch">
                          <Button
                            leftIcon={<FiZap />}
                            colorScheme="green"
                            onClick={() => window.open('/admin/experimental/addon-builder', '_blank')}
                          >
                            Addon Builder
                          </Button>
                          <Button
                            leftIcon={<FiMessageSquare />}
                            colorScheme="blue"
                            onClick={() => window.open('/admin/applications-builder', '_blank')}
                          >
                            Applications Builder
                          </Button>
                          <Button
                            leftIcon={<FiSettings />}
                            colorScheme="purple"
                            onClick={() => window.open('/admin/addons', '_blank')}
                          >
                            Manage Addons
                          </Button>
                        </VStack>
                      </CardBody>
                    </Card>

                    <Card>
                      <CardHeader>
                        <Heading size="sm">Activity Templates</Heading>
                      </CardHeader>
                      <CardBody>
                        <Text color="gray.500" fontSize="sm" mb={4}>
                          Pre-built activity templates to get you started quickly:
                        </Text>
                        <VStack spacing={2} align="stretch">
                          <Text fontSize="sm">• Event Management System</Text>
                          <Text fontSize="sm">• Welcome & Onboarding Flow</Text>
                          <Text fontSize="sm">• Moderation Automation</Text>
                          <Text fontSize="sm">• Custom Commands</Text>
                        </VStack>
                      </CardBody>
                    </Card>
                  </SimpleGrid>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </VStack>

        {/* Modals */}
        <Suspense fallback={<Spinner />}>
          <CreateChannelDialog
            isOpen={isCreateChannelOpen}
            onClose={onCreateChannelClose}
            onSuccess={fetchChannels}
          />
        </Suspense>

        <Suspense fallback={<Spinner />}>
          <EditChannelDialog
            isOpen={isEditChannelOpen}
            onClose={onEditChannelClose}
            channel={selectedChannel}
            categories={channels.filter(c => c.type === 4)}
            onSuccess={fetchChannels}
          />
        </Suspense>

        <Suspense fallback={<Spinner />}>
          <CreateRoleDialog
            isOpen={isCreateRoleOpen}
            onClose={onCreateRoleClose}
            onSuccess={fetchGuildData}
          />
        </Suspense>

        <Suspense fallback={<Spinner />}>
          <EditRoleDialog
            isOpen={isEditRoleOpen}
            onClose={onEditRoleClose}
            role={selectedRole}
            onSuccess={fetchGuildData}
          />
        </Suspense>
      </Container>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);

  if (!session) {
    return {
      redirect: {
        destination: '/api/auth/signin?callbackUrl=%2Fadmin%2Fguilds',
        permanent: false,
      },
    };
  }

  return { props: { session } };
};
