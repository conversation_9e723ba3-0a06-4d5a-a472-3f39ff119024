"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e";
exports.ids = ["lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Feature: () => (/* binding */ Feature)\n/* harmony export */ });\nclass Feature {\n    constructor(node) {\n        this.isMounted = false;\n        this.node = node;\n    }\n    update() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL0ZlYXR1cmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFbUIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcbW90aW9uXFxmZWF0dXJlc1xcRmVhdHVyZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY2xhc3MgRmVhdHVyZSB7XG4gICAgY29uc3RydWN0b3Iobm9kZSkge1xuICAgICAgICB0aGlzLmlzTW91bnRlZCA9IGZhbHNlO1xuICAgICAgICB0aGlzLm5vZGUgPSBub2RlO1xuICAgIH1cbiAgICB1cGRhdGUoKSB7IH1cbn1cblxuZXhwb3J0IHsgRmVhdHVyZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExitAnimationFeature: () => (/* binding */ ExitAnimationFeature)\n/* harmony export */ });\n/* harmony import */ var _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Feature.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\nlet id = 0;\nclass ExitAnimationFeature extends _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor() {\n        super(...arguments);\n        this.id = id++;\n    }\n    update() {\n        if (!this.node.presenceContext)\n            return;\n        const { isPresent, onExitComplete } = this.node.presenceContext;\n        const { isPresent: prevIsPresent } = this.node.prevPresenceContext || {};\n        if (!this.node.animationState || isPresent === prevIsPresent) {\n            return;\n        }\n        const exitAnimation = this.node.animationState.setActive(\"exit\", !isPresent);\n        if (onExitComplete && !isPresent) {\n            exitAnimation.then(() => {\n                onExitComplete(this.id);\n            });\n        }\n    }\n    mount() {\n        const { register, onExitComplete } = this.node.presenceContext || {};\n        if (onExitComplete) {\n            onExitComplete(this.id);\n        }\n        if (register) {\n            this.unmount = register(this.id);\n        }\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimationFeature: () => (/* binding */ AnimationFeature)\n/* harmony export */ });\n/* harmony import */ var _animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../animation/utils/is-animation-controls.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs\");\n/* harmony import */ var _render_utils_animation_state_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../render/utils/animation-state.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/animation-state.mjs\");\n/* harmony import */ var _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Feature.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\n\n\nclass AnimationFeature extends _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    /**\n     * We dynamically generate the AnimationState manager as it contains a reference\n     * to the underlying animation library. We only want to load that if we load this,\n     * so people can optionally code split it out using the `m` component.\n     */\n    constructor(node) {\n        super(node);\n        node.animationState || (node.animationState = (0,_render_utils_animation_state_mjs__WEBPACK_IMPORTED_MODULE_1__.createAnimationState)(node));\n    }\n    updateAnimationControlsSubscription() {\n        const { animate } = this.node.getProps();\n        if ((0,_animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_2__.isAnimationControls)(animate)) {\n            this.unmountControls = animate.subscribe(this.node);\n        }\n    }\n    /**\n     * Subscribe any provided AnimationControls to the component's VisualElement\n     */\n    mount() {\n        this.updateAnimationControlsSubscription();\n    }\n    update() {\n        const { animate } = this.node.getProps();\n        const { animate: prevAnimate } = this.node.prevProps || {};\n        if (animate !== prevAnimate) {\n            this.updateAnimationControlsSubscription();\n        }\n    }\n    unmount() {\n        this.node.animationState.reset();\n        this.unmountControls?.();\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animations.mjs":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animations.mjs ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animations: () => (/* binding */ animations)\n/* harmony export */ });\n/* harmony import */ var _animation_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./animation/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs\");\n/* harmony import */ var _animation_exit_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animation/exit.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs\");\n\n\n\nconst animations = {\n    animation: {\n        Feature: _animation_index_mjs__WEBPACK_IMPORTED_MODULE_0__.AnimationFeature,\n    },\n    exit: {\n        Feature: _animation_exit_mjs__WEBPACK_IMPORTED_MODULE_1__.ExitAnimationFeature,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2FuaW1hdGlvbnMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF5RDtBQUNHOztBQUU1RDtBQUNBO0FBQ0EsaUJBQWlCLGtFQUFnQjtBQUNqQyxLQUFLO0FBQ0w7QUFDQSxpQkFBaUIscUVBQW9CO0FBQ3JDLEtBQUs7QUFDTDs7QUFFc0IiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcbW90aW9uXFxmZWF0dXJlc1xcYW5pbWF0aW9ucy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQW5pbWF0aW9uRmVhdHVyZSB9IGZyb20gJy4vYW5pbWF0aW9uL2luZGV4Lm1qcyc7XG5pbXBvcnQgeyBFeGl0QW5pbWF0aW9uRmVhdHVyZSB9IGZyb20gJy4vYW5pbWF0aW9uL2V4aXQubWpzJztcblxuY29uc3QgYW5pbWF0aW9ucyA9IHtcbiAgICBhbmltYXRpb246IHtcbiAgICAgICAgRmVhdHVyZTogQW5pbWF0aW9uRmVhdHVyZSxcbiAgICB9LFxuICAgIGV4aXQ6IHtcbiAgICAgICAgRmVhdHVyZTogRXhpdEFuaW1hdGlvbkZlYXR1cmUsXG4gICAgfSxcbn07XG5cbmV4cG9ydCB7IGFuaW1hdGlvbnMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animations.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/definitions.mjs":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/definitions.mjs ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   featureDefinitions: () => (/* binding */ featureDefinitions)\n/* harmony export */ });\nconst featureProps = {\n    animation: [\n        \"animate\",\n        \"variants\",\n        \"whileHover\",\n        \"whileTap\",\n        \"exit\",\n        \"whileInView\",\n        \"whileFocus\",\n        \"whileDrag\",\n    ],\n    exit: [\"exit\"],\n    drag: [\"drag\", \"dragControls\"],\n    focus: [\"whileFocus\"],\n    hover: [\"whileHover\", \"onHoverStart\", \"onHoverEnd\"],\n    tap: [\"whileTap\", \"onTap\", \"onTapStart\", \"onTapCancel\"],\n    pan: [\"onPan\", \"onPanStart\", \"onPanSessionStart\", \"onPanEnd\"],\n    inView: [\"whileInView\", \"onViewportEnter\", \"onViewportLeave\"],\n    layout: [\"layout\", \"layoutId\"],\n};\nconst featureDefinitions = {};\nfor (const key in featureProps) {\n    featureDefinitions[key] = {\n        isEnabled: (props) => featureProps[key].some((name) => !!props[name]),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/definitions.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/drag.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/drag.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drag: () => (/* binding */ drag)\n/* harmony export */ });\n/* harmony import */ var _gestures_drag_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../gestures/drag/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/index.mjs\");\n/* harmony import */ var _gestures_pan_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../gestures/pan/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/index.mjs\");\n/* harmony import */ var _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./layout/MeasureLayout.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs\");\n/* harmony import */ var _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../projection/node/HTMLProjectionNode.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs\");\n\n\n\n\n\nconst drag = {\n    pan: {\n        Feature: _gestures_pan_index_mjs__WEBPACK_IMPORTED_MODULE_0__.PanGesture,\n    },\n    drag: {\n        Feature: _gestures_drag_index_mjs__WEBPACK_IMPORTED_MODULE_1__.DragGesture,\n        ProjectionNode: _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_2__.HTMLProjectionNode,\n        MeasureLayout: _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_3__.MeasureLayout,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2RyYWcubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTREO0FBQ0Y7QUFDQztBQUN1Qjs7QUFFbEY7QUFDQTtBQUNBLGlCQUFpQiwrREFBVTtBQUMzQixLQUFLO0FBQ0w7QUFDQSxpQkFBaUIsaUVBQVc7QUFDNUIsd0JBQXdCLHVGQUFrQjtBQUMxQyxxQkFBcUI7QUFDckIsS0FBSztBQUNMOztBQUVnQiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxtb3Rpb25cXGZlYXR1cmVzXFxkcmFnLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBEcmFnR2VzdHVyZSB9IGZyb20gJy4uLy4uL2dlc3R1cmVzL2RyYWcvaW5kZXgubWpzJztcbmltcG9ydCB7IFBhbkdlc3R1cmUgfSBmcm9tICcuLi8uLi9nZXN0dXJlcy9wYW4vaW5kZXgubWpzJztcbmltcG9ydCB7IE1lYXN1cmVMYXlvdXQgfSBmcm9tICcuL2xheW91dC9NZWFzdXJlTGF5b3V0Lm1qcyc7XG5pbXBvcnQgeyBIVE1MUHJvamVjdGlvbk5vZGUgfSBmcm9tICcuLi8uLi9wcm9qZWN0aW9uL25vZGUvSFRNTFByb2plY3Rpb25Ob2RlLm1qcyc7XG5cbmNvbnN0IGRyYWcgPSB7XG4gICAgcGFuOiB7XG4gICAgICAgIEZlYXR1cmU6IFBhbkdlc3R1cmUsXG4gICAgfSxcbiAgICBkcmFnOiB7XG4gICAgICAgIEZlYXR1cmU6IERyYWdHZXN0dXJlLFxuICAgICAgICBQcm9qZWN0aW9uTm9kZTogSFRNTFByb2plY3Rpb25Ob2RlLFxuICAgICAgICBNZWFzdXJlTGF5b3V0LFxuICAgIH0sXG59O1xuXG5leHBvcnQgeyBkcmFnIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/drag.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/gestures.mjs":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/gestures.mjs ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gestureAnimations: () => (/* binding */ gestureAnimations)\n/* harmony export */ });\n/* harmony import */ var _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../gestures/hover.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/hover.mjs\");\n/* harmony import */ var _gestures_focus_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../gestures/focus.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/focus.mjs\");\n/* harmony import */ var _gestures_press_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../gestures/press.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/press.mjs\");\n/* harmony import */ var _viewport_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./viewport/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs\");\n\n\n\n\n\nconst gestureAnimations = {\n    inView: {\n        Feature: _viewport_index_mjs__WEBPACK_IMPORTED_MODULE_0__.InViewFeature,\n    },\n    tap: {\n        Feature: _gestures_press_mjs__WEBPACK_IMPORTED_MODULE_1__.PressGesture,\n    },\n    focus: {\n        Feature: _gestures_focus_mjs__WEBPACK_IMPORTED_MODULE_2__.FocusGesture,\n    },\n    hover: {\n        Feature: _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_3__.HoverGesture,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2dlc3R1cmVzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF3RDtBQUNBO0FBQ0E7QUFDSDs7QUFFckQ7QUFDQTtBQUNBLGlCQUFpQiw4REFBYTtBQUM5QixLQUFLO0FBQ0w7QUFDQSxpQkFBaUIsNkRBQVk7QUFDN0IsS0FBSztBQUNMO0FBQ0EsaUJBQWlCLDZEQUFZO0FBQzdCLEtBQUs7QUFDTDtBQUNBLGlCQUFpQiw2REFBWTtBQUM3QixLQUFLO0FBQ0w7O0FBRTZCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXG1vdGlvblxcZmVhdHVyZXNcXGdlc3R1cmVzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIb3Zlckdlc3R1cmUgfSBmcm9tICcuLi8uLi9nZXN0dXJlcy9ob3Zlci5tanMnO1xuaW1wb3J0IHsgRm9jdXNHZXN0dXJlIH0gZnJvbSAnLi4vLi4vZ2VzdHVyZXMvZm9jdXMubWpzJztcbmltcG9ydCB7IFByZXNzR2VzdHVyZSB9IGZyb20gJy4uLy4uL2dlc3R1cmVzL3ByZXNzLm1qcyc7XG5pbXBvcnQgeyBJblZpZXdGZWF0dXJlIH0gZnJvbSAnLi92aWV3cG9ydC9pbmRleC5tanMnO1xuXG5jb25zdCBnZXN0dXJlQW5pbWF0aW9ucyA9IHtcbiAgICBpblZpZXc6IHtcbiAgICAgICAgRmVhdHVyZTogSW5WaWV3RmVhdHVyZSxcbiAgICB9LFxuICAgIHRhcDoge1xuICAgICAgICBGZWF0dXJlOiBQcmVzc0dlc3R1cmUsXG4gICAgfSxcbiAgICBmb2N1czoge1xuICAgICAgICBGZWF0dXJlOiBGb2N1c0dlc3R1cmUsXG4gICAgfSxcbiAgICBob3Zlcjoge1xuICAgICAgICBGZWF0dXJlOiBIb3Zlckdlc3R1cmUsXG4gICAgfSxcbn07XG5cbmV4cG9ydCB7IGdlc3R1cmVBbmltYXRpb25zIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/gestures.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout.mjs":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout.mjs ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   layout: () => (/* binding */ layout)\n/* harmony export */ });\n/* harmony import */ var _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../projection/node/HTMLProjectionNode.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs\");\n/* harmony import */ var _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./layout/MeasureLayout.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs\");\n\n\n\nconst layout = {\n    layout: {\n        ProjectionNode: _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_0__.HTMLProjectionNode,\n        MeasureLayout: _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_1__.MeasureLayout,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2xheW91dC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtGO0FBQ3ZCOztBQUUzRDtBQUNBO0FBQ0Esd0JBQXdCLHVGQUFrQjtBQUMxQyxxQkFBcUI7QUFDckIsS0FBSztBQUNMOztBQUVrQiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxtb3Rpb25cXGZlYXR1cmVzXFxsYXlvdXQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEhUTUxQcm9qZWN0aW9uTm9kZSB9IGZyb20gJy4uLy4uL3Byb2plY3Rpb24vbm9kZS9IVE1MUHJvamVjdGlvbk5vZGUubWpzJztcbmltcG9ydCB7IE1lYXN1cmVMYXlvdXQgfSBmcm9tICcuL2xheW91dC9NZWFzdXJlTGF5b3V0Lm1qcyc7XG5cbmNvbnN0IGxheW91dCA9IHtcbiAgICBsYXlvdXQ6IHtcbiAgICAgICAgUHJvamVjdGlvbk5vZGU6IEhUTUxQcm9qZWN0aW9uTm9kZSxcbiAgICAgICAgTWVhc3VyZUxheW91dCxcbiAgICB9LFxufTtcblxuZXhwb3J0IHsgbGF5b3V0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MeasureLayout: () => (/* binding */ MeasureLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _components_AnimatePresence_use_presence_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/AnimatePresence/use-presence.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../context/LayoutGroupContext.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../context/SwitchLayoutGroupContext.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs\");\n/* harmony import */ var _projection_node_state_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../projection/node/state.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/node/state.mjs\");\n/* harmony import */ var _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../projection/styles/scale-border-radius.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/styles/scale-border-radius.mjs\");\n/* harmony import */ var _projection_styles_scale_box_shadow_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../projection/styles/scale-box-shadow.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs\");\n/* harmony import */ var _projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../projection/styles/scale-correction.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs\");\n\"use client\";\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Track whether we've taken any snapshots yet. If not,\n * we can safely skip notification of didUpdate.\n */\nlet hasTakenAnySnapshot = false;\nclass MeasureLayoutWithContext extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    /**\n     * This only mounts projection nodes for components that\n     * need measuring, we might want to do it for all components\n     * in order to incorporate transforms\n     */\n    componentDidMount() {\n        const { visualElement, layoutGroup, switchLayoutGroup, layoutId } = this.props;\n        const { projection } = visualElement;\n        (0,_projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_2__.addScaleCorrector)(defaultScaleCorrectors);\n        if (projection) {\n            if (layoutGroup.group)\n                layoutGroup.group.add(projection);\n            if (switchLayoutGroup && switchLayoutGroup.register && layoutId) {\n                switchLayoutGroup.register(projection);\n            }\n            if (hasTakenAnySnapshot) {\n                projection.root.didUpdate();\n            }\n            projection.addEventListener(\"animationComplete\", () => {\n                this.safeToRemove();\n            });\n            projection.setOptions({\n                ...projection.options,\n                onExitComplete: () => this.safeToRemove(),\n            });\n        }\n        _projection_node_state_mjs__WEBPACK_IMPORTED_MODULE_3__.globalProjectionState.hasEverUpdated = true;\n    }\n    getSnapshotBeforeUpdate(prevProps) {\n        const { layoutDependency, visualElement, drag, isPresent } = this.props;\n        const { projection } = visualElement;\n        if (!projection)\n            return null;\n        /**\n         * TODO: We use this data in relegate to determine whether to\n         * promote a previous element. There's no guarantee its presence data\n         * will have updated by this point - if a bug like this arises it will\n         * have to be that we markForRelegation and then find a new lead some other way,\n         * perhaps in didUpdate\n         */\n        projection.isPresent = isPresent;\n        hasTakenAnySnapshot = true;\n        if (drag ||\n            prevProps.layoutDependency !== layoutDependency ||\n            layoutDependency === undefined ||\n            prevProps.isPresent !== isPresent) {\n            projection.willUpdate();\n        }\n        else {\n            this.safeToRemove();\n        }\n        if (prevProps.isPresent !== isPresent) {\n            if (isPresent) {\n                projection.promote();\n            }\n            else if (!projection.relegate()) {\n                /**\n                 * If there's another stack member taking over from this one,\n                 * it's in charge of the exit animation and therefore should\n                 * be in charge of the safe to remove. Otherwise we call it here.\n                 */\n                motion_dom__WEBPACK_IMPORTED_MODULE_4__.frame.postRender(() => {\n                    const stack = projection.getStack();\n                    if (!stack || !stack.members.length) {\n                        this.safeToRemove();\n                    }\n                });\n            }\n        }\n        return null;\n    }\n    componentDidUpdate() {\n        const { projection } = this.props.visualElement;\n        if (projection) {\n            projection.root.didUpdate();\n            motion_dom__WEBPACK_IMPORTED_MODULE_4__.microtask.postRender(() => {\n                if (!projection.currentAnimation && projection.isLead()) {\n                    this.safeToRemove();\n                }\n            });\n        }\n    }\n    componentWillUnmount() {\n        const { visualElement, layoutGroup, switchLayoutGroup: promoteContext, } = this.props;\n        const { projection } = visualElement;\n        if (projection) {\n            projection.scheduleCheckAfterUnmount();\n            if (layoutGroup && layoutGroup.group)\n                layoutGroup.group.remove(projection);\n            if (promoteContext && promoteContext.deregister)\n                promoteContext.deregister(projection);\n        }\n    }\n    safeToRemove() {\n        const { safeToRemove } = this.props;\n        safeToRemove && safeToRemove();\n    }\n    render() {\n        return null;\n    }\n}\nfunction MeasureLayout(props) {\n    const [isPresent, safeToRemove] = (0,_components_AnimatePresence_use_presence_mjs__WEBPACK_IMPORTED_MODULE_5__.usePresence)();\n    const layoutGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__.LayoutGroupContext);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MeasureLayoutWithContext, { ...props, layoutGroup: layoutGroup, switchLayoutGroup: (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_7__.SwitchLayoutGroupContext), isPresent: isPresent, safeToRemove: safeToRemove }));\n}\nconst defaultScaleCorrectors = {\n    borderRadius: {\n        ..._projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__.correctBorderRadius,\n        applyTo: [\n            \"borderTopLeftRadius\",\n            \"borderTopRightRadius\",\n            \"borderBottomLeftRadius\",\n            \"borderBottomRightRadius\",\n        ],\n    },\n    borderTopLeftRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__.correctBorderRadius,\n    borderTopRightRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__.correctBorderRadius,\n    borderBottomLeftRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__.correctBorderRadius,\n    borderBottomRightRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__.correctBorderRadius,\n    boxShadow: _projection_styles_scale_box_shadow_mjs__WEBPACK_IMPORTED_MODULE_9__.correctBoxShadow,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/load-features.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/load-features.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadFeatures: () => (/* binding */ loadFeatures)\n/* harmony export */ });\n/* harmony import */ var _definitions_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./definitions.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/definitions.mjs\");\n\n\nfunction loadFeatures(features) {\n    for (const key in features) {\n        _definitions_mjs__WEBPACK_IMPORTED_MODULE_0__.featureDefinitions[key] = {\n            ..._definitions_mjs__WEBPACK_IMPORTED_MODULE_0__.featureDefinitions[key],\n            ...features[key],\n        };\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL2ZlYXR1cmVzL2xvYWQtZmVhdHVyZXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVEOztBQUV2RDtBQUNBO0FBQ0EsUUFBUSxnRUFBa0I7QUFDMUIsZUFBZSxnRUFBa0I7QUFDakM7QUFDQTtBQUNBO0FBQ0E7O0FBRXdCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXG1vdGlvblxcZmVhdHVyZXNcXGxvYWQtZmVhdHVyZXMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZlYXR1cmVEZWZpbml0aW9ucyB9IGZyb20gJy4vZGVmaW5pdGlvbnMubWpzJztcblxuZnVuY3Rpb24gbG9hZEZlYXR1cmVzKGZlYXR1cmVzKSB7XG4gICAgZm9yIChjb25zdCBrZXkgaW4gZmVhdHVyZXMpIHtcbiAgICAgICAgZmVhdHVyZURlZmluaXRpb25zW2tleV0gPSB7XG4gICAgICAgICAgICAuLi5mZWF0dXJlRGVmaW5pdGlvbnNba2V5XSxcbiAgICAgICAgICAgIC4uLmZlYXR1cmVzW2tleV0sXG4gICAgICAgIH07XG4gICAgfVxufVxuXG5leHBvcnQgeyBsb2FkRmVhdHVyZXMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/load-features.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InViewFeature: () => (/* binding */ InViewFeature)\n/* harmony export */ });\n/* harmony import */ var _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Feature.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var _observers_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./observers.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs\");\n\n\n\nconst thresholdNames = {\n    some: 0,\n    all: 1,\n};\nclass InViewFeature extends _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor() {\n        super(...arguments);\n        this.hasEnteredView = false;\n        this.isInView = false;\n    }\n    startObserver() {\n        this.unmount();\n        const { viewport = {} } = this.node.getProps();\n        const { root, margin: rootMargin, amount = \"some\", once } = viewport;\n        const options = {\n            root: root ? root.current : undefined,\n            rootMargin,\n            threshold: typeof amount === \"number\" ? amount : thresholdNames[amount],\n        };\n        const onIntersectionUpdate = (entry) => {\n            const { isIntersecting } = entry;\n            /**\n             * If there's been no change in the viewport state, early return.\n             */\n            if (this.isInView === isIntersecting)\n                return;\n            this.isInView = isIntersecting;\n            /**\n             * Handle hasEnteredView. If this is only meant to run once, and\n             * element isn't visible, early return. Otherwise set hasEnteredView to true.\n             */\n            if (once && !isIntersecting && this.hasEnteredView) {\n                return;\n            }\n            else if (isIntersecting) {\n                this.hasEnteredView = true;\n            }\n            if (this.node.animationState) {\n                this.node.animationState.setActive(\"whileInView\", isIntersecting);\n            }\n            /**\n             * Use the latest committed props rather than the ones in scope\n             * when this observer is created\n             */\n            const { onViewportEnter, onViewportLeave } = this.node.getProps();\n            const callback = isIntersecting ? onViewportEnter : onViewportLeave;\n            callback && callback(entry);\n        };\n        return (0,_observers_mjs__WEBPACK_IMPORTED_MODULE_1__.observeIntersection)(this.node.current, options, onIntersectionUpdate);\n    }\n    mount() {\n        this.startObserver();\n    }\n    update() {\n        if (typeof IntersectionObserver === \"undefined\")\n            return;\n        const { props, prevProps } = this.node;\n        const hasOptionsChanged = [\"amount\", \"margin\", \"root\"].some(hasViewportOptionChanged(props, prevProps));\n        if (hasOptionsChanged) {\n            this.startObserver();\n        }\n    }\n    unmount() { }\n}\nfunction hasViewportOptionChanged({ viewport = {} }, { viewport: prevViewport = {} } = {}) {\n    return (name) => viewport[name] !== prevViewport[name];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   observeIntersection: () => (/* binding */ observeIntersection)\n/* harmony export */ });\n/**\n * Map an IntersectionHandler callback to an element. We only ever make one handler for one\n * element, so even though these handlers might all be triggered by different\n * observers, we can keep them in the same map.\n */\nconst observerCallbacks = new WeakMap();\n/**\n * Multiple observers can be created for multiple element/document roots. Each with\n * different settings. So here we store dictionaries of observers to each root,\n * using serialised settings (threshold/margin) as lookup keys.\n */\nconst observers = new WeakMap();\nconst fireObserverCallback = (entry) => {\n    const callback = observerCallbacks.get(entry.target);\n    callback && callback(entry);\n};\nconst fireAllObserverCallbacks = (entries) => {\n    entries.forEach(fireObserverCallback);\n};\nfunction initIntersectionObserver({ root, ...options }) {\n    const lookupRoot = root || document;\n    /**\n     * If we don't have an observer lookup map for this root, create one.\n     */\n    if (!observers.has(lookupRoot)) {\n        observers.set(lookupRoot, {});\n    }\n    const rootObservers = observers.get(lookupRoot);\n    const key = JSON.stringify(options);\n    /**\n     * If we don't have an observer for this combination of root and settings,\n     * create one.\n     */\n    if (!rootObservers[key]) {\n        rootObservers[key] = new IntersectionObserver(fireAllObserverCallbacks, { root, ...options });\n    }\n    return rootObservers[key];\n}\nfunction observeIntersection(element, options, callback) {\n    const rootInteresectionObserver = initIntersectionObserver(options);\n    observerCallbacks.set(element, callback);\n    rootInteresectionObserver.observe(element);\n    return () => {\n        observerCallbacks.delete(element);\n        rootInteresectionObserver.unobserve(element);\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRendererMotionComponent: () => (/* binding */ createRendererMotionComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/LayoutGroupContext.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../context/LazyContext.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/LazyContext.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/MotionContext/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/MotionContext/create.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionContext/create.mjs\");\n/* harmony import */ var _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/is-browser.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/is-browser.mjs\");\n/* harmony import */ var _features_definitions_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./features/definitions.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/definitions.mjs\");\n/* harmony import */ var _features_load_features_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./features/load-features.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/load-features.mjs\");\n/* harmony import */ var _utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/symbol.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\");\n/* harmony import */ var _utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/use-motion-ref.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs\");\n/* harmony import */ var _utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/use-visual-element.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs\");\n\"use client\";\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */\nfunction createRendererMotionComponent({ preloadedFeatures, createVisualElement, useRender, useVisualState, Component, }) {\n    preloadedFeatures && (0,_features_load_features_mjs__WEBPACK_IMPORTED_MODULE_2__.loadFeatures)(preloadedFeatures);\n    function MotionComponent(props, externalRef) {\n        /**\n         * If we need to measure the element we load this functionality in a\n         * separate class component in order to gain access to getSnapshotBeforeUpdate.\n         */\n        let MeasureLayout;\n        const configAndProps = {\n            ...(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext),\n            ...props,\n            layoutId: useLayoutId(props),\n        };\n        const { isStatic } = configAndProps;\n        const context = (0,_context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__.useCreateMotionContext)(props);\n        const visualState = useVisualState(props, isStatic);\n        if (!isStatic && _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_5__.isBrowser) {\n            useStrictMode(configAndProps, preloadedFeatures);\n            const layoutProjection = getProjectionFunctionality(configAndProps);\n            MeasureLayout = layoutProjection.MeasureLayout;\n            /**\n             * Create a VisualElement for this component. A VisualElement provides a common\n             * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n             * providing a way of rendering to these APIs outside of the React render loop\n             * for more performant animations and interactions\n             */\n            context.visualElement = (0,_utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__.useVisualElement)(Component, visualState, configAndProps, createVisualElement, layoutProjection.ProjectionNode);\n        }\n        /**\n         * The mount order and hierarchy is specific to ensure our element ref\n         * is hydrated by the time features fire their effects.\n         */\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_7__.MotionContext.Provider, { value: context, children: [MeasureLayout && context.visualElement ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MeasureLayout, { visualElement: context.visualElement, ...configAndProps })) : null, useRender(Component, props, (0,_utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__.useMotionRef)(visualState, context.visualElement, externalRef), visualState, isStatic, context.visualElement)] }));\n    }\n    MotionComponent.displayName = `motion.${typeof Component === \"string\"\n        ? Component\n        : `create(${Component.displayName ?? Component.name ?? \"\"})`}`;\n    const ForwardRefMotionComponent = (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(MotionComponent);\n    ForwardRefMotionComponent[_utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_9__.motionComponentSymbol] = Component;\n    return ForwardRefMotionComponent;\n}\nfunction useLayoutId({ layoutId }) {\n    const layoutGroupId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_10__.LayoutGroupContext).id;\n    return layoutGroupId && layoutId !== undefined\n        ? layoutGroupId + \"-\" + layoutId\n        : layoutId;\n}\nfunction useStrictMode(configAndProps, preloadedFeatures) {\n    const isStrict = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_11__.LazyContext).strict;\n    /**\n     * If we're in development mode, check to make sure we're not rendering a motion component\n     * as a child of LazyMotion, as this will break the file-size benefits of using it.\n     */\n    if ( true &&\n        preloadedFeatures &&\n        isStrict) {\n        const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n        configAndProps.ignoreStrict\n            ? (0,motion_utils__WEBPACK_IMPORTED_MODULE_12__.warning)(false, strictMessage)\n            : (0,motion_utils__WEBPACK_IMPORTED_MODULE_12__.invariant)(false, strictMessage);\n    }\n}\nfunction getProjectionFunctionality(props) {\n    const { drag, layout } = _features_definitions_mjs__WEBPACK_IMPORTED_MODULE_13__.featureDefinitions;\n    if (!drag && !layout)\n        return {};\n    const combined = { ...drag, ...layout };\n    return {\n        MeasureLayout: drag?.isEnabled(props) || layout?.isEnabled(props)\n            ? combined.MeasureLayout\n            : undefined,\n        ProjectionNode: combined.ProjectionNode,\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isForcedMotionValue: () => (/* binding */ isForcedMotionValue)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../projection/styles/scale-correction.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs\");\n\n\n\nfunction isForcedMotionValue(key, { layout, layoutId }) {\n    return (motion_dom__WEBPACK_IMPORTED_MODULE_0__.transformProps.has(key) ||\n        key.startsWith(\"origin\") ||\n        ((layout || layoutId !== undefined) &&\n            (!!_projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_1__.scaleCorrectors[key] || key === \"opacity\")));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL3V0aWxzL2lzLWZvcmNlZC1tb3Rpb24tdmFsdWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNtQzs7QUFFL0Usb0NBQW9DLGtCQUFrQjtBQUN0RCxZQUFZLHNEQUFjO0FBQzFCO0FBQ0E7QUFDQSxlQUFlLG9GQUFlO0FBQzlCOztBQUUrQiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxtb3Rpb25cXHV0aWxzXFxpcy1mb3JjZWQtbW90aW9uLXZhbHVlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0cmFuc2Zvcm1Qcm9wcyB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgc2NhbGVDb3JyZWN0b3JzIH0gZnJvbSAnLi4vLi4vcHJvamVjdGlvbi9zdHlsZXMvc2NhbGUtY29ycmVjdGlvbi5tanMnO1xuXG5mdW5jdGlvbiBpc0ZvcmNlZE1vdGlvblZhbHVlKGtleSwgeyBsYXlvdXQsIGxheW91dElkIH0pIHtcbiAgICByZXR1cm4gKHRyYW5zZm9ybVByb3BzLmhhcyhrZXkpIHx8XG4gICAgICAgIGtleS5zdGFydHNXaXRoKFwib3JpZ2luXCIpIHx8XG4gICAgICAgICgobGF5b3V0IHx8IGxheW91dElkICE9PSB1bmRlZmluZWQpICYmXG4gICAgICAgICAgICAoISFzY2FsZUNvcnJlY3RvcnNba2V5XSB8fCBrZXkgPT09IFwib3BhY2l0eVwiKSkpO1xufVxuXG5leHBvcnQgeyBpc0ZvcmNlZE1vdGlvblZhbHVlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-motion-component.mjs":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-motion-component.mjs ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isMotionComponent: () => (/* binding */ isMotionComponent)\n/* harmony export */ });\n/* harmony import */ var _symbol_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./symbol.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\");\n\n\n/**\n * Checks if a component is a `motion` component.\n */\nfunction isMotionComponent(component) {\n    return (component !== null &&\n        typeof component === \"object\" &&\n        _symbol_mjs__WEBPACK_IMPORTED_MODULE_0__.motionComponentSymbol in component);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL3V0aWxzL2lzLW1vdGlvbi1jb21wb25lbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFEOztBQUVyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhEQUFxQjtBQUM3Qjs7QUFFNkIiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcbW90aW9uXFx1dGlsc1xcaXMtbW90aW9uLWNvbXBvbmVudC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbW90aW9uQ29tcG9uZW50U3ltYm9sIH0gZnJvbSAnLi9zeW1ib2wubWpzJztcblxuLyoqXG4gKiBDaGVja3MgaWYgYSBjb21wb25lbnQgaXMgYSBgbW90aW9uYCBjb21wb25lbnQuXG4gKi9cbmZ1bmN0aW9uIGlzTW90aW9uQ29tcG9uZW50KGNvbXBvbmVudCkge1xuICAgIHJldHVybiAoY29tcG9uZW50ICE9PSBudWxsICYmXG4gICAgICAgIHR5cGVvZiBjb21wb25lbnQgPT09IFwib2JqZWN0XCIgJiZcbiAgICAgICAgbW90aW9uQ29tcG9uZW50U3ltYm9sIGluIGNvbXBvbmVudCk7XG59XG5cbmV4cG9ydCB7IGlzTW90aW9uQ29tcG9uZW50IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-motion-component.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs":
/*!********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   motionComponentSymbol: () => (/* binding */ motionComponentSymbol)\n/* harmony export */ });\nconst motionComponentSymbol = Symbol.for(\"motionComponentSymbol\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL3V0aWxzL3N5bWJvbC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVpQyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxtb3Rpb25cXHV0aWxzXFxzeW1ib2wubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1vdGlvbkNvbXBvbmVudFN5bWJvbCA9IFN5bWJvbC5mb3IoXCJtb3Rpb25Db21wb25lbnRTeW1ib2xcIik7XG5cbmV4cG9ydCB7IG1vdGlvbkNvbXBvbmVudFN5bWJvbCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/unwrap-motion-component.mjs":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/unwrap-motion-component.mjs ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unwrapMotionComponent: () => (/* binding */ unwrapMotionComponent)\n/* harmony export */ });\n/* harmony import */ var _is_motion_component_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-motion-component.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-motion-component.mjs\");\n/* harmony import */ var _symbol_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./symbol.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\");\n\n\n\n/**\n * Unwraps a `motion` component and returns either a string for `motion.div` or\n * the React component for `motion(Component)`.\n *\n * If the component is not a `motion` component it returns undefined.\n */\nfunction unwrapMotionComponent(component) {\n    if ((0,_is_motion_component_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionComponent)(component)) {\n        return component[_symbol_mjs__WEBPACK_IMPORTED_MODULE_1__.motionComponentSymbol];\n    }\n    return undefined;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvbW90aW9uL3V0aWxzL3Vud3JhcC1tb3Rpb24tY29tcG9uZW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEQ7QUFDVDs7QUFFckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDJFQUFpQjtBQUN6Qix5QkFBeUIsOERBQXFCO0FBQzlDO0FBQ0E7QUFDQTs7QUFFaUMiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcbW90aW9uXFx1dGlsc1xcdW53cmFwLW1vdGlvbi1jb21wb25lbnQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzTW90aW9uQ29tcG9uZW50IH0gZnJvbSAnLi9pcy1tb3Rpb24tY29tcG9uZW50Lm1qcyc7XG5pbXBvcnQgeyBtb3Rpb25Db21wb25lbnRTeW1ib2wgfSBmcm9tICcuL3N5bWJvbC5tanMnO1xuXG4vKipcbiAqIFVud3JhcHMgYSBgbW90aW9uYCBjb21wb25lbnQgYW5kIHJldHVybnMgZWl0aGVyIGEgc3RyaW5nIGZvciBgbW90aW9uLmRpdmAgb3JcbiAqIHRoZSBSZWFjdCBjb21wb25lbnQgZm9yIGBtb3Rpb24oQ29tcG9uZW50KWAuXG4gKlxuICogSWYgdGhlIGNvbXBvbmVudCBpcyBub3QgYSBgbW90aW9uYCBjb21wb25lbnQgaXQgcmV0dXJucyB1bmRlZmluZWQuXG4gKi9cbmZ1bmN0aW9uIHVud3JhcE1vdGlvbkNvbXBvbmVudChjb21wb25lbnQpIHtcbiAgICBpZiAoaXNNb3Rpb25Db21wb25lbnQoY29tcG9uZW50KSkge1xuICAgICAgICByZXR1cm4gY29tcG9uZW50W21vdGlvbkNvbXBvbmVudFN5bWJvbF07XG4gICAgfVxuICAgIHJldHVybiB1bmRlZmluZWQ7XG59XG5cbmV4cG9ydCB7IHVud3JhcE1vdGlvbkNvbXBvbmVudCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/unwrap-motion-component.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionRef: () => (/* binding */ useMotionRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/is-ref-object.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/is-ref-object.mjs\");\n\n\n\n/**\n * Creates a ref function that, when called, hydrates the provided\n * external ref and VisualElement.\n */\nfunction useMotionRef(visualState, visualElement, externalRef) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((instance) => {\n        if (instance) {\n            visualState.onMount && visualState.onMount(instance);\n        }\n        if (visualElement) {\n            if (instance) {\n                visualElement.mount(instance);\n            }\n            else {\n                visualElement.unmount();\n            }\n        }\n        if (externalRef) {\n            if (typeof externalRef === \"function\") {\n                externalRef(instance);\n            }\n            else if ((0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_1__.isRefObject)(externalRef)) {\n                externalRef.current = instance;\n            }\n        }\n    }, \n    /**\n     * Only pass a new ref callback to React if we've received a visual element\n     * factory. Otherwise we'll be mounting/remounting every time externalRef\n     * or other dependencies change.\n     */\n    [visualElement]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useVisualElement: () => (/* binding */ useVisualElement)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _animation_optimized_appear_data_id_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../animation/optimized-appear/data-id.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs\");\n/* harmony import */ var _context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/LazyContext.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/LazyContext.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../context/MotionConfigContext.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../context/MotionContext/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../context/SwitchLayoutGroupContext.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs\");\n/* harmony import */ var _utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../utils/is-ref-object.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/is-ref-object.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/use-isomorphic-effect.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n\n\n\n\n\n\n\n\n\n\n\nfunction useVisualElement(Component, visualState, props, createVisualElement, ProjectionNodeConstructor) {\n    const { visualElement: parent } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionContext);\n    const lazyContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_2__.LazyContext);\n    const presenceContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__.PresenceContext);\n    const reducedMotionConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_4__.MotionConfigContext).reducedMotion;\n    const visualElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    /**\n     * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n     */\n    createVisualElement = createVisualElement || lazyContext.renderer;\n    if (!visualElementRef.current && createVisualElement) {\n        visualElementRef.current = createVisualElement(Component, {\n            visualState,\n            parent,\n            props,\n            presenceContext,\n            blockInitialAnimation: presenceContext\n                ? presenceContext.initial === false\n                : false,\n            reducedMotionConfig,\n        });\n    }\n    const visualElement = visualElementRef.current;\n    /**\n     * Load Motion gesture and animation features. These are rendered as renderless\n     * components so each feature can optionally make use of React lifecycle methods.\n     */\n    const initialLayoutGroupConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_5__.SwitchLayoutGroupContext);\n    if (visualElement &&\n        !visualElement.projection &&\n        ProjectionNodeConstructor &&\n        (visualElement.type === \"html\" || visualElement.type === \"svg\")) {\n        createProjectionNode(visualElementRef.current, props, ProjectionNodeConstructor, initialLayoutGroupConfig);\n    }\n    const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useInsertionEffect)(() => {\n        /**\n         * Check the component has already mounted before calling\n         * `update` unnecessarily. This ensures we skip the initial update.\n         */\n        if (visualElement && isMounted.current) {\n            visualElement.update(props, presenceContext);\n        }\n    });\n    /**\n     * Cache this value as we want to know whether HandoffAppearAnimations\n     * was present on initial render - it will be deleted after this.\n     */\n    const optimisedAppearId = props[_animation_optimized_appear_data_id_mjs__WEBPACK_IMPORTED_MODULE_6__.optimizedAppearDataAttribute];\n    const wantsHandoff = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Boolean(optimisedAppearId) &&\n        !window.MotionHandoffIsComplete?.(optimisedAppearId) &&\n        window.MotionHasOptimisedAnimation?.(optimisedAppearId));\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(() => {\n        if (!visualElement)\n            return;\n        isMounted.current = true;\n        window.MotionIsMounted = true;\n        visualElement.updateFeatures();\n        motion_dom__WEBPACK_IMPORTED_MODULE_8__.microtask.render(visualElement.render);\n        /**\n         * Ideally this function would always run in a useEffect.\n         *\n         * However, if we have optimised appear animations to handoff from,\n         * it needs to happen synchronously to ensure there's no flash of\n         * incorrect styles in the event of a hydration error.\n         *\n         * So if we detect a situtation where optimised appear animations\n         * are running, we use useLayoutEffect to trigger animations.\n         */\n        if (wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!visualElement)\n            return;\n        if (!wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n        if (wantsHandoff.current) {\n            // This ensures all future calls to animateChanges() in this component will run in useEffect\n            queueMicrotask(() => {\n                window.MotionHandoffMarkAsComplete?.(optimisedAppearId);\n            });\n            wantsHandoff.current = false;\n        }\n    });\n    return visualElement;\n}\nfunction createProjectionNode(visualElement, props, ProjectionNodeConstructor, initialPromotionConfig) {\n    const { layoutId, layout, drag, dragConstraints, layoutScroll, layoutRoot, layoutCrossfade, } = props;\n    visualElement.projection = new ProjectionNodeConstructor(visualElement.latestValues, props[\"data-framer-portal-id\"]\n        ? undefined\n        : getClosestProjectingNode(visualElement.parent));\n    visualElement.projection.setOptions({\n        layoutId,\n        layout,\n        alwaysMeasureLayout: Boolean(drag) || (dragConstraints && (0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__.isRefObject)(dragConstraints)),\n        visualElement,\n        /**\n         * TODO: Update options in an effect. This could be tricky as it'll be too late\n         * to update by the time layout animations run.\n         * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n         * ensuring it gets called if there's no potential layout animations.\n         *\n         */\n        animationType: typeof layout === \"string\" ? layout : \"both\",\n        initialPromotionConfig,\n        crossfade: layoutCrossfade,\n        layoutScroll,\n        layoutRoot,\n    });\n}\nfunction getClosestProjectingNode(visualElement) {\n    if (!visualElement)\n        return undefined;\n    return visualElement.options.allowProjection !== false\n        ? visualElement.projection\n        : getClosestProjectingNode(visualElement.parent);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeUseVisualState: () => (/* binding */ makeUseVisualState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../animation/utils/is-animation-controls.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../context/MotionContext/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _render_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../render/utils/is-controlling-variants.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs\");\n/* harmony import */ var _render_utils_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../render/utils/resolve-variants.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _value_utils_resolve_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../value/utils/resolve-motion-value.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs\");\n\n\n\n\n\n\n\n\n\nfunction makeState({ scrapeMotionValuesFromProps, createRenderState, }, props, context, presenceContext) {\n    const state = {\n        latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n        renderState: createRenderState(),\n    };\n    return state;\n}\nconst makeUseVisualState = (config) => (props, isStatic) => {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionContext);\n    const presenceContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_2__.PresenceContext);\n    const make = () => makeState(config, props, context, presenceContext);\n    return isStatic ? make() : (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__.useConstant)(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n    const values = {};\n    const motionValues = scrapeMotionValues(props, {});\n    for (const key in motionValues) {\n        values[key] = (0,_value_utils_resolve_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__.resolveMotionValue)(motionValues[key]);\n    }\n    let { initial, animate } = props;\n    const isControllingVariants$1 = (0,_render_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_5__.isControllingVariants)(props);\n    const isVariantNode$1 = (0,_render_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_5__.isVariantNode)(props);\n    if (context &&\n        isVariantNode$1 &&\n        !isControllingVariants$1 &&\n        props.inherit !== false) {\n        if (initial === undefined)\n            initial = context.initial;\n        if (animate === undefined)\n            animate = context.animate;\n    }\n    let isInitialAnimationBlocked = presenceContext\n        ? presenceContext.initial === false\n        : false;\n    isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n    const variantToSet = isInitialAnimationBlocked ? animate : initial;\n    if (variantToSet &&\n        typeof variantToSet !== \"boolean\" &&\n        !(0,_animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_6__.isAnimationControls)(variantToSet)) {\n        const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n        for (let i = 0; i < list.length; i++) {\n            const resolved = (0,_render_utils_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_7__.resolveVariantFromProps)(props, list[i]);\n            if (resolved) {\n                const { transitionEnd, transition, ...target } = resolved;\n                for (const key in target) {\n                    let valueTarget = target[key];\n                    if (Array.isArray(valueTarget)) {\n                        /**\n                         * Take final keyframe if the initial animation is blocked because\n                         * we want to initialise at the end of that blocked animation.\n                         */\n                        const index = isInitialAnimationBlocked\n                            ? valueTarget.length - 1\n                            : 0;\n                        valueTarget = valueTarget[index];\n                    }\n                    if (valueTarget !== null) {\n                        values[key] = valueTarget;\n                    }\n                }\n                for (const key in transitionEnd) {\n                    values[key] = transitionEnd[key];\n                }\n            }\n        }\n    }\n    return values;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isValidMotionProp: () => (/* binding */ isValidMotionProp)\n/* harmony export */ });\n/**\n * A list of all valid MotionProps.\n *\n * @privateRemarks\n * This doesn't throw if a `MotionProp` name is missing - it should.\n */\nconst validMotionProps = new Set([\n    \"animate\",\n    \"exit\",\n    \"variants\",\n    \"initial\",\n    \"style\",\n    \"values\",\n    \"variants\",\n    \"transition\",\n    \"transformTemplate\",\n    \"custom\",\n    \"inherit\",\n    \"onBeforeLayoutMeasure\",\n    \"onAnimationStart\",\n    \"onAnimationComplete\",\n    \"onUpdate\",\n    \"onDragStart\",\n    \"onDrag\",\n    \"onDragEnd\",\n    \"onMeasureDragConstraints\",\n    \"onDirectionLock\",\n    \"onDragTransitionEnd\",\n    \"_dragX\",\n    \"_dragY\",\n    \"onHoverStart\",\n    \"onHoverEnd\",\n    \"onViewportEnter\",\n    \"onViewportLeave\",\n    \"globalTapTarget\",\n    \"ignoreStrict\",\n    \"viewport\",\n]);\n/**\n * Check whether a prop name is a valid `MotionProp` key.\n *\n * @param key - Name of the property to check\n * @returns `true` is key is a valid `MotionProp`.\n *\n * @public\n */\nfunction isValidMotionProp(key) {\n    return (key.startsWith(\"while\") ||\n        (key.startsWith(\"drag\") && key !== \"draggable\") ||\n        key.startsWith(\"layout\") ||\n        key.startsWith(\"onTap\") ||\n        key.startsWith(\"onPan\") ||\n        key.startsWith(\"onLayout\") ||\n        validMotionProps.has(key));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Feature: () => (/* binding */ Feature)\n/* harmony export */ });\nclass Feature {\n    constructor(node) {\n        this.isMounted = false;\n        this.node = node;\n    }\n    update() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi9mZWF0dXJlcy9GZWF0dXJlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW1CIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXG1vdGlvblxcZmVhdHVyZXNcXEZlYXR1cmUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNsYXNzIEZlYXR1cmUge1xuICAgIGNvbnN0cnVjdG9yKG5vZGUpIHtcbiAgICAgICAgdGhpcy5pc01vdW50ZWQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5ub2RlID0gbm9kZTtcbiAgICB9XG4gICAgdXBkYXRlKCkgeyB9XG59XG5cbmV4cG9ydCB7IEZlYXR1cmUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExitAnimationFeature: () => (/* binding */ ExitAnimationFeature)\n/* harmony export */ });\n/* harmony import */ var _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Feature.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\nlet id = 0;\nclass ExitAnimationFeature extends _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor() {\n        super(...arguments);\n        this.id = id++;\n    }\n    update() {\n        if (!this.node.presenceContext)\n            return;\n        const { isPresent, onExitComplete } = this.node.presenceContext;\n        const { isPresent: prevIsPresent } = this.node.prevPresenceContext || {};\n        if (!this.node.animationState || isPresent === prevIsPresent) {\n            return;\n        }\n        const exitAnimation = this.node.animationState.setActive(\"exit\", !isPresent);\n        if (onExitComplete && !isPresent) {\n            exitAnimation.then(() => {\n                onExitComplete(this.id);\n            });\n        }\n    }\n    mount() {\n        const { register, onExitComplete } = this.node.presenceContext || {};\n        if (onExitComplete) {\n            onExitComplete(this.id);\n        }\n        if (register) {\n            this.unmount = register(this.id);\n        }\n    }\n    unmount() { }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimationFeature: () => (/* binding */ AnimationFeature)\n/* harmony export */ });\n/* harmony import */ var _animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../animation/utils/is-animation-controls.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs\");\n/* harmony import */ var _render_utils_animation_state_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../render/utils/animation-state.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/animation-state.mjs\");\n/* harmony import */ var _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Feature.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n\n\n\n\nclass AnimationFeature extends _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    /**\n     * We dynamically generate the AnimationState manager as it contains a reference\n     * to the underlying animation library. We only want to load that if we load this,\n     * so people can optionally code split it out using the `m` component.\n     */\n    constructor(node) {\n        super(node);\n        node.animationState || (node.animationState = (0,_render_utils_animation_state_mjs__WEBPACK_IMPORTED_MODULE_1__.createAnimationState)(node));\n    }\n    updateAnimationControlsSubscription() {\n        const { animate } = this.node.getProps();\n        if ((0,_animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_2__.isAnimationControls)(animate)) {\n            this.unmountControls = animate.subscribe(this.node);\n        }\n    }\n    /**\n     * Subscribe any provided AnimationControls to the component's VisualElement\n     */\n    mount() {\n        this.updateAnimationControlsSubscription();\n    }\n    update() {\n        const { animate } = this.node.getProps();\n        const { animate: prevAnimate } = this.node.prevProps || {};\n        if (animate !== prevAnimate) {\n            this.updateAnimationControlsSubscription();\n        }\n    }\n    unmount() {\n        this.node.animationState.reset();\n        this.unmountControls?.();\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animations.mjs":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animations.mjs ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animations: () => (/* binding */ animations)\n/* harmony export */ });\n/* harmony import */ var _animation_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./animation/index.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs\");\n/* harmony import */ var _animation_exit_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animation/exit.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs\");\n\n\n\nconst animations = {\n    animation: {\n        Feature: _animation_index_mjs__WEBPACK_IMPORTED_MODULE_0__.AnimationFeature,\n    },\n    exit: {\n        Feature: _animation_exit_mjs__WEBPACK_IMPORTED_MODULE_1__.ExitAnimationFeature,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi9mZWF0dXJlcy9hbmltYXRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBeUQ7QUFDRzs7QUFFNUQ7QUFDQTtBQUNBLGlCQUFpQixrRUFBZ0I7QUFDakMsS0FBSztBQUNMO0FBQ0EsaUJBQWlCLHFFQUFvQjtBQUNyQyxLQUFLO0FBQ0w7O0FBRXNCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXG1vdGlvblxcZmVhdHVyZXNcXGFuaW1hdGlvbnMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFuaW1hdGlvbkZlYXR1cmUgfSBmcm9tICcuL2FuaW1hdGlvbi9pbmRleC5tanMnO1xuaW1wb3J0IHsgRXhpdEFuaW1hdGlvbkZlYXR1cmUgfSBmcm9tICcuL2FuaW1hdGlvbi9leGl0Lm1qcyc7XG5cbmNvbnN0IGFuaW1hdGlvbnMgPSB7XG4gICAgYW5pbWF0aW9uOiB7XG4gICAgICAgIEZlYXR1cmU6IEFuaW1hdGlvbkZlYXR1cmUsXG4gICAgfSxcbiAgICBleGl0OiB7XG4gICAgICAgIEZlYXR1cmU6IEV4aXRBbmltYXRpb25GZWF0dXJlLFxuICAgIH0sXG59O1xuXG5leHBvcnQgeyBhbmltYXRpb25zIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/animations.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/definitions.mjs":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/definitions.mjs ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   featureDefinitions: () => (/* binding */ featureDefinitions)\n/* harmony export */ });\nconst featureProps = {\n    animation: [\n        \"animate\",\n        \"variants\",\n        \"whileHover\",\n        \"whileTap\",\n        \"exit\",\n        \"whileInView\",\n        \"whileFocus\",\n        \"whileDrag\",\n    ],\n    exit: [\"exit\"],\n    drag: [\"drag\", \"dragControls\"],\n    focus: [\"whileFocus\"],\n    hover: [\"whileHover\", \"onHoverStart\", \"onHoverEnd\"],\n    tap: [\"whileTap\", \"onTap\", \"onTapStart\", \"onTapCancel\"],\n    pan: [\"onPan\", \"onPanStart\", \"onPanSessionStart\", \"onPanEnd\"],\n    inView: [\"whileInView\", \"onViewportEnter\", \"onViewportLeave\"],\n    layout: [\"layout\", \"layoutId\"],\n};\nconst featureDefinitions = {};\nfor (const key in featureProps) {\n    featureDefinitions[key] = {\n        isEnabled: (props) => featureProps[key].some((name) => !!props[name]),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi9mZWF0dXJlcy9kZWZpbml0aW9ucy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXG1vdGlvblxcZmVhdHVyZXNcXGRlZmluaXRpb25zLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBmZWF0dXJlUHJvcHMgPSB7XG4gICAgYW5pbWF0aW9uOiBbXG4gICAgICAgIFwiYW5pbWF0ZVwiLFxuICAgICAgICBcInZhcmlhbnRzXCIsXG4gICAgICAgIFwid2hpbGVIb3ZlclwiLFxuICAgICAgICBcIndoaWxlVGFwXCIsXG4gICAgICAgIFwiZXhpdFwiLFxuICAgICAgICBcIndoaWxlSW5WaWV3XCIsXG4gICAgICAgIFwid2hpbGVGb2N1c1wiLFxuICAgICAgICBcIndoaWxlRHJhZ1wiLFxuICAgIF0sXG4gICAgZXhpdDogW1wiZXhpdFwiXSxcbiAgICBkcmFnOiBbXCJkcmFnXCIsIFwiZHJhZ0NvbnRyb2xzXCJdLFxuICAgIGZvY3VzOiBbXCJ3aGlsZUZvY3VzXCJdLFxuICAgIGhvdmVyOiBbXCJ3aGlsZUhvdmVyXCIsIFwib25Ib3ZlclN0YXJ0XCIsIFwib25Ib3ZlckVuZFwiXSxcbiAgICB0YXA6IFtcIndoaWxlVGFwXCIsIFwib25UYXBcIiwgXCJvblRhcFN0YXJ0XCIsIFwib25UYXBDYW5jZWxcIl0sXG4gICAgcGFuOiBbXCJvblBhblwiLCBcIm9uUGFuU3RhcnRcIiwgXCJvblBhblNlc3Npb25TdGFydFwiLCBcIm9uUGFuRW5kXCJdLFxuICAgIGluVmlldzogW1wid2hpbGVJblZpZXdcIiwgXCJvblZpZXdwb3J0RW50ZXJcIiwgXCJvblZpZXdwb3J0TGVhdmVcIl0sXG4gICAgbGF5b3V0OiBbXCJsYXlvdXRcIiwgXCJsYXlvdXRJZFwiXSxcbn07XG5jb25zdCBmZWF0dXJlRGVmaW5pdGlvbnMgPSB7fTtcbmZvciAoY29uc3Qga2V5IGluIGZlYXR1cmVQcm9wcykge1xuICAgIGZlYXR1cmVEZWZpbml0aW9uc1trZXldID0ge1xuICAgICAgICBpc0VuYWJsZWQ6IChwcm9wcykgPT4gZmVhdHVyZVByb3BzW2tleV0uc29tZSgobmFtZSkgPT4gISFwcm9wc1tuYW1lXSksXG4gICAgfTtcbn1cblxuZXhwb3J0IHsgZmVhdHVyZURlZmluaXRpb25zIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/definitions.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/drag.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/drag.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drag: () => (/* binding */ drag)\n/* harmony export */ });\n/* harmony import */ var _gestures_drag_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../gestures/drag/index.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/drag/index.mjs\");\n/* harmony import */ var _gestures_pan_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../gestures/pan/index.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/pan/index.mjs\");\n/* harmony import */ var _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./layout/MeasureLayout.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs\");\n/* harmony import */ var _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../projection/node/HTMLProjectionNode.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs\");\n\n\n\n\n\nconst drag = {\n    pan: {\n        Feature: _gestures_pan_index_mjs__WEBPACK_IMPORTED_MODULE_0__.PanGesture,\n    },\n    drag: {\n        Feature: _gestures_drag_index_mjs__WEBPACK_IMPORTED_MODULE_1__.DragGesture,\n        ProjectionNode: _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_2__.HTMLProjectionNode,\n        MeasureLayout: _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_3__.MeasureLayout,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi9mZWF0dXJlcy9kcmFnLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE0RDtBQUNGO0FBQ0M7QUFDdUI7O0FBRWxGO0FBQ0E7QUFDQSxpQkFBaUIsK0RBQVU7QUFDM0IsS0FBSztBQUNMO0FBQ0EsaUJBQWlCLGlFQUFXO0FBQzVCLHdCQUF3Qix1RkFBa0I7QUFDMUMscUJBQXFCO0FBQ3JCLEtBQUs7QUFDTDs7QUFFZ0IiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcbW90aW9uXFxmZWF0dXJlc1xcZHJhZy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRHJhZ0dlc3R1cmUgfSBmcm9tICcuLi8uLi9nZXN0dXJlcy9kcmFnL2luZGV4Lm1qcyc7XG5pbXBvcnQgeyBQYW5HZXN0dXJlIH0gZnJvbSAnLi4vLi4vZ2VzdHVyZXMvcGFuL2luZGV4Lm1qcyc7XG5pbXBvcnQgeyBNZWFzdXJlTGF5b3V0IH0gZnJvbSAnLi9sYXlvdXQvTWVhc3VyZUxheW91dC5tanMnO1xuaW1wb3J0IHsgSFRNTFByb2plY3Rpb25Ob2RlIH0gZnJvbSAnLi4vLi4vcHJvamVjdGlvbi9ub2RlL0hUTUxQcm9qZWN0aW9uTm9kZS5tanMnO1xuXG5jb25zdCBkcmFnID0ge1xuICAgIHBhbjoge1xuICAgICAgICBGZWF0dXJlOiBQYW5HZXN0dXJlLFxuICAgIH0sXG4gICAgZHJhZzoge1xuICAgICAgICBGZWF0dXJlOiBEcmFnR2VzdHVyZSxcbiAgICAgICAgUHJvamVjdGlvbk5vZGU6IEhUTUxQcm9qZWN0aW9uTm9kZSxcbiAgICAgICAgTWVhc3VyZUxheW91dCxcbiAgICB9LFxufTtcblxuZXhwb3J0IHsgZHJhZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/drag.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/gestures.mjs":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/gestures.mjs ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gestureAnimations: () => (/* binding */ gestureAnimations)\n/* harmony export */ });\n/* harmony import */ var _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../gestures/hover.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/hover.mjs\");\n/* harmony import */ var _gestures_focus_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../gestures/focus.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/focus.mjs\");\n/* harmony import */ var _gestures_press_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../gestures/press.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/gestures/press.mjs\");\n/* harmony import */ var _viewport_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./viewport/index.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs\");\n\n\n\n\n\nconst gestureAnimations = {\n    inView: {\n        Feature: _viewport_index_mjs__WEBPACK_IMPORTED_MODULE_0__.InViewFeature,\n    },\n    tap: {\n        Feature: _gestures_press_mjs__WEBPACK_IMPORTED_MODULE_1__.PressGesture,\n    },\n    focus: {\n        Feature: _gestures_focus_mjs__WEBPACK_IMPORTED_MODULE_2__.FocusGesture,\n    },\n    hover: {\n        Feature: _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_3__.HoverGesture,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi9mZWF0dXJlcy9nZXN0dXJlcy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBd0Q7QUFDQTtBQUNBO0FBQ0g7O0FBRXJEO0FBQ0E7QUFDQSxpQkFBaUIsOERBQWE7QUFDOUIsS0FBSztBQUNMO0FBQ0EsaUJBQWlCLDZEQUFZO0FBQzdCLEtBQUs7QUFDTDtBQUNBLGlCQUFpQiw2REFBWTtBQUM3QixLQUFLO0FBQ0w7QUFDQSxpQkFBaUIsNkRBQVk7QUFDN0IsS0FBSztBQUNMOztBQUU2QiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxtb3Rpb25cXGZlYXR1cmVzXFxnZXN0dXJlcy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSG92ZXJHZXN0dXJlIH0gZnJvbSAnLi4vLi4vZ2VzdHVyZXMvaG92ZXIubWpzJztcbmltcG9ydCB7IEZvY3VzR2VzdHVyZSB9IGZyb20gJy4uLy4uL2dlc3R1cmVzL2ZvY3VzLm1qcyc7XG5pbXBvcnQgeyBQcmVzc0dlc3R1cmUgfSBmcm9tICcuLi8uLi9nZXN0dXJlcy9wcmVzcy5tanMnO1xuaW1wb3J0IHsgSW5WaWV3RmVhdHVyZSB9IGZyb20gJy4vdmlld3BvcnQvaW5kZXgubWpzJztcblxuY29uc3QgZ2VzdHVyZUFuaW1hdGlvbnMgPSB7XG4gICAgaW5WaWV3OiB7XG4gICAgICAgIEZlYXR1cmU6IEluVmlld0ZlYXR1cmUsXG4gICAgfSxcbiAgICB0YXA6IHtcbiAgICAgICAgRmVhdHVyZTogUHJlc3NHZXN0dXJlLFxuICAgIH0sXG4gICAgZm9jdXM6IHtcbiAgICAgICAgRmVhdHVyZTogRm9jdXNHZXN0dXJlLFxuICAgIH0sXG4gICAgaG92ZXI6IHtcbiAgICAgICAgRmVhdHVyZTogSG92ZXJHZXN0dXJlLFxuICAgIH0sXG59O1xuXG5leHBvcnQgeyBnZXN0dXJlQW5pbWF0aW9ucyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/gestures.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout.mjs":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout.mjs ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   layout: () => (/* binding */ layout)\n/* harmony export */ });\n/* harmony import */ var _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../projection/node/HTMLProjectionNode.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs\");\n/* harmony import */ var _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./layout/MeasureLayout.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs\");\n\n\n\nconst layout = {\n    layout: {\n        ProjectionNode: _projection_node_HTMLProjectionNode_mjs__WEBPACK_IMPORTED_MODULE_0__.HTMLProjectionNode,\n        MeasureLayout: _layout_MeasureLayout_mjs__WEBPACK_IMPORTED_MODULE_1__.MeasureLayout,\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi9mZWF0dXJlcy9sYXlvdXQubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRjtBQUN2Qjs7QUFFM0Q7QUFDQTtBQUNBLHdCQUF3Qix1RkFBa0I7QUFDMUMscUJBQXFCO0FBQ3JCLEtBQUs7QUFDTDs7QUFFa0IiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcbW90aW9uXFxmZWF0dXJlc1xcbGF5b3V0Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIVE1MUHJvamVjdGlvbk5vZGUgfSBmcm9tICcuLi8uLi9wcm9qZWN0aW9uL25vZGUvSFRNTFByb2plY3Rpb25Ob2RlLm1qcyc7XG5pbXBvcnQgeyBNZWFzdXJlTGF5b3V0IH0gZnJvbSAnLi9sYXlvdXQvTWVhc3VyZUxheW91dC5tanMnO1xuXG5jb25zdCBsYXlvdXQgPSB7XG4gICAgbGF5b3V0OiB7XG4gICAgICAgIFByb2plY3Rpb25Ob2RlOiBIVE1MUHJvamVjdGlvbk5vZGUsXG4gICAgICAgIE1lYXN1cmVMYXlvdXQsXG4gICAgfSxcbn07XG5cbmV4cG9ydCB7IGxheW91dCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MeasureLayout: () => (/* binding */ MeasureLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _components_AnimatePresence_use_presence_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/AnimatePresence/use-presence.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../context/LayoutGroupContext.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../context/SwitchLayoutGroupContext.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs\");\n/* harmony import */ var _projection_node_state_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../projection/node/state.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/node/state.mjs\");\n/* harmony import */ var _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../projection/styles/scale-border-radius.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/styles/scale-border-radius.mjs\");\n/* harmony import */ var _projection_styles_scale_box_shadow_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../projection/styles/scale-box-shadow.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs\");\n/* harmony import */ var _projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../projection/styles/scale-correction.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs\");\n\"use client\";\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Track whether we've taken any snapshots yet. If not,\n * we can safely skip notification of didUpdate.\n */\nlet hasTakenAnySnapshot = false;\nclass MeasureLayoutWithContext extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    /**\n     * This only mounts projection nodes for components that\n     * need measuring, we might want to do it for all components\n     * in order to incorporate transforms\n     */\n    componentDidMount() {\n        const { visualElement, layoutGroup, switchLayoutGroup, layoutId } = this.props;\n        const { projection } = visualElement;\n        (0,_projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_2__.addScaleCorrector)(defaultScaleCorrectors);\n        if (projection) {\n            if (layoutGroup.group)\n                layoutGroup.group.add(projection);\n            if (switchLayoutGroup && switchLayoutGroup.register && layoutId) {\n                switchLayoutGroup.register(projection);\n            }\n            if (hasTakenAnySnapshot) {\n                projection.root.didUpdate();\n            }\n            projection.addEventListener(\"animationComplete\", () => {\n                this.safeToRemove();\n            });\n            projection.setOptions({\n                ...projection.options,\n                onExitComplete: () => this.safeToRemove(),\n            });\n        }\n        _projection_node_state_mjs__WEBPACK_IMPORTED_MODULE_3__.globalProjectionState.hasEverUpdated = true;\n    }\n    getSnapshotBeforeUpdate(prevProps) {\n        const { layoutDependency, visualElement, drag, isPresent } = this.props;\n        const { projection } = visualElement;\n        if (!projection)\n            return null;\n        /**\n         * TODO: We use this data in relegate to determine whether to\n         * promote a previous element. There's no guarantee its presence data\n         * will have updated by this point - if a bug like this arises it will\n         * have to be that we markForRelegation and then find a new lead some other way,\n         * perhaps in didUpdate\n         */\n        projection.isPresent = isPresent;\n        hasTakenAnySnapshot = true;\n        if (drag ||\n            prevProps.layoutDependency !== layoutDependency ||\n            layoutDependency === undefined ||\n            prevProps.isPresent !== isPresent) {\n            projection.willUpdate();\n        }\n        else {\n            this.safeToRemove();\n        }\n        if (prevProps.isPresent !== isPresent) {\n            if (isPresent) {\n                projection.promote();\n            }\n            else if (!projection.relegate()) {\n                /**\n                 * If there's another stack member taking over from this one,\n                 * it's in charge of the exit animation and therefore should\n                 * be in charge of the safe to remove. Otherwise we call it here.\n                 */\n                motion_dom__WEBPACK_IMPORTED_MODULE_4__.frame.postRender(() => {\n                    const stack = projection.getStack();\n                    if (!stack || !stack.members.length) {\n                        this.safeToRemove();\n                    }\n                });\n            }\n        }\n        return null;\n    }\n    componentDidUpdate() {\n        const { projection } = this.props.visualElement;\n        if (projection) {\n            projection.root.didUpdate();\n            motion_dom__WEBPACK_IMPORTED_MODULE_4__.microtask.postRender(() => {\n                if (!projection.currentAnimation && projection.isLead()) {\n                    this.safeToRemove();\n                }\n            });\n        }\n    }\n    componentWillUnmount() {\n        const { visualElement, layoutGroup, switchLayoutGroup: promoteContext, } = this.props;\n        const { projection } = visualElement;\n        if (projection) {\n            projection.scheduleCheckAfterUnmount();\n            if (layoutGroup && layoutGroup.group)\n                layoutGroup.group.remove(projection);\n            if (promoteContext && promoteContext.deregister)\n                promoteContext.deregister(projection);\n        }\n    }\n    safeToRemove() {\n        const { safeToRemove } = this.props;\n        safeToRemove && safeToRemove();\n    }\n    render() {\n        return null;\n    }\n}\nfunction MeasureLayout(props) {\n    const [isPresent, safeToRemove] = (0,_components_AnimatePresence_use_presence_mjs__WEBPACK_IMPORTED_MODULE_5__.usePresence)();\n    const layoutGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__.LayoutGroupContext);\n    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MeasureLayoutWithContext, { ...props, layoutGroup: layoutGroup, switchLayoutGroup: (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_7__.SwitchLayoutGroupContext), isPresent: isPresent, safeToRemove: safeToRemove }));\n}\nconst defaultScaleCorrectors = {\n    borderRadius: {\n        ..._projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__.correctBorderRadius,\n        applyTo: [\n            \"borderTopLeftRadius\",\n            \"borderTopRightRadius\",\n            \"borderBottomLeftRadius\",\n            \"borderBottomRightRadius\",\n        ],\n    },\n    borderTopLeftRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__.correctBorderRadius,\n    borderTopRightRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__.correctBorderRadius,\n    borderBottomLeftRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__.correctBorderRadius,\n    borderBottomRightRadius: _projection_styles_scale_border_radius_mjs__WEBPACK_IMPORTED_MODULE_8__.correctBorderRadius,\n    boxShadow: _projection_styles_scale_box_shadow_mjs__WEBPACK_IMPORTED_MODULE_9__.correctBoxShadow,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi9mZWF0dXJlcy9sYXlvdXQvTWVhc3VyZUxheW91dC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUN3QztBQUNNO0FBQ0E7QUFDcUM7QUFDTjtBQUNZO0FBQ2Q7QUFDYztBQUNOO0FBQ0M7O0FBRXBGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsNENBQVM7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDBEQUEwRDtBQUMxRSxnQkFBZ0IsYUFBYTtBQUM3QixRQUFRLDBGQUFpQjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLFFBQVEsNkVBQXFCO0FBQzdCO0FBQ0E7QUFDQSxnQkFBZ0IsbURBQW1EO0FBQ25FLGdCQUFnQixhQUFhO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiw2Q0FBSztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGFBQWE7QUFDN0I7QUFDQTtBQUNBLFlBQVksaURBQVM7QUFDckI7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixpRUFBaUU7QUFDakYsZ0JBQWdCLGFBQWE7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGVBQWU7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MseUZBQVc7QUFDakQsd0JBQXdCLGlEQUFVLENBQUMsK0VBQWtCO0FBQ3JELFlBQVksc0RBQUcsNkJBQTZCLHVEQUF1RCxpREFBVSxDQUFDLDJGQUF3QixxREFBcUQ7QUFDM0w7QUFDQTtBQUNBO0FBQ0EsV0FBVywyRkFBbUI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLHlCQUF5QiwyRkFBbUI7QUFDNUMsMEJBQTBCLDJGQUFtQjtBQUM3Qyw0QkFBNEIsMkZBQW1CO0FBQy9DLDZCQUE2QiwyRkFBbUI7QUFDaEQsZUFBZSxxRkFBZ0I7QUFDL0I7O0FBRXlCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXG1vdGlvblxcZmVhdHVyZXNcXGxheW91dFxcTWVhc3VyZUxheW91dC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tICdyZWFjdC9qc3gtcnVudGltZSc7XG5pbXBvcnQgeyBmcmFtZSwgbWljcm90YXNrIH0gZnJvbSAnbW90aW9uLWRvbSc7XG5pbXBvcnQgeyB1c2VDb250ZXh0LCBDb21wb25lbnQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VQcmVzZW5jZSB9IGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvQW5pbWF0ZVByZXNlbmNlL3VzZS1wcmVzZW5jZS5tanMnO1xuaW1wb3J0IHsgTGF5b3V0R3JvdXBDb250ZXh0IH0gZnJvbSAnLi4vLi4vLi4vY29udGV4dC9MYXlvdXRHcm91cENvbnRleHQubWpzJztcbmltcG9ydCB7IFN3aXRjaExheW91dEdyb3VwQ29udGV4dCB9IGZyb20gJy4uLy4uLy4uL2NvbnRleHQvU3dpdGNoTGF5b3V0R3JvdXBDb250ZXh0Lm1qcyc7XG5pbXBvcnQgeyBnbG9iYWxQcm9qZWN0aW9uU3RhdGUgfSBmcm9tICcuLi8uLi8uLi9wcm9qZWN0aW9uL25vZGUvc3RhdGUubWpzJztcbmltcG9ydCB7IGNvcnJlY3RCb3JkZXJSYWRpdXMgfSBmcm9tICcuLi8uLi8uLi9wcm9qZWN0aW9uL3N0eWxlcy9zY2FsZS1ib3JkZXItcmFkaXVzLm1qcyc7XG5pbXBvcnQgeyBjb3JyZWN0Qm94U2hhZG93IH0gZnJvbSAnLi4vLi4vLi4vcHJvamVjdGlvbi9zdHlsZXMvc2NhbGUtYm94LXNoYWRvdy5tanMnO1xuaW1wb3J0IHsgYWRkU2NhbGVDb3JyZWN0b3IgfSBmcm9tICcuLi8uLi8uLi9wcm9qZWN0aW9uL3N0eWxlcy9zY2FsZS1jb3JyZWN0aW9uLm1qcyc7XG5cbi8qKlxuICogVHJhY2sgd2hldGhlciB3ZSd2ZSB0YWtlbiBhbnkgc25hcHNob3RzIHlldC4gSWYgbm90LFxuICogd2UgY2FuIHNhZmVseSBza2lwIG5vdGlmaWNhdGlvbiBvZiBkaWRVcGRhdGUuXG4gKi9cbmxldCBoYXNUYWtlbkFueVNuYXBzaG90ID0gZmFsc2U7XG5jbGFzcyBNZWFzdXJlTGF5b3V0V2l0aENvbnRleHQgZXh0ZW5kcyBDb21wb25lbnQge1xuICAgIC8qKlxuICAgICAqIFRoaXMgb25seSBtb3VudHMgcHJvamVjdGlvbiBub2RlcyBmb3IgY29tcG9uZW50cyB0aGF0XG4gICAgICogbmVlZCBtZWFzdXJpbmcsIHdlIG1pZ2h0IHdhbnQgdG8gZG8gaXQgZm9yIGFsbCBjb21wb25lbnRzXG4gICAgICogaW4gb3JkZXIgdG8gaW5jb3Jwb3JhdGUgdHJhbnNmb3Jtc1xuICAgICAqL1xuICAgIGNvbXBvbmVudERpZE1vdW50KCkge1xuICAgICAgICBjb25zdCB7IHZpc3VhbEVsZW1lbnQsIGxheW91dEdyb3VwLCBzd2l0Y2hMYXlvdXRHcm91cCwgbGF5b3V0SWQgfSA9IHRoaXMucHJvcHM7XG4gICAgICAgIGNvbnN0IHsgcHJvamVjdGlvbiB9ID0gdmlzdWFsRWxlbWVudDtcbiAgICAgICAgYWRkU2NhbGVDb3JyZWN0b3IoZGVmYXVsdFNjYWxlQ29ycmVjdG9ycyk7XG4gICAgICAgIGlmIChwcm9qZWN0aW9uKSB7XG4gICAgICAgICAgICBpZiAobGF5b3V0R3JvdXAuZ3JvdXApXG4gICAgICAgICAgICAgICAgbGF5b3V0R3JvdXAuZ3JvdXAuYWRkKHByb2plY3Rpb24pO1xuICAgICAgICAgICAgaWYgKHN3aXRjaExheW91dEdyb3VwICYmIHN3aXRjaExheW91dEdyb3VwLnJlZ2lzdGVyICYmIGxheW91dElkKSB7XG4gICAgICAgICAgICAgICAgc3dpdGNoTGF5b3V0R3JvdXAucmVnaXN0ZXIocHJvamVjdGlvbik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaGFzVGFrZW5BbnlTbmFwc2hvdCkge1xuICAgICAgICAgICAgICAgIHByb2plY3Rpb24ucm9vdC5kaWRVcGRhdGUoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHByb2plY3Rpb24uYWRkRXZlbnRMaXN0ZW5lcihcImFuaW1hdGlvbkNvbXBsZXRlXCIsICgpID0+IHtcbiAgICAgICAgICAgICAgICB0aGlzLnNhZmVUb1JlbW92ZSgpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBwcm9qZWN0aW9uLnNldE9wdGlvbnMoe1xuICAgICAgICAgICAgICAgIC4uLnByb2plY3Rpb24ub3B0aW9ucyxcbiAgICAgICAgICAgICAgICBvbkV4aXRDb21wbGV0ZTogKCkgPT4gdGhpcy5zYWZlVG9SZW1vdmUoKSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGdsb2JhbFByb2plY3Rpb25TdGF0ZS5oYXNFdmVyVXBkYXRlZCA9IHRydWU7XG4gICAgfVxuICAgIGdldFNuYXBzaG90QmVmb3JlVXBkYXRlKHByZXZQcm9wcykge1xuICAgICAgICBjb25zdCB7IGxheW91dERlcGVuZGVuY3ksIHZpc3VhbEVsZW1lbnQsIGRyYWcsIGlzUHJlc2VudCB9ID0gdGhpcy5wcm9wcztcbiAgICAgICAgY29uc3QgeyBwcm9qZWN0aW9uIH0gPSB2aXN1YWxFbGVtZW50O1xuICAgICAgICBpZiAoIXByb2plY3Rpb24pXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIFRPRE86IFdlIHVzZSB0aGlzIGRhdGEgaW4gcmVsZWdhdGUgdG8gZGV0ZXJtaW5lIHdoZXRoZXIgdG9cbiAgICAgICAgICogcHJvbW90ZSBhIHByZXZpb3VzIGVsZW1lbnQuIFRoZXJlJ3Mgbm8gZ3VhcmFudGVlIGl0cyBwcmVzZW5jZSBkYXRhXG4gICAgICAgICAqIHdpbGwgaGF2ZSB1cGRhdGVkIGJ5IHRoaXMgcG9pbnQgLSBpZiBhIGJ1ZyBsaWtlIHRoaXMgYXJpc2VzIGl0IHdpbGxcbiAgICAgICAgICogaGF2ZSB0byBiZSB0aGF0IHdlIG1hcmtGb3JSZWxlZ2F0aW9uIGFuZCB0aGVuIGZpbmQgYSBuZXcgbGVhZCBzb21lIG90aGVyIHdheSxcbiAgICAgICAgICogcGVyaGFwcyBpbiBkaWRVcGRhdGVcbiAgICAgICAgICovXG4gICAgICAgIHByb2plY3Rpb24uaXNQcmVzZW50ID0gaXNQcmVzZW50O1xuICAgICAgICBoYXNUYWtlbkFueVNuYXBzaG90ID0gdHJ1ZTtcbiAgICAgICAgaWYgKGRyYWcgfHxcbiAgICAgICAgICAgIHByZXZQcm9wcy5sYXlvdXREZXBlbmRlbmN5ICE9PSBsYXlvdXREZXBlbmRlbmN5IHx8XG4gICAgICAgICAgICBsYXlvdXREZXBlbmRlbmN5ID09PSB1bmRlZmluZWQgfHxcbiAgICAgICAgICAgIHByZXZQcm9wcy5pc1ByZXNlbnQgIT09IGlzUHJlc2VudCkge1xuICAgICAgICAgICAgcHJvamVjdGlvbi53aWxsVXBkYXRlKCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aGlzLnNhZmVUb1JlbW92ZSgpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwcmV2UHJvcHMuaXNQcmVzZW50ICE9PSBpc1ByZXNlbnQpIHtcbiAgICAgICAgICAgIGlmIChpc1ByZXNlbnQpIHtcbiAgICAgICAgICAgICAgICBwcm9qZWN0aW9uLnByb21vdGUoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKCFwcm9qZWN0aW9uLnJlbGVnYXRlKCkpIHtcbiAgICAgICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAgICAgKiBJZiB0aGVyZSdzIGFub3RoZXIgc3RhY2sgbWVtYmVyIHRha2luZyBvdmVyIGZyb20gdGhpcyBvbmUsXG4gICAgICAgICAgICAgICAgICogaXQncyBpbiBjaGFyZ2Ugb2YgdGhlIGV4aXQgYW5pbWF0aW9uIGFuZCB0aGVyZWZvcmUgc2hvdWxkXG4gICAgICAgICAgICAgICAgICogYmUgaW4gY2hhcmdlIG9mIHRoZSBzYWZlIHRvIHJlbW92ZS4gT3RoZXJ3aXNlIHdlIGNhbGwgaXQgaGVyZS5cbiAgICAgICAgICAgICAgICAgKi9cbiAgICAgICAgICAgICAgICBmcmFtZS5wb3N0UmVuZGVyKCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RhY2sgPSBwcm9qZWN0aW9uLmdldFN0YWNrKCk7XG4gICAgICAgICAgICAgICAgICAgIGlmICghc3RhY2sgfHwgIXN0YWNrLm1lbWJlcnMubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNhZmVUb1JlbW92ZSgpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGNvbXBvbmVudERpZFVwZGF0ZSgpIHtcbiAgICAgICAgY29uc3QgeyBwcm9qZWN0aW9uIH0gPSB0aGlzLnByb3BzLnZpc3VhbEVsZW1lbnQ7XG4gICAgICAgIGlmIChwcm9qZWN0aW9uKSB7XG4gICAgICAgICAgICBwcm9qZWN0aW9uLnJvb3QuZGlkVXBkYXRlKCk7XG4gICAgICAgICAgICBtaWNyb3Rhc2sucG9zdFJlbmRlcigoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKCFwcm9qZWN0aW9uLmN1cnJlbnRBbmltYXRpb24gJiYgcHJvamVjdGlvbi5pc0xlYWQoKSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLnNhZmVUb1JlbW92ZSgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgfVxuICAgIGNvbXBvbmVudFdpbGxVbm1vdW50KCkge1xuICAgICAgICBjb25zdCB7IHZpc3VhbEVsZW1lbnQsIGxheW91dEdyb3VwLCBzd2l0Y2hMYXlvdXRHcm91cDogcHJvbW90ZUNvbnRleHQsIH0gPSB0aGlzLnByb3BzO1xuICAgICAgICBjb25zdCB7IHByb2plY3Rpb24gfSA9IHZpc3VhbEVsZW1lbnQ7XG4gICAgICAgIGlmIChwcm9qZWN0aW9uKSB7XG4gICAgICAgICAgICBwcm9qZWN0aW9uLnNjaGVkdWxlQ2hlY2tBZnRlclVubW91bnQoKTtcbiAgICAgICAgICAgIGlmIChsYXlvdXRHcm91cCAmJiBsYXlvdXRHcm91cC5ncm91cClcbiAgICAgICAgICAgICAgICBsYXlvdXRHcm91cC5ncm91cC5yZW1vdmUocHJvamVjdGlvbik7XG4gICAgICAgICAgICBpZiAocHJvbW90ZUNvbnRleHQgJiYgcHJvbW90ZUNvbnRleHQuZGVyZWdpc3RlcilcbiAgICAgICAgICAgICAgICBwcm9tb3RlQ29udGV4dC5kZXJlZ2lzdGVyKHByb2plY3Rpb24pO1xuICAgICAgICB9XG4gICAgfVxuICAgIHNhZmVUb1JlbW92ZSgpIHtcbiAgICAgICAgY29uc3QgeyBzYWZlVG9SZW1vdmUgfSA9IHRoaXMucHJvcHM7XG4gICAgICAgIHNhZmVUb1JlbW92ZSAmJiBzYWZlVG9SZW1vdmUoKTtcbiAgICB9XG4gICAgcmVuZGVyKCkge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG59XG5mdW5jdGlvbiBNZWFzdXJlTGF5b3V0KHByb3BzKSB7XG4gICAgY29uc3QgW2lzUHJlc2VudCwgc2FmZVRvUmVtb3ZlXSA9IHVzZVByZXNlbmNlKCk7XG4gICAgY29uc3QgbGF5b3V0R3JvdXAgPSB1c2VDb250ZXh0KExheW91dEdyb3VwQ29udGV4dCk7XG4gICAgcmV0dXJuIChqc3goTWVhc3VyZUxheW91dFdpdGhDb250ZXh0LCB7IC4uLnByb3BzLCBsYXlvdXRHcm91cDogbGF5b3V0R3JvdXAsIHN3aXRjaExheW91dEdyb3VwOiB1c2VDb250ZXh0KFN3aXRjaExheW91dEdyb3VwQ29udGV4dCksIGlzUHJlc2VudDogaXNQcmVzZW50LCBzYWZlVG9SZW1vdmU6IHNhZmVUb1JlbW92ZSB9KSk7XG59XG5jb25zdCBkZWZhdWx0U2NhbGVDb3JyZWN0b3JzID0ge1xuICAgIGJvcmRlclJhZGl1czoge1xuICAgICAgICAuLi5jb3JyZWN0Qm9yZGVyUmFkaXVzLFxuICAgICAgICBhcHBseVRvOiBbXG4gICAgICAgICAgICBcImJvcmRlclRvcExlZnRSYWRpdXNcIixcbiAgICAgICAgICAgIFwiYm9yZGVyVG9wUmlnaHRSYWRpdXNcIixcbiAgICAgICAgICAgIFwiYm9yZGVyQm90dG9tTGVmdFJhZGl1c1wiLFxuICAgICAgICAgICAgXCJib3JkZXJCb3R0b21SaWdodFJhZGl1c1wiLFxuICAgICAgICBdLFxuICAgIH0sXG4gICAgYm9yZGVyVG9wTGVmdFJhZGl1czogY29ycmVjdEJvcmRlclJhZGl1cyxcbiAgICBib3JkZXJUb3BSaWdodFJhZGl1czogY29ycmVjdEJvcmRlclJhZGl1cyxcbiAgICBib3JkZXJCb3R0b21MZWZ0UmFkaXVzOiBjb3JyZWN0Qm9yZGVyUmFkaXVzLFxuICAgIGJvcmRlckJvdHRvbVJpZ2h0UmFkaXVzOiBjb3JyZWN0Qm9yZGVyUmFkaXVzLFxuICAgIGJveFNoYWRvdzogY29ycmVjdEJveFNoYWRvdyxcbn07XG5cbmV4cG9ydCB7IE1lYXN1cmVMYXlvdXQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/load-features.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/load-features.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadFeatures: () => (/* binding */ loadFeatures)\n/* harmony export */ });\n/* harmony import */ var _definitions_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./definitions.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/definitions.mjs\");\n\n\nfunction loadFeatures(features) {\n    for (const key in features) {\n        _definitions_mjs__WEBPACK_IMPORTED_MODULE_0__.featureDefinitions[key] = {\n            ..._definitions_mjs__WEBPACK_IMPORTED_MODULE_0__.featureDefinitions[key],\n            ...features[key],\n        };\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi9mZWF0dXJlcy9sb2FkLWZlYXR1cmVzLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1RDs7QUFFdkQ7QUFDQTtBQUNBLFFBQVEsZ0VBQWtCO0FBQzFCLGVBQWUsZ0VBQWtCO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxtb3Rpb25cXGZlYXR1cmVzXFxsb2FkLWZlYXR1cmVzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmZWF0dXJlRGVmaW5pdGlvbnMgfSBmcm9tICcuL2RlZmluaXRpb25zLm1qcyc7XG5cbmZ1bmN0aW9uIGxvYWRGZWF0dXJlcyhmZWF0dXJlcykge1xuICAgIGZvciAoY29uc3Qga2V5IGluIGZlYXR1cmVzKSB7XG4gICAgICAgIGZlYXR1cmVEZWZpbml0aW9uc1trZXldID0ge1xuICAgICAgICAgICAgLi4uZmVhdHVyZURlZmluaXRpb25zW2tleV0sXG4gICAgICAgICAgICAuLi5mZWF0dXJlc1trZXldLFxuICAgICAgICB9O1xuICAgIH1cbn1cblxuZXhwb3J0IHsgbG9hZEZlYXR1cmVzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/load-features.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InViewFeature: () => (/* binding */ InViewFeature)\n/* harmony export */ });\n/* harmony import */ var _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Feature.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/Feature.mjs\");\n/* harmony import */ var _observers_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./observers.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs\");\n\n\n\nconst thresholdNames = {\n    some: 0,\n    all: 1,\n};\nclass InViewFeature extends _Feature_mjs__WEBPACK_IMPORTED_MODULE_0__.Feature {\n    constructor() {\n        super(...arguments);\n        this.hasEnteredView = false;\n        this.isInView = false;\n    }\n    startObserver() {\n        this.unmount();\n        const { viewport = {} } = this.node.getProps();\n        const { root, margin: rootMargin, amount = \"some\", once } = viewport;\n        const options = {\n            root: root ? root.current : undefined,\n            rootMargin,\n            threshold: typeof amount === \"number\" ? amount : thresholdNames[amount],\n        };\n        const onIntersectionUpdate = (entry) => {\n            const { isIntersecting } = entry;\n            /**\n             * If there's been no change in the viewport state, early return.\n             */\n            if (this.isInView === isIntersecting)\n                return;\n            this.isInView = isIntersecting;\n            /**\n             * Handle hasEnteredView. If this is only meant to run once, and\n             * element isn't visible, early return. Otherwise set hasEnteredView to true.\n             */\n            if (once && !isIntersecting && this.hasEnteredView) {\n                return;\n            }\n            else if (isIntersecting) {\n                this.hasEnteredView = true;\n            }\n            if (this.node.animationState) {\n                this.node.animationState.setActive(\"whileInView\", isIntersecting);\n            }\n            /**\n             * Use the latest committed props rather than the ones in scope\n             * when this observer is created\n             */\n            const { onViewportEnter, onViewportLeave } = this.node.getProps();\n            const callback = isIntersecting ? onViewportEnter : onViewportLeave;\n            callback && callback(entry);\n        };\n        return (0,_observers_mjs__WEBPACK_IMPORTED_MODULE_1__.observeIntersection)(this.node.current, options, onIntersectionUpdate);\n    }\n    mount() {\n        this.startObserver();\n    }\n    update() {\n        if (typeof IntersectionObserver === \"undefined\")\n            return;\n        const { props, prevProps } = this.node;\n        const hasOptionsChanged = [\"amount\", \"margin\", \"root\"].some(hasViewportOptionChanged(props, prevProps));\n        if (hasOptionsChanged) {\n            this.startObserver();\n        }\n    }\n    unmount() { }\n}\nfunction hasViewportOptionChanged({ viewport = {} }, { viewport: prevViewport = {} } = {}) {\n    return (name) => viewport[name] !== prevViewport[name];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   observeIntersection: () => (/* binding */ observeIntersection)\n/* harmony export */ });\n/**\n * Map an IntersectionHandler callback to an element. We only ever make one handler for one\n * element, so even though these handlers might all be triggered by different\n * observers, we can keep them in the same map.\n */\nconst observerCallbacks = new WeakMap();\n/**\n * Multiple observers can be created for multiple element/document roots. Each with\n * different settings. So here we store dictionaries of observers to each root,\n * using serialised settings (threshold/margin) as lookup keys.\n */\nconst observers = new WeakMap();\nconst fireObserverCallback = (entry) => {\n    const callback = observerCallbacks.get(entry.target);\n    callback && callback(entry);\n};\nconst fireAllObserverCallbacks = (entries) => {\n    entries.forEach(fireObserverCallback);\n};\nfunction initIntersectionObserver({ root, ...options }) {\n    const lookupRoot = root || document;\n    /**\n     * If we don't have an observer lookup map for this root, create one.\n     */\n    if (!observers.has(lookupRoot)) {\n        observers.set(lookupRoot, {});\n    }\n    const rootObservers = observers.get(lookupRoot);\n    const key = JSON.stringify(options);\n    /**\n     * If we don't have an observer for this combination of root and settings,\n     * create one.\n     */\n    if (!rootObservers[key]) {\n        rootObservers[key] = new IntersectionObserver(fireAllObserverCallbacks, { root, ...options });\n    }\n    return rootObservers[key];\n}\nfunction observeIntersection(element, options, callback) {\n    const rootInteresectionObserver = initIntersectionObserver(options);\n    observerCallbacks.set(element, callback);\n    rootInteresectionObserver.observe(element);\n    return () => {\n        observerCallbacks.delete(element);\n        rootInteresectionObserver.unobserve(element);\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRendererMotionComponent: () => (/* binding */ createRendererMotionComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"react/jsx-runtime\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! motion-utils */ \"../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/LayoutGroupContext.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../context/LazyContext.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/LazyContext.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/MotionContext/index.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/MotionContext/create.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionContext/create.mjs\");\n/* harmony import */ var _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/is-browser.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/is-browser.mjs\");\n/* harmony import */ var _features_definitions_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./features/definitions.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/definitions.mjs\");\n/* harmony import */ var _features_load_features_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./features/load-features.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/features/load-features.mjs\");\n/* harmony import */ var _utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/symbol.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\");\n/* harmony import */ var _utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/use-motion-ref.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs\");\n/* harmony import */ var _utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/use-visual-element.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs\");\n\"use client\";\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */\nfunction createRendererMotionComponent({ preloadedFeatures, createVisualElement, useRender, useVisualState, Component, }) {\n    preloadedFeatures && (0,_features_load_features_mjs__WEBPACK_IMPORTED_MODULE_2__.loadFeatures)(preloadedFeatures);\n    function MotionComponent(props, externalRef) {\n        /**\n         * If we need to measure the element we load this functionality in a\n         * separate class component in order to gain access to getSnapshotBeforeUpdate.\n         */\n        let MeasureLayout;\n        const configAndProps = {\n            ...(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext),\n            ...props,\n            layoutId: useLayoutId(props),\n        };\n        const { isStatic } = configAndProps;\n        const context = (0,_context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__.useCreateMotionContext)(props);\n        const visualState = useVisualState(props, isStatic);\n        if (!isStatic && _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_5__.isBrowser) {\n            useStrictMode(configAndProps, preloadedFeatures);\n            const layoutProjection = getProjectionFunctionality(configAndProps);\n            MeasureLayout = layoutProjection.MeasureLayout;\n            /**\n             * Create a VisualElement for this component. A VisualElement provides a common\n             * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n             * providing a way of rendering to these APIs outside of the React render loop\n             * for more performant animations and interactions\n             */\n            context.visualElement = (0,_utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__.useVisualElement)(Component, visualState, configAndProps, createVisualElement, layoutProjection.ProjectionNode);\n        }\n        /**\n         * The mount order and hierarchy is specific to ensure our element ref\n         * is hydrated by the time features fire their effects.\n         */\n        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_7__.MotionContext.Provider, { value: context, children: [MeasureLayout && context.visualElement ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MeasureLayout, { visualElement: context.visualElement, ...configAndProps })) : null, useRender(Component, props, (0,_utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__.useMotionRef)(visualState, context.visualElement, externalRef), visualState, isStatic, context.visualElement)] }));\n    }\n    MotionComponent.displayName = `motion.${typeof Component === \"string\"\n        ? Component\n        : `create(${Component.displayName ?? Component.name ?? \"\"})`}`;\n    const ForwardRefMotionComponent = (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(MotionComponent);\n    ForwardRefMotionComponent[_utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_9__.motionComponentSymbol] = Component;\n    return ForwardRefMotionComponent;\n}\nfunction useLayoutId({ layoutId }) {\n    const layoutGroupId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_10__.LayoutGroupContext).id;\n    return layoutGroupId && layoutId !== undefined\n        ? layoutGroupId + \"-\" + layoutId\n        : layoutId;\n}\nfunction useStrictMode(configAndProps, preloadedFeatures) {\n    const isStrict = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_11__.LazyContext).strict;\n    /**\n     * If we're in development mode, check to make sure we're not rendering a motion component\n     * as a child of LazyMotion, as this will break the file-size benefits of using it.\n     */\n    if ( true &&\n        preloadedFeatures &&\n        isStrict) {\n        const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n        configAndProps.ignoreStrict\n            ? (0,motion_utils__WEBPACK_IMPORTED_MODULE_12__.warning)(false, strictMessage)\n            : (0,motion_utils__WEBPACK_IMPORTED_MODULE_12__.invariant)(false, strictMessage);\n    }\n}\nfunction getProjectionFunctionality(props) {\n    const { drag, layout } = _features_definitions_mjs__WEBPACK_IMPORTED_MODULE_13__.featureDefinitions;\n    if (!drag && !layout)\n        return {};\n    const combined = { ...drag, ...layout };\n    return {\n        MeasureLayout: drag?.isEnabled(props) || layout?.isEnabled(props)\n            ? combined.MeasureLayout\n            : undefined,\n        ProjectionNode: combined.ProjectionNode,\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/index.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isForcedMotionValue: () => (/* binding */ isForcedMotionValue)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../projection/styles/scale-correction.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs\");\n\n\n\nfunction isForcedMotionValue(key, { layout, layoutId }) {\n    return (motion_dom__WEBPACK_IMPORTED_MODULE_0__.transformProps.has(key) ||\n        key.startsWith(\"origin\") ||\n        ((layout || layoutId !== undefined) &&\n            (!!_projection_styles_scale_correction_mjs__WEBPACK_IMPORTED_MODULE_1__.scaleCorrectors[key] || key === \"opacity\")));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi91dGlscy9pcy1mb3JjZWQtbW90aW9uLXZhbHVlLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEM7QUFDbUM7O0FBRS9FLG9DQUFvQyxrQkFBa0I7QUFDdEQsWUFBWSxzREFBYztBQUMxQjtBQUNBO0FBQ0EsZUFBZSxvRkFBZTtBQUM5Qjs7QUFFK0IiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcbW90aW9uXFx1dGlsc1xcaXMtZm9yY2VkLW1vdGlvbi12YWx1ZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHJhbnNmb3JtUHJvcHMgfSBmcm9tICdtb3Rpb24tZG9tJztcbmltcG9ydCB7IHNjYWxlQ29ycmVjdG9ycyB9IGZyb20gJy4uLy4uL3Byb2plY3Rpb24vc3R5bGVzL3NjYWxlLWNvcnJlY3Rpb24ubWpzJztcblxuZnVuY3Rpb24gaXNGb3JjZWRNb3Rpb25WYWx1ZShrZXksIHsgbGF5b3V0LCBsYXlvdXRJZCB9KSB7XG4gICAgcmV0dXJuICh0cmFuc2Zvcm1Qcm9wcy5oYXMoa2V5KSB8fFxuICAgICAgICBrZXkuc3RhcnRzV2l0aChcIm9yaWdpblwiKSB8fFxuICAgICAgICAoKGxheW91dCB8fCBsYXlvdXRJZCAhPT0gdW5kZWZpbmVkKSAmJlxuICAgICAgICAgICAgKCEhc2NhbGVDb3JyZWN0b3JzW2tleV0gfHwga2V5ID09PSBcIm9wYWNpdHlcIikpKTtcbn1cblxuZXhwb3J0IHsgaXNGb3JjZWRNb3Rpb25WYWx1ZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-motion-component.mjs":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-motion-component.mjs ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isMotionComponent: () => (/* binding */ isMotionComponent)\n/* harmony export */ });\n/* harmony import */ var _symbol_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./symbol.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\");\n\n\n/**\n * Checks if a component is a `motion` component.\n */\nfunction isMotionComponent(component) {\n    return (component !== null &&\n        typeof component === \"object\" &&\n        _symbol_mjs__WEBPACK_IMPORTED_MODULE_0__.motionComponentSymbol in component);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi91dGlscy9pcy1tb3Rpb24tY29tcG9uZW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDs7QUFFckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4REFBcUI7QUFDN0I7O0FBRTZCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXG1vdGlvblxcdXRpbHNcXGlzLW1vdGlvbi1jb21wb25lbnQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1vdGlvbkNvbXBvbmVudFN5bWJvbCB9IGZyb20gJy4vc3ltYm9sLm1qcyc7XG5cbi8qKlxuICogQ2hlY2tzIGlmIGEgY29tcG9uZW50IGlzIGEgYG1vdGlvbmAgY29tcG9uZW50LlxuICovXG5mdW5jdGlvbiBpc01vdGlvbkNvbXBvbmVudChjb21wb25lbnQpIHtcbiAgICByZXR1cm4gKGNvbXBvbmVudCAhPT0gbnVsbCAmJlxuICAgICAgICB0eXBlb2YgY29tcG9uZW50ID09PSBcIm9iamVjdFwiICYmXG4gICAgICAgIG1vdGlvbkNvbXBvbmVudFN5bWJvbCBpbiBjb21wb25lbnQpO1xufVxuXG5leHBvcnQgeyBpc01vdGlvbkNvbXBvbmVudCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-motion-component.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs":
/*!********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   motionComponentSymbol: () => (/* binding */ motionComponentSymbol)\n/* harmony export */ });\nconst motionComponentSymbol = Symbol.for(\"motionComponentSymbol\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi91dGlscy9zeW1ib2wubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFaUMiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcbW90aW9uXFx1dGlsc1xcc3ltYm9sLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb3Rpb25Db21wb25lbnRTeW1ib2wgPSBTeW1ib2wuZm9yKFwibW90aW9uQ29tcG9uZW50U3ltYm9sXCIpO1xuXG5leHBvcnQgeyBtb3Rpb25Db21wb25lbnRTeW1ib2wgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/unwrap-motion-component.mjs":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/unwrap-motion-component.mjs ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unwrapMotionComponent: () => (/* binding */ unwrapMotionComponent)\n/* harmony export */ });\n/* harmony import */ var _is_motion_component_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-motion-component.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/is-motion-component.mjs\");\n/* harmony import */ var _symbol_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./symbol.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\");\n\n\n\n/**\n * Unwraps a `motion` component and returns either a string for `motion.div` or\n * the React component for `motion(Component)`.\n *\n * If the component is not a `motion` component it returns undefined.\n */\nfunction unwrapMotionComponent(component) {\n    if ((0,_is_motion_component_mjs__WEBPACK_IMPORTED_MODULE_0__.isMotionComponent)(component)) {\n        return component[_symbol_mjs__WEBPACK_IMPORTED_MODULE_1__.motionComponentSymbol];\n    }\n    return undefined;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL21vdGlvbi91dGlscy91bndyYXAtbW90aW9uLWNvbXBvbmVudC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThEO0FBQ1Q7O0FBRXJEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSwyRUFBaUI7QUFDekIseUJBQXlCLDhEQUFxQjtBQUM5QztBQUNBO0FBQ0E7O0FBRWlDIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXG1vdGlvblxcdXRpbHNcXHVud3JhcC1tb3Rpb24tY29tcG9uZW50Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc01vdGlvbkNvbXBvbmVudCB9IGZyb20gJy4vaXMtbW90aW9uLWNvbXBvbmVudC5tanMnO1xuaW1wb3J0IHsgbW90aW9uQ29tcG9uZW50U3ltYm9sIH0gZnJvbSAnLi9zeW1ib2wubWpzJztcblxuLyoqXG4gKiBVbndyYXBzIGEgYG1vdGlvbmAgY29tcG9uZW50IGFuZCByZXR1cm5zIGVpdGhlciBhIHN0cmluZyBmb3IgYG1vdGlvbi5kaXZgIG9yXG4gKiB0aGUgUmVhY3QgY29tcG9uZW50IGZvciBgbW90aW9uKENvbXBvbmVudClgLlxuICpcbiAqIElmIHRoZSBjb21wb25lbnQgaXMgbm90IGEgYG1vdGlvbmAgY29tcG9uZW50IGl0IHJldHVybnMgdW5kZWZpbmVkLlxuICovXG5mdW5jdGlvbiB1bndyYXBNb3Rpb25Db21wb25lbnQoY29tcG9uZW50KSB7XG4gICAgaWYgKGlzTW90aW9uQ29tcG9uZW50KGNvbXBvbmVudCkpIHtcbiAgICAgICAgcmV0dXJuIGNvbXBvbmVudFttb3Rpb25Db21wb25lbnRTeW1ib2xdO1xuICAgIH1cbiAgICByZXR1cm4gdW5kZWZpbmVkO1xufVxuXG5leHBvcnQgeyB1bndyYXBNb3Rpb25Db21wb25lbnQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/unwrap-motion-component.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionRef: () => (/* binding */ useMotionRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/is-ref-object.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/is-ref-object.mjs\");\n\n\n\n/**\n * Creates a ref function that, when called, hydrates the provided\n * external ref and VisualElement.\n */\nfunction useMotionRef(visualState, visualElement, externalRef) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((instance) => {\n        if (instance) {\n            visualState.onMount && visualState.onMount(instance);\n        }\n        if (visualElement) {\n            if (instance) {\n                visualElement.mount(instance);\n            }\n            else {\n                visualElement.unmount();\n            }\n        }\n        if (externalRef) {\n            if (typeof externalRef === \"function\") {\n                externalRef(instance);\n            }\n            else if ((0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_1__.isRefObject)(externalRef)) {\n                externalRef.current = instance;\n            }\n        }\n    }, \n    /**\n     * Only pass a new ref callback to React if we've received a visual element\n     * factory. Otherwise we'll be mounting/remounting every time externalRef\n     * or other dependencies change.\n     */\n    [visualElement]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useVisualElement: () => (/* binding */ useVisualElement)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _animation_optimized_appear_data_id_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../animation/optimized-appear/data-id.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs\");\n/* harmony import */ var _context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/LazyContext.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/LazyContext.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../context/MotionConfigContext.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../context/MotionContext/index.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../context/SwitchLayoutGroupContext.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs\");\n/* harmony import */ var _utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../utils/is-ref-object.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/is-ref-object.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/use-isomorphic-effect.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n\n\n\n\n\n\n\n\n\n\n\nfunction useVisualElement(Component, visualState, props, createVisualElement, ProjectionNodeConstructor) {\n    const { visualElement: parent } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionContext);\n    const lazyContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_2__.LazyContext);\n    const presenceContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__.PresenceContext);\n    const reducedMotionConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_4__.MotionConfigContext).reducedMotion;\n    const visualElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    /**\n     * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n     */\n    createVisualElement = createVisualElement || lazyContext.renderer;\n    if (!visualElementRef.current && createVisualElement) {\n        visualElementRef.current = createVisualElement(Component, {\n            visualState,\n            parent,\n            props,\n            presenceContext,\n            blockInitialAnimation: presenceContext\n                ? presenceContext.initial === false\n                : false,\n            reducedMotionConfig,\n        });\n    }\n    const visualElement = visualElementRef.current;\n    /**\n     * Load Motion gesture and animation features. These are rendered as renderless\n     * components so each feature can optionally make use of React lifecycle methods.\n     */\n    const initialLayoutGroupConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_SwitchLayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_5__.SwitchLayoutGroupContext);\n    if (visualElement &&\n        !visualElement.projection &&\n        ProjectionNodeConstructor &&\n        (visualElement.type === \"html\" || visualElement.type === \"svg\")) {\n        createProjectionNode(visualElementRef.current, props, ProjectionNodeConstructor, initialLayoutGroupConfig);\n    }\n    const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useInsertionEffect)(() => {\n        /**\n         * Check the component has already mounted before calling\n         * `update` unnecessarily. This ensures we skip the initial update.\n         */\n        if (visualElement && isMounted.current) {\n            visualElement.update(props, presenceContext);\n        }\n    });\n    /**\n     * Cache this value as we want to know whether HandoffAppearAnimations\n     * was present on initial render - it will be deleted after this.\n     */\n    const optimisedAppearId = props[_animation_optimized_appear_data_id_mjs__WEBPACK_IMPORTED_MODULE_6__.optimizedAppearDataAttribute];\n    const wantsHandoff = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Boolean(optimisedAppearId) &&\n        !window.MotionHandoffIsComplete?.(optimisedAppearId) &&\n        window.MotionHasOptimisedAnimation?.(optimisedAppearId));\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_7__.useIsomorphicLayoutEffect)(() => {\n        if (!visualElement)\n            return;\n        isMounted.current = true;\n        window.MotionIsMounted = true;\n        visualElement.updateFeatures();\n        motion_dom__WEBPACK_IMPORTED_MODULE_8__.microtask.render(visualElement.render);\n        /**\n         * Ideally this function would always run in a useEffect.\n         *\n         * However, if we have optimised appear animations to handoff from,\n         * it needs to happen synchronously to ensure there's no flash of\n         * incorrect styles in the event of a hydration error.\n         *\n         * So if we detect a situtation where optimised appear animations\n         * are running, we use useLayoutEffect to trigger animations.\n         */\n        if (wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!visualElement)\n            return;\n        if (!wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n        if (wantsHandoff.current) {\n            // This ensures all future calls to animateChanges() in this component will run in useEffect\n            queueMicrotask(() => {\n                window.MotionHandoffMarkAsComplete?.(optimisedAppearId);\n            });\n            wantsHandoff.current = false;\n        }\n    });\n    return visualElement;\n}\nfunction createProjectionNode(visualElement, props, ProjectionNodeConstructor, initialPromotionConfig) {\n    const { layoutId, layout, drag, dragConstraints, layoutScroll, layoutRoot, layoutCrossfade, } = props;\n    visualElement.projection = new ProjectionNodeConstructor(visualElement.latestValues, props[\"data-framer-portal-id\"]\n        ? undefined\n        : getClosestProjectingNode(visualElement.parent));\n    visualElement.projection.setOptions({\n        layoutId,\n        layout,\n        alwaysMeasureLayout: Boolean(drag) || (dragConstraints && (0,_utils_is_ref_object_mjs__WEBPACK_IMPORTED_MODULE_9__.isRefObject)(dragConstraints)),\n        visualElement,\n        /**\n         * TODO: Update options in an effect. This could be tricky as it'll be too late\n         * to update by the time layout animations run.\n         * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n         * ensuring it gets called if there's no potential layout animations.\n         *\n         */\n        animationType: typeof layout === \"string\" ? layout : \"both\",\n        initialPromotionConfig,\n        crossfade: layoutCrossfade,\n        layoutScroll,\n        layoutRoot,\n    });\n}\nfunction getClosestProjectingNode(visualElement) {\n    if (!visualElement)\n        return undefined;\n    return visualElement.options.allowProjection !== false\n        ? visualElement.projection\n        : getClosestProjectingNode(visualElement.parent);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeUseVisualState: () => (/* binding */ makeUseVisualState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var _animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../animation/utils/is-animation-controls.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../context/MotionContext/index.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _render_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../render/utils/is-controlling-variants.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs\");\n/* harmony import */ var _render_utils_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../render/utils/resolve-variants.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _value_utils_resolve_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../value/utils/resolve-motion-value.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs\");\n\n\n\n\n\n\n\n\n\nfunction makeState({ scrapeMotionValuesFromProps, createRenderState, }, props, context, presenceContext) {\n    const state = {\n        latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n        renderState: createRenderState(),\n    };\n    return state;\n}\nconst makeUseVisualState = (config) => (props, isStatic) => {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionContext);\n    const presenceContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_2__.PresenceContext);\n    const make = () => makeState(config, props, context, presenceContext);\n    return isStatic ? make() : (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__.useConstant)(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n    const values = {};\n    const motionValues = scrapeMotionValues(props, {});\n    for (const key in motionValues) {\n        values[key] = (0,_value_utils_resolve_motion_value_mjs__WEBPACK_IMPORTED_MODULE_4__.resolveMotionValue)(motionValues[key]);\n    }\n    let { initial, animate } = props;\n    const isControllingVariants$1 = (0,_render_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_5__.isControllingVariants)(props);\n    const isVariantNode$1 = (0,_render_utils_is_controlling_variants_mjs__WEBPACK_IMPORTED_MODULE_5__.isVariantNode)(props);\n    if (context &&\n        isVariantNode$1 &&\n        !isControllingVariants$1 &&\n        props.inherit !== false) {\n        if (initial === undefined)\n            initial = context.initial;\n        if (animate === undefined)\n            animate = context.animate;\n    }\n    let isInitialAnimationBlocked = presenceContext\n        ? presenceContext.initial === false\n        : false;\n    isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n    const variantToSet = isInitialAnimationBlocked ? animate : initial;\n    if (variantToSet &&\n        typeof variantToSet !== \"boolean\" &&\n        !(0,_animation_utils_is_animation_controls_mjs__WEBPACK_IMPORTED_MODULE_6__.isAnimationControls)(variantToSet)) {\n        const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n        for (let i = 0; i < list.length; i++) {\n            const resolved = (0,_render_utils_resolve_variants_mjs__WEBPACK_IMPORTED_MODULE_7__.resolveVariantFromProps)(props, list[i]);\n            if (resolved) {\n                const { transitionEnd, transition, ...target } = resolved;\n                for (const key in target) {\n                    let valueTarget = target[key];\n                    if (Array.isArray(valueTarget)) {\n                        /**\n                         * Take final keyframe if the initial animation is blocked because\n                         * we want to initialise at the end of that blocked animation.\n                         */\n                        const index = isInitialAnimationBlocked\n                            ? valueTarget.length - 1\n                            : 0;\n                        valueTarget = valueTarget[index];\n                    }\n                    if (valueTarget !== null) {\n                        values[key] = valueTarget;\n                    }\n                }\n                for (const key in transitionEnd) {\n                    values[key] = transitionEnd[key];\n                }\n            }\n        }\n    }\n    return values;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isValidMotionProp: () => (/* binding */ isValidMotionProp)\n/* harmony export */ });\n/**\n * A list of all valid MotionProps.\n *\n * @privateRemarks\n * This doesn't throw if a `MotionProp` name is missing - it should.\n */\nconst validMotionProps = new Set([\n    \"animate\",\n    \"exit\",\n    \"variants\",\n    \"initial\",\n    \"style\",\n    \"values\",\n    \"variants\",\n    \"transition\",\n    \"transformTemplate\",\n    \"custom\",\n    \"inherit\",\n    \"onBeforeLayoutMeasure\",\n    \"onAnimationStart\",\n    \"onAnimationComplete\",\n    \"onUpdate\",\n    \"onDragStart\",\n    \"onDrag\",\n    \"onDragEnd\",\n    \"onMeasureDragConstraints\",\n    \"onDirectionLock\",\n    \"onDragTransitionEnd\",\n    \"_dragX\",\n    \"_dragY\",\n    \"onHoverStart\",\n    \"onHoverEnd\",\n    \"onViewportEnter\",\n    \"onViewportLeave\",\n    \"globalTapTarget\",\n    \"ignoreStrict\",\n    \"viewport\",\n]);\n/**\n * Check whether a prop name is a valid `MotionProp` key.\n *\n * @param key - Name of the property to check\n * @returns `true` is key is a valid `MotionProp`.\n *\n * @public\n */\nfunction isValidMotionProp(key) {\n    return (key.startsWith(\"while\") ||\n        (key.startsWith(\"drag\") && key !== \"draggable\") ||\n        key.startsWith(\"layout\") ||\n        key.startsWith(\"onTap\") ||\n        key.startsWith(\"onPan\") ||\n        key.startsWith(\"onLayout\") ||\n        validMotionProps.has(key));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs\n");

/***/ })

};
;