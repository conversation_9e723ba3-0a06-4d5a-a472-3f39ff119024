"use strict";exports.id=523,exports.ids=[523],exports.modules={8193:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return p},defaultHead:function(){return c}});let r=n(2403),u=n(12742),o=n(8732),i=u._(n(82015)),a=r._(n(5416)),s=n(93118),f=n(75100),l=n(76665);function c(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function _(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===i.default.Fragment?e.concat(i.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(5817);let E=["name","httpEquiv","charSet","itemProp"];function d(e,t){let{inAmpMode:n}=t;return e.reduce(_,[]).reverse().concat(c(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,r={};return u=>{let o=!0,i=!1;if(u.key&&"number"!=typeof u.key&&u.key.indexOf("$")>0){i=!0;let t=u.key.slice(u.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(u.type){case"title":case"base":t.has(u.type)?o=!1:t.add(u.type);break;case"meta":for(let e=0,t=E.length;e<t;e++){let t=E[e];if(u.props.hasOwnProperty(t))if("charSet"===t)n.has(t)?o=!1:n.add(t);else{let e=u.props[t],n=r[t]||new Set;("name"!==t||!i)&&n.has(e)?o=!1:(n.add(e),r[t]=n)}}}return o}}()).reverse().map((e,t)=>{let r=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,i.default.cloneElement(e,t)}return i.default.cloneElement(e,{key:r})})}let p=function(e){let{children:t}=e,n=(0,i.useContext)(s.AmpStateContext),r=(0,i.useContext)(f.HeadManagerContext);return(0,o.jsx)(a.default,{reduceComponentsToState:d,headManager:r,inAmpMode:(0,l.isInAmpMode)(n),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19708:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return u}});let n=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function u(e){return n.test(e)?e.replace(r,"\\$&"):e}},34977:(e,t)=>{function n(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isThenable",{enumerable:!0,get:function(){return n}})},45872:(e,t)=>{function n(e){return Object.prototype.toString.call(e)}function r(e){if("[object Object]"!==n(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getObjectClassLabel:function(){return n},isPlainObject:function(){return r}})},49346:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return r}});let n=new WeakMap;function r(e,t){let r;if(!t)return{pathname:e};let u=n.get(t);u||(u=t.map(e=>e.toLowerCase()),n.set(t,u));let o=e.split("/",2);if(!o[1])return{pathname:e};let i=o[1].toLowerCase(),a=u.indexOf(i);return a<0?{pathname:e}:(r=t[a],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}},54440:(e,t)=>{function n(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return n}})},72075:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BloomFilter",{enumerable:!0,get:function(){return r}});class r{static from(e,t){void 0===t&&(t=1e-4);let n=new r(e.length,t);for(let t of e)n.add(t);return n}export(){let e={numItems:this.numItems,errorRate:this.errorRate,numBits:this.numBits,numHashes:this.numHashes,bitArray:this.bitArray};if(this.errorRate<1e-4){let t=JSON.stringify(e);n(99179).sync(t)}return e}import(e){this.numItems=e.numItems,this.errorRate=e.errorRate,this.numBits=e.numBits,this.numHashes=e.numHashes,this.bitArray=e.bitArray}add(e){this.getHashValues(e).forEach(e=>{this.bitArray[e]=1})}contains(e){return this.getHashValues(e).every(e=>this.bitArray[e])}getHashValues(e){let t=[];for(let n=1;n<=this.numHashes;n++){let r=function(e){let t=0;for(let n=0;n<e.length;n++)t=Math.imul(t^e.charCodeAt(n),0x5bd1e995),t^=t>>>13,t=Math.imul(t,0x5bd1e995);return t>>>0}(""+e+n)%this.numBits;t.push(r)}return t}constructor(e,t=1e-4){this.numItems=e,this.errorRate=t,this.numBits=Math.ceil(-(e*Math.log(t))/(Math.log(2)*Math.log(2))),this.numHashes=Math.ceil(this.numBits/e*Math.log(2)),this.bitArray=Array(this.numBits).fill(0)}}},76665:(e,t)=>{function n(e){let{ampFirst:t=!1,hybrid:n=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||n&&r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return n}})},88198:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{APP_BUILD_MANIFEST:function(){return m},APP_CLIENT_INTERNALS:function(){return Q},APP_PATHS_MANIFEST:function(){return T},APP_PATH_ROUTES_MANIFEST:function(){return I},BARREL_OPTIMIZATION_PREFIX:function(){return k},BLOCKED_PAGES:function(){return B},BUILD_ID_FILE:function(){return U},BUILD_MANIFEST:function(){return S},CLIENT_PUBLIC_FILES_PATH:function(){return x},CLIENT_REFERENCE_MANIFEST:function(){return G},CLIENT_STATIC_FILES_PATH:function(){return w},CLIENT_STATIC_FILES_RUNTIME_AMP:function(){return J},CLIENT_STATIC_FILES_RUNTIME_MAIN:function(){return $},CLIENT_STATIC_FILES_RUNTIME_MAIN_APP:function(){return q},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS:function(){return et},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL:function(){return en},CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH:function(){return Z},CLIENT_STATIC_FILES_RUNTIME_WEBPACK:function(){return ee},COMPILER_INDEXES:function(){return o},COMPILER_NAMES:function(){return u},CONFIG_FILES:function(){return D},DEFAULT_RUNTIME_WEBPACK:function(){return er},DEFAULT_SANS_SERIF_FONT:function(){return es},DEFAULT_SERIF_FONT:function(){return ea},DEV_CLIENT_MIDDLEWARE_MANIFEST:function(){return j},DEV_CLIENT_PAGES_MANIFEST:function(){return C},DYNAMIC_CSS_MANIFEST:function(){return z},EDGE_RUNTIME_WEBPACK:function(){return eu},EDGE_UNSUPPORTED_NODE_APIS:function(){return eE},EXPORT_DETAIL:function(){return N},EXPORT_MARKER:function(){return h},FUNCTIONS_CONFIG_MANIFEST:function(){return A},IMAGES_MANIFEST:function(){return b},INTERCEPTION_ROUTE_REWRITE_MANIFEST:function(){return K},MIDDLEWARE_BUILD_MANIFEST:function(){return Y},MIDDLEWARE_MANIFEST:function(){return L},MIDDLEWARE_REACT_LOADABLE_MANIFEST:function(){return X},MODERN_BROWSERSLIST_TARGET:function(){return r.default},NEXT_BUILTIN_DOCUMENT:function(){return W},NEXT_FONT_MANIFEST:function(){return O},PAGES_MANIFEST:function(){return d},PHASE_DEVELOPMENT_SERVER:function(){return c},PHASE_EXPORT:function(){return s},PHASE_INFO:function(){return E},PHASE_PRODUCTION_BUILD:function(){return f},PHASE_PRODUCTION_SERVER:function(){return l},PHASE_TEST:function(){return _},PRERENDER_MANIFEST:function(){return P},REACT_LOADABLE_MANIFEST:function(){return F},ROUTES_MANIFEST:function(){return M},RSC_MODULE_TYPES:function(){return e_},SERVER_DIRECTORY:function(){return v},SERVER_FILES_MANIFEST:function(){return y},SERVER_PROPS_ID:function(){return ei},SERVER_REFERENCE_MANIFEST:function(){return V},STATIC_PROPS_ID:function(){return eo},STATIC_STATUS_PAGES:function(){return ef},STRING_LITERAL_DROP_BUNDLE:function(){return H},SUBRESOURCE_INTEGRITY_MANIFEST:function(){return R},SYSTEM_ENTRYPOINTS:function(){return ed},TRACE_OUTPUT_VERSION:function(){return el},TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST:function(){return g},TURBO_TRACE_DEFAULT_MEMORY_LIMIT:function(){return ec},UNDERSCORE_NOT_FOUND_ROUTE:function(){return i},UNDERSCORE_NOT_FOUND_ROUTE_ENTRY:function(){return a},WEBPACK_STATS:function(){return p}});let r=n(2403)._(n(39426)),u={client:"client",server:"server",edgeServer:"edge-server"},o={[u.client]:0,[u.server]:1,[u.edgeServer]:2},i="/_not-found",a=""+i+"/page",s="phase-export",f="phase-production-build",l="phase-production-server",c="phase-development-server",_="phase-test",E="phase-info",d="pages-manifest.json",p="webpack-stats.json",T="app-paths-manifest.json",I="app-path-routes-manifest.json",S="build-manifest.json",m="app-build-manifest.json",A="functions-config-manifest.json",R="subresource-integrity-manifest",O="next-font-manifest",h="export-marker.json",N="export-detail.json",P="prerender-manifest.json",M="routes-manifest.json",b="images-manifest.json",y="required-server-files.json",C="_devPagesManifest.json",L="middleware-manifest.json",g="_clientMiddlewareManifest.json",j="_devMiddlewareManifest.json",F="react-loadable-manifest.json",v="server",D=["next.config.js","next.config.mjs","next.config.ts"],U="BUILD_ID",B=["/_document","/_app","/_error"],x="public",w="static",H="__NEXT_DROP_CLIENT_FILE__",W="__NEXT_BUILTIN_DOCUMENT__",k="__barrel_optimize__",G="client-reference-manifest",V="server-reference-manifest",Y="middleware-build-manifest",X="middleware-react-loadable-manifest",K="interception-route-rewrite-manifest",z="dynamic-css-manifest",$="main",q=""+$+"-app",Q="app-pages-internals",Z="react-refresh",J="amp",ee="webpack",et="polyfills",en=Symbol(et),er="webpack-runtime",eu="edge-runtime-webpack",eo="__N_SSG",ei="__N_SSP",ea={name:"Times New Roman",xAvgCharWidth:821,azAvgWidth:854.3953488372093,unitsPerEm:2048},es={name:"Arial",xAvgCharWidth:904,azAvgWidth:934.5116279069767,unitsPerEm:2048},ef=["/500"],el=1,ec=6e3,e_={client:"client",server:"server"},eE=["clearImmediate","setImmediate","BroadcastChannel","ByteLengthQueuingStrategy","CompressionStream","CountQueuingStrategy","DecompressionStream","DomException","MessageChannel","MessageEvent","MessagePort","ReadableByteStreamController","ReadableStreamBYOBRequest","ReadableStreamDefaultController","TransformStreamDefaultController","WritableStreamDefaultController"],ed=new Set([$,Z,J,q]);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89554:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return s},noSSR:function(){return a}});let r=n(2403),u=n(8732);n(82015);let o=r._(n(58914));function i(e){return{default:(null==e?void 0:e.default)||e}}function a(e,t){delete t.webpack,delete t.modules;let n=t.loading;return()=>(0,u.jsx)(n,{error:null,isLoading:!0,pastDelay:!1,timedOut:!1})}function s(e,t){let n=o.default,r={loading:e=>{let{error:t,isLoading:n,pastDelay:r}=e;return null}};e instanceof Promise?r.loader=()=>e:"function"==typeof e?r.loader=e:"object"==typeof e&&(r={...r,...e});let u=(r={...r,...t}).loader;return(r.loadableGenerated&&(r={...r,...r.loadableGenerated},delete r.loadableGenerated),"boolean"!=typeof r.ssr||r.ssr)?n({...r,loader:()=>null!=u?u().then(i):Promise.resolve(i(()=>null))}):(delete r.webpack,delete r.modules,a(n,r))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},91801:(e,t)=>{function n(){let e=Object.create(null);return{on(t,n){(e[t]||(e[t]=[])).push(n)},off(t,n){e[t]&&e[t].splice(e[t].indexOf(n)>>>0,1)},emit(t){for(var n=arguments.length,r=Array(n>1?n-1:0),u=1;u<n;u++)r[u-1]=arguments[u];(e[t]||[]).slice().map(e=>{e(...r)})}}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}})}};