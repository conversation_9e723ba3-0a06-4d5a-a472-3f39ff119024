"use strict";(()=>{var e={};e.id=8015,e.ids=[8015],e.modules={224:e=>{e.exports=import("@discordjs/rest")},4722:e=>{e.exports=require("next-auth/react")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},33915:e=>{e.exports=import("discord-api-types/v10")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},90935:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>p,default:()=>u,routeModule:()=>l});var i=r(93433),s=r(20264),o=r(20584),n=r(96374),d=e([n]);n=(d.then?(await d)():d)[0];let u=(0,o.M)(n,"default"),p=(0,o.M)(n,"config"),l=new i.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/discord/audit-logs",pathname:"/api/discord/audit-logs",bundlePath:"",filename:""},userland:n});a()}catch(e){a(e)}})},96374:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>u});var i=r(4722),s=r(224),o=r(33915),n=r(20381),d=e([s,o]);async function u(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{if(!await (0,i.getSession)({req:e}))return t.status(401).json({error:"Unauthorized"});let r=n._.DISCORD_GUILD_ID;if(!r)return t.status(500).json({error:"DISCORD_GUILD_ID not configured"});let a=new s.REST({version:"10"}).setToken(n._.DISCORD_BOT_TOKEN),{before:d,after:u,limit:p="50"}=e.query,l=new URLSearchParams;d&&"string"==typeof d&&l.append("before",d),u&&"string"==typeof u&&l.append("after",u),l.append("limit",String(Math.min(Number(p)||50,100)));let c=await a.get(`${o.Routes.guildAuditLog(r)}?${l}`),g=Array.isArray(c?.audit_log_entries)?c.audit_log_entries:[],m=new Map;if(Array.isArray(c?.users))for(let e of c.users)m.set(e.id,e);let f=g.map(e=>{let t=m.get(e.user_id);return{id:e.id,action:e.action_type,executor:t?{id:t.id,tag:`${t.username}#${t.discriminator}`,avatar:t.avatar?`https://cdn.discordapp.com/avatars/${t.id}/${t.avatar}.png`:null}:null,target:e.target_id?{id:e.target_id,type:e.target_type}:null,reason:e.reason,createdTimestamp:(()=>{let t=Number(e.id);return Math.round(t/4194304+14200704e5)})(),changes:e.changes?.map(e=>({key:e.key,old:e.old_value,new:e.new_value}))}});return t.status(200).json(f)}catch(e){return t.status(500).json({error:"Failed to fetch audit logs",message:void 0})}}[s,o]=d.then?(await d)():d,a()}catch(e){a(e)}})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(90935));module.exports=a})();