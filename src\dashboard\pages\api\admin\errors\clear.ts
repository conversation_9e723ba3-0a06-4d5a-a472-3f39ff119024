import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { dashboardConfig } from '../../../../core/config';
import { MongoClient } from 'mongodb';

// Reuse connection pattern
let cachedClient: MongoClient | null = null;
const mongoUrl = dashboardConfig.database?.url || 'mongodb://localhost:27017';
const dbName = dashboardConfig.database?.name || 'discord_bot';

async function getDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl, {
      ...(dashboardConfig.database?.options || {}),
    });
  }
  return cachedClient.db(dbName);
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'DELETE') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check admin permission
    const isAdmin = (session.user as any).isAdmin;
    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const db = await getDb();

    // Clear all error logs
    const result = await db.collection('error_logs').deleteMany({});

    res.status(200).json({ 
      success: true, 
      deletedCount: result.deletedCount 
    });
  } catch (error: any) {
    console.error('Error clearing error logs:', error);
    res.status(500).json({ error: 'Internal server error', details: error.message });
  }
} 