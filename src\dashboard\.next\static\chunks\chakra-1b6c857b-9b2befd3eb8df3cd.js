"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9784],{567:(e,a,t)=>{t.d(a,{e:()=>b});var n=t(94513),r=t(56005),l=t(79435),s=t(94285);let[i,o]=(0,l.q6)({name:"AvatarStylesContext",hookName:"useAvatarStyles",providerName:"<Avatar/>"});var c=t(21533);function d(e){let a=e.trim().split(" "),t=a[0]??"",n=a.length>1?a[a.length-1]:"";return t&&n?`${t.charAt(0)}${n.charAt(0)}`:t.charAt(0)}function u(e){let{name:a,getInitials:t,...r}=e,l=o();return(0,n.jsx)(c.B.div,{role:"img","aria-label":a,...r,__css:l.label,children:a?t?.(a):null})}u.displayName="AvatarName";let m=e=>(0,n.jsxs)(c.B.svg,{viewBox:"0 0 128 128",color:"#fff",width:"100%",height:"100%",className:"chakra-avatar__svg",...e,children:[(0,n.jsx)("path",{fill:"currentColor",d:"M103,102.1388 C93.094,111.92 79.3504,118 64.1638,118 C48.8056,118 34.9294,111.768 25,101.7892 L25,95.2 C25,86.8096 31.981,80 40.6,80 L87.4,80 C96.019,80 103,86.8096 103,95.2 L103,102.1388 Z"}),(0,n.jsx)("path",{fill:"currentColor",d:"M63.9961647,24 C51.2938136,24 41,34.2938136 41,46.9961647 C41,59.7061864 51.2938136,70 63.9961647,70 C76.6985159,70 87,59.7061864 87,46.9961647 C87,34.2938136 76.6985159,24 63.9961647,24"})]});var h=t(78846);function f(e){let{src:a,srcSet:t,onError:r,onLoad:l,getInitials:i,name:o,borderRadius:d,loading:f,iconLabel:v,icon:p=(0,n.jsx)(m,{}),ignoreFallback:x,referrerPolicy:b,crossOrigin:y}=e,k=(0,h.l)({src:a,onError:r,crossOrigin:y,ignoreFallback:x});return a&&"loaded"===k?(0,n.jsx)(c.B.img,{src:a,srcSet:t,alt:o??v,onLoad:l,referrerPolicy:b,crossOrigin:y??void 0,className:"chakra-avatar__img",loading:f,__css:{width:"100%",height:"100%",objectFit:"cover",borderRadius:d}}):o?(0,n.jsx)(u,{className:"chakra-avatar__initials",getInitials:i,name:o}):(0,s.cloneElement)(p,{role:"img","aria-label":v})}f.displayName="AvatarImage";var v=t(84756),p=t(8475);let x=(0,r.H2)({display:"inline-flex",alignItems:"center",justifyContent:"center",textAlign:"center",textTransform:"uppercase",fontWeight:"medium",position:"relative",flexShrink:0}),b=(0,v.R)((e,a)=>{let t=(0,p.o)("Avatar",e),[o,u]=(0,s.useState)(!1),{src:h,srcSet:v,name:b,showBorder:y,borderRadius:k="full",onError:C,onLoad:g,getInitials:j=d,icon:_=(0,n.jsx)(m,{}),iconLabel:N=" avatar",loading:A,children:E,borderColor:w,ignoreFallback:B,crossOrigin:S,referrerPolicy:M,...I}=(0,r.MN)(e),D={borderRadius:k,borderWidth:y?"2px":void 0,...x,...t.container};return w&&(D.borderColor=w),(0,n.jsx)(c.B.span,{ref:a,...I,className:(0,l.cx)("chakra-avatar",e.className),"data-loaded":(0,l.sE)(o),__css:D,children:(0,n.jsxs)(i,{value:t,children:[(0,n.jsx)(f,{src:h,srcSet:v,loading:A,onLoad:(0,l.Hj)(g,()=>{u(!0)}),onError:C,getInitials:j,name:b,borderRadius:k,icon:_,iconLabel:N,ignoreFallback:B,crossOrigin:S,referrerPolicy:M}),E]})})});b.displayName="Avatar"},990:(e,a,t)=>{t.d(a,{L:()=>r,a:()=>n});let[n,r]=(0,t(29035).q)({name:"CheckboxGroupContext",strict:!1})},1648:(e,a,t)=>{t.d(a,{_:()=>i});var n=t(94513),r=t(79435),l=t(86756),s=t(21533);function i(e){let{status:a}=(0,l.ZM)(),t=(0,l.cR)(a),i=(0,l.mC)(),o="loading"===a?i.spinner:i.icon;return(0,n.jsx)(s.B.span,{display:"inherit","data-status":a,...e,className:(0,r.cx)("chakra-alert__icon",e.className),__css:o,children:e.children||(0,n.jsx)(t,{h:"100%",w:"100%"})})}i.displayName="AlertIcon"},1870:(e,a,t)=>{t.d(a,{a:()=>o});var n=t(94513),r=t(79435),l=t(58076),s=t(84756),i=t(21533);let o=(0,s.R)(function(e,a){let{className:t,...s}=e,o=(0,l.Q)();return(0,n.jsx)(i.B.div,{ref:a,className:(0,r.cx)("chakra-card__header",t),__css:o.header,...s})})},5142:(e,a,t)=>{t.d(a,{Z:()=>d});var n=t(94513),r=t(56005),l=t(79435),s=t(58076),i=t(84756),o=t(8475),c=t(21533);let d=(0,i.R)(function(e,a){let{className:t,children:i,direction:d="column",justify:u,align:m,...h}=(0,r.MN)(e),f=(0,o.o)("Card",e);return(0,n.jsx)(c.B.div,{ref:a,className:(0,l.cx)("chakra-card",t),__css:{display:"flex",flexDirection:d,justifyContent:u,alignItems:m,position:"relative",minWidth:0,wordWrap:"break-word",...f.container},...h,children:(0,n.jsx)(s.s,{value:f,children:i})})})},11895:(e,a,t)=>{t.d(a,{J:()=>o});var n=t(94513),r=t(79435),l=t(70270),s=t(84756),i=t(21533);let o=(0,s.R)(function(e,a){let{getButtonProps:t}=(0,l.AV)(),s=t(e,a),o={display:"flex",alignItems:"center",width:"100%",outline:0,...(0,l.EF)().button};return(0,n.jsx)(i.B.button,{...s,className:(0,r.cx)("chakra-accordion__button",e.className),__css:o})});o.displayName="AccordionButton"},19237:(e,a,t)=>{t.d(a,{X:()=>o});var n=t(94513),r=t(79435),l=t(86756),s=t(84756),i=t(21533);let o=(0,s.R)(function(e,a){let t=(0,l.mC)(),{status:s}=(0,l.ZM)();return(0,n.jsx)(i.B.div,{ref:a,"data-status":s,...e,className:(0,r.cx)("chakra-alert__title",e.className),__css:t.title})});o.displayName="AlertTitle"},22184:(e,a,t)=>{t.d(a,{K:()=>s});var n=t(94513),r=t(94285),l=t(64349);let s=(0,t(84756).R)((e,a)=>{let{icon:t,children:s,isRound:i,"aria-label":o,...c}=e,d=t||s,u=(0,r.isValidElement)(d)?(0,r.cloneElement)(d,{"aria-hidden":!0,focusable:!1}):null;return(0,n.jsx)(l.$,{px:"0",py:"0",borderRadius:i?"full":void 0,ref:a,"aria-label":o,...c,children:u})});s.displayName="IconButton"},22416:(e,a,t)=>{t.d(a,{Q:()=>o});var n=t(94513),r=t(79435),l=t(70270),s=t(26298),i=t(29484);function o(e){let{isOpen:a,isDisabled:t}=(0,l.AV)(),{reduceMotion:o}=(0,s.Dr)(),c=(0,r.cx)("chakra-accordion__icon",e.className),d={opacity:t?.4:1,transform:a?"rotate(-180deg)":void 0,transition:o?void 0:"transform 0.2s",transformOrigin:"center",...(0,l.EF)().icon};return(0,n.jsx)(i.I,{viewBox:"0 0 24 24","aria-hidden":!0,className:c,__css:d,...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})})}o.displayName="AccordionIcon"},26298:(e,a,t)=>{t.d(a,{Dr:()=>c,If:()=>o,O3:()=>i,r9:()=>d});var n=t(56084),r=t(79435),l=t(94285),s=t(70270);function i(e){var a;let{onChange:t,defaultIndex:i,index:o,allowMultiple:c,allowToggle:d,...u}=e;(function(e){let a=e.index||e.defaultIndex,t=null!=a&&!Array.isArray(a)&&e.allowMultiple;(0,r.R8)({condition:!!t,message:`If 'allowMultiple' is passed, then 'index' or 'defaultIndex' must be an array. You passed: ${typeof a},`})})(e),a=e,(0,r.R8)({condition:!!(a.allowMultiple&&a.allowToggle),message:"If 'allowMultiple' is passed, 'allowToggle' will be ignored. Either remove 'allowToggle' or 'allowMultiple' depending on whether you want multiple accordions visible or not"});let m=(0,s.Of)(),[h,f]=(0,l.useState)(-1);(0,l.useEffect)(()=>()=>{f(-1)},[]);let[v,p]=(0,n.ic)({value:o,defaultValue:()=>c?i??[]:i??-1,onChange:t});return{index:v,setIndex:p,htmlProps:u,getAccordionItemProps:e=>{let a=!1;return null!==e&&(a=Array.isArray(v)?v.includes(e):v===e),{isOpen:a,onChange:a=>{null!==e&&(c&&Array.isArray(v)?p(a?v.concat(e):v.filter(a=>a!==e)):a?p(e):d&&p(-1))}}},focusedIndex:h,setFocusedIndex:f,descendants:m}}let[o,c]=(0,r.q6)({name:"AccordionContext",hookName:"useAccordionContext",providerName:"Accordion"});function d(e){var a,t;let{isDisabled:i,isFocusable:o,id:d,...u}=e,{getAccordionItemProps:m,setFocusedIndex:h}=c(),f=(0,l.useRef)(null),v=(0,l.useId)(),p=d??v,x=`accordion-button-${p}`,b=`accordion-panel-${p}`;a=e,(0,r.R8)({condition:!!(a.isFocusable&&!a.isDisabled),message:`Using only 'isFocusable', this prop is reserved for situations where you pass 'isDisabled' but you still want the element to receive focus (A11y). Either remove it or pass 'isDisabled' as well.
    `});let{register:y,index:k,descendants:C}=(0,s.v3)({disabled:i&&!o}),{isOpen:g,onChange:j}=m(-1===k?null:k);t={isOpen:g,isDisabled:i},(0,r.R8)({condition:t.isOpen&&!!t.isDisabled,message:"Cannot open a disabled accordion item"});let _=(0,l.useCallback)(()=>{j?.(!g),h(k)},[k,h,g,j]),N=(0,l.useCallback)(e=>{let a={ArrowDown:()=>{let e=C.nextEnabled(k);e?.node.focus()},ArrowUp:()=>{let e=C.prevEnabled(k);e?.node.focus()},Home:()=>{let e=C.firstEnabled();e?.node.focus()},End:()=>{let e=C.lastEnabled();e?.node.focus()}}[e.key];a&&(e.preventDefault(),a(e))},[C,k]),A=(0,l.useCallback)(()=>{h(k)},[h,k]),E=(0,l.useCallback)(function(e={},a=null){return{...e,type:"button",ref:(0,n.Px)(y,f,a),id:x,disabled:!!i,"aria-expanded":!!g,"aria-controls":b,onClick:(0,r.Hj)(e.onClick,_),onFocus:(0,r.Hj)(e.onFocus,A),onKeyDown:(0,r.Hj)(e.onKeyDown,N)}},[x,i,g,_,A,N,b,y]),w=(0,l.useCallback)(function(e={},a=null){return{...e,ref:a,role:"region",id:b,"aria-labelledby":x,hidden:!g}},[x,g,b]);return{isOpen:g,isDisabled:i,isFocusable:o,onOpen:()=>{j?.(!0)},onClose:()=>{j?.(!1)},getButtonProps:E,getPanelProps:w,htmlProps:u}}},28365:(e,a,t)=>{t.d(a,{b:()=>o});var n=t(94513),r=t(79435),l=t(58076),s=t(84756),i=t(21533);let o=(0,s.R)(function(e,a){let{className:t,...s}=e,o=(0,l.Q)();return(0,n.jsx)(i.B.div,{ref:a,className:(0,r.cx)("chakra-card__body",t),__css:o.body,...s})})},39905:(e,a,t)=>{t.d(a,{T:()=>c});var n=t(94513),r=t(56005),l=t(79435),s=t(86756),i=t(84756),o=t(21533);let c=(0,i.R)(function(e,a){let{status:t}=(0,s.ZM)(),i=(0,s.mC)(),c=(0,r.H2)({display:"inline",...i.description});return(0,n.jsx)(o.B.div,{ref:a,"data-status":t,...e,className:(0,l.cx)("chakra-alert__desc",e.className),__css:c})});c.displayName="AlertDescription"},42910:(e,a,t)=>{t.d(a,{S:()=>C});var n=t(94513),r=t(56005),l=t(79435),s=t(83541),i=t(94285),o=t(990),c=t(21533);function d(e){return(0,n.jsx)(c.B.svg,{width:"1.2em",viewBox:"0 0 12 10",style:{fill:"none",strokeWidth:2,stroke:"currentColor",strokeDasharray:16},...e,children:(0,n.jsx)("polyline",{points:"1.5 6 4.5 9 10.5 1"})})}function u(e){return(0,n.jsx)(c.B.svg,{width:"1.2em",viewBox:"0 0 24 24",style:{stroke:"currentColor",strokeWidth:4},...e,children:(0,n.jsx)("line",{x1:"21",x2:"3",y1:"12",y2:"12"})})}function m(e){let{isIndeterminate:a,isChecked:t,...r}=e;return t||a?(0,n.jsx)(c.B.div,{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},children:(0,n.jsx)(a?u:d,{...r})}):null}var h=t(63006),f=t(84756),v=t(8475);let p={display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",userSelect:"none",flexShrink:0},x={cursor:"pointer",display:"inline-flex",alignItems:"center",verticalAlign:"top",position:"relative"},b=(0,s.i7)({from:{opacity:0,strokeDashoffset:16,transform:"scale(0.95)"},to:{opacity:1,strokeDashoffset:0,transform:"scale(1)"}}),y=(0,s.i7)({from:{opacity:0},to:{opacity:1}}),k=(0,s.i7)({from:{transform:"scaleX(0.65)"},to:{transform:"scaleX(1)"}}),C=(0,f.R)(function(e,a){let t=(0,o.L)(),s={...t,...e},d=(0,v.o)("Checkbox",s),u=(0,r.MN)(e),{spacing:f="0.5rem",className:C,children:g,iconColor:j,iconSize:_,icon:N=(0,n.jsx)(m,{}),isChecked:A,isDisabled:E=t?.isDisabled,onChange:w,inputProps:B,...S}=u,M=A;t?.value&&u.value&&(M=t.value.includes(u.value));let I=w;t?.onChange&&u.value&&(I=(0,l.OK)(t.onChange,w));let{state:D,getInputProps:R,getCheckboxProps:H,getLabelProps:L,getRootProps:T}=(0,h.v)({...S,isDisabled:E,isChecked:M,onChange:I}),F=function(e){let[a,t]=(0,i.useState)(e),[n,r]=(0,i.useState)(!1);return e!==a&&(r(!0),t(e)),n}(D.isChecked),O=(0,i.useMemo)(()=>({animation:F?D.isIndeterminate?`${y} 20ms linear, ${k} 200ms linear`:`${b} 200ms linear`:void 0,...d.icon,...(0,l.oE)({fontSize:_,color:j})}),[j,_,F,D.isIndeterminate,d.icon]),Z=(0,i.cloneElement)(N,{__css:O,isIndeterminate:D.isIndeterminate,isChecked:D.isChecked});return(0,n.jsxs)(c.B.label,{__css:{...x,...d.container},className:(0,l.cx)("chakra-checkbox",C),...T(),children:[(0,n.jsx)("input",{className:"chakra-checkbox__input",...R(B,a)}),(0,n.jsx)(c.B.span,{__css:{...p,...d.control},className:"chakra-checkbox__control",...H(),children:Z}),g&&(0,n.jsx)(c.B.span,{className:"chakra-checkbox__label",...L(),__css:{marginStart:f,...d.label},children:g})]})});C.displayName="Checkbox"},43700:(e,a,t)=>{t.d(a,{E:()=>c});var n=t(94513),r=t(56005),l=t(79435),s=t(84756),i=t(8475),o=t(21533);let c=(0,s.R)(function(e,a){let t=(0,i.V)("Badge",e),{className:s,...c}=(0,r.MN)(e);return(0,n.jsx)(o.B.span,{ref:a,className:(0,l.cx)("chakra-badge",e.className),...c,__css:{display:"inline-block",whiteSpace:"nowrap",verticalAlign:"middle",...t}})});c.displayName="Badge"},52305:(e,a,t)=>{t.d(a,{s:()=>r});var n=t(29326);let r=(0,t(56340).Q)(n.w4)},58076:(e,a,t)=>{t.d(a,{Q:()=>r,s:()=>n});let[n,r]=(0,t(93681).Wh)("Card")},62306:(e,a,t)=>{t.d(a,{A:()=>u});var n=t(94513),r=t(56005),l=t(79435),s=t(94285),i=t(70270),o=t(26298),c=t(84756),d=t(21533);let u=(0,c.R)(function(e,a){let{children:t,className:c}=e,{htmlProps:u,...m}=(0,o.r9)(e),h=(0,i.EF)(),f=(0,r.H2)({...h.container,overflowAnchor:"none"}),v=(0,s.useMemo)(()=>m,[m]);return(0,n.jsx)(i.TG,{value:v,children:(0,n.jsx)(d.B.div,{ref:a,...u,className:(0,l.cx)("chakra-accordion__item",c),__css:f,children:"function"==typeof t?t({isExpanded:!!m.isOpen,isDisabled:!!m.isDisabled}):t})})});u.displayName="AccordionItem"},63006:(e,a,t)=>{t.d(a,{v:()=>c});var n=t(56084),r=t(79435),l=t(98258),s=t(94285),i=t(97680),o=t(3853);function c(e={}){let{isDisabled:a,isReadOnly:t,isRequired:u,isInvalid:m,id:h,onBlur:f,onFocus:v,"aria-describedby":p}=(0,i.v)(e),{defaultChecked:x,isChecked:b,isFocusable:y,onChange:k,isIndeterminate:C,name:g,value:j,tabIndex:_,"aria-label":N,"aria-labelledby":A,"aria-invalid":E,...w}=e,B=(0,r.cJ)(w,["isDisabled","isReadOnly","isRequired","isInvalid","id","onBlur","onFocus","aria-describedby"]),S=(0,n.c9)(k),M=(0,n.c9)(f),I=(0,n.c9)(v),[D,R]=(0,s.useState)(!1),[H,L]=(0,s.useState)(!1),[T,F]=(0,s.useState)(!1),O=(0,s.useRef)(!1);(0,s.useEffect)(()=>(0,l.Yy)(e=>{O.current=e}),[]);let Z=(0,s.useRef)(null),[P,U]=(0,s.useState)(!0),[K,V]=(0,s.useState)(!!x),q=void 0!==b,$=q?b:K,Q=(0,s.useCallback)(e=>{if(t||a)return void e.preventDefault();q||($?V(e.currentTarget.checked):V(!!C||e.currentTarget.checked)),S?.(e)},[t,a,$,q,C,S]);(0,n.UQ)(()=>{Z.current&&(Z.current.indeterminate=!!C)},[C]),(0,n.w5)(()=>{a&&R(!1)},[a,R]),(0,n.UQ)(()=>{let e=Z.current;if(!e?.form)return;let a=()=>{V(!!x)};return e.form.addEventListener("reset",a),()=>e.form?.removeEventListener("reset",a)},[]);let G=a&&!y,W=(0,s.useCallback)(e=>{" "===e.key&&F(!0)},[F]),z=(0,s.useCallback)(e=>{" "===e.key&&F(!1)},[F]);(0,n.UQ)(()=>{Z.current&&Z.current.checked!==$&&V(Z.current.checked)},[Z.current]);let X=(0,s.useCallback)((e={},n=null)=>({...e,ref:n,"data-active":(0,r.sE)(T),"data-hover":(0,r.sE)(H),"data-checked":(0,r.sE)($),"data-focus":(0,r.sE)(D),"data-focus-visible":(0,r.sE)(D&&O.current),"data-indeterminate":(0,r.sE)(C),"data-disabled":(0,r.sE)(a),"data-invalid":(0,r.sE)(m),"data-readonly":(0,r.sE)(t),"aria-hidden":!0,onMouseDown:(0,r.Hj)(e.onMouseDown,e=>{D&&e.preventDefault(),F(!0)}),onMouseUp:(0,r.Hj)(e.onMouseUp,()=>F(!1)),onMouseEnter:(0,r.Hj)(e.onMouseEnter,()=>L(!0)),onMouseLeave:(0,r.Hj)(e.onMouseLeave,()=>L(!1))}),[T,$,a,D,H,C,m,t]),J=(0,s.useCallback)((e={},n=null)=>({...e,ref:n,"data-active":(0,r.sE)(T),"data-hover":(0,r.sE)(H),"data-checked":(0,r.sE)($),"data-focus":(0,r.sE)(D),"data-focus-visible":(0,r.sE)(D&&O.current),"data-indeterminate":(0,r.sE)(C),"data-disabled":(0,r.sE)(a),"data-invalid":(0,r.sE)(m),"data-readonly":(0,r.sE)(t)}),[T,$,a,D,H,C,m,t]),Y=(0,s.useCallback)((e={},t=null)=>({...B,...e,ref:(0,n.Px)(t,e=>{e&&U("LABEL"===e.tagName)}),onClick:(0,r.Hj)(e.onClick,()=>{P||(Z.current?.click(),requestAnimationFrame(()=>{Z.current?.focus({preventScroll:!0})}))}),"data-disabled":(0,r.sE)(a),"data-checked":(0,r.sE)($),"data-invalid":(0,r.sE)(m)}),[B,a,$,m,P]),ee=(0,s.useCallback)((e={},l=null)=>({...e,ref:(0,n.Px)(Z,l),type:"checkbox",name:g,value:j,id:h,tabIndex:_,onChange:(0,r.Hj)(e.onChange,Q),onBlur:(0,r.Hj)(e.onBlur,M,()=>R(!1)),onFocus:(0,r.Hj)(e.onFocus,I,()=>R(!0)),onKeyDown:(0,r.Hj)(e.onKeyDown,W),onKeyUp:(0,r.Hj)(e.onKeyUp,z),required:u,checked:$,disabled:G,readOnly:t,"aria-label":N,"aria-labelledby":A,"aria-invalid":E?!!E:m,"aria-describedby":p,"aria-disabled":a,"aria-checked":C?"mixed":$,style:o.f}),[g,j,h,_,Q,M,I,W,z,u,$,G,t,N,A,E,m,p,a,C]),ea=(0,s.useCallback)((e={},t=null)=>({...e,ref:t,onMouseDown:(0,r.Hj)(e.onMouseDown,d),"data-disabled":(0,r.sE)(a),"data-checked":(0,r.sE)($),"data-invalid":(0,r.sE)(m)}),[$,a,m]);return{state:{isInvalid:m,isFocused:D,isChecked:$,isActive:T,isHovered:H,isIndeterminate:C,isDisabled:a,isReadOnly:t,isRequired:u},getRootProps:Y,getCheckboxProps:X,getIndicatorProps:J,getInputProps:ee,getLabelProps:ea,htmlProps:B}}function d(e){e.preventDefault(),e.stopPropagation()}},64349:(e,a,t)=>{t.d(a,{$:()=>p});var n=t(94513),r=t(56084),l=t(56005),s=t(79435),i=t(94285);let[o,c]=(0,s.q6)({strict:!1,name:"ButtonGroupContext"});var d=t(21533);function u(e){let{children:a,className:t,...r}=e,l=(0,i.isValidElement)(a)?(0,i.cloneElement)(a,{"aria-hidden":!0,focusable:!1}):a,o=(0,s.cx)("chakra-button__icon",t);return(0,n.jsx)(d.B.span,{display:"inline-flex",alignSelf:"center",flexShrink:0,...r,className:o,children:l})}u.displayName="ButtonIcon";var m=t(84748);function h(e){let{label:a,placement:t,spacing:r="0.5rem",children:o=(0,n.jsx)(m.y,{color:"currentColor",width:"1em",height:"1em"}),className:c,__css:u,...h}=e,f=(0,s.cx)("chakra-button__spinner",c),v="start"===t?"marginEnd":"marginStart",p=(0,i.useMemo)(()=>(0,l.H2)({display:"flex",alignItems:"center",position:a?"relative":"absolute",[v]:a?r:0,fontSize:"1em",lineHeight:"normal",...u}),[u,a,v,r]);return(0,n.jsx)(d.B.div,{className:f,...h,__css:p,children:o})}h.displayName="ButtonSpinner";var f=t(84756),v=t(8475);let p=(0,f.R)((e,a)=>{let t=c(),o=(0,v.V)("Button",{...t,...e}),{isDisabled:u=t?.isDisabled,isLoading:m,isActive:f,children:p,leftIcon:b,rightIcon:y,loadingText:k,iconSpacing:C="0.5rem",type:g,spinner:j,spinnerPlacement:_="start",className:N,as:A,shouldWrapChildren:E,...w}=(0,l.MN)(e),B=(0,i.useMemo)(()=>{let e={...o?._focus,zIndex:1};return{display:"inline-flex",appearance:"none",alignItems:"center",justifyContent:"center",userSelect:"none",position:"relative",whiteSpace:"nowrap",verticalAlign:"middle",outline:"none",...o,...!!t&&{_focus:e}}},[o,t]),{ref:S,type:M}=function(e){let[a,t]=(0,i.useState)(!e);return{ref:(0,i.useCallback)(e=>{e&&t("BUTTON"===e.tagName)},[]),type:a?"button":void 0}}(A),I={rightIcon:y,leftIcon:b,iconSpacing:C,children:p,shouldWrapChildren:E};return(0,n.jsxs)(d.B.button,{disabled:u||m,ref:(0,r.SV)(a,S),as:A,type:g??M,"data-active":(0,s.sE)(f),"data-loading":(0,s.sE)(m),__css:B,className:(0,s.cx)("chakra-button",N),...w,children:[m&&"start"===_&&(0,n.jsx)(h,{className:"chakra-button__spinner--start",label:k,placement:"start",spacing:C,children:j}),m?k||(0,n.jsx)(d.B.span,{opacity:0,children:(0,n.jsx)(x,{...I})}):(0,n.jsx)(x,{...I}),m&&"end"===_&&(0,n.jsx)(h,{className:"chakra-button__spinner--end",label:k,placement:"end",spacing:C,children:j})]})});function x(e){let{leftIcon:a,rightIcon:t,children:r,iconSpacing:l,shouldWrapChildren:s}=e;return s?(0,n.jsxs)("span",{style:{display:"contents"},children:[a&&(0,n.jsx)(u,{marginEnd:l,children:a}),r,t&&(0,n.jsx)(u,{marginStart:l,children:t})]}):(0,n.jsxs)(n.Fragment,{children:[a&&(0,n.jsx)(u,{marginEnd:l,children:a}),r,t&&(0,n.jsx)(u,{marginStart:l,children:t})]})}p.displayName="Button"},64389:(e,a,t)=>{t.d(a,{v:()=>d});var n=t(94513),r=t(79435),l=t(70270),s=t(26298),i=t(23640),o=t(84756),c=t(21533);let d=(0,o.R)(function(e,a){let{className:t,motionProps:o,...d}=e,{reduceMotion:u}=(0,s.Dr)(),{getPanelProps:m,isOpen:h}=(0,l.AV)(),f=m(d,a),v=(0,r.cx)("chakra-accordion__panel",t),p=(0,l.EF)();u||delete f.hidden;let x=(0,n.jsx)(c.B.div,{...f,__css:p.panel,className:v});return u?x:(0,n.jsx)(i.S,{in:h,...o,children:x})});d.displayName="AccordionPanel"},65012:(e,a,t)=>{t.d(a,{J:()=>d});var n=t(94513),r=t(56005),l=t(29484),s=t(84756),i=t(8475),o=t(21533);function c(e){return(0,n.jsx)(l.I,{focusable:"false","aria-hidden":!0,...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M.439,21.44a1.5,1.5,0,0,0,2.122,2.121L11.823,14.3a.25.25,0,0,1,.354,0l9.262,9.263a1.5,1.5,0,1,0,2.122-2.121L14.3,12.177a.25.25,0,0,1,0-.354l9.263-9.262A1.5,1.5,0,0,0,21.439.44L12.177,9.7a.25.25,0,0,1-.354,0L2.561.44A1.5,1.5,0,0,0,.439,2.561L9.7,11.823a.25.25,0,0,1,0,.354Z"})})}let d=(0,s.R)(function(e,a){let t=(0,i.V)("CloseButton",e),{children:l,isDisabled:s,__css:d,...u}=(0,r.MN)(e);return(0,n.jsx)(o.B.button,{type:"button","aria-label":"Close",ref:a,disabled:s,__css:{outline:0,display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,...t,...d},...u,children:l||(0,n.jsx)(c,{width:"1em",height:"1em"})})});d.displayName="CloseButton"},67116:(e,a,t)=>{t.d(a,{F:()=>d});var n=t(94513),r=t(56005),l=t(79435),s=t(86756),i=t(8475),o=t(84756),c=t(21533);let d=(0,o.R)(function(e,a){let{status:t="info",addRole:o=!0,...d}=(0,r.MN)(e),u=e.colorScheme??(0,s.He)(t),m=(0,i.o)("Alert",{...e,colorScheme:u}),h=(0,r.H2)({width:"100%",display:"flex",alignItems:"center",position:"relative",overflow:"hidden",...m.container});return(0,n.jsx)(s.Sw,{value:{status:t},children:(0,n.jsx)(s._N,{value:m,children:(0,n.jsx)(c.B.div,{"data-status":t,role:o?"alert":void 0,ref:a,...d,className:(0,l.cx)("chakra-alert",e.className),__css:h})})})});d.displayName="Alert"},70270:(e,a,t)=>{t.d(a,{AV:()=>o,C3:()=>c,EF:()=>s,Of:()=>u,TG:()=>i,gm:()=>l,v3:()=>m});var n=t(79435),r=t(55588);let[l,s]=(0,n.q6)({name:"AccordionStylesContext",hookName:"useAccordionStyles",providerName:"<Accordion />"}),[i,o]=(0,n.q6)({name:"AccordionItemContext",hookName:"useAccordionItemContext",providerName:"<AccordionItem />"}),[c,d,u,m]=(0,r.D)()},73336:(e,a,t)=>{t.d(a,{I:()=>i});var n=t(56084),r=t(79435),l=t(94285);function s(e){let{tagName:a,isContentEditable:t}=e.composedPath?.()?.[0]??e.target;return"INPUT"!==a&&"TEXTAREA"!==a&&!0!==t}function i(e={}){let{ref:a,isDisabled:t,isFocusable:o,clickOnEnter:c=!0,clickOnSpace:d=!0,onMouseDown:u,onMouseUp:m,onClick:h,onKeyDown:f,onKeyUp:v,tabIndex:p,onMouseOver:x,onMouseLeave:b,...y}=e,[k,C]=(0,l.useState)(!0),[g,j]=(0,l.useState)(!1),_=function(){let e=(0,l.useRef)(new Map),a=e.current,t=(0,l.useCallback)((a,t,n,r)=>{e.current.set(n,{type:t,el:a,options:r}),a.addEventListener(t,n,r)},[]),n=(0,l.useCallback)((a,t,n,r)=>{a.removeEventListener(t,n,r),e.current.delete(n)},[]);return(0,l.useEffect)(()=>()=>{a.forEach((e,a)=>{n(e.el,e.type,a,e.options)})},[n,a]),{add:t,remove:n}}(),N=k?p:p||0,A=t&&!o,E=(0,l.useCallback)(e=>{if(t){e.stopPropagation(),e.preventDefault();return}e.currentTarget.focus(),h?.(e)},[t,h]),w=(0,l.useCallback)(e=>{g&&s(e)&&(e.preventDefault(),e.stopPropagation(),j(!1),_.remove(document,"keyup",w,!1))},[g,_]),B=(0,l.useCallback)(e=>{if(f?.(e),t||e.defaultPrevented||e.metaKey||!s(e.nativeEvent)||k)return;let a=c&&"Enter"===e.key;d&&" "===e.key&&(e.preventDefault(),j(!0)),a&&(e.preventDefault(),e.currentTarget.click()),_.add(document,"keyup",w,!1)},[t,k,f,c,d,_,w]),S=(0,l.useCallback)(e=>{v?.(e),!t&&!e.defaultPrevented&&!e.metaKey&&s(e.nativeEvent)&&!k&&d&&" "===e.key&&(e.preventDefault(),j(!1),e.currentTarget.click())},[d,k,t,v]),M=(0,l.useCallback)(e=>{0===e.button&&(j(!1),_.remove(document,"mouseup",M,!1))},[_]),I=(0,l.useCallback)(e=>{if(0===e.button){if(t){e.stopPropagation(),e.preventDefault();return}k||j(!0),e.currentTarget.focus({preventScroll:!0}),_.add(document,"mouseup",M,!1),u?.(e)}},[t,k,u,_,M]),D=(0,l.useCallback)(e=>{0===e.button&&(k||j(!1),m?.(e))},[m,k]),R=(0,l.useCallback)(e=>{if(t)return void e.preventDefault();x?.(e)},[t,x]),H=(0,l.useCallback)(e=>{g&&(e.preventDefault(),j(!1)),b?.(e)},[g,b]),L=(0,n.Px)(a,e=>{e&&"BUTTON"!==e.tagName&&C(!1)});return k?{...y,ref:L,type:"button","aria-disabled":A?void 0:t,disabled:A,onClick:E,onMouseDown:u,onMouseUp:m,onKeyUp:v,onKeyDown:f,onMouseOver:x,onMouseLeave:b}:{...y,ref:L,role:"button","data-active":(0,r.sE)(g),"aria-disabled":t?"true":void 0,tabIndex:A?void 0:N,onClick:E,onMouseDown:I,onMouseUp:D,onKeyUp:S,onKeyDown:B,onMouseOver:R,onMouseLeave:H}}},79028:(e,a,t)=>{t.d(a,{a:()=>n});let n=(0,t(21533).B)("div");n.displayName="Box"},81420:(e,a,t)=>{t.d(a,{n:()=>m});var n=t(94513),r=t(56005),l=t(79435),s=t(94285),i=t(70270),o=t(26298),c=t(84756),d=t(8475),u=t(21533);let m=(0,c.R)(function({children:e,reduceMotion:a,...t},c){let m=(0,d.o)("Accordion",t),h=(0,r.MN)(t),{htmlProps:f,descendants:v,...p}=(0,o.O3)(h),x=(0,s.useMemo)(()=>({...p,reduceMotion:!!a}),[p,a]);return(0,n.jsx)(i.C3,{value:v,children:(0,n.jsx)(o.If,{value:x,children:(0,n.jsx)(i.gm,{value:m,children:(0,n.jsx)(u.B.div,{ref:c,...f,className:(0,l.cx)("chakra-accordion",t.className),__css:m.root,children:e})})})})});m.displayName="Accordion"},85852:(e,a,t)=>{t.d(a,{w:()=>o});var n=t(94513),r=t(79435),l=t(58076),s=t(84756),i=t(21533);let o=(0,s.R)(function(e,a){let{className:t,justify:s,...o}=e,c=(0,l.Q)();return(0,n.jsx)(i.B.div,{ref:a,className:(0,r.cx)("chakra-card__footer",t),__css:{display:"flex",justifyContent:s,...c.footer},...o})})},86756:(e,a,t)=>{t.d(a,{Sw:()=>o,_N:()=>d,He:()=>h,cR:()=>f,ZM:()=>c,mC:()=>u});var n=t(79435),r=t(94513),l=t(29484);function s(e){return(0,r.jsx)(l.I,{viewBox:"0 0 24 24",...e,children:(0,r.jsx)("path",{fill:"currentColor",d:"M11.983,0a12.206,12.206,0,0,0-8.51,3.653A11.8,11.8,0,0,0,0,12.207,11.779,11.779,0,0,0,11.8,24h.214A12.111,12.111,0,0,0,24,11.791h0A11.766,11.766,0,0,0,11.983,0ZM10.5,16.542a1.476,1.476,0,0,1,1.449-1.53h.027a1.527,1.527,0,0,1,1.523,1.47,1.475,1.475,0,0,1-1.449,1.53h-.027A1.529,1.529,0,0,1,10.5,16.542ZM11,12.5v-6a1,1,0,0,1,2,0v6a1,1,0,1,1-2,0Z"})})}var i=t(84748);let[o,c]=(0,n.q6)({name:"AlertContext",hookName:"useAlertContext",providerName:"<Alert />"}),[d,u]=(0,n.q6)({name:"AlertStylesContext",hookName:"useAlertStyles",providerName:"<Alert />"}),m={info:{icon:function(e){return(0,r.jsx)(l.I,{viewBox:"0 0 24 24",...e,children:(0,r.jsx)("path",{fill:"currentColor",d:"M12,0A12,12,0,1,0,24,12,12.013,12.013,0,0,0,12,0Zm.25,5a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,12.25,5ZM14.5,18.5h-4a1,1,0,0,1,0-2h.75a.25.25,0,0,0,.25-.25v-4.5a.25.25,0,0,0-.25-.25H10.5a1,1,0,0,1,0-2h1a2,2,0,0,1,2,2v4.75a.25.25,0,0,0,.25.25h.75a1,1,0,1,1,0,2Z"})})},colorScheme:"blue"},warning:{icon:s,colorScheme:"orange"},success:{icon:function(e){return(0,r.jsx)(l.I,{viewBox:"0 0 24 24",...e,children:(0,r.jsx)("path",{fill:"currentColor",d:"M12,0A12,12,0,1,0,24,12,12.014,12.014,0,0,0,12,0Zm6.927,8.2-6.845,9.289a1.011,1.011,0,0,1-1.43.188L5.764,13.769a1,1,0,1,1,1.25-1.562l4.076,3.261,6.227-8.451A1,1,0,1,1,18.927,8.2Z"})})},colorScheme:"green"},error:{icon:s,colorScheme:"red"},loading:{icon:i.y,colorScheme:"blue"}};function h(e){return m[e].colorScheme}function f(e){return m[e].icon}},89089:(e,a,t)=>{t.d(a,{$:()=>c});var n=t(94513),r=t(94285),l=t(990),s=t(56084),i=t(79435);function o(e){return e&&(0,i.Gv)(e)&&(0,i.Gv)(e.target)}function c(e){let{colorScheme:a,size:t,variant:i,children:c,isDisabled:d}=e,{value:u,onChange:m}=function(e={}){let{defaultValue:a,value:t,onChange:n,isDisabled:l,isNative:i}=e,c=(0,s.c9)(n),[d,u]=(0,s.ic)({value:t,defaultValue:a||[],onChange:c}),m=(0,r.useCallback)(e=>{if(!d)return;let a=o(e)?e.target.checked:!d.includes(e),t=o(e)?e.target.value:e;u(a?[...d,t]:d.filter(e=>String(e)!==String(t)))},[u,d]),h=(0,r.useCallback)((e={})=>{let a=i?"checked":"isChecked";return{...e,[a]:d.some(a=>String(e.value)===String(a)),onChange:m}},[m,i,d]);return{value:d,isDisabled:l,onChange:m,setValue:u,getCheckboxProps:h}}(e),h=(0,r.useMemo)(()=>({size:t,onChange:m,colorScheme:a,value:u,variant:i,isDisabled:d}),[t,m,a,u,i,d]);return(0,n.jsx)(l.a,{value:h,children:c})}c.displayName="CheckboxGroup"},95903:(e,a,t)=>{t.d(a,{o:()=>s});var n=t(94513),r=t(21533),l=t(84756);let s=(0,r.B)("div",{baseStyle:{display:"flex",alignItems:"center",justifyContent:"center"}});s.displayName="Center";let i={horizontal:{insetStart:"50%",transform:"translateX(-50%)"},vertical:{top:"50%",transform:"translateY(-50%)"},both:{insetStart:"50%",top:"50%",transform:"translate(-50%, -50%)"}};(0,l.R)(function(e,a){let{axis:t="both",...l}=e;return(0,n.jsx)(r.B.div,{ref:a,__css:i[t],...l,position:"absolute"})})}}]);