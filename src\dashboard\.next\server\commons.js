exports.id=4223,exports.ids=[4223],exports.modules={3001:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{DP:()=>h,NP:()=>b});var a=t(8732),s=t(82015),i=t(73907),n=t(79135),l=e([i]);i=(l.then?(await l)():l)[0];let c=[{id:"default",name:"Default Purple",description:"Classic purple and blue gradient theme",colors:{primary:"#8b5cf6",primaryLight:"#a78bfa",primaryDark:"#7c3aed",secondary:"#5865F2",accent:"#06b6d4",background:"#1a202c",surface:"rgba(255,255,255,0.03)",text:"#f7fafc",textSecondary:"#a0aec0",border:"rgba(255,255,255,0.2)",success:"#68d391",warning:"#fbb6ce",error:"#fc8181",info:"#63b3ed"}},{id:"ocean",name:"Ocean Blue",description:"Deep blue ocean-inspired theme",colors:{primary:"#0ea5e9",primaryLight:"#38bdf8",primaryDark:"#0284c7",secondary:"#06b6d4",accent:"#8b5cf6",background:"#0f172a",surface:"rgba(59, 130, 246, 0.05)",text:"#f1f5f9",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}},{id:"forest",name:"Forest Green",description:"Nature-inspired green theme",colors:{primary:"#059669",primaryLight:"#10b981",primaryDark:"#047857",secondary:"#065f46",accent:"#8b5cf6",background:"#0f1419",surface:"rgba(16, 185, 129, 0.05)",text:"#f0fdf4",textSecondary:"#86efac",border:"rgba(16, 185, 129, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"sunset",name:"Sunset Orange",description:"Warm sunset-inspired theme",colors:{primary:"#ea580c",primaryLight:"#fb923c",primaryDark:"#c2410c",secondary:"#dc2626",accent:"#8b5cf6",background:"#1c1917",surface:"rgba(251, 146, 60, 0.05)",text:"#fef7ed",textSecondary:"#fdba74",border:"rgba(251, 146, 60, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"rose",name:"Rose Pink",description:"Elegant rose and pink theme",colors:{primary:"#e11d48",primaryLight:"#f43f5e",primaryDark:"#be123c",secondary:"#ec4899",accent:"#8b5cf6",background:"#1f1720",surface:"rgba(244, 63, 94, 0.05)",text:"#fdf2f8",textSecondary:"#fda4af",border:"rgba(244, 63, 94, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"midnight",name:"Midnight Blue",description:"Dark midnight blue theme",colors:{primary:"#1e40af",primaryLight:"#3b82f6",primaryDark:"#1e3a8a",secondary:"#4338ca",accent:"#06b6d4",background:"#0c0a1f",surface:"rgba(59, 130, 246, 0.05)",text:"#f8fafc",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}}],d=7311==t.j?(0,s.createContext)(void 0):null,h=()=>{let e=(0,s.useContext)(d);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e},b=({children:e})=>{let[r,t]=(0,s.useState)(c[0]),[o,l]=(0,s.useState)([]);(0,s.useEffect)(()=>{let e=localStorage.getItem("dashboard-color-scheme"),r=localStorage.getItem("dashboard-custom-schemes");if(r)try{let e=JSON.parse(r);l(e)}catch(e){}if(e){let r=c.find(r=>r.id===e);if(r)t(r);else{let r=localStorage.getItem("dashboard-custom-schemes");if(r)try{let o=JSON.parse(r).find(r=>r.id===e);o&&t(o)}catch(e){}}}},[]),(0,s.useEffect)(()=>{localStorage.setItem("dashboard-color-scheme",r.id)},[r]),(0,s.useEffect)(()=>{localStorage.setItem("dashboard-custom-schemes",JSON.stringify(o))},[o]);let h=[...c,...o],b=(0,i.o)({...n.A,colors:{...n.A.colors,brand:{50:r.colors.primaryLight+"20",100:r.colors.primaryLight+"40",200:r.colors.primaryLight+"60",300:r.colors.primaryLight+"80",400:r.colors.primaryLight,500:r.colors.primary,600:r.colors.primaryDark,700:r.colors.primaryDark+"CC",800:r.colors.primaryDark+"AA",900:r.colors.primaryDark+"88"},custom:{primary:r.colors.primary,primaryLight:r.colors.primaryLight,primaryDark:r.colors.primaryDark,secondary:r.colors.secondary,accent:r.colors.accent,background:r.colors.background,surface:r.colors.surface,text:r.colors.text,textSecondary:r.colors.textSecondary,border:r.colors.border,success:r.colors.success,warning:r.colors.warning,error:r.colors.error,info:r.colors.info}},styles:{global:{body:{bg:r.colors.background,color:r.colors.text}}}});return(0,a.jsx)(d.Provider,{value:{currentScheme:r,setColorScheme:e=>{let r=c.find(r=>r.id===e);if(r)return void t(r);let a=o.find(r=>r.id===e);a&&t(a)},colorSchemes:h,customSchemes:o,addCustomScheme:e=>{l(r=>[...r.filter(r=>r.id!==e.id),e]),t(e)},deleteCustomScheme:e=>{l(r=>r.filter(r=>r.id!==e)),r.id===e&&t(c[0])},resetToDefault:()=>{t(c[0])}},children:(0,a.jsx)(i.s,{theme:b,children:e})})};o()}catch(e){o(e)}})},3027:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{Box:()=>a.a,Button:()=>s.$,Collapse:()=>i.S,Divider:()=>n.c,Icon:()=>l.I,Link:()=>c.N,Text:()=>d.E,Tooltip:()=>h.m,VStack:()=>b.T,useDisclosure:()=>u.j});var a=t(45200),s=t(77502),i=t(10692),n=t(464),l=t(50792),c=t(84802),d=t(87378),h=t(63792),b=t(17335),u=t(66646);t(9436),t(25035);var p=e([a,s,n,l,c,d,h,b]);[a,s,n,l,c,d,h,b]=p.then?(await p)():p,o()}catch(e){o(e)}})},8077:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{default:()=>h});var a=t(8732),s=t(4722),i=t(54959),n=t.n(i),l=t(3001),c=e([l]);function d({Component:e,pageProps:r}){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(n(),{children:[(0,a.jsx)("title",{children:"404 Bot Dashboard"}),(0,a.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,a.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,a.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,a.jsx)("link",{rel:"manifest",href:"/site.webmanifest"})]}),(0,a.jsx)(e,{...r})]})}function h({Component:e,pageProps:{session:r,...t}}){return(0,a.jsx)(s.SessionProvider,{session:r,children:(0,a.jsx)(l.NP,{children:(0,a.jsx)(d,{Component:e,pageProps:t})})})}l=(c.then?(await c)():c)[0],o()}catch(e){o(e)}})},9436:()=>{},12087:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{A:()=>d});var a=t(8732),s=t(80541),i=t(84606),n=t(82015),l=t(4722),c=e([s]);function d(){let{data:e}=(0,l.useSession)(),[r]=(0,n.useState)([]);return e?.user?(0,a.jsxs)(s.AM,{placement:"bottom-end",children:[(0,a.jsx)(s.Wv,{children:(0,a.jsxs)(s.az,{position:"relative",children:[(0,a.jsx)(s.m_,{label:"Notifications",placement:"bottom",children:(0,a.jsx)(s.K0,{"aria-label":"Notifications",icon:(0,a.jsx)(i.zd,{}),variant:"ghost",size:"sm",color:"gray.300",_hover:{bg:"whiteAlpha.200",color:"white",transform:"scale(1.05)"},transition:"all 0.2s"})}),!1]})}),(0,a.jsxs)(s.hl,{bg:"gray.800",borderColor:"whiteAlpha.200",boxShadow:"2xl",maxW:"400px",_focus:{boxShadow:"2xl"},children:[(0,a.jsx)(s.DI,{borderBottomColor:"whiteAlpha.200",fontWeight:"semibold",fontSize:"lg",color:"white",children:"Notifications"}),(0,a.jsx)(s.ej,{maxH:"400px",overflowY:"auto",children:(0,a.jsx)(s.Tk,{spacing:0,align:"stretch",children:0===r.length?(0,a.jsx)(s.az,{py:8,textAlign:"center",children:(0,a.jsx)(s.EY,{color:"gray.400",fontSize:"sm",children:"No notifications yet"})}):r.map(e=>(0,a.jsxs)(s.az,{p:3,borderBottom:"1px",borderColor:"whiteAlpha.100",children:[(0,a.jsx)(s.EY,{fontSize:"sm",color:"white",fontWeight:"medium",children:e.title}),(0,a.jsx)(s.EY,{fontSize:"xs",color:"gray.400",mt:1,children:e.message})]},e.id))})})]})]}):null}s=(c.then?(await c)():c)[0],o()}catch(e){o(e)}})},19097:(e,r,t)=>{"use strict";t.d(r,{QeK:()=>o.FiLogOut});var o=t(64960)},20381:(e,r,t)=>{"use strict";t.d(r,{_:()=>p});var o=t(72115),a=t.n(o),s=t(29021),i=t.n(s),n=t(33873),l=t.n(n);let c=function(){let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>l().resolve(process.cwd(),e)).find(e=>i().existsSync(e));if(!e){let r=l().resolve(__dirname,"../../../config.yml");i().existsSync(r)&&(e=r)}if(!e)throw Error("config.yml not found");return e}(),d=a().parse(i().readFileSync(c,"utf8")),h="http://***********:3000",b=new URL(d.dashboard?.url||"http://localhost:3000"),u=`${b.protocol}//localhost:${b.port||"3000"}`,p={DISCORD_BOT_TOKEN:d.bot.token,DISCORD_CLIENT_ID:d.bot.clientId,DISCORD_CLIENT_SECRET:d.bot.clientSecret,DISCORD_GUILD_ID:d.bot.guildId,NEXTAUTH_URL:h||d.dashboard?.nextAuthUrl||d.dashboard?.url||u,NEXTAUTH_LOCALHOST_URL:u};d.bot.token,d.bot.clientId,d.bot.clientSecret,d.bot.guildId,d.bot.presence,h||d.dashboard?.url,h||d.dashboard?.nextAuthUrl||d.dashboard?.url,d.dashboard?.admins,d.dashboard?.adminRoleIds,d.dashboard?.session?.secret||d.bot.clientSecret,Object.entries(p).forEach(([e,r])=>{if(!r)throw Error(`Missing required environment variable: ${e}`)})},22685:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{A:()=>d});var a=t(8732),s=t(54011),i=t(82015),n=t(42820),l=t(4722),c=e([s]);function d({isOpen:e,onClose:r}){let{data:t}=(0,l.useSession)(),o=(0,s.dj)(),[c,d]=(0,i.useState)(!1),[h,b]=(0,i.useState)({motivation:"",experience:"",hoursPerWeek:"",feedback:"",contact:""}),u=(e,r)=>{b(t=>({...t,[e]:r}))},p=()=>h.motivation.length>10&&h.hoursPerWeek&&h.contact,m=async()=>{if(!p())return void o({title:"Form Incomplete",description:"Please fill in all required fields.",status:"error",duration:3e3});d(!0);try{if(!(await fetch("/api/admin/applications",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"experimental",feature:"experimental-access",reason:JSON.stringify({motivation:h.motivation,experience:h.experience,hoursPerWeek:h.hoursPerWeek,feedback:h.feedback,contact:h.contact,username:t?.user?.name||"Unknown",userId:t?.user?.id,submittedAt:new Date().toISOString()})})})).ok)throw Error("Failed to submit application");o({title:"Application Submitted!",description:"Your application has been submitted and will be reviewed by OnedEyePete.",status:"success",duration:5e3}),r(),b({motivation:"",experience:"",hoursPerWeek:"",feedback:"",contact:""})}catch(e){o({title:"Submission Failed",description:"There was an error submitting your application. Please try again.",status:"error",duration:5e3})}finally{d(!1)}};return(0,a.jsxs)(s.aF,{isOpen:e,onClose:r,size:"xl",scrollBehavior:"inside",children:[(0,a.jsx)(s.mH,{bg:"blackAlpha.700",backdropFilter:"blur(10px)"}),(0,a.jsxs)(s.$m,{bg:"gray.800",border:"1px solid",borderColor:"whiteAlpha.200",children:[(0,a.jsx)(s.rQ,{borderBottom:"1px solid",borderColor:"whiteAlpha.200",children:(0,a.jsxs)(s.Tk,{align:"start",spacing:2,children:[(0,a.jsx)(s.EY,{fontSize:"xl",fontWeight:"bold",children:"Experimental Features Application"}),(0,a.jsx)(s.EY,{fontSize:"sm",color:"gray.400",children:"Apply to test cutting-edge features and help improve the bot"})]})}),(0,a.jsx)(s.s_,{}),(0,a.jsx)(s.cw,{p:6,children:(0,a.jsxs)(s.Tk,{spacing:6,align:"stretch",children:[(0,a.jsxs)(s.Fc,{status:"info",bg:"blue.900",border:"1px solid",borderColor:"blue.700",children:[(0,a.jsx)(s._0,{}),(0,a.jsx)(s.EY,{fontSize:"sm",children:"Your application will be submitted to OnedEyePete's dashboard for review. Response time can take up to one week. Only serious testers who can provide valuable feedback will be accepted."})]}),(0,a.jsxs)(s.MJ,{isRequired:!0,children:[(0,a.jsx)(s.lR,{color:"white",children:"Why do you want to test experimental features? *"}),(0,a.jsx)(s.TM,{placeholder:"Tell us about your motivation and what you hope to contribute...",value:h.motivation,onChange:e=>u("motivation",e.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300",rows:4,_focus:{borderColor:"yellow.400",boxShadow:"0 0 0 1px #F6E05E"}}),(0,a.jsxs)(s.EY,{fontSize:"xs",color:"gray.400",mt:1,children:[h.motivation.length,"/500 characters (minimum 10)"]})]}),(0,a.jsxs)(s.MJ,{children:[(0,a.jsx)(s.lR,{color:"white",children:"Previous testing or beta experience"}),(0,a.jsx)(s.TM,{placeholder:"Describe any previous experience with beta testing, bug reporting, or feedback...",value:h.experience,onChange:e=>u("experience",e.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300",rows:3,_focus:{borderColor:"yellow.400",boxShadow:"0 0 0 1px #F6E05E"}})]}),(0,a.jsxs)(s.MJ,{isRequired:!0,children:[(0,a.jsx)(s.lR,{color:"white",children:"How many hours per week can you dedicate to testing? *"}),(0,a.jsxs)(s.l6,{placeholder:"Select hours per week",value:h.hoursPerWeek,onChange:e=>u("hoursPerWeek",e.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300",_focus:{borderColor:"yellow.400",boxShadow:"0 0 0 1px #F6E05E"},children:[(0,a.jsx)("option",{value:"1-2",children:"1-2 hours"}),(0,a.jsx)("option",{value:"3-5",children:"3-5 hours"}),(0,a.jsx)("option",{value:"6-10",children:"6-10 hours"}),(0,a.jsx)("option",{value:"10+",children:"10+ hours"})]})]}),(0,a.jsxs)(s.MJ,{children:[(0,a.jsx)(s.lR,{color:"white",children:"What kind of feedback can you provide?"}),(0,a.jsx)(s.TM,{placeholder:"Describe your ability to provide detailed bug reports, suggestions, or usability feedback...",value:h.feedback,onChange:e=>u("feedback",e.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300",rows:3,_focus:{borderColor:"yellow.400",boxShadow:"0 0 0 1px #F6E05E"}})]}),(0,a.jsxs)(s.MJ,{isRequired:!0,children:[(0,a.jsx)(s.lR,{color:"white",children:"Best way to contact you for follow-up *"}),(0,a.jsx)(s.pd,{placeholder:"Discord username, email, or other contact method",value:h.contact,onChange:e=>u("contact",e.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300",_focus:{borderColor:"yellow.400",boxShadow:"0 0 0 1px #F6E05E"}})]})]})}),(0,a.jsx)(s.jl,{borderTop:"1px solid",borderColor:"whiteAlpha.200",children:(0,a.jsxs)(s.zt,{spacing:4,width:"full",justify:"space-between",children:[(0,a.jsxs)(s.zt,{spacing:4,children:[(0,a.jsx)(s.In,{as:n.y8Q,color:"yellow.300",boxSize:6}),(0,a.jsx)(s.EY,{color:"gray.400",fontSize:"sm",children:"Submitted to OnedEyePete's dashboard • Response within 1 week"})]}),(0,a.jsxs)(s.zt,{spacing:4,children:[(0,a.jsx)(s.$n,{variant:"ghost",onClick:r,children:"Cancel"}),(0,a.jsx)(s.$n,{colorScheme:"yellow",onClick:m,isLoading:c,loadingText:"Submitting...",leftIcon:(0,a.jsx)(s.In,{as:n.A7C}),isDisabled:!p(),children:"Submit Application"})]})]})})]})]})}s=(c.then?(await c)():c)[0],o()}catch(e){o(e)}})},25035:(e,r,t)=>{"use strict";t(45261)},31749:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var o=t(4722),a=t(82015);function s(){let{data:e}=(0,o.useSession)(),[r,t]=(0,a.useState)(!1),[s,i]=(0,a.useState)(""),[n,l]=(0,a.useState)(!0);return{hasAccess:r,reason:s,isLoading:n,isDeveloper:"developer"===s,isTester:"tester"===s}}},42820:(e,r,t)=>{"use strict";t.d(r,{A7C:()=>o.A7C,y8Q:()=>o.y8Q});var o=t(48648)},46637:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{$m:()=>f.$,$n:()=>s.$,EY:()=>j.E,In:()=>h.I,MJ:()=>l.MJ,Sc:()=>i.S,Tk:()=>v.T,aF:()=>u.aF,az:()=>a.a,cG:()=>n.c,cw:()=>p.c,dj:()=>w.d,jl:()=>g.j,lR:()=>c.l,mH:()=>y.m,pd:()=>b.p,rQ:()=>x.r,rS:()=>S.r,s_:()=>m.s,zt:()=>d.z});var a=t(45200),s=t(77502),i=t(76776),n=t(464),l=t(23678),c=t(63957),d=t(55197),h=t(50792),b=t(15376),u=t(75460),p=t(42929),m=t(7394),f=t(89164),g=t(87346),x=t(95148),y=t(12725),S=t(67981),j=t(87378),v=t(17335),w=t(5978),k=e([a,s,i,n,l,c,d,h,b,u,p,m,f,g,x,y,S,j,v,w]);[a,s,i,n,l,c,d,h,b,u,p,m,f,g,x,y,S,j,v,w]=k.then?(await k)():k,o()}catch(e){o(e)}})},53304:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{$n:()=>n.$,DZ:()=>d.D,Dr:()=>p.D,EY:()=>f.E,Ex:()=>s.E,IU:()=>u.I,In:()=>h.I,W1:()=>b.W,az:()=>i.a,cO:()=>m.c,eu:()=>a.e,so:()=>l.s,zt:()=>c.z});var a=t(36308),s=t(25392),i=t(45200),n=t(77502),l=t(9888),c=t(55197),d=t(30519),h=t(50792),b=t(3608),u=t(51317),p=t(35834),m=t(68801),f=t(87378),g=e([a,s,i,n,l,c,d,h,b,u,p,m,f]);[a,s,i,n,l,c,d,h,b,u,p,m,f]=g.then?(await g)():g,o()}catch(e){o(e)}})},54011:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{$m:()=>m.$,$n:()=>i.$,EY:()=>S.E,Fc:()=>a.F,In:()=>d.I,MJ:()=>n.MJ,TM:()=>j.T,Tk:()=>v.T,_0:()=>s._,aF:()=>b.aF,cw:()=>u.c,dj:()=>w.d,jl:()=>f.j,l6:()=>y.l,lR:()=>l.l,mH:()=>x.m,pd:()=>h.p,rQ:()=>g.r,s_:()=>p.s,zt:()=>c.z});var a=t(5128),s=t(31772),i=t(77502),n=t(23678),l=t(63957),c=t(55197),d=t(50792),h=t(15376),b=t(75460),u=t(42929),p=t(7394),m=t(89164),f=t(87346),g=t(95148),x=t(12725),y=t(29742),S=t(87378),j=t(37506),v=t(17335),w=t(5978),k=e([a,s,i,n,l,c,d,h,b,u,p,m,f,g,x,y,S,j,v,w]);[a,s,i,n,l,c,d,h,b,u,p,m,f,g,x,y,S,j,v,w]=k.then?(await k)():k,o()}catch(e){o(e)}})},56287:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{A:()=>x});var a=t(8732),s=t(3027),i=t(93876),n=t(95814),l=t(36281),c=t.n(l),d=t(4722),h=t(88358),b=t(82015),u=t(74715),p=t(31749),m=t(22685),f=t(3001),g=e([s,u,m,f]);[s,u,m,f]=g.then?(await g)():g;let y="1.0.0";function x(){let{data:e}=(0,d.useSession)(),r=(0,h.useRouter)(),t=e?.user?.isAdmin;e?.user?.id;let[o,l]=(0,b.useState)(!1),[g,x]=(0,b.useState)(!1),{displayName:S}=(0,u.A)(),{hasAccess:j,isLoading:v,isDeveloper:w,reason:k}=(0,p.default)(),{isOpen:C,onOpen:I,onClose:T}=(0,s.useDisclosure)(),{currentScheme:z}=(0,f.DP)(),A=[{name:"Overview",icon:i.V5Y,href:"/overview"},{name:"Applications",icon:i.est,href:"/applications"},{name:"Tickets",icon:i.lrG,href:"/tickets"},{name:"Game Servers",icon:i.ufi,href:"/gameservers"}],E=[{name:"Server Management",href:"/admin/guilds",icon:i.VSk},{name:"Applications",href:"/admin/applications",icon:n.t69},{name:"Applications Builder",href:"/admin/applications-builder",icon:i.est},{name:"Addons",href:"/admin/addons",icon:i.X3y},{name:"Commands",href:"/admin/commands",icon:i.JSe}],_=[{name:"Addon Builder",href:"/admin/experimental/addon-builder",icon:i.X3y},{name:"Feature Flags",href:"/admin/experimental/feature-flags",icon:n.XcJ},{name:"Beta Testing",href:"/admin/experimental/beta-testing",icon:n.XcJ}],D=e=>"/overview"===e?r.pathname===e:r.pathname.startsWith(e);return(0,a.jsxs)(s.Box,{as:"nav",h:"100%",bg:z.colors.surface,backdropFilter:"blur(20px)",borderRight:"1px solid",borderColor:z.colors.border,py:8,display:"flex",flexDirection:"column",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:`linear-gradient(180deg, ${z.colors.primary}15 0%, ${z.colors.accent}15 100%)`,zIndex:-1},children:[(0,a.jsxs)(s.VStack,{spacing:2,align:"stretch",flex:"1",children:[A.map(e=>{let r=D(e.href);return(0,a.jsx)(s.Tooltip,{label:e.name,placement:"right",hasArrow:!0,gutter:20,openDelay:500,display:{base:"block","2xl":"none"},children:(0,a.jsxs)(s.Link,{as:c(),href:e.href,display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:r?z.colors.text:z.colors.textSecondary,bg:r?`${z.colors.primary}30`:"transparent",_hover:{bg:r?`${z.colors.primary}40`:z.colors.surface,color:z.colors.text,transform:"translateX(4px)"},_active:{bg:`${z.colors.primary}50`},borderRight:r?"2px solid":"none",borderColor:r?z.colors.primary:"transparent",transition:"all 0.2s",role:"group",children:[(0,a.jsx)(s.Icon,{as:e.icon,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,a.jsx)(s.Text,{display:{base:"none",lg:"block"},bgGradient:r?`linear(to-r, ${z.colors.primaryLight}, ${z.colors.accent})`:"none",bgClip:r?"text":"none",transition:"all 0.2s",children:e.name})]})},e.name)}),t&&(0,a.jsxs)(s.VStack,{spacing:0,align:"stretch",children:[(0,a.jsxs)(s.Box,{display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:z.colors.textSecondary,bg:"transparent",_hover:{bg:z.colors.surface,color:z.colors.text,transform:"translateX(4px)"},transition:"all 0.2s",cursor:"pointer",role:"group",onClick:()=>l(!o),children:[(0,a.jsx)(s.Icon,{as:i.LIi,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,a.jsx)(s.Text,{display:{base:"none",lg:"block"},transition:"all 0.2s",flex:"1",children:"Admin"}),(0,a.jsx)(s.Icon,{as:i.fK4,w:4,h:4,ml:2,transition:"all 0.2s",transform:o?"rotate(180deg)":"rotate(0deg)",opacity:.6})]}),(0,a.jsx)(s.Collapse,{in:o,animateOpacity:!0,children:(0,a.jsx)(s.VStack,{spacing:1,align:"stretch",pl:4,py:2,children:E.map(e=>(0,a.jsxs)(s.Link,{as:c(),href:e.href,display:"flex",alignItems:"center",px:4,py:2,fontSize:"xs",fontWeight:"medium",color:D(e.href)?z.colors.text:z.colors.textSecondary,bg:D(e.href)?`${z.colors.primary}20`:"transparent",_hover:{bg:z.colors.surface,color:z.colors.text,transform:"translateX(2px)"},borderRadius:"md",transition:"all 0.2s",role:"group",children:[(0,a.jsx)(s.Icon,{as:e.icon,w:4,h:4,mr:2,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,a.jsx)(s.Text,{display:{base:"none",lg:"block"},children:e.name})]},e.href))})})]}),w&&(0,a.jsxs)(s.VStack,{spacing:0,align:"stretch",children:[(0,a.jsxs)(s.Box,{display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:z.colors.textSecondary,bg:"transparent",_hover:{bg:z.colors.surface,color:z.colors.text,transform:"translateX(4px)"},transition:"all 0.2s",cursor:"pointer",role:"group",onClick:()=>x(!g),children:[(0,a.jsx)(s.Icon,{as:n.XcJ,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,a.jsx)(s.Text,{display:{base:"none",lg:"block"},transition:"all 0.2s",flex:"1",bgGradient:"linear(to-r, purple.400, pink.400)",bgClip:"text",children:"Manage Experimental"}),(0,a.jsx)(s.Icon,{as:i.fK4,w:4,h:4,ml:2,transition:"all 0.2s",transform:g?"rotate(180deg)":"rotate(0deg)",opacity:.6})]}),(0,a.jsx)(s.Collapse,{in:g,animateOpacity:!0,children:(0,a.jsx)(s.VStack,{spacing:1,align:"stretch",pl:4,py:2,children:_.map(e=>(0,a.jsxs)(s.Link,{as:c(),href:e.href,display:"flex",alignItems:"center",px:4,py:2,fontSize:"xs",fontWeight:"medium",color:D(e.href)?z.colors.text:z.colors.textSecondary,bg:D(e.href)?`${z.colors.primary}20`:"transparent",_hover:{bg:z.colors.surface,color:z.colors.text,transform:"translateX(2px)"},borderRadius:"md",transition:"all 0.2s",role:"group",children:[(0,a.jsx)(s.Icon,{as:e.icon,w:4,h:4,mr:2,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,a.jsx)(s.Text,{display:{base:"none",lg:"block"},children:e.name})]},e.href))})})]})]}),!v&&!w&&(j||"open"===k)&&(0,a.jsxs)(s.Box,{px:4,mt:"auto",children:[(0,a.jsx)(s.Divider,{borderColor:z.colors.border,mb:4}),j?(0,a.jsx)(s.Button,{size:"sm",colorScheme:"yellow",variant:"ghost",leftIcon:(0,a.jsx)(s.Icon,{as:n.XcJ}),onClick:()=>r.push("/experimental"),w:"full",justifyContent:"flex-start",fontSize:"xs",_hover:{bg:z.colors.surface,transform:"translateX(2px)"},children:(0,a.jsx)(s.Text,{display:{base:"none",lg:"block"},children:"Experimental Features"})}):"open"===k?(0,a.jsxs)(s.VStack,{spacing:2,align:"stretch",children:[(0,a.jsxs)(s.Box,{bg:"rgba(236, 201, 75, 0.1)",border:"1px solid",borderColor:"yellow.400",borderRadius:"md",p:2,textAlign:"center",children:[(0,a.jsx)(s.Text,{fontSize:"xs",color:"yellow.300",fontWeight:"bold",bgGradient:"linear(to-r, yellow.200, orange.200)",bgClip:"text",children:"\uD83E\uDDEA Experimental Features"}),(0,a.jsx)(s.Text,{fontSize:"xs",color:"yellow.400",mt:1,children:"Apply Now • Response in ~1 week"})]}),(0,a.jsxs)(s.Button,{size:"sm",colorScheme:"yellow",variant:"outline",onClick:I,w:"full",fontSize:"xs",_hover:{bg:"yellow.400",color:"black",transform:"translateY(-1px)"},children:[(0,a.jsx)(s.Text,{display:{base:"none",lg:"block"},children:"Apply Now"}),(0,a.jsx)(s.Text,{display:{base:"block",lg:"none"},children:"Apply"})]})]}):null]}),(0,a.jsxs)(s.Box,{px:4,pt:4,...!v&&!w&&(j||"open"===k)?{}:{mt:"auto"},children:[(0,a.jsx)(s.Divider,{borderColor:z.colors.border,mb:4}),(0,a.jsx)(s.Text,{fontSize:"xs",color:z.colors.textSecondary,textAlign:"center",bgGradient:`linear(to-r, ${z.colors.primaryLight}, ${z.colors.accent})`,bgClip:"text",opacity:.7,_hover:{opacity:1,transform:"scale(1.05)"},transition:"all 0.2s",children:S?`${S} v${y}`:`Bot v${y}`})]}),(0,a.jsx)(m.A,{isOpen:C,onClose:T})]})}o()}catch(e){o(e)}})},66646:(e,r,t)=>{"use strict";t.d(r,{j:()=>o.j});var o=t(479)},69192:(e,r,t)=>{"use strict";t.d(r,{dashboardConfig:()=>l});var o=t(29021),a=t(72115),s=t.n(a),i=t(33873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let r=i.resolve(__dirname,"../../../config.yml");o.existsSync(r)&&(e=r)}if(!e)throw Error("config.yml not found");let r=o.readFileSync(e,"utf8");n=s().parse(r)}catch(e){process.exit(1)}let l={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};l.bot.token||process.exit(1),l.bot.clientId&&l.bot.clientSecret||process.exit(1),l.bot.guildId||process.exit(1),l.database.url&&l.database.name||process.exit(1)},72290:(e,r,t)=>{"use strict";t.d(r,{L:()=>n});var o=t(98580),a=t(12518);let s=null,i=null;async function n(){if(i)return i;let e=o.dashboardConfig.database?.url||"mongodb://localhost:27017",r=o.dashboardConfig.database?.name||"discord_bot";return s||(s=await a.MongoClient.connect(e,{...o.dashboardConfig.database?.options||{}})),i=s.db(r)}},72653:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{a:()=>a.a,m:()=>s.m});var a=t(45200),s=t(64304),i=e([a,s]);[a,s]=i.then?(await i)():i,o()}catch(e){o(e)}})},73427:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{A:()=>u});var a=t(8732),s=t(53304),i=t(4722),n=t(19097),l=t(79656),c=t(74715),d=t(31749),h=t(12087),b=e([s,c,h]);function u(){let{data:e}=(0,i.useSession)(),{displayName:r}=(0,c.A)(),{isLoading:t,isDeveloper:o,reason:b}=(0,d.default)(),u=r?`${r} Dashboard`:"Bot Dashboard";return(0,a.jsx)(s.az,{px:6,py:2,bg:"rgba(255,255,255,0.05)",backdropFilter:"blur(20px)",borderBottom:"1px solid",borderColor:"whiteAlpha.200",position:"sticky",top:0,zIndex:1e3,_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(159, 122, 234, 0.1))",zIndex:-1},children:(0,a.jsxs)(s.so,{h:16,alignItems:"center",justifyContent:"space-between",children:[(0,a.jsx)(s.az,{flex:"1",children:(0,a.jsx)(s.DZ,{as:"h1",fontSize:"xl",bgGradient:"linear(to-r, blue.400, purple.400)",bgClip:"text",_hover:{bgGradient:"linear(to-r, blue.300, purple.300)",transform:"scale(1.02)"},transition:"all 0.2s",children:u})}),!t&&!o&&"open"===b&&(0,a.jsx)(s.az,{flex:"2",display:"flex",justifyContent:"center",children:(0,a.jsxs)(s.zt,{spacing:2,bg:"rgba(236, 201, 75, 0.1)",border:"1px solid",borderColor:"yellow.400",borderRadius:"full",px:4,py:2,_hover:{bg:"rgba(236, 201, 75, 0.15)",transform:"scale(1.02)"},transition:"all 0.2s",children:[(0,a.jsx)(s.In,{as:l.XcJ,color:"yellow.300"}),(0,a.jsx)(s.EY,{fontSize:"sm",fontWeight:"bold",bgGradient:"linear(to-r, yellow.200, orange.200)",bgClip:"text",children:"Experimental features are open to applicants!"}),(0,a.jsx)(s.Ex,{colorScheme:"yellow",variant:"solid",fontSize:"xs",children:"NEW"})]})}),(0,a.jsx)(s.az,{flex:"1",display:"flex",justifyContent:"flex-end",children:(0,a.jsx)(s.so,{alignItems:"center",gap:4,children:e?.user?(0,a.jsxs)(s.zt,{spacing:4,children:[(0,a.jsx)(h.A,{}),(0,a.jsxs)(s.W1,{children:[(0,a.jsx)(s.IU,{as:s.$n,variant:"ghost",size:"sm",px:2,py:1,borderRadius:"full",_hover:{bg:"whiteAlpha.200"},children:(0,a.jsxs)(s.zt,{spacing:2,children:[(0,a.jsx)(s.eu,{size:"sm",name:e.user.name??void 0,src:e.user.image??void 0,borderWidth:2,borderColor:"blue.400",_hover:{borderColor:"purple.400",transform:"scale(1.05)"},transition:"all 0.2s"}),(0,a.jsx)(s.EY,{color:"gray.300",display:{base:"none",md:"block"},children:e.user.name})]})}),(0,a.jsx)(s.cO,{bg:"gray.800",borderColor:"whiteAlpha.200",boxShadow:"lg",_hover:{borderColor:"blue.400"},children:(0,a.jsx)(s.Dr,{icon:(0,a.jsx)(n.QeK,{}),onClick:()=>(0,i.signOut)(),_hover:{bg:"whiteAlpha.200",color:"red.400"},children:"Sign out"})})]})]}):(0,a.jsx)(s.$n,{onClick:()=>(0,i.signIn)("discord",{callbackUrl:"/overview"}),bgGradient:"linear(to-r, blue.500, purple.500)",color:"white",_hover:{bgGradient:"linear(to-r, blue.400, purple.400)",transform:"translateY(-1px)"},_active:{bgGradient:"linear(to-r, blue.600, purple.600)",transform:"translateY(1px)"},transition:"all 0.2s",children:"Login with Discord"})})})]})})}[s,c,h]=b.then?(await b)():b,o()}catch(e){o(e)}})},73907:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{o:()=>s.oY,s:()=>a.s});var a=t(39323),s=t(12530),i=e([a]);a=(i.then?(await i)():i)[0],o()}catch(e){o(e)}})},74715:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{A:()=>l});var a=t(14078),s=t(82015),i=t(4722),n=e([a]);a=(n.then?(await n)():n)[0];let c=async e=>{let r=await fetch(e);if(!r.ok){if(401===r.status)return{name:"404 Bot",botName:"404 Bot"};throw Error("Failed to fetch guild info")}return r.json()};function l(){let{data:e,status:r}=(0,i.useSession)(),t="authenticated"===r,{data:o,error:n}=(0,a.default)(t?"/api/discord/guild":null,c,{revalidateOnFocus:!1,revalidateOnReconnect:!1}),[l,d]=(0,s.useState)(()=>"guild"),h=(0,s.useCallback)(e=>{d(e)},[]),b="404 Bot Dashboard",u=b;return o&&(u="bot"===l&&o.botName?o.botName:o.name||b),{guild:o,displayName:u,pref:l,updatePreference:h,isLoading:t&&!n&&!o,isError:!!n}}o()}catch(e){o(e)}})},76732:(e,r,t)=>{"use strict";t.r(r),t.d(r,{logDatabaseOperation:()=>i});var o=t(12518);let{url:a,name:s}=t(98580).dashboardConfig.database;async function i(e,r,t){let i=null;try{let n=(i=await o.MongoClient.connect(a)).db(s).collection("database_logs");await n.insertOne({timestamp:new Date,operation:e,collection:r,details:t,id:new Date().getTime().toString()})}catch(e){}finally{i&&await i.close()}}},79135:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});let o=(0,t(12530).oY)({config:{initialColorMode:"dark",useSystemColorMode:!1},fonts:{heading:"'Inter', sans-serif",body:"'Inter', sans-serif"},colors:{brand:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95"},discord:{50:"#e8e9fd",100:"#d1d3fc",200:"#b9bcfa",300:"#a2a5f9",400:"#8b8ef7",500:"#5865F2",600:"#4752c4",700:"#363f97",800:"#242c69",900:"#12193c"}},styles:{global:{body:{bg:"gray.900",color:"gray.100"}}},components:{Button:{defaultProps:{colorScheme:"brand"},variants:{solid:e=>({bg:`${e.colorScheme}.500`,color:"white",_hover:{bg:`${e.colorScheme}.600`,transform:"translateY(-2px)",boxShadow:"lg"},_active:{bg:`${e.colorScheme}.700`,transform:"translateY(0)"},transition:"all 0.2s ease"})}},Link:{baseStyle:{_hover:{textDecoration:"none"}}},Box:{baseStyle:{transition:"all 0.2s ease"}}}})},79656:(e,r,t)=>{"use strict";t.d(r,{XcJ:()=>o.XcJ});var o=t(48648)},80541:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{AM:()=>n.A,DI:()=>d.D,EY:()=>b.E,Ex:()=>a.E,K0:()=>i.K,Tk:()=>p.T,Wv:()=>h.W,az:()=>s.a,ej:()=>l.e,hl:()=>c.h,m_:()=>u.m});var a=t(25392),s=t(45200),i=t(23476),n=t(63280),l=t(58253),c=t(88192),d=t(16656),h=t(78053),b=t(87378),u=t(63792),p=t(17335),m=e([a,s,i,n,l,c,d,b,u,p]);[a,s,i,n,l,c,d,b,u,p]=m.then?(await m)():m,o()}catch(e){o(e)}})},81011:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{A:()=>d});var a=t(8732),s=t(72653),i=t(73427),n=t(56287),l=t(3001),c=e([s,i,n,l]);[s,i,n,l]=c.then?(await c)():c;let d=7311==t.j?({children:e})=>{let{currentScheme:r}=(0,l.DP)();return(0,a.jsx)(s.a,{minH:"100vh",bg:r.colors.background,position:"relative",overflow:"hidden",_before:{content:'""',position:"fixed",top:0,left:0,right:0,bottom:0,bgImage:`
          radial-gradient(circle at 15% 50%, ${r.colors.primary}15 0%, transparent 25%),
          radial-gradient(circle at 85% 30%, ${r.colors.accent}15 0%, transparent 25%)
        `,zIndex:0},_after:{content:'""',position:"fixed",top:0,left:0,right:0,bottom:0,backdropFilter:"blur(100px)",zIndex:0},children:(0,a.jsxs)(s.a,{position:"relative",zIndex:1,display:"flex",flexDirection:"column",minH:"100vh",children:[(0,a.jsx)(s.a,{position:"fixed",top:0,left:0,right:0,zIndex:30,children:(0,a.jsx)(i.A,{})}),(0,a.jsxs)(s.a,{display:"flex",flex:"1",position:"relative",pt:"4rem",children:[(0,a.jsx)(s.a,{position:"fixed",top:"4rem",bottom:0,left:0,w:"64",zIndex:20,children:(0,a.jsx)(n.A,{})}),(0,a.jsx)(s.a,{flex:"1",ml:"64",p:{base:4,md:8},maxW:"100%",transition:"all 0.3s",position:"relative",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(135deg, rgba(255,255,255,0.03) 0%, transparent 100%)",pointerEvents:"none",zIndex:-1},children:(0,a.jsx)(s.m,{maxW:"container.xl",children:e})})]})]})})}:null;o()}catch(e){o(e)}})},83567:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var o=t(8732),a=t(28270);function s(){return(0,o.jsxs)(a.Html,{lang:"en",children:[(0,o.jsx)(a.Head,{}),(0,o.jsxs)("body",{children:[(0,o.jsx)(a.Main,{}),(0,o.jsx)(a.NextScript,{})]})]})}},84606:(e,r,t)=>{"use strict";t.d(r,{zd:()=>o.FiBell});var o=t(64960)},87615:(e,r,t)=>{"use strict";t.d(r,{cfS:()=>o.FiUsers,mEP:()=>o.FiMessageSquare,o77:()=>o.FiVolume2,pcC:()=>o.FiShield});var o=t(64960)},92546:(e,r,t)=>{"use strict";t.d(r,{N:()=>l});var o=t(65542),a=t.n(o),s=t(20396),i=t.n(s),n=t(69192);let l={providers:[i()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:r,profile:t})=>(r&&t&&(e.accessToken=r.access_token||null,e.id=t.id||null),e),async session({session:e,token:r}){if(e?.user){let t=r.id||null,o=r.accessToken||null;e.user.id=t,e.user.accessToken=o;let a=!1;if(t)if((n.dashboardConfig.dashboard.admins||[]).includes(t))a=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let r=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${t}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(r.ok){let t=await r.json();a=e.some(e=>t.roles?.includes(e))||!1}else await r.text()}catch(e){}}e.user.isAdmin=a,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:r}){let t=new URL(r),o=`${t.protocol}//localhost${t.port?`:${t.port}`:""}`;return e.startsWith(r)||e.startsWith(o)?e:r}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,r)=>{},warn:e=>{},debug:(e,r)=>{}}};a()(l)},93876:(e,r,t)=>{"use strict";t.d(r,{JSe:()=>o.FiCommand,LIi:()=>o.FiServer,V5Y:()=>o.FiHome,VSk:()=>o.FiSettings,X3y:()=>o.FiBox,est:()=>o.FiPackage,fK4:()=>o.FiChevronDown,lrG:()=>o.FiHelpCircle,ufi:()=>o.FiMonitor});var o=t(64960)},94506:(e,r,t)=>{"use strict";t.r(r),t.d(r,{authOptions:()=>l,default:()=>c});var o=t(65542),a=t.n(o),s=t(20396),i=t.n(s),n=t(98580);let l={providers:[i()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:r,profile:t})=>(r&&t&&(e.accessToken=r.access_token||null,e.id=t.id||null),e),async session({session:e,token:r}){if(e?.user){let t=r.id||null,o=r.accessToken||null;e.user.id=t,e.user.accessToken=o;let a=!1;if(t)if((n.dashboardConfig.dashboard.admins||[]).includes(t))a=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let r=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${t}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(r.ok){let t=await r.json();a=e.some(e=>t.roles?.includes(e))||!1}else await r.text()}catch(e){}}e.user.isAdmin=a,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:r}){let t=new URL(r),o=`${t.protocol}//localhost${t.port?`:${t.port}`:""}`;return e.startsWith(r)||e.startsWith(o)?e:r}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,r)=>{},warn:e=>{},debug:(e,r)=>{}}},c=a()(l)},95814:(e,r,t)=>{"use strict";t.d(r,{XcJ:()=>o.XcJ,t69:()=>o.t69});var o=t(48648)},98580:(e,r,t)=>{"use strict";t.r(r),t.d(r,{dashboardConfig:()=>l,default:()=>c});var o=t(29021),a=t(72115),s=t.n(a),i=t(33873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let r=i.resolve(__dirname,"../../../config.yml");o.existsSync(r)&&(e=r)}if(!e)throw Error("config.yml not found");let r=o.readFileSync(e,"utf8");n=s().parse(r)}catch(e){process.exit(1)}let l={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};l.bot.token||process.exit(1),l.bot.clientId&&l.bot.clientSecret||process.exit(1),l.bot.guildId||process.exit(1),l.database.url&&l.database.name||process.exit(1);let c=8456==t.j?l:null}};