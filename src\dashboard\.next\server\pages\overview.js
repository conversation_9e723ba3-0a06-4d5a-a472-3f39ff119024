"use strict";(()=>{var e={};e.id=5449,e.ids=[636,3220,5449],e.modules={4722:e=>{e.exports=require("next-auth/react")},7167:(e,r,t)=>{t.d(r,{H:()=>i});var o=t(64960);let i=[{id:"overview",title:"Overview",description:"View server statistics and general information.",icon:o.FiActivity,href:"/overview",color:"blue",gradient:{from:"rgba(49, 130, 206, 0.4)",to:"rgba(49, 130, 206, 0.1)"},accentColor:"#63B3ED"},{id:"gameservers",title:"Game Servers",description:"Manage and monitor your game servers. View status, add or edit server configurations.",icon:o.FiMonitor,href:"/gameservers",color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accentColor:"#68D391"},{id:"applications",title:"Applications",description:"Review and manage guild applications. Process new members and handle requests.",icon:o.<PERSON>ackage,href:"/applications",color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"},accentColor:"#B794F4"},{id:"tickets",title:"Support Tickets",description:"Track and manage support tickets. Respond to user inquiries and resolve issues.",icon:o.FiHelpCircle,href:"/tickets",color:"orange",gradient:{from:"rgba(237, 137, 54, 0.4)",to:"rgba(237, 137, 54, 0.1)"},accentColor:"#F6AD55"},{id:"moderation",title:"Moderation",description:"Tools and features for server moderators.",icon:o.FiLock,href:"/moderation",color:"teal",gradient:{from:"rgba(49, 151, 149, 0.4)",to:"rgba(49, 151, 149, 0.1)"},accentColor:"#4FD1C5",requiredRole:"moderator"},{id:"experimental",title:"Experimental Features",description:"Try out new features that are still in development. These may not work as expected.",icon:o.FiSettings,href:"#",color:"yellow",gradient:{from:"rgba(236, 201, 75, 0.4)",to:"rgba(236, 201, 75, 0.1)"},accentColor:"#F6E05E",experimental:!0,disabled:!0}]},8732:e=>{e.exports=require("react/jsx-runtime")},14078:e=>{e.exports=import("swr")},15190:(e,r,t)=>{t.d(r,{Es:()=>i.E,Fq:()=>n.F,WX:()=>h.W,dC:()=>a.d,fh:()=>s.f,h8:()=>p.h,m_:()=>d.m,rW:()=>l.r,uf:()=>c.u,yP:()=>o.y});var o=t(73262),i=t(28162),a=t(39202),s=t(44581),n=t(49528),l=t(62157),c=t(79332),d=t(6776),h=t(94643),p=t(16742)},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},22326:e=>{e.exports=require("react-dom")},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},30067:e=>{e.exports=import("@emotion/styled")},33873:e=>{e.exports=require("path")},34682:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>h,getServerSideProps:()=>u,getStaticPaths:()=>x,getStaticProps:()=>p,reportWebVitals:()=>m,routeModule:()=>w,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>b});var i=t(1292),a=t(58834),s=t(40786),n=t(83567),l=t(8077),c=t(42878),d=e([l,c]);[l,c]=d.then?(await d)():d;let h=(0,s.M)(c,"default"),p=(0,s.M)(c,"getStaticProps"),x=(0,s.M)(c,"getStaticPaths"),u=(0,s.M)(c,"getServerSideProps"),g=(0,s.M)(c,"config"),m=(0,s.M)(c,"reportWebVitals"),b=(0,s.M)(c,"unstable_getStaticProps"),f=(0,s.M)(c,"unstable_getStaticPaths"),j=(0,s.M)(c,"unstable_getStaticParams"),v=(0,s.M)(c,"unstable_getServerProps"),y=(0,s.M)(c,"unstable_getServerSideProps"),w=new i.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/overview",pathname:"/overview",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:c});o()}catch(e){o(e)}})},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42878:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{default:()=>y,getServerSideProps:()=>w});var i=t(8732),a=t(93335),s=t(74044),n=t(81011),l=t(15806),c=t(92546),d=t(7167),h=t(88455),p=t(82015),x=t(15190),u=t(4722),g=t(67672),m=t(31749),b=t(36281),f=t.n(b),j=t(70525),v=e([a,n,h,g,j]);function y(){let{data:e}=(0,u.useSession)(),[r,t]=(0,p.useState)(null),[o,l]=(0,p.useState)(!0);(0,a.dj)();let{hasAccess:c,isDeveloper:h,isLoading:b}=(0,m.default)(),v=['"Talk is cheap. Show me the code." – Linus Torvalds','"Programs must be written for people to read, and only incidentally for machines to execute." – Harold Abelson','"Any fool can write code that a computer can understand. Good programmers write code that humans can understand." – Martin Fowler','"First, solve the problem. Then, write the code." – John Johnson','"404 Chill Not Found? Keep calm and debug on." – Unknown',"It's not a bug – it's an undocumented feature.",'"The best error message is the one that never shows up." – Thomas Fuchs',"Code is like humor. When you have to explain it, it's bad.",'"Experience is the name everyone gives to their mistakes." – Oscar Wilde','"In order to be irreplaceable, one must always be different." – Coco Chanel'];v[new Date().getDate()%v.length];let y=d.H.filter(r=>"admin"===r.requiredRole?e?.user?.isAdmin:"moderator"===r.requiredRole?e?.user?.isModerator:"overview"!==r.id&&"experimental"!==r.id),w=r?[{name:"Text",value:r.serverStats.textChannels||0,color:"#4299E1"},{name:"Voice",value:r.serverStats.voiceChannels||0,color:"#48BB78"},{name:"Categories",value:r.serverStats.categories||0,color:"#9F7AEA"}]:[],S=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],k=S.map(e=>({day:e,commands:0,joins:0,leaves:0})),z=r?S.map(e=>{let t=r.botStats?.weeklyActivity?.find(r=>r.day===e)||{},o=r.serverStats?.weeklyMembers?.find(r=>r.day===e)||{};return{day:e,commands:t.commands||0,joins:o.joins||0,leaves:o.leaves||0}}):k;return(0,i.jsx)(n.A,{children:(0,i.jsxs)(a.az,{p:8,position:"relative",_before:{content:'""',position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"100%",height:"100%",background:"radial-gradient(circle at center, rgba(66, 153, 225, 0.1) 0%, transparent 70%)",pointerEvents:"none"},children:[(0,i.jsx)(a.az,{position:"absolute",top:0,left:0,width:"100%",height:"100%",pointerEvents:"none",opacity:.05,sx:{"@keyframes glow":{"0%":{opacity:.03},"50%":{opacity:.07},"100%":{opacity:.03}},animation:"glow 4s infinite"},fontSize:"3xl",fontFamily:"monospace",color:"blue.200",textAlign:"center",pt:20,children:"ORACLE"}),(0,i.jsxs)(a.Tk,{spacing:8,mb:8,children:[(0,i.jsxs)(a.DZ,{size:"lg",textAlign:"center",display:"flex",alignItems:"center",justifyContent:"center",children:[(0,i.jsx)("span",{style:{marginRight:"0.5rem"},role:"img","aria-label":"chart",children:"\uD83D\uDCCA"}),(0,i.jsx)(a.az,{as:"span",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",children:"Server Analytics"})]}),(0,i.jsx)(a.rS,{columns:{base:1,md:2,lg:4},spacing:6,w:"full",children:o?Array.from({length:4}).map((e,r)=>(0,i.jsx)(a.Zp,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.bw,{children:(0,i.jsx)(a.EA,{height:"80px"})})},r)):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Zp,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.bw,{children:(0,i.jsxs)(a.ro,{children:[(0,i.jsxs)(a.zt,{children:[(0,i.jsx)(a.In,{as:s.cfS,color:"blue.400",boxSize:6}),(0,i.jsx)(a.v0,{color:"gray.300",children:"Total Members"})]}),(0,i.jsx)(a.km,{color:"white",fontSize:"2xl",children:r?.serverStats.totalMembers.toLocaleString()||"0"}),(0,i.jsxs)(a.h7,{color:"green.400",children:[(0,i.jsx)(a.In,{as:s.ARf,mr:1}),r?.serverStats.onlineMembers||"0"," online"]}),(0,i.jsxs)(a.h7,{color:"green.300",children:["+",r?.serverStats.newMembersToday||0," joined"]}),(0,i.jsxs)(a.h7,{color:"red.400",children:["-",r?.serverStats.leftMembersToday||0," left"]})]})})}),(0,i.jsx)(a.Zp,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.bw,{children:(0,i.jsxs)(a.ro,{children:[(0,i.jsxs)(a.zt,{children:[(0,i.jsx)(a.In,{as:s.mEP,color:"green.400",boxSize:6}),(0,i.jsx)(a.v0,{color:"gray.300",children:"Channels"})]}),(0,i.jsx)(a.km,{color:"white",fontSize:"2xl",children:r?.serverStats.totalChannels||"0"}),(0,i.jsxs)(a.h7,{color:"gray.400",children:[r?.serverStats.totalRoles||"0"," roles"]})]})})}),(0,i.jsx)(a.Zp,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.bw,{children:(0,i.jsxs)(a.ro,{children:[(0,i.jsxs)(a.zt,{children:[(0,i.jsx)(a.In,{as:s.z1n,color:"purple.400",boxSize:6}),(0,i.jsx)(a.v0,{color:"gray.300",children:"Commands Today"})]}),(0,i.jsx)(a.km,{color:"white",fontSize:"2xl",children:r?.botStats.commandsToday||"0"}),(0,i.jsxs)(a.h7,{color:"gray.400",children:[r?.botStats.responseTime||"0ms"," avg response"]})]})})}),(0,i.jsx)(a.Zp,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.bw,{children:(0,i.jsxs)(a.ro,{children:[(0,i.jsxs)(a.zt,{children:[(0,i.jsx)(a.In,{as:s.LIi,color:"orange.400",boxSize:6}),(0,i.jsx)(a.v0,{color:"gray.300",children:"Bot Uptime"})]}),(0,i.jsx)(a.km,{color:"white",fontSize:"xl",children:r?.botStats.uptime||"Unknown"}),(0,i.jsxs)(a.h7,{color:"green.400",children:[r?.botStats.activeAddons||"0"," addons active"]}),(0,i.jsxs)(a.h7,{color:"red.400",children:[r?.botStats.inactiveAddons||"0"," addons inactive"]})]})})}),r?.botStats.errorsToday>0&&(0,i.jsx)(j.N,{as:f(),href:"/admin/errors",_hover:{textDecoration:"none"},w:"full",children:(0,i.jsx)(a.Zp,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",borderColor:"red.400",borderWidth:"1px",cursor:"pointer",_hover:{transform:"translateY(-4px)",boxShadow:"0 4px 12px rgba(0,0,0,0.2)",borderColor:"red.500"},children:(0,i.jsx)(a.bw,{children:(0,i.jsxs)(a.ro,{children:[(0,i.jsxs)(a.zt,{children:[(0,i.jsx)(a.In,{as:s.y3G,color:"red.400",boxSize:6}),(0,i.jsx)(a.v0,{color:"gray.300",children:"Errors Today"})]}),(0,i.jsx)(a.km,{color:"red.400",fontSize:"2xl",children:r.botStats.errorsToday}),(0,i.jsx)(a.h7,{color:"red.300",children:"Needs attention"})]})})})})]})})]}),!o&&r&&(0,i.jsxs)(a.Tk,{spacing:8,mb:8,children:[(0,i.jsxs)(a.DZ,{size:"lg",textAlign:"center",display:"flex",alignItems:"center",justifyContent:"center",children:[(0,i.jsx)("span",{style:{marginRight:"0.5rem"},role:"img","aria-label":"graph",children:"\uD83D\uDCC8"}),(0,i.jsx)(a.az,{as:"span",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",children:"Activity Overview"})]}),(0,i.jsxs)(a.rS,{columns:{base:1,lg:2},spacing:8,w:"full",children:[(0,i.jsx)(a.Zp,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.bw,{children:(0,i.jsxs)(a.Tk,{spacing:4,children:[(0,i.jsx)(a.DZ,{size:"md",color:"white",children:"Channel Distribution"}),(0,i.jsx)(a.az,{h:"200px",w:"full",children:(0,i.jsx)(x.uf,{width:"100%",height:"100%",children:(0,i.jsxs)(x.rW,{children:[(0,i.jsx)(x.Fq,{data:w,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:5,dataKey:"value",children:w.map((e,r)=>(0,i.jsx)(x.fh,{fill:e.color},`cell-${r}`))}),(0,i.jsx)(x.m_,{wrapperStyle:{backgroundColor:"transparent"},contentStyle:{backgroundColor:"rgba(26, 32, 44, 0.9)",border:"1px solid rgba(255,255,255,0.2)",borderRadius:"8px",color:"#fff"},itemStyle:{color:"#fff"},labelStyle:{color:"#fff"}})]})})}),(0,i.jsx)(a.zt,{spacing:4,justify:"center",children:w.map((e,r)=>(0,i.jsxs)(a.zt,{spacing:2,children:[(0,i.jsx)(a.az,{w:"3",h:"3",bg:e.color,rounded:"full"}),(0,i.jsxs)(a.EY,{fontSize:"sm",color:"gray.300",children:[e.name,": ",e.value]})]},r))})]})})}),(0,i.jsx)(a.Zp,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,i.jsx)(a.bw,{children:(0,i.jsxs)(a.Tk,{spacing:4,children:[(0,i.jsx)(a.DZ,{size:"md",color:"white",children:"Weekly Activity"}),(0,i.jsx)(a.az,{h:"200px",w:"full",children:(0,i.jsx)(x.uf,{width:"100%",height:"100%",children:(0,i.jsxs)(x.Es,{data:z,children:[(0,i.jsx)(x.dC,{strokeDasharray:"3 3",stroke:"rgba(255,255,255,0.1)"}),(0,i.jsx)(x.WX,{dataKey:"day",axisLine:!1,tickLine:!1,tick:{fill:"#A0AEC0",fontSize:12}}),(0,i.jsx)(x.h8,{axisLine:!1,tickLine:!1,tick:{fill:"#A0AEC0",fontSize:12}}),(0,i.jsx)(x.m_,{wrapperStyle:{backgroundColor:"transparent"},contentStyle:{backgroundColor:"rgba(26, 32, 44, 0.9)",border:"1px solid rgba(255,255,255,0.2)",borderRadius:"8px",color:"#fff"},itemStyle:{color:"#fff"},labelStyle:{color:"#fff"},cursor:{fill:"rgba(255,255,255,0.08)"}}),(0,i.jsx)(x.yP,{dataKey:"commands",fill:"#4299E1",name:"Commands"}),(0,i.jsx)(x.yP,{dataKey:"joins",fill:"#48BB78",name:"Joins"}),(0,i.jsx)(x.yP,{dataKey:"leaves",fill:"#E53E3E",name:"Leaves"})]})})})]})})})]})]}),(0,i.jsx)(a.B_,{spacing:"24px",justify:"start",children:y.map(e=>(0,i.jsx)(a.Qe,{flex:"1 0 260px",children:(0,i.jsx)(a.az,{onClick:()=>window.dispatchEvent(new CustomEvent("colorClick",{detail:e.color})),children:(0,i.jsx)(g.o,{...e})})},e.id))})]})})}[a,n,h,g,j]=v.then?(await v)():v,(0,h.keyframes)`
  0% { opacity: 0.3; }
  50% { opacity: 0.7; }
  100% { opacity: 0.3; }
`;let w=async e=>await (0,l.getServerSession)(e.req,e.res,c.N)?{props:{}}:{redirect:{destination:"/signin",permanent:!1}};o()}catch(e){o(e)}})},65542:e=>{e.exports=require("next-auth")},67672:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.d(r,{o:()=>c});var i=t(8732);t(82015);var a=t(89600),s=t(36281),n=t.n(s),l=e([a]);a=(l.then?(await l)():l)[0];let c=({title:e,description:r,icon:t,href:o,color:s,gradient:l,accentColor:c,disabled:d=!1,experimental:h=!1})=>{let p=o&&"#"!==o&&!d,x=(0,i.jsx)(a.az,{px:10,py:5,bg:l?`linear-gradient(135deg, ${l.from}, ${l.to})`:"gray.800",borderRadius:"lg",border:"1px solid",borderColor:d?"whiteAlpha.100":"whiteAlpha.200",transition:"all 0.3s",h:"140px",minW:"360px",w:"full",overflow:"hidden",display:"flex",flexDirection:"column",cursor:p?"pointer":d?"not-allowed":"default",position:"relative",opacity:d?.6:1,_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:h?"repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0,0,0,0.1) 10px, rgba(0,0,0,0.1) 20px)":"none",opacity:.5,pointerEvents:"none"},_hover:p?{transform:"translateY(-3px)",boxShadow:`0 6px 14px ${c||`var(--chakra-colors-${s}-900)`}40`,borderColor:`${s}.400`,_before:{opacity:.7}}:{},children:(0,i.jsxs)(a.Tk,{spacing:4,align:"start",flex:"1",justify:"flex-start",h:"full",position:"relative",zIndex:1,children:[(0,i.jsxs)(a.zt,{spacing:3,children:[(0,i.jsx)(a.In,{as:t,boxSize:6,color:c||`${s}.300`,filter:h?"drop-shadow(0 0 2px currentColor)":"none"}),(0,i.jsx)(a.DZ,{size:"md",color:"white",noOfLines:1,whiteSpace:"nowrap",children:e})]}),(0,i.jsx)(a.EY,{color:d?"gray.500":"gray.300",fontSize:"sm",lineHeight:"1.4",noOfLines:3,overflow:"hidden",textOverflow:"ellipsis",flex:"1",children:r})]})});return p?(0,i.jsx)(n(),{href:o,passHref:!0,children:x}):x};o()}catch(e){o(e)}})},70525:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.d(r,{N:()=>i.N});var i=t(84802),a=e([i]);i=(a.then?(await a)():a)[0],o()}catch(e){o(e)}})},72115:e=>{e.exports=require("yaml")},74044:(e,r,t)=>{t.d(r,{ARf:()=>o.FiTrendingUp,LIi:()=>o.FiServer,cfS:()=>o.FiUsers,mEP:()=>o.FiMessageSquare,y3G:()=>o.FiAlertCircle,z1n:()=>o.FiActivity});var o=t(64960)},74075:e=>{e.exports=require("zlib")},82015:e=>{e.exports=require("react")},88455:e=>{e.exports=import("@emotion/react")},89600:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.d(r,{DZ:()=>s.D,EY:()=>l.E,In:()=>n.I,Tk:()=>c.T,az:()=>i.a,zt:()=>a.z});var i=t(45200),a=t(55197),s=t(30519),n=t(50792),l=t(87378),c=t(17335),d=e([i,a,s,n,l,c]);[i,a,s,n,l,c]=d.then?(await d)():d,o()}catch(e){o(e)}})},93335:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.d(r,{B_:()=>f.B,DZ:()=>l.D,EA:()=>h.E,EY:()=>m.E,In:()=>c.I,Qe:()=>f.Q,Tk:()=>b.T,Zp:()=>a.Z,az:()=>i.a,bw:()=>s.b,dj:()=>j.d,h7:()=>x.h,km:()=>g.k,rS:()=>d.r,ro:()=>p.r,v0:()=>u.v,zt:()=>n.z});var i=t(45200),a=t(90846),s=t(60615),n=t(55197),l=t(30519),c=t(50792),d=t(67981),h=t(45792),p=t(33593),x=t(58018),u=t(71577),g=t(42650),m=t(87378),b=t(17335),f=t(64426),j=t(5978),v=e([i,a,s,n,l,c,d,h,p,x,u,g,m,b,f,j]);[i,a,s,n,l,c,d,h,p,x,u,g,m,b,f,j]=v.then?(await v)():v,o()}catch(e){o(e)}})}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[2457,9784,6021,3786,8740,9498,2142,1283,589,6185,4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(34682));module.exports=o})();