(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8360],{29027:(e,t,n)=>{"use strict";var r=n(34544);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,i,a){if(a!==r){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}},34544:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},68688:(e,t,n)=>{"use strict";n.d(t,{n4:()=>eo});var r="bottom",o="right",i="left",a="auto",s=["top",r,o,i],f="start",c="viewport",p="popper",u=s.reduce(function(e,t){return e.concat([t+"-"+f,t+"-end"])},[]),l=[].concat(s,[a]).reduce(function(e,t){return e.concat([t,t+"-"+f,t+"-end"])},[]),d=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function h(e){return e?(e.nodeName||"").toLowerCase():null}function m(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function v(e){var t=m(e).Element;return e instanceof t||e instanceof Element}function y(e){var t=m(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function g(e){if("undefined"==typeof ShadowRoot)return!1;var t=m(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function b(e){return e.split("-")[0]}var x=Math.max,w=Math.min,O=Math.round;function E(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function j(){return!/^((?!chrome|android).)*safari/i.test(E())}function D(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,i=1;t&&y(e)&&(o=e.offsetWidth>0&&O(r.width)/e.offsetWidth||1,i=e.offsetHeight>0&&O(r.height)/e.offsetHeight||1);var a=(v(e)?m(e):window).visualViewport,s=!j()&&n,f=(r.left+(s&&a?a.offsetLeft:0))/o,c=(r.top+(s&&a?a.offsetTop:0))/i,p=r.width/o,u=r.height/i;return{width:p,height:u,top:c,right:f+p,bottom:c+u,left:f,x:f,y:c}}function A(e){var t=D(e),n=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(t.width-n)&&(n=t.width),1>=Math.abs(t.height-r)&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function k(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&g(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function P(e){return m(e).getComputedStyle(e)}function T(e){return((v(e)?e.ownerDocument:e.document)||window.document).documentElement}function L(e){return"html"===h(e)?e:e.assignedSlot||e.parentNode||(g(e)?e.host:null)||T(e)}function W(e){return y(e)&&"fixed"!==P(e).position?e.offsetParent:null}function R(e){for(var t=m(e),n=W(e);n&&["table","td","th"].indexOf(h(n))>=0&&"static"===P(n).position;)n=W(n);return n&&("html"===h(n)||"body"===h(n)&&"static"===P(n).position)?t:n||function(e){var t=/firefox/i.test(E());if(/Trident/i.test(E())&&y(e)&&"fixed"===P(e).position)return null;var n=L(e);for(g(n)&&(n=n.host);y(n)&&0>["html","body"].indexOf(h(n));){var r=P(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function M(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function B(e,t,n){return x(e,w(t,n))}function H(){return{top:0,right:0,bottom:0,left:0}}function C(e){return Object.assign({},H(),e)}function S(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}function _(e){return e.split("-")[1]}var V={top:"auto",right:"auto",bottom:"auto",left:"auto"};function q(e){var t,n,a,s,f,c,p,u=e.popper,l=e.popperRect,d=e.placement,h=e.variation,v=e.offsets,y=e.position,g=e.gpuAcceleration,b=e.adaptive,x=e.roundOffsets,w=e.isFixed,E=v.x,j=void 0===E?0:E,D=v.y,A=void 0===D?0:D,k="function"==typeof x?x({x:j,y:A}):{x:j,y:A};j=k.x,A=k.y;var L=v.hasOwnProperty("x"),W=v.hasOwnProperty("y"),M=i,B="top",H=window;if(b){var C=R(u),S="clientHeight",_="clientWidth";C===m(u)&&"static"!==P(C=T(u)).position&&"absolute"===y&&(S="scrollHeight",_="scrollWidth"),("top"===d||(d===i||d===o)&&"end"===h)&&(B=r,A-=(w&&C===H&&H.visualViewport?H.visualViewport.height:C[S])-l.height,A*=g?1:-1),(d===i||("top"===d||d===r)&&"end"===h)&&(M=o,j-=(w&&C===H&&H.visualViewport?H.visualViewport.width:C[_])-l.width,j*=g?1:-1)}var q=Object.assign({position:y},b&&V),N=!0===x?(t={x:j,y:A},n=m(u),a=t.x,s=t.y,{x:O(a*(f=n.devicePixelRatio||1))/f||0,y:O(s*f)/f||0}):{x:j,y:A};return(j=N.x,A=N.y,g)?Object.assign({},q,((p={})[B]=W?"0":"",p[M]=L?"0":"",p.transform=1>=(H.devicePixelRatio||1)?"translate("+j+"px, "+A+"px)":"translate3d("+j+"px, "+A+"px, 0)",p)):Object.assign({},q,((c={})[B]=W?A+"px":"",c[M]=L?j+"px":"",c.transform="",c))}var N={passive:!0},I={left:"right",right:"left",bottom:"top",top:"bottom"};function U(e){return e.replace(/left|right|bottom|top/g,function(e){return I[e]})}var F={start:"end",end:"start"};function z(e){return e.replace(/start|end/g,function(e){return F[e]})}function Y(e){var t=m(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function X(e){return D(T(e)).left+Y(e).scrollLeft}function G(e){var t=P(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function J(e,t){void 0===t&&(t=[]);var n,r=function e(t){return["html","body","#document"].indexOf(h(t))>=0?t.ownerDocument.body:y(t)&&G(t)?t:e(L(t))}(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),i=m(r),a=o?[i].concat(i.visualViewport||[],G(r)?r:[]):r,s=t.concat(a);return o?s:s.concat(J(L(a)))}function K(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Q(e,t,n){var r,o,i,a,s,f,p,u,l,d;return t===c?K(function(e,t){var n=m(e),r=T(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,s=0,f=0;if(o){i=o.width,a=o.height;var c=j();(c||!c&&"fixed"===t)&&(s=o.offsetLeft,f=o.offsetTop)}return{width:i,height:a,x:s+X(e),y:f}}(e,n)):v(t)?((r=D(t,!1,"fixed"===n)).top=r.top+t.clientTop,r.left=r.left+t.clientLeft,r.bottom=r.top+t.clientHeight,r.right=r.left+t.clientWidth,r.width=t.clientWidth,r.height=t.clientHeight,r.x=r.left,r.y=r.top,r):K((o=T(e),a=T(o),s=Y(o),f=null==(i=o.ownerDocument)?void 0:i.body,p=x(a.scrollWidth,a.clientWidth,f?f.scrollWidth:0,f?f.clientWidth:0),u=x(a.scrollHeight,a.clientHeight,f?f.scrollHeight:0,f?f.clientHeight:0),l=-s.scrollLeft+X(o),d=-s.scrollTop,"rtl"===P(f||a).direction&&(l+=x(a.clientWidth,f?f.clientWidth:0)-p),{width:p,height:u,x:l,y:d}))}function Z(e){var t,n=e.reference,a=e.element,s=e.placement,c=s?b(s):null,p=s?_(s):null,u=n.x+n.width/2-a.width/2,l=n.y+n.height/2-a.height/2;switch(c){case"top":t={x:u,y:n.y-a.height};break;case r:t={x:u,y:n.y+n.height};break;case o:t={x:n.x+n.width,y:l};break;case i:t={x:n.x-a.width,y:l};break;default:t={x:n.x,y:n.y}}var d=c?M(c):null;if(null!=d){var h="y"===d?"height":"width";switch(p){case f:t[d]=t[d]-(n[h]/2-a[h]/2);break;case"end":t[d]=t[d]+(n[h]/2-a[h]/2)}}return t}function $(e,t){void 0===t&&(t={});var n,i,a,f,u,l,d,m,g=t,b=g.placement,O=void 0===b?e.placement:b,E=g.strategy,j=void 0===E?e.strategy:E,A=g.boundary,W=g.rootBoundary,M=g.elementContext,B=void 0===M?p:M,H=g.altBoundary,_=g.padding,V=void 0===_?0:_,q=C("number"!=typeof V?V:S(V,s)),N=e.rects.popper,I=e.elements[void 0!==H&&H?B===p?"reference":p:B],U=(n=v(I)?I:I.contextElement||T(e.elements.popper),i=void 0===A?"clippingParents":A,a=void 0===W?c:W,d=(l=[].concat("clippingParents"===i?(f=J(L(n)),!v(u=["absolute","fixed"].indexOf(P(n).position)>=0&&y(n)?R(n):n)?[]:f.filter(function(e){return v(e)&&k(e,u)&&"body"!==h(e)})):[].concat(i),[a]))[0],(m=l.reduce(function(e,t){var r=Q(n,t,j);return e.top=x(r.top,e.top),e.right=w(r.right,e.right),e.bottom=w(r.bottom,e.bottom),e.left=x(r.left,e.left),e},Q(n,d,j))).width=m.right-m.left,m.height=m.bottom-m.top,m.x=m.left,m.y=m.top,m),F=D(e.elements.reference),z=Z({reference:F,element:N,strategy:"absolute",placement:O}),Y=K(Object.assign({},N,z)),X=B===p?Y:F,G={top:U.top-X.top+q.top,bottom:X.bottom-U.bottom+q.bottom,left:U.left-X.left+q.left,right:X.right-U.right+q.right},$=e.modifiersData.offset;if(B===p&&$){var ee=$[O];Object.keys(G).forEach(function(e){var t=[o,r].indexOf(e)>=0?1:-1,n=["top",r].indexOf(e)>=0?"y":"x";G[e]+=ee[n]*t})}return G}function ee(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function et(e){return["top",o,r,i].some(function(t){return e[t]>=0})}var en={placement:"bottom",modifiers:[],strategy:"absolute"};function er(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}var eo=function(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,i=void 0===o?en:o;return function(e,t,n){void 0===n&&(n=i);var o,a,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},en,i),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},f=[],c=!1,p={state:s,setOptions:function(n){var o,a,c,l,h,m,y="function"==typeof n?n(s.options):n;u(),s.options=Object.assign({},i,s.options,y),s.scrollParents={reference:v(e)?J(e):e.contextElement?J(e.contextElement):[],popper:J(t)};var g=(a=Object.keys(o=[].concat(r,s.options.modifiers).reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{})).map(function(e){return o[e]}),c=new Map,l=new Set,h=[],a.forEach(function(e){c.set(e.name,e)}),a.forEach(function(e){l.has(e.name)||function e(t){l.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!l.has(t)){var n=c.get(t);n&&e(n)}}),h.push(t)}(e)}),m=h,d.reduce(function(e,t){return e.concat(m.filter(function(e){return e.phase===t}))},[]));return s.orderedModifiers=g.filter(function(e){return e.enabled}),s.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=e.effect;if("function"==typeof r){var o=r({state:s,name:t,instance:p,options:void 0===n?{}:n});f.push(o||function(){})}}),p.update()},forceUpdate:function(){if(!c){var e=s.elements,t=e.reference,n=e.popper;if(er(t,n)){s.rects={reference:(r=R(n),o="fixed"===s.options.strategy,i=y(r),l=y(r)&&(f=O((a=r.getBoundingClientRect()).width)/r.offsetWidth||1,u=O(a.height)/r.offsetHeight||1,1!==f||1!==u),d=T(r),v=D(t,l,o),g={scrollLeft:0,scrollTop:0},b={x:0,y:0},(i||!i&&!o)&&(("body"!==h(r)||G(d))&&(g=function(e){return e!==m(e)&&y(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:Y(e)}(r)),y(r)?(b=D(r,!0),b.x+=r.clientLeft,b.y+=r.clientTop):d&&(b.x=X(d))),{x:v.left+g.scrollLeft-b.x,y:v.top+g.scrollTop-b.y,width:v.width,height:v.height}),popper:A(n)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach(function(e){return s.modifiersData[e.name]=Object.assign({},e.data)});for(var r,o,i,a,f,u,l,d,v,g,b,x=0;x<s.orderedModifiers.length;x++){if(!0===s.reset){s.reset=!1,x=-1;continue}var w=s.orderedModifiers[x],E=w.fn,j=w.options,k=void 0===j?{}:j,P=w.name;"function"==typeof E&&(s=E({state:s,options:k,name:P,instance:p})||s)}}}},update:(o=function(){return new Promise(function(e){p.forceUpdate(),e(s)})},function(){return a||(a=new Promise(function(e){Promise.resolve().then(function(){a=void 0,e(o())})})),a}),destroy:function(){u(),c=!0}};if(!er(e,t))return p;function u(){f.forEach(function(e){return e()}),f=[]}return p.setOptions(n).then(function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)}),p}}({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,i=void 0===o||o,a=r.resize,s=void 0===a||a,f=m(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&c.forEach(function(e){e.addEventListener("scroll",n.update,N)}),s&&f.addEventListener("resize",n.update,N),function(){i&&c.forEach(function(e){e.removeEventListener("scroll",n.update,N)}),s&&f.removeEventListener("resize",n.update,N)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Z({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=n.adaptive,i=n.roundOffsets,a=void 0===i||i,s={placement:b(t.placement),variation:_(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,q(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===o||o,roundOffsets:a})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,q(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];y(o)&&h(o)&&(Object.assign(o.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],o=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});y(r)&&h(r)&&(Object.assign(r.style,i),Object.keys(o).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,a=n.offset,s=void 0===a?[0,0]:a,f=l.reduce(function(e,n){var r,a,f,c,p,u;return e[n]=(r=t.rects,f=[i,"top"].indexOf(a=b(n))>=0?-1:1,p=(c="function"==typeof s?s(Object.assign({},r,{placement:n})):s)[0],u=c[1],p=p||0,u=(u||0)*f,[i,o].indexOf(a)>=0?{x:u,y:p}:{x:p,y:u}),e},{}),c=f[t.placement],p=c.x,u=c.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=p,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=f}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,c=e.name;if(!t.modifiersData[c]._skip){for(var p=n.mainAxis,d=void 0===p||p,h=n.altAxis,m=void 0===h||h,v=n.fallbackPlacements,y=n.padding,g=n.boundary,x=n.rootBoundary,w=n.altBoundary,O=n.flipVariations,E=void 0===O||O,j=n.allowedAutoPlacements,D=t.options.placement,A=b(D)===D,k=v||(A||!E?[U(D)]:function(e){if(b(e)===a)return[];var t=U(e);return[z(e),t,z(t)]}(D)),P=[D].concat(k).reduce(function(e,n){var r,o,i,f,c,p,d,h,m,v,w,O;return e.concat(b(n)===a?(o=(r={placement:n,boundary:g,rootBoundary:x,padding:y,flipVariations:E,allowedAutoPlacements:j}).placement,i=r.boundary,f=r.rootBoundary,c=r.padding,p=r.flipVariations,h=void 0===(d=r.allowedAutoPlacements)?l:d,0===(w=(v=(m=_(o))?p?u:u.filter(function(e){return _(e)===m}):s).filter(function(e){return h.indexOf(e)>=0})).length&&(w=v),Object.keys(O=w.reduce(function(e,n){return e[n]=$(t,{placement:n,boundary:i,rootBoundary:f,padding:c})[b(n)],e},{})).sort(function(e,t){return O[e]-O[t]})):n)},[]),T=t.rects.reference,L=t.rects.popper,W=new Map,R=!0,M=P[0],B=0;B<P.length;B++){var H=P[B],C=b(H),S=_(H)===f,V=["top",r].indexOf(C)>=0,q=V?"width":"height",N=$(t,{placement:H,boundary:g,rootBoundary:x,altBoundary:w,padding:y}),I=V?S?o:i:S?r:"top";T[q]>L[q]&&(I=U(I));var F=U(I),Y=[];if(d&&Y.push(N[C]<=0),m&&Y.push(N[I]<=0,N[F]<=0),Y.every(function(e){return e})){M=H,R=!1;break}W.set(H,Y)}if(R)for(var X=E?3:1,G=function(e){var t=P.find(function(t){var n=W.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return M=t,"break"},J=X;J>0&&"break"!==G(J);J--);t.placement!==M&&(t.modifiersData[c]._skip=!0,t.placement=M,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,a=e.name,s=n.mainAxis,c=n.altAxis,p=n.boundary,u=n.rootBoundary,l=n.altBoundary,d=n.padding,h=n.tether,m=void 0===h||h,v=n.tetherOffset,y=void 0===v?0:v,g=$(t,{boundary:p,rootBoundary:u,padding:d,altBoundary:l}),O=b(t.placement),E=_(t.placement),j=!E,D=M(O),k="x"===D?"y":"x",P=t.modifiersData.popperOffsets,T=t.rects.reference,L=t.rects.popper,W="function"==typeof y?y(Object.assign({},t.rects,{placement:t.placement})):y,C="number"==typeof W?{mainAxis:W,altAxis:W}:Object.assign({mainAxis:0,altAxis:0},W),S=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,V={x:0,y:0};if(P){if(void 0===s||s){var q,N="y"===D?"top":i,I="y"===D?r:o,U="y"===D?"height":"width",F=P[D],z=F+g[N],Y=F-g[I],X=m?-L[U]/2:0,G=E===f?T[U]:L[U],J=E===f?-L[U]:-T[U],K=t.elements.arrow,Q=m&&K?A(K):{width:0,height:0},Z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:H(),ee=Z[N],et=Z[I],en=B(0,T[U],Q[U]),er=j?T[U]/2-X-en-ee-C.mainAxis:G-en-ee-C.mainAxis,eo=j?-T[U]/2+X+en+et+C.mainAxis:J+en+et+C.mainAxis,ei=t.elements.arrow&&R(t.elements.arrow),ea=ei?"y"===D?ei.clientTop||0:ei.clientLeft||0:0,es=null!=(q=null==S?void 0:S[D])?q:0,ef=B(m?w(z,F+er-es-ea):z,F,m?x(Y,F+eo-es):Y);P[D]=ef,V[D]=ef-F}if(void 0!==c&&c){var ec,ep,eu="x"===D?"top":i,el="x"===D?r:o,ed=P[k],eh="y"===k?"height":"width",em=ed+g[eu],ev=ed-g[el],ey=-1!==["top",i].indexOf(O),eg=null!=(ep=null==S?void 0:S[k])?ep:0,eb=ey?em:ed-T[eh]-L[eh]-eg+C.altAxis,ex=ey?ed+T[eh]+L[eh]-eg-C.altAxis:ev,ew=m&&ey?(ec=B(eb,ed,ex))>ex?ex:ec:B(m?eb:em,ed,m?ex:ev);P[k]=ew,V[k]=ew-ed}t.modifiersData[a]=V}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,a=e.name,f=e.options,c=n.elements.arrow,p=n.modifiersData.popperOffsets,u=b(n.placement),l=M(u),d=[i,o].indexOf(u)>=0?"height":"width";if(c&&p){var h,m=(h=f.padding,C("number"!=typeof(h="function"==typeof h?h(Object.assign({},n.rects,{placement:n.placement})):h)?h:S(h,s))),v=A(c),y="y"===l?"top":i,g="y"===l?r:o,x=n.rects.reference[d]+n.rects.reference[l]-p[l]-n.rects.popper[d],w=p[l]-n.rects.reference[l],O=R(c),E=O?"y"===l?O.clientHeight||0:O.clientWidth||0:0,j=m[y],D=E-v[d]-m[g],k=E/2-v[d]/2+(x/2-w/2),P=B(j,k,D);n.modifiersData[a]=((t={})[l]=P,t.centerOffset=P-k,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;if(null!=r)("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&k(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=$(t,{elementContext:"reference"}),s=$(t,{altBoundary:!0}),f=ee(a,r),c=ee(s,o,i),p=et(f),u=et(c);t.modifiersData[n]={referenceClippingOffsets:f,popperEscapeOffsets:c,isReferenceHidden:p,hasPopperEscaped:u},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":u})}}]})},77117:(e,t,n)=>{e.exports=n(29027)()}}]);