"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es2015_c"],{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/commands.js":
/*!*************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/commands.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusOn: () => (/* binding */ focusOn)\n/* harmony export */ });\nvar focusOn = function (target, focusOptions) {\n    if (!target) {\n        // not clear how, but is possible https://github.com/theKashey/focus-lock/issues/53\n        return;\n    }\n    if ('focus' in target) {\n        target.focus(focusOptions);\n    }\n    if ('contentWindow' in target && target.contentWindow) {\n        target.contentWindow.focus();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9jb21tYW5kcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmb2N1cy1sb2NrQDEuMy42XFxub2RlX21vZHVsZXNcXGZvY3VzLWxvY2tcXGRpc3RcXGVzMjAxNVxcY29tbWFuZHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBmb2N1c09uID0gZnVuY3Rpb24gKHRhcmdldCwgZm9jdXNPcHRpb25zKSB7XG4gICAgaWYgKCF0YXJnZXQpIHtcbiAgICAgICAgLy8gbm90IGNsZWFyIGhvdywgYnV0IGlzIHBvc3NpYmxlIGh0dHBzOi8vZ2l0aHViLmNvbS90aGVLYXNoZXkvZm9jdXMtbG9jay9pc3N1ZXMvNTNcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBpZiAoJ2ZvY3VzJyBpbiB0YXJnZXQpIHtcbiAgICAgICAgdGFyZ2V0LmZvY3VzKGZvY3VzT3B0aW9ucyk7XG4gICAgfVxuICAgIGlmICgnY29udGVudFdpbmRvdycgaW4gdGFyZ2V0ICYmIHRhcmdldC5jb250ZW50V2luZG93KSB7XG4gICAgICAgIHRhcmdldC5jb250ZW50V2luZG93LmZvY3VzKCk7XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/commands.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FOCUS_ALLOW: () => (/* binding */ FOCUS_ALLOW),\n/* harmony export */   FOCUS_AUTO: () => (/* binding */ FOCUS_AUTO),\n/* harmony export */   FOCUS_DISABLED: () => (/* binding */ FOCUS_DISABLED),\n/* harmony export */   FOCUS_GROUP: () => (/* binding */ FOCUS_GROUP),\n/* harmony export */   FOCUS_NO_AUTOFOCUS: () => (/* binding */ FOCUS_NO_AUTOFOCUS)\n/* harmony export */ });\n/**\n * defines a focus group\n */\nvar FOCUS_GROUP = 'data-focus-lock';\n/**\n * disables element discovery inside a group marked by key\n */\nvar FOCUS_DISABLED = 'data-focus-lock-disabled';\n/**\n * allows uncontrolled focus within the marked area, effectively disabling focus lock for it's content\n */\nvar FOCUS_ALLOW = 'data-no-focus-lock';\n/**\n * instructs autofocus engine to pick default autofocus inside a given node\n * can be set on the element or container\n */\nvar FOCUS_AUTO = 'data-autofocus-inside';\n/**\n * instructs autofocus to ignore elements within a given node\n * can be set on the element or container\n */\nvar FOCUS_NO_AUTOFOCUS = 'data-no-autofocus';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZm9jdXMtbG9ja0AxLjMuNlxcbm9kZV9tb2R1bGVzXFxmb2N1cy1sb2NrXFxkaXN0XFxlczIwMTVcXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGRlZmluZXMgYSBmb2N1cyBncm91cFxuICovXG5leHBvcnQgdmFyIEZPQ1VTX0dST1VQID0gJ2RhdGEtZm9jdXMtbG9jayc7XG4vKipcbiAqIGRpc2FibGVzIGVsZW1lbnQgZGlzY292ZXJ5IGluc2lkZSBhIGdyb3VwIG1hcmtlZCBieSBrZXlcbiAqL1xuZXhwb3J0IHZhciBGT0NVU19ESVNBQkxFRCA9ICdkYXRhLWZvY3VzLWxvY2stZGlzYWJsZWQnO1xuLyoqXG4gKiBhbGxvd3MgdW5jb250cm9sbGVkIGZvY3VzIHdpdGhpbiB0aGUgbWFya2VkIGFyZWEsIGVmZmVjdGl2ZWx5IGRpc2FibGluZyBmb2N1cyBsb2NrIGZvciBpdCdzIGNvbnRlbnRcbiAqL1xuZXhwb3J0IHZhciBGT0NVU19BTExPVyA9ICdkYXRhLW5vLWZvY3VzLWxvY2snO1xuLyoqXG4gKiBpbnN0cnVjdHMgYXV0b2ZvY3VzIGVuZ2luZSB0byBwaWNrIGRlZmF1bHQgYXV0b2ZvY3VzIGluc2lkZSBhIGdpdmVuIG5vZGVcbiAqIGNhbiBiZSBzZXQgb24gdGhlIGVsZW1lbnQgb3IgY29udGFpbmVyXG4gKi9cbmV4cG9ydCB2YXIgRk9DVVNfQVVUTyA9ICdkYXRhLWF1dG9mb2N1cy1pbnNpZGUnO1xuLyoqXG4gKiBpbnN0cnVjdHMgYXV0b2ZvY3VzIHRvIGlnbm9yZSBlbGVtZW50cyB3aXRoaW4gYSBnaXZlbiBub2RlXG4gKiBjYW4gYmUgc2V0IG9uIHRoZSBlbGVtZW50IG9yIGNvbnRhaW5lclxuICovXG5leHBvcnQgdmFyIEZPQ1VTX05PX0FVVE9GT0NVUyA9ICdkYXRhLW5vLWF1dG9mb2N1cyc7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusInside.js":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusInside.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusInside: () => (/* binding */ focusInside)\n/* harmony export */ });\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_all_affected__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/all-affected */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/all-affected.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/array */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/getActiveElement */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\");\n\n\n\n\nvar focusInFrame = function (frame, activeElement) { return frame === activeElement; };\nvar focusInsideIframe = function (topNode, activeElement) {\n    return Boolean((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(topNode.querySelectorAll('iframe')).some(function (node) { return focusInFrame(node, activeElement); }));\n};\n/**\n * @returns {Boolean} true, if the current focus is inside given node or nodes.\n * Supports nodes hidden inside shadowDom\n */\nvar focusInside = function (topNode, activeElement) {\n    // const activeElement = document && getActiveElement();\n    if (activeElement === void 0) { activeElement = (0,_utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__.getActiveElement)((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.getFirst)(topNode).ownerDocument); }\n    if (!activeElement || (activeElement.dataset && activeElement.dataset.focusGuard)) {\n        return false;\n    }\n    return (0,_utils_all_affected__WEBPACK_IMPORTED_MODULE_2__.getAllAffectedNodes)(topNode).some(function (node) {\n        return (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__.contains)(node, activeElement) || focusInsideIframe(node, activeElement);\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusInside.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusIsHidden.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusIsHidden.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusIsHidden: () => (/* binding */ focusIsHidden)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/array */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _utils_getActiveElement__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/getActiveElement */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\");\n\n\n\n\n/**\n * checks if focus is hidden FROM the focus-lock\n * ie contained inside a node focus-lock shall ignore\n *\n * This is a utility function coupled with {@link FOCUS_ALLOW} constant\n *\n * @returns {boolean} focus is currently is in \"allow\" area\n */\nvar focusIsHidden = function (inDocument) {\n    if (inDocument === void 0) { inDocument = document; }\n    var activeElement = (0,_utils_getActiveElement__WEBPACK_IMPORTED_MODULE_0__.getActiveElement)(inDocument);\n    if (!activeElement) {\n        return false;\n    }\n    // this does not support setting FOCUS_ALLOW within shadow dom\n    return (0,_utils_array__WEBPACK_IMPORTED_MODULE_1__.toArray)(inDocument.querySelectorAll(\"[\".concat(_constants__WEBPACK_IMPORTED_MODULE_2__.FOCUS_ALLOW, \"]\"))).some(function (node) { return (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__.contains)(node, activeElement); });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9mb2N1c0lzSGlkZGVuLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTBDO0FBQ0U7QUFDSjtBQUNvQjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxtQkFBbUI7QUFDL0Q7QUFDQSxhQUFhLFNBQVM7QUFDdEI7QUFDTztBQUNQLGlDQUFpQztBQUNqQyx3QkFBd0IseUVBQWdCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxxREFBTyx3Q0FBd0MsbURBQVcsZ0NBQWdDLE9BQU8seURBQVEsd0JBQXdCO0FBQzVJIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmb2N1cy1sb2NrQDEuMy42XFxub2RlX21vZHVsZXNcXGZvY3VzLWxvY2tcXGRpc3RcXGVzMjAxNVxcZm9jdXNJc0hpZGRlbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBGT0NVU19BTExPVyB9IGZyb20gJy4vY29uc3RhbnRzJztcbmltcG9ydCB7IGNvbnRhaW5zIH0gZnJvbSAnLi91dGlscy9ET011dGlscyc7XG5pbXBvcnQgeyB0b0FycmF5IH0gZnJvbSAnLi91dGlscy9hcnJheSc7XG5pbXBvcnQgeyBnZXRBY3RpdmVFbGVtZW50IH0gZnJvbSAnLi91dGlscy9nZXRBY3RpdmVFbGVtZW50Jztcbi8qKlxuICogY2hlY2tzIGlmIGZvY3VzIGlzIGhpZGRlbiBGUk9NIHRoZSBmb2N1cy1sb2NrXG4gKiBpZSBjb250YWluZWQgaW5zaWRlIGEgbm9kZSBmb2N1cy1sb2NrIHNoYWxsIGlnbm9yZVxuICpcbiAqIFRoaXMgaXMgYSB1dGlsaXR5IGZ1bmN0aW9uIGNvdXBsZWQgd2l0aCB7QGxpbmsgRk9DVVNfQUxMT1d9IGNvbnN0YW50XG4gKlxuICogQHJldHVybnMge2Jvb2xlYW59IGZvY3VzIGlzIGN1cnJlbnRseSBpcyBpbiBcImFsbG93XCIgYXJlYVxuICovXG5leHBvcnQgdmFyIGZvY3VzSXNIaWRkZW4gPSBmdW5jdGlvbiAoaW5Eb2N1bWVudCkge1xuICAgIGlmIChpbkRvY3VtZW50ID09PSB2b2lkIDApIHsgaW5Eb2N1bWVudCA9IGRvY3VtZW50OyB9XG4gICAgdmFyIGFjdGl2ZUVsZW1lbnQgPSBnZXRBY3RpdmVFbGVtZW50KGluRG9jdW1lbnQpO1xuICAgIGlmICghYWN0aXZlRWxlbWVudCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIC8vIHRoaXMgZG9lcyBub3Qgc3VwcG9ydCBzZXR0aW5nIEZPQ1VTX0FMTE9XIHdpdGhpbiBzaGFkb3cgZG9tXG4gICAgcmV0dXJuIHRvQXJyYXkoaW5Eb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKFwiW1wiLmNvbmNhdChGT0NVU19BTExPVywgXCJdXCIpKSkuc29tZShmdW5jdGlvbiAobm9kZSkgeyByZXR1cm4gY29udGFpbnMobm9kZSwgYWN0aXZlRWxlbWVudCk7IH0pO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusIsHidden.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusSolver.js":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusSolver.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusSolver: () => (/* binding */ focusSolver)\n/* harmony export */ });\n/* harmony import */ var _solver__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./solver */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/solver.js\");\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_all_affected__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/all-affected */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/all-affected.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/array */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _utils_auto_focus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/auto-focus */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/auto-focus.js\");\n/* harmony import */ var _utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/getActiveElement */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\");\n/* harmony import */ var _utils_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/is */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/is.js\");\n/* harmony import */ var _utils_parenting__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/parenting */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/parenting.js\");\n\n\n\n\n\n\n\n\nvar reorderNodes = function (srcNodes, dstNodes) {\n    var remap = new Map();\n    // no Set(dstNodes) for IE11 :(\n    dstNodes.forEach(function (entity) { return remap.set(entity.node, entity); });\n    // remap to dstNodes\n    return srcNodes.map(function (node) { return remap.get(node); }).filter(_utils_is__WEBPACK_IMPORTED_MODULE_0__.isDefined);\n};\n/**\n * contains the main logic of the `focus-lock` package.\n *\n * ! you probably dont need this function !\n *\n * given top node(s) and the last active element returns the element to be focused next\n * @returns element which should be focused to move focus inside\n * @param topNode\n * @param lastNode\n */\nvar focusSolver = function (topNode, lastNode) {\n    var activeElement = (0,_utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__.getActiveElement)((0,_utils_array__WEBPACK_IMPORTED_MODULE_2__.asArray)(topNode).length > 0 ? document : (0,_utils_array__WEBPACK_IMPORTED_MODULE_2__.getFirst)(topNode).ownerDocument);\n    var entries = (0,_utils_all_affected__WEBPACK_IMPORTED_MODULE_3__.getAllAffectedNodes)(topNode).filter(_utils_is__WEBPACK_IMPORTED_MODULE_0__.isNotAGuard);\n    var commonParent = (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_4__.getTopCommonParent)(activeElement || topNode, topNode, entries);\n    var visibilityCache = new Map();\n    var anyFocusable = (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_5__.getFocusableNodes)(entries, visibilityCache);\n    var innerElements = anyFocusable.filter(function (_a) {\n        var node = _a.node;\n        return (0,_utils_is__WEBPACK_IMPORTED_MODULE_0__.isNotAGuard)(node);\n    });\n    if (!innerElements[0]) {\n        return undefined;\n    }\n    var outerNodes = (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_5__.getFocusableNodes)([commonParent], visibilityCache).map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var orderedInnerElements = reorderNodes(outerNodes, innerElements);\n    // collect inner focusable and separately tabbables\n    var innerFocusables = orderedInnerElements.map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var innerTabbable = orderedInnerElements.filter(function (_a) {\n        var tabIndex = _a.tabIndex;\n        return tabIndex >= 0;\n    }).map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var newId = (0,_solver__WEBPACK_IMPORTED_MODULE_6__.newFocus)(innerFocusables, innerTabbable, outerNodes, activeElement, lastNode);\n    if (newId === _solver__WEBPACK_IMPORTED_MODULE_6__.NEW_FOCUS) {\n        var focusNode = \n        // first try only tabbable, and the fallback to all focusable, as long as at least one element should be picked for focus\n        (0,_utils_auto_focus__WEBPACK_IMPORTED_MODULE_7__.pickAutofocus)(anyFocusable, innerTabbable, (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_4__.allParentAutofocusables)(entries, visibilityCache)) ||\n            (0,_utils_auto_focus__WEBPACK_IMPORTED_MODULE_7__.pickAutofocus)(anyFocusable, innerFocusables, (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_4__.allParentAutofocusables)(entries, visibilityCache));\n        if (focusNode) {\n            return { node: focusNode };\n        }\n        else {\n            console.warn('focus-lock: cannot find any node to move focus into');\n            return undefined;\n        }\n    }\n    if (newId === undefined) {\n        return newId;\n    }\n    return orderedInnerElements[newId];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusSolver.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusables.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusables.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   expandFocusableNodes: () => (/* binding */ expandFocusableNodes)\n/* harmony export */ });\n/* harmony import */ var _utils_all_affected__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/all-affected */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/all-affected.js\");\n/* harmony import */ var _utils_is__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/is */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/is.js\");\n/* harmony import */ var _utils_parenting__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/parenting */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/parenting.js\");\n/* harmony import */ var _utils_tabOrder__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/tabOrder */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabOrder.js\");\n/* harmony import */ var _utils_tabUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/tabUtils */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabUtils.js\");\n\n\n\n\n\n/**\n * traverses all related nodes (including groups) returning a list of all nodes(outer and internal) with meta information\n * This is low-level API!\n * @returns list of focusable elements inside a given top(!) node.\n * @see {@link getFocusableNodes} providing a simpler API\n */\nvar expandFocusableNodes = function (topNode) {\n    var entries = (0,_utils_all_affected__WEBPACK_IMPORTED_MODULE_0__.getAllAffectedNodes)(topNode).filter(_utils_is__WEBPACK_IMPORTED_MODULE_1__.isNotAGuard);\n    var commonParent = (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_2__.getTopCommonParent)(topNode, topNode, entries);\n    var outerNodes = (0,_utils_tabOrder__WEBPACK_IMPORTED_MODULE_3__.orderByTabIndex)((0,_utils_tabUtils__WEBPACK_IMPORTED_MODULE_4__.getFocusables)([commonParent], true), true, true);\n    var innerElements = (0,_utils_tabUtils__WEBPACK_IMPORTED_MODULE_4__.getFocusables)(entries, false);\n    return outerNodes.map(function (_a) {\n        var node = _a.node, index = _a.index;\n        return ({\n            node: node,\n            index: index,\n            lockItem: innerElements.indexOf(node) >= 0,\n            guard: (0,_utils_is__WEBPACK_IMPORTED_MODULE_1__.isGuard)(node),\n        });\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusables.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/index.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/index.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   captureFocusRestore: () => (/* reexport safe */ _return_focus__WEBPACK_IMPORTED_MODULE_8__.captureFocusRestore),\n/* harmony export */   constants: () => (/* binding */ constants),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   expandFocusableNodes: () => (/* reexport safe */ _focusables__WEBPACK_IMPORTED_MODULE_5__.expandFocusableNodes),\n/* harmony export */   focusFirstElement: () => (/* reexport safe */ _sibling__WEBPACK_IMPORTED_MODULE_7__.focusFirstElement),\n/* harmony export */   focusInside: () => (/* reexport safe */ _focusInside__WEBPACK_IMPORTED_MODULE_1__.focusInside),\n/* harmony export */   focusIsHidden: () => (/* reexport safe */ _focusIsHidden__WEBPACK_IMPORTED_MODULE_2__.focusIsHidden),\n/* harmony export */   focusLastElement: () => (/* reexport safe */ _sibling__WEBPACK_IMPORTED_MODULE_7__.focusLastElement),\n/* harmony export */   focusNextElement: () => (/* reexport safe */ _sibling__WEBPACK_IMPORTED_MODULE_7__.focusNextElement),\n/* harmony export */   focusPrevElement: () => (/* reexport safe */ _sibling__WEBPACK_IMPORTED_MODULE_7__.focusPrevElement),\n/* harmony export */   focusSolver: () => (/* reexport safe */ _focusSolver__WEBPACK_IMPORTED_MODULE_4__.focusSolver),\n/* harmony export */   getFocusableNodes: () => (/* reexport safe */ _utils_DOMutils__WEBPACK_IMPORTED_MODULE_6__.getFocusableNodes),\n/* harmony export */   getRelativeFocusable: () => (/* reexport safe */ _sibling__WEBPACK_IMPORTED_MODULE_7__.getRelativeFocusable),\n/* harmony export */   getTabbableNodes: () => (/* reexport safe */ _utils_DOMutils__WEBPACK_IMPORTED_MODULE_6__.getTabbableNodes),\n/* harmony export */   moveFocusInside: () => (/* reexport safe */ _moveFocusInside__WEBPACK_IMPORTED_MODULE_3__.moveFocusInside)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _focusInside__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./focusInside */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusInside.js\");\n/* harmony import */ var _focusIsHidden__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusIsHidden */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusIsHidden.js\");\n/* harmony import */ var _focusSolver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./focusSolver */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusSolver.js\");\n/* harmony import */ var _focusables__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./focusables */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusables.js\");\n/* harmony import */ var _moveFocusInside__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./moveFocusInside */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/moveFocusInside.js\");\n/* harmony import */ var _return_focus__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./return-focus */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/return-focus.js\");\n/* harmony import */ var _sibling__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./sibling */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/sibling.js\");\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n\n\n\n\n\n\n\n\n\n/**\n * magic symbols to control focus behavior from DOM\n * see description of every particular one\n */\nvar constants = _constants__WEBPACK_IMPORTED_MODULE_0__;\n\n/**\n * @deprecated - please use {@link moveFocusInside} named export\n */\nvar deprecated_default_moveFocusInside = _moveFocusInside__WEBPACK_IMPORTED_MODULE_3__.moveFocusInside;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (deprecated_default_moveFocusInside);\n//\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/moveFocusInside.js":
/*!********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/moveFocusInside.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   moveFocusInside: () => (/* binding */ moveFocusInside)\n/* harmony export */ });\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./commands */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/commands.js\");\n/* harmony import */ var _focusSolver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./focusSolver */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/focusSolver.js\");\n\n\nvar guardCount = 0;\nvar lockDisabled = false;\n/**\n * The main functionality of the focus-lock package\n *\n * Contains focus at a given node.\n * The last focused element will help to determine which element(first or last) should be focused.\n * The found element will be focused.\n *\n * This is one time action (move), not a persistent focus-lock\n *\n * HTML markers (see {@link import('./constants').FOCUS_AUTO} constants) can control autofocus\n * @see {@link focusSolver} for the same functionality without autofocus\n */\nvar moveFocusInside = function (topNode, lastNode, options) {\n    if (options === void 0) { options = {}; }\n    var focusable = (0,_focusSolver__WEBPACK_IMPORTED_MODULE_0__.focusSolver)(topNode, lastNode);\n    // global local side effect to countain recursive lock activation and resolve focus-fighting\n    if (lockDisabled) {\n        return;\n    }\n    if (focusable) {\n        /** +FOCUS-FIGHTING prevention **/\n        if (guardCount > 2) {\n            // we have recursive entered back the lock activation\n            console.error('FocusLock: focus-fighting detected. Only one focus management system could be active. ' +\n                'See https://github.com/theKashey/focus-lock/#focus-fighting');\n            lockDisabled = true;\n            setTimeout(function () {\n                lockDisabled = false;\n            }, 1);\n            return;\n        }\n        guardCount++;\n        (0,_commands__WEBPACK_IMPORTED_MODULE_1__.focusOn)(focusable.node, options.focusOptions);\n        guardCount--;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/moveFocusInside.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/return-focus.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/return-focus.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   captureFocusRestore: () => (/* binding */ captureFocusRestore),\n/* harmony export */   recordElementLocation: () => (/* binding */ recordElementLocation)\n/* harmony export */ });\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n\nfunction weakRef(value) {\n    if (!value)\n        return null;\n    // #68 Safari 14.1 dont have it yet\n    // FIXME: remove in 2025\n    if (typeof WeakRef === 'undefined') {\n        return function () { return value || null; };\n    }\n    var w = value ? new WeakRef(value) : null;\n    return function () { return (w === null || w === void 0 ? void 0 : w.deref()) || null; };\n}\nvar recordElementLocation = function (element) {\n    if (!element) {\n        return null;\n    }\n    var stack = [];\n    var currentElement = element;\n    while (currentElement && currentElement !== document.body) {\n        stack.push({\n            current: weakRef(currentElement),\n            parent: weakRef(currentElement.parentElement),\n            left: weakRef(currentElement.previousElementSibling),\n            right: weakRef(currentElement.nextElementSibling),\n        });\n        currentElement = currentElement.parentElement;\n    }\n    return {\n        element: weakRef(element),\n        stack: stack,\n        ownerDocument: element.ownerDocument,\n    };\n};\nvar restoreFocusTo = function (location) {\n    var _a, _b, _c, _d, _e;\n    if (!location) {\n        return undefined;\n    }\n    var stack = location.stack, ownerDocument = location.ownerDocument;\n    var visibilityCache = new Map();\n    for (var _i = 0, stack_1 = stack; _i < stack_1.length; _i++) {\n        var line = stack_1[_i];\n        var parent_1 = (_a = line.parent) === null || _a === void 0 ? void 0 : _a.call(line);\n        // is it still here?\n        if (parent_1 && ownerDocument.contains(parent_1)) {\n            var left = (_b = line.left) === null || _b === void 0 ? void 0 : _b.call(line);\n            var savedCurrent = line.current();\n            var current = parent_1.contains(savedCurrent) ? savedCurrent : undefined;\n            var right = (_c = line.right) === null || _c === void 0 ? void 0 : _c.call(line);\n            var focusables = (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_0__.getTabbableNodes)([parent_1], visibilityCache);\n            var aim = \n            // that is element itself\n            (_e = (_d = current !== null && current !== void 0 ? current : \n            // or something in it's place\n            left === null || left === void 0 ? void 0 : left.nextElementSibling) !== null && _d !== void 0 ? _d : \n            // or somebody to the right, still close enough\n            right) !== null && _e !== void 0 ? _e : \n            // or somebody to the left, something?\n            left;\n            while (aim) {\n                for (var _f = 0, focusables_1 = focusables; _f < focusables_1.length; _f++) {\n                    var focusable = focusables_1[_f];\n                    if (aim === null || aim === void 0 ? void 0 : aim.contains(focusable.node)) {\n                        return focusable.node;\n                    }\n                }\n                aim = aim.nextElementSibling;\n            }\n            if (focusables.length) {\n                // if parent contains a focusable - move there\n                return focusables[0].node;\n            }\n        }\n    }\n    // nothing matched\n    return undefined;\n};\n/**\n * Captures the current focused element to restore focus as close as possible in the future\n * Handles situations where the focused element is removed from the DOM or no longer focusable\n * moving focus to the closest focusable element\n * @param targetElement - element where focus should be restored\n * @returns a function returning a new element to focus\n */\nvar captureFocusRestore = function (targetElement) {\n    var location = recordElementLocation(targetElement);\n    return function () {\n        return restoreFocusTo(location);\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/return-focus.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/sibling.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/sibling.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusFirstElement: () => (/* binding */ focusFirstElement),\n/* harmony export */   focusLastElement: () => (/* binding */ focusLastElement),\n/* harmony export */   focusNextElement: () => (/* binding */ focusNextElement),\n/* harmony export */   focusPrevElement: () => (/* binding */ focusPrevElement),\n/* harmony export */   getRelativeFocusable: () => (/* binding */ getRelativeFocusable)\n/* harmony export */ });\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./commands */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/commands.js\");\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/DOMutils */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/array */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js\");\n\n\n\n/**\n * for a given `element` in a given `scope` returns focusable siblings\n * @param element - base element\n * @param scope - common parent. Can be document, but better to narrow it down for performance reasons\n * @returns {prev,next} - references to a focusable element before and after\n * @returns undefined - if operation is not applicable\n */\nvar getRelativeFocusable = function (element, scope, useTabbables) {\n    if (!element || !scope) {\n        console.error('no element or scope given');\n        return {};\n    }\n    var shards = (0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(scope);\n    if (shards.every(function (shard) { return !(0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.contains)(shard, element); })) {\n        console.error('Active element is not contained in the scope');\n        return {};\n    }\n    var focusables = useTabbables\n        ? (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getTabbableNodes)(shards, new Map())\n        : (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getFocusableNodes)(shards, new Map());\n    var current = focusables.findIndex(function (_a) {\n        var node = _a.node;\n        return node === element;\n    });\n    if (current === -1) {\n        // an edge case, when anchor element is not found\n        return undefined;\n    }\n    return {\n        prev: focusables[current - 1],\n        next: focusables[current + 1],\n        first: focusables[0],\n        last: focusables[focusables.length - 1],\n    };\n};\nvar getBoundary = function (shards, useTabbables) {\n    var set = useTabbables\n        ? (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getTabbableNodes)((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(shards), new Map())\n        : (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getFocusableNodes)((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(shards), new Map());\n    return {\n        first: set[0],\n        last: set[set.length - 1],\n    };\n};\nvar defaultOptions = function (options) {\n    return Object.assign({\n        scope: document.body,\n        cycle: true,\n        onlyTabbable: true,\n    }, options);\n};\nvar moveFocus = function (fromElement, options, cb) {\n    if (options === void 0) { options = {}; }\n    var newOptions = defaultOptions(options);\n    var solution = getRelativeFocusable(fromElement, newOptions.scope, newOptions.onlyTabbable);\n    if (!solution) {\n        return;\n    }\n    var target = cb(solution, newOptions.cycle);\n    if (target) {\n        (0,_commands__WEBPACK_IMPORTED_MODULE_2__.focusOn)(target.node, newOptions.focusOptions);\n    }\n};\n/**\n * focuses next element in the tab-order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nvar focusNextElement = function (fromElement, options) {\n    if (options === void 0) { options = {}; }\n    moveFocus(fromElement, options, function (_a, cycle) {\n        var next = _a.next, first = _a.first;\n        return next || (cycle && first);\n    });\n};\n/**\n * focuses prev element in the tab order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nvar focusPrevElement = function (fromElement, options) {\n    if (options === void 0) { options = {}; }\n    moveFocus(fromElement, options, function (_a, cycle) {\n        var prev = _a.prev, last = _a.last;\n        return prev || (cycle && last);\n    });\n};\nvar pickBoundary = function (scope, options, what) {\n    var _a;\n    var boundary = getBoundary(scope, (_a = options.onlyTabbable) !== null && _a !== void 0 ? _a : true);\n    var node = boundary[what];\n    if (node) {\n        (0,_commands__WEBPACK_IMPORTED_MODULE_2__.focusOn)(node.node, options.focusOptions);\n    }\n};\n/**\n * focuses first element in the tab-order\n * @param {FocusNextOptions} options - focus options\n */\nvar focusFirstElement = function (scope, options) {\n    if (options === void 0) { options = {}; }\n    pickBoundary(scope, options, 'first');\n};\n/**\n * focuses last element in the tab order\n * @param {FocusNextOptions} options - focus options\n */\nvar focusLastElement = function (scope, options) {\n    if (options === void 0) { options = {}; }\n    pickBoundary(scope, options, 'last');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/sibling.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/solver.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/solver.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NEW_FOCUS: () => (/* binding */ NEW_FOCUS),\n/* harmony export */   newFocus: () => (/* binding */ newFocus)\n/* harmony export */ });\n/* harmony import */ var _utils_correctFocus__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/correctFocus */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/correctFocus.js\");\n/* harmony import */ var _utils_firstFocus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/firstFocus */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/firstFocus.js\");\n/* harmony import */ var _utils_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/is */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/is.js\");\n\n\n\nvar NEW_FOCUS = 'NEW_FOCUS';\n/**\n * Main solver for the \"find next focus\" question\n * @param innerNodes - used to control \"return focus\"\n * @param innerTabbables - used to control \"autofocus\"\n * @param outerNodes\n * @param activeElement\n * @param lastNode\n * @returns {number|string|undefined|*}\n */\nvar newFocus = function (innerNodes, innerTabbables, outerNodes, activeElement, lastNode) {\n    var cnt = innerNodes.length;\n    var firstFocus = innerNodes[0];\n    var lastFocus = innerNodes[cnt - 1];\n    var isOnGuard = (0,_utils_is__WEBPACK_IMPORTED_MODULE_0__.isGuard)(activeElement);\n    // focus is inside\n    if (activeElement && innerNodes.indexOf(activeElement) >= 0) {\n        return undefined;\n    }\n    var activeIndex = activeElement !== undefined ? outerNodes.indexOf(activeElement) : -1;\n    var lastIndex = lastNode ? outerNodes.indexOf(lastNode) : activeIndex;\n    var lastNodeInside = lastNode ? innerNodes.indexOf(lastNode) : -1;\n    // no active focus (or focus is on the body)\n    if (activeIndex === -1) {\n        // known fallback\n        if (lastNodeInside !== -1) {\n            return lastNodeInside;\n        }\n        return NEW_FOCUS;\n    }\n    // new focus, nothing to calculate\n    if (lastNodeInside === -1) {\n        return NEW_FOCUS;\n    }\n    var indexDiff = activeIndex - lastIndex;\n    var firstNodeIndex = outerNodes.indexOf(firstFocus);\n    var lastNodeIndex = outerNodes.indexOf(lastFocus);\n    var correctedNodes = (0,_utils_correctFocus__WEBPACK_IMPORTED_MODULE_1__.correctNodes)(outerNodes);\n    var currentFocusableIndex = activeElement !== undefined ? correctedNodes.indexOf(activeElement) : -1;\n    var previousFocusableIndex = lastNode ? correctedNodes.indexOf(lastNode) : currentFocusableIndex;\n    var tabbableNodes = correctedNodes.filter(function (node) { return node.tabIndex >= 0; });\n    var currentTabbableIndex = activeElement !== undefined ? tabbableNodes.indexOf(activeElement) : -1;\n    var previousTabbableIndex = lastNode ? tabbableNodes.indexOf(lastNode) : currentTabbableIndex;\n    var focusIndexDiff = currentTabbableIndex >= 0 && previousTabbableIndex >= 0\n        ? // old/new are tabbables, measure distance in tabbable space\n            previousTabbableIndex - currentTabbableIndex\n        : // or else measure in focusable space\n            previousFocusableIndex - currentFocusableIndex;\n    // old focus\n    if (!indexDiff && lastNodeInside >= 0) {\n        return lastNodeInside;\n    }\n    // no tabbable elements, autofocus is not possible\n    if (innerTabbables.length === 0) {\n        // an edge case with no tabbable elements\n        // return the last focusable one\n        // with some probability this will prevent focus from cycling across the lock, but there is no tabbale elements to cycle to\n        return lastNodeInside;\n    }\n    var returnFirstNode = (0,_utils_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFocusable)(innerNodes, innerTabbables[0]);\n    var returnLastNode = (0,_utils_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFocusable)(innerNodes, innerTabbables[innerTabbables.length - 1]);\n    // first element\n    if (activeIndex <= firstNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n        return returnLastNode;\n    }\n    // last element\n    if (activeIndex >= lastNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n        return returnFirstNode;\n    }\n    // jump out, but not on the guard\n    if (indexDiff && Math.abs(focusIndexDiff) > 1) {\n        return lastNodeInside;\n    }\n    // focus above lock\n    if (activeIndex <= firstNodeIndex) {\n        return returnLastNode;\n    }\n    // focus below lock\n    if (activeIndex > lastNodeIndex) {\n        return returnFirstNode;\n    }\n    // index is inside tab order, but outside Lock\n    if (indexDiff) {\n        if (Math.abs(indexDiff) > 1) {\n            return lastNodeInside;\n        }\n        return (cnt + lastNodeInside + indexDiff) % cnt;\n    }\n    // do nothing\n    return undefined;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/solver.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/DOMutils.js":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/DOMutils.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contains: () => (/* binding */ contains),\n/* harmony export */   filterAutoFocusable: () => (/* binding */ filterAutoFocusable),\n/* harmony export */   filterFocusable: () => (/* binding */ filterFocusable),\n/* harmony export */   getFocusableNodes: () => (/* binding */ getFocusableNodes),\n/* harmony export */   getTabbableNodes: () => (/* binding */ getTabbableNodes),\n/* harmony export */   parentAutofocusables: () => (/* binding */ parentAutofocusables)\n/* harmony export */ });\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _is__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/is.js\");\n/* harmony import */ var _tabOrder__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tabOrder */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabOrder.js\");\n/* harmony import */ var _tabUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tabUtils */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabUtils.js\");\n\n\n\n\n/**\n * given list of focusable elements keeps the ones user can interact with\n * @param nodes\n * @param visibilityCache\n */\nvar filterFocusable = function (nodes, visibilityCache) {\n    return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(nodes)\n        .filter(function (node) { return (0,_is__WEBPACK_IMPORTED_MODULE_1__.isVisibleCached)(visibilityCache, node); })\n        .filter(function (node) { return (0,_is__WEBPACK_IMPORTED_MODULE_1__.notHiddenInput)(node); });\n};\nvar filterAutoFocusable = function (nodes, cache) {\n    if (cache === void 0) { cache = new Map(); }\n    return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(nodes).filter(function (node) { return (0,_is__WEBPACK_IMPORTED_MODULE_1__.isAutoFocusAllowedCached)(cache, node); });\n};\n/**\n * !__WARNING__! Low level API.\n * @returns all tabbable nodes\n *\n * @see {@link getFocusableNodes} to get any focusable element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nvar getTabbableNodes = function (topNodes, visibilityCache, withGuards) {\n    return (0,_tabOrder__WEBPACK_IMPORTED_MODULE_2__.orderByTabIndex)(filterFocusable((0,_tabUtils__WEBPACK_IMPORTED_MODULE_3__.getFocusables)(topNodes, withGuards), visibilityCache), true, withGuards);\n};\n/**\n * !__WARNING__! Low level API.\n *\n * @returns anything \"focusable\", not only tabbable. The difference is in `tabIndex=-1`\n * (without guards, as long as they are not expected to be ever focused)\n *\n * @see {@link getTabbableNodes} to get only tabble nodes element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nvar getFocusableNodes = function (topNodes, visibilityCache) {\n    return (0,_tabOrder__WEBPACK_IMPORTED_MODULE_2__.orderByTabIndex)(filterFocusable((0,_tabUtils__WEBPACK_IMPORTED_MODULE_3__.getFocusables)(topNodes), visibilityCache), false);\n};\n/**\n * return list of nodes which are expected to be auto-focused\n * @param topNode\n * @param visibilityCache\n */\nvar parentAutofocusables = function (topNode, visibilityCache) {\n    return filterFocusable((0,_tabUtils__WEBPACK_IMPORTED_MODULE_3__.getParentAutofocusables)(topNode), visibilityCache);\n};\n/*\n * Determines if element is contained in scope, including nested shadow DOMs\n */\nvar contains = function (scope, element) {\n    if (scope.shadowRoot) {\n        return contains(scope.shadowRoot, element);\n    }\n    else {\n        if (Object.getPrototypeOf(scope).contains !== undefined &&\n            Object.getPrototypeOf(scope).contains.call(scope, element)) {\n            return true;\n        }\n        return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(scope.children).some(function (child) {\n            var _a;\n            if (child instanceof HTMLIFrameElement) {\n                var iframeBody = (_a = child.contentDocument) === null || _a === void 0 ? void 0 : _a.body;\n                if (iframeBody) {\n                    return contains(iframeBody, element);\n                }\n                return false;\n            }\n            return contains(child, element);\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/DOMutils.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/all-affected.js":
/*!***********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/all-affected.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAllAffectedNodes: () => (/* binding */ getAllAffectedNodes)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js\");\n\n\n/**\n * in case of multiple nodes nested inside each other\n * keeps only top ones\n * this is O(nlogn)\n * @param nodes\n * @returns {*}\n */\nvar filterNested = function (nodes) {\n    var contained = new Set();\n    var l = nodes.length;\n    for (var i = 0; i < l; i += 1) {\n        for (var j = i + 1; j < l; j += 1) {\n            var position = nodes[i].compareDocumentPosition(nodes[j]);\n            /* eslint-disable no-bitwise */\n            if ((position & Node.DOCUMENT_POSITION_CONTAINED_BY) > 0) {\n                contained.add(j);\n            }\n            if ((position & Node.DOCUMENT_POSITION_CONTAINS) > 0) {\n                contained.add(i);\n            }\n            /* eslint-enable */\n        }\n    }\n    return nodes.filter(function (_, index) { return !contained.has(index); });\n};\n/**\n * finds top most parent for a node\n * @param node\n * @returns {*}\n */\nvar getTopParent = function (node) {\n    return node.parentNode ? getTopParent(node.parentNode) : node;\n};\n/**\n * returns all \"focus containers\" inside a given node\n * @param node - node or nodes to look inside\n * @returns Element[]\n */\nvar getAllAffectedNodes = function (node) {\n    var nodes = (0,_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(node);\n    return nodes.filter(Boolean).reduce(function (acc, currentNode) {\n        var group = currentNode.getAttribute(_constants__WEBPACK_IMPORTED_MODULE_1__.FOCUS_GROUP);\n        acc.push.apply(acc, (group\n            ? filterNested((0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(getTopParent(currentNode).querySelectorAll(\"[\".concat(_constants__WEBPACK_IMPORTED_MODULE_1__.FOCUS_GROUP, \"=\\\"\").concat(group, \"\\\"]:not([\").concat(_constants__WEBPACK_IMPORTED_MODULE_1__.FOCUS_DISABLED, \"=\\\"disabled\\\"])\"))))\n            : [currentNode]));\n        return acc;\n    }, []);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/all-affected.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asArray: () => (/* binding */ asArray),\n/* harmony export */   getFirst: () => (/* binding */ getFirst),\n/* harmony export */   toArray: () => (/* binding */ toArray)\n/* harmony export */ });\n/*\nIE11 support\n */\nvar toArray = function (a) {\n    var ret = Array(a.length);\n    for (var i = 0; i < a.length; ++i) {\n        ret[i] = a[i];\n    }\n    return ret;\n};\nvar asArray = function (a) { return (Array.isArray(a) ? a : [a]); };\nvar getFirst = function (a) { return (Array.isArray(a) ? a[0] : a); };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9hcnJheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0Esb0JBQW9CLGNBQWM7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDTyw2QkFBNkI7QUFDN0IsOEJBQThCIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmb2N1cy1sb2NrQDEuMy42XFxub2RlX21vZHVsZXNcXGZvY3VzLWxvY2tcXGRpc3RcXGVzMjAxNVxcdXRpbHNcXGFycmF5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG5JRTExIHN1cHBvcnRcbiAqL1xuZXhwb3J0IHZhciB0b0FycmF5ID0gZnVuY3Rpb24gKGEpIHtcbiAgICB2YXIgcmV0ID0gQXJyYXkoYS5sZW5ndGgpO1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgYS5sZW5ndGg7ICsraSkge1xuICAgICAgICByZXRbaV0gPSBhW2ldO1xuICAgIH1cbiAgICByZXR1cm4gcmV0O1xufTtcbmV4cG9ydCB2YXIgYXNBcnJheSA9IGZ1bmN0aW9uIChhKSB7IHJldHVybiAoQXJyYXkuaXNBcnJheShhKSA/IGEgOiBbYV0pOyB9O1xuZXhwb3J0IHZhciBnZXRGaXJzdCA9IGZ1bmN0aW9uIChhKSB7IHJldHVybiAoQXJyYXkuaXNBcnJheShhKSA/IGFbMF0gOiBhKTsgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/auto-focus.js":
/*!*********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/auto-focus.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pickAutofocus: () => (/* binding */ pickAutofocus)\n/* harmony export */ });\n/* harmony import */ var _DOMutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DOMutils */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _firstFocus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firstFocus */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/firstFocus.js\");\n/* harmony import */ var _is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/is.js\");\n\n\n\nvar findAutoFocused = function (autoFocusables) {\n    return function (node) {\n        var _a;\n        var autofocus = (_a = (0,_is__WEBPACK_IMPORTED_MODULE_0__.getDataset)(node)) === null || _a === void 0 ? void 0 : _a.autofocus;\n        return (\n        // @ts-expect-error\n        node.autofocus ||\n            //\n            (autofocus !== undefined && autofocus !== 'false') ||\n            //\n            autoFocusables.indexOf(node) >= 0);\n    };\n};\nvar pickAutofocus = function (nodesIndexes, orderedNodes, groups) {\n    var nodes = nodesIndexes.map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var autoFocusable = (0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.filterAutoFocusable)(nodes.filter(findAutoFocused(groups)));\n    if (autoFocusable && autoFocusable.length) {\n        return (0,_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFirstFocus)(autoFocusable);\n    }\n    return (0,_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFirstFocus)((0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.filterAutoFocusable)(orderedNodes));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/auto-focus.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/correctFocus.js":
/*!***********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/correctFocus.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   correctNode: () => (/* binding */ correctNode),\n/* harmony export */   correctNodes: () => (/* binding */ correctNodes)\n/* harmony export */ });\n/* harmony import */ var _is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/is.js\");\n\nvar findSelectedRadio = function (node, nodes) {\n    return nodes\n        .filter(_is__WEBPACK_IMPORTED_MODULE_0__.isRadioElement)\n        .filter(function (el) { return el.name === node.name; })\n        .filter(function (el) { return el.checked; })[0] || node;\n};\nvar correctNode = function (node, nodes) {\n    if ((0,_is__WEBPACK_IMPORTED_MODULE_0__.isRadioElement)(node) && node.name) {\n        return findSelectedRadio(node, nodes);\n    }\n    return node;\n};\n/**\n * giving a set of radio inputs keeps only selected (tabbable) ones\n * @param nodes\n */\nvar correctNodes = function (nodes) {\n    // IE11 has no Set(array) constructor\n    var resultSet = new Set();\n    nodes.forEach(function (node) { return resultSet.add(correctNode(node, nodes)); });\n    // using filter to support IE11\n    return nodes.filter(function (node) { return resultSet.has(node); });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9jb3JyZWN0Rm9jdXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNDO0FBQ3RDO0FBQ0E7QUFDQSxnQkFBZ0IsK0NBQWM7QUFDOUIsZ0NBQWdDLCtCQUErQjtBQUMvRCxnQ0FBZ0Msb0JBQW9CO0FBQ3BEO0FBQ087QUFDUCxRQUFRLG1EQUFjO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxvQ0FBb0MsaURBQWlEO0FBQ3JGO0FBQ0EsMENBQTBDLDZCQUE2QjtBQUN2RSIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZm9jdXMtbG9ja0AxLjMuNlxcbm9kZV9tb2R1bGVzXFxmb2N1cy1sb2NrXFxkaXN0XFxlczIwMTVcXHV0aWxzXFxjb3JyZWN0Rm9jdXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNSYWRpb0VsZW1lbnQgfSBmcm9tICcuL2lzJztcbnZhciBmaW5kU2VsZWN0ZWRSYWRpbyA9IGZ1bmN0aW9uIChub2RlLCBub2Rlcykge1xuICAgIHJldHVybiBub2Rlc1xuICAgICAgICAuZmlsdGVyKGlzUmFkaW9FbGVtZW50KVxuICAgICAgICAuZmlsdGVyKGZ1bmN0aW9uIChlbCkgeyByZXR1cm4gZWwubmFtZSA9PT0gbm9kZS5uYW1lOyB9KVxuICAgICAgICAuZmlsdGVyKGZ1bmN0aW9uIChlbCkgeyByZXR1cm4gZWwuY2hlY2tlZDsgfSlbMF0gfHwgbm9kZTtcbn07XG5leHBvcnQgdmFyIGNvcnJlY3ROb2RlID0gZnVuY3Rpb24gKG5vZGUsIG5vZGVzKSB7XG4gICAgaWYgKGlzUmFkaW9FbGVtZW50KG5vZGUpICYmIG5vZGUubmFtZSkge1xuICAgICAgICByZXR1cm4gZmluZFNlbGVjdGVkUmFkaW8obm9kZSwgbm9kZXMpO1xuICAgIH1cbiAgICByZXR1cm4gbm9kZTtcbn07XG4vKipcbiAqIGdpdmluZyBhIHNldCBvZiByYWRpbyBpbnB1dHMga2VlcHMgb25seSBzZWxlY3RlZCAodGFiYmFibGUpIG9uZXNcbiAqIEBwYXJhbSBub2Rlc1xuICovXG5leHBvcnQgdmFyIGNvcnJlY3ROb2RlcyA9IGZ1bmN0aW9uIChub2Rlcykge1xuICAgIC8vIElFMTEgaGFzIG5vIFNldChhcnJheSkgY29uc3RydWN0b3JcbiAgICB2YXIgcmVzdWx0U2V0ID0gbmV3IFNldCgpO1xuICAgIG5vZGVzLmZvckVhY2goZnVuY3Rpb24gKG5vZGUpIHsgcmV0dXJuIHJlc3VsdFNldC5hZGQoY29ycmVjdE5vZGUobm9kZSwgbm9kZXMpKTsgfSk7XG4gICAgLy8gdXNpbmcgZmlsdGVyIHRvIHN1cHBvcnQgSUUxMVxuICAgIHJldHVybiBub2Rlcy5maWx0ZXIoZnVuY3Rpb24gKG5vZGUpIHsgcmV0dXJuIHJlc3VsdFNldC5oYXMobm9kZSk7IH0pO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/correctFocus.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/firstFocus.js":
/*!*********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/firstFocus.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pickFirstFocus: () => (/* binding */ pickFirstFocus),\n/* harmony export */   pickFocusable: () => (/* binding */ pickFocusable)\n/* harmony export */ });\n/* harmony import */ var _correctFocus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./correctFocus */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/correctFocus.js\");\n\nvar pickFirstFocus = function (nodes) {\n    if (nodes[0] && nodes.length > 1) {\n        return (0,_correctFocus__WEBPACK_IMPORTED_MODULE_0__.correctNode)(nodes[0], nodes);\n    }\n    return nodes[0];\n};\nvar pickFocusable = function (nodes, node) {\n    return nodes.indexOf((0,_correctFocus__WEBPACK_IMPORTED_MODULE_0__.correctNode)(node, nodes));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9maXJzdEZvY3VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUN0QztBQUNQO0FBQ0EsZUFBZSwwREFBVztBQUMxQjtBQUNBO0FBQ0E7QUFDTztBQUNQLHlCQUF5QiwwREFBVztBQUNwQyIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZm9jdXMtbG9ja0AxLjMuNlxcbm9kZV9tb2R1bGVzXFxmb2N1cy1sb2NrXFxkaXN0XFxlczIwMTVcXHV0aWxzXFxmaXJzdEZvY3VzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvcnJlY3ROb2RlIH0gZnJvbSAnLi9jb3JyZWN0Rm9jdXMnO1xuZXhwb3J0IHZhciBwaWNrRmlyc3RGb2N1cyA9IGZ1bmN0aW9uIChub2Rlcykge1xuICAgIGlmIChub2Rlc1swXSAmJiBub2Rlcy5sZW5ndGggPiAxKSB7XG4gICAgICAgIHJldHVybiBjb3JyZWN0Tm9kZShub2Rlc1swXSwgbm9kZXMpO1xuICAgIH1cbiAgICByZXR1cm4gbm9kZXNbMF07XG59O1xuZXhwb3J0IHZhciBwaWNrRm9jdXNhYmxlID0gZnVuY3Rpb24gKG5vZGVzLCBub2RlKSB7XG4gICAgcmV0dXJuIG5vZGVzLmluZGV4T2YoY29ycmVjdE5vZGUobm9kZSwgbm9kZXMpKTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/firstFocus.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/getActiveElement.js":
/*!***************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/getActiveElement.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getActiveElement: () => (/* binding */ getActiveElement)\n/* harmony export */ });\n/* harmony import */ var _safe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./safe */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/safe.js\");\n/**\n * returns active element from document or from nested shadowdoms\n */\n\n/**\n * returns current active element. If the active element is a \"container\" itself(shadowRoot or iframe) returns active element inside it\n * @param [inDocument]\n */\nvar getActiveElement = function (inDocument) {\n    if (inDocument === void 0) { inDocument = document; }\n    if (!inDocument || !inDocument.activeElement) {\n        return undefined;\n    }\n    var activeElement = inDocument.activeElement;\n    return (activeElement.shadowRoot\n        ? getActiveElement(activeElement.shadowRoot)\n        : activeElement instanceof HTMLIFrameElement && (0,_safe__WEBPACK_IMPORTED_MODULE_0__.safeProbe)(function () { return activeElement.contentWindow.document; })\n            ? getActiveElement(activeElement.contentWindow.document)\n            : activeElement);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9nZXRBY3RpdmVFbGVtZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ21DO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELGdEQUFTLGVBQWUsOENBQThDO0FBQzlIO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZm9jdXMtbG9ja0AxLjMuNlxcbm9kZV9tb2R1bGVzXFxmb2N1cy1sb2NrXFxkaXN0XFxlczIwMTVcXHV0aWxzXFxnZXRBY3RpdmVFbGVtZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogcmV0dXJucyBhY3RpdmUgZWxlbWVudCBmcm9tIGRvY3VtZW50IG9yIGZyb20gbmVzdGVkIHNoYWRvd2RvbXNcbiAqL1xuaW1wb3J0IHsgc2FmZVByb2JlIH0gZnJvbSAnLi9zYWZlJztcbi8qKlxuICogcmV0dXJucyBjdXJyZW50IGFjdGl2ZSBlbGVtZW50LiBJZiB0aGUgYWN0aXZlIGVsZW1lbnQgaXMgYSBcImNvbnRhaW5lclwiIGl0c2VsZihzaGFkb3dSb290IG9yIGlmcmFtZSkgcmV0dXJucyBhY3RpdmUgZWxlbWVudCBpbnNpZGUgaXRcbiAqIEBwYXJhbSBbaW5Eb2N1bWVudF1cbiAqL1xuZXhwb3J0IHZhciBnZXRBY3RpdmVFbGVtZW50ID0gZnVuY3Rpb24gKGluRG9jdW1lbnQpIHtcbiAgICBpZiAoaW5Eb2N1bWVudCA9PT0gdm9pZCAwKSB7IGluRG9jdW1lbnQgPSBkb2N1bWVudDsgfVxuICAgIGlmICghaW5Eb2N1bWVudCB8fCAhaW5Eb2N1bWVudC5hY3RpdmVFbGVtZW50KSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIHZhciBhY3RpdmVFbGVtZW50ID0gaW5Eb2N1bWVudC5hY3RpdmVFbGVtZW50O1xuICAgIHJldHVybiAoYWN0aXZlRWxlbWVudC5zaGFkb3dSb290XG4gICAgICAgID8gZ2V0QWN0aXZlRWxlbWVudChhY3RpdmVFbGVtZW50LnNoYWRvd1Jvb3QpXG4gICAgICAgIDogYWN0aXZlRWxlbWVudCBpbnN0YW5jZW9mIEhUTUxJRnJhbWVFbGVtZW50ICYmIHNhZmVQcm9iZShmdW5jdGlvbiAoKSB7IHJldHVybiBhY3RpdmVFbGVtZW50LmNvbnRlbnRXaW5kb3cuZG9jdW1lbnQ7IH0pXG4gICAgICAgICAgICA/IGdldEFjdGl2ZUVsZW1lbnQoYWN0aXZlRWxlbWVudC5jb250ZW50V2luZG93LmRvY3VtZW50KVxuICAgICAgICAgICAgOiBhY3RpdmVFbGVtZW50KTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/is.js":
/*!*************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/is.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDataset: () => (/* binding */ getDataset),\n/* harmony export */   isAutoFocusAllowed: () => (/* binding */ isAutoFocusAllowed),\n/* harmony export */   isAutoFocusAllowedCached: () => (/* binding */ isAutoFocusAllowedCached),\n/* harmony export */   isDefined: () => (/* binding */ isDefined),\n/* harmony export */   isGuard: () => (/* binding */ isGuard),\n/* harmony export */   isHTMLButtonElement: () => (/* binding */ isHTMLButtonElement),\n/* harmony export */   isHTMLInputElement: () => (/* binding */ isHTMLInputElement),\n/* harmony export */   isNotAGuard: () => (/* binding */ isNotAGuard),\n/* harmony export */   isRadioElement: () => (/* binding */ isRadioElement),\n/* harmony export */   isVisibleCached: () => (/* binding */ isVisibleCached),\n/* harmony export */   notHiddenInput: () => (/* binding */ notHiddenInput)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js\");\n\nvar isElementHidden = function (node) {\n    // we can measure only \"elements\"\n    // consider others as \"visible\"\n    if (node.nodeType !== Node.ELEMENT_NODE) {\n        return false;\n    }\n    var computedStyle = window.getComputedStyle(node, null);\n    if (!computedStyle || !computedStyle.getPropertyValue) {\n        return false;\n    }\n    return (computedStyle.getPropertyValue('display') === 'none' || computedStyle.getPropertyValue('visibility') === 'hidden');\n};\nvar getParentNode = function (node) {\n    // DOCUMENT_FRAGMENT_NODE can also point on ShadowRoot. In this case .host will point on the next node\n    return node.parentNode && node.parentNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE\n        ? // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            node.parentNode.host\n        : node.parentNode;\n};\nvar isTopNode = function (node) {\n    // @ts-ignore\n    return node === document || (node && node.nodeType === Node.DOCUMENT_NODE);\n};\nvar isInert = function (node) { return node.hasAttribute('inert'); };\n/**\n * @see https://github.com/testing-library/jest-dom/blob/main/src/to-be-visible.js\n */\nvar isVisibleUncached = function (node, checkParent) {\n    return !node || isTopNode(node) || (!isElementHidden(node) && !isInert(node) && checkParent(getParentNode(node)));\n};\nvar isVisibleCached = function (visibilityCache, node) {\n    var cached = visibilityCache.get(node);\n    if (cached !== undefined) {\n        return cached;\n    }\n    var result = isVisibleUncached(node, isVisibleCached.bind(undefined, visibilityCache));\n    visibilityCache.set(node, result);\n    return result;\n};\nvar isAutoFocusAllowedUncached = function (node, checkParent) {\n    return node && !isTopNode(node) ? (isAutoFocusAllowed(node) ? checkParent(getParentNode(node)) : false) : true;\n};\nvar isAutoFocusAllowedCached = function (cache, node) {\n    var cached = cache.get(node);\n    if (cached !== undefined) {\n        return cached;\n    }\n    var result = isAutoFocusAllowedUncached(node, isAutoFocusAllowedCached.bind(undefined, cache));\n    cache.set(node, result);\n    return result;\n};\nvar getDataset = function (node) {\n    // @ts-ignore\n    return node.dataset;\n};\nvar isHTMLButtonElement = function (node) { return node.tagName === 'BUTTON'; };\nvar isHTMLInputElement = function (node) { return node.tagName === 'INPUT'; };\nvar isRadioElement = function (node) {\n    return isHTMLInputElement(node) && node.type === 'radio';\n};\nvar notHiddenInput = function (node) {\n    return !((isHTMLInputElement(node) || isHTMLButtonElement(node)) && (node.type === 'hidden' || node.disabled));\n};\nvar isAutoFocusAllowed = function (node) {\n    var attribute = node.getAttribute(_constants__WEBPACK_IMPORTED_MODULE_0__.FOCUS_NO_AUTOFOCUS);\n    return ![true, 'true', ''].includes(attribute);\n};\nvar isGuard = function (node) { var _a; return Boolean(node && ((_a = getDataset(node)) === null || _a === void 0 ? void 0 : _a.focusGuard)); };\nvar isNotAGuard = function (node) { return !isGuard(node); };\nvar isDefined = function (x) { return Boolean(x); };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/is.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/parenting.js":
/*!********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/parenting.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allParentAutofocusables: () => (/* binding */ allParentAutofocusables),\n/* harmony export */   getCommonParent: () => (/* binding */ getCommonParent),\n/* harmony export */   getTopCommonParent: () => (/* binding */ getTopCommonParent)\n/* harmony export */ });\n/* harmony import */ var _DOMutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DOMutils */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js\");\n\n\n\nvar getParents = function (node, parents) {\n    if (parents === void 0) { parents = []; }\n    parents.push(node);\n    if (node.parentNode) {\n        getParents(node.parentNode.host || node.parentNode, parents);\n    }\n    return parents;\n};\n/**\n * finds a parent for both nodeA and nodeB\n * @param nodeA\n * @param nodeB\n * @returns {boolean|*}\n */\nvar getCommonParent = function (nodeA, nodeB) {\n    var parentsA = getParents(nodeA);\n    var parentsB = getParents(nodeB);\n    // tslint:disable-next-line:prefer-for-of\n    for (var i = 0; i < parentsA.length; i += 1) {\n        var currentParent = parentsA[i];\n        if (parentsB.indexOf(currentParent) >= 0) {\n            return currentParent;\n        }\n    }\n    return false;\n};\nvar getTopCommonParent = function (baseActiveElement, leftEntry, rightEntries) {\n    var activeElements = (0,_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(baseActiveElement);\n    var leftEntries = (0,_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(leftEntry);\n    var activeElement = activeElements[0];\n    var topCommon = false;\n    leftEntries.filter(Boolean).forEach(function (entry) {\n        topCommon = getCommonParent(topCommon || entry, entry) || topCommon;\n        rightEntries.filter(Boolean).forEach(function (subEntry) {\n            var common = getCommonParent(activeElement, subEntry);\n            if (common) {\n                if (!topCommon || (0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.contains)(common, topCommon)) {\n                    topCommon = common;\n                }\n                else {\n                    topCommon = getCommonParent(common, topCommon);\n                }\n            }\n        });\n    });\n    // TODO: add assert here?\n    return topCommon;\n};\n/**\n * return list of nodes which are expected to be autofocused inside a given top nodes\n * @param entries\n * @param visibilityCache\n */\nvar allParentAutofocusables = function (entries, visibilityCache) {\n    return entries.reduce(function (acc, node) { return acc.concat((0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.parentAutofocusables)(node, visibilityCache)); }, []);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/parenting.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/safe.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/safe.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safeProbe: () => (/* binding */ safeProbe)\n/* harmony export */ });\nvar safeProbe = function (cb) {\n    try {\n        return cb();\n    }\n    catch (e) {\n        return undefined;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9zYWZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmb2N1cy1sb2NrQDEuMy42XFxub2RlX21vZHVsZXNcXGZvY3VzLWxvY2tcXGRpc3RcXGVzMjAxNVxcdXRpbHNcXHNhZmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBzYWZlUHJvYmUgPSBmdW5jdGlvbiAoY2IpIHtcbiAgICB0cnkge1xuICAgICAgICByZXR1cm4gY2IoKTtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/safe.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabOrder.js":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabOrder.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orderByTabIndex: () => (/* binding */ orderByTabIndex),\n/* harmony export */   tabSort: () => (/* binding */ tabSort)\n/* harmony export */ });\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js\");\n\nvar tabSort = function (a, b) {\n    var aTab = Math.max(0, a.tabIndex);\n    var bTab = Math.max(0, b.tabIndex);\n    var tabDiff = aTab - bTab;\n    var indexDiff = a.index - b.index;\n    if (tabDiff) {\n        if (!aTab) {\n            return 1;\n        }\n        if (!bTab) {\n            return -1;\n        }\n    }\n    return tabDiff || indexDiff;\n};\nvar getTabIndex = function (node) {\n    if (node.tabIndex < 0) {\n        // all \"focusable\" elements are already preselected\n        // but some might have implicit negative tabIndex\n        // return 0 for <audio without tabIndex attribute - it is \"tabbable\"\n        if (!node.hasAttribute('tabindex')) {\n            return 0;\n        }\n    }\n    return node.tabIndex;\n};\nvar orderByTabIndex = function (nodes, filterNegative, keepGuards) {\n    return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(nodes)\n        .map(function (node, index) {\n        var tabIndex = getTabIndex(node);\n        return {\n            node: node,\n            index: index,\n            tabIndex: keepGuards && tabIndex === -1 ? ((node.dataset || {}).focusGuard ? 0 : -1) : tabIndex,\n        };\n    })\n        .filter(function (data) { return !filterNegative || data.tabIndex >= 0; })\n        .sort(tabSort);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabOrder.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabUtils.js":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabUtils.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFocusables: () => (/* binding */ getFocusables),\n/* harmony export */   getParentAutofocusables: () => (/* binding */ getParentAutofocusables)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./array */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _tabbables__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tabbables */ \"(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabbables.js\");\n\n\n\nvar queryTabbables = _tabbables__WEBPACK_IMPORTED_MODULE_0__.tabbables.join(',');\nvar queryGuardTabbables = \"\".concat(queryTabbables, \", [data-focus-guard]\");\nvar getFocusablesWithShadowDom = function (parent, withGuards) {\n    return (0,_array__WEBPACK_IMPORTED_MODULE_1__.toArray)((parent.shadowRoot || parent).children).reduce(function (acc, child) {\n        return acc.concat(child.matches(withGuards ? queryGuardTabbables : queryTabbables) ? [child] : [], getFocusablesWithShadowDom(child));\n    }, []);\n};\nvar getFocusablesWithIFrame = function (parent, withGuards) {\n    var _a;\n    // contentDocument of iframe will be null if current origin cannot access it\n    if (parent instanceof HTMLIFrameElement && ((_a = parent.contentDocument) === null || _a === void 0 ? void 0 : _a.body)) {\n        return getFocusables([parent.contentDocument.body], withGuards);\n    }\n    return [parent];\n};\nvar getFocusables = function (parents, withGuards) {\n    return parents.reduce(function (acc, parent) {\n        var _a;\n        var focusableWithShadowDom = getFocusablesWithShadowDom(parent, withGuards);\n        var focusableWithIframes = (_a = []).concat.apply(_a, focusableWithShadowDom.map(function (node) { return getFocusablesWithIFrame(node, withGuards); }));\n        return acc.concat(\n        // add all tabbables inside and within shadow DOMs in DOM order\n        focusableWithIframes, \n        // add if node is tabbable itself\n        parent.parentNode\n            ? (0,_array__WEBPACK_IMPORTED_MODULE_1__.toArray)(parent.parentNode.querySelectorAll(queryTabbables)).filter(function (node) { return node === parent; })\n            : []);\n    }, []);\n};\n/**\n * return a list of focusable nodes within an area marked as \"auto-focusable\"\n * @param parent\n */\nvar getParentAutofocusables = function (parent) {\n    var parentFocus = parent.querySelectorAll(\"[\".concat(_constants__WEBPACK_IMPORTED_MODULE_2__.FOCUS_AUTO, \"]\"));\n    return (0,_array__WEBPACK_IMPORTED_MODULE_1__.toArray)(parentFocus)\n        .map(function (node) { return getFocusables([node]); })\n        .reduce(function (acc, nodes) { return acc.concat(nodes); }, []);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabUtils.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabbables.js":
/*!********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabbables.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tabbables: () => (/* binding */ tabbables)\n/* harmony export */ });\n/**\n * list of the object to be considered as focusable\n */\nvar tabbables = [\n    'button:enabled',\n    'select:enabled',\n    'textarea:enabled',\n    'input:enabled',\n    // elements with explicit roles will also use explicit tabindex\n    // '[role=\"button\"]',\n    'a[href]',\n    'area[href]',\n    'summary',\n    'iframe',\n    'object',\n    'embed',\n    'audio[controls]',\n    'video[controls]',\n    '[tabindex]',\n    '[contenteditable]',\n    '[autofocus]',\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZm9jdXMtbG9ja0AxLjMuNi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy90YWJiYWJsZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmb2N1cy1sb2NrQDEuMy42XFxub2RlX21vZHVsZXNcXGZvY3VzLWxvY2tcXGRpc3RcXGVzMjAxNVxcdXRpbHNcXHRhYmJhYmxlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGxpc3Qgb2YgdGhlIG9iamVjdCB0byBiZSBjb25zaWRlcmVkIGFzIGZvY3VzYWJsZVxuICovXG5leHBvcnQgdmFyIHRhYmJhYmxlcyA9IFtcbiAgICAnYnV0dG9uOmVuYWJsZWQnLFxuICAgICdzZWxlY3Q6ZW5hYmxlZCcsXG4gICAgJ3RleHRhcmVhOmVuYWJsZWQnLFxuICAgICdpbnB1dDplbmFibGVkJyxcbiAgICAvLyBlbGVtZW50cyB3aXRoIGV4cGxpY2l0IHJvbGVzIHdpbGwgYWxzbyB1c2UgZXhwbGljaXQgdGFiaW5kZXhcbiAgICAvLyAnW3JvbGU9XCJidXR0b25cIl0nLFxuICAgICdhW2hyZWZdJyxcbiAgICAnYXJlYVtocmVmXScsXG4gICAgJ3N1bW1hcnknLFxuICAgICdpZnJhbWUnLFxuICAgICdvYmplY3QnLFxuICAgICdlbWJlZCcsXG4gICAgJ2F1ZGlvW2NvbnRyb2xzXScsXG4gICAgJ3ZpZGVvW2NvbnRyb2xzXScsXG4gICAgJ1t0YWJpbmRleF0nLFxuICAgICdbY29udGVudGVkaXRhYmxlXScsXG4gICAgJ1thdXRvZm9jdXNdJyxcbl07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/focus-lock@1.3.6/node_modules/focus-lock/dist/es2015/utils/tabbables.js\n"));

/***/ })

}]);