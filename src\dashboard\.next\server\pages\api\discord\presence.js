"use strict";(()=>{var e={};e.id=6671,e.ids=[6671],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},22090:(e,t,r)=>{r.r(t),r.d(t,{config:()=>b,default:()=>y,routeModule:()=>h});var i={};r.r(i),r.d(i,{default:()=>m});var a=r(93433),s=r(20264),o=r(20584),n=r(15806),d=r(94506),u=r(98580),l=r(12518);let c=null,p=u.dashboardConfig.database?.url||"mongodb://localhost:27017",f=u.dashboardConfig.database?.name||"discord_bot";async function v(){return c||(c=await l.MongoClient.connect(p,{...u.dashboardConfig.database?.options||{}})),c.db(f)}async function m(e,t){let r=await (0,n.getServerSession)(e,t,d.authOptions);if(!r?.user)return t.status(401).json({error:"Unauthorized"});try{let i=(await v()).collection("presence");if("GET"===e.method){let e=await i.findOne({key:"activities"});return t.status(200).json({activities:e?.activities||[]})}if("POST"===e.method){if(!r.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});let{activities:a,activityRotationInterval:s}=e.body;if(!Array.isArray(a))return t.status(400).json({error:"Invalid activities"});let o=s||60;if("number"!=typeof o||o<10||o>3600)return t.status(400).json({error:"Invalid rotation interval (must be 10-3600 seconds)"});let n=["PLAYING","STREAMING","LISTENING","WATCHING","COMPETING"];for(let e of a)if("object"!=typeof e||"string"!=typeof e.name||!n.includes(e.type))return t.status(400).json({error:"Invalid activity entry"});return await i.updateOne({key:"activities"},{$set:{activities:a,activityRotationInterval:o,key:"activities"}},{upsert:!0}),t.status(200).json({message:"Activities updated",activities:a,activityRotationInterval:o})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Server error",details:e.message})}}let y=(0,o.M)(i,"default"),b=(0,o.M)(i,"config"),h=new a.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/discord/presence",pathname:"/api/discord/presence",bundlePath:"",filename:""},userland:i})},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(22090));module.exports=i})();