/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  devIndicators: false,
  // Transpile these ESM-only packages
  transpilePackages: ['@monaco-editor/react', 'monaco-editor', 'monaco-yaml', 'jsonc-parser'],

  // Configure experimental features
  experimental: {
    serverActions: true,
    // Moving serverComponentsExternalPackages to the new location
    serverExternalPackages: []
  },

  // silence the "require function is used ..." warnings coming from jsonc-parser
  webpack(config, { isServer }) {
    const ignored = /jsonc-parser[\\/].+Critical dependency: require function/;
    config.ignoreWarnings = [
      ...(config.ignoreWarnings || []),
      (warn) =>
        typeof warn.message === 'string' &&
        ignored.test(warn.message),
    ];

    // Ensure Monaco uses the ESM build in the browser bundle
    if (!isServer) {
      config.resolve.alias = {
        ...config.resolve.alias,
        'monaco-editor': 'monaco-editor/esm/vs/editor/editor.api'
      };
    }

    // Workers expect globalThis to be 'self'
    config.output = {
      ...config.output,
      globalObject: 'self',
    };

    return config;
  },

  // Configure hostname and port for Pterodactyl
  hostname: '0.0.0.0',
  port: process.env.SERVER_PORT || 3000
};

export default nextConfig; 