(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2432],{10241:(t,n,e)=>{"use strict";e.d(n,{j:()=>function t(n,e){var r,o;if(n===e)return!0;if(n&&e&&(r=n.constructor)===e.constructor){if(r===Date)return n.getTime()===e.getTime();if(r===RegExp)return n.toString()===e.toString();if(r===Array){if((o=n.length)===e.length)for(;o--&&t(n[o],e[o]););return -1===o}if(!r||"object"==typeof n){for(r in o=0,n)if(i.call(n,r)&&++o&&!i.call(e,r)||!(r in e)||!t(n[r],e[r]))return!1;return Object.keys(e).length===o}}return n!=n&&e!=e}});var i=Object.prototype.hasOwnProperty},10903:(t,n,e)=>{"use strict";e.d(n,{s_:()=>R,GS:()=>T});var i=e(47096),r=e(86423),o=e(71928),s=e(96472),u=e(68108),h=(0,i.J)("start","end","cancel","interrupt"),a=[];function c(t,n,e,i,r,o){var s=t.__transition;if(s){if(e in s)return}else t.__transition={};!function(t,n,e){var i,r=t.__transition;function o(a){var c,f,l,p;if(1!==e.state)return h();for(c in r)if((p=r[c]).name===e.name){if(3===p.state)return(0,u.wR)(o);4===p.state?(p.state=6,p.timer.stop(),p.on.call("interrupt",t,t.__data__,p.index,p.group),delete r[c]):+c<n&&(p.state=6,p.timer.stop(),p.on.call("cancel",t,t.__data__,p.index,p.group),delete r[c])}if((0,u.wR)(function(){3===e.state&&(e.state=4,e.timer.restart(s,e.delay,e.time),s(a))}),e.state=2,e.on.call("start",t,t.__data__,e.index,e.group),2===e.state){for(c=0,e.state=3,i=Array(l=e.tween.length),f=-1;c<l;++c)(p=e.tween[c].value.call(t,t.__data__,e.index,e.group))&&(i[++f]=p);i.length=f+1}}function s(n){for(var r=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(h),e.state=5,1),o=-1,s=i.length;++o<s;)i[o].call(t,r);5===e.state&&(e.on.call("end",t,t.__data__,e.index,e.group),h())}function h(){for(var i in e.state=6,e.timer.stop(),delete r[n],r)return;delete t.__transition}r[n]=e,e.timer=(0,u.O1)(function(t){e.state=1,e.timer.restart(o,e.delay,e.time),e.delay<=t&&o(t-e.delay)},0,e.time)}(t,e,{name:n,index:i,group:r,on:h,tween:a,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function f(t,n){var e=p(t,n);if(e.state>0)throw Error("too late; already scheduled");return e}function l(t,n){var e=p(t,n);if(e.state>3)throw Error("too late; already running");return e}function p(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw Error("transition not found");return e}function v(t,n){var e,i,r,o=t.__transition,s=!0;if(o){for(r in n=null==n?null:n+"",o){if((e=o[r]).name!==n){s=!1;continue}i=e.state>2&&e.state<5,e.state=6,e.timer.stop(),e.on.call(i?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[r]}s&&delete t.__transition}}function d(t,n,e){var i=t._id;return t.each(function(){var t=l(this,i);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return p(t,i).value[n]}}var m=e(68056);function g(t,n){var e;return("number"==typeof n?o.Dj:n instanceof m.yW?o.Zr:(e=(0,m.yW)(n))?(n=e,o.Zr):o.zl)(t,n)}var _=s.r1.prototype.constructor;function y(t){return function(){this.style.removeProperty(t)}}var w=0;function x(t,n,e,i){this._groups=t,this._parents=n,this._name=e,this._id=i}var E=s.r1.prototype;x.prototype=(function(t){return(0,s.r1)().transition(t)}).prototype={constructor:x,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=(0,s.gD)(t));for(var i=this._groups,r=i.length,o=Array(r),u=0;u<r;++u)for(var h,a,f=i[u],l=f.length,v=o[u]=Array(l),d=0;d<l;++d)(h=f[d])&&(a=t.call(h,h.__data__,d,f))&&("__data__"in h&&(a.__data__=h.__data__),v[d]=a,c(v[d],n,e,d,v,p(h,e)));return new x(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=(0,s.XM)(t));for(var i=this._groups,r=i.length,o=[],u=[],h=0;h<r;++h)for(var a,f=i[h],l=f.length,v=0;v<l;++v)if(a=f[v]){for(var d,m=t.call(a,a.__data__,v,f),g=p(a,e),_=0,y=m.length;_<y;++_)(d=m[_])&&c(d,n,e,_,m,g);o.push(m),u.push(a)}return new x(o,u,n,e)},selectChild:E.selectChild,selectChildren:E.selectChildren,filter:function(t){"function"!=typeof t&&(t=(0,s.jN)(t));for(var n=this._groups,e=n.length,i=Array(e),r=0;r<e;++r)for(var o,u=n[r],h=u.length,a=i[r]=[],c=0;c<h;++c)(o=u[c])&&t.call(o,o.__data__,c,u)&&a.push(o);return new x(i,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var n=this._groups,e=t._groups,i=n.length,r=e.length,o=Math.min(i,r),s=Array(i),u=0;u<o;++u)for(var h,a=n[u],c=e[u],f=a.length,l=s[u]=Array(f),p=0;p<f;++p)(h=a[p]||c[p])&&(l[p]=h);for(;u<i;++u)s[u]=n[u];return new x(s,this._parents,this._name,this._id)},selection:function(){return new _(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=++w,i=this._groups,r=i.length,o=0;o<r;++o)for(var s,u=i[o],h=u.length,a=0;a<h;++a)if(s=u[a]){var f=p(s,n);c(s,t,e,a,u,{time:f.time+f.delay+f.duration,delay:0,duration:f.duration,ease:f.ease})}return new x(i,this._parents,t,e)},call:E.call,nodes:E.nodes,node:E.node,size:E.size,empty:E.empty,each:E.each,on:function(t,n){var e,i,r,o,s,u,h=this._id;return arguments.length<2?p(this.node(),h).on.on(t):this.each((e=h,i=t,r=n,u=(i+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})?f:l,function(){var t=u(this,e),n=t.on;n!==o&&(s=(o=n).copy()).on(i,r),t.on=s}))},attr:function(t,n){var e=(0,s.MF)(t),i="transform"===e?o.pN:g;return this.attrTween(t,"function"==typeof n?(e.local?function(t,n,e){var i,r,o;return function(){var s,u,h=e(this);return null==h?void this.removeAttributeNS(t.space,t.local):(s=this.getAttributeNS(t.space,t.local))===(u=h+"")?null:s===i&&u===r?o:(r=u,o=n(i=s,h))}}:function(t,n,e){var i,r,o;return function(){var s,u,h=e(this);return null==h?void this.removeAttribute(t):(s=this.getAttribute(t))===(u=h+"")?null:s===i&&u===r?o:(r=u,o=n(i=s,h))}})(e,i,d(this,"attr."+t,n)):null==n?(e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(e):(e.local?function(t,n,e){var i,r,o=e+"";return function(){var s=this.getAttributeNS(t.space,t.local);return s===o?null:s===i?r:r=n(i=s,e)}}:function(t,n,e){var i,r,o=e+"";return function(){var s=this.getAttribute(t);return s===o?null:s===i?r:r=n(i=s,e)}})(e,i,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw Error();var i=(0,s.MF)(t);return this.tween(e,(i.local?function(t,n){var e,i;function r(){var r=n.apply(this,arguments);return r!==i&&(e=(i=r)&&function(n){this.setAttributeNS(t.space,t.local,r.call(this,n))}),e}return r._value=n,r}:function(t,n){var e,i;function r(){var r=n.apply(this,arguments);return r!==i&&(e=(i=r)&&function(n){this.setAttribute(t,r.call(this,n))}),e}return r._value=n,r})(i,n))},style:function(t,n,e){var i,r,u,h,a,c,f,p,v,m,_,w,x,E,b,N,z,k,T,M,A,O="transform"==(t+="")?o.TE:g;return null==n?this.styleTween(t,(i=t,function(){var t=(0,s.iF)(this,i),n=(this.style.removeProperty(i),(0,s.iF)(this,i));return t===n?null:t===r&&n===u?h:h=O(r=t,u=n)})).on("end.style."+t,y(t)):"function"==typeof n?this.styleTween(t,(a=t,c=d(this,"style."+t,n),function(){var t=(0,s.iF)(this,a),n=c(this),e=n+"";return null==n&&(this.style.removeProperty(a),e=n=(0,s.iF)(this,a)),t===e?null:t===f&&e===p?v:(p=e,v=O(f=t,n))})).each((m=this._id,z="end."+(N="style."+(_=t)),function(){var t=l(this,m),n=t.on,e=null==t.value[N]?b||(b=y(_)):void 0;(n!==w||E!==e)&&(x=(w=n).copy()).on(z,E=e),t.on=x})):this.styleTween(t,(k=t,A=n+"",function(){var t=(0,s.iF)(this,k);return t===A?null:t===T?M:M=O(T=t,n)}),e).on("end.style."+t,null)},styleTween:function(t,n,e){var i="style."+(t+="");if(arguments.length<2)return(i=this.tween(i))&&i._value;if(null==n)return this.tween(i,null);if("function"!=typeof n)throw Error();return this.tween(i,function(t,n,e){var i,r;function o(){var o=n.apply(this,arguments);return o!==r&&(i=(r=o)&&function(n){this.style.setProperty(t,o.call(this,n),e)}),i}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){var n,e;return this.tween("text","function"==typeof t?(n=d(this,"text",t),function(){var t=n(this);this.textContent=null==t?"":t}):(e=null==t?"":t+"",function(){this.textContent=e}))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();return this.tween(n,function(t){var n,e;function i(){var i=t.apply(this,arguments);return i!==e&&(n=(e=i)&&function(t){this.textContent=i.call(this,t)}),n}return i._value=t,i}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var i,r=p(this.node(),e).tween,o=0,s=r.length;o<s;++o)if((i=r[o]).name===t)return i.value;return null}return this.each((null==n?function(t,n){var e,i;return function(){var r=l(this,t),o=r.tween;if(o!==e){i=e=o;for(var s=0,u=i.length;s<u;++s)if(i[s].name===n){(i=i.slice()).splice(s,1);break}}r.tween=i}}:function(t,n,e){var i,r;if("function"!=typeof e)throw Error();return function(){var o=l(this,t),s=o.tween;if(s!==i){r=(i=s).slice();for(var u={name:n,value:e},h=0,a=r.length;h<a;++h)if(r[h].name===n){r[h]=u;break}h===a&&r.push(u)}o.tween=r}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){f(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n*=1,function(){f(this,t).delay=n}})(n,t)):p(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){l(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n*=1,function(){l(this,t).duration=n}})(n,t)):p(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw Error();return function(){l(this,t).ease=n}}(n,t)):p(this.node(),n).ease},easeVarying:function(t){var n;if("function"!=typeof t)throw Error();return this.each((n=this._id,function(){var e=t.apply(this,arguments);if("function"!=typeof e)throw Error();l(this,n).ease=e}))},end:function(){var t,n,e=this,i=e._id,r=e.size();return new Promise(function(o,s){var u={value:s},h={value:function(){0==--r&&o()}};e.each(function(){var e=l(this,i),r=e.on;r!==t&&((n=(t=r).copy())._.cancel.push(u),n._.interrupt.push(u),n._.end.push(h)),e.on=n}),0===r&&o()})},[Symbol.iterator]:E[Symbol.iterator]};var b={time:null,delay:0,duration:250,ease:e(61289).oR};s.r1.prototype.interrupt=function(t){return this.each(function(){v(this,t)})},s.r1.prototype.transition=function(t){var n,e;t instanceof x?(n=t._id,t=t._name):(n=++w,(e=b).time=(0,u.tB)(),t=null==t?null:t+"");for(var i=this._groups,r=i.length,o=0;o<r;++o)for(var s,h=i[o],a=h.length,f=0;f<a;++f)(s=h[f])&&c(s,t,n,f,h,e||function(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw Error(`transition ${n} not found`);return e}(s,n));return new x(i,this._parents,t,n)};let N=t=>()=>t;function z(t,{sourceEvent:n,target:e,transform:i,dispatch:r}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:i,enumerable:!0,configurable:!0},_:{value:r}})}function k(t,n,e){this.k=t,this.x=n,this.y=e}k.prototype={constructor:k,scale:function(t){return 1===t?this:new k(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new k(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var T=new k(1,0,0);function M(t){t.stopImmediatePropagation()}function A(t){t.preventDefault(),t.stopImmediatePropagation()}function O(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function D(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function L(){return this.__zoom||T}function P(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function S(){return navigator.maxTouchPoints||"ontouchstart"in this}function q(t,n,e){var i=t.invertX(n[0][0])-e[0][0],r=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],s=t.invertY(n[1][1])-e[1][1];return t.translate(r>i?(i+r)/2:Math.min(0,i)||Math.max(0,r),s>o?(o+s)/2:Math.min(0,o)||Math.max(0,s))}function R(){var t,n,e,u=O,h=D,a=q,c=P,f=S,l=[0,1/0],p=[[-1/0,-1/0],[1/0,1/0]],d=250,m=o.p7,g=(0,i.J)("start","zoom","end"),_=0,y=10;function w(t){t.property("__zoom",L).on("wheel.zoom",W,{passive:!1}).on("mousedown.zoom",I).on("dblclick.zoom",U).filter(f).on("touchstart.zoom",X).on("touchmove.zoom",j).on("touchend.zoom touchcancel.zoom",Y).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function x(t,n){return(n=Math.max(l[0],Math.min(l[1],n)))===t.k?t:new k(n,t.x,t.y)}function E(t,n,e){var i=n[0]-e[0]*t.k,r=n[1]-e[1]*t.k;return i===t.x&&r===t.y?t:new k(t.k,i,r)}function b(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function R(t,n,e,i){t.on("start.zoom",function(){C(this,arguments).event(i).start()}).on("interrupt.zoom end.zoom",function(){C(this,arguments).event(i).end()}).tween("zoom",function(){var t=arguments,r=C(this,t).event(i),o=h.apply(this,t),s=null==e?b(o):"function"==typeof e?e.apply(this,t):e,u=Math.max(o[1][0]-o[0][0],o[1][1]-o[0][1]),a=this.__zoom,c="function"==typeof n?n.apply(this,t):n,f=m(a.invert(s).concat(u/a.k),c.invert(s).concat(u/c.k));return function(t){if(1===t)t=c;else{var n=f(t),e=u/n[2];t=new k(e,s[0]-n[0]*e,s[1]-n[1]*e)}r.zoom(null,t)}})}function C(t,n,e){return!e&&t.__zooming||new F(t,n)}function F(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=h.apply(t,n),this.taps=0}function W(t,...n){if(u.apply(this,arguments)){var e=C(this,n).event(t),i=this.__zoom,r=Math.max(l[0],Math.min(l[1],i.k*Math.pow(2,c.apply(this,arguments)))),o=(0,s.Wn)(t);if(e.wheel)(e.mouse[0][0]!==o[0]||e.mouse[0][1]!==o[1])&&(e.mouse[1]=i.invert(e.mouse[0]=o)),clearTimeout(e.wheel);else{if(i.k===r)return;e.mouse=[o,i.invert(o)],v(this),e.start()}A(t),e.wheel=setTimeout(function(){e.wheel=null,e.end()},150),e.zoom("mouse",a(E(x(i,r),e.mouse[0],e.mouse[1]),e.extent,p))}}function I(t,...n){if(!e&&u.apply(this,arguments)){var i=t.currentTarget,o=C(this,n,!0).event(t),h=(0,s.Lt)(t.view).on("mousemove.zoom",function(t){if(A(t),!o.moved){var n=t.clientX-f,e=t.clientY-l;o.moved=n*n+e*e>_}o.event(t).zoom("mouse",a(E(o.that.__zoom,o.mouse[0]=(0,s.Wn)(t,i),o.mouse[1]),o.extent,p))},!0).on("mouseup.zoom",function(t){h.on("mousemove.zoom mouseup.zoom",null),(0,r.EH)(t.view,o.moved),A(t),o.event(t).end()},!0),c=(0,s.Wn)(t,i),f=t.clientX,l=t.clientY;(0,r.XD)(t.view),M(t),o.mouse=[c,this.__zoom.invert(c)],v(this),o.start()}}function U(t,...n){if(u.apply(this,arguments)){var e=this.__zoom,i=(0,s.Wn)(t.changedTouches?t.changedTouches[0]:t,this),r=e.invert(i),o=e.k*(t.shiftKey?.5:2),c=a(E(x(e,o),i,r),h.apply(this,n),p);A(t),d>0?(0,s.Lt)(this).transition().duration(d).call(R,c,i,t):(0,s.Lt)(this).call(w.transform,c,i,t)}}function X(e,...i){if(u.apply(this,arguments)){var r,o,h,a,c=e.touches,f=c.length,l=C(this,i,e.changedTouches.length===f).event(e);for(M(e),o=0;o<f;++o)h=c[o],a=[a=(0,s.Wn)(h,this),this.__zoom.invert(a),h.identifier],l.touch0?l.touch1||l.touch0[2]===a[2]||(l.touch1=a,l.taps=0):(l.touch0=a,r=!0,l.taps=1+!!t);t&&(t=clearTimeout(t)),r&&(l.taps<2&&(n=a[0],t=setTimeout(function(){t=null},500)),v(this),l.start())}}function j(t,...n){if(this.__zooming){var e,i,r,o,u=C(this,n).event(t),h=t.changedTouches,c=h.length;for(A(t),e=0;e<c;++e)i=h[e],r=(0,s.Wn)(i,this),u.touch0&&u.touch0[2]===i.identifier?u.touch0[0]=r:u.touch1&&u.touch1[2]===i.identifier&&(u.touch1[0]=r);if(i=u.that.__zoom,u.touch1){var f=u.touch0[0],l=u.touch0[1],v=u.touch1[0],d=u.touch1[1],m=(m=v[0]-f[0])*m+(m=v[1]-f[1])*m,g=(g=d[0]-l[0])*g+(g=d[1]-l[1])*g;i=x(i,Math.sqrt(m/g)),r=[(f[0]+v[0])/2,(f[1]+v[1])/2],o=[(l[0]+d[0])/2,(l[1]+d[1])/2]}else{if(!u.touch0)return;r=u.touch0[0],o=u.touch0[1]}u.zoom("touch",a(E(i,r,o),u.extent,p))}}function Y(t,...i){if(this.__zooming){var r,o,u=C(this,i).event(t),h=t.changedTouches,a=h.length;for(M(t),e&&clearTimeout(e),e=setTimeout(function(){e=null},500),r=0;r<a;++r)o=h[r],u.touch0&&u.touch0[2]===o.identifier?delete u.touch0:u.touch1&&u.touch1[2]===o.identifier&&delete u.touch1;if(u.touch1&&!u.touch0&&(u.touch0=u.touch1,delete u.touch1),u.touch0)u.touch0[1]=this.__zoom.invert(u.touch0[0]);else if(u.end(),2===u.taps&&(o=(0,s.Wn)(o,this),Math.hypot(n[0]-o[0],n[1]-o[1])<y)){var c=(0,s.Lt)(this).on("dblclick.zoom");c&&c.apply(this,arguments)}}}return w.transform=function(t,n,e,i){var r=t.selection?t.selection():t;r.property("__zoom",L),t!==r?R(t,n,e,i):r.interrupt().each(function(){C(this,arguments).event(i).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},w.scaleBy=function(t,n,e,i){w.scaleTo(t,function(){var t=this.__zoom.k,e="function"==typeof n?n.apply(this,arguments):n;return t*e},e,i)},w.scaleTo=function(t,n,e,i){w.transform(t,function(){var t=h.apply(this,arguments),i=this.__zoom,r=null==e?b(t):"function"==typeof e?e.apply(this,arguments):e,o=i.invert(r),s="function"==typeof n?n.apply(this,arguments):n;return a(E(x(i,s),r,o),t,p)},e,i)},w.translateBy=function(t,n,e,i){w.transform(t,function(){return a(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),h.apply(this,arguments),p)},null,i)},w.translateTo=function(t,n,e,i,r){w.transform(t,function(){var t=h.apply(this,arguments),r=this.__zoom,o=null==i?b(t):"function"==typeof i?i.apply(this,arguments):i;return a(T.translate(o[0],o[1]).scale(r.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,p)},i,r)},F.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=(0,s.Lt)(this.that).datum();g.call(t,this.that,new z(t,{sourceEvent:this.sourceEvent,target:w,type:t,transform:this.that.__zoom,dispatch:g}),n)}},w.wheelDelta=function(t){return arguments.length?(c="function"==typeof t?t:N(+t),w):c},w.filter=function(t){return arguments.length?(u="function"==typeof t?t:N(!!t),w):u},w.touchable=function(t){return arguments.length?(f="function"==typeof t?t:N(!!t),w):f},w.extent=function(t){return arguments.length?(h="function"==typeof t?t:N([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),w):h},w.scaleExtent=function(t){return arguments.length?(l[0]=+t[0],l[1]=+t[1],w):[l[0],l[1]]},w.translateExtent=function(t){return arguments.length?(p[0][0]=+t[0][0],p[1][0]=+t[1][0],p[0][1]=+t[0][1],p[1][1]=+t[1][1],w):[[p[0][0],p[0][1]],[p[1][0],p[1][1]]]},w.constrain=function(t){return arguments.length?(a=t,w):a},w.duration=function(t){return arguments.length?(d=+t,w):d},w.interpolate=function(t){return arguments.length?(m=t,w):m},w.on=function(){var t=g.on.apply(g,arguments);return t===g?w:t},w.clickDistance=function(t){return arguments.length?(_=(t*=1)*t,w):Math.sqrt(_)},w.tapDistance=function(t){return arguments.length?(y=+t,w):y},w}k.prototype},50985:function(t,n,e){var i;!function(r){"use strict";var o,s={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,h="[DecimalError] ",a=h+"Invalid argument: ",c=h+"Exponent out of range: ",f=Math.floor,l=Math.pow,p=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,v=f(1286742750677284.5),d={};function m(t,n){var e,i,r,o,s,h,a,c,f=t.constructor,l=f.precision;if(!t.s||!n.s)return n.s||(n=new f(t)),u?k(n,l):n;if(a=t.d,c=n.d,s=t.e,r=n.e,a=a.slice(),o=s-r){for(o<0?(i=a,o=-o,h=c.length):(i=c,r=s,h=a.length),o>(h=(s=Math.ceil(l/7))>h?s+1:h+1)&&(o=h,i.length=1),i.reverse();o--;)i.push(0);i.reverse()}for((h=a.length)-(o=c.length)<0&&(o=h,i=c,c=a,a=i),e=0;o;)e=(a[--o]=a[o]+c[o]+e)/1e7|0,a[o]%=1e7;for(e&&(a.unshift(e),++r),h=a.length;0==a[--h];)a.pop();return n.d=a,n.e=r,u?k(n,l):n}function g(t,n,e){if(t!==~~t||t<n||t>e)throw Error(a+t)}function _(t){var n,e,i,r=t.length-1,o="",s=t[0];if(r>0){for(o+=s,n=1;n<r;n++)(e=7-(i=t[n]+"").length)&&(o+=b(e)),o+=i;(e=7-(i=(s=t[n])+"").length)&&(o+=b(e))}else if(0===s)return"0";for(;s%10==0;)s/=10;return o+s}d.absoluteValue=d.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},d.comparedTo=d.cmp=function(t){var n,e,i,r;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(n=0,e=(i=this.d.length)<(r=t.d.length)?i:r;n<e;++n)if(this.d[n]!==t.d[n])return this.d[n]>t.d[n]^this.s<0?1:-1;return i===r?0:i>r^this.s<0?1:-1},d.decimalPlaces=d.dp=function(){var t=this.d.length-1,n=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)n--;return n<0?0:n},d.dividedBy=d.div=function(t){return y(this,new this.constructor(t))},d.dividedToIntegerBy=d.idiv=function(t){var n=this.constructor;return k(y(this,new n(t),0,1),n.precision)},d.equals=d.eq=function(t){return!this.cmp(t)},d.exponent=function(){return x(this)},d.greaterThan=d.gt=function(t){return this.cmp(t)>0},d.greaterThanOrEqualTo=d.gte=function(t){return this.cmp(t)>=0},d.isInteger=d.isint=function(){return this.e>this.d.length-2},d.isNegative=d.isneg=function(){return this.s<0},d.isPositive=d.ispos=function(){return this.s>0},d.isZero=function(){return 0===this.s},d.lessThan=d.lt=function(t){return 0>this.cmp(t)},d.lessThanOrEqualTo=d.lte=function(t){return 1>this.cmp(t)},d.logarithm=d.log=function(t){var n,e=this.constructor,i=e.precision,r=i+5;if(void 0===t)t=new e(10);else if((t=new e(t)).s<1||t.eq(o))throw Error(h+"NaN");if(this.s<1)throw Error(h+(this.s?"NaN":"-Infinity"));return this.eq(o)?new e(0):(u=!1,n=y(N(this,r),N(t,r),r),u=!0,k(n,i))},d.minus=d.sub=function(t){return t=new this.constructor(t),this.s==t.s?T(this,t):m(this,(t.s=-t.s,t))},d.modulo=d.mod=function(t){var n,e=this.constructor,i=e.precision;if(!(t=new e(t)).s)throw Error(h+"NaN");return this.s?(u=!1,n=y(this,t,0,1).times(t),u=!0,this.minus(n)):k(new e(this),i)},d.naturalExponential=d.exp=function(){return w(this)},d.naturalLogarithm=d.ln=function(){return N(this)},d.negated=d.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},d.plus=d.add=function(t){return t=new this.constructor(t),this.s==t.s?m(this,t):T(this,(t.s=-t.s,t))},d.precision=d.sd=function(t){var n,e,i;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(a+t);if(n=x(this)+1,e=7*(i=this.d.length-1)+1,i=this.d[i]){for(;i%10==0;i/=10)e--;for(i=this.d[0];i>=10;i/=10)e++}return t&&n>e?n:e},d.squareRoot=d.sqrt=function(){var t,n,e,i,r,o,s,a=this.constructor;if(this.s<1){if(!this.s)return new a(0);throw Error(h+"NaN")}for(t=x(this),u=!1,0==(r=Math.sqrt(+this))||r==1/0?(((n=_(this.d)).length+t)%2==0&&(n+="0"),r=Math.sqrt(n),t=f((t+1)/2)-(t<0||t%2),i=new a(n=r==1/0?"5e"+t:(n=r.toExponential()).slice(0,n.indexOf("e")+1)+t)):i=new a(r.toString()),r=s=(e=a.precision)+3;;)if(i=(o=i).plus(y(this,o,s+2)).times(.5),_(o.d).slice(0,s)===(n=_(i.d)).slice(0,s)){if(n=n.slice(s-3,s+1),r==s&&"4999"==n){if(k(o,e+1,0),o.times(o).eq(this)){i=o;break}}else if("9999"!=n)break;s+=4}return u=!0,k(i,e)},d.times=d.mul=function(t){var n,e,i,r,o,s,h,a,c,f=this.constructor,l=this.d,p=(t=new f(t)).d;if(!this.s||!t.s)return new f(0);for(t.s*=this.s,e=this.e+t.e,(a=l.length)<(c=p.length)&&(o=l,l=p,p=o,s=a,a=c,c=s),o=[],i=s=a+c;i--;)o.push(0);for(i=c;--i>=0;){for(n=0,r=a+i;r>i;)h=o[r]+p[i]*l[r-i-1]+n,o[r--]=h%1e7|0,n=h/1e7|0;o[r]=(o[r]+n)%1e7|0}for(;!o[--s];)o.pop();return n?++e:o.shift(),t.d=o,t.e=e,u?k(t,f.precision):t},d.toDecimalPlaces=d.todp=function(t,n){var e=this,i=e.constructor;return(e=new i(e),void 0===t)?e:(g(t,0,1e9),void 0===n?n=i.rounding:g(n,0,8),k(e,t+x(e)+1,n))},d.toExponential=function(t,n){var e,i=this,r=i.constructor;return void 0===t?e=M(i,!0):(g(t,0,1e9),void 0===n?n=r.rounding:g(n,0,8),e=M(i=k(new r(i),t+1,n),!0,t+1)),e},d.toFixed=function(t,n){var e,i,r=this.constructor;return void 0===t?M(this):(g(t,0,1e9),void 0===n?n=r.rounding:g(n,0,8),e=M((i=k(new r(this),t+x(this)+1,n)).abs(),!1,t+x(i)+1),this.isneg()&&!this.isZero()?"-"+e:e)},d.toInteger=d.toint=function(){var t=this.constructor;return k(new t(this),x(this)+1,t.rounding)},d.toNumber=function(){return+this},d.toPower=d.pow=function(t){var n,e,i,r,s,a,c=this,l=c.constructor,p=+(t=new l(t));if(!t.s)return new l(o);if(!(c=new l(c)).s){if(t.s<1)throw Error(h+"Infinity");return c}if(c.eq(o))return c;if(i=l.precision,t.eq(o))return k(c,i);if(a=(n=t.e)>=(e=t.d.length-1),s=c.s,a){if((e=p<0?-p:p)<=0x1fffffffffffff){for(r=new l(o),n=Math.ceil(i/7+4),u=!1;e%2&&A((r=r.times(c)).d,n),0!==(e=f(e/2));)A((c=c.times(c)).d,n);return u=!0,t.s<0?new l(o).div(r):k(r,i)}}else if(s<0)throw Error(h+"NaN");return s=s<0&&1&t.d[Math.max(n,e)]?-1:1,c.s=1,u=!1,r=t.times(N(c,i+12)),u=!0,(r=w(r)).s=s,r},d.toPrecision=function(t,n){var e,i,r=this,o=r.constructor;return void 0===t?(e=x(r),i=M(r,e<=o.toExpNeg||e>=o.toExpPos)):(g(t,1,1e9),void 0===n?n=o.rounding:g(n,0,8),e=x(r=k(new o(r),t,n)),i=M(r,t<=e||e<=o.toExpNeg,t)),i},d.toSignificantDigits=d.tosd=function(t,n){var e=this.constructor;return void 0===t?(t=e.precision,n=e.rounding):(g(t,1,1e9),void 0===n?n=e.rounding:g(n,0,8)),k(new e(this),t,n)},d.toString=d.valueOf=d.val=d.toJSON=function(){var t=x(this),n=this.constructor;return M(this,t<=n.toExpNeg||t>=n.toExpPos)};var y=function(){function t(t,n){var e,i=0,r=t.length;for(t=t.slice();r--;)e=t[r]*n+i,t[r]=e%1e7|0,i=e/1e7|0;return i&&t.unshift(i),t}function n(t,n,e,i){var r,o;if(e!=i)o=e>i?1:-1;else for(r=o=0;r<e;r++)if(t[r]!=n[r]){o=t[r]>n[r]?1:-1;break}return o}function e(t,n,e){for(var i=0;e--;)t[e]-=i,i=+(t[e]<n[e]),t[e]=1e7*i+t[e]-n[e];for(;!t[0]&&t.length>1;)t.shift()}return function(i,r,o,s){var u,a,c,f,l,p,v,d,m,g,_,y,w,E,b,N,z,T,M=i.constructor,A=i.s==r.s?1:-1,O=i.d,D=r.d;if(!i.s)return new M(i);if(!r.s)throw Error(h+"Division by zero");for(c=0,a=i.e-r.e,z=D.length,b=O.length,d=(v=new M(A)).d=[];D[c]==(O[c]||0);)++c;if(D[c]>(O[c]||0)&&--a,(y=null==o?o=M.precision:s?o+(x(i)-x(r))+1:o)<0)return new M(0);if(y=y/7+2|0,c=0,1==z)for(f=0,D=D[0],y++;(c<b||f)&&y--;c++)w=1e7*f+(O[c]||0),d[c]=w/D|0,f=w%D|0;else{for((f=1e7/(D[0]+1)|0)>1&&(D=t(D,f),O=t(O,f),z=D.length,b=O.length),E=z,g=(m=O.slice(0,z)).length;g<z;)m[g++]=0;(T=D.slice()).unshift(0),N=D[0],D[1]>=1e7/2&&++N;do f=0,(u=n(D,m,z,g))<0?(_=m[0],z!=g&&(_=1e7*_+(m[1]||0)),(f=_/N|0)>1?(f>=1e7&&(f=1e7-1),p=(l=t(D,f)).length,g=m.length,1==(u=n(l,m,p,g))&&(f--,e(l,z<p?T:D,p))):(0==f&&(u=f=1),l=D.slice()),(p=l.length)<g&&l.unshift(0),e(m,l,g),-1==u&&(g=m.length,(u=n(D,m,z,g))<1&&(f++,e(m,z<g?T:D,g))),g=m.length):0===u&&(f++,m=[0]),d[c++]=f,u&&m[0]?m[g++]=O[E]||0:(m=[O[E]],g=1);while((E++<b||void 0!==m[0])&&y--)}return d[0]||d.shift(),v.e=a,k(v,s?o+x(v)+1:o)}}();function w(t,n){var e,i,r,s,h,a=0,f=0,p=t.constructor,v=p.precision;if(x(t)>16)throw Error(c+x(t));if(!t.s)return new p(o);for(null==n?(u=!1,h=v):h=n,s=new p(.03125);t.abs().gte(.1);)t=t.times(s),f+=5;for(h+=Math.log(l(2,f))/Math.LN10*2+5|0,e=i=r=new p(o),p.precision=h;;){if(i=k(i.times(t),h),e=e.times(++a),_((s=r.plus(y(i,e,h))).d).slice(0,h)===_(r.d).slice(0,h)){for(;f--;)r=k(r.times(r),h);return p.precision=v,null==n?(u=!0,k(r,v)):r}r=s}}function x(t){for(var n=7*t.e,e=t.d[0];e>=10;e/=10)n++;return n}function E(t,n,e){if(n>t.LN10.sd())throw u=!0,e&&(t.precision=e),Error(h+"LN10 precision limit exceeded");return k(new t(t.LN10),n)}function b(t){for(var n="";t--;)n+="0";return n}function N(t,n){var e,i,r,s,a,c,f,l,p,v=1,d=t,m=d.d,g=d.constructor,w=g.precision;if(d.s<1)throw Error(h+(d.s?"NaN":"-Infinity"));if(d.eq(o))return new g(0);if(null==n?(u=!1,l=w):l=n,d.eq(10))return null==n&&(u=!0),E(g,l);if(g.precision=l+=10,i=(e=_(m)).charAt(0),!(15e14>Math.abs(s=x(d))))return f=E(g,l+2,w).times(s+""),d=N(new g(i+"."+e.slice(1)),l-10).plus(f),g.precision=w,null==n?(u=!0,k(d,w)):d;for(;i<7&&1!=i||1==i&&e.charAt(1)>3;)i=(e=_((d=d.times(t)).d)).charAt(0),v++;for(s=x(d),i>1?(d=new g("0."+e),s++):d=new g(i+"."+e.slice(1)),c=a=d=y(d.minus(o),d.plus(o),l),p=k(d.times(d),l),r=3;;){if(a=k(a.times(p),l),_((f=c.plus(y(a,new g(r),l))).d).slice(0,l)===_(c.d).slice(0,l))return c=c.times(2),0!==s&&(c=c.plus(E(g,l+2,w).times(s+""))),c=y(c,new g(v),l),g.precision=w,null==n?(u=!0,k(c,w)):c;c=f,r+=2}}function z(t,n){var e,i,r;for((e=n.indexOf("."))>-1&&(n=n.replace(".","")),(i=n.search(/e/i))>0?(e<0&&(e=i),e+=+n.slice(i+1),n=n.substring(0,i)):e<0&&(e=n.length),i=0;48===n.charCodeAt(i);)++i;for(r=n.length;48===n.charCodeAt(r-1);)--r;if(n=n.slice(i,r)){if(r-=i,t.e=f((e=e-i-1)/7),t.d=[],i=(e+1)%7,e<0&&(i+=7),i<r){for(i&&t.d.push(+n.slice(0,i)),r-=7;i<r;)t.d.push(+n.slice(i,i+=7));i=7-(n=n.slice(i)).length}else i-=r;for(;i--;)n+="0";if(t.d.push(+n),u&&(t.e>v||t.e<-v))throw Error(c+e)}else t.s=0,t.e=0,t.d=[0];return t}function k(t,n,e){var i,r,o,s,h,a,p,d,m=t.d;for(s=1,o=m[0];o>=10;o/=10)s++;if((i=n-s)<0)i+=7,r=n,p=m[d=0];else{if((d=Math.ceil((i+1)/7))>=(o=m.length))return t;for(s=1,p=o=m[d];o>=10;o/=10)s++;i%=7,r=i-7+s}if(void 0!==e&&(h=p/(o=l(10,s-r-1))%10|0,a=n<0||void 0!==m[d+1]||p%o,a=e<4?(h||a)&&(0==e||e==(t.s<0?3:2)):h>5||5==h&&(4==e||a||6==e&&(i>0?r>0?p/l(10,s-r):0:m[d-1])%10&1||e==(t.s<0?8:7))),n<1||!m[0])return a?(o=x(t),m.length=1,n=n-o-1,m[0]=l(10,(7-n%7)%7),t.e=f(-n/7)||0):(m.length=1,m[0]=t.e=t.s=0),t;if(0==i?(m.length=d,o=1,d--):(m.length=d+1,o=l(10,7-i),m[d]=r>0?(p/l(10,s-r)%l(10,r)|0)*o:0),a)for(;;)if(0==d){1e7==(m[0]+=o)&&(m[0]=1,++t.e);break}else{if(m[d]+=o,1e7!=m[d])break;m[d--]=0,o=1}for(i=m.length;0===m[--i];)m.pop();if(u&&(t.e>v||t.e<-v))throw Error(c+x(t));return t}function T(t,n){var e,i,r,o,s,h,a,c,f,l,p=t.constructor,v=p.precision;if(!t.s||!n.s)return n.s?n.s=-n.s:n=new p(t),u?k(n,v):n;if(a=t.d,l=n.d,i=n.e,c=t.e,a=a.slice(),s=c-i){for((f=s<0)?(e=a,s=-s,h=l.length):(e=l,i=c,h=a.length),s>(r=Math.max(Math.ceil(v/7),h)+2)&&(s=r,e.length=1),e.reverse(),r=s;r--;)e.push(0);e.reverse()}else{for((f=(r=a.length)<(h=l.length))&&(h=r),r=0;r<h;r++)if(a[r]!=l[r]){f=a[r]<l[r];break}s=0}for(f&&(e=a,a=l,l=e,n.s=-n.s),h=a.length,r=l.length-h;r>0;--r)a[h++]=0;for(r=l.length;r>s;){if(a[--r]<l[r]){for(o=r;o&&0===a[--o];)a[o]=1e7-1;--a[o],a[r]+=1e7}a[r]-=l[r]}for(;0===a[--h];)a.pop();for(;0===a[0];a.shift())--i;return a[0]?(n.d=a,n.e=i,u?k(n,v):n):new p(0)}function M(t,n,e){var i,r=x(t),o=_(t.d),s=o.length;return n?(e&&(i=e-s)>0?o=o.charAt(0)+"."+o.slice(1)+b(i):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(r<0?"e":"e+")+r):r<0?(o="0."+b(-r-1)+o,e&&(i=e-s)>0&&(o+=b(i))):r>=s?(o+=b(r+1-s),e&&(i=e-r-1)>0&&(o=o+"."+b(i))):((i=r+1)<s&&(o=o.slice(0,i)+"."+o.slice(i)),e&&(i=e-s)>0&&(r+1===s&&(o+="."),o+=b(i))),t.s<0?"-"+o:o}function A(t,n){if(t.length>n)return t.length=n,!0}function O(t){if(!t||"object"!=typeof t)throw Error(h+"Object expected");var n,e,i,r=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(n=0;n<r.length;n+=3)if(void 0!==(i=t[e=r[n]]))if(f(i)===i&&i>=r[n+1]&&i<=r[n+2])this[e]=i;else throw Error(a+e+": "+i);if(void 0!==(i=t[e="LN10"]))if(i==Math.LN10)this[e]=new this(i);else throw Error(a+e+": "+i);return this}(s=function t(n){var e,i,r;function o(t){if(!(this instanceof o))return new o(t);if(this.constructor=o,t instanceof o){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(a+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return z(this,t.toString())}if("string"!=typeof t)throw Error(a+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,p.test(t))z(this,t);else throw Error(a+t)}if(o.prototype=d,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=t,o.config=o.set=O,void 0===n&&(n={}),n)for(e=0,r=["precision","rounding","toExpNeg","toExpPos","LN10"];e<r.length;)n.hasOwnProperty(i=r[e++])||(n[i]=this[i]);return o.config(n),o}(s)).default=s.Decimal=s,o=new s(1),void 0===(i=(function(){return s}).call(n,e,n,t))||(t.exports=i)}(0)}}]);