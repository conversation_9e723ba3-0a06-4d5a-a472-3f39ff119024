// @ts-nocheck
import { VStack, Heading, Text, Textarea, Button, useToast, Box, Switch, HStack, SimpleGrid, useDisclosure, Modal, ModalOverlay, ModalContent, ModalHeader, ModalCloseButton, ModalBody, ModalFooter, Tabs, TabList, TabPanels, Tab, TabPanel, Progress, Icon, Badge, Input, Radio, RadioGroup, Stack, Select, Container, FormControl, FormLabel, Alert, AlertIcon, Card, CardBody, Flex, Spinner } from '@chakra-ui/react';
import Layout from '../components/Layout';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './api/auth/[...nextauth]';
import { useEffect, useState, useMemo, useCallback, Suspense } from 'react';
import { useSession } from 'next-auth/react';
import { FiInfo } from 'react-icons/fi';
import { FaQuoteLeft, FaQuoteRight, FaClipboardList, FaUserClock, FaCheckCircle, FaTimesCircle, FaRobot } from 'react-icons/fa';
import { NextLink } from 'next/link';
import { useRouter } from 'next/router';
import { ApplicationType, ApplicationSubmission, ApplicationsState } from '../types/applications';
import { DEFAULT_APPLICATION_TYPES } from '../config/applicationTypes';
import { debug } from '../utils/debug';
import dynamic from 'next/dynamic';

// Dynamic imports for heavy components
const ApplicationCard = dynamic(() => import('../components/ApplicationCard').then(mod => ({ default: mod.ApplicationCard })), {
  loading: () => <Spinner size="lg" />,
  ssr: false
});

const ScenarioForm = dynamic(() => import('../components/ScenarioForm'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

const ApplicationForm = dynamic(() => import('../components/ApplicationForm'), {
  loading: () => <Spinner size="md" />,
  ssr: false
});

// List of timezones with common names
const TIMEZONES = [
  { value: 'GMT-12:00', label: '(GMT-12:00) International Date Line West' },
  { value: 'GMT-11:00', label: '(GMT-11:00) Midway Island, Samoa' },
  { value: 'GMT-10:00', label: '(GMT-10:00) Hawaii' },
  { value: 'GMT-09:00', label: '(GMT-09:00) Alaska' },
  { value: 'GMT-08:00', label: '(GMT-08:00) Pacific Time (US & Canada)' },
  { value: 'GMT-07:00', label: '(GMT-07:00) Mountain Time (US & Canada)' },
  { value: 'GMT-06:00', label: '(GMT-06:00) Central Time (US & Canada)' },
  { value: 'GMT-05:00', label: '(GMT-05:00) Eastern Time (US & Canada)' },
  { value: 'GMT-04:00', label: '(GMT-04:00) Atlantic Time (Canada)' },
  { value: 'GMT-03:00', label: '(GMT-03:00) Buenos Aires, Georgetown' },
  { value: 'GMT-02:00', label: '(GMT-02:00) Mid-Atlantic' },
  { value: 'GMT-01:00', label: '(GMT-01:00) Azores, Cape Verde Islands' },
  { value: 'GMT+00:00', label: '(GMT+00:00) London, Dublin, Edinburgh' },
  { value: 'GMT+01:00', label: '(GMT+01:00) Paris, Amsterdam, Berlin' },
  { value: 'GMT+02:00', label: '(GMT+02:00) Athens, Istanbul, Helsinki' },
  { value: 'GMT+03:00', label: '(GMT+03:00) Moscow, Baghdad, Kuwait' },
  { value: 'GMT+04:00', label: '(GMT+04:00) Abu Dhabi, Dubai, Baku' },
  { value: 'GMT+05:00', label: '(GMT+05:00) Karachi, Tashkent' },
  { value: 'GMT+06:00', label: '(GMT+06:00) Dhaka, Almaty' },
  { value: 'GMT+07:00', label: '(GMT+07:00) Bangkok, Jakarta' },
  { value: 'GMT+08:00', label: '(GMT+08:00) Beijing, Singapore, Hong Kong' },
  { value: 'GMT+09:00', label: '(GMT+09:00) Tokyo, Seoul, Osaka' },
  { value: 'GMT+10:00', label: '(GMT+10:00) Sydney, Melbourne, Brisbane' },
  { value: 'GMT+11:00', label: '(GMT+11:00) Solomon Islands' },
  { value: 'GMT+12:00', label: '(GMT+12:00) Auckland, Wellington' }
];

interface ApplicationConfig {
  isOpen: boolean;
  questions: string[];
  quiz: {
    question: string;
    options: string[];
    correctAnswer: number;
  }[];
  open: boolean;
}

const QUOTES = [
  "Join our team and make a difference!",
  "Great moderators are the backbone of great communities.",
  "Help us keep the community safe and friendly.",
  "Be the change you want to see in the community.",
  "Together we can build something amazing!"
];

interface FormState {
  age: string;
  hoursPerWeek: string;
  timezone: string;
  motivation: string;
  scenarioResponses: Record<number, string>;
}

interface ApplicationsPageProps {
  ownerIds: string[];
}

export default function Applications({ ownerIds }: ApplicationsPageProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const toast = useToast();
  const isAdmin = (session?.user as any)?.isAdmin;

  const [formState, setFormState] = useState<FormState>({
    age: '',
    hoursPerWeek: '',
    timezone: '',
    motivation: '',
    scenarioResponses: {}
  });

  const [quiz, setQuiz] = useState<{
    question: string;
    options: string[];
    correctAnswer: number;
  }[]>([]);

  const [submissions, setSubmissions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(true);
  const [showSubmissions, setShowSubmissions] = useState(false);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const [currentTab, setCurrentTab] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isApplicationsOpen, setIsApplicationsOpen] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [config, setConfig] = useState<ApplicationConfig | null>(null);

  const [state, setState] = useState<ApplicationsState>({
    types: DEFAULT_APPLICATION_TYPES,
    userSubmissions: [],
    loading: true,
    error: null
  });

  // Experimental features are handled through a separate system

  // Application modal state
  const [selectedApplication, setSelectedApplication] = useState<ApplicationType | null>(null);

  // Owner check (bot owners defined in config.yml dashboard.admins array)
  const isOwner = ownerIds.includes((session?.user as any)?.id || '');

  const handleApply = (app: ApplicationType) => {
    setSelectedApplication(app);
    setCurrentTab(0);
    onOpen();
  };

  // Check if current user has already applied for the given application type
  const hasApplied = (typeId: string) => {
    return state.userSubmissions.some(sub => sub.applicationTypeId === typeId);
  };

  const isPersonalInfoComplete = useMemo(() => {
    return (
      formState.age.trim() !== '' &&
      formState.hoursPerWeek.trim() !== '' &&
      formState.timezone.trim() !== '' &&
      formState.motivation.trim() !== ''
    );
  }, [formState]);

  const TOTAL_SCENARIOS = 15;
  const isScenarioComplete = useMemo(() => {
    return Object.keys(formState.scenarioResponses || {}).length >= TOTAL_SCENARIOS;
  }, [formState.scenarioResponses]);

  // Get quote of the day
  const getQuoteOfTheDay = () => {
    const today = new Date();
    return QUOTES[today.getDate() % QUOTES.length];
  };

  const quoteOfTheDay = getQuoteOfTheDay();

  // Try to get user's timezone on mount
  useEffect(() => {
    try {
      const offset = new Date().getTimezoneOffset();
      const hours = Math.abs(Math.floor(offset / 60));
      const minutes = Math.abs(offset % 60);
      const sign = offset > 0 ? '-' : '+';
      const gmtOffset = `GMT${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      setFormState(prev => ({ ...prev, timezone: gmtOffset }));
    } catch (e) {
      setFormState(prev => ({ ...prev, timezone: 'GMT+00:00' }));
    }
  }, []);

  // fetch config and submissions
  useEffect(() => {
    // Fetch application configuration
    fetch('/api/applications/config')
      .then(res => res.json())
      .then((cfg: ApplicationConfig) => {
        debug.log('Applications', 'Received config', cfg);
        if (!cfg) {
          throw new Error('No config received');
        }
        
        setConfig(cfg);
        
        // Ensure quiz data exists and is an array
        const quizData = Array.isArray(cfg.quiz) ? cfg.quiz : [];
        debug.log('Applications', 'Setting quiz data', quizData);
        setQuiz(quizData);
        
        // Initialize answer arrays with proper length
        setFormState(prev => ({
          ...prev,
          scenarioResponses: {}
        }));
        
        setOpen(cfg.open);
      })
      .catch(error => {
        console.error('Error fetching application config:', error);
        toast({
          title: 'Error',
          description: 'Failed to load application configuration. Please try again.',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      });

    if (isAdmin) {
      fetch('/api/applications/moderation')
        .then(r => r.json())
        .then(data => {
          // Ensure submissions is always an array
          setSubmissions(Array.isArray(data) ? data : []);
        })
        .catch(error => {
          debug.error('Applications', 'Failed to fetch submissions', error);
          setSubmissions([]); // Fallback to empty array
        });
    }

    // Check if applications are open
    const checkApplicationStatus = async () => {
      try {
        const res = await fetch('/api/applications/config');
        const data = await res.json();
        setIsApplicationsOpen(data.isOpen);
      } catch (error) {
        debug.error('Applications', 'Failed to check application status', error);
        toast({
          title: 'Error',
          description: 'Failed to check if applications are open',
          status: 'error',
          duration: 5000,
        });
      }
    };

    checkApplicationStatus();
  }, [isAdmin]);

  // Experimental features are handled through a separate system
  // No need to inject experimental application type here

  // Add debug logging for quiz state changes
  useEffect(() => {
    debug.log('Applications', 'Quiz state updated', quiz);
  }, [quiz]);

  const handleFormChange = useCallback((data: Partial<FormState>) => {
    setFormState(prev => ({
      ...prev,
      ...data
    }));
  }, []);

  const handlePersonalInfoChange = useCallback((data: Partial<FormState>) => {
    setFormState(prev => ({
      ...prev,
      ...data
    }));
  }, []);

  const handleScenarioResponsesChange = useCallback((responses: Record<number, string>) => {
    setFormState(prev => ({
      ...prev,
      scenarioResponses: responses
    }));
  }, []);

  const handleSubmit = async () => {
    if (!session?.user?.id) {
      toast({
        title: 'Error',
        description: 'You must be logged in to apply.',
        status: 'error',
        duration: 5000,
      });
      return;
    }

    // Validate all required fields
    if (!formState.age || !formState.hoursPerWeek || !formState.timezone || !formState.motivation) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in all required fields before submitting.',
        status: 'error',
        duration: 5000,
      });
      return;
    }

    setIsLoading(true);
    try {
      const res = await fetch('/api/applications/moderation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: session.user.id,
          age: parseInt(formState.age),
          hoursPerWeek: parseInt(formState.hoursPerWeek),
          timezone: formState.timezone,
          motivation: formState.motivation,
          scenarioResponses: formState.scenarioResponses
        }),
      });

      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || 'Failed to submit application');
      }

      toast({
        title: 'Application submitted',
        description: 'Your application has been submitted successfully.',
        status: 'success',
        duration: 5000,
      });

      router.push('/');
    } catch (error) {
      console.error('Error submitting application:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to submit application',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = async (applicationId: string, newStatus: 'approved' | 'rejected') => {
    try {
      const res = await fetch('/api/applications/moderation', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          applicationId,
          status: newStatus
        }),
      });

      if (!res.ok) {
        throw new Error('Failed to update application status');
      }

      // Refresh submissions list
      const updatedRes = await fetch('/api/applications/moderation');
      const updatedSubmissions = await updatedRes.json();
      setSubmissions(updatedSubmissions);

      toast({ 
        title: 'Status updated', 
        description: `Application has been ${newStatus}`,
        status: 'success',
        duration: 3000
      });
    } catch (error) {
      toast({ 
        title: 'Failed to update status', 
        description: error.message,
        status: 'error',
        duration: 5000
      });
    }
  };

  const startApplication = () => {
    if (!session?.user?.id) {
      toast({
        title: 'Error',
        description: 'You must be logged in to apply.',
        status: 'error',
        duration: 5000,
      });
      return;
    }

    // Reset all form fields
    setFormState(prev => ({
      ...prev,
      age: '',
      hoursPerWeek: '',
      motivation: '',
      scenarioResponses: {}
    }));
    
    // Ensure quiz data is properly initialized
    if (config?.quiz && Array.isArray(config.quiz)) {
      if (process.env.NODE_ENV !== 'production') console.log('Initializing quiz data in startApplication:', config.quiz);
      setQuiz(config.quiz);
    } else {
      console.error('Invalid quiz data in config:', config?.quiz);
      toast({
        title: 'Error',
        description: 'Failed to load quiz questions. Please try again later.',
        status: 'error',
        duration: 5000,
      });
      return;
    }

    setCurrentTab(0);
    onOpen();
  };

  // Prevent navigating forward if requirements not met
  const handleTabChange = (index: number) => {
    if (index === 1 && !isPersonalInfoComplete) return; // Can't go to scenarios unless personal done
    if (index === 2 && !isScenarioComplete) return; // Can't go to additional unless scenarios done
    setCurrentTab(index);
  };

  // Update the quiz state to track score
  const [quizState, setQuizState] = useState<{
    score: number;
    total: number;
    completed: boolean;
  }>({
    score: 0,
    total: 0,
    completed: false
  });

  if (status === 'loading' || !config) {
    return (
      <Layout>
        <Box w="full" p={4}>
          <Container maxW="4xl" centerContent>
            <Progress size="xs" isIndeterminate w="full" colorScheme="blue" />
          </Container>
        </Box>
      </Layout>
    );
  }

  if (!session) {
    return (
      <Layout>
        <Container maxW="container.md" py={8}>
          <Alert status="warning">
            <AlertIcon />
            Please sign in to submit an application.
          </Alert>
        </Container>
      </Layout>
    );
  }

  if (!config.open) {
    return (
      <Layout>
        <Box w="full" p={4}>
          {/* Header Card */}
          <Box
            maxW="4xl"
            mx="auto"
            mb={8}
            mt={8}
            bg="rgba(255,255,255,0.08)"
            p={8}
            rounded="2xl"
            backdropFilter="blur(10px)"
            border="2px solid"
            borderColor="red.400"
            boxShadow="0 0 15px rgba(255, 0, 0, 0.2)"
            textAlign="center"
          >
            <Heading
              size="2xl"
              bgGradient="linear(to-r, red.300, orange.400)"
              bgClip="text"
              mb={4}
            >
              Applications Closed
            </Heading>
            <Text color="gray.300" fontSize="lg">
              We are not accepting new applications at this time. Please check back later!
            </Text>
          </Box>
        </Box>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxW="container.xl" py={8}>
        {/* Header Card */}
        <Box
          maxW="4xl"
          mx="auto"
          mb={8}
          mt={8}
          bg="rgba(255,255,255,0.08)"
          p={8}
          rounded="2xl"
          backdropFilter="blur(10px)"
          border="2px solid"
          borderColor="purple.400"
          boxShadow="0 0 15px rgba(159, 122, 234, 0.4)"
          textAlign="center"
        >
          <Heading
            size="2xl"
            bgGradient="linear(to-r, purple.300, blue.400)"
            bgClip="text"
            mb={4}
          >
            Join Our Team
          </Heading>
          <Text color="gray.300" fontSize="lg" mb={6}>
            Explore available positions and become part of our community staff
          </Text>

          {/* Application Quote */}
          <Box position="relative" bg="gray.900" p={6} rounded="lg" border="1px" borderColor="whiteAlpha.200">
            <Icon as={FaQuoteLeft} color="purple.300" boxSize={6} position="absolute" top={-3} left={-3} />
            <Icon as={FaQuoteRight} color="purple.300" boxSize={6} position="absolute" bottom={-3} right={-3} />
            <HStack spacing={3} justify="center">
              <Icon as={FaRobot} color="purple.300" boxSize={6} />
              <Text fontStyle="italic" color="purple.200" fontSize="md">
                {quoteOfTheDay}
              </Text>
            </HStack>
          </Box>
        </Box>

        {/* Admin Section */}
        {isAdmin && (
          <Box maxW="4xl" mx="auto" mb={8}>
            <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
              <Card bg="rgba(255,255,255,0.08)" backdropFilter="blur(10px)" border="1px solid" borderColor="whiteAlpha.200">
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <HStack justify="space-between">
                      <Heading size="md">Pending</Heading>
                      <Badge colorScheme="yellow" fontSize="md">
                        {Array.isArray(submissions) ? submissions.filter(s => s.status === 'pending').length : 0}
                      </Badge>
                    </HStack>
                    <Icon as={FaUserClock} boxSize={8} color="yellow.400" />
                  </VStack>
                </CardBody>
              </Card>

              <Card bg="rgba(255,255,255,0.08)" backdropFilter="blur(10px)" border="1px solid" borderColor="whiteAlpha.200">
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <HStack justify="space-between">
                      <Heading size="md">Approved</Heading>
                      <Badge colorScheme="green" fontSize="md">
                        {Array.isArray(submissions) ? submissions.filter(s => s.status === 'approved').length : 0}
                      </Badge>
                    </HStack>
                    <Icon as={FaCheckCircle} boxSize={8} color="green.400" />
                  </VStack>
                </CardBody>
              </Card>

              <Card bg="rgba(255,255,255,0.08)" backdropFilter="blur(10px)" border="1px solid" borderColor="whiteAlpha.200">
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <HStack justify="space-between">
                      <Heading size="md">Rejected</Heading>
                      <Badge colorScheme="red" fontSize="md">
                        {Array.isArray(submissions) ? submissions.filter(s => s.status === 'rejected').length : 0}
                      </Badge>
                    </HStack>
                    <Icon as={FaTimesCircle} boxSize={8} color="red.400" />
                  </VStack>
                </CardBody>
              </Card>
            </SimpleGrid>

            <Button
              mt={6}
              size="lg"
              w="full"
              colorScheme={showSubmissions ? "red" : "blue"}
              onClick={() => setShowSubmissions(!showSubmissions)}
              leftIcon={<Icon as={FaClipboardList} />}
            >
              {showSubmissions ? "Hide Submissions" : "View All Submissions"}
            </Button>
          </Box>
        )}

        {/* Available Application Types */}
        {!isAdmin && (
          <Box maxW="6xl" mx="auto" mb={12}>
            <Heading size="lg" mb={6} textAlign="center" color="white">
              Available Applications
            </Heading>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {state.types.filter(t=>{
                if(t.id==='support') return false;
                if(t.id==='developer' && !isOwner) return false;
                return true;
              }).map((applicationType) => (
                <Suspense key={applicationType.id} fallback={<Spinner />}>
                  <ApplicationCard
                    key={applicationType.id}
                    application={applicationType}
                    onApply={handleApply}
                    hasApplied={hasApplied(applicationType.id)}
                  />
                </Suspense>
              ))}
            </SimpleGrid>
          </Box>
        )}

        {/* Dynamic Application Modal */}
        {selectedApplication && (
        <Modal 
          isOpen={isOpen} 
          onClose={() => { onClose(); setSelectedApplication(null);} } 
          size="6xl"
          scrollBehavior="inside"
          isCentered
        >
          <ModalOverlay 
            bg="blackAlpha.700"
            backdropFilter="blur(10px)"
          />
          <ModalContent 
            bg="gray.800" 
            minH="80vh"
            maxH="90vh"
            borderRadius="xl"
            border="1px solid"
            borderColor="whiteAlpha.200"
            boxShadow="2xl"
          >
            <ModalHeader 
              borderBottom="1px solid" 
              borderColor="whiteAlpha.200"
              py={6}
              px={8}
            >
              <HStack justify="space-between" align="center">
                <VStack align="start" spacing={2}>
                  <Heading size="lg">{selectedApplication.title}</Heading>
                  <Text color="gray.400" fontSize="md">
                    {selectedApplication.description}
                  </Text>
                </VStack>
                <HStack spacing={4}>
                  <Badge 
                    colorScheme={selectedApplication.color} 
                    fontSize="md" 
                    px={3} 
                    py={1}
                    borderRadius="full"
                  >
                    Step {currentTab + 1} of 3
                  </Badge>
                  <ModalCloseButton 
                    position="static"
                    size="lg"
                    _hover={{
                      bg: "whiteAlpha.200"
                    }}
                  />
                </HStack>
              </HStack>
            </ModalHeader>

            <ModalBody p={0}>
              <Box position="relative">
                {/* Progress bar */}
                <Progress
                  value={(currentTab + 1) * (100/(selectedApplication.id==='experimental-features'?2:3))}
                  size="xs"
                  colorScheme={selectedApplication.color}
                  bg="whiteAlpha.100"
                  sx={{
                    "& > div": {
                      transition: "all 0.3s ease-in-out"
                    }
                  }}
                />

                {/* Content */}
                <Box p={8}>
                  <Tabs 
                    index={currentTab} 
                    onChange={handleTabChange} 
                    variant="unstyled"
                    isLazy
                  >
                    <TabList mb={6}>
                      <HStack spacing={4} width="full">
                        <Tab
                          flex={1}
                          py={3}
                          _selected={{
                            color: "white",
                            bg: `${selectedApplication.color}.500`,
                            boxShadow: "lg"
                          }}
                          bg="whiteAlpha.50"
                          borderRadius="lg"
                          fontWeight="semibold"
                          transition="all 0.2s"
                          _hover={{
                            bg: currentTab === 0 ? `${selectedApplication.color}.500` : "whiteAlpha.100"
                          }}
                        >
                          <HStack>
                            <Icon as={FaClipboardList} />
                            <Text>Personal Information</Text>
                          </HStack>
                        </Tab>
                        <Tab
                          flex={1}
                          py={3}
                          isDisabled={!isPersonalInfoComplete}
                          _selected={{
                            color: "white",
                            bg: `${selectedApplication.color}.500`,
                            boxShadow: "lg"
                          }}
                          bg="whiteAlpha.50"
                          borderRadius="lg"
                          fontWeight="semibold"
                          transition="all 0.2s"
                          _hover={{
                            bg: currentTab === 1 ? `${selectedApplication.color}.500` : "whiteAlpha.100"
                          }}
                        >
                          <HStack>
                            <Icon as={FaUserClock} />
                            <Text>{selectedApplication.id==='moderator' ? 'Moderation Scenarios' : 'Application Questions'}</Text>
                          </HStack>
                        </Tab>
                        <Tab
                          flex={1}
                          py={3}
                          isDisabled={!isScenarioComplete}
                          _selected={{
                            color: "white",
                            bg: `${selectedApplication.color}.500`,
                            boxShadow: "lg"
                          }}
                          bg="whiteAlpha.50"
                          borderRadius="lg"
                          fontWeight="semibold"
                          transition="all 0.2s"
                          _hover={{
                            bg: currentTab === 2 ? `${selectedApplication.color}.500` : "whiteAlpha.100"
                          }}
                        >
                          <HStack>
                            <Icon as={FiInfo} />
                            <Text>Additional Information</Text>
                          </HStack>
                        </Tab>
                      </HStack>
                    </TabList>

                    <TabPanels>
                      <TabPanel p={0}>
                        <Suspense fallback={<Spinner />}>
                          <ApplicationForm
                            session={session}
                            onFormChange={handlePersonalInfoChange}
                            initialData={formState}
                          />
                        </Suspense>
                      </TabPanel>

                      <TabPanel p={0}>
                        {selectedApplication.id === 'moderator' ? (
                          <Suspense fallback={<Spinner />}>
                            <ScenarioForm
                              onFormChange={handleScenarioResponsesChange}
                              initialData={formState.scenarioResponses}
                            />
                          </Suspense>
                        ) : (
                          <VStack spacing={4} align="stretch">
                            {selectedApplication.questions?.map((q) => (
                              <FormControl key={q.id} isRequired={q.required}>
                                <FormLabel color="white">{q.text}</FormLabel>
                                {q.type === 'text' && (
                                  <Textarea
                                    placeholder="Your answer..."
                                    value={(formState as any)[q.id] || ''}
                                    onChange={(e)=>handlePersonalInfoChange(q.id, e.target.value)}
                                    bg="gray.700"
                                    border="1px solid"
                                    borderColor="whiteAlpha.300"
                                  />
                                )}
                              </FormControl>
                            ))}
                          </VStack>
                        )}
                      </TabPanel>

                      <TabPanel p={0}>
                        <Text>Additional information will be shown here.</Text>
                      </TabPanel>
                    </TabPanels>
                  </Tabs>
                </Box>
              </Box>
            </ModalBody>

            <ModalFooter
              borderTop="1px solid"
              borderColor="whiteAlpha.200"
              py={6}
              px={8}
            >
              <HStack spacing={4} width="full" justify="space-between">
                <HStack spacing={4}>
                  <Icon 
                    as={FaRobot} 
                    color={`${selectedApplication.color}.300`} 
                    boxSize={6} 
                  />
                  <Text color="gray.400" fontSize="sm">
                    Your responses will be reviewed by our moderation team
                  </Text>
                </HStack>

                <HStack spacing={4}>
                  {currentTab > 0 && (
                    <Button
                      onClick={() => setCurrentTab(currentTab - 1)}
                      leftIcon={<Icon as={FaClipboardList} />}
                      variant="ghost"
                      size="lg"
                      _hover={{
                        bg: "whiteAlpha.100"
                      }}
                    >
                      Previous
                    </Button>
                  )}

                  {currentTab === 0 && (
                    <Button
                      onClick={() => setCurrentTab(1)}
                      rightIcon={<Icon as={FaClipboardList} />}
                      colorScheme={`${selectedApplication.color}.500`}
                      size="lg"
                      isDisabled={!isPersonalInfoComplete}
                    >
                      Continue
                    </Button>
                  )}

                  {currentTab === 1 && (
                    <Button
                      onClick={() => setCurrentTab(2)}
                      rightIcon={<Icon as={FaClipboardList} />}
                      colorScheme={`${selectedApplication.color}.500`}
                      size="lg"
                      isDisabled={!isScenarioComplete}
                    >
                      Continue
                    </Button>
                  )}

                  {currentTab === 2 && (
                    <Button
                      onClick={handleSubmit}
                      leftIcon={<Icon as={FaCheckCircle} />}
                      colorScheme="green"
                      size="lg"
                      isLoading={isSubmitting}
                      loadingText="Submitting..."
                    >
                      Submit Application
                    </Button>
                  )}
                </HStack>
              </HStack>
            </ModalFooter>
          </ModalContent>
        </Modal>
        )}

        {/* Submissions Modal */}
        {showSubmissions && (
          <Modal isOpen={showSubmissions} onClose={() => setShowSubmissions(false)} size="4xl" scrollBehavior="inside">
            <ModalOverlay backdropFilter="blur(10px)" />
            <ModalContent bg="gray.800" border="1px solid" borderColor="whiteAlpha.300">
              <ModalHeader>
                <HStack justify="space-between" align="center">
                  <Heading size="lg">Application Submissions</Heading>
                  <HStack spacing={4}>
                    <Badge colorScheme="yellow" fontSize="md" p={2}>
                      {Array.isArray(submissions) ? submissions.filter(s => s.status === 'pending').length : 0} Pending
                    </Badge>
                    <Badge colorScheme="green" fontSize="md" p={2}>
                      {Array.isArray(submissions) ? submissions.filter(s => s.status === 'approved').length : 0} Approved
                    </Badge>
                    <Badge colorScheme="red" fontSize="md" p={2}>
                      {Array.isArray(submissions) ? submissions.filter(s => s.status === 'rejected').length : 0} Rejected
                    </Badge>
                  </HStack>
                </HStack>
              </ModalHeader>
              <ModalCloseButton />
              <ModalBody>
                <VStack spacing={4} align="stretch">
                  {Array.isArray(submissions) ? submissions.map((submission) => (
                    <Card
                      key={submission._id}
                      bg="rgba(255,255,255,0.08)"
                      backdropFilter="blur(10px)"
                      border="1px solid"
                      borderColor={
                        submission.status === 'approved'
                          ? 'green.400'
                          : submission.status === 'rejected'
                          ? 'red.400'
                          : 'yellow.400'
                      }
                      rounded="lg"
                      overflow="hidden"
                    >
                      <CardBody>
                        <VStack spacing={4} align="stretch">
                          <HStack justify="space-between">
                            <VStack align="start" spacing={1}>
                              <Heading size="md">{submission.username || 'Anonymous'}</Heading>
                              <Text fontSize="sm" color="gray.400">
                                Applied {new Date(submission.date).toLocaleDateString()}
                              </Text>
                            </VStack>
                            <Badge
                              colorScheme={
                                submission.status === 'approved'
                                  ? 'green'
                                  : submission.status === 'rejected'
                                  ? 'red'
                                  : 'yellow'
                              }
                              fontSize="md"
                              p={2}
                            >
                              {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
                            </Badge>
                          </HStack>

                          <Box bg="gray.900" p={4} rounded="md">
                            <Text fontWeight="bold" mb={2}>Why do you want to be a moderator?</Text>
                            <Text color="gray.300">{submission.motivation || 'No response'}</Text>
                          </Box>

                          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                            <Box>
                              <Text fontWeight="bold" mb={2}>Age</Text>
                              <Text color="gray.300">{submission.age} years old</Text>
                            </Box>
                            <Box>
                              <Text fontWeight="bold" mb={2}>Hours per Week</Text>
                              <Text color="gray.300">{submission.hoursPerWeek} hours</Text>
                            </Box>
                            <Box>
                              <Text fontWeight="bold" mb={2}>Timezone</Text>
                              <Text color="gray.300">{submission.timezone}</Text>
                            </Box>
                          </SimpleGrid>

                          {submission.scenarioResponses && (
                            <Box bg="gray.900" p={4} rounded="md">
                              <Text fontWeight="bold" mb={2}>Scenario Responses</Text>
                              <Text color="gray.300">{JSON.stringify(submission.scenarioResponses)}</Text>
                            </Box>
                          )}

                          {submission.status === 'pending' && (
                            <HStack spacing={4} justify="flex-end">
                              <Button
                                colorScheme="green"
                                onClick={() => handleStatusChange(submission._id, 'approved')}
                                leftIcon={<Icon as={FaCheckCircle} />}
                              >
                                Approve
                              </Button>
                              <Button
                                colorScheme="red"
                                onClick={() => handleStatusChange(submission._id, 'rejected')}
                                leftIcon={<Icon as={FaTimesCircle} />}
                              >
                                Reject
                              </Button>
                            </HStack>
                          )}
                        </VStack>
                      </CardBody>
                    </Card>
                  )) : []}
                </VStack>
              </ModalBody>
            </ModalContent>
          </Modal>
        )}
      </Container>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);
  if (!session) {
    return {
      redirect: {
        destination: '/signin',
        permanent: false,
      },
    };
  }
  const { dashboardConfig } = await import('../core/config');
  const ownerIds: string[] = dashboardConfig.dashboard.admins || [];
  return { props: { ownerIds } };
}; 