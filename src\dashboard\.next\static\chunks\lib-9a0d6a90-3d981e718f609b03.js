"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{17600:(e,t,r)=>{r.d(t,{f:()=>S});var n=r(78165);function i(e){return e}function o(e){return{startLineNumber:e.start.line+1,startColumn:e.start.character+1,endLineNumber:e.end.line+1,endColumn:e.end.character+1}}function a(e){return{...o(e.location.range),message:e.message,resource:n.r.parse(e.location.uri)}}function u(e){var t;let r={...o(e.range),message:e.message,severity:e.severity?4===(t=e.severity)?1:3===t?2:2===t?4:8:8};return null!=e.code&&(r.code=null==e.codeDescription?String(e.code):{value:String(e.code),target:n.r.parse(e.codeDescription.href)}),e.relatedInformation&&(r.relatedInformation=e.relatedInformation.map(a)),e.tags&&(r.tags=e.tags.map(i)),null!=e.source&&(r.source=e.source),r}function l(e){return{range:o(e.range),text:e.newText}}function c(e,t,r){return{resource:n.r.parse(t),versionId:r,textEdit:l(e)}}function s(e){let t={title:e.title,isPreferred:e.isPreferred};return e.diagnostics&&(t.diagnostics=e.diagnostics.map(u)),e.disabled&&(t.disabled=e.disabled.reason),e.edit&&(t.edit=function(e){var t;let r=[];if(e.changes)for(let[t,n]of Object.entries(e.changes))for(let e of n)r.push(c(e,t));if(e.documentChanges)for(let i of e.documentChanges)if("textDocument"in i)for(let e of i.edits)r.push(c(e,i.textDocument.uri,null!=(t=i.textDocument.version)?t:void 0));else r.push(function(e){let t="create"===e.kind?{newResource:n.r.parse(e.uri)}:"delete"===e.kind?{oldResource:n.r.parse(e.uri)}:{oldResource:n.r.parse(e.oldUri),newResource:n.r.parse(e.newUri)};return e.options&&(t.options=function(e){let t={};return null!=e.ignoreIfExists&&(t.ignoreIfExists=e.ignoreIfExists),null!=e.ignoreIfNotExists&&(t.ignoreIfNotExists=e.ignoreIfNotExists),null!=e.overwrite&&(t.overwrite=e.overwrite),null!=e.recursive&&(t.recursive=e.recursive),t}(e.options)),t}(i));return{edits:r}}(e.edit)),null!=e.isPreferred&&(t.isPreferred=e.isPreferred),e.kind&&(t.kind=e.kind),t}function d(e){return e}function g(e){return{value:e.value}}function f(e){return{range:o(e.range),text:e.newText}}function m(e){return e}function p(e){let t={detail:e.detail,kind:fromSymbolKind(e.kind),name:e.name,range:fromRange(e.range),selectionRange:fromRange(e.selectionRange),tags:e.tags.map(fromSymbolTag)};return e.children&&(t.children=e.children.map(p)),t}function v(e){var t,r,n,i;let a={detail:null!=(t=e.detail)?t:"",kind:1===(i=e.kind)?0:2===i?1:3===i?2:4===i?3:5===i?4:6===i?5:7===i?6:8===i?7:9===i?8:10===i?9:11===i?10:12===i?11:13===i?12:14===i?13:15===i?14:16===i?15:17===i?16:18===i?17:19===i?18:20===i?19:21===i?20:22===i?21:23===i?22:24===i?23:25===i?24:25,name:e.name,range:o(e.range),selectionRange:o(e.selectionRange),tags:null!=(n=null==(r=e.tags)?void 0:r.map(m))?n:[]};return e.children&&(a.children=e.children.map(v)),a}function y(e){let t={start:e.startLine+1,end:e.endLine+1};return null!=e.kind&&(t.kind={value:e.kind}),t}function h(e){return"string"==typeof e?{value:e}:{value:`\`\`\`${e.language}
${e.value}
\`\`\``}}function w(e){return{character:e.column-1,line:e.lineNumber-1}}function b(e){let t={range:o(e.range)};return null!=e.tooltip&&(t.tooltip=e.tooltip),null!=e.target&&(t.url=n.r.parse(e.target)),t}function M(e){let t={range:o(e.targetRange),targetSelectionRange:o(e.targetSelectionRange),uri:n.r.parse(e.targetUri)};return e.originSelectionRange&&(t.originSelectionRange=o(e.originSelectionRange)),t}function k(e){let t=[],r=e;for(;r;)t.push({range:o(r.range)}),r=r.parent;return t}function S(e,t){let r={completion:!0,customTags:[],enableSchemaRequest:!1,format:!0,isKubernetes:!1,hover:!0,schemas:[],validate:!0,yamlVersion:"1.2",...t};e.languages.register({id:"yaml",extensions:[".yaml",".yml"],aliases:["YAML","yaml","YML","yml"],mimetypes:["application/x-yaml"]});let n=function(e,t){let r,{createData:n,interval:i=3e4,label:o,moduleId:a,stopWhenIdleFor:u=12e4}=t,l=0,c=!1,s=()=>{r&&(r.dispose(),r=void 0)},d=setInterval(()=>{r&&Date.now()-l>u&&s()},i);return{dispose(){c=!0,clearInterval(d),s()},getWorker(...t){if(c)throw Error("Worker manager has been disposed");return l=Date.now(),r||(r=e.editor.createWebWorker({createData:n,label:o,moduleId:a})),r.withSyncedResources(t)},updateCreateData(e){n=e,s()}}}(e,{label:"yaml",moduleId:"monaco-yaml/yaml.worker",createData:r}),i=new WeakMap,a=function(e,t,r){let n=new Map,i=e=>{if("*"===t)return!0;let r=e.getLanguageId();return Array.isArray(t)?t.includes(r):t===r},o=async t=>{let n=t.getVersionId(),o=await r.provideMarkerData(t);!t.isDisposed()&&n===t.getVersionId()&&i(t)&&e.editor.setModelMarkers(t,r.owner,o??[])},a=e=>{let t;if(!i(e))return;let r=e.onDidChangeContent(()=>{clearTimeout(t),t=setTimeout(()=>{o(e)},500)});n.set(e,{dispose(){clearTimeout(t),r.dispose()}}),o(e)},u=t=>{e.editor.setModelMarkers(t,r.owner,[]);let i=n.get(t);i&&(i.dispose(),n.delete(t))},l=e.editor.onDidCreateModel(a),c=e.editor.onWillDisposeModel(e=>{u(e),r.doReset?.(e)}),s=e.editor.onDidChangeModelLanguage(({model:e})=>{u(e),a(e),r.doReset?.(e)});for(let t of e.editor.getModels())a(t);return{dispose(){for(let e of n.keys())u(e);l.dispose(),c.dispose(),s.dispose()},async revalidate(){await Promise.all(e.editor.getModels().map(o))}}}(e,"yaml",{owner:"yaml",async provideMarkerData(e){let t=await n.getWorker(e.uri),r=await t.doValidation(String(e.uri));return i.set(e,r),null==r?void 0:r.map(u)},async doReset(e){let t=await n.getWorker(e.uri);await t.resetSchema(String(e.uri))}}),c=[n,a,e.languages.registerCompletionItemProvider("yaml",{triggerCharacters:[" ",":"],async provideCompletionItems(e,t){let r=e.getWordUntilPosition(t),i=await n.getWorker(e.uri),a=await i.doComplete(String(e.uri),w(t));if(a){var u;return u={range:{startLineNumber:t.lineNumber,startColumn:r.startColumn,endLineNumber:t.lineNumber,endColumn:r.endColumn}},{incomplete:!!a.isIncomplete,suggestions:a.items.map(e=>(function(e,t){var r,n,i,a,u,l;let c,s=null!=(r=t.itemDefaults)?r:{},m=null!=(n=e.textEdit)?n:s.editRange,p=null!=(i=e.commitCharacters)?i:s.commitCharacters,v=null!=(a=e.insertTextFormat)?a:s.insertTextFormat,y=null!=(u=e.insertTextMode)?u:s.insertTextMode,h=e.insertText;m?(c="range"in m?o(m.range):"insert"in m&&"replace"in m?{insert:o(m.insert),replace:o(m.replace)}:o(m),"newText"in m&&(h=m.newText)):c={...t.range};let w={insertText:null!=h?h:e.label,kind:null==e.kind||1===(l=e.kind)?18:2===l?0:3===l?1:4===l?2:5===l?3:6===l?4:7===l?5:8===l?7:9===l?8:10===l?9:11===l?12:12===l?13:13===l?15:14===l?17:15===l?27:16===l?19:17===l?20:18===l?21:19===l?23:20===l?16:21===l?14:22===l?6:23===l?10:24===l?11:24,label:e.label,range:c};return e.additionalTextEdits&&(w.additionalTextEdits=e.additionalTextEdits.map(f)),e.command&&(w.command=function(e){let t={title:e.title,id:e.command};return e.arguments&&(t.arguments=e.arguments),t}(e.command)),p&&(w.commitCharacters=p),null!=e.detail&&(w.detail=e.detail),"string"==typeof e.documentation?w.documentation=e.documentation:e.documentation&&(w.documentation=g(e.documentation)),null!=e.filterText&&(w.filterText=e.filterText),2===v?w.insertTextRules=4:2===y&&(w.insertTextRules=1),null!=e.preselect&&(w.preselect=e.preselect),null!=e.sortText&&(w.sortText=e.sortText),e.tags&&(w.tags=e.tags.map(d)),w})(e,{range:u.range,itemDefaults:a.itemDefaults}))}}}}),e.languages.registerHoverProvider("yaml",{async provideHover(e,t){let r=await n.getWorker(e.uri),i=await r.doHover(String(e.uri),w(t));if(i){var a;let e={contents:"string"==typeof(a=i.contents)||"language"in a?[h(a)]:Array.isArray(a)?a.map(h):[g(a)]};return i.range&&(e.range=o(i.range)),e}}}),e.languages.registerDefinitionProvider("yaml",{async provideDefinition(e,t){let r=await n.getWorker(e.uri),i=await r.doDefinition(String(e.uri),w(t));return null==i?void 0:i.map(M)}}),e.languages.registerDocumentSymbolProvider("yaml",{displayName:"yaml",async provideDocumentSymbols(e){let t=await n.getWorker(e.uri),r=await t.findDocumentSymbols(String(e.uri));return null==r?void 0:r.map(v)}}),e.languages.registerDocumentFormattingEditProvider("yaml",{displayName:"yaml",async provideDocumentFormattingEdits(e){let t=await n.getWorker(e.uri),r=await t.format(String(e.uri));return null==r?void 0:r.map(l)}}),e.languages.registerLinkProvider("yaml",{async provideLinks(e){let t=await n.getWorker(e.uri),r=await t.findLinks(String(e.uri));if(r)return{links:r.map(b)}}}),e.languages.registerCodeActionProvider("yaml",{async provideCodeActions(e,t,r){var a;let u=await n.getWorker(e.uri),l=await u.getCodeAction(String(e.uri),{start:{line:t.startLineNumber-1,character:t.startColumn-1},end:{line:t.endLineNumber-1,character:t.endColumn-1}},{diagnostics:(null==(a=i.get(e))?void 0:a.filter(e=>t.intersectRanges(o(e.range))))||[],only:r.only?[r.only]:void 0,triggerKind:r.trigger});if(l)return{actions:l.map(s),dispose(){}}}}),e.languages.registerFoldingRangeProvider("yaml",{async provideFoldingRanges(e){let t=await n.getWorker(e.uri),r=await t.getFoldingRanges(String(e.uri));return null==r?void 0:r.map(y)}}),e.languages.setLanguageConfiguration("yaml",{comments:{lineComment:"#"},brackets:[["{","}"],["[","]"],["(",")"]],autoClosingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"'},{open:"'",close:"'"}],surroundingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"'},{open:"'",close:"'"}]}),e.languages.registerOnTypeFormattingEditProvider("yaml",{autoFormatTriggerCharacters:["\n"],async provideOnTypeFormattingEdits(e,t,r,i){let o=await n.getWorker(e.uri),a=await o.doDocumentOnTypeFormatting(String(e.uri),w(t),r,{insertSpaces:i.insertSpaces,tabSize:i.tabSize});return null==a?void 0:a.map(l)}}),e.languages.registerSelectionRangeProvider("yaml",{async provideSelectionRanges(e,t){let r=await n.getWorker(e.uri),i=await r.getSelectionRanges(String(e.uri),t.map(w));return null==i?void 0:i.map(k)}})];return{dispose(){for(let e of c)e.dispose()},async update(e){n.updateCreateData(Object.assign(r,e)),await a.revalidate()}}}},75533:(e,t,r)=>{function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n;n=r[t],t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}r.d(t,{Ay:()=>L,wG:()=>M});var a=r(31848),u={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},l=(function(e){return function t(){for(var r=this,n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return i.length>=e.length?e.apply(this,i):function(){for(var e=arguments.length,n=Array(e),o=0;o<e;o++)n[o]=arguments[o];return t.apply(r,[].concat(i,n))}}})(function(e,t){throw Error(e[t]||e.default)})(u);let c={config:function(e){return(e||l("configIsRequired"),({}).toString.call(e).includes("Object")||l("configType"),e.urls)?(console.warn(u.deprecation),{paths:{vs:e.urls.monacoBase}}):e}},s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}};var d={type:"cancelation",msg:"operation is manually canceled"};let g=function(e){var t=!1,r=new Promise(function(r,n){e.then(function(e){return t?n(d):r(e)}),e.catch(n)});return r.cancel=function(){return t=!0},r};var f=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,i=!1,o=void 0;try{for(var a,u=e[Symbol.iterator]();!(n=(a=u.next()).done)&&(r.push(a.value),r.length!==t);n=!0);}catch(e){i=!0,o=e}finally{try{n||null==u.return||u.return()}finally{if(i)throw o}}return r}}(e,2)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return o(e,t)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(a.A.create({config:{paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}},isInitialized:!1,resolve:null,reject:null,monaco:null}),2),m=f[0],p=f[1];function v(e){return document.body.appendChild(e)}function y(e){var t,r,n=m(function(e){return{config:e.config,reject:e.reject}}),i=(t="".concat(n.config.paths.vs,"/loader.js"),r=document.createElement("script"),t&&(r.src=t),r);return i.onload=function(){return e()},i.onerror=n.reject,i}function h(){var e=m(function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(t){w(t),e.resolve(t)},function(t){e.reject(t)})}function w(e){m().monaco||p({monaco:e})}var b=new Promise(function(e,t){return p({resolve:e,reject:t})});let M={config:function(e){var t=c.config(e),r=t.monaco,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(t,["monaco"]);p(function(e){return{config:function e(t,r){return Object.keys(r).forEach(function(n){r[n]instanceof Object&&t[n]&&Object.assign(r[n],e(t[n],r[n]))}),i(i({},t),r)}(e.config,n),monaco:r}})},init:function(){var e=m(function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}});if(!e.isInitialized){if(p({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),g(b);if(window.monaco&&window.monaco.editor)return w(window.monaco),e.resolve(window.monaco),g(b);s(v,y)(h)}return g(b)},__getMonacoInstance:function(){return m(function(e){return e.monaco})}};var k=r(94285),S={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},R={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},x=function({children:e}){return k.createElement("div",{style:R.container},e)},E=(0,k.memo)(function({width:e,height:t,isEditorReady:r,loading:n,_ref:i,className:o,wrapperProps:a}){return k.createElement("section",{style:{...S.wrapper,width:e,height:t},...a},!r&&k.createElement(x,null,n),k.createElement("div",{ref:i,style:{...S.fullWidth,...!r&&S.hide},className:o}))}),O=function(e){(0,k.useEffect)(e,[])},C=function(e,t,r=!0){let n=(0,k.useRef)(!0);(0,k.useEffect)(n.current||!r?()=>{n.current=!1}:e,t)};function j(){}function P(e,t,r,n){var i,o,a,u,l,c;return i=e,o=n,i.editor.getModel(T(i,o))||(a=e,u=t,l=r,c=n,a.editor.createModel(u,l,c?T(a,c):void 0))}function T(e,t){return e.Uri.parse(t)}(0,k.memo)(function({original:e,modified:t,language:r,originalLanguage:n,modifiedLanguage:i,originalModelPath:o,modifiedModelPath:a,keepCurrentOriginalModel:u=!1,keepCurrentModifiedModel:l=!1,theme:c="light",loading:s="Loading...",options:d={},height:g="100%",width:f="100%",className:m,wrapperProps:p={},beforeMount:v=j,onMount:y=j}){let[h,w]=(0,k.useState)(!1),[b,S]=(0,k.useState)(!0),R=(0,k.useRef)(null),x=(0,k.useRef)(null),T=(0,k.useRef)(null),D=(0,k.useRef)(y),I=(0,k.useRef)(v),L=(0,k.useRef)(!1);O(()=>{let e=M.init();return e.then(e=>(x.current=e)&&S(!1)).catch(e=>e?.type!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>{let t;return R.current?(t=R.current?.getModel(),void(u||t?.original?.dispose(),l||t?.modified?.dispose(),R.current?.dispose())):e.cancel()}}),C(()=>{if(R.current&&x.current){let t=R.current.getOriginalEditor(),i=P(x.current,e||"",n||r||"text",o||"");i!==t.getModel()&&t.setModel(i)}},[o],h),C(()=>{if(R.current&&x.current){let e=R.current.getModifiedEditor(),n=P(x.current,t||"",i||r||"text",a||"");n!==e.getModel()&&e.setModel(n)}},[a],h),C(()=>{let e=R.current.getModifiedEditor();e.getOption(x.current.editor.EditorOption.readOnly)?e.setValue(t||""):t!==e.getValue()&&(e.executeEdits("",[{range:e.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),e.pushUndoStop())},[t],h),C(()=>{R.current?.getModel()?.original.setValue(e||"")},[e],h),C(()=>{let{original:e,modified:t}=R.current.getModel();x.current.editor.setModelLanguage(e,n||r||"text"),x.current.editor.setModelLanguage(t,i||r||"text")},[r,n,i],h),C(()=>{x.current?.editor.setTheme(c)},[c],h),C(()=>{R.current?.updateOptions(d)},[d],h);let N=(0,k.useCallback)(()=>{if(!x.current)return;I.current(x.current);let u=P(x.current,e||"",n||r||"text",o||""),l=P(x.current,t||"",i||r||"text",a||"");R.current?.setModel({original:u,modified:l})},[r,t,i,e,n,o,a]),W=(0,k.useCallback)(()=>{!L.current&&T.current&&(R.current=x.current.editor.createDiffEditor(T.current,{automaticLayout:!0,...d}),N(),x.current?.editor.setTheme(c),w(!0),L.current=!0)},[d,c,N]);return(0,k.useEffect)(()=>{h&&D.current(R.current,x.current)},[h]),(0,k.useEffect)(()=>{b||h||W()},[b,h,W]),k.createElement(E,{width:f,height:g,isEditorReady:h,loading:s,_ref:T,className:m,wrapperProps:p})});var D=function(e){let t=(0,k.useRef)();return(0,k.useEffect)(()=>{t.current=e},[e]),t.current},I=new Map,L=(0,k.memo)(function({defaultValue:e,defaultLanguage:t,defaultPath:r,value:n,language:i,path:o,theme:a="light",line:u,loading:l="Loading...",options:c={},overrideServices:s={},saveViewState:d=!0,keepCurrentModel:g=!1,width:f="100%",height:m="100%",className:p,wrapperProps:v={},beforeMount:y=j,onMount:h=j,onChange:w,onValidate:b=j}){let[S,R]=(0,k.useState)(!1),[x,T]=(0,k.useState)(!0),L=(0,k.useRef)(null),N=(0,k.useRef)(null),W=(0,k.useRef)(null),A=(0,k.useRef)(h),V=(0,k.useRef)(y),F=(0,k.useRef)(),z=(0,k.useRef)(n),_=D(o),U=(0,k.useRef)(!1),q=(0,k.useRef)(!1);O(()=>{let e=M.init();return e.then(e=>(L.current=e)&&T(!1)).catch(e=>e?.type!=="cancelation"&&console.error("Monaco initialization: error:",e)),()=>N.current?void(F.current?.dispose(),g?d&&I.set(o,N.current.saveViewState()):N.current.getModel()?.dispose(),N.current.dispose()):e.cancel()}),C(()=>{let a=P(L.current,e||n||"",t||i||"",o||r||"");a!==N.current?.getModel()&&(d&&I.set(_,N.current?.saveViewState()),N.current?.setModel(a),d&&N.current?.restoreViewState(I.get(o)))},[o],S),C(()=>{N.current?.updateOptions(c)},[c],S),C(()=>{N.current&&void 0!==n&&(N.current.getOption(L.current.editor.EditorOption.readOnly)?N.current.setValue(n):n!==N.current.getValue()&&(q.current=!0,N.current.executeEdits("",[{range:N.current.getModel().getFullModelRange(),text:n,forceMoveMarkers:!0}]),N.current.pushUndoStop(),q.current=!1))},[n],S),C(()=>{let e=N.current?.getModel();e&&i&&L.current?.editor.setModelLanguage(e,i)},[i],S),C(()=>{void 0!==u&&N.current?.revealLine(u)},[u],S),C(()=>{L.current?.editor.setTheme(a)},[a],S);let H=(0,k.useCallback)(()=>{if(!(!W.current||!L.current)&&!U.current){V.current(L.current);let l=o||r,g=P(L.current,n||e||"",t||i||"",l||"");N.current=L.current?.editor.create(W.current,{model:g,automaticLayout:!0,...c},s),d&&N.current.restoreViewState(I.get(l)),L.current.editor.setTheme(a),void 0!==u&&N.current.revealLine(u),R(!0),U.current=!0}},[e,t,r,n,i,o,c,s,d,a,u]);return(0,k.useEffect)(()=>{S&&A.current(N.current,L.current)},[S]),(0,k.useEffect)(()=>{x||S||H()},[x,S,H]),z.current=n,(0,k.useEffect)(()=>{S&&w&&(F.current?.dispose(),F.current=N.current?.onDidChangeModelContent(e=>{q.current||w(N.current.getValue(),e)}))},[S,w]),(0,k.useEffect)(()=>{if(S){let e=L.current.editor.onDidChangeMarkers(e=>{let t=N.current.getModel()?.uri;if(t&&e.find(e=>e.path===t.path)){let e=L.current.editor.getModelMarkers({resource:t});b?.(e)}});return()=>{e?.dispose()}}return()=>{}},[S,b]),k.createElement(E,{width:f,height:m,isEditorReady:S,loading:l,_ref:W,className:p,wrapperProps:v})})}}]);