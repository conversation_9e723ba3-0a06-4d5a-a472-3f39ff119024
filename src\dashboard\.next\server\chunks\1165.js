"use strict";exports.id=1165,exports.ids=[1165],exports.modules={21165:(e,s,i)=>{i.a(e,async(e,n)=>{try{i.r(s),i.d(s,{default:()=>a});var o=i(8732),r=i(46637),l=i(82015),t=i(87615),c=e([r]);r=(c.then?(await c)():c)[0];let E={General:{icon:t.pcC,permissions:["ADMINISTRATOR","VIEW_AUDIT_LOG","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","MANAGE_EMOJIS_AND_STICKERS","MANAGE_WEBHOOKS","VIEW_CHANNEL"]},Text:{icon:t.mEP,permissions:["SEND_MESSAGES","EMBED_LINKS","ATTACH_FILES","ADD_REACTIONS","USE_EXTERNAL_EMOJIS","MENTION_EVERYONE","MANAGE_MESSAGES","READ_MESSAGE_HISTORY"]},Voice:{icon:t.o77,permissions:["CONNECT","SPEAK","STREAM","USE_VAD","PRIORITY_SPEAKER","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS"]},Members:{icon:t.cfS,permissions:["KICK_MEMBERS","BAN_MEMBERS","CHANGE_NICKNAME","MANAGE_NICKNAMES","CREATE_INSTANT_INVITE"]}};function a({isOpen:e,onClose:s,onSuccess:i,role:n}){let t=(0,r.dj)(),[c,a]=(0,l.useState)(!1),[d,h]=(0,l.useState)({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1}),S=async()=>{a(!0);try{let e=await fetch(`/api/discord/roles/${n.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to update role")}t({title:"Success",description:"Role updated successfully",status:"success",duration:3e3}),i(),s()}catch(e){t({title:"Error",description:e.message||"Failed to update role",status:"error",duration:5e3})}finally{a(!1)}},A=e=>{h(s=>({...s,permissions:s.permissions.includes(e)?s.permissions.filter(s=>s!==e):[...s.permissions,e]}))},p=(e,s)=>{h(i=>({...i,[e]:s}))};return(0,o.jsxs)(r.aF,{isOpen:e,onClose:s,size:"xl",scrollBehavior:"inside",children:[(0,o.jsx)(r.mH,{backdropFilter:"blur(10px)"}),(0,o.jsxs)(r.$m,{bg:"gray.800",children:[(0,o.jsx)(r.rQ,{children:"Edit Role"}),(0,o.jsx)(r.s_,{}),(0,o.jsx)(r.cw,{children:(0,o.jsxs)(r.Tk,{spacing:6,children:[(0,o.jsxs)(r.MJ,{children:[(0,o.jsx)(r.lR,{children:"Role Name"}),(0,o.jsx)(r.pd,{placeholder:"Enter role name",value:d.name,onChange:e=>p("name",e.target.value)})]}),(0,o.jsxs)(r.MJ,{children:[(0,o.jsx)(r.lR,{children:"Role Color"}),(0,o.jsx)(r.pd,{type:"color",value:d.color,onChange:e=>p("color",e.target.value)})]}),(0,o.jsx)(r.MJ,{children:(0,o.jsxs)(r.zt,{spacing:4,children:[(0,o.jsx)(r.Sc,{isChecked:d.hoist,onChange:e=>p("hoist",e.target.checked),children:"Display role separately"}),(0,o.jsx)(r.Sc,{isChecked:d.mentionable,onChange:e=>p("mentionable",e.target.checked),children:"Allow anyone to @mention"})]})}),(0,o.jsx)(r.cG,{}),(0,o.jsx)(r.EY,{fontSize:"lg",fontWeight:"bold",alignSelf:"flex-start",children:"Permissions"}),Object.entries(E).map(([e,s])=>(0,o.jsxs)(r.az,{w:"full",children:[(0,o.jsxs)(r.zt,{mb:2,children:[(0,o.jsx)(r.In,{as:s.icon}),(0,o.jsx)(r.EY,{fontWeight:"semibold",children:e})]}),(0,o.jsx)(r.rS,{columns:2,spacing:2,children:s.permissions.map(e=>(0,o.jsx)(r.Sc,{isChecked:d.permissions.includes(e),onChange:()=>A(e),children:e.split("_").map(e=>e.charAt(0)+e.slice(1).toLowerCase()).join(" ")},e))})]},e))]})}),(0,o.jsxs)(r.jl,{children:[(0,o.jsx)(r.$n,{variant:"ghost",mr:3,onClick:s,children:"Cancel"}),(0,o.jsx)(r.$n,{colorScheme:"blue",onClick:S,isLoading:c,children:"Save Changes"})]})]})]})}n()}catch(e){n(e)}})}};