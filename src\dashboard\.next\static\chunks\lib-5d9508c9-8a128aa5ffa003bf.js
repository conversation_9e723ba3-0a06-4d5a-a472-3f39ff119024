"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5388],{57691:(e,t,n)=>{n.d(t,{h:()=>f,n:()=>s});var o=n(94285),r=n(79007);let i=e=>{let t,n=new Set,o=(e,o)=>{let r="function"==typeof e?e(t):e;if(!Object.is(r,t)){let e=t;t=(null!=o?o:"object"!=typeof r||null===r)?r:Object.assign({},t,r),n.forEach(n=>n(t,e))}},r=()=>t,i={setState:o,getState:r,getInitialState:()=>u,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=t=e(o,r,i);return i},u=e=>e?i(e):i,{useDebugValue:a}=o,{useSyncExternalStoreWithSelector:d}=r,c=e=>e;function s(e,t=c,n){let o=d(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return a(o),o}let l=(e,t)=>{let n=u(e),o=(e,o=t)=>s(n,e,o);return Object.assign(o,n),o},f=(e,t)=>e?l(e,t):l},80829:(e,t,n)=>{},88671:(e,t,n)=>{function o(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,o]of e)if(!Object.is(o,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let o of n)if(!Object.prototype.hasOwnProperty.call(t,o)||!Object.is(e[o],t[o]))return!1;return!0}n.d(t,{x:()=>o})},98258:(e,t,n)=>{n.d(t,{Yy:()=>E});var o=e=>void 0!==e.nodeType;var r=()=>"undefined"!=typeof document,i=e=>r()&&e.test(function(){let e=navigator.userAgentData;return e?.platform??navigator.platform}()),u=()=>r()&&!!navigator.maxTouchPoints,a=!1,d=null,c=!1,s=!1,l=new Set;function f(e,t){l.forEach(n=>n(e,t))}var p="undefined"!=typeof window&&null!=window.navigator&&/^Mac/.test(window.navigator.platform);function y(e){c=!0,e.metaKey||!p&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key||(d="keyboard",f("keyboard",e))}function b(e){if(d="pointer","mousedown"===e.type||"pointerdown"===e.type){c=!0;let t=e.composedPath?e.composedPath()[0]:e.target,n=!1;try{n=t.matches(":focus-visible")}catch{}n||f("pointer",e)}}function v(e){(0===e.mozInputSource&&e.isTrusted||0===e.detail&&!e.pointerType)&&(c=!0,d="virtual")}function m(e){e.target!==window&&e.target!==document&&(e.target instanceof Element&&e.target.hasAttribute("tabindex")||(c||s||(d="virtual",f("virtual",e)),c=!1,s=!1))}function g(){c=!1,s=!0}function w(){return"pointer"!==d}function E(e){(function(){if(!r()||a)return;let{focus:e}=HTMLElement.prototype;HTMLElement.prototype.focus=function(...t){c=!0,e.apply(this,t)},document.addEventListener("keydown",y,!0),document.addEventListener("keyup",y,!0),document.addEventListener("click",v,!0),window.addEventListener("focus",m,!0),window.addEventListener("blur",g,!1),"undefined"!=typeof PointerEvent?(document.addEventListener("pointerdown",b,!0),document.addEventListener("pointermove",b,!0),document.addEventListener("pointerup",b,!0)):(document.addEventListener("mousedown",b,!0),document.addEventListener("mousemove",b,!0),document.addEventListener("mouseup",b,!0)),a=!0})(),e(w());let t=()=>e(w());return l.add(t),()=>{l.delete(t)}}}}]);