"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7102],{2965:(e,t,r)=>{r.d(t,{y:()=>ei,L:()=>ea});var n=r(94285),a=r(3638),i=r(39712),o=r(45096),l=r(49634),c=r(91397),s=r(14901),u=r(47655),f=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e){var{direction:t,width:r,dataKey:a,isAnimationActive:s,animationBegin:p,animationDuration:v,animationEasing:y}=e,h=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,f),m=(0,o.J9)(h,!1),{data:b,dataPointFormatter:g,xAxisId:O,yAxisId:w,errorBarOffset:P}=(0,l.G9)(),x=(0,c.ZI)(O),E=(0,c.gi)(w);if((null==x?void 0:x.scale)==null||(null==E?void 0:E.scale)==null||null==b||"x"===t&&"number"!==x.type)return null;var k=b.map(e=>{var o,l,{x:c,y:f,value:h,errorVal:b}=g(e,a,t);if(!b)return null;var O=[];if(Array.isArray(b)?[o,l]=b:o=l=b,"x"===t){var{scale:w}=x,k=f+P,j=k+r,S=k-r,C=w(h-o),A=w(h+l);O.push({x1:A,y1:j,x2:A,y2:S}),O.push({x1:C,y1:k,x2:A,y2:k}),O.push({x1:C,y1:j,x2:C,y2:S})}else if("y"===t){var{scale:D}=E,M=c+P,N=M-r,T=M+r,z=D(h-o),I=D(h+l);O.push({x1:N,y1:I,x2:T,y2:I}),O.push({x1:M,y1:z,x2:M,y2:I}),O.push({x1:N,y1:z,x2:T,y2:z})}var G="".concat(c+P,"px ").concat(f+P,"px");return n.createElement(i.W,d({className:"recharts-errorBar",key:"bar-".concat(O.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},m),O.map(e=>{var t=s?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return n.createElement(u.i,{from:{transform:"scaleY(0)",transformOrigin:G},to:{transform:"scaleY(1)",transformOrigin:G},begin:p,easing:y,isActive:s,duration:v,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:G}},n.createElement("line",d({},e,{style:t})))}))});return n.createElement(i.W,{className:"recharts-errorBars"},k)}var y=(0,n.createContext)(void 0);function h(e){var{direction:t,children:r}=e;return n.createElement(y.Provider,{value:t},r)}var m={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function b(e){var t,r,a=(t=e.direction,r=(0,n.useContext)(y),null!=t?t:null!=r?r:"x"),{width:i,isAnimationActive:o,animationBegin:c,animationDuration:u,animationEasing:f}=(0,s.e)(e,m);return n.createElement(n.Fragment,null,n.createElement(l.pU,{dataKey:e.dataKey,direction:a}),n.createElement(v,d({},e,{direction:a,width:i,isAnimationActive:o,animationBegin:c,animationDuration:u,animationEasing:f})))}class g extends n.Component{render(){return n.createElement(b,this.props)}}p(g,"defaultProps",m),p(g,"displayName","ErrorBar");var O=r(83415),w=r(16707),P=r(93833),x=r(26471),E=r(33053),k=r(83733),j=r(48278),S=r(13001),C=r(99095),A=r(15436),D=r(92735),M=r(56199);function N(e,t){var r,n,a=(0,D.G)(t=>(0,M.Rl)(t,e)),i=(0,D.G)(e=>(0,M.sf)(e,t)),o=null!=(r=null==a?void 0:a.allowDataOverflow)?r:M.PU.allowDataOverflow,l=null!=(n=null==i?void 0:i.allowDataOverflow)?n:M.cd.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function T(e){var{xAxisId:t,yAxisId:r,clipPathId:a}=e,i=(0,c.oM)(),{needClipX:o,needClipY:l,needClip:s}=N(t,r);if(!s)return null;var{x:u,y:f,width:p,height:d}=i;return n.createElement("clipPath",{id:"clipPath-".concat(a)},n.createElement("rect",{x:o?u:u-p/2,y:l?f:f-d/2,width:o?p:2*p,height:l?d:2*d}))}var z=r(14710),I=r(71931),G=r(61587),B=r(18080),R=r(25824),L=r(76378),F=["onMouseEnter","onMouseLeave","onClick"],U=["value","background","tooltipPosition"],J=["onMouseEnter","onClick","onMouseLeave"];function K(){return(K=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function W(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function $(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?W(Object(r),!0).forEach(function(t){V(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):W(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function V(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Z(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}var H=e=>{var{dataKey:t,name:r,fill:n,legendType:a,hide:i}=e;return[{inactive:i,dataKey:t,type:a,color:n,value:(0,E.uM)(r,t),payload:e}]};function X(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:a,name:i,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:a,dataKey:t,nameKey:void 0,name:(0,E.uM)(i,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function _(e){var t=(0,D.G)(B.A2),{data:r,dataKey:a,background:i,allOtherBarProps:l}=e,{onMouseEnter:c,onMouseLeave:s,onClick:u}=l,f=Z(l,F),p=(0,S.Cj)(c,a),d=(0,S.Pg)(s),v=(0,S.Ub)(u,a);if(!i||null==r)return null;var y=(0,o.J9)(i,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:o,background:l,tooltipPosition:c}=e,s=Z(e,U);if(!l)return null;var u=p(e,r),h=d(e,r),m=v(e,r),b=$($($($($({option:i,isActive:String(r)===t},s),{},{fill:"#eee"},l),y),(0,k.XC)(f,e,r)),{},{onMouseEnter:u,onMouseLeave:h,onClick:m,dataKey:a,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(j.z,K({key:"background-bar-".concat(r)},b))}))}function q(e){var{data:t,props:r,showLabels:a}=e,l=(0,o.J9)(r,!1),{shape:c,dataKey:s,activeBar:u}=r,f=(0,D.G)(B.A2),p=(0,D.G)(B.Xb),{onMouseEnter:d,onClick:v,onMouseLeave:y}=r,h=Z(r,J),m=(0,S.Cj)(d,s),b=(0,S.Pg)(y),g=(0,S.Ub)(v,s);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=u&&String(t)===f&&(null==p||s===p),a=$($($({},l),e),{},{isActive:r,option:r?u:c,index:t,dataKey:s});return n.createElement(i.W,K({className:"recharts-bar-rectangle"},(0,k.XC)(h,e,t),{onMouseEnter:m(e,t),onMouseLeave:b(e,t),onClick:g(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),n.createElement(j.z,a))}),a&&w.Z.renderCallByParent(r,t)):null}function Y(e){var{props:t,previousRectanglesRef:r}=e,{data:a,layout:o,isAnimationActive:l,animationBegin:c,animationDuration:s,animationEasing:f,onAnimationEnd:p,onAnimationStart:d}=t,v=r.current,y=(0,L.n)(t,"recharts-bar-"),[h,m]=(0,n.useState)(!1),b=(0,n.useCallback)(()=>{"function"==typeof p&&p(),m(!1)},[p]),g=(0,n.useCallback)(()=>{"function"==typeof d&&d(),m(!0)},[d]);return n.createElement(u.i,{begin:c,duration:s,isActive:l,easing:f,from:{t:0},to:{t:1},onAnimationEnd:b,onAnimationStart:g,key:y},e=>{var{t:l}=e,c=1===l?a:a.map((e,t)=>{var r=v&&v[t];if(r){var n=(0,P.Dj)(r.x,e.x),a=(0,P.Dj)(r.y,e.y),i=(0,P.Dj)(r.width,e.width),c=(0,P.Dj)(r.height,e.height);return $($({},e),{},{x:n(l),y:a(l),width:i(l),height:c(l)})}if("horizontal"===o){var s=(0,P.Dj)(0,e.height)(l);return $($({},e),{},{y:e.y+e.height-s,height:s})}var u=(0,P.Dj)(0,e.width)(l);return $($({},e),{},{width:u})});return l>0&&(r.current=c),n.createElement(i.W,null,n.createElement(q,{props:t,data:c,showLabels:!h}))})}function Q(e){var{data:t,isAnimationActive:r}=e,a=(0,n.useRef)(null);return r&&t&&t.length&&(null==a.current||a.current!==t)?n.createElement(Y,{previousRectanglesRef:a,props:e}):n.createElement(q,{props:e,data:t,showLabels:!0})}var ee=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,E.kr)(e,t)}};class et extends n.PureComponent{constructor(){super(...arguments),V(this,"id",(0,P.NF)("recharts-bar-"))}render(){var{hide:e,data:t,dataKey:r,className:o,xAxisId:l,yAxisId:c,needClip:s,background:u,id:f,layout:p}=this.props;if(e)return null;var d=(0,a.$)("recharts-bar",o),v=(0,P.uy)(f)?this.id:f;return n.createElement(i.W,{className:d},s&&n.createElement("defs",null,n.createElement(T,{clipPathId:v,xAxisId:l,yAxisId:c})),n.createElement(i.W,{className:"recharts-bar-rectangles",clipPath:s?"url(#clipPath-".concat(v,")"):null},n.createElement(_,{data:t,dataKey:r,background:u,allOtherBarProps:this.props}),n.createElement(Q,this.props)),n.createElement(h,{direction:"horizontal"===p?"y":"x"},this.props.children))}}var er={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!x.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function en(e){var t,{xAxisId:r,yAxisId:a,hide:i,legendType:c,minPointSize:u,activeBar:f,animationBegin:p,animationDuration:d,animationEasing:v,isAnimationActive:y}=(0,s.e)(e,er),{needClip:h}=N(r,a),m=(0,z.WX)(),b=(0,G.r)(),g=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:u,stackId:(0,E.$8)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,u,e.stackId]),w=(0,o.aS)(e.children,O.f),P=(0,D.G)(e=>(0,I.OS)(e,r,a,b,g,w));if("vertical"!==m&&"horizontal"!==m)return null;var x=null==P?void 0:P[0];return t=null==x||null==x.height||null==x.width?0:"vertical"===m?x.height/2:x.width/2,n.createElement(l.zk,{xAxisId:r,yAxisId:a,data:P,dataPointFormatter:ee,errorBarOffset:t},n.createElement(et,K({},e,{layout:m,needClip:h,data:P,xAxisId:r,yAxisId:a,hide:i,legendType:c,minPointSize:u,activeBar:f,animationBegin:p,animationDuration:d,animationEasing:v,isAnimationActive:y})))}function ea(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:a,bandSize:i,xAxis:o,yAxis:l,xAxisTicks:c,yAxisTicks:s,stackedData:u,displayedData:f,offset:p,cells:d}=e,v="horizontal"===t?l:o,y=u?v.scale.domain():null,h=(0,E.DW)({numericAxis:v});return f.map((e,f)=>{u?m=(0,E._f)(u[f],y):Array.isArray(m=(0,E.kr)(e,r))||(m=[h,m]);var v=(0,j.l)(n,0)(m[1],f);if("horizontal"===t){var m,b,g,O,w,x,k,[S,C]=[l.scale(m[0]),l.scale(m[1])];b=(0,E.y2)({axis:o,ticks:c,bandSize:i,offset:a.offset,entry:e,index:f}),g=null!=(k=null!=C?C:S)?k:void 0,O=a.size;var A=S-C;if(w=(0,P.M8)(A)?0:A,x={x:b,y:p.top,width:O,height:p.height},Math.abs(v)>0&&Math.abs(w)<Math.abs(v)){var D=(0,P.sA)(w||v)*(Math.abs(v)-Math.abs(w));g-=D,w+=D}}else{var[M,N]=[o.scale(m[0]),o.scale(m[1])];if(b=M,g=(0,E.y2)({axis:l,ticks:s,bandSize:i,offset:a.offset,entry:e,index:f}),O=N-M,w=a.size,x={x:p.left,y:g,width:p.width,height:w},Math.abs(v)>0&&Math.abs(O)<Math.abs(v)){var T=(0,P.sA)(O||v)*(Math.abs(v)-Math.abs(O));O+=T}}return $($({},e),{},{x:b,y:g,width:O,height:w,value:u?m:m[1],payload:e,background:x,tooltipPosition:{x:b+O/2,y:g+w/2}},d&&d[f]&&d[f].props)})}class ei extends n.PureComponent{render(){return n.createElement(l._S,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(A.y,null),n.createElement(R.A,{legendPayload:H(this.props)}),n.createElement(C.r,{fn:X,args:this.props}),n.createElement(en,this.props))}}V(ei,"displayName","Bar"),V(ei,"defaultProps",er)},8219:(e,t,r)=>{r.d(t,{E:()=>m});var n=r(94285),a=r(17649),i=r(96575),o=r(39075),l=r(65392),c=r(73705),s=r(31053),u=r(14901),f=r(6944),p=["width","height"];function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},y=(0,n.forwardRef)(function(e,t){var r,a=(0,u.e)(e.categoricalChartProps,v),{width:y,height:h}=a,m=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(a,p);if(!(0,f.F)(y)||!(0,f.F)(h))return null;var{chartName:b,defaultTooltipEventType:g,validateTooltipEventTypes:O,tooltipPayloadSearcher:w,categoricalChartProps:P}=e;return n.createElement(i.J,{preloadedState:{options:{chartName:b,defaultTooltipEventType:g,validateTooltipEventTypes:O,tooltipPayloadSearcher:w,eventEmitter:void 0}},reduxStoreName:null!=(r=P.id)?r:b},n.createElement(o.TK,{chartData:P.data}),n.createElement(l.s,{width:y,height:h,layout:a.layout,margin:a.margin}),n.createElement(c.p,{accessibilityLayer:a.accessibilityLayer,barCategoryGap:a.barCategoryGap,maxBarSize:a.maxBarSize,stackOffset:a.stackOffset,barGap:a.barGap,barSize:a.barSize,syncId:a.syncId,syncMethod:a.syncMethod,className:a.className}),n.createElement(s.L,d({},m,{width:y,height:h,ref:t})))}),h=["axis","item"],m=(0,n.forwardRef)((e,t)=>n.createElement(y,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:h,tooltipPayloadSearcher:a.uN,categoricalChartProps:e,ref:t}))},31053:(e,t,r)=>{r.d(t,{L:()=>w});var n=r(94285),a=r(45096),i=r(52104),o=r(3638),l=r(93042),c=r(92735),s=r(41381),u=r(92860),f=r(3065),p=r(38837),d=r(55419),v=r(88346),y=r(19735),h=r(90643);function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var b=(0,n.forwardRef)((e,t)=>{var{children:r,className:a,height:i,onClick:b,onContextMenu:g,onDoubleClick:O,onMouseDown:w,onMouseEnter:P,onMouseLeave:x,onMouseMove:E,onMouseUp:k,onTouchEnd:j,onTouchMove:S,onTouchStart:C,style:A,width:D}=e,M=(0,c.j)(),[N,T]=(0,n.useState)(null),[z,I]=(0,n.useState)(null);(0,u.l3)();var G=(0,p.C)(),B=(0,n.useCallback)(e=>{G(e),"function"==typeof t&&t(e),T(e),I(e)},[G,t,T,I]),R=(0,n.useCallback)(e=>{M((0,s.ky)(e)),M((0,d.y)({handler:b,reactEvent:e}))},[M,b]),L=(0,n.useCallback)(e=>{M((0,s.dj)(e)),M((0,d.y)({handler:P,reactEvent:e}))},[M,P]),F=(0,n.useCallback)(e=>{M((0,l.xS)()),M((0,d.y)({handler:x,reactEvent:e}))},[M,x]),U=(0,n.useCallback)(e=>{M((0,s.dj)(e)),M((0,d.y)({handler:E,reactEvent:e}))},[M,E]),J=(0,n.useCallback)(()=>{M((0,f.Ru)())},[M]),K=(0,n.useCallback)(e=>{M((0,f.uZ)(e.key))},[M]),W=(0,n.useCallback)(e=>{M((0,d.y)({handler:g,reactEvent:e}))},[M,g]),$=(0,n.useCallback)(e=>{M((0,d.y)({handler:O,reactEvent:e}))},[M,O]),V=(0,n.useCallback)(e=>{M((0,d.y)({handler:w,reactEvent:e}))},[M,w]),Z=(0,n.useCallback)(e=>{M((0,d.y)({handler:k,reactEvent:e}))},[M,k]),H=(0,n.useCallback)(e=>{M((0,d.y)({handler:C,reactEvent:e}))},[M,C]),X=(0,n.useCallback)(e=>{M((0,v.e)(e)),M((0,d.y)({handler:S,reactEvent:e}))},[M,S]),_=(0,n.useCallback)(e=>{M((0,d.y)({handler:j,reactEvent:e}))},[M,j]);return n.createElement(y.$.Provider,{value:N},n.createElement(h.t.Provider,{value:z},n.createElement("div",{className:(0,o.$)("recharts-wrapper",a),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:D,height:i},A),onClick:R,onContextMenu:W,onDoubleClick:$,onFocus:J,onKeyDown:K,onMouseDown:V,onMouseEnter:L,onMouseLeave:F,onMouseMove:U,onMouseUp:Z,onTouchEnd:_,onTouchMove:X,onTouchStart:H,ref:B},r)))}),g=r(75019),O=["children","className","width","height","style","compact","title","desc"],w=(0,n.forwardRef)((e,t)=>{var{children:r,className:o,width:l,height:c,style:s,compact:u,title:f,desc:p}=e,d=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,O),v=(0,a.J9)(d,!1);return u?n.createElement(i.a,{otherAttributes:v,title:f,desc:p},r):n.createElement(b,{className:o,style:s,width:l,height:c,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(i.a,{otherAttributes:v,title:f,desc:p,ref:t},n.createElement(g.f,null,r)))})},33438:(e,t,r)=>{r.d(t,{h:()=>O});var n=r(94285),a=r(3638),i=r(77667),o=r(10674),l=r(92735),c=r(56199),s=r(22146),u=r(61587),f=r(72287),p=r(82807),d=["dangerouslySetInnerHTML","ticks"];function v(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function h(e){var t=(0,l.j)();return(0,n.useEffect)(()=>(t((0,o.cU)(e)),()=>{t((0,o.fR)(e))}),[e,t]),null}var m=e=>{var t,{yAxisId:r,className:v,width:h,label:m}=e,b=(0,n.useRef)(null),g=(0,n.useRef)(null),O=(0,l.G)(s.c2),w=(0,u.r)(),P=(0,l.j)(),x="yAxis",E=(0,l.G)(e=>(0,c.iV)(e,x,r,w)),k=(0,l.G)(e=>(0,c.wP)(e,r)),j=(0,l.G)(e=>(0,c.KR)(e,r)),S=(0,l.G)(e=>(0,c.Zi)(e,x,r,w));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==h||!k||(0,p.Z)(m)||(0,n.isValidElement)(m))){var e,t=b.current,a=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:i,tickMargin:l}=t.props,c=(0,f.z)({ticks:a,label:g.current,labelGapWithTick:5,tickSize:i,tickMargin:l});Math.round(k.width)!==Math.round(c)&&P((0,o.QG)({id:r,width:c}))}},[b,null==b||null==(t=b.current)||null==(t=t.tickRefs)?void 0:t.current,null==k?void 0:k.width,k,P,m,r,h]),null==k||null==j)return null;var{dangerouslySetInnerHTML:C,ticks:A}=e,D=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,d);return n.createElement(i.u,y({},D,{ref:b,labelRef:g,scale:E,x:j.x,y:j.y,width:k.width,height:k.height,className:(0,a.$)("recharts-".concat(x," ").concat(x),v),viewBox:O,ticks:S}))},b=e=>{var t,r,a,i,o;return n.createElement(n.Fragment,null,n.createElement(h,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(a=e.angle)?a:0,minTickGap:null!=(i=e.minTickGap)?i:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter}),n.createElement(m,e))},g={allowDataOverflow:c.cd.allowDataOverflow,allowDecimals:c.cd.allowDecimals,allowDuplicatedCategory:c.cd.allowDuplicatedCategory,hide:!1,mirror:c.cd.mirror,orientation:c.cd.orientation,padding:c.cd.padding,reversed:c.cd.reversed,scale:c.cd.scale,tickCount:c.cd.tickCount,type:c.cd.type,width:c.cd.width,yAxisId:0};class O extends n.Component{render(){return n.createElement(b,this.props)}}v(O,"displayName","YAxis"),v(O,"defaultProps",g)},39034:(e,t,r)=>{r.d(t,{d:()=>N});var n=r(94285),a=r(17551),i=r(93833),o=r(45096),l=r(33053),c=r(59774),s=r(77667),u=r(14710),f=r(56199),p=r(92735),d=r(61587),v=r(14901),y=["x1","y1","x2","y2","key"],h=["offset"],m=["xAxisId","yAxisId"],b=["xAxisId","yAxisId"];function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function O(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function P(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}var x=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:a,y:i,width:o,height:l,ry:c}=e;return n.createElement("rect",{x:a,y:i,ry:c,width:o,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function E(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:a,y1:i,x2:l,y2:c,key:s}=t,u=P(t,y),f=(0,o.J9)(u,!1),{offset:p}=f,d=P(f,h);r=n.createElement("line",w({},d,{x1:a,y1:i,x2:l,y2:c,fill:"none",key:s}))}return r}function k(e){var{x:t,width:r,horizontal:a=!0,horizontalPoints:i}=e;if(!a||!i||!i.length)return null;var{xAxisId:o,yAxisId:l}=e,c=P(e,m),s=i.map((e,n)=>E(a,O(O({},c),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},s)}function j(e){var{y:t,height:r,vertical:a=!0,verticalPoints:i}=e;if(!a||!i||!i.length)return null;var{xAxisId:o,yAxisId:l}=e,c=P(e,b),s=i.map((e,n)=>E(a,O(O({},c),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},s)}function S(e){var{horizontalFill:t,fillOpacity:r,x:a,y:i,width:o,height:l,horizontalPoints:c,horizontal:s=!0}=e;if(!s||!t||!t.length)return null;var u=c.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==u[0]&&u.unshift(0);var f=u.map((e,c)=>{var s=u[c+1]?u[c+1]-e:i+l-e;if(s<=0)return null;var f=c%t.length;return n.createElement("rect",{key:"react-".concat(c),y:e,x:a,height:s,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function C(e){var{vertical:t=!0,verticalFill:r,fillOpacity:a,x:i,y:o,width:l,height:c,verticalPoints:s}=e;if(!t||!r||!r.length)return null;var u=s.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==u[0]&&u.unshift(0);var f=u.map((e,t)=>{var s=u[t+1]?u[t+1]-e:i+l-e;if(s<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:s,height:c,stroke:"none",fill:r[f],fillOpacity:a,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var A=(e,t)=>{var{xAxis:r,width:n,height:a,offset:i}=e;return(0,l.PW)((0,c.f)(O(O(O({},s.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:a}})),i.left,i.left+i.width,t)},D=(e,t)=>{var{yAxis:r,width:n,height:a,offset:i}=e;return(0,l.PW)((0,c.f)(O(O(O({},s.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:a}})),i.top,i.top+i.height,t)},M={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function N(e){var t=(0,u.yi)(),r=(0,u.rY)(),o=(0,u.W7)(),l=O(O({},(0,v.e)(e,M)),{},{x:(0,i.Et)(e.x)?e.x:o.left,y:(0,i.Et)(e.y)?e.y:o.top,width:(0,i.Et)(e.width)?e.width:o.width,height:(0,i.Et)(e.height)?e.height:o.height}),{xAxisId:c,yAxisId:s,x:y,y:h,width:m,height:b,syncWithTicks:g,horizontalValues:P,verticalValues:E}=l,N=(0,d.r)(),T=(0,p.G)(e=>(0,f.ZB)(e,"xAxis",c,N)),z=(0,p.G)(e=>(0,f.ZB)(e,"yAxis",s,N));if(!(0,i.Et)(m)||m<=0||!(0,i.Et)(b)||b<=0||!(0,i.Et)(y)||y!==+y||!(0,i.Et)(h)||h!==+h)return null;var I=l.verticalCoordinatesGenerator||A,G=l.horizontalCoordinatesGenerator||D,{horizontalPoints:B,verticalPoints:R}=l;if((!B||!B.length)&&"function"==typeof G){var L=P&&P.length,F=G({yAxis:z?O(O({},z),{},{ticks:L?P:z.ticks}):void 0,width:t,height:r,offset:o},!!L||g);(0,a.R)(Array.isArray(F),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof F,"]")),Array.isArray(F)&&(B=F)}if((!R||!R.length)&&"function"==typeof I){var U=E&&E.length,J=I({xAxis:T?O(O({},T),{},{ticks:U?E:T.ticks}):void 0,width:t,height:r,offset:o},!!U||g);(0,a.R)(Array.isArray(J),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof J,"]")),Array.isArray(J)&&(R=J)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(x,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(S,w({},l,{horizontalPoints:B})),n.createElement(C,w({},l,{verticalPoints:R})),n.createElement(k,w({},l,{offset:o,horizontalPoints:B,xAxis:T,yAxis:z})),n.createElement(j,w({},l,{offset:o,verticalPoints:R,xAxis:T,yAxis:z})))}N.displayName="CartesianGrid"},39529:(e,t,r)=>{r.d(t,{W:()=>g});var n=r(94285),a=r(3638),i=r(77667),o=r(92735),l=r(10674),c=r(56199),s=r(22146),u=r(61587),f=["children"],p=["dangerouslySetInnerHTML","ticks"];function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function y(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function h(e){var t=(0,o.j)(),r=(0,n.useMemo)(()=>{var{children:t}=e;return y(e,f)},[e]),a=(0,o.G)(e=>(0,c.Rl)(e,r.id)),i=r===a;return((0,n.useEffect)(()=>(t((0,l.Vi)(r)),()=>{t((0,l.MC)(r))}),[r,t]),i)?e.children:null}var m=e=>{var{xAxisId:t,className:r}=e,l=(0,o.G)(s.c2),f=(0,u.r)(),d="xAxis",h=(0,o.G)(e=>(0,c.iV)(e,d,t,f)),m=(0,o.G)(e=>(0,c.Zi)(e,d,t,f)),b=(0,o.G)(e=>(0,c.Lw)(e,t)),g=(0,o.G)(e=>(0,c.L$)(e,t));if(null==b||null==g)return null;var{dangerouslySetInnerHTML:O,ticks:w}=e,P=y(e,p);return n.createElement(i.u,v({},P,{scale:h,x:g.x,y:g.y,width:b.width,height:b.height,className:(0,a.$)("recharts-".concat(d," ").concat(d),r),viewBox:l,ticks:m}))},b=e=>{var t,r,a,i,o;return n.createElement(h,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(a=e.angle)?a:0,minTickGap:null!=(i=e.minTickGap)?i:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter},n.createElement(m,e))};class g extends n.Component{render(){return n.createElement(b,this.props)}}d(g,"displayName","XAxis"),d(g,"defaultProps",{allowDataOverflow:c.PU.allowDataOverflow,allowDecimals:c.PU.allowDecimals,allowDuplicatedCategory:c.PU.allowDuplicatedCategory,height:c.PU.height,hide:!1,mirror:c.PU.mirror,orientation:c.PU.orientation,padding:c.PU.padding,reversed:c.PU.reversed,scale:c.PU.scale,tickCount:c.PU.tickCount,type:c.PU.type,xAxisId:0})},47655:(e,t,r)=>{r.d(t,{i:()=>T});var n=r(94285),a=r(72533),i=r.n(a),o=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],l=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),c=(e,t)=>r=>l(o(e,t),r),s=(e,t)=>r=>l([...o(e,t).map((e,t)=>e*t).slice(1),0],r),u=function(){for(var e,t,r,n,a=arguments.length,i=Array(a),o=0;o<a;o++)i[o]=arguments[o];if(1===i.length)switch(i[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=i[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===i.length&&([e,r,t,n]=i);var u=c(e,t),f=c(r,n),p=s(e,t),d=e=>e>1?1:e<0?0:e,v=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var a=u(r)-t,i=p(r);if(1e-4>Math.abs(a-t)||i<1e-4)break;r=d(r-a/i)}return f(r)};return v.isStepper=!1,v},f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,a=(e,a,i)=>{var o=i+(-(e-a)*t-i*r)*n/1e3,l=i*n/1e3+e;return 1e-4>Math.abs(l-a)&&1e-4>Math.abs(o)?[a,0]:[l,o]};return a.isStepper=!0,a.dt=n,a},p=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return u(e);case"spring":return f();default:if("cubic-bezier"===e.split("(")[0])return u(e)}return"function"==typeof e?e:null};function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),h=(e,t,r)=>e.map(e=>"".concat(y(e)," ").concat(t,"ms ").concat(r)).join(","),m=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),b=(e,t)=>Object.keys(t).reduce((r,n)=>v(v({},r),{},{[n]:e(n,t[n])}),{});function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function O(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var w=(e,t,r)=>e+(t-e)*r,P=e=>{var{from:t,to:r}=e;return t!==r},x=(e,t,r)=>{var n=b((t,r)=>{if(P(r)){var[n,a]=e(r.from,r.to,r.velocity);return O(O({},r),{},{from:n,velocity:a})}return r},t);return r<1?b((e,t)=>P(t)?O(O({},t),{},{velocity:w(t.velocity,n[e].velocity,r),from:w(t.from,n[e].from,r)}):t,t):x(e,n,r-1)};let E=(e,t,r,n,a,i)=>{var o=m(e,t);return!0===r.isStepper?function(e,t,r,n,a,i){var o,l=n.reduce((r,n)=>O(O({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),c=()=>b((e,t)=>t.from,l),s=()=>!Object.values(l).filter(P).length,u=null,f=n=>{o||(o=n);var p=(n-o)/r.dt;l=x(r,l,p),a(O(O(O({},e),t),c())),o=n,s()||(u=i.setTimeout(f))};return()=>(u=i.setTimeout(f),()=>{u()})}(e,t,r,o,a,i):function(e,t,r,n,a,i,o){var l,c=null,s=a.reduce((r,n)=>O(O({},r),{},{[n]:[e[n],t[n]]}),{}),u=a=>{l||(l=a);var f=(a-l)/n,p=b((e,t)=>w(...t,r(f)),s);if(i(O(O(O({},e),t),p)),f<1)c=o.setTimeout(u);else{var d=b((e,t)=>w(...t,r(1)),s);i(O(O(O({},e),t),d))}};return()=>(c=o.setTimeout(u),()=>{c()})}(e,t,r,n,o,a,i)};class k{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,a=i=>{i-r>=t?e(i):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(a))};return n=requestAnimationFrame(a),()=>{cancelAnimationFrame(n)}}}var j=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function S(){return(S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function C(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?C(Object(r),!0).forEach(function(t){D(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function D(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class M extends n.PureComponent{constructor(e,t){super(e,t),D(this,"mounted",!1),D(this,"manager",null),D(this,"stopJSAnimation",null),D(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:a,to:i,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:i});return}if(a){if("function"==typeof o){this.state={style:a};return}this.state={style:n?{[n]:a}:a}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:a,to:o,from:l}=this.props,{style:c}=this.state;if(r){if(!t){this.state&&c&&(n&&c[n]!==o||!n&&c!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!i()(e.to,o)||!e.canBegin||!e.isActive){var s=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var u=s||a?l:e.to;this.state&&c&&(n&&c[n]!==u||!n&&c!==u)&&this.setState({style:n?{[n]:u}:u}),this.runAnimation(A(A({},this.props),{},{from:u,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:a,begin:i,onAnimationEnd:o,onAnimationStart:l}=e,c=E(t,r,p(a),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,i,()=>{this.stopJSAnimation=c()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:a,easing:i,onAnimationStart:o,onAnimationEnd:l,children:c}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof i||"function"==typeof c||"spring"===i)return void this.runJSAnimation(e);var s=n?{[n]:a}:a,u=h(Object.keys(s),r,i);this.manager.start([o,t,A(A({},s),{},{transition:u}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:a,attributeName:i,easing:o,isActive:l,from:c,to:s,canBegin:u,onAnimationEnd:f,shouldReAnimate:p,onAnimationReStart:d,animationManager:v}=e,y=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,j),h=n.Children.count(t),m=this.state.style;if("function"==typeof t)return t(m);if(!l||0===h||a<=0)return t;var b=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,A(A({},y),{},{style:A(A({},t),m),className:r}))};return 1===h?b(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>b(e)))}}D(M,"displayName","Animate"),D(M,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var N=(0,n.createContext)(null);function T(e){var t,r,a,i,o,l,c,s,u=(0,n.useContext)(N);return n.createElement(M,S({},e,{animationManager:null!=(c=null!=(s=e.animationManager)?s:u)?c:(t=new k,a=()=>null,i=!1,o=null,l=e=>{if(!i){if(Array.isArray(e)){if(!e.length)return;var[r,...n]=e;if("number"==typeof r){o=t.setTimeout(l.bind(null,n),r);return}l(r),o=t.setTimeout(l.bind(null,n));return}"object"==typeof e&&a(e),"function"==typeof e&&e()}},{stop:()=>{i=!0},start:e=>{i=!1,o&&(o(),o=null),l(e)},subscribe:e=>(a=e,()=>{a=()=>null}),getTimeoutController:()=>t})}))}},59774:(e,t,r)=>{r.d(t,{f:()=>u});var n=r(93833),a=r(44789),i=r(26471),o=r(67434),l=r(95324);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e,t,r){var c,{tick:u,ticks:f,viewBox:p,minTickGap:d,orientation:v,interval:y,tickFormatter:h,unit:m,angle:b}=e;if(!f||!f.length||!u)return[];if((0,n.Et)(y)||i.m.isSsr)return null!=(c=(0,o.pB)(f,(0,n.Et)(y)?y:0))?c:[];var g=[],O="top"===v||"bottom"===v?"width":"height",w=m&&"width"===O?(0,a.P)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},P=(e,n)=>{var i="function"==typeof h?h(e.value,n):e.value;return"width"===O?(0,o.HX)((0,a.P)(i,{fontSize:t,letterSpacing:r}),w,b):(0,a.P)(i,{fontSize:t,letterSpacing:r})[O]},x=f.length>=2?(0,n.sA)(f[1].coordinate-f[0].coordinate):1,E=(0,o.y)(p,x,O);return"equidistantPreserveStart"===y?function(e,t,r,n,a){for(var i,c=(n||[]).slice(),{start:s,end:u}=t,f=0,p=1,d=s;p<=c.length;)if(i=function(){var t,i=null==n?void 0:n[f];if(void 0===i)return{v:(0,l.B)(n,p)};var c=f,v=()=>(void 0===t&&(t=r(i,c)),t),y=i.coordinate,h=0===f||(0,o.zN)(e,y,v,d,u);h||(f=0,d=s,p+=1),h&&(d=y+e*(v()/2+a),f+=p)}())return i.v;return[]}(x,E,P,f,d):("preserveStart"===y||"preserveStartEnd"===y?function(e,t,r,n,a,i){var l=(n||[]).slice(),c=l.length,{start:u,end:f}=t;if(i){var p=n[c-1],d=r(p,c-1),v=e*(p.coordinate+e*d/2-f);l[c-1]=p=s(s({},p),{},{tickCoord:v>0?p.coordinate-v*e:p.coordinate}),(0,o.zN)(e,p.tickCoord,()=>d,u,f)&&(f=p.tickCoord-e*(d/2+a),l[c-1]=s(s({},p),{},{isShow:!0}))}for(var y=i?c-1:c,h=function(t){var n,i=l[t],c=()=>(void 0===n&&(n=r(i,t)),n);if(0===t){var p=e*(i.coordinate-e*c()/2-u);l[t]=i=s(s({},i),{},{tickCoord:p<0?i.coordinate-p*e:i.coordinate})}else l[t]=i=s(s({},i),{},{tickCoord:i.coordinate});(0,o.zN)(e,i.tickCoord,c,u,f)&&(u=i.tickCoord+e*(c()/2+a),l[t]=s(s({},i),{},{isShow:!0}))},m=0;m<y;m++)h(m);return l}(x,E,P,f,d,"preserveStartEnd"===y):function(e,t,r,n,a){for(var i=(n||[]).slice(),l=i.length,{start:c}=t,{end:u}=t,f=function(t){var n,f=i[t],p=()=>(void 0===n&&(n=r(f,t)),n);if(t===l-1){var d=e*(f.coordinate+e*p()/2-u);i[t]=f=s(s({},f),{},{tickCoord:d>0?f.coordinate-d*e:f.coordinate})}else i[t]=f=s(s({},f),{},{tickCoord:f.coordinate});(0,o.zN)(e,f.tickCoord,p,c,u)&&(u=f.tickCoord-e*(p()/2+a),i[t]=s(s({},f),{},{isShow:!0}))},p=l-1;p>=0;p--)f(p);return i}(x,E,P,f,d)).filter(e=>e.isShow)}},76282:(e,t,r)=>{r.d(t,{r:()=>g});var n=r(94285),a=r(17649),i=r(96575),o=r(39075),l=r(65392),c=r(73705),s=r(88061),u=r(31053),f=r(14901),p=r(6944),d=["width","height","layout"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var y={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},h=(0,n.forwardRef)(function(e,t){var r,a=(0,f.e)(e.categoricalChartProps,y),{width:h,height:m,layout:b}=a,g=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(a,d);if(!(0,p.F)(h)||!(0,p.F)(m))return null;var{chartName:O,defaultTooltipEventType:w,validateTooltipEventTypes:P,tooltipPayloadSearcher:x}=e;return n.createElement(i.J,{preloadedState:{options:{chartName:O,defaultTooltipEventType:w,validateTooltipEventTypes:P,tooltipPayloadSearcher:x,eventEmitter:void 0}},reduxStoreName:null!=(r=a.id)?r:O},n.createElement(o.TK,{chartData:a.data}),n.createElement(l.s,{width:h,height:m,layout:b,margin:a.margin}),n.createElement(c.p,{accessibilityLayer:a.accessibilityLayer,barCategoryGap:a.barCategoryGap,maxBarSize:a.maxBarSize,stackOffset:a.stackOffset,barGap:a.barGap,barSize:a.barSize,syncId:a.syncId,syncMethod:a.syncMethod,className:a.className}),n.createElement(s.P,{cx:a.cx,cy:a.cy,startAngle:a.startAngle,endAngle:a.endAngle,innerRadius:a.innerRadius,outerRadius:a.outerRadius}),n.createElement(u.L,v({width:h,height:m},g,{ref:t})))}),m=["item"],b={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},g=(0,n.forwardRef)((e,t)=>{var r=(0,f.e)(e,b);return n.createElement(h,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:m,tooltipPayloadSearcher:a.uN,categoricalChartProps:r,ref:t})})},77667:(e,t,r)=>{r.d(t,{u:()=>P});var n=r(94285),a=r(56797),i=r.n(a),o=r(3638),l=r(55148),c=r(39712),s=r(4736),u=r(82807),f=r(93833),p=r(83733),d=r(45096),v=r(59774),y=["viewBox"],h=["viewBox"];function m(){return(m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function O(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function w(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class P extends n.Component{constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(e,t){var{viewBox:r}=e,n=O(e,y),a=this.props,{viewBox:i}=a,o=O(a,h);return!(0,l.b)(r,i)||!(0,l.b)(n,o)||!(0,l.b)(t,this.state)}getTickLineCoord(e){var t,r,n,a,i,o,{x:l,y:c,width:s,height:u,orientation:p,tickSize:d,mirror:v,tickMargin:y}=this.props,h=v?-1:1,m=e.tickSize||d,b=(0,f.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(p){case"top":t=r=e.coordinate,o=(n=(a=c+!v*u)-h*m)-h*y,i=b;break;case"left":n=a=e.coordinate,i=(t=(r=l+!v*s)-h*m)-h*y,o=b;break;case"right":n=a=e.coordinate,i=(t=(r=l+v*s)+h*m)+h*y,o=b;break;default:t=r=e.coordinate,o=(n=(a=c+v*u)+h*m)+h*y,i=b}return{line:{x1:t,y1:n,x2:r,y2:a},tick:{x:i,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:a,orientation:l,mirror:c,axisLine:s}=this.props,u=g(g(g({},(0,d.J9)(this.props,!1)),(0,d.J9)(s,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var f=+("top"===l&&!c||"bottom"===l&&c);u=g(g({},u),{},{x1:e,y1:t+f*a,x2:e+r,y2:t+f*a})}else{var p=+("left"===l&&!c||"right"===l&&c);u=g(g({},u),{},{x1:e+p*r,y1:t,x2:e+p*r,y2:t+a})}return n.createElement("line",m({},u,{className:(0,o.$)("recharts-cartesian-axis-line",i()(s,"className"))}))}static renderTickItem(e,t,r){var a,i=(0,o.$)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))a=n.cloneElement(e,g(g({},t),{},{className:i}));else if("function"==typeof e)a=e(g(g({},t),{},{className:i}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=(0,o.$)(l,e.className)),a=n.createElement(s.E,m({},t,{className:l}),r)}return a}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:a,stroke:l,tick:s,tickFormatter:u,unit:f}=this.props,y=(0,v.f)(g(g({},this.props),{},{ticks:r}),e,t),h=this.getTickTextAnchor(),b=this.getTickVerticalAnchor(),O=(0,d.J9)(this.props,!1),w=(0,d.J9)(s,!1),x=g(g({},O),{},{fill:"none"},(0,d.J9)(a,!1)),E=y.map((e,t)=>{var{line:r,tick:d}=this.getTickLineCoord(e),v=g(g(g(g({textAnchor:h,verticalAnchor:b},O),{},{stroke:"none",fill:l},w),d),{},{index:t,payload:e,visibleTicksCount:y.length,tickFormatter:u});return n.createElement(c.W,m({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,p.XC)(this.props,e,t)),a&&n.createElement("line",m({},x,r,{className:(0,o.$)("recharts-cartesian-axis-tick-line",i()(a,"className"))})),s&&P.renderTickItem(s,v,"".concat("function"==typeof u?u(e.value,t):e.value).concat(f||"")))});return E.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},E):null}render(){var{axisLine:e,width:t,height:r,className:a,hide:i}=this.props;if(i)return null;var{ticks:l}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(c.W,{className:(0,o.$)("recharts-cartesian-axis",a),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,a=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||a!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),u.J.renderCallByParent(this.props))}}w(P,"displayName","CartesianAxis"),w(P,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})}}]);