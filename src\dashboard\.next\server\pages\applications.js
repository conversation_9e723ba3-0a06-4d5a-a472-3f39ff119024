"use strict";(()=>{var e={};e.id=3925,e.ids=[636,3220,3925],e.modules={4722:e=>{e.exports=require("next-auth/react")},5459:(e,t,r)=>{r.d(t,{S8s:()=>o.FiInfo});var o=r(64960)},8732:e=>{e.exports=require("react/jsx-runtime")},14078:e=>{e.exports=import("swr")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},22326:e=>{e.exports=require("react-dom")},24689:(e,t,r)=>{r.d(t,{Y:()=>o});let o={log:(e,t,r)=>{},error:(e,t,r)=>{}}},26102:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{config:()=>m,default:()=>p,getServerSideProps:()=>x,getStaticPaths:()=>u,getStaticProps:()=>h,reportWebVitals:()=>g,routeModule:()=>w,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>b});var i=r(1292),s=r(58834),a=r(40786),n=r(83567),l=r(8077),d=r(99538),c=e([l,d]);[l,d]=c.then?(await c)():c;let p=(0,a.M)(d,"default"),h=(0,a.M)(d,"getStaticProps"),u=(0,a.M)(d,"getStaticPaths"),x=(0,a.M)(d,"getServerSideProps"),m=(0,a.M)(d,"config"),g=(0,a.M)(d,"reportWebVitals"),b=(0,a.M)(d,"unstable_getStaticProps"),j=(0,a.M)(d,"unstable_getStaticPaths"),y=(0,a.M)(d,"unstable_getStaticParams"),S=(0,a.M)(d,"unstable_getServerProps"),f=(0,a.M)(d,"unstable_getServerSideProps"),w=new i.PagesRouteModule({definition:{kind:s.A.PAGES,page:"/applications",pathname:"/applications",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:d});o()}catch(e){o(e)}})},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},30067:e=>{e.exports=import("@emotion/styled")},33873:e=>{e.exports=require("path")},39911:(e,t,r)=>{r.d(t,{A7C:()=>o.A7C,JwJ:()=>o.JwJ,K9h:()=>o.K9h,NPF:()=>o.NPF,_Hm:()=>o._Hm,kkc:()=>o.kkc,y8Q:()=>o.y8Q});var o=r(48648)},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},65542:e=>{e.exports=require("next-auth")},68583:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.d(t,{Alert:()=>i.F,AlertIcon:()=>s._,Badge:()=>a.E,Box:()=>n.a,Button:()=>l.$,Card:()=>d.Z,CardBody:()=>c.b,Container:()=>p.m,FormControl:()=>h.MJ,FormLabel:()=>u.l,HStack:()=>x.z,Heading:()=>m.D,Icon:()=>g.I,Modal:()=>b.aF,ModalBody:()=>j.c,ModalCloseButton:()=>y.s,ModalContent:()=>S.$,ModalFooter:()=>f.j,ModalHeader:()=>w.r,ModalOverlay:()=>v.m,Progress:()=>A.k,SimpleGrid:()=>k.r,Spinner:()=>C.y,Tab:()=>T.o,TabList:()=>z.w,TabPanel:()=>P.K,TabPanels:()=>q.T,Tabs:()=>B.t,Text:()=>H.E,Textarea:()=>I.T,VStack:()=>M.T,useDisclosure:()=>F.j,useToast:()=>W.d});var i=r(5128),s=r(31772),a=r(25392),n=r(45200),l=r(77502),d=r(90846),c=r(60615),p=r(64304),h=r(23678),u=r(63957),x=r(55197),m=r(30519),g=r(50792),b=r(75460),j=r(42929),y=r(7394),S=r(89164),f=r(87346),w=r(95148),v=r(12725),A=r(97040),k=r(67981),C=r(90088),T=r(8399),z=r(81248),P=r(46596),q=r(92279),B=r(64450),H=r(87378),I=r(37506),M=r(17335),W=r(5978),F=r(66646);r(9436),r(25035);var R=e([i,s,a,n,l,d,c,p,h,u,x,m,g,b,j,y,S,f,w,v,A,k,C,T,z,P,q,B,H,I,M,W]);[i,s,a,n,l,d,c,p,h,u,x,m,g,b,j,y,S,f,w,v,A,k,C,T,z,P,q,B,H,I,M,W]=R.then?(await R)():R,o()}catch(e){o(e)}})},72115:e=>{e.exports=require("yaml")},74075:e=>{e.exports=require("zlib")},82015:e=>{e.exports=require("react")},88455:e=>{e.exports=import("@emotion/react")},92086:(e,t,r)=>{r.d(t,{p:()=>o});let o=[{id:"moderator",title:"Moderator Application",description:"Join our moderation team to help maintain a safe and friendly community environment.",icon:"FiShield",color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"},isOpen:!0,requiresApproval:!0,questions:[{id:"motivation",text:"Why do you want to be a moderator?",type:"text",required:!0},{id:"experience",text:"What experience do you have with community moderation?",type:"text",required:!0},{id:"scenario1",text:"How would you handle a difficult situation with a user?",type:"radio",required:!0,options:["Immediately ban them without warning","Give them a warning and explain why their behavior is inappropriate","Ignore it and let other moderators handle it","Timeout the user and delete the messages"],correctAnswer:1}],requirements:{minAge:18,minAccountAge:30,timezone:!0,availability:!0},metadata:{totalApplications:0,acceptanceRate:0,averageResponseTime:"48 hours"},quiz:[{question:"A user is spamming offensive content in multiple channels. What's your first action?",options:["Immediately ban the user","Warn them and explain why their behavior is inappropriate","Temporarily mute them and remove the offensive content","Ignore it and let other moderators handle it"],correctAnswer:2},{question:"Two users are having a heated argument that's disrupting chat. How do you handle it?",options:["Mute both users immediately","Take the discussion to a private channel and mediate","Tell them to stop or face consequences","Move their conversation to an appropriate channel and remind them of the rules"],correctAnswer:3},{question:"You notice a user sharing what might be personal information. What do you do?",options:["Delete the message and send them a warning","Temporarily mute them without explanation","Remove the message and privately explain why sharing personal info is dangerous","Publicly call them out to set an example"],correctAnswer:2},{question:"A user reports a bug in the bot. What's your response?",options:["Tell them to check if it's already reported","Document the issue and escalate to the development team","Ignore it since you're not a developer","Tell them to fix it themselves"],correctAnswer:1},{question:"You discover another moderator abusing their powers. What do you do?",options:["Confront them publicly","Remove their permissions immediately","Document the abuse and report it to senior staff privately","Ignore it to avoid conflict"],correctAnswer:2},{question:"A user is repeatedly asking for moderator roles. How do you respond?",options:["Ban them for being annoying","Direct them to the application process and explain the requirements","Give them a trial moderator role","Tell them to stop asking"],correctAnswer:1},{question:"You notice suspicious bot activity in the server. What's your first step?",options:["Shut down all bots immediately","Alert senior staff and monitor the situation","Ignore it since it's probably nothing","Delete all bot messages"],correctAnswer:1},{question:"A user claims they were wrongly banned. How do you proceed?",options:["Unban them immediately","Review the ban logs and discuss with the mod team","Tell them bans are final","Direct them to create a new account"],correctAnswer:1},{question:"Multiple users are using excessive caps and emojis. What do you do?",options:["Mute everyone involved","Delete all their messages","Send a friendly reminder about chat etiquette","Add a slowmode to the channel temporarily"],correctAnswer:2},{question:"You notice a potential raid beginning. What's your immediate action?",options:["Ban all new accounts immediately","Enable server lockdown and alert other moderators","Warn users in chat about the raid","Try to talk to the raiders"],correctAnswer:1}]},{id:"support",title:"Support Team Application",description:"Help other members with technical issues and general support inquiries.",icon:"FiHelpCircle",color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"},isOpen:!0,requiresApproval:!0,questions:[{id:"tech_experience",text:"What is your experience with technical support?",type:"text",required:!0},{id:"availability",text:"How many hours per week can you dedicate to support?",type:"select",required:!0,options:["5-10 hours","10-20 hours","20+ hours"]}],requirements:{minAge:16,timezone:!0,availability:!0},metadata:{totalApplications:0,acceptanceRate:0,averageResponseTime:"24 hours"}},{id:"developer",title:"Developer Team Application",description:"Join our development team to help improve and maintain our bot and systems.",icon:"FiCode",color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},isOpen:!1,requiresApproval:!0,questions:[{id:"programming_exp",text:"What programming languages are you proficient in?",type:"checkbox",required:!0,options:["JavaScript/TypeScript","Python","Java","C++","Other"]},{id:"github",text:"Please provide your GitHub profile URL",type:"text",required:!0}],requirements:{minAccountAge:60,availability:!0},metadata:{totalApplications:0,acceptanceRate:0,averageResponseTime:"72 hours",nextReviewDate:"2024-04-01"}}]},99538:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{default:()=>j,getServerSideProps:()=>v});var i=r(8732),s=r(68583),a=r(81011),n=r(15806),l=r(92546),d=r(82015),c=r(4722),p=r(5459),h=r(39911),u=r(88358),x=r(92086);r(24689);var m=r(98364),g=r.n(m),b=e([s,a]);[s,a]=b.then?(await b)():b;let y=g()(()=>Promise.all([r.e(2457),r.e(9784),r.e(6021),r.e(3786),r.e(8740),r.e(9498),r.e(2142),r.e(1283),r.e(9231)]).then(r.bind(r,69231)).then(e=>({default:e.ApplicationCard})),{loadableGenerated:{modules:["pages\\applications.tsx -> ../components/ApplicationCard"]},loading:()=>(0,i.jsx)(s.Spinner,{size:"lg"}),ssr:!1}),S=g()(()=>r.e(313).then(r.bind(r,50313)),{loadableGenerated:{modules:["pages\\applications.tsx -> ../components/ScenarioForm"]},loading:()=>(0,i.jsx)(s.Spinner,{size:"md"}),ssr:!1}),f=g()(()=>r.e(2315).then(r.bind(r,12315)),{loadableGenerated:{modules:["pages\\applications.tsx -> ../components/ApplicationForm"]},loading:()=>(0,i.jsx)(s.Spinner,{size:"md"}),ssr:!1}),w=["Join our team and make a difference!","Great moderators are the backbone of great communities.","Help us keep the community safe and friendly.","Be the change you want to see in the community.","Together we can build something amazing!"];function j({ownerIds:e}){let{data:t,status:r}=(0,c.useSession)(),o=(0,u.useRouter)(),n=(0,s.useToast)(),l=t?.user?.isAdmin,[m,g]=(0,d.useState)({age:"",hoursPerWeek:"",timezone:"",motivation:"",scenarioResponses:{}}),[b,j]=(0,d.useState)([]),[v,A]=(0,d.useState)([]),[k,C]=(0,d.useState)(!1),[T,z]=(0,d.useState)(!0),[P,q]=(0,d.useState)(!1),{isOpen:B,onOpen:H,onClose:I}=(0,s.useDisclosure)(),[M,W]=(0,d.useState)(0),[F,R]=(0,d.useState)(!1),[_,D]=(0,d.useState)(!1),[J,O]=(0,d.useState)(!1),[G,V]=(0,d.useState)(null),[$,E]=(0,d.useState)({types:x.p,userSubmissions:[],loading:!0,error:null}),[N,Y]=(0,d.useState)(null),L=e.includes(t?.user?.id||""),Q=e=>{Y(e),W(0),H()},K=e=>$.userSubmissions.some(t=>t.applicationTypeId===e),U=(0,d.useMemo)(()=>""!==m.age.trim()&&""!==m.hoursPerWeek.trim()&&""!==m.timezone.trim()&&""!==m.motivation.trim(),[m]),X=(0,d.useMemo)(()=>Object.keys(m.scenarioResponses||{}).length>=15,[m.scenarioResponses]),Z=w[new Date().getDate()%w.length];(0,d.useCallback)(e=>{g(t=>({...t,...e}))},[]);let ee=(0,d.useCallback)(e=>{g(t=>({...t,...e}))},[]),et=(0,d.useCallback)(e=>{g(t=>({...t,scenarioResponses:e}))},[]),er=async()=>{if(!t?.user?.id)return void n({title:"Error",description:"You must be logged in to apply.",status:"error",duration:5e3});if(!m.age||!m.hoursPerWeek||!m.timezone||!m.motivation)return void n({title:"Missing Information",description:"Please fill in all required fields before submitting.",status:"error",duration:5e3});O(!0);try{let e=await fetch("/api/applications/moderation",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t.user.id,age:parseInt(m.age),hoursPerWeek:parseInt(m.hoursPerWeek),timezone:m.timezone,motivation:m.motivation,scenarioResponses:m.scenarioResponses})});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to submit application")}n({title:"Application submitted",description:"Your application has been submitted successfully.",status:"success",duration:5e3}),o.push("/")}catch(e){n({title:"Error",description:e instanceof Error?e.message:"Failed to submit application",status:"error",duration:5e3})}finally{O(!1)}},eo=async(e,t)=>{try{if(!(await fetch("/api/applications/moderation",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({applicationId:e,status:t})})).ok)throw Error("Failed to update application status");let r=await fetch("/api/applications/moderation"),o=await r.json();A(o),n({title:"Status updated",description:`Application has been ${t}`,status:"success",duration:3e3})}catch(e){n({title:"Failed to update status",description:e.message,status:"error",duration:5e3})}},[ei,es]=(0,d.useState)({score:0,total:0,completed:!1});return"loading"!==r&&G?t?G.open?(0,i.jsx)(a.A,{children:(0,i.jsxs)(s.Container,{maxW:"container.xl",py:8,children:[(0,i.jsxs)(s.Box,{maxW:"4xl",mx:"auto",mb:8,mt:8,bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"purple.400",boxShadow:"0 0 15px rgba(159, 122, 234, 0.4)",textAlign:"center",children:[(0,i.jsx)(s.Heading,{size:"2xl",bgGradient:"linear(to-r, purple.300, blue.400)",bgClip:"text",mb:4,children:"Join Our Team"}),(0,i.jsx)(s.Text,{color:"gray.300",fontSize:"lg",mb:6,children:"Explore available positions and become part of our community staff"}),(0,i.jsxs)(s.Box,{position:"relative",bg:"gray.900",p:6,rounded:"lg",border:"1px",borderColor:"whiteAlpha.200",children:[(0,i.jsx)(s.Icon,{as:h.JwJ,color:"purple.300",boxSize:6,position:"absolute",top:-3,left:-3}),(0,i.jsx)(s.Icon,{as:h.K9h,color:"purple.300",boxSize:6,position:"absolute",bottom:-3,right:-3}),(0,i.jsxs)(s.HStack,{spacing:3,justify:"center",children:[(0,i.jsx)(s.Icon,{as:h.y8Q,color:"purple.300",boxSize:6}),(0,i.jsx)(s.Text,{fontStyle:"italic",color:"purple.200",fontSize:"md",children:Z})]})]})]}),l&&(0,i.jsxs)(s.Box,{maxW:"4xl",mx:"auto",mb:8,children:[(0,i.jsxs)(s.SimpleGrid,{columns:{base:1,md:3},spacing:6,children:[(0,i.jsx)(s.Card,{bg:"rgba(255,255,255,0.08)",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",children:(0,i.jsx)(s.CardBody,{children:(0,i.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,i.jsxs)(s.HStack,{justify:"space-between",children:[(0,i.jsx)(s.Heading,{size:"md",children:"Pending"}),(0,i.jsx)(s.Badge,{colorScheme:"yellow",fontSize:"md",children:Array.isArray(v)?v.filter(e=>"pending"===e.status).length:0})]}),(0,i.jsx)(s.Icon,{as:h.NPF,boxSize:8,color:"yellow.400"})]})})}),(0,i.jsx)(s.Card,{bg:"rgba(255,255,255,0.08)",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",children:(0,i.jsx)(s.CardBody,{children:(0,i.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,i.jsxs)(s.HStack,{justify:"space-between",children:[(0,i.jsx)(s.Heading,{size:"md",children:"Approved"}),(0,i.jsx)(s.Badge,{colorScheme:"green",fontSize:"md",children:Array.isArray(v)?v.filter(e=>"approved"===e.status).length:0})]}),(0,i.jsx)(s.Icon,{as:h.A7C,boxSize:8,color:"green.400"})]})})}),(0,i.jsx)(s.Card,{bg:"rgba(255,255,255,0.08)",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",children:(0,i.jsx)(s.CardBody,{children:(0,i.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,i.jsxs)(s.HStack,{justify:"space-between",children:[(0,i.jsx)(s.Heading,{size:"md",children:"Rejected"}),(0,i.jsx)(s.Badge,{colorScheme:"red",fontSize:"md",children:Array.isArray(v)?v.filter(e=>"rejected"===e.status).length:0})]}),(0,i.jsx)(s.Icon,{as:h._Hm,boxSize:8,color:"red.400"})]})})})]}),(0,i.jsx)(s.Button,{mt:6,size:"lg",w:"full",colorScheme:P?"red":"blue",onClick:()=>q(!P),leftIcon:(0,i.jsx)(s.Icon,{as:h.kkc}),children:P?"Hide Submissions":"View All Submissions"})]}),!l&&(0,i.jsxs)(s.Box,{maxW:"6xl",mx:"auto",mb:12,children:[(0,i.jsx)(s.Heading,{size:"lg",mb:6,textAlign:"center",color:"white",children:"Available Applications"}),(0,i.jsx)(s.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:6,children:$.types.filter(e=>"support"!==e.id&&("developer"!==e.id||!!L)).map(e=>(0,i.jsx)(d.Suspense,{fallback:(0,i.jsx)(s.Spinner,{}),children:(0,i.jsx)(y,{application:e,onApply:Q,hasApplied:K(e.id)},e.id)},e.id))})]}),N&&(0,i.jsxs)(s.Modal,{isOpen:B,onClose:()=>{I(),Y(null)},size:"6xl",scrollBehavior:"inside",isCentered:!0,children:[(0,i.jsx)(s.ModalOverlay,{bg:"blackAlpha.700",backdropFilter:"blur(10px)"}),(0,i.jsxs)(s.ModalContent,{bg:"gray.800",minH:"80vh",maxH:"90vh",borderRadius:"xl",border:"1px solid",borderColor:"whiteAlpha.200",boxShadow:"2xl",children:[(0,i.jsx)(s.ModalHeader,{borderBottom:"1px solid",borderColor:"whiteAlpha.200",py:6,px:8,children:(0,i.jsxs)(s.HStack,{justify:"space-between",align:"center",children:[(0,i.jsxs)(s.VStack,{align:"start",spacing:2,children:[(0,i.jsx)(s.Heading,{size:"lg",children:N.title}),(0,i.jsx)(s.Text,{color:"gray.400",fontSize:"md",children:N.description})]}),(0,i.jsxs)(s.HStack,{spacing:4,children:[(0,i.jsxs)(s.Badge,{colorScheme:N.color,fontSize:"md",px:3,py:1,borderRadius:"full",children:["Step ",M+1," of 3"]}),(0,i.jsx)(s.ModalCloseButton,{position:"static",size:"lg",_hover:{bg:"whiteAlpha.200"}})]})]})}),(0,i.jsx)(s.ModalBody,{p:0,children:(0,i.jsxs)(s.Box,{position:"relative",children:[(0,i.jsx)(s.Progress,{value:(M+1)*(100/("experimental-features"===N.id?2:3)),size:"xs",colorScheme:N.color,bg:"whiteAlpha.100",sx:{"& > div":{transition:"all 0.3s ease-in-out"}}}),(0,i.jsx)(s.Box,{p:8,children:(0,i.jsxs)(s.Tabs,{index:M,onChange:e=>{(1!==e||U)&&(2!==e||X)&&W(e)},variant:"unstyled",isLazy:!0,children:[(0,i.jsx)(s.TabList,{mb:6,children:(0,i.jsxs)(s.HStack,{spacing:4,width:"full",children:[(0,i.jsx)(s.Tab,{flex:1,py:3,_selected:{color:"white",bg:`${N.color}.500`,boxShadow:"lg"},bg:"whiteAlpha.50",borderRadius:"lg",fontWeight:"semibold",transition:"all 0.2s",_hover:{bg:0===M?`${N.color}.500`:"whiteAlpha.100"},children:(0,i.jsxs)(s.HStack,{children:[(0,i.jsx)(s.Icon,{as:h.kkc}),(0,i.jsx)(s.Text,{children:"Personal Information"})]})}),(0,i.jsx)(s.Tab,{flex:1,py:3,isDisabled:!U,_selected:{color:"white",bg:`${N.color}.500`,boxShadow:"lg"},bg:"whiteAlpha.50",borderRadius:"lg",fontWeight:"semibold",transition:"all 0.2s",_hover:{bg:1===M?`${N.color}.500`:"whiteAlpha.100"},children:(0,i.jsxs)(s.HStack,{children:[(0,i.jsx)(s.Icon,{as:h.NPF}),(0,i.jsx)(s.Text,{children:"moderator"===N.id?"Moderation Scenarios":"Application Questions"})]})}),(0,i.jsx)(s.Tab,{flex:1,py:3,isDisabled:!X,_selected:{color:"white",bg:`${N.color}.500`,boxShadow:"lg"},bg:"whiteAlpha.50",borderRadius:"lg",fontWeight:"semibold",transition:"all 0.2s",_hover:{bg:2===M?`${N.color}.500`:"whiteAlpha.100"},children:(0,i.jsxs)(s.HStack,{children:[(0,i.jsx)(s.Icon,{as:p.S8s}),(0,i.jsx)(s.Text,{children:"Additional Information"})]})})]})}),(0,i.jsxs)(s.TabPanels,{children:[(0,i.jsx)(s.TabPanel,{p:0,children:(0,i.jsx)(d.Suspense,{fallback:(0,i.jsx)(s.Spinner,{}),children:(0,i.jsx)(f,{session:t,onFormChange:ee,initialData:m})})}),(0,i.jsx)(s.TabPanel,{p:0,children:"moderator"===N.id?(0,i.jsx)(d.Suspense,{fallback:(0,i.jsx)(s.Spinner,{}),children:(0,i.jsx)(S,{onFormChange:et,initialData:m.scenarioResponses})}):(0,i.jsx)(s.VStack,{spacing:4,align:"stretch",children:N.questions?.map(e=>(0,i.jsxs)(s.FormControl,{isRequired:e.required,children:[(0,i.jsx)(s.FormLabel,{color:"white",children:e.text}),"text"===e.type&&(0,i.jsx)(s.Textarea,{placeholder:"Your answer...",value:m[e.id]||"",onChange:t=>ee(e.id,t.target.value),bg:"gray.700",border:"1px solid",borderColor:"whiteAlpha.300"})]},e.id))})}),(0,i.jsx)(s.TabPanel,{p:0,children:(0,i.jsx)(s.Text,{children:"Additional information will be shown here."})})]})]})})]})}),(0,i.jsx)(s.ModalFooter,{borderTop:"1px solid",borderColor:"whiteAlpha.200",py:6,px:8,children:(0,i.jsxs)(s.HStack,{spacing:4,width:"full",justify:"space-between",children:[(0,i.jsxs)(s.HStack,{spacing:4,children:[(0,i.jsx)(s.Icon,{as:h.y8Q,color:`${N.color}.300`,boxSize:6}),(0,i.jsx)(s.Text,{color:"gray.400",fontSize:"sm",children:"Your responses will be reviewed by our moderation team"})]}),(0,i.jsxs)(s.HStack,{spacing:4,children:[M>0&&(0,i.jsx)(s.Button,{onClick:()=>W(M-1),leftIcon:(0,i.jsx)(s.Icon,{as:h.kkc}),variant:"ghost",size:"lg",_hover:{bg:"whiteAlpha.100"},children:"Previous"}),0===M&&(0,i.jsx)(s.Button,{onClick:()=>W(1),rightIcon:(0,i.jsx)(s.Icon,{as:h.kkc}),colorScheme:`${N.color}.500`,size:"lg",isDisabled:!U,children:"Continue"}),1===M&&(0,i.jsx)(s.Button,{onClick:()=>W(2),rightIcon:(0,i.jsx)(s.Icon,{as:h.kkc}),colorScheme:`${N.color}.500`,size:"lg",isDisabled:!X,children:"Continue"}),2===M&&(0,i.jsx)(s.Button,{onClick:er,leftIcon:(0,i.jsx)(s.Icon,{as:h.A7C}),colorScheme:"green",size:"lg",isLoading:F,loadingText:"Submitting...",children:"Submit Application"})]})]})})]})]}),P&&(0,i.jsxs)(s.Modal,{isOpen:P,onClose:()=>q(!1),size:"4xl",scrollBehavior:"inside",children:[(0,i.jsx)(s.ModalOverlay,{backdropFilter:"blur(10px)"}),(0,i.jsxs)(s.ModalContent,{bg:"gray.800",border:"1px solid",borderColor:"whiteAlpha.300",children:[(0,i.jsx)(s.ModalHeader,{children:(0,i.jsxs)(s.HStack,{justify:"space-between",align:"center",children:[(0,i.jsx)(s.Heading,{size:"lg",children:"Application Submissions"}),(0,i.jsxs)(s.HStack,{spacing:4,children:[(0,i.jsxs)(s.Badge,{colorScheme:"yellow",fontSize:"md",p:2,children:[Array.isArray(v)?v.filter(e=>"pending"===e.status).length:0," Pending"]}),(0,i.jsxs)(s.Badge,{colorScheme:"green",fontSize:"md",p:2,children:[Array.isArray(v)?v.filter(e=>"approved"===e.status).length:0," Approved"]}),(0,i.jsxs)(s.Badge,{colorScheme:"red",fontSize:"md",p:2,children:[Array.isArray(v)?v.filter(e=>"rejected"===e.status).length:0," Rejected"]})]})]})}),(0,i.jsx)(s.ModalCloseButton,{}),(0,i.jsx)(s.ModalBody,{children:(0,i.jsx)(s.VStack,{spacing:4,align:"stretch",children:Array.isArray(v)?v.map(e=>(0,i.jsx)(s.Card,{bg:"rgba(255,255,255,0.08)",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"approved"===e.status?"green.400":"rejected"===e.status?"red.400":"yellow.400",rounded:"lg",overflow:"hidden",children:(0,i.jsx)(s.CardBody,{children:(0,i.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,i.jsxs)(s.HStack,{justify:"space-between",children:[(0,i.jsxs)(s.VStack,{align:"start",spacing:1,children:[(0,i.jsx)(s.Heading,{size:"md",children:e.username||"Anonymous"}),(0,i.jsxs)(s.Text,{fontSize:"sm",color:"gray.400",children:["Applied ",new Date(e.date).toLocaleDateString()]})]}),(0,i.jsx)(s.Badge,{colorScheme:"approved"===e.status?"green":"rejected"===e.status?"red":"yellow",fontSize:"md",p:2,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,i.jsxs)(s.Box,{bg:"gray.900",p:4,rounded:"md",children:[(0,i.jsx)(s.Text,{fontWeight:"bold",mb:2,children:"Why do you want to be a moderator?"}),(0,i.jsx)(s.Text,{color:"gray.300",children:e.motivation||"No response"})]}),(0,i.jsxs)(s.SimpleGrid,{columns:{base:1,md:2},spacing:4,children:[(0,i.jsxs)(s.Box,{children:[(0,i.jsx)(s.Text,{fontWeight:"bold",mb:2,children:"Age"}),(0,i.jsxs)(s.Text,{color:"gray.300",children:[e.age," years old"]})]}),(0,i.jsxs)(s.Box,{children:[(0,i.jsx)(s.Text,{fontWeight:"bold",mb:2,children:"Hours per Week"}),(0,i.jsxs)(s.Text,{color:"gray.300",children:[e.hoursPerWeek," hours"]})]}),(0,i.jsxs)(s.Box,{children:[(0,i.jsx)(s.Text,{fontWeight:"bold",mb:2,children:"Timezone"}),(0,i.jsx)(s.Text,{color:"gray.300",children:e.timezone})]})]}),e.scenarioResponses&&(0,i.jsxs)(s.Box,{bg:"gray.900",p:4,rounded:"md",children:[(0,i.jsx)(s.Text,{fontWeight:"bold",mb:2,children:"Scenario Responses"}),(0,i.jsx)(s.Text,{color:"gray.300",children:JSON.stringify(e.scenarioResponses)})]}),"pending"===e.status&&(0,i.jsxs)(s.HStack,{spacing:4,justify:"flex-end",children:[(0,i.jsx)(s.Button,{colorScheme:"green",onClick:()=>eo(e._id,"approved"),leftIcon:(0,i.jsx)(s.Icon,{as:h.A7C}),children:"Approve"}),(0,i.jsx)(s.Button,{colorScheme:"red",onClick:()=>eo(e._id,"rejected"),leftIcon:(0,i.jsx)(s.Icon,{as:h._Hm}),children:"Reject"})]})]})})},e._id)):[]})})]})]})]})}):(0,i.jsx)(a.A,{children:(0,i.jsx)(s.Box,{w:"full",p:4,children:(0,i.jsxs)(s.Box,{maxW:"4xl",mx:"auto",mb:8,mt:8,bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"red.400",boxShadow:"0 0 15px rgba(255, 0, 0, 0.2)",textAlign:"center",children:[(0,i.jsx)(s.Heading,{size:"2xl",bgGradient:"linear(to-r, red.300, orange.400)",bgClip:"text",mb:4,children:"Applications Closed"}),(0,i.jsx)(s.Text,{color:"gray.300",fontSize:"lg",children:"We are not accepting new applications at this time. Please check back later!"})]})})}):(0,i.jsx)(a.A,{children:(0,i.jsx)(s.Container,{maxW:"container.md",py:8,children:(0,i.jsxs)(s.Alert,{status:"warning",children:[(0,i.jsx)(s.AlertIcon,{}),"Please sign in to submit an application."]})})}):(0,i.jsx)(a.A,{children:(0,i.jsx)(s.Box,{w:"full",p:4,children:(0,i.jsx)(s.Container,{maxW:"4xl",centerContent:!0,children:(0,i.jsx)(s.Progress,{size:"xs",isIndeterminate:!0,w:"full",colorScheme:"blue"})})})})}let v=async e=>{if(!await (0,n.getServerSession)(e.req,e.res,l.N))return{redirect:{destination:"/signin",permanent:!1}};let{dashboardConfig:t}=await Promise.resolve().then(r.bind(r,69192));return{props:{ownerIds:t.dashboard.admins||[]}}};o()}catch(e){o(e)}})}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[2457,9784,6021,3786,8740,9498,2142,1283,589,6185,4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(26102));module.exports=o})();