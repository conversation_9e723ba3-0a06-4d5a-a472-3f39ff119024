"use strict";exports.id=9114,exports.ids=[9114],exports.modules={4487:(t,e,r)=>{function n(t,e,r){return Math.min(Math.max(t,r),e)}r.d(e,{Du:()=>o,No:()=>y,nj:()=>m});class i extends Error{constructor(t){super(`Failed to parse color: "${t}"`)}}var a=7311==r.j?i:null;function o(t){if("string"!=typeof t)throw new a(t);if("transparent"===t.trim().toLowerCase())return[0,0,0,0];let e=t.trim();e=p.test(t)?function(t){let e=l[function(t){let e=5381,r=t.length;for(;r;)e=33*e^t.charCodeAt(--r);return(e>>>0)%2341}(t.toLowerCase().trim())];if(!e)throw new a(t);return`#${e}`}(t):t;let r=h.exec(e);if(r){let t=Array.from(r).slice(1);return[...t.slice(0,3).map(t=>parseInt(u(t,2),16)),parseInt(u(t[3]||"f",2),16)/255]}let i=f.exec(e);if(i){let t=Array.from(i).slice(1);return[...t.slice(0,3).map(t=>parseInt(t,16)),parseInt(t[3]||"ff",16)/255]}let o=c.exec(e);if(o){let t=Array.from(o).slice(1);return[...t.slice(0,3).map(t=>parseInt(t,10)),parseFloat(t[3]||"1")]}let s=d.exec(e);if(s){let[e,r,i,o]=Array.from(s).slice(1).map(parseFloat);if(n(0,100,r)!==r||n(0,100,i)!==i)throw new a(t);return[...x(e,r,i),Number.isNaN(o)?1:o]}throw new a(t)}let s=t=>parseInt(t.replace(/_/g,""),36),l="1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm".split(" ").reduce((t,e)=>{let r=s(e.substring(0,3)),n=s(e.substring(3)).toString(16),i="";for(let t=0;t<6-n.length;t++)i+="0";return t[r]=`${i}${n}`,t},{}),u=(t,e)=>Array.from(Array(e)).map(()=>t).join(""),h=RegExp(`^#${u("([a-f0-9])",3)}([a-f0-9])?$`,"i"),f=RegExp(`^#${u("([a-f0-9]{2})",3)}([a-f0-9]{2})?$`,"i"),c=RegExp(`^rgba?\\(\\s*(\\d+)\\s*${u(",\\s*(\\d+)\\s*",2)}(?:,\\s*([\\d.]+))?\\s*\\)$`,"i"),d=/^hsla?\(\s*([\d.]+)\s*,\s*([\d.]+)%\s*,\s*([\d.]+)%(?:\s*,\s*([\d.]+))?\s*\)$/i,p=/^[a-z]+$/i,g=t=>Math.round(255*t),x=(t,e,r)=>{let n=r/100;if(0===e)return[n,n,n].map(g);let i=(t%360+360)%360/60,a=e/100*(1-Math.abs(2*n-1)),o=a*(1-Math.abs(i%2-1)),s=0,l=0,u=0;i>=0&&i<1?(s=a,l=o):i>=1&&i<2?(s=o,l=a):i>=2&&i<3?(l=a,u=o):i>=3&&i<4?(l=o,u=a):i>=4&&i<5?(s=o,u=a):i>=5&&i<6&&(s=a,u=o);let h=n-a/2;return[s+h,l+h,u+h].map(g)};function y(t,e){var r,i,a,s;let[l,u,h,f]=o(t);return r=l,i=u,a=h,s=f-e,`rgba(${n(0,255,r).toFixed()}, ${n(0,255,i).toFixed()}, ${n(0,255,a).toFixed()}, ${parseFloat(n(0,1,s).toFixed(3))})`}function m(t){let[e,r,i,a]=o(t),s=t=>{let e=n(0,255,t).toString(16);return 1===e.length?`0${e}`:e};return`#${s(e)}${s(r)}${s(i)}${a<1?s(Math.round(255*a)):""}`}},15081:(t,e,r)=>{function n(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function i(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function a(t){let e,r,a;function s(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>r(t[e],n)?i=e+1:a=e}while(i<a)}return i}return 2!==t.length?(e=n,r=(e,r)=>n(t(e),r),a=(e,r)=>t(e)-r):(e=t===n||t===i?t:o,r=t,a=t),{left:s,center:function(t,e,r=0,n=t.length){let i=s(t,e,r,n-1);return i>r&&a(t[i-1],e)>-a(t[i],e)?i-1:i},right:function(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>=r(t[e],n)?i=e+1:a=e}while(i<a)}return i}}}function o(){return 0}function s(t){return null===t?NaN:+t}r.d(e,{Bu:()=>x.B,V_:()=>n,h1:()=>h,yl:()=>a,YV:()=>q,Z4:()=>A,y1:()=>E,lq:()=>$,sG:()=>k,Zc:()=>M});let l=a(n),u=l.right;l.left,a(s).center;let h=u;function f(t){return function(e,r,n=r){if(!((r*=1)>=0))throw RangeError("invalid rx");if(!((n*=1)>=0))throw RangeError("invalid ry");let{data:i,width:a,height:o}=e;if(!((a=Math.floor(a))>=0))throw RangeError("invalid width");if(!((o=Math.floor(void 0!==o?o:i.length/a))>=0))throw RangeError("invalid height");if(!a||!o||!r&&!n)return e;let s=r&&t(r),l=n&&t(n),u=i.slice();return s&&l?(c(s,u,i,a,o),c(s,i,u,a,o),c(s,u,i,a,o),d(l,i,u,a,o),d(l,u,i,a,o),d(l,i,u,a,o)):s?(c(s,i,u,a,o),c(s,u,i,a,o),c(s,i,u,a,o)):l&&(d(l,i,u,a,o),d(l,u,i,a,o),d(l,i,u,a,o)),e}}function c(t,e,r,n,i){for(let a=0,o=n*i;a<o;)t(e,r,a,a+=n,1)}function d(t,e,r,n,i){for(let a=0,o=n*i;a<n;++a)t(e,r,a,a+o,n)}function p(t){let e=Math.floor(t);if(e===t){var r=t;let e=2*r+1;return(t,n,i,a,o)=>{if(!((a-=o)>=i))return;let s=r*n[i],l=o*r;for(let t=i,e=i+l;t<e;t+=o)s+=n[Math.min(a,t)];for(let r=i,u=a;r<=u;r+=o)s+=n[Math.min(a,r+l)],t[r]=s/e,s-=n[Math.max(i,r-l)]}}let n=t-e,i=2*t+1;return(t,r,a,o,s)=>{if(!((o-=s)>=a))return;let l=e*r[a],u=s*e,h=u+s;for(let t=a,e=a+u;t<e;t+=s)l+=r[Math.min(o,t)];for(let e=a,f=o;e<=f;e+=s)l+=r[Math.min(o,e+u)],t[e]=(l+n*(r[Math.max(a,e-h)]+r[Math.min(o,e+h)]))/i,l-=r[Math.max(a,e-u)]}}f(p),f(function(t){let e=p(t);return(t,r,n,i,a)=>{e(t,r,(n<<=2)+0,(i<<=2)+0,a<<=2),e(t,r,n+1,i+1,a),e(t,r,n+2,i+2,a),e(t,r,n+3,i+3,a)}});var g,x=r(28442);function y(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}var m=Array.prototype;m.slice,m.map;let b=Math.sqrt(50),_=Math.sqrt(10),w=Math.sqrt(2);function v(t,e,r){let n,i,a,o=(e-t)/Math.max(0,r),s=Math.floor(Math.log10(o)),l=o/Math.pow(10,s),u=l>=b?10:l>=_?5:l>=w?2:1;return(s<0?(n=Math.round(t*(a=Math.pow(10,-s)/u)),i=Math.round(e*a),n/a<t&&++n,i/a>e&&--i,a=-a):(n=Math.round(t/(a=Math.pow(10,s)*u)),i=Math.round(e/a),n*a<t&&++n,i*a>e&&--i),i<n&&.5<=r&&r<2)?v(t,e,2*r):[n,i,a]}function M(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[i,a,o]=n?v(e,t,r):v(t,e,r);if(!(a>=i))return[];let s=a-i+1,l=Array(s);if(n)if(o<0)for(let t=0;t<s;++t)l[t]=-((a-t)/o);else for(let t=0;t<s;++t)l[t]=(a-t)*o;else if(o<0)for(let t=0;t<s;++t)l[t]=-((i+t)/o);else for(let t=0;t<s;++t)l[t]=(i+t)*o;return l}function $(t,e,r){return v(t*=1,e*=1,r*=1)[2]}function k(t,e,r){e*=1,t*=1,r*=1;let n=e<t,i=n?$(e,t,r):$(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function N(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function z(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function j(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}function q(t,e,r){if(!(!(i=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,r))).length)||isNaN(e*=1))){if(e<=0||i<2)return z(t);if(e>=1)return N(t);var i,a=(i-1)*e,o=Math.floor(a),s=N((function t(e,r,i=0,a=1/0,o){if(r=Math.floor(r),i=Math.floor(Math.max(0,i)),a=Math.floor(Math.min(e.length-1,a)),!(i<=r&&r<=a))return e;for(o=void 0===o?y:function(t=n){if(t===n)return y;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);a>i;){if(a-i>600){let n=a-i+1,s=r-i+1,l=Math.log(n),u=.5*Math.exp(2*l/3),h=.5*Math.sqrt(l*u*(n-u)/n)*(s-n/2<0?-1:1),f=Math.max(i,Math.floor(r-s*u/n+h)),c=Math.min(a,Math.floor(r+(n-s)*u/n+h));t(e,r,f,c,o)}let n=e[r],s=i,l=a;for(j(e,i,r),o(e[a],n)>0&&j(e,i,a);s<l;){for(j(e,s,l),++s,--l;0>o(e[s],n);)++s;for(;o(e[l],n)>0;)--l}0===o(e[i],n)?j(e,i,l):j(e,++l,a),l<=r&&(i=l+1),r<=l&&(a=l-1)}return e})(t,o).subarray(0,o+1));return s+(z(t.subarray(o+1))-s)*(a-o)}}function A(t,e,r=s){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,a=Math.floor(i),o=+r(t[a],a,t);return o+(r(t[a+1],a+1,t)-o)*(i-a)}}function E(t,e,r){t*=1,e*=1,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),a=Array(i);++n<i;)a[n]=t+n*r;return a}g=Math.random},16596:(t,e,r)=>{r.d(e,{wA:()=>s});let n=Math.PI,i=2*n,a=i-1e-6;function o(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class s{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?o:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return o;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,a){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(t,e,r,i,a){if(t*=1,e*=1,r*=1,i*=1,(a*=1)<0)throw Error(`negative radius: ${a}`);let o=this._x1,s=this._y1,l=r-t,u=i-e,h=o-t,f=s-e,c=h*h+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(c>1e-6)if(Math.abs(f*l-u*h)>1e-6&&a){let d=r-o,p=i-s,g=l*l+u*u,x=Math.sqrt(g),y=Math.sqrt(c),m=a*Math.tan((n-Math.acos((g+c-(d*d+p*p))/(2*x*y)))/2),b=m/y,_=m/x;Math.abs(b-1)>1e-6&&this._append`L${t+b*h},${e+b*f}`,this._append`A${a},${a},0,0,${+(f*d>h*p)},${this._x1=t+_*l},${this._y1=e+_*u}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,o,s,l){if(t*=1,e*=1,r*=1,l=!!l,r<0)throw Error(`negative radius: ${r}`);let u=r*Math.cos(o),h=r*Math.sin(o),f=t+u,c=e+h,d=1^l,p=l?o-s:s-o;null===this._x1?this._append`M${f},${c}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-c)>1e-6)&&this._append`L${f},${c}`,r&&(p<0&&(p=p%i+i),p>a?this._append`A${r},${r},0,1,${d},${t-u},${e-h}A${r},${r},0,1,${d},${this._x1=f},${this._y1=c}`:p>1e-6&&this._append`A${r},${r},0,${+(p>=n)},${d},${this._x1=t+r*Math.cos(s)},${this._y1=e+r*Math.sin(s)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}s.prototype},39845:(t,e,r)=>{function n(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function i(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function a(){}r.d(e,{GW:()=>tc,Dj:()=>tu,sH:()=>td,$B:()=>tv});var o,s="\\s*([+-]?\\d+)\\s*",l="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",u="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",h=/^#([0-9a-f]{3,8})$/,f=RegExp(`^rgb\\(${s},${s},${s}\\)$`),c=RegExp(`^rgb\\(${u},${u},${u}\\)$`),d=RegExp(`^rgba\\(${s},${s},${s},${l}\\)$`),p=RegExp(`^rgba\\(${u},${u},${u},${l}\\)$`),g=RegExp(`^hsl\\(${l},${u},${u}\\)$`),x=RegExp(`^hsla\\(${l},${u},${u},${l}\\)$`),y={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function m(){return this.rgb().formatHex()}function b(){return this.rgb().formatRgb()}function _(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=h.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?w(e):3===r?new k(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?v(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?v(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=f.exec(t))?new k(e[1],e[2],e[3],1):(e=c.exec(t))?new k(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=d.exec(t))?v(e[1],e[2],e[3],e[4]):(e=p.exec(t))?v(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=g.exec(t))?E(e[1],e[2]/100,e[3]/100,1):(e=x.exec(t))?E(e[1],e[2]/100,e[3]/100,e[4]):y.hasOwnProperty(t)?w(y[t]):"transparent"===t?new k(NaN,NaN,NaN,0):null}function w(t){return new k(t>>16&255,t>>8&255,255&t,1)}function v(t,e,r,n){return n<=0&&(t=e=r=NaN),new k(t,e,r,n)}function M(t){return(t instanceof a||(t=_(t)),t)?new k((t=t.rgb()).r,t.g,t.b,t.opacity):new k}function $(t,e,r,n){return 1==arguments.length?M(t):new k(t,e,r,null==n?1:n)}function k(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function N(){return`#${A(this.r)}${A(this.g)}${A(this.b)}`}function z(){let t=j(this.opacity);return`${1===t?"rgb(":"rgba("}${q(this.r)}, ${q(this.g)}, ${q(this.b)}${1===t?")":`, ${t})`}`}function j(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function q(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function A(t){return((t=q(t))<16?"0":"")+t.toString(16)}function E(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new C(t,e,r,n)}function S(t){if(t instanceof C)return new C(t.h,t.s,t.l,t.opacity);if(t instanceof a||(t=_(t)),!t)return new C;if(t instanceof C)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),o=Math.max(e,r,n),s=NaN,l=o-i,u=(o+i)/2;return l?(s=e===o?(r-n)/l+(r<n)*6:r===o?(n-e)/l+2:(e-r)/l+4,l/=u<.5?o+i:2-o-i,s*=60):l=u>0&&u<1?0:s,new C(s,l,u,t.opacity)}function R(t,e,r,n){return 1==arguments.length?S(t):new C(t,e,r,null==n?1:n)}function C(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function D(t){return(t=(t||0)%360)<0?t+360:t}function I(t){return Math.max(0,Math.min(1,t||0))}function X(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}n(a,_,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:m,formatHex:m,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return S(this).formatHsl()},formatRgb:b,toString:b}),n(k,$,i(a,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new k(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new k(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new k(q(this.r),q(this.g),q(this.b),j(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:N,formatHex:N,formatHex8:function(){return`#${A(this.r)}${A(this.g)}${A(this.b)}${A((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:z,toString:z})),n(C,R,i(a,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new C(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new C(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new k(X(t>=240?t-240:t+120,i,n),X(t,i,n),X(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new C(D(this.h),I(this.s),I(this.l),j(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=j(this.opacity);return`${1===t?"hsl(":"hsla("}${D(this.h)}, ${100*I(this.s)}%, ${100*I(this.l)}%${1===t?")":`, ${t})`}`}}));let T=Math.PI/180,P=180/Math.PI,F=4/29,H=6/29,L=6/29*3*(6/29),Y=6/29*(6/29)*(6/29);function O(t){if(t instanceof G)return new G(t.l,t.a,t.b,t.opacity);if(t instanceof W)return K(t);t instanceof k||(t=M(t));var e,r,n=B(t.r),i=B(t.g),a=B(t.b),o=U((.2225045*n+.7168786*i+.0606169*a)/1);return n===i&&i===a?e=r=o:(e=U((.4360747*n+.3850649*i+.1430804*a)/.96422),r=U((.0139322*n+.0971045*i+.7141733*a)/.82521)),new G(116*o-16,500*(e-o),200*(o-r),t.opacity)}function G(t,e,r,n){this.l=+t,this.a=+e,this.b=+r,this.opacity=+n}function U(t){return t>Y?Math.pow(t,1/3):t/L+F}function V(t){return t>H?t*t*t:L*(t-F)}function Z(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function B(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function Q(t,e,r,n){return 1==arguments.length?function(t){if(t instanceof W)return new W(t.h,t.c,t.l,t.opacity);if(t instanceof G||(t=O(t)),0===t.a&&0===t.b)return new W(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*P;return new W(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}(t):new W(t,e,r,null==n?1:n)}function W(t,e,r,n){this.h=+t,this.c=+e,this.l=+r,this.opacity=+n}function K(t){if(isNaN(t.h))return new G(t.l,0,0,t.opacity);var e=t.h*T;return new G(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}n(G,function(t,e,r,n){return 1==arguments.length?O(t):new G(t,e,r,null==n?1:n)},i(a,{brighter(t){return new G(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new G(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,r=isNaN(this.b)?t:t-this.b/200;return e=.96422*V(e),new k(Z(3.1338561*e-1.6168667*(t=+V(t))-.4906146*(r=.82521*V(r))),Z(-.9787684*e+1.9161415*t+.033454*r),Z(.0719453*e-.2289914*t+1.4052427*r),this.opacity)}})),n(W,Q,i(a,{brighter(t){return new W(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new W(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return K(this).rgb()}}));var J=-1.78277*.29227-.1347134789;function tt(t,e,r,n){return 1==arguments.length?function(t){if(t instanceof te)return new te(t.h,t.s,t.l,t.opacity);t instanceof k||(t=M(t));var e=t.r/255,r=t.g/255,n=t.b/255,i=(J*n+-1.7884503806*e-3.5172982438*r)/(J+-1.7884503806-3.5172982438),a=n-i,o=-((1.97294*(r-i)- -.29227*a)/.90649),s=Math.sqrt(o*o+a*a)/(1.97294*i*(1-i)),l=s?Math.atan2(o,a)*P-120:NaN;return new te(l<0?l+360:l,s,i,t.opacity)}(t):new te(t,e,r,null==n?1:n)}function te(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function tr(t,e,r,n,i){var a=t*t,o=a*t;return((1-3*t+3*a-o)*e+(4-6*a+3*o)*r+(1+3*t+3*a-3*o)*n+o*i)/6}n(te,tt,i(a,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new te(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new te(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*T,e=+this.l,r=isNaN(this.s)?0:this.s*e*(1-e),n=Math.cos(t),i=Math.sin(t);return new k(255*(e+r*(-.14861*n+1.78277*i)),255*(e+r*(-.29227*n+-.90649*i)),255*(e+1.97294*n*r),this.opacity)}}));let tn=t=>()=>t;function ti(t,e){return function(r){return t+r*e}}function ta(t,e){var r=e-t;return r?ti(t,r>180||r<-180?r-360*Math.round(r/360):r):tn(isNaN(t)?e:t)}function to(t,e){var r=e-t;return r?ti(t,r):tn(isNaN(t)?e:t)}let ts=function t(e){var r,n=1==(r=+e)?to:function(t,e){var n,i,a;return e-t?(n=t,i=e,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(t){return Math.pow(n+t*i,a)}):tn(isNaN(t)?e:t)};function i(t,e){var r=n((t=$(t)).r,(e=$(e)).r),i=n(t.g,e.g),a=n(t.b,e.b),o=to(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=a(e),t.opacity=o(e),t+""}}return i.gamma=t,i}(1);function tl(t){return function(e){var r,n,i=e.length,a=Array(i),o=Array(i),s=Array(i);for(r=0;r<i;++r)n=$(e[r]),a[r]=n.r||0,o[r]=n.g||0,s[r]=n.b||0;return a=t(a),o=t(o),s=t(s),n.opacity=1,function(t){return n.r=a(t),n.g=o(t),n.b=s(t),n+""}}}tl(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],a=t[n+1],o=n>0?t[n-1]:2*i-a,s=n<e-1?t[n+2]:2*a-i;return tr((r-n/e)*e,o,i,a,s)}}),tl(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],a=t[n%e],o=t[(n+1)%e],s=t[(n+2)%e];return tr((r-n/e)*e,i,a,o,s)}});function tu(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var th=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tf=RegExp(th.source,"g");function tc(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?tn(e):("number"===i?tu:"string"===i?(n=_(e))?(e=n,ts):function(t,e){var r,n,i,a,o,s=th.lastIndex=tf.lastIndex=0,l=-1,u=[],h=[];for(t+="",e+="";(i=th.exec(t))&&(a=tf.exec(e));)(o=a.index)>s&&(o=e.slice(s,o),u[l]?u[l]+=o:u[++l]=o),(i=i[0])===(a=a[0])?u[l]?u[l]+=a:u[++l]=a:(u[++l]=null,h.push({i:l,x:tu(i,a)})),s=tf.lastIndex;return s<e.length&&(o=e.slice(s),u[l]?u[l]+=o:u[++l]=o),u.length<2?h[0]?(r=h[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=h.length,function(t){for(var r,n=0;n<e;++n)u[(r=h[n]).i]=r.x(t);return u.join("")})}:e instanceof _?ts:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=tc(t[r],e[r]);for(;r<n;++r)o[r]=e[r];return function(t){for(r=0;r<i;++r)o[r]=a[r](t);return o}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=tc(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:tu:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(a){for(r=0;r<n;++r)i[r]=t[r]*(1-a)+e[r]*a;return i}})(t,e)}function td(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}var tp=180/Math.PI,tg={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function tx(t,e,r,n,i,a){var o,s,l;return(o=Math.sqrt(t*t+e*e))&&(t/=o,e/=o),(l=t*r+e*n)&&(r-=t*l,n-=e*l),(s=Math.sqrt(r*r+n*n))&&(r/=s,n/=s,l/=s),t*n<e*r&&(t=-t,e=-e,l=-l,o=-o),{translateX:i,translateY:a,rotate:Math.atan2(e,t)*tp,skewX:Math.atan(l)*tp,scaleX:o,scaleY:s}}function ty(t,e,r,n){function i(t){return t.length?t.pop()+" ":""}return function(a,o){var s,l,u,h,f=[],c=[];return a=t(a),o=t(o),!function(t,n,i,a,o,s){if(t!==i||n!==a){var l=o.push("translate(",null,e,null,r);s.push({i:l-4,x:tu(t,i)},{i:l-2,x:tu(n,a)})}else(i||a)&&o.push("translate("+i+e+a+r)}(a.translateX,a.translateY,o.translateX,o.translateY,f,c),s=a.rotate,l=o.rotate,s!==l?(s-l>180?l+=360:l-s>180&&(s+=360),c.push({i:f.push(i(f)+"rotate(",null,n)-2,x:tu(s,l)})):l&&f.push(i(f)+"rotate("+l+n),u=a.skewX,h=o.skewX,u!==h?c.push({i:f.push(i(f)+"skewX(",null,n)-2,x:tu(u,h)}):h&&f.push(i(f)+"skewX("+h+n),!function(t,e,r,n,a,o){if(t!==r||e!==n){var s=a.push(i(a)+"scale(",null,",",null,")");o.push({i:s-4,x:tu(t,r)},{i:s-2,x:tu(e,n)})}else(1!==r||1!==n)&&a.push(i(a)+"scale("+r+","+n+")")}(a.scaleX,a.scaleY,o.scaleX,o.scaleY,f,c),a=o=null,function(t){for(var e,r=-1,n=c.length;++r<n;)f[(e=c[r]).i]=e.x(t);return f.join("")}}}function tm(t){return((t=Math.exp(t))+1/t)/2}function tb(t){return function(e,r){var n=t((e=R(e)).h,(r=R(r)).h),i=to(e.s,r.s),a=to(e.l,r.l),o=to(e.opacity,r.opacity);return function(t){return e.h=n(t),e.s=i(t),e.l=a(t),e.opacity=o(t),e+""}}}function t_(t){return function(e,r){var n=t((e=Q(e)).h,(r=Q(r)).h),i=to(e.c,r.c),a=to(e.l,r.l),o=to(e.opacity,r.opacity);return function(t){return e.h=n(t),e.c=i(t),e.l=a(t),e.opacity=o(t),e+""}}}function tw(t){return function e(r){function n(e,n){var i=t((e=tt(e)).h,(n=tt(n)).h),a=to(e.s,n.s),o=to(e.l,n.l),s=to(e.opacity,n.opacity);return function(t){return e.h=i(t),e.s=a(t),e.l=o(Math.pow(t,r)),e.opacity=s(t),e+""}}return r*=1,n.gamma=e,n}(1)}function tv(t,e){void 0===e&&(e=t,t=tc);for(var r=0,n=e.length-1,i=e[0],a=Array(n<0?0:n);r<n;)a[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return a[e](t-e)}}ty(function(t){let e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?tg:tx(e.a,e.b,e.c,e.d,e.e,e.f)},"px, ","px)","deg)"),ty(function(t){return null!=t&&(o||(o=document.createElementNS("http://www.w3.org/2000/svg","g")),o.setAttribute("transform",t),t=o.transform.baseVal.consolidate())?tx((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):tg},", ",")",")"),!function t(e,r,n){function i(t,i){var a,o,s=t[0],l=t[1],u=t[2],h=i[0],f=i[1],c=i[2],d=h-s,p=f-l,g=d*d+p*p;if(g<1e-12)o=Math.log(c/u)/e,a=function(t){return[s+t*d,l+t*p,u*Math.exp(e*t*o)]};else{var x=Math.sqrt(g),y=(c*c-u*u+n*g)/(2*u*r*x),m=(c*c-u*u-n*g)/(2*c*r*x),b=Math.log(Math.sqrt(y*y+1)-y);o=(Math.log(Math.sqrt(m*m+1)-m)-b)/e,a=function(t){var n,i,a=t*o,h=tm(b),f=u/(r*x)*(h*(((n=Math.exp(2*(n=e*a+b)))-1)/(n+1))-((i=Math.exp(i=b))-1/i)/2);return[s+f*d,l+f*p,u*h/tm(e*a+b)]}}return a.duration=1e3*o*e/Math.SQRT2,a}return i.rho=function(e){var r=Math.max(.001,+e),n=r*r;return t(r,n,n*n)},i}(Math.SQRT2,2,4),tb(ta),tb(to),t_(ta),t_(to),tw(ta),tw(to)},41355:(t,e,r)=>{function n(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function i(t){return(t=n(Math.abs(t)))?t[1]:NaN}r.d(e,{GP:()=>s,s:()=>l,Gp:()=>h,RT:()=>y,dT:()=>m,Pj:()=>b});var a,o,s,l,u=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function h(t){var e;if(!(e=u.exec(t)))throw Error("invalid format: "+t);return new f({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function f(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function c(t,e){var r=n(t,e);if(!r)return t+"";var i=r[0],a=r[1];return a<0?"0."+Array(-a).join("0")+i:i.length>a+1?i.slice(0,a+1)+"."+i.slice(a+1):i+Array(a-i.length+2).join("0")}h.prototype=f.prototype,f.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let d={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>c(100*t,e),r:c,s:function(t,e){var r=n(t,e);if(!r)return t+"";var i=r[0],o=r[1],s=o-(a=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,l=i.length;return s===l?i:s>l?i+Array(s-l+1).join("0"):s>0?i.slice(0,s)+"."+i.slice(s):"0."+Array(1-s).join("0")+n(t,Math.max(0,e+s-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function p(t){return t}var g=Array.prototype.map,x=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function y(t){return Math.max(0,-i(Math.abs(t)))}function m(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(i(e)/3)))-i(Math.abs(t)))}function b(t,e){return Math.max(0,i(e=Math.abs(e)-(t=Math.abs(t)))-i(t))+1}s=(o=function(t){var e,r,n,o=void 0===t.grouping||void 0===t.thousands?p:(e=g.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,a=[],o=0,s=e[0],l=0;i>0&&s>0&&(l+s+1>n&&(s=Math.max(1,n-l)),a.push(t.substring(i-=s,i+s)),!((l+=s+1)>n));)s=e[o=(o+1)%e.length];return a.reverse().join(r)}),s=void 0===t.currency?"":t.currency[0]+"",l=void 0===t.currency?"":t.currency[1]+"",u=void 0===t.decimal?".":t.decimal+"",f=void 0===t.numerals?p:(n=g.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),c=void 0===t.percent?"%":t.percent+"",y=void 0===t.minus?"−":t.minus+"",m=void 0===t.nan?"NaN":t.nan+"";function b(t){var e=(t=h(t)).fill,r=t.align,n=t.sign,i=t.symbol,p=t.zero,g=t.width,b=t.comma,_=t.precision,w=t.trim,v=t.type;"n"===v?(b=!0,v="g"):d[v]||(void 0===_&&(_=12),w=!0,v="g"),(p||"0"===e&&"="===r)&&(p=!0,e="0",r="=");var M="$"===i?s:"#"===i&&/[boxX]/.test(v)?"0"+v.toLowerCase():"",$="$"===i?l:/[%p]/.test(v)?c:"",k=d[v],N=/[defgprs%]/.test(v);function z(t){var i,s,l,h=M,c=$;if("c"===v)c=k(t)+c,t="";else{var d=(t*=1)<0||1/t<0;if(t=isNaN(t)?m:k(Math.abs(t),_),w&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),d&&0==+t&&"+"!==n&&(d=!1),h=(d?"("===n?n:y:"-"===n||"("===n?"":n)+h,c=("s"===v?x[8+a/3]:"")+c+(d&&"("===n?")":""),N){for(i=-1,s=t.length;++i<s;)if(48>(l=t.charCodeAt(i))||l>57){c=(46===l?u+t.slice(i+1):t.slice(i))+c,t=t.slice(0,i);break}}}b&&!p&&(t=o(t,1/0));var z=h.length+t.length+c.length,j=z<g?Array(g-z+1).join(e):"";switch(b&&p&&(t=o(j+t,j.length?g-c.length:1/0),j=""),r){case"<":t=h+t+c+j;break;case"=":t=h+j+t+c;break;case"^":t=j.slice(0,z=j.length>>1)+h+t+c+j.slice(z);break;default:t=j+h+t+c}return f(t)}return _=void 0===_?6:/[gprs]/.test(v)?Math.max(1,Math.min(21,_)):Math.max(0,Math.min(20,_)),z.toString=function(){return t+""},z}return{format:b,formatPrefix:function(t,e){var r=b(((t=h(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(i(e)/3))),a=Math.pow(10,-n),o=x[8+n/3];return function(t){return r(a*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,l=o.formatPrefix},99897:(t,e,r)=>{var n=r(27767),i={"text/plain":"Text","text/html":"Url",default:"Text"};t.exports=function(t,e){var r,a,o,s,l,u,h,f,c=!1;e||(e={}),o=e.debug||!1;try{if(l=n(),u=document.createRange(),h=document.getSelection(),(f=document.createElement("span")).textContent=t,f.ariaHidden="true",f.style.all="unset",f.style.position="fixed",f.style.top=0,f.style.clip="rect(0, 0, 0, 0)",f.style.whiteSpace="pre",f.style.webkitUserSelect="text",f.style.MozUserSelect="text",f.style.msUserSelect="text",f.style.userSelect="text",f.addEventListener("copy",function(r){if(r.stopPropagation(),e.format)if(r.preventDefault(),void 0===r.clipboardData){o&&console.warn("unable to use e.clipboardData"),o&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var n=i[e.format]||i.default;window.clipboardData.setData(n,t)}else r.clipboardData.clearData(),r.clipboardData.setData(e.format,t);e.onCopy&&(r.preventDefault(),e.onCopy(r.clipboardData))}),document.body.appendChild(f),u.selectNodeContents(f),h.addRange(u),!document.execCommand("copy"))throw Error("copy command was unsuccessful");c=!0}catch(n){o&&console.error("unable to copy using execCommand: ",n),o&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(e.format||"text",t),e.onCopy&&e.onCopy(window.clipboardData),c=!0}catch(n){o&&console.error("unable to copy using clipboardData: ",n),o&&console.error("falling back to prompt"),r="message"in e?e.message:"Copy to clipboard: #{key}, Enter",a=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",s=r.replace(/#{\s*key\s*}/g,a),window.prompt(s,t)}}finally{h&&("function"==typeof h.removeRange?h.removeRange(u):h.removeAllRanges()),f&&document.body.removeChild(f),l()}return c}}};