"use strict";(()=>{var e={};e.id=3106,e.ids=[636,3106,3220],e.modules={4722:e=>{e.exports=require("next-auth/react")},8732:e=>{e.exports=require("react/jsx-runtime")},14078:e=>{e.exports=import("swr")},15806:e=>{e.exports=require("next-auth/next")},16625:(e,r,t)=>{t.d(r,{FSj:()=>s.FSj,JhU:()=>s.JhU,XcJ:()=>s.XcJ,uoG:()=>s.uoG});var s=t(48648)},18151:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>g,getServerSideProps:()=>f});var a=t(8732),n=t(27189),i=t(81011),l=t(15806),o=t(92546),c=t(88358),d=t(16625),x=t(98364),p=t.n(x),u=t(82015),h=e([n,i]);[n,i]=h.then?(await h)():h;let b=p()(()=>t.e(9760).then(t.bind(t,59760)),{loadableGenerated:{modules:["pages\\experimental.tsx -> ../components/ExperimentalFeatures"]},loading:()=>(0,a.jsx)(n.y$,{size:"lg"}),ssr:!1}),j=p()(()=>Promise.resolve().then(t.bind(t,31749)),{loadableGenerated:{modules:["pages\\experimental.tsx -> ../hooks/useExperimentalFeatures"]},ssr:!1}),S=[{id:"addon-builder",name:"Addon Builder",description:"Create custom Discord bot addons with a visual interface",icon:d.FSj,status:"beta",enabled:!0,path:"/experimental/addon-builder"}];function m({feature:e}){let r=(0,c.useRouter)(),{hasAccess:t}=j();return(0,a.jsx)(n.Zp,{cursor:t&&e.enabled?"pointer":"not-allowed",onClick:()=>{t&&e.enabled&&r.push(e.path)},opacity:t&&e.enabled?1:.6,_hover:t&&e.enabled?{transform:"translateY(-2px)",shadow:"xl"}:{},transition:"all 0.2s",border:"1px solid",borderColor:t&&e.enabled?"transparent":"gray.300",bg:t&&e.enabled?"white":"gray.50",children:(0,a.jsx)(n.bw,{children:(0,a.jsxs)(n.Tk,{align:"start",spacing:3,children:[(0,a.jsxs)(n.zt,{children:[(0,a.jsx)(n.In,{as:e.icon,boxSize:6,color:"purple.500"}),(0,a.jsx)(n.Tk,{align:"start",spacing:0,flex:1,children:(0,a.jsxs)(n.zt,{children:[(0,a.jsx)(n.EY,{fontWeight:"bold",fontSize:"lg",children:e.name}),(0,a.jsx)(n.Ex,{colorScheme:{alpha:"red",beta:"orange",stable:"green"}[e.status],size:"sm",children:e.status.toUpperCase()})]})}),!e.enabled&&(0,a.jsx)(n.In,{as:d.JhU,boxSize:4,color:"gray.400"})]}),(0,a.jsx)(n.EY,{color:"gray.600",fontSize:"sm",children:e.description}),(0,a.jsx)(n.$n,{size:"sm",colorScheme:"purple",variant:"outline",isDisabled:!t||!e.enabled,leftIcon:(0,a.jsx)(n.In,{as:d.uoG}),children:t?e.enabled?"Try Now":"Coming Soon":"Access Required"})]})})})}function g(){let{hasAccess:e,isLoading:r,reason:t}=j();return r?(0,a.jsx)(i.A,{children:(0,a.jsx)(n.mc,{maxW:"container.xl",py:8,children:(0,a.jsxs)(n.Tk,{spacing:8,children:[(0,a.jsx)(n.y$,{size:"xl"}),(0,a.jsx)(n.DZ,{size:"md",children:"Loading experimental features..."})]})})}):e?(0,a.jsx)(i.A,{children:(0,a.jsx)(n.mc,{maxW:"container.xl",py:8,children:(0,a.jsxs)(n.Tk,{spacing:8,align:"stretch",children:[(0,a.jsxs)(n.az,{textAlign:"center",children:[(0,a.jsx)(n.DZ,{size:"lg",mb:4,children:"⚗️ Experimental Features"}),(0,a.jsx)(n.EY,{color:"gray.600",fontSize:"lg",children:"Cutting-edge features in development"})]}),(0,a.jsxs)(n.Fc,{status:"info",borderRadius:"md",children:[(0,a.jsx)(n._0,{}),(0,a.jsxs)(n.az,{children:[(0,a.jsx)(n.XL,{children:"Beta Testing Program"}),(0,a.jsxs)(n.TN,{children:[(0,a.jsx)("strong",{children:"Welcome to Experimental Features!"})," These features are in active development. Your feedback helps us improve them before general release."]})]})]}),(0,a.jsx)(n.rS,{columns:{base:1,md:2,lg:3},spacing:6,children:S.map(e=>(0,a.jsx)(m,{feature:e},e.id))}),(0,a.jsxs)(n.az,{children:[(0,a.jsx)(n.cG,{mb:4}),(0,a.jsx)(u.Suspense,{fallback:(0,a.jsx)(n.y$,{}),children:(0,a.jsx)(b,{})})]}),(0,a.jsxs)(n.Fc,{status:"warning",borderRadius:"md",children:[(0,a.jsx)(n._0,{}),(0,a.jsx)(n.TN,{children:"Experimental features may change or be removed without notice. Use at your own discretion."})]})]})})}):(0,a.jsx)(i.A,{children:(0,a.jsx)(n.mc,{maxW:"container.xl",py:8,children:(0,a.jsxs)(n.Tk,{spacing:8,children:[(0,a.jsx)(n.In,{as:d.XcJ,boxSize:16,color:"purple.500"}),(0,a.jsx)(n.DZ,{size:"lg",textAlign:"center",children:"⚗️ Experimental Features"}),(0,a.jsxs)(n.Fc,{status:"warning",borderRadius:"md",children:[(0,a.jsx)(n._0,{}),(0,a.jsxs)(n.az,{children:[(0,a.jsx)(n.XL,{children:"Access Required!"}),(0,a.jsxs)(n.TN,{children:["You need experimental features access to use these features.","no_access"===t&&" Please apply for experimental features access from the overview page."]})]})]})]})})})}let f=async e=>await (0,l.getServerSession)(e.req,e.res,o.N)?{props:{}}:{redirect:{destination:"/api/auth/signin?callbackUrl=%2Fexperimental",permanent:!1}};s()}catch(e){s(e)}})},20396:e=>{e.exports=require("next-auth/providers/discord")},22326:e=>{e.exports=require("react-dom")},27189:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.d(r,{$n:()=>d.$,DZ:()=>g.D,EY:()=>f.E,Ex:()=>o.E,Fc:()=>a.F,In:()=>b.I,TN:()=>n.T,Tk:()=>v.T,XL:()=>l.X,Zp:()=>x.Z,_0:()=>i._,az:()=>c.a,bw:()=>p.b,cG:()=>h.c,mc:()=>u.m,rS:()=>j.r,y$:()=>S.y,zt:()=>m.z});var a=t(5128),n=t(76331),i=t(31772),l=t(40575),o=t(25392),c=t(45200),d=t(77502),x=t(90846),p=t(60615),u=t(64304),h=t(464),m=t(55197),g=t(30519),b=t(50792),j=t(67981),S=t(90088),f=t(87378),v=t(17335),y=e([a,n,i,l,o,c,d,x,p,u,h,m,g,b,j,S,f,v]);[a,n,i,l,o,c,d,x,p,u,h,m,g,b,j,S,f,v]=y.then?(await y)():y,s()}catch(e){s(e)}})},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},30067:e=>{e.exports=import("@emotion/styled")},33873:e=>{e.exports=require("path")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},41306:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>m,default:()=>x,getServerSideProps:()=>h,getStaticPaths:()=>u,getStaticProps:()=>p,reportWebVitals:()=>g,routeModule:()=>y,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>b});var a=t(1292),n=t(58834),i=t(40786),l=t(83567),o=t(8077),c=t(18151),d=e([o,c]);[o,c]=d.then?(await d)():d;let x=(0,i.M)(c,"default"),p=(0,i.M)(c,"getStaticProps"),u=(0,i.M)(c,"getStaticPaths"),h=(0,i.M)(c,"getServerSideProps"),m=(0,i.M)(c,"config"),g=(0,i.M)(c,"reportWebVitals"),b=(0,i.M)(c,"unstable_getStaticProps"),j=(0,i.M)(c,"unstable_getStaticPaths"),S=(0,i.M)(c,"unstable_getStaticParams"),f=(0,i.M)(c,"unstable_getServerProps"),v=(0,i.M)(c,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:n.A.PAGES,page:"/experimental",pathname:"/experimental",bundlePath:"",filename:""},components:{App:o.default,Document:l.default},userland:c});s()}catch(e){s(e)}})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},74075:e=>{e.exports=require("zlib")},82015:e=>{e.exports=require("react")},88455:e=>{e.exports=import("@emotion/react")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2457,9784,6021,3786,8740,9498,2142,1283,589,6185,4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(41306));module.exports=s})();