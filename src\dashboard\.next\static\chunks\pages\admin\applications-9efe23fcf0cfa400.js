(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7453],{73510:(e,s,i)=>{"use strict";i.r(s),i.d(s,{__N_SSP:()=>U,default:()=>V});var n=i(94513),r=i(67116),l=i(39905),c=i(1648),t=i(567),d=i(43700),a=i(79028),h=i(64349),o=i(5142),x=i(28365),j=i(36468),p=i(1871),u=i(78813),m=i(29484),g=i(46949),f=i(81139),E=i(95066),T=i(53083),S=i(31840),w=i(29607),y=i(30301),b=i(84748),z=i(42398),_=i(94042),v=i(67866),k=i(11067),A=i(30994),I=i(93493),C=i(91140),R=i(57688),N=i(21181),W=i(82824),F=i(56858),D=i(46312),q=i(84482),$=i(5130),P=i(10246),X=i(32338),M=i(80456),L=i(3037),O=i(7836),Z=i(84622);i(75632),i(82273);var H=i(97119),J=i(94285),K=i(21489),U=!0;function V(){var e;let[s,i]=(0,J.useState)([]),[U,V]=(0,J.useState)([]),[B,G]=(0,J.useState)(!0),[Q,Y]=(0,J.useState)({total:0,pending:0,approved:0,rejected:0,recentIncrease:0}),ee=(0,O.d)(),{isOpen:es,onOpen:ei,onClose:en}=(0,Z.j)(),[er,el]=(0,J.useState)(null);(0,J.useEffect)(()=>{ec(),et()},[]);let ec=async()=>{try{let e=await fetch("/api/admin/applications");if(e.ok){let s=await e.json();i(s.applications||[]),Y(s.stats||Q)}}catch(e){ee({title:"Error",description:"Failed to load applications",status:"error",duration:3e3})}finally{G(!1)}},et=async()=>{try{let e=await fetch("/api/admin/applications?type=experimental");if(e.ok){let s=await e.json();V((s.applications||[]).filter(e=>"experimental"===e.type))}}catch(e){}},ed=async(e,s,i)=>{try{(await fetch("".concat("/api/admin/applications","/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:s})})).ok&&("regular"===i?ec():et(),ee({title:"Success",description:"Application ".concat(s,"d successfully"),status:"success",duration:3e3}),en())}catch(e){ee({title:"Error",description:"Failed to ".concat(s," application"),status:"error",duration:3e3})}},ea=e=>{el(e),ei()};return B?(0,n.jsx)(H.A,{children:(0,n.jsx)(a.a,{p:8,display:"flex",justifyContent:"center",alignItems:"center",minH:"400px",children:(0,n.jsx)(b.y,{size:"xl"})})}):(0,n.jsxs)(H.A,{children:[(0,n.jsx)(a.a,{p:8,children:(0,n.jsxs)(L.T,{align:"stretch",spacing:6,children:[(0,n.jsxs)(p.z,{children:[(0,n.jsx)(m.I,{as:K.t69,boxSize:6,color:"blue.500"}),(0,n.jsx)(u.D,{size:"lg",children:"Applications Management"})]}),(0,n.jsx)($.E,{color:"gray.600",_dark:{color:"gray.300"},children:"Manage and review all user applications including role applications and experimental feature requests."}),(0,n.jsxs)(y.r,{columns:{base:1,md:4},spacing:6,children:[(0,n.jsx)(o.Z,{children:(0,n.jsx)(x.b,{children:(0,n.jsxs)(z.r,{children:[(0,n.jsx)(k.v,{children:"Total Applications"}),(0,n.jsx)(A.k,{children:Q.total}),(0,n.jsxs)(v.h,{children:[(0,n.jsx)(_.Ip,{type:Q.recentIncrease>=0?"increase":"decrease"}),Math.abs(Q.recentIncrease),"% this month"]})]})})}),(0,n.jsx)(o.Z,{children:(0,n.jsx)(x.b,{children:(0,n.jsxs)(z.r,{children:[(0,n.jsx)(k.v,{children:"Pending Review"}),(0,n.jsx)(A.k,{color:"yellow.500",children:Q.pending}),(0,n.jsx)(v.h,{children:"Requires attention"})]})})}),(0,n.jsx)(o.Z,{children:(0,n.jsx)(x.b,{children:(0,n.jsxs)(z.r,{children:[(0,n.jsx)(k.v,{children:"Approved"}),(0,n.jsx)(A.k,{color:"green.500",children:Q.approved}),(0,n.jsx)(v.h,{children:"Accepted applications"})]})})}),(0,n.jsx)(o.Z,{children:(0,n.jsx)(x.b,{children:(0,n.jsxs)(z.r,{children:[(0,n.jsx)(k.v,{children:"Rejected"}),(0,n.jsx)(A.k,{color:"red.500",children:Q.rejected}),(0,n.jsx)(v.h,{children:"Declined applications"})]})})})]}),(0,n.jsxs)(F.t,{colorScheme:"blue",isLazy:!0,children:[(0,n.jsxs)(C.w,{children:[(0,n.jsx)(I.o,{children:(0,n.jsxs)(p.z,{children:[(0,n.jsx)(m.I,{as:K.t69}),(0,n.jsx)($.E,{children:"Role Applications"}),s.filter(e=>"pending"===e.status).length>0&&(0,n.jsx)(d.E,{colorScheme:"red",children:s.filter(e=>"pending"===e.status).length})]})}),(0,n.jsx)(I.o,{children:(0,n.jsxs)(p.z,{children:[(0,n.jsx)(m.I,{as:K.XcJ}),(0,n.jsx)($.E,{children:"Experimental Requests"}),U.filter(e=>"pending"===e.status).length>0&&(0,n.jsx)(d.E,{colorScheme:"yellow",children:U.filter(e=>"pending"===e.status).length})]})})]}),(0,n.jsxs)(N.T,{children:[(0,n.jsx)(R.K,{children:0===s.length?(0,n.jsxs)(r.F,{status:"info",children:[(0,n.jsx)(c._,{}),(0,n.jsx)(l.T,{children:"No role applications found. Applications will appear here when users apply for moderator or other roles."})]}):(0,n.jsxs)(W.X,{variant:"simple",children:[(0,n.jsx)(X.d,{children:(0,n.jsxs)(M.Tr,{children:[(0,n.jsx)(P.Th,{children:"User"}),(0,n.jsx)(P.Th,{children:"Application Type"}),(0,n.jsx)(P.Th,{children:"Submitted"}),(0,n.jsx)(P.Th,{children:"Status"}),(0,n.jsx)(P.Th,{children:"Actions"})]})}),(0,n.jsx)(D.N,{children:s.map(e=>(0,n.jsxs)(M.Tr,{children:[(0,n.jsx)(q.Td,{children:(0,n.jsxs)(p.z,{children:[(0,n.jsx)(t.e,{size:"sm",name:e.username||e.userId}),(0,n.jsx)($.E,{children:e.username||e.userId})]})}),(0,n.jsx)(q.Td,{children:"Moderator"}),(0,n.jsx)(q.Td,{children:new Date(e.date).toLocaleDateString()}),(0,n.jsx)(q.Td,{children:(0,n.jsx)(d.E,{colorScheme:"approved"===e.status?"green":"rejected"===e.status?"red":"yellow",children:e.status})}),(0,n.jsx)(q.Td,{children:(0,n.jsxs)(p.z,{spacing:2,children:[(0,n.jsx)(h.$,{size:"sm",colorScheme:"blue",onClick:()=>ea(e),children:"View"}),"pending"===e.status&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(h.$,{size:"sm",colorScheme:"green",onClick:()=>ed(e._id,"approve","regular"),children:"Accept"}),(0,n.jsx)(h.$,{size:"sm",colorScheme:"red",onClick:()=>ed(e._id,"reject","regular"),children:"Reject"})]})]})})]},e._id))})]})}),(0,n.jsx)(R.K,{children:0===U.length?(0,n.jsxs)(r.F,{status:"info",children:[(0,n.jsx)(c._,{}),(0,n.jsx)(l.T,{children:"No experimental feature requests found. Requests will appear here when users apply for experimental features access."})]}):(0,n.jsxs)(W.X,{variant:"simple",children:[(0,n.jsx)(X.d,{children:(0,n.jsxs)(M.Tr,{children:[(0,n.jsx)(P.Th,{children:"User"}),(0,n.jsx)(P.Th,{children:"Feature"}),(0,n.jsx)(P.Th,{children:"Submitted"}),(0,n.jsx)(P.Th,{children:"Status"}),(0,n.jsx)(P.Th,{children:"Actions"})]})}),(0,n.jsx)(D.N,{children:U.map(e=>(0,n.jsxs)(M.Tr,{children:[(0,n.jsx)(q.Td,{children:(0,n.jsxs)(p.z,{children:[(0,n.jsx)(t.e,{size:"sm",name:e.username||e.userId}),(0,n.jsx)($.E,{children:e.username||e.userId})]})}),(0,n.jsx)(q.Td,{children:(0,n.jsx)(d.E,{colorScheme:"purple",children:e.feature})}),(0,n.jsx)(q.Td,{children:new Date(e.timestamp).toLocaleDateString()}),(0,n.jsx)(q.Td,{children:(0,n.jsx)(d.E,{colorScheme:"approved"===e.status?"green":"rejected"===e.status?"red":"yellow",children:e.status})}),(0,n.jsx)(q.Td,{children:(0,n.jsxs)(p.z,{spacing:2,children:[(0,n.jsx)(h.$,{size:"sm",colorScheme:"blue",onClick:()=>ea(e),children:"View"}),"pending"===e.status&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(h.$,{size:"sm",colorScheme:"green",onClick:()=>ed(e._id,"approve","experimental"),children:"Accept"}),(0,n.jsx)(h.$,{size:"sm",colorScheme:"red",onClick:()=>ed(e._id,"reject","experimental"),children:"Reject"})]})]})})]},e._id))})]})})]})]})]})}),(0,n.jsxs)(g.aF,{isOpen:es,onClose:en,size:"xl",children:[(0,n.jsx)(w.m,{}),(0,n.jsxs)(T.$,{children:[(0,n.jsx)(S.r,{children:(0,n.jsxs)(p.z,{children:[(0,n.jsx)(m.I,{as:er&&"feature"in er?K.XcJ:K.t69}),(0,n.jsx)($.E,{children:"Application Details"})]})}),(0,n.jsx)(E.s,{}),(0,n.jsx)(f.c,{pb:6,children:er&&(0,n.jsxs)(L.T,{align:"stretch",spacing:4,children:[(0,n.jsxs)(p.z,{children:[(0,n.jsx)(t.e,{name:er.username||er.userId}),(0,n.jsxs)(L.T,{align:"start",spacing:0,children:[(0,n.jsx)($.E,{fontWeight:"bold",children:er.username||er.userId}),(0,n.jsx)($.E,{fontSize:"sm",color:"gray.500",children:"feature"in er?"Experimental Request":"Role Application"})]})]}),(0,n.jsx)(j.c,{}),"feature"in er?(0,n.jsxs)(L.T,{align:"stretch",spacing:3,children:[(0,n.jsxs)(a.a,{children:[(0,n.jsx)($.E,{fontWeight:"bold",children:"Feature Requested:"}),(0,n.jsx)(d.E,{colorScheme:"purple",children:er.feature})]}),(0,n.jsxs)(a.a,{children:[(0,n.jsx)($.E,{fontWeight:"bold",children:"Reason:"}),(0,n.jsx)($.E,{children:er.reason})]}),(0,n.jsxs)(a.a,{children:[(0,n.jsx)($.E,{fontWeight:"bold",children:"Submitted:"}),(0,n.jsx)($.E,{children:new Date(er.timestamp).toLocaleString()})]})]}):(0,n.jsxs)(L.T,{align:"stretch",spacing:3,children:[(0,n.jsxs)(a.a,{children:[(0,n.jsx)($.E,{fontWeight:"bold",children:"Age:"}),(0,n.jsxs)($.E,{children:[er.age," years old"]})]}),(0,n.jsxs)(a.a,{children:[(0,n.jsx)($.E,{fontWeight:"bold",children:"Timezone:"}),(0,n.jsx)($.E,{children:er.timezone})]}),(0,n.jsxs)(a.a,{children:[(0,n.jsx)($.E,{fontWeight:"bold",children:"Hours per week:"}),(0,n.jsxs)($.E,{children:[er.hoursPerWeek," hours"]})]}),(0,n.jsxs)(a.a,{children:[(0,n.jsx)($.E,{fontWeight:"bold",children:"Motivation:"}),(0,n.jsx)($.E,{children:null==(e=er.answers)?void 0:e.statement})]}),er.extraInfo&&(0,n.jsxs)(a.a,{children:[(0,n.jsx)($.E,{fontWeight:"bold",children:"Additional Information:"}),(0,n.jsx)($.E,{children:er.extraInfo})]})]})]})})]})]})]})}},77087:(e,s,i)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/applications",function(){return i(73510)}])}},e=>{var s=s=>e(e.s=s);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,636,7398,1203,8792],()=>s(77087)),_N_E=e.O()}]);