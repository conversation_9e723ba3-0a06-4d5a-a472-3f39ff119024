(()=>{var e={};e.id=6934,e.ids=[636,3220,6934],e.modules={620:(e,r,t)=>{"use strict";t(45261)},4722:e=>{"use strict";e.exports=require("next-auth/react")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},14078:e=>{"use strict";e.exports=import("swr")},15806:e=>{"use strict";e.exports=require("next-auth/next")},20396:e=>{"use strict";e.exports=require("next-auth/providers/discord")},22326:e=>{"use strict";e.exports=require("react-dom")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},30067:e=>{"use strict";e.exports=import("@emotion/styled")},30468:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{_:()=>n});var s=t(8732),a=t(82015),i=t(59743),l=e([i]);function n({isOpen:e,onClose:r,onSave:t,server:o}){let[l,n]=(0,a.useState)(o?.name||""),[c,d]=(0,a.useState)(o?.description||""),[h,p]=(0,a.useState)(o?.host||""),[u,g]=(0,a.useState)(o?.port||25565),[x,b]=(0,a.useState)(o?.type||""),[m,j]=(0,a.useState)(o?.hasPassword||!1),[S,v]=(0,a.useState)(o?.password||""),[w,y]=(0,a.useState)(""),[f,C]=(0,a.useState)([]),[k,_]=(0,a.useState)(!1),[A,P]=(0,a.useState)(!1),$=(0,i.dj)(),B=async()=>{if(!l||!h||!u||!x)return void $({title:"Error",description:"Please fill in all required fields",status:"error",duration:5e3,isClosable:!0});if(m&&!S)return void $({title:"Error",description:"Password is required when password protection is enabled",status:"error",duration:5e3,isClosable:!0});try{let e={_id:o?._id,name:l,description:c,host:h,port:Number(u),type:x,hasPassword:m,password:m?S:void 0};await t(e),r()}catch(e){$({title:"Error",description:e instanceof Error?e.message:"Failed to save server",status:"error",duration:5e3,isClosable:!0})}},D=e=>{b(e.id),y(e.name),P(!1)};return(0,s.jsxs)(i.aF,{isOpen:e,onClose:r,size:"lg",children:[(0,s.jsx)(i.mH,{backdropFilter:"blur(10px)"}),(0,s.jsxs)(i.$m,{bg:"gray.800",border:"1px",borderColor:"whiteAlpha.200",children:[(0,s.jsx)(i.rQ,{color:"white",children:o?"Edit Server":"Add Server"}),(0,s.jsx)(i.s_,{}),(0,s.jsx)(i.cw,{children:(0,s.jsxs)(i.Tk,{spacing:6,children:[(0,s.jsxs)(i.MJ,{isRequired:!0,children:[(0,s.jsx)(i.lR,{color:"gray.200",children:"Server Name"}),(0,s.jsx)(i.pd,{value:l,onChange:e=>n(e.target.value),placeholder:"My Game Server",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,s.jsxs)(i.MJ,{children:[(0,s.jsx)(i.lR,{color:"gray.200",children:"Description"}),(0,s.jsx)(i.pd,{value:c,onChange:e=>d(e.target.value),placeholder:"Optional description",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,s.jsxs)(i.MJ,{isRequired:!0,children:[(0,s.jsx)(i.lR,{color:"gray.200",children:"Game Type"}),(0,s.jsx)(i.pd,{value:w,onChange:e=>{y(e.target.value),b("")},placeholder:"Search for a game...",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"},onFocus:()=>P(!0)}),k&&(0,s.jsx)(i.so,{justify:"center",mt:2,children:(0,s.jsx)(i.y$,{size:"sm",color:"blue.300"})}),A&&f.length>0&&(0,s.jsx)(i.az,{mt:2,border:"1px",borderColor:"whiteAlpha.200",borderRadius:"md",maxH:"200px",overflowY:"auto",bg:"gray.700",children:(0,s.jsx)(i.B8,{spacing:0,children:f.map(e=>(0,s.jsx)(i.ck,{p:2,cursor:"pointer",_hover:{bg:"whiteAlpha.100"},onClick:()=>D(e),color:"gray.200",children:(0,s.jsx)(i.EY,{children:e.name})},e.id))})}),x&&(0,s.jsxs)(i.EY,{mt:1,fontSize:"sm",color:"blue.300",children:["Selected: ",f.find(e=>e.id===x)?.name||x]})]}),(0,s.jsxs)(i.MJ,{isRequired:!0,children:[(0,s.jsx)(i.lR,{color:"gray.200",children:"Host"}),(0,s.jsx)(i.pd,{value:h,onChange:e=>p(e.target.value),placeholder:"localhost or IP address",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,s.jsxs)(i.MJ,{isRequired:!0,children:[(0,s.jsx)(i.lR,{color:"gray.200",children:"Port"}),(0,s.jsx)(i.Q7,{value:u,min:1,max:65535,children:(0,s.jsx)(i.OO,{onChange:e=>g(parseInt(e.target.value)||25565),bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})})]}),(0,s.jsx)(i.MJ,{children:(0,s.jsxs)(i.zt,{children:[(0,s.jsx)(i.lR,{color:"gray.200",mb:"0",children:"Password Protected"}),(0,s.jsx)(i.dO,{colorScheme:"blue",isChecked:m,onChange:e=>j(e.target.checked)})]})}),m&&(0,s.jsxs)(i.MJ,{isRequired:!0,children:[(0,s.jsx)(i.lR,{color:"gray.200",children:"Server Password"}),(0,s.jsx)(i.pd,{value:S,onChange:e=>v(e.target.value),placeholder:"Enter server password",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]})]})}),(0,s.jsxs)(i.jl,{gap:3,children:[(0,s.jsx)(i.$n,{variant:"ghost",onClick:r,color:"gray.300",_hover:{bg:"whiteAlpha.100"},children:"Cancel"}),(0,s.jsx)(i.$n,{colorScheme:"blue",onClick:B,_hover:{bg:"blue.500"},_active:{bg:"blue.600"},children:o?"Save Changes":"Add Server"})]})]})]})}i=(l.then?(await l)():l)[0],o()}catch(e){o(e)}})},33873:e=>{"use strict";e.exports=require("path")},39337:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{Badge:()=>s.E,Box:()=>a.a,Button:()=>i.$,Code:()=>l.C,Collapse:()=>n.S,Divider:()=>c.c,HStack:()=>d.z,Icon:()=>h.I,Stat:()=>p.r,StatLabel:()=>u.v,StatNumber:()=>g.k,Text:()=>x.E,Tooltip:()=>b.m,VStack:()=>m.T,useClipboard:()=>S.i,useColorModeValue:()=>j.dU});var s=t(25392),a=t(45200),i=t(77502),l=t(29180),n=t(10692),c=t(464),d=t(55197),h=t(50792),p=t(33593),u=t(71577),g=t(42650),x=t(87378),b=t(63792),m=t(17335),j=t(72982),S=t(91911);t(95339),t(620);var v=e([s,a,i,l,c,d,h,p,u,g,x,b,m]);[s,a,i,l,c,d,h,p,u,g,x,b,m]=v.then?(await v)():v,o()}catch(e){o(e)}})},40361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},56532:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{config:()=>x,default:()=>h,getServerSideProps:()=>g,getStaticPaths:()=>u,getStaticProps:()=>p,reportWebVitals:()=>b,routeModule:()=>y,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>w,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>m});var s=t(1292),a=t(58834),i=t(40786),l=t(83567),n=t(8077),c=t(80261),d=e([n,c]);[n,c]=d.then?(await d)():d;let h=(0,i.M)(c,"default"),p=(0,i.M)(c,"getStaticProps"),u=(0,i.M)(c,"getStaticPaths"),g=(0,i.M)(c,"getServerSideProps"),x=(0,i.M)(c,"config"),b=(0,i.M)(c,"reportWebVitals"),m=(0,i.M)(c,"unstable_getStaticProps"),j=(0,i.M)(c,"unstable_getStaticPaths"),S=(0,i.M)(c,"unstable_getStaticParams"),v=(0,i.M)(c,"unstable_getServerProps"),w=(0,i.M)(c,"unstable_getServerSideProps"),y=new s.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/gameservers",pathname:"/gameservers",bundlePath:"",filename:""},components:{App:n.default,Document:l.default},userland:c});o()}catch(e){o(e)}})},59743:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{$m:()=>x.$,$n:()=>a.$,B8:()=>h.B8,EY:()=>y.E,MJ:()=>l.MJ,OO:()=>S.OO,Q7:()=>S.Q7,Tk:()=>f.T,aF:()=>p.aF,az:()=>s.a,ck:()=>h.ck,cw:()=>u.c,dO:()=>w.d,dj:()=>C.d,jl:()=>b.j,lR:()=>n.l,mH:()=>j.m,pd:()=>d.p,rQ:()=>m.r,s_:()=>g.s,so:()=>i.s,y$:()=>v.y,zt:()=>c.z});var s=t(45200),a=t(77502),i=t(9888),l=t(23678),n=t(63957),c=t(55197),d=t(15376),h=t(36058),p=t(75460),u=t(42929),g=t(7394),x=t(89164),b=t(87346),m=t(95148),j=t(12725),S=t(71342),v=t(90088),w=t(24046),y=t(87378),f=t(17335),C=t(5978),k=e([s,a,i,l,n,c,d,h,p,u,g,x,b,m,j,S,v,w,y,f,C]);[s,a,i,l,n,c,d,h,p,u,g,x,b,m,j,S,v,w,y,f,C]=k.then?(await k)():k,o()}catch(e){o(e)}})},65542:e=>{"use strict";e.exports=require("next-auth")},66391:(e,r,t)=>{"use strict";t.d(r,{DIg:()=>o.DIg,OiG:()=>o.OiG,pBr:()=>o.pBr,qbC:()=>o.qbC,uO9:()=>o.uO9});var o=t(48648)},68087:(e,r,t)=>{"use strict";t.d(r,{CMH:()=>o.CMH,JhU:()=>o.JhU,__w:()=>o.__w,pBr:()=>o.pBr,paH:()=>o.paH,w_X:()=>o.w_X,x$1:()=>o.x$1});var o=t(48648)},72115:e=>{"use strict";e.exports=require("yaml")},72390:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{e:()=>d});var s=t(8732),a=t(82015),i=t(39337),l=t(68087),n=e([i]);i=(n.then?(await n)():n)[0];let c={minecraft:{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accent:"#68D391"},minecraftbe:{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accent:"#68D391"},csgo:{color:"orange",gradient:{from:"rgba(237, 137, 54, 0.4)",to:"rgba(237, 137, 54, 0.1)"},accent:"#ED8936"},valheim:{color:"red",gradient:{from:"rgba(245, 101, 101, 0.4)",to:"rgba(245, 101, 101, 0.1)"},accent:"#F56565"},rust:{color:"brown",gradient:{from:"rgba(193, 105, 79, 0.4)",to:"rgba(193, 105, 79, 0.1)"},accent:"#C1694F"},arkse:{color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"},accent:"#9F7AEA"},sdtd:{color:"yellow",gradient:{from:"rgba(236, 201, 75, 0.4)",to:"rgba(236, 201, 75, 0.1)"},accent:"#ECC94B"},default:{color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"},accent:"#4299E1"}},d=({server:e})=>{let[r,t]=(0,a.useState)(!1),{hasCopied:o,onCopy:n}=(0,i.useClipboard)(e.hasPassword?`Server: ${e.host}:${e.port}
Password: ${e.password}`:`${e.host}:${e.port}`),d=c[e.type.toLowerCase()]||c.default;(0,i.useColorModeValue)("gray.800","gray.800");let h=e.online?`${d.color}.400`:"red.400",p=e.online?d.color:"red",u=e.players?.length||0;return(0,s.jsx)(i.Box,{p:5,bg:`linear-gradient(135deg, ${d.gradient.from}, ${d.gradient.to})`,borderRadius:"xl",border:"2px",borderColor:h,width:"100%",position:"relative",overflow:"hidden",zIndex:1,transition:"all 0.2s",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(45deg, rgba(0,0,0,0.1) 0%, rgba(255,255,255,0.05) 100%)",zIndex:-1},_hover:{transform:"translateY(-2px)",boxShadow:`0 8px 20px ${d.gradient.from}`,borderColor:e.online?`${d.color}.300`:"red.300"},children:(0,s.jsxs)(i.VStack,{align:"stretch",spacing:4,children:[(0,s.jsx)(i.HStack,{justify:"space-between",children:(0,s.jsxs)(i.VStack,{align:"start",spacing:1,children:[(0,s.jsxs)(i.HStack,{children:[(0,s.jsx)(i.Text,{fontSize:"xl",fontWeight:"bold",color:"white",children:e.name||`${e.host}:${e.port}`}),e.hasPassword&&(0,s.jsx)(i.Tooltip,{label:"Password Protected",children:(0,s.jsx)("span",{children:(0,s.jsx)(i.Icon,{as:l.JhU,color:`${d.color}.200`})})})]}),(0,s.jsxs)(i.HStack,{spacing:2,children:[(0,s.jsx)(i.Badge,{colorScheme:p,fontSize:"sm",children:e.online?"Online":"Offline"}),(0,s.jsx)(i.Badge,{colorScheme:d.color,fontSize:"sm",children:e.type.toUpperCase()})]})]})}),e.description&&(0,s.jsx)(i.Text,{color:"gray.300",fontSize:"sm",children:e.description}),e.online?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.HStack,{spacing:8,justify:"space-around",children:[(0,s.jsxs)(i.Stat,{children:[(0,s.jsx)(i.StatLabel,{color:"gray.400",children:(0,s.jsxs)(i.HStack,{children:[(0,s.jsx)(l.x$1,{}),(0,s.jsx)(i.Text,{children:"Players"})]})}),(0,s.jsxs)(i.StatNumber,{color:"white",children:[u,"/",e.maxPlayers||"?"]})]}),e.map&&(0,s.jsxs)(i.Stat,{children:[(0,s.jsx)(i.StatLabel,{color:"gray.400",children:(0,s.jsxs)(i.HStack,{children:[(0,s.jsx)(l.pBr,{}),(0,s.jsx)(i.Text,{children:"Map"})]})}),(0,s.jsx)(i.StatNumber,{color:"white",fontSize:"lg",children:e.map})]}),e.ping&&(0,s.jsxs)(i.Stat,{children:[(0,s.jsx)(i.StatLabel,{color:"gray.400",children:"Ping"}),(0,s.jsxs)(i.StatNumber,{color:e.ping<100?`${d.color}.400`:e.ping<200?"yellow.400":"red.400",children:[e.ping,"ms"]})]})]}),(0,s.jsx)(i.Button,{size:"sm",variant:"ghost",colorScheme:d.color,onClick:()=>t(!r),leftIcon:(0,s.jsx)(l.__w,{}),children:r?"Hide Details":"Show Details"}),(0,s.jsx)(i.Collapse,{in:r,children:(0,s.jsxs)(i.VStack,{align:"stretch",spacing:3,pt:2,children:[(0,s.jsx)(i.Divider,{borderColor:"whiteAlpha.200"}),(0,s.jsxs)(i.Box,{children:[(0,s.jsx)(i.Text,{color:"gray.400",mb:2,fontWeight:"bold",children:"Connection Information"}),(0,s.jsx)(i.VStack,{align:"stretch",spacing:2,children:(0,s.jsxs)(i.HStack,{children:[(0,s.jsxs)(i.Code,{p:2,borderRadius:"md",bg:"gray.700",color:`${d.color}.300`,children:[e.host,":",e.port]}),e.hasPassword&&(0,s.jsxs)(i.Code,{p:2,borderRadius:"md",bg:"gray.700",color:`${d.color}.300`,children:["Password: ",e.password]}),(0,s.jsx)(i.Tooltip,{label:o?"Copied!":"Copy All",children:(0,s.jsx)(i.Button,{size:"sm",variant:"ghost",colorScheme:o?"green":d.color,onClick:n,children:(0,s.jsx)(i.Icon,{as:o?l.CMH:l.paH})})})]})})]}),(0,s.jsxs)(i.Box,{children:[(0,s.jsx)(i.Text,{color:"gray.400",mb:2,fontWeight:"bold",children:"How to Connect"}),(0,s.jsx)(i.Code,{display:"block",whiteSpace:"pre",p:3,borderRadius:"md",bg:"gray.700",color:`${d.color}.300`,children:(()=>{switch(e.type.toLowerCase()){case"minecraft":case"minecraftbe":return`1. Open Minecraft
2. Click "Multiplayer"
3. Click "Add Server"
4. Enter server address: ${e.host}:${e.port}${e.hasPassword?`
5. Enter Password: ${e.password}`:""}`;case"sdtd":return`1. Open 7 Days to Die
2. Click "Join Game"
3. Click "Server Browser"
4. Search for "${e.name}"
${e.hasPassword?`5. Enter Password: ${e.password}`:""}`;default:return`Connect using: ${e.host}:${e.port}${e.hasPassword?`
Password: ${e.password}`:""}`}})()})]})]})})]}):(0,s.jsx)(i.Text,{color:"red.400",children:e.error||"Server is offline"}),(0,s.jsxs)(i.HStack,{fontSize:"sm",color:"gray.500",spacing:2,children:[(0,s.jsx)(l.w_X,{}),(0,s.jsxs)(i.Text,{children:["Last updated: ",new Date(e.lastUpdated).toLocaleTimeString()]})]})]})})};o()}catch(e){o(e)}})},74075:e=>{"use strict";e.exports=require("zlib")},80261:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{default:()=>b,getServerSideProps:()=>m});var s=t(8732),a=t(82015),i=t.n(a),l=t(84643),n=t(66391),c=t(81011),d=t(72390),h=t(30468),p=t(15806),u=t(92546),g=t(4722),x=e([l,c,d,h]);function b(){let{data:e}=(0,g.useSession)(),r=e?.user?.isAdmin===!0,[t,o]=(0,a.useState)([]),[p,u]=(0,a.useState)(!0),[x,b]=(0,a.useState)(!1),[m,j]=(0,a.useState)(),[S,v]=(0,a.useState)(),{isOpen:w,onOpen:y,onClose:f}=(0,l.useDisclosure)(),{isOpen:C,onOpen:k,onClose:_}=(0,l.useDisclosure)(),A=i().useRef(null),P=(0,l.useToast)(),$=async()=>{try{b(!0);let e=await fetch("/api/gameservers/query");if(!e.ok)throw Error("Failed to fetch server status");let r=await e.json();o(r)}catch(e){o([]),P({title:"Error",description:e instanceof Error?e.message:"Failed to fetch server status",status:"error",duration:5e3,isClosable:!0})}finally{u(!1),b(!1)}},B=async e=>{try{let r,t=await fetch(`/api/gameservers/games?type=${encodeURIComponent(e.type)}`);if(!t.ok)throw Error("Invalid game type");let{type:o}=await t.json();e.type=o;let s=e._id?"PUT":"POST";if("PUT"===s){let{_id:t,...o}=e;r={id:t,...o}}else{let{_id:t,...o}=e;r=o}let a=await fetch("/api/gameservers/manage",{method:s,headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),i=await a.json();if(!a.ok)throw Error(i.error||"Failed to save server");$(),P({title:e._id?"Server Updated":"Server Added",description:e._id?"The server has been updated successfully":"The server has been added successfully",status:"success",duration:3e3,isClosable:!0})}catch(e){throw P({title:"Error",description:e instanceof Error?e.message:"Failed to save server",status:"error",duration:5e3,isClosable:!0}),e}},D=e=>{j({_id:e._id,name:e.name,type:e.type,host:e.host,port:e.port,description:e.description,hasPassword:e.hasPassword,password:e.password}),y()},T=e=>{j(e),k()},E=async()=>{if(m)try{let e={id:m._id},r=await fetch("/api/gameservers/manage",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),t=await r.json();if(!r.ok)throw Error(t.error||"Failed to delete server");o(e=>e.filter(e=>e._id!==m._id)),j(void 0),_(),P({title:"Success",description:"Server deleted successfully",status:"success",duration:5e3,isClosable:!0})}catch(e){P({title:"Error",description:e.message||"Failed to delete server",status:"error",duration:5e3,isClosable:!0})}};return(0,s.jsx)(c.A,{children:(0,s.jsxs)(l.Box,{w:"full",p:4,children:[(0,s.jsxs)(l.Box,{maxW:"4xl",mx:"auto",mb:8,mt:8,bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"green.400",boxShadow:"0 0 15px rgba(72, 187, 120, 0.4)",textAlign:"center",children:[(0,s.jsx)(l.Heading,{size:"2xl",bgGradient:"linear(to-r, green.300, teal.400)",bgClip:"text",mb:4,children:"Game Servers"}),(0,s.jsx)(l.Text,{color:"gray.300",fontSize:"lg",mb:6,children:"Monitor and manage your game servers in real-time"}),(0,s.jsxs)(l.HStack,{spacing:4,justify:"center",children:[r&&(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(n.OiG,{}),colorScheme:"green",onClick:()=>{j(void 0),y()},size:"md",variant:"solid",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Add Server"}),(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(n.DIg,{}),onClick:$,isLoading:x,loadingText:"Refreshing",size:"md",variant:"outline",colorScheme:"green",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Refresh Status"})]})]}),(0,s.jsx)(l.Box,{maxW:"7xl",mx:"auto",children:p?(0,s.jsxs)(l.VStack,{py:8,bg:"rgba(255,255,255,0.08)",rounded:"2xl",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",children:[(0,s.jsx)(l.Spinner,{size:"xl",color:"green.400"}),(0,s.jsx)(l.Text,{color:"gray.400",children:"Loading servers..."})]}):t&&0!==t.length?(0,s.jsx)(l.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:6,children:(t||[]).map((e,t)=>(0,s.jsxs)(l.Box,{position:"relative",transition:"all 0.2s",_hover:r?{transform:"translateY(-4px)","& > .server-actions":{opacity:1,transform:"translateY(0)"}}:void 0,children:[(0,s.jsx)(d.e,{server:e}),r&&(0,s.jsxs)(l.HStack,{className:"server-actions",position:"absolute",top:2,right:2,spacing:1,bg:"blackAlpha.800",p:1,borderRadius:"md",opacity:0,transform:"translateY(-4px)",transition:"all 0.2s",zIndex:2,backdropFilter:"blur(8px)",children:[(0,s.jsx)(l.IconButton,{"aria-label":"Edit server",icon:(0,s.jsx)(n.uO9,{}),size:"sm",variant:"ghost",colorScheme:"green",onClick:()=>D(e),_hover:{bg:"green.700"}}),(0,s.jsx)(l.IconButton,{"aria-label":"Delete server",icon:(0,s.jsx)(n.qbC,{}),size:"sm",variant:"ghost",colorScheme:"red",onClick:()=>T(e),_hover:{bg:"red.700"}})]})]},`${e._id||`${e.host}:${e.port}-${t}`}`))}):(0,s.jsxs)(l.VStack,{spacing:4,p:8,bg:"rgba(255,255,255,0.08)",rounded:"2xl",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",textAlign:"center",children:[(0,s.jsx)(l.Icon,{as:n.pBr,boxSize:12,color:"green.400"}),(0,s.jsx)(l.Text,{color:"gray.300",fontSize:"lg",children:"No game servers found"}),(0,s.jsx)(l.Text,{fontSize:"md",color:"gray.500",children:"Add your first game server to start monitoring"}),r&&(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(n.OiG,{}),colorScheme:"green",onClick:()=>{j(void 0),y()},size:"md",variant:"outline",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Add Your First Server"})]})}),r&&(0,s.jsx)(h._,{isOpen:w,onClose:f,server:m,onSave:B}),(0,s.jsx)(l.AlertDialog,{isOpen:C,leastDestructiveRef:A,onClose:_,isCentered:!0,children:(0,s.jsx)(l.AlertDialogOverlay,{backdropFilter:"blur(10px)",children:(0,s.jsxs)(l.AlertDialogContent,{bg:"gray.800",border:"1px",borderColor:"whiteAlpha.200",children:[(0,s.jsx)(l.AlertDialogHeader,{fontSize:"lg",fontWeight:"bold",color:"white",children:"Delete Server"}),(0,s.jsxs)(l.AlertDialogBody,{color:"gray.300",children:["Are you sure you want to delete ",m?.name||`${m?.host}:${m?.port}`,"? This action cannot be undone."]}),(0,s.jsxs)(l.AlertDialogFooter,{gap:3,children:[(0,s.jsx)(l.Button,{ref:A,onClick:_,variant:"ghost",color:"gray.300",children:"Cancel"}),(0,s.jsx)(l.Button,{colorScheme:"red",onClick:E,_hover:{bg:"red.600"},_active:{bg:"red.700"},children:"Delete"})]})]})})})]})})}[l,c,d,h]=x.then?(await x)():x;let m=async e=>await (0,p.getServerSession)(e.req,e.res,u.N)?{props:{}}:{redirect:{destination:"/signin",permanent:!1}};o()}catch(e){o(e)}})},82015:e=>{"use strict";e.exports=require("react")},84643:(e,r,t)=>{"use strict";t.a(e,async(e,o)=>{try{t.d(r,{AlertDialog:()=>s.Lt,AlertDialogBody:()=>a.c,AlertDialogContent:()=>s.EO,AlertDialogFooter:()=>i.j,AlertDialogHeader:()=>l.r,AlertDialogOverlay:()=>n.m,Box:()=>c.a,Button:()=>d.$,HStack:()=>h.z,Heading:()=>p.D,Icon:()=>u.I,IconButton:()=>g.K,SimpleGrid:()=>x.r,Spinner:()=>b.y,Text:()=>m.E,VStack:()=>j.T,useDisclosure:()=>v.j,useToast:()=>S.d});var s=t(70288),a=t(42929),i=t(87346),l=t(95148),n=t(12725),c=t(45200),d=t(77502),h=t(55197),p=t(30519),u=t(50792),g=t(23476),x=t(67981),b=t(90088),m=t(87378),j=t(17335),S=t(5978),v=t(66646);t(9436),t(25035);var w=e([s,a,i,l,n,c,d,h,p,u,g,x,b,m,j,S]);[s,a,i,l,n,c,d,h,p,u,g,x,b,m,j,S]=w.then?(await w)():w,o()}catch(e){o(e)}})},88455:e=>{"use strict";e.exports=import("@emotion/react")},91911:(e,r,t)=>{"use strict";t.d(r,{i:()=>o.i});var o=t(39354)},95339:()=>{}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[2457,9784,6021,3786,8740,9498,2142,1283,589,6185,4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(56532));module.exports=o})();