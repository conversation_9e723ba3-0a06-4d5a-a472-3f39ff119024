(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8111],{5357:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/tickets",function(){return r(9973)}])},9973:(e,t,r)=>{"use strict";r.r(t),r.d(t,{__N_SSP:()=>L,default:()=>P});var i=r(94513),s=r(43700),c=r(79028),n=r(64349),l=r(7476),o=r(1871),a=r(78813),d=r(29484),h=r(84748),x=r(82824),p=r(46312),j=r(84482),u=r(5130),k=r(10246),g=r(32338),f=r(80456),b=r(3037),w=r(84622);r(75632),r(82273);var T=r(94285),y=r(97119),m=r(31862),C=r(15975),S=r(46949),_=r(81139),E=r(95066),v=r(53083),F=r(24490),z=r(31840),N=r(29607),D=r(35339),I=r(52442),O=r(7836);function $(e){let{isOpen:t,onClose:r,onSuccess:s}=e,c=(0,O.d)(),[l,o]=(0,T.useState)(""),[a,d]=(0,T.useState)("support"),[h,x]=(0,T.useState)(!1),p=async()=>{x(!0);try{let e=await fetch("/api/discord/tickets",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({reason:l,category:a})});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to create ticket")}c({title:"Ticket Created",description:"Your support ticket has been opened.",status:"success",duration:3e3}),o(""),d("support"),s(),r()}catch(e){c({title:"Error",description:e.message||"Failed to create ticket",status:"error",duration:5e3})}finally{x(!1)}};return(0,i.jsxs)(S.aF,{isOpen:t,onClose:r,size:"lg",scrollBehavior:"inside",children:[(0,i.jsx)(N.m,{backdropFilter:"blur(10px)"}),(0,i.jsxs)(v.$,{bg:"gray.800",children:[(0,i.jsx)(z.r,{children:"Create Support Ticket"}),(0,i.jsx)(E.s,{}),(0,i.jsxs)(_.c,{children:[(0,i.jsxs)(m.MJ,{mb:4,children:[(0,i.jsx)(C.l,{children:"Category"}),(0,i.jsxs)(D.l,{value:a,onChange:e=>d(e.target.value),children:[(0,i.jsx)("option",{value:"support",children:"Support"}),(0,i.jsx)("option",{value:"18plus",children:"18+"}),(0,i.jsx)("option",{value:"other",children:"Other"})]})]}),(0,i.jsxs)(m.MJ,{children:[(0,i.jsx)(C.l,{children:"Describe your issue"}),(0,i.jsx)(I.T,{placeholder:"I need help with...",value:l,onChange:e=>o(e.target.value),rows:5})]})]}),(0,i.jsxs)(F.j,{children:[(0,i.jsx)(n.$,{mr:3,variant:"ghost",onClick:r,children:"Cancel"}),(0,i.jsx)(n.$,{colorScheme:"blue",onClick:p,isLoading:h,children:"Create Ticket"})]})]})]})}var A=r(35044),L=!0;function P(e){let{isAdmin:t}=e,[r,m]=(0,T.useState)([]),[C,S]=(0,T.useState)(!0),{isOpen:_,onOpen:E,onClose:v}=(0,w.j)(),F=async()=>{S(!0);try{let e=await fetch("/api/discord/tickets");if(!e.ok)throw Error("Failed to fetch tickets");let t=await e.json();m(t)}catch(e){}finally{S(!1)}};return(0,T.useEffect)(()=>{F()},[]),(0,i.jsx)(y.A,{children:(0,i.jsxs)(l.m,{maxW:"container.xl",py:8,children:[(0,i.jsxs)(b.T,{spacing:8,align:"stretch",children:[(0,i.jsxs)(c.a,{bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"blue.400",boxShadow:"0 0 15px rgba(66, 153, 225, 0.4)",textAlign:"center",children:[(0,i.jsx)(a.D,{size:"2xl",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",mb:4,children:"Support Tickets"}),(0,i.jsx)(u.E,{color:"gray.300",fontSize:"lg",children:"Open new tickets or review existing ones"})]}),(0,i.jsx)(o.z,{justify:"flex-end",children:(0,i.jsx)(n.$,{leftIcon:(0,i.jsx)(d.I,{as:A.FiPlus}),colorScheme:"blue",onClick:E,children:"Create Ticket"})}),(0,i.jsx)(c.a,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",rounded:"lg",p:4,children:C?(0,i.jsx)(o.z,{justify:"center",py:10,children:(0,i.jsx)(h.y,{size:"lg"})}):0===r.length?(0,i.jsx)(u.E,{textAlign:"center",py:10,color:"gray.300",children:"You have no tickets yet."}):(0,i.jsxs)(x.X,{variant:"simple",children:[(0,i.jsx)(g.d,{children:(0,i.jsxs)(f.Tr,{children:[(0,i.jsx)(k.Th,{children:"ID"}),(0,i.jsx)(k.Th,{children:"Reason"}),(0,i.jsx)(k.Th,{children:"Status"}),(0,i.jsx)(k.Th,{children:"Created"}),(0,i.jsx)(k.Th,{children:"Actions"})]})}),(0,i.jsx)(p.N,{children:r.map(e=>(0,i.jsxs)(f.Tr,{children:[(0,i.jsx)(j.Td,{children:e._id.toString().slice(-6)}),(0,i.jsx)(j.Td,{maxW:"300px",children:(0,i.jsx)(u.E,{isTruncated:!0,title:e.reason,children:e.reason||"No reason provided"})}),(0,i.jsx)(j.Td,{children:(0,i.jsx)(s.E,{colorScheme:"open"===e.status?"green":"red",children:e.status})}),(0,i.jsx)(j.Td,{children:new Date(e.createdAt).toLocaleString()}),(0,i.jsx)(j.Td,{children:(0,i.jsxs)(o.z,{spacing:2,children:[e.discordLink&&(0,i.jsx)(n.$,{as:"a",href:e.discordLink,target:"_blank",size:"sm",leftIcon:(0,i.jsx)(d.I,{as:A.FiExternalLink}),children:"Discord"}),"open"===e.status&&(0,i.jsx)(n.$,{size:"sm",colorScheme:"yellow",onClick:async()=>{if(window.confirm("Close this ticket?"))try{if(!(await fetch("/api/discord/tickets/".concat(e._id),{method:"PATCH"})).ok)throw Error("Failed to close ticket");F()}catch(e){}},children:"Close"}),t&&"closed"===e.status&&(0,i.jsx)(n.$,{size:"sm",colorScheme:"red",onClick:async()=>{if(window.confirm("Delete this ticket? This is irreversible."))try{if(!(await fetch("/api/discord/tickets/".concat(e._id),{method:"DELETE"})).ok)throw Error("Failed to delete ticket");F()}catch(e){}},children:"Delete"}),"closed"===e.status&&(0,i.jsx)(n.$,{as:"a",href:"/api/discord/tickets/".concat(e._id,"/transcript"),size:"sm",colorScheme:"green",leftIcon:(0,i.jsx)(d.I,{as:A.FiExternalLink}),target:"_blank",children:"Transcript"})]})})]},e._id))})]})})]}),(0,i.jsx)($,{isOpen:_,onClose:v,onSuccess:F})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,636,7398,1203,8792],()=>t(5357)),_N_E=e.O()}]);