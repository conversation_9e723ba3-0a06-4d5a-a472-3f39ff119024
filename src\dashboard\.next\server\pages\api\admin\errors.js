"use strict";(()=>{var e={};e.id=2462,e.ids=[2462],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},43002:(e,t,r)=>{r.r(t),r.d(t,{config:()=>w,default:()=>f,routeModule:()=>x});var o={};r.r(o),r.d(o,{default:()=>h});var a=r(93433),n=r(20264),s=r(20584),i=r(15806),u=r(94506),d=r(98580),l=r(12518);let c=null,g=d.dashboardConfig.database?.url||"mongodb://localhost:27017",m=d.dashboardConfig.database?.name||"discord_bot";async function p(){return c||(c=await l.MongoClient.connect(g,{...d.dashboardConfig.database?.options||{}})),c.db(m)}async function h(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let r=await (0,i.getServerSession)(e,t,u.authOptions);if(!r)return t.status(401).json({error:"Unauthorized"});if(!r.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});let o=await p(),a=new Date,n=new Date(a.getFullYear(),a.getMonth(),a.getDate()),s=new Date(n.getTime()+864e5),d=new Date(n.getTime()-864e5),l=new Date(d.getFullYear(),d.getMonth(),d.getDate()),c=await o.collection("error_logs").find({}).sort({timestamp:-1}).limit(100).toArray().catch(()=>[]),g=await o.collection("error_logs").countDocuments({}).catch(()=>0),m=await o.collection("error_logs").countDocuments({timestamp:{$gte:n,$lt:s}}).catch(()=>0),h=await o.collection("error_logs").countDocuments({timestamp:{$gte:l,$lt:n}}).catch(()=>0),f="stable";m>h?f="up":m<h&&(f="down");let w=await o.collection("error_logs").aggregate([{$group:{_id:"$type",count:{$sum:1}}}]).toArray().catch(()=>[]),x={};w.forEach(e=>{x[e._id]=e.count});let b={totalErrors:g,errorsToday:m,errorsByType:x,recentTrend:f};t.status(200).json({errors:c.map(e=>({...e,_id:e._id.toString()})),stats:b})}catch(e){t.status(500).json({error:"Internal server error",details:e.message})}}let f=(0,s.M)(o,"default"),w=(0,s.M)(o,"config"),x=new a.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/admin/errors",pathname:"/api/admin/errors",bundlePath:"",filename:""},userland:o})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(43002));module.exports=o})();