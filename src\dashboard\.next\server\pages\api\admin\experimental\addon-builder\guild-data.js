"use strict";(()=>{var e={};e.id=9309,e.ids=[9309],e.modules={4722:e=>{e.exports=require("next-auth/react")},27264:(e,o,r)=>{r.r(o),r.d(o,{config:()=>p,default:()=>l,routeModule:()=>u});var a={};r.r(a),r.d(a,{default:()=>n});var i=r(93433),t=r(20264),s=r(20584),d=r(4722);async function n(e,o){if("GET"!==e.method)return o.status(405).json({error:"Method not allowed"});try{let r=await (0,d.getSession)({req:e});if(!r?.user||"933023999770918932"!==r.user.id)return o.status(403).json({error:"Unauthorized"});let a=`${e.headers["x-forwarded-proto"]||"http"}://${e.headers.host}`,[i,t,s,n]=await Promise.all([fetch(`${a}/api/discord/roles`,{headers:{Cookie:e.headers.cookie||""}}),fetch(`${a}/api/discord/channels`,{headers:{Cookie:e.headers.cookie||""}}),fetch(`${a}/api/discord/guild`,{headers:{Cookie:e.headers.cookie||""}}),fetch(`${a}/api/discord/users`,{headers:{Cookie:e.headers.cookie||""}}).catch(()=>null)]);if(!i.ok||!t.ok||!s.ok)throw Error("Failed to fetch data from Discord API endpoints");let[l,p,u]=await Promise.all([i.json(),t.json(),s.json()]),c=[];if(n&&n.ok)try{c=await n.json()}catch(e){}let m=p.filter(e=>"GUILD_TEXT"===e.type||"GUILD_VOICE"===e.type||"GUILD_CATEGORY"===e.type).map(e=>({id:e.id,name:e.name,type:"GUILD_TEXT"===e.type?"text":"GUILD_VOICE"===e.type?"voice":"category",position:e.position||0})).sort((e,o)=>e.position-o.position),h=l.filter(e=>!e.managed&&"@everyone"!==e.name).map(e=>({id:e.id,name:e.name,color:e.color,position:e.position,permissions:e.permissions||[]})).sort((e,o)=>o.position-e.position),f=Array.isArray(c)?c.filter(e=>!e.bot).map(e=>({id:e.id,username:e.username,displayName:e.displayName||e.username,avatar:e.avatar,roles:e.roles||[],joinedAt:e.joinedAt})).slice(0,20):[];o.status(200).json({guild:{id:u.id,name:u.name,icon:u.icon,memberCount:u.memberCount,ownerId:u.ownerId,createdAt:u.createdAt},channels:m,roles:h,members:f})}catch(e){o.status(500).json({error:"Failed to fetch guild data",details:e instanceof Error?e.message:"Unknown error"})}}let l=(0,s.M)(a,"default"),p=(0,s.M)(a,"config"),u=new i.PagesAPIRouteModule({definition:{kind:t.A.PAGES_API,page:"/api/admin/experimental/addon-builder/guild-data",pathname:"/api/admin/experimental/addon-builder/guild-data",bundlePath:"",filename:""},userland:a})},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var o=require("../../../../../webpack-api-runtime.js");o.C(e);var r=e=>o(o.s=e),a=o.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(27264));module.exports=a})();