"use strict";(()=>{var e={};e.id=8255,e.ids=[8255],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},78351:(e,t,r)=>{r.r(t),r.d(t,{config:()=>c,default:()=>l,routeModule:()=>f});var i={};r.r(i),r.d(i,{default:()=>p});var a=r(93433),s=r(20264),n=r(20584),o=r(15806),d=r(94506),u=r(72290);async function p(e,t){let r=await (0,o.getServerSession)(e,t,d.authOptions);if(!r?.user?.id)return t.status(401).json({error:"Unauthorized"});if(!r.user?.isAdmin)return t.status(403).json({error:"Admin access required"});let i=(await (0,u.L)()).collection("custom_applications");if("GET"===e.method)try{let e=(await i.find({}).toArray()).map(({_id:e,...t})=>t);return t.status(200).json({applications:e})}catch(e){return t.status(500).json({error:"Failed to fetch applications"})}if("POST"===e.method)try{let a=e.body;if(!a.title||!a.description)return t.status(400).json({error:"Title and description are required"});if(!a.id)return t.status(400).json({error:"Application ID is required"});if(await i.findOne({id:a.id})){let e={id:a.id,title:a.title,description:a.description,color:a.color,icon:a.icon,enabled:a.enabled,questions:a.questions,settings:a.settings,updatedAt:new Date};return await i.updateOne({id:a.id},{$set:e}),t.status(200).json({application:e})}{let e={...a,createdAt:new Date,updatedAt:new Date,createdBy:r.user.id};return await i.insertOne(e),t.status(201).json({application:e})}}catch(e){return t.status(500).json({error:"Failed to save application"})}return t.status(405).json({error:"Method not allowed"})}let l=(0,n.M)(i,"default"),c=(0,n.M)(i,"config"),f=new a.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/admin/applications-builder",pathname:"/api/admin/applications-builder",bundlePath:"",filename:""},userland:i})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(78351));module.exports=i})();