"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/applications-builder";
exports.ids = ["pages/api/admin/applications-builder"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications-builder%5Cindex.ts&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications-builder%5Cindex.ts&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_applications_builder_index_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\applications-builder\\index.ts */ \"(api-node)/./pages/api/admin/applications-builder/index.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_applications_builder_index_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_applications_builder_index_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/applications-builder\",\n        pathname: \"/api/admin/applications-builder\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_admin_applications_builder_index_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications-builder%5Cindex.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./apiHelpers/db.ts":
/*!**************************!*\
  !*** ./apiHelpers/db.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDb: () => (/* binding */ getDb)\n/* harmony export */ });\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/config */ \"(api-node)/./core/config.ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_1__);\n// Simple Mongo helper for API routes\n\n\nlet cachedClient = null;\nlet cachedDb = null;\nasync function getDb() {\n    if (cachedDb) return cachedDb;\n    const mongoUrl = _core_config__WEBPACK_IMPORTED_MODULE_0__.dashboardConfig.database?.url || 'mongodb://localhost:27017';\n    const dbName = _core_config__WEBPACK_IMPORTED_MODULE_0__.dashboardConfig.database?.name || 'discord_bot';\n    if (!cachedClient) {\n        cachedClient = await mongodb__WEBPACK_IMPORTED_MODULE_1__.MongoClient.connect(mongoUrl, {\n            ..._core_config__WEBPACK_IMPORTED_MODULE_0__.dashboardConfig.database?.options || {}\n        });\n    }\n    cachedDb = cachedClient.db(dbName);\n    return cachedDb;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2FwaUhlbHBlcnMvZGIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLHFDQUFxQztBQUNZO0FBQ1A7QUFFMUMsSUFBSUUsZUFBbUM7QUFDdkMsSUFBSUMsV0FBc0I7QUFFbkIsZUFBZUM7SUFDcEIsSUFBSUQsVUFBVSxPQUFPQTtJQUVyQixNQUFNRSxXQUFXTCx5REFBZUEsQ0FBQ00sUUFBUSxFQUFFQyxPQUFPO0lBQ2xELE1BQU1DLFNBQVNSLHlEQUFlQSxDQUFDTSxRQUFRLEVBQUVHLFFBQVE7SUFFakQsSUFBSSxDQUFDUCxjQUFjO1FBQ2pCQSxlQUFlLE1BQU1ELGdEQUFXQSxDQUFDUyxPQUFPLENBQUNMLFVBQVU7WUFDakQsR0FBSUwseURBQWVBLENBQUNNLFFBQVEsRUFBRUssV0FBVyxDQUFDLENBQUM7UUFDN0M7SUFDRjtJQUNBUixXQUFXRCxhQUFhVSxFQUFFLENBQUNKO0lBQzNCLE9BQU9MO0FBQ1QiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXHNyY1xcZGFzaGJvYXJkXFxhcGlIZWxwZXJzXFxkYi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTaW1wbGUgTW9uZ28gaGVscGVyIGZvciBBUEkgcm91dGVzXHJcbmltcG9ydCB7IGRhc2hib2FyZENvbmZpZyB9IGZyb20gJy4uL2NvcmUvY29uZmlnJztcclxuaW1wb3J0IHsgTW9uZ29DbGllbnQsIERiIH0gZnJvbSAnbW9uZ29kYic7XHJcblxyXG5sZXQgY2FjaGVkQ2xpZW50OiBNb25nb0NsaWVudCB8IG51bGwgPSBudWxsO1xyXG5sZXQgY2FjaGVkRGI6IERiIHwgbnVsbCA9IG51bGw7XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0RGIoKTogUHJvbWlzZTxEYj4ge1xyXG4gIGlmIChjYWNoZWREYikgcmV0dXJuIGNhY2hlZERiO1xyXG5cclxuICBjb25zdCBtb25nb1VybCA9IGRhc2hib2FyZENvbmZpZy5kYXRhYmFzZT8udXJsIHx8ICdtb25nb2RiOi8vbG9jYWxob3N0OjI3MDE3JztcclxuICBjb25zdCBkYk5hbWUgPSBkYXNoYm9hcmRDb25maWcuZGF0YWJhc2U/Lm5hbWUgfHwgJ2Rpc2NvcmRfYm90JztcclxuXHJcbiAgaWYgKCFjYWNoZWRDbGllbnQpIHtcclxuICAgIGNhY2hlZENsaWVudCA9IGF3YWl0IE1vbmdvQ2xpZW50LmNvbm5lY3QobW9uZ29VcmwsIHtcclxuICAgICAgLi4uKGRhc2hib2FyZENvbmZpZy5kYXRhYmFzZT8ub3B0aW9ucyB8fCB7fSksXHJcbiAgICB9KTtcclxuICB9XHJcbiAgY2FjaGVkRGIgPSBjYWNoZWRDbGllbnQuZGIoZGJOYW1lKTtcclxuICByZXR1cm4gY2FjaGVkRGI7XHJcbn0gIl0sIm5hbWVzIjpbImRhc2hib2FyZENvbmZpZyIsIk1vbmdvQ2xpZW50IiwiY2FjaGVkQ2xpZW50IiwiY2FjaGVkRGIiLCJnZXREYiIsIm1vbmdvVXJsIiwiZGF0YWJhc2UiLCJ1cmwiLCJkYk5hbWUiLCJuYW1lIiwiY29ubmVjdCIsIm9wdGlvbnMiLCJkYiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./apiHelpers/db.ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/admin/applications-builder/index.ts":
/*!*******************************************************!*\
  !*** ./pages/api/admin/applications-builder/index.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _apiHelpers_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../apiHelpers/db */ \"(api-node)/./apiHelpers/db.ts\");\n\n\n\nasync function handler(req, res) {\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    if (!session?.user?.id) {\n        return res.status(401).json({\n            error: 'Unauthorized'\n        });\n    }\n    // Check if user is admin\n    const isAdmin = session.user?.isAdmin;\n    if (!isAdmin) {\n        return res.status(403).json({\n            error: 'Admin access required'\n        });\n    }\n    const db = await (0,_apiHelpers_db__WEBPACK_IMPORTED_MODULE_2__.getDb)();\n    const collection = db.collection('custom_applications');\n    if (req.method === 'GET') {\n        try {\n            const applications = await collection.find({}).toArray();\n            // Remove MongoDB _id field from response to avoid update conflicts\n            const cleanApplications = applications.map(({ _id, ...app })=>app);\n            return res.status(200).json({\n                applications: cleanApplications\n            });\n        } catch (error) {\n            console.error('Error fetching applications:', error);\n            return res.status(500).json({\n                error: 'Failed to fetch applications'\n            });\n        }\n    }\n    if (req.method === 'POST') {\n        try {\n            const applicationData = req.body;\n            // Validate required fields\n            if (!applicationData.title || !applicationData.description) {\n                return res.status(400).json({\n                    error: 'Title and description are required'\n                });\n            }\n            if (!applicationData.id) {\n                return res.status(400).json({\n                    error: 'Application ID is required'\n                });\n            }\n            // Check if application ID already exists\n            const existingApp = await collection.findOne({\n                id: applicationData.id\n            });\n            if (existingApp) {\n                // Update existing application - exclude _id field\n                const updatedApp = {\n                    id: applicationData.id,\n                    title: applicationData.title,\n                    description: applicationData.description,\n                    color: applicationData.color,\n                    icon: applicationData.icon,\n                    enabled: applicationData.enabled,\n                    questions: applicationData.questions,\n                    settings: applicationData.settings,\n                    updatedAt: new Date()\n                };\n                const result = await collection.updateOne({\n                    id: applicationData.id\n                }, {\n                    $set: updatedApp\n                });\n                return res.status(200).json({\n                    application: updatedApp\n                });\n            } else {\n                // Create new application\n                const newApp = {\n                    ...applicationData,\n                    createdAt: new Date(),\n                    updatedAt: new Date(),\n                    createdBy: session.user.id\n                };\n                const result = await collection.insertOne(newApp);\n                return res.status(201).json({\n                    application: newApp\n                });\n            }\n        } catch (error) {\n            console.error('Error saving application:', error);\n            return res.status(500).json({\n                error: 'Failed to save application'\n            });\n        }\n    }\n    return res.status(405).json({\n        error: 'Method not allowed'\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL3BhZ2VzL2FwaS9hZG1pbi9hcHBsaWNhdGlvbnMtYnVpbGRlci9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNrRDtBQUNLO0FBQ0w7QUEwQ25DLGVBQWVHLFFBQVFDLEdBQW1CLEVBQUVDLEdBQW9CO0lBQzdFLE1BQU1DLFVBQVUsTUFBTU4sZ0VBQWdCQSxDQUFDSSxLQUFLQyxLQUFLSix3REFBV0E7SUFDNUQsSUFBSSxDQUFDSyxTQUFTQyxNQUFNQyxJQUFJO1FBQ3RCLE9BQU9ILElBQUlJLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7WUFBRUMsT0FBTztRQUFlO0lBQ3REO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU1DLFVBQVdOLFFBQVFDLElBQUksRUFBVUs7SUFDdkMsSUFBSSxDQUFDQSxTQUFTO1FBQ1osT0FBT1AsSUFBSUksTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztZQUFFQyxPQUFPO1FBQXdCO0lBQy9EO0lBRUEsTUFBTUUsS0FBSyxNQUFNWCxxREFBS0E7SUFDdEIsTUFBTVksYUFBYUQsR0FBR0MsVUFBVSxDQUFDO0lBRWpDLElBQUlWLElBQUlXLE1BQU0sS0FBSyxPQUFPO1FBQ3hCLElBQUk7WUFDRixNQUFNQyxlQUFlLE1BQU1GLFdBQVdHLElBQUksQ0FBQyxDQUFDLEdBQUdDLE9BQU87WUFDdEQsbUVBQW1FO1lBQ25FLE1BQU1DLG9CQUFvQkgsYUFBYUksR0FBRyxDQUFDLENBQUMsRUFBRUMsR0FBRyxFQUFFLEdBQUdDLEtBQUssR0FBS0E7WUFDaEUsT0FBT2pCLElBQUlJLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7Z0JBQUVNLGNBQWNHO1lBQWtCO1FBQ2hFLEVBQUUsT0FBT1IsT0FBTztZQUNkWSxRQUFRWixLQUFLLENBQUMsZ0NBQWdDQTtZQUM5QyxPQUFPTixJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQStCO1FBQ3RFO0lBQ0Y7SUFFQSxJQUFJUCxJQUFJVyxNQUFNLEtBQUssUUFBUTtRQUN6QixJQUFJO1lBQ0YsTUFBTVMsa0JBQWtCcEIsSUFBSXFCLElBQUk7WUFFaEMsMkJBQTJCO1lBQzNCLElBQUksQ0FBQ0QsZ0JBQWdCRSxLQUFLLElBQUksQ0FBQ0YsZ0JBQWdCRyxXQUFXLEVBQUU7Z0JBQzFELE9BQU90QixJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO29CQUFFQyxPQUFPO2dCQUFxQztZQUM1RTtZQUVBLElBQUksQ0FBQ2EsZ0JBQWdCaEIsRUFBRSxFQUFFO2dCQUN2QixPQUFPSCxJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO29CQUFFQyxPQUFPO2dCQUE2QjtZQUNwRTtZQUVBLHlDQUF5QztZQUN6QyxNQUFNaUIsY0FBYyxNQUFNZCxXQUFXZSxPQUFPLENBQUM7Z0JBQUVyQixJQUFJZ0IsZ0JBQWdCaEIsRUFBRTtZQUFDO1lBRXRFLElBQUlvQixhQUFhO2dCQUNmLGtEQUFrRDtnQkFDbEQsTUFBTUUsYUFBYTtvQkFDakJ0QixJQUFJZ0IsZ0JBQWdCaEIsRUFBRTtvQkFDdEJrQixPQUFPRixnQkFBZ0JFLEtBQUs7b0JBQzVCQyxhQUFhSCxnQkFBZ0JHLFdBQVc7b0JBQ3hDSSxPQUFPUCxnQkFBZ0JPLEtBQUs7b0JBQzVCQyxNQUFNUixnQkFBZ0JRLElBQUk7b0JBQzFCQyxTQUFTVCxnQkFBZ0JTLE9BQU87b0JBQ2hDQyxXQUFXVixnQkFBZ0JVLFNBQVM7b0JBQ3BDQyxVQUFVWCxnQkFBZ0JXLFFBQVE7b0JBQ2xDQyxXQUFXLElBQUlDO2dCQUNqQjtnQkFFQSxNQUFNQyxTQUFTLE1BQU14QixXQUFXeUIsU0FBUyxDQUN2QztvQkFBRS9CLElBQUlnQixnQkFBZ0JoQixFQUFFO2dCQUFDLEdBQ3pCO29CQUFFZ0MsTUFBTVY7Z0JBQVc7Z0JBR3JCLE9BQU96QixJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO29CQUFFK0IsYUFBYVg7Z0JBQVc7WUFDeEQsT0FBTztnQkFDTCx5QkFBeUI7Z0JBQ3pCLE1BQU1ZLFNBQVM7b0JBQ2IsR0FBR2xCLGVBQWU7b0JBQ2xCbUIsV0FBVyxJQUFJTjtvQkFDZkQsV0FBVyxJQUFJQztvQkFDZk8sV0FBV3RDLFFBQVFDLElBQUksQ0FBQ0MsRUFBRTtnQkFDNUI7Z0JBRUEsTUFBTThCLFNBQVMsTUFBTXhCLFdBQVcrQixTQUFTLENBQUNIO2dCQUMxQyxPQUFPckMsSUFBSUksTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztvQkFBRStCLGFBQWFDO2dCQUFPO1lBQ3BEO1FBQ0YsRUFBRSxPQUFPL0IsT0FBTztZQUNkWSxRQUFRWixLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxPQUFPTixJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQTZCO1FBQ3BFO0lBQ0Y7SUFFQSxPQUFPTixJQUFJSSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO1FBQUVDLE9BQU87SUFBcUI7QUFDNUQiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXHNyY1xcZGFzaGJvYXJkXFxwYWdlc1xcYXBpXFxhZG1pblxcYXBwbGljYXRpb25zLWJ1aWxkZXJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTmV4dEFwaVJlcXVlc3QsIE5leHRBcGlSZXNwb25zZSB9IGZyb20gJ25leHQnO1xyXG5pbXBvcnQgeyBnZXRTZXJ2ZXJTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoL25leHQnO1xyXG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJy4uLy4uL2F1dGgvWy4uLm5leHRhdXRoXSc7XHJcbmltcG9ydCB7IGdldERiIH0gZnJvbSAnLi4vLi4vLi4vLi4vYXBpSGVscGVycy9kYic7XHJcblxyXG5pbnRlcmZhY2UgQXBwbGljYXRpb25UeXBlIHtcclxuICBpZDogc3RyaW5nO1xyXG4gIHRpdGxlOiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICBjb2xvcjogc3RyaW5nO1xyXG4gIGljb246IHN0cmluZztcclxuICBlbmFibGVkOiBib29sZWFuO1xyXG4gIHF1ZXN0aW9uczogUXVlc3Rpb25bXTtcclxuICBzZXR0aW5nczogQXBwbGljYXRpb25TZXR0aW5ncztcclxuICBjcmVhdGVkQXQ6IERhdGU7XHJcbiAgdXBkYXRlZEF0OiBEYXRlO1xyXG4gIGNyZWF0ZWRCeTogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgUXVlc3Rpb24ge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgdHlwZTogJ3RleHQnIHwgJ3RleHRhcmVhJyB8ICdzZWxlY3QnIHwgJ3JhZGlvJyB8ICdjaGVja2JveCcgfCAnbnVtYmVyJyB8ICdlbWFpbCc7XHJcbiAgbGFiZWw6IHN0cmluZztcclxuICBwbGFjZWhvbGRlcj86IHN0cmluZztcclxuICByZXF1aXJlZDogYm9vbGVhbjtcclxuICBvcHRpb25zPzogc3RyaW5nW107XHJcbiAgdmFsaWRhdGlvbj86IHtcclxuICAgIG1pbkxlbmd0aD86IG51bWJlcjtcclxuICAgIG1heExlbmd0aD86IG51bWJlcjtcclxuICAgIHBhdHRlcm4/OiBzdHJpbmc7XHJcbiAgfTtcclxufVxyXG5cclxuaW50ZXJmYWNlIEFwcGxpY2F0aW9uU2V0dGluZ3Mge1xyXG4gIGFsbG93TXVsdGlwbGVTdWJtaXNzaW9uczogYm9vbGVhbjtcclxuICByZXF1aXJlQXBwcm92YWw6IGJvb2xlYW47XHJcbiAgYXV0b1Jlc3BvbnNlOiBib29sZWFuO1xyXG4gIG5vdGlmaWNhdGlvbkNoYW5uZWxzOiBzdHJpbmdbXTtcclxuICBvcGVuaW5nU2NoZWR1bGU/OiB7XHJcbiAgICBlbmFibGVkOiBib29sZWFuO1xyXG4gICAgc3RhcnREYXRlOiBzdHJpbmc7XHJcbiAgICBlbmREYXRlOiBzdHJpbmc7XHJcbiAgfTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gaGFuZGxlcihyZXE6IE5leHRBcGlSZXF1ZXN0LCByZXM6IE5leHRBcGlSZXNwb25zZSkge1xyXG4gIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKHJlcSwgcmVzLCBhdXRoT3B0aW9ucyk7XHJcbiAgaWYgKCFzZXNzaW9uPy51c2VyPy5pZCkge1xyXG4gICAgcmV0dXJuIHJlcy5zdGF0dXMoNDAxKS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0pO1xyXG4gIH1cclxuXHJcbiAgLy8gQ2hlY2sgaWYgdXNlciBpcyBhZG1pblxyXG4gIGNvbnN0IGlzQWRtaW4gPSAoc2Vzc2lvbi51c2VyIGFzIGFueSk/LmlzQWRtaW47XHJcbiAgaWYgKCFpc0FkbWluKSB7XHJcbiAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDMpLmpzb24oeyBlcnJvcjogJ0FkbWluIGFjY2VzcyByZXF1aXJlZCcgfSk7XHJcbiAgfVxyXG5cclxuICBjb25zdCBkYiA9IGF3YWl0IGdldERiKCk7XHJcbiAgY29uc3QgY29sbGVjdGlvbiA9IGRiLmNvbGxlY3Rpb24oJ2N1c3RvbV9hcHBsaWNhdGlvbnMnKTtcclxuXHJcbiAgaWYgKHJlcS5tZXRob2QgPT09ICdHRVQnKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBhcHBsaWNhdGlvbnMgPSBhd2FpdCBjb2xsZWN0aW9uLmZpbmQoe30pLnRvQXJyYXkoKTtcclxuICAgICAgLy8gUmVtb3ZlIE1vbmdvREIgX2lkIGZpZWxkIGZyb20gcmVzcG9uc2UgdG8gYXZvaWQgdXBkYXRlIGNvbmZsaWN0c1xyXG4gICAgICBjb25zdCBjbGVhbkFwcGxpY2F0aW9ucyA9IGFwcGxpY2F0aW9ucy5tYXAoKHsgX2lkLCAuLi5hcHAgfSkgPT4gYXBwKTtcclxuICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoMjAwKS5qc29uKHsgYXBwbGljYXRpb25zOiBjbGVhbkFwcGxpY2F0aW9ucyB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGFwcGxpY2F0aW9uczonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiByZXMuc3RhdHVzKDUwMCkuanNvbih7IGVycm9yOiAnRmFpbGVkIHRvIGZldGNoIGFwcGxpY2F0aW9ucycgfSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBpZiAocmVxLm1ldGhvZCA9PT0gJ1BPU1QnKSB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBhcHBsaWNhdGlvbkRhdGEgPSByZXEuYm9keSBhcyBBcHBsaWNhdGlvblR5cGU7XHJcbiAgICAgIFxyXG4gICAgICAvLyBWYWxpZGF0ZSByZXF1aXJlZCBmaWVsZHNcclxuICAgICAgaWYgKCFhcHBsaWNhdGlvbkRhdGEudGl0bGUgfHwgIWFwcGxpY2F0aW9uRGF0YS5kZXNjcmlwdGlvbikge1xyXG4gICAgICAgIHJldHVybiByZXMuc3RhdHVzKDQwMCkuanNvbih7IGVycm9yOiAnVGl0bGUgYW5kIGRlc2NyaXB0aW9uIGFyZSByZXF1aXJlZCcgfSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmICghYXBwbGljYXRpb25EYXRhLmlkKSB7XHJcbiAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoNDAwKS5qc29uKHsgZXJyb3I6ICdBcHBsaWNhdGlvbiBJRCBpcyByZXF1aXJlZCcgfSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENoZWNrIGlmIGFwcGxpY2F0aW9uIElEIGFscmVhZHkgZXhpc3RzXHJcbiAgICAgIGNvbnN0IGV4aXN0aW5nQXBwID0gYXdhaXQgY29sbGVjdGlvbi5maW5kT25lKHsgaWQ6IGFwcGxpY2F0aW9uRGF0YS5pZCB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmIChleGlzdGluZ0FwcCkge1xyXG4gICAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyBhcHBsaWNhdGlvbiAtIGV4Y2x1ZGUgX2lkIGZpZWxkXHJcbiAgICAgICAgY29uc3QgdXBkYXRlZEFwcCA9IHtcclxuICAgICAgICAgIGlkOiBhcHBsaWNhdGlvbkRhdGEuaWQsXHJcbiAgICAgICAgICB0aXRsZTogYXBwbGljYXRpb25EYXRhLnRpdGxlLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246IGFwcGxpY2F0aW9uRGF0YS5kZXNjcmlwdGlvbixcclxuICAgICAgICAgIGNvbG9yOiBhcHBsaWNhdGlvbkRhdGEuY29sb3IsXHJcbiAgICAgICAgICBpY29uOiBhcHBsaWNhdGlvbkRhdGEuaWNvbixcclxuICAgICAgICAgIGVuYWJsZWQ6IGFwcGxpY2F0aW9uRGF0YS5lbmFibGVkLFxyXG4gICAgICAgICAgcXVlc3Rpb25zOiBhcHBsaWNhdGlvbkRhdGEucXVlc3Rpb25zLFxyXG4gICAgICAgICAgc2V0dGluZ3M6IGFwcGxpY2F0aW9uRGF0YS5zZXR0aW5ncyxcclxuICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgICB9O1xyXG4gICAgICAgIFxyXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNvbGxlY3Rpb24udXBkYXRlT25lKFxyXG4gICAgICAgICAgeyBpZDogYXBwbGljYXRpb25EYXRhLmlkIH0sXHJcbiAgICAgICAgICB7ICRzZXQ6IHVwZGF0ZWRBcHAgfVxyXG4gICAgICAgICk7XHJcbiAgICAgICAgXHJcbiAgICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoMjAwKS5qc29uKHsgYXBwbGljYXRpb246IHVwZGF0ZWRBcHAgfSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgLy8gQ3JlYXRlIG5ldyBhcHBsaWNhdGlvblxyXG4gICAgICAgIGNvbnN0IG5ld0FwcCA9IHtcclxuICAgICAgICAgIC4uLmFwcGxpY2F0aW9uRGF0YSxcclxuICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgICAgIGNyZWF0ZWRCeTogc2Vzc2lvbi51c2VyLmlkLFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgXHJcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY29sbGVjdGlvbi5pbnNlcnRPbmUobmV3QXBwKTtcclxuICAgICAgICByZXR1cm4gcmVzLnN0YXR1cygyMDEpLmpzb24oeyBhcHBsaWNhdGlvbjogbmV3QXBwIH0pO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzYXZpbmcgYXBwbGljYXRpb246JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg1MDApLmpzb24oeyBlcnJvcjogJ0ZhaWxlZCB0byBzYXZlIGFwcGxpY2F0aW9uJyB9KTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHJldHVybiByZXMuc3RhdHVzKDQwNSkuanNvbih7IGVycm9yOiAnTWV0aG9kIG5vdCBhbGxvd2VkJyB9KTtcclxufSAiXSwibmFtZXMiOlsiZ2V0U2VydmVyU2Vzc2lvbiIsImF1dGhPcHRpb25zIiwiZ2V0RGIiLCJoYW5kbGVyIiwicmVxIiwicmVzIiwic2Vzc2lvbiIsInVzZXIiLCJpZCIsInN0YXR1cyIsImpzb24iLCJlcnJvciIsImlzQWRtaW4iLCJkYiIsImNvbGxlY3Rpb24iLCJtZXRob2QiLCJhcHBsaWNhdGlvbnMiLCJmaW5kIiwidG9BcnJheSIsImNsZWFuQXBwbGljYXRpb25zIiwibWFwIiwiX2lkIiwiYXBwIiwiY29uc29sZSIsImFwcGxpY2F0aW9uRGF0YSIsImJvZHkiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZXhpc3RpbmdBcHAiLCJmaW5kT25lIiwidXBkYXRlZEFwcCIsImNvbG9yIiwiaWNvbiIsImVuYWJsZWQiLCJxdWVzdGlvbnMiLCJzZXR0aW5ncyIsInVwZGF0ZWRBdCIsIkRhdGUiLCJyZXN1bHQiLCJ1cGRhdGVPbmUiLCIkc2V0IiwiYXBwbGljYXRpb24iLCJuZXdBcHAiLCJjcmVhdGVkQXQiLCJjcmVhdGVkQnkiLCJpbnNlcnRPbmUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/admin/applications-builder/index.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Capplications-builder%5Cindex.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();