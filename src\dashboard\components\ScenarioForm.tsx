import React, { useState, useEffect, useCallback, memo } from 'react';
import {
  VStack,
  Card,
  CardBody,
  Text,
  Box,
  Textarea,
  Heading,
  Progress
} from '@chakra-ui/react';
import { ModScenario } from '../types/applications';
import { MOD_SCENARIOS } from '../config/modScenarios';

// Helper function to get random scenarios but assign sequential IDs
const getRandomScenarios = (count: number): (ModScenario & { displayId: number })[] => {
  const shuffled = [...MOD_SCENARIOS]
    .sort(() => Math.random() - 0.5)
    .map((scenario, index) => ({
      ...scenario,
      displayId: index + 1 // Add sequential display ID
    }));
  return shuffled.slice(0, count);
};

// Memoized scenario card component
const ScenarioCard = memo(({ scenario, response, onResponseChange }: {
  scenario: ModScenario & { displayId: number };
  response: string;
  onResponseChange: (response: string) => void;
}) => {
  return (
    <Card bg="whiteAlpha.50" border="1px solid" borderColor="whiteAlpha.200" mb={4}>
      <CardBody>
        <VStack align="stretch" spacing={4}>
          <Box>
            <Text fontWeight="bold" mb={2}>Scenario {scenario.displayId}:</Text>
            <Text>{scenario.scenario}</Text>
          </Box>
          <Box>
            <Text fontWeight="bold" color="blue.300" mb={2}>Context:</Text>
            <Text>{scenario.context}</Text>
          </Box>
          <Box>
            <Text fontWeight="bold" color="green.300" mb={2}>How would you handle this situation?</Text>
            <Textarea
              value={response}
              onChange={(e) => onResponseChange(e.target.value)}
              placeholder="Explain your approach to handling this situation..."
              minH="150px"
              bg="whiteAlpha.100"
              _hover={{ bg: "whiteAlpha.200" }}
              resize="vertical"
            />
          </Box>
        </VStack>
      </CardBody>
    </Card>
  );
});

ScenarioCard.displayName = 'ScenarioCard';

interface ScenarioFormProps {
  onFormChange: (responses: Record<number, string>) => void;
  initialData?: Record<number, string>;
}

const ScenarioForm: React.FC<ScenarioFormProps> = ({ onFormChange, initialData }) => {
  const [scenarios, setScenarios] = useState<(ModScenario & { displayId: number })[]>([]);
  const [responses, setResponses] = useState<Record<number, string>>(initialData || {});

  // Initialize scenarios
  useEffect(() => {
    setScenarios(getRandomScenarios(15));
  }, []);

  // Notify parent of changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onFormChange(responses);
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [responses, onFormChange]);

  // Handle response changes
  const handleResponseChange = useCallback((scenarioId: number, response: string) => {
    setResponses(prev => ({
      ...prev,
      [scenarioId]: response
    }));
  }, []);

  // Calculate progress
  const progress = (Object.keys(responses).length / scenarios.length) * 100;

  return (
    <VStack spacing={6} align="stretch" position="relative">
      <Box position="sticky" top={0} bg="gray.800" p={4} zIndex={1}>
        <Heading size="md" mb={2}>Moderation Scenarios</Heading>
        <Progress value={progress} size="sm" colorScheme="blue" borderRadius="full" />
        <Text mt={2} fontSize="sm" color="gray.400">
          {Math.round(progress)}% Complete ({Object.keys(responses).length} of {scenarios.length} scenarios)
        </Text>
      </Box>

      {scenarios.map((scenario) => (
        <ScenarioCard
          key={scenario.id}
          scenario={scenario}
          response={responses[scenario.id] || ''}
          onResponseChange={(response) => handleResponseChange(scenario.id, response)}
        />
      ))}
    </VStack>
  );
};

export default memo(ScenarioForm); 