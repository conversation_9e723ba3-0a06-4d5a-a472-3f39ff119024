"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["lib-node_modules_pnpm_rec"],{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/getChartPointer.js":
/*!***********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/getChartPointer.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChartPointer: () => (/* binding */ getChartPointer)\n/* harmony export */ });\n/**\n * Computes the chart coordinates from the mouse event.\n *\n * The coordinates are relative to the top-left corner of the chart,\n * where the top-left corner of the chart is (0, 0).\n * Moving right, the x-coordinate increases, and moving down, the y-coordinate increases.\n *\n * The coordinates are rounded to the nearest integer and are including a CSS transform scale.\n * So a chart that's scaled will return the same coordinates as a chart that's not scaled.\n *\n * @param event The mouse event from React event handlers\n * @return chartPointer The chart coordinates relative to the top-left corner of the chart\n */\nvar getChartPointer = event => {\n  var rect = event.currentTarget.getBoundingClientRect();\n  var scaleX = rect.width / event.currentTarget.offsetWidth;\n  var scaleY = rect.height / event.currentTarget.offsetHeight;\n  return {\n    /*\n     * Here it's important to use:\n     * - event.clientX and event.clientY to get the mouse position relative to the viewport, including scroll.\n     * - pageX and pageY are not used because they are relative to the whole document, and ignore scroll.\n     * - rect.left and rect.top are used to get the position of the chart relative to the viewport.\n     * - offsetX and offsetY are not used because they are relative to the offset parent\n     *  which may or may not be the same as the clientX and clientY, depending on the position of the chart in the DOM\n     *  and surrounding element styles. CSS position: relative, absolute, fixed, will change the offset parent.\n     * - scaleX and scaleY are necessary for when the chart element is scaled using CSS `transform: scale(N)`.\n     */\n    chartX: Math.round((event.clientX - rect.left) / scaleX),\n    chartY: Math.round((event.clientY - rect.top) / scaleY)\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/getChartPointer.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/getEveryNthWithCondition.js":
/*!********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/getEveryNthWithCondition.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEveryNthWithCondition: () => (/* binding */ getEveryNthWithCondition)\n/* harmony export */ });\n/**\n * Given an array and a number N, return a new array which contains every nTh\n * element of the input array. For n below 1, an empty array is returned.\n * If isValid is provided, all candidates must suffice the condition, else undefined is returned.\n * @param {T[]} array An input array.\n * @param {integer} n A number\n * @param {Function} isValid A function to evaluate a candidate form the array\n * @returns {T[]} The result array of the same type as the input array.\n */\nfunction getEveryNthWithCondition(array, n, isValid) {\n  if (n < 1) {\n    return [];\n  }\n  if (n === 1 && isValid === undefined) {\n    return array;\n  }\n  var result = [];\n  for (var i = 0; i < array.length; i += n) {\n    if (isValid === undefined || isValid(array[i]) === true) {\n      result.push(array[i]);\n    } else {\n      return undefined;\n    }\n  }\n  return result;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVjaGFydHNAMy4xLjBfQHR5cGVzK3JlYWN0XzI0ZTRmZDVlYWZkZjlmNThkMDRhZTFiMmNlMWQ2YTE1L25vZGVfbW9kdWxlcy9yZWNoYXJ0cy9lczYvdXRpbC9nZXRFdmVyeU50aFdpdGhDb25kaXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxLQUFLO0FBQ2hCLFdBQVcsU0FBUztBQUNwQixXQUFXLFVBQVU7QUFDckIsYUFBYSxLQUFLO0FBQ2xCO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixrQkFBa0I7QUFDcEM7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWNoYXJ0c0AzLjEuMF9AdHlwZXMrcmVhY3RfMjRlNGZkNWVhZmRmOWY1OGQwNGFlMWIyY2UxZDZhMTVcXG5vZGVfbW9kdWxlc1xccmVjaGFydHNcXGVzNlxcdXRpbFxcZ2V0RXZlcnlOdGhXaXRoQ29uZGl0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogR2l2ZW4gYW4gYXJyYXkgYW5kIGEgbnVtYmVyIE4sIHJldHVybiBhIG5ldyBhcnJheSB3aGljaCBjb250YWlucyBldmVyeSBuVGhcbiAqIGVsZW1lbnQgb2YgdGhlIGlucHV0IGFycmF5LiBGb3IgbiBiZWxvdyAxLCBhbiBlbXB0eSBhcnJheSBpcyByZXR1cm5lZC5cbiAqIElmIGlzVmFsaWQgaXMgcHJvdmlkZWQsIGFsbCBjYW5kaWRhdGVzIG11c3Qgc3VmZmljZSB0aGUgY29uZGl0aW9uLCBlbHNlIHVuZGVmaW5lZCBpcyByZXR1cm5lZC5cbiAqIEBwYXJhbSB7VFtdfSBhcnJheSBBbiBpbnB1dCBhcnJheS5cbiAqIEBwYXJhbSB7aW50ZWdlcn0gbiBBIG51bWJlclxuICogQHBhcmFtIHtGdW5jdGlvbn0gaXNWYWxpZCBBIGZ1bmN0aW9uIHRvIGV2YWx1YXRlIGEgY2FuZGlkYXRlIGZvcm0gdGhlIGFycmF5XG4gKiBAcmV0dXJucyB7VFtdfSBUaGUgcmVzdWx0IGFycmF5IG9mIHRoZSBzYW1lIHR5cGUgYXMgdGhlIGlucHV0IGFycmF5LlxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0RXZlcnlOdGhXaXRoQ29uZGl0aW9uKGFycmF5LCBuLCBpc1ZhbGlkKSB7XG4gIGlmIChuIDwgMSkge1xuICAgIHJldHVybiBbXTtcbiAgfVxuICBpZiAobiA9PT0gMSAmJiBpc1ZhbGlkID09PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gYXJyYXk7XG4gIH1cbiAgdmFyIHJlc3VsdCA9IFtdO1xuICBmb3IgKHZhciBpID0gMDsgaSA8IGFycmF5Lmxlbmd0aDsgaSArPSBuKSB7XG4gICAgaWYgKGlzVmFsaWQgPT09IHVuZGVmaW5lZCB8fCBpc1ZhbGlkKGFycmF5W2ldKSA9PT0gdHJ1ZSkge1xuICAgICAgcmVzdWx0LnB1c2goYXJyYXlbaV0pO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cbiAgfVxuICByZXR1cm4gcmVzdWx0O1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/getEveryNthWithCondition.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/isDomainSpecifiedByUser.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/isDomainSpecifiedByUser.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extendDomain: () => (/* binding */ extendDomain),\n/* harmony export */   isWellFormedNumberDomain: () => (/* binding */ isWellFormedNumberDomain),\n/* harmony export */   numericalDomainSpecifiedWithoutRequiringData: () => (/* binding */ numericalDomainSpecifiedWithoutRequiringData),\n/* harmony export */   parseNumericalUserDomain: () => (/* binding */ parseNumericalUserDomain)\n/* harmony export */ });\n/* harmony import */ var _ChartUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChartUtils */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/ChartUtils.js\");\n/* harmony import */ var _DataUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DataUtils */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/DataUtils.js\");\n/* harmony import */ var _isWellBehavedNumber__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isWellBehavedNumber */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/isWellBehavedNumber.js\");\n\n\n\nfunction isWellFormedNumberDomain(v) {\n  if (Array.isArray(v) && v.length === 2) {\n    var [min, max] = v;\n    if ((0,_isWellBehavedNumber__WEBPACK_IMPORTED_MODULE_0__.isWellBehavedNumber)(min) && (0,_isWellBehavedNumber__WEBPACK_IMPORTED_MODULE_0__.isWellBehavedNumber)(max)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction extendDomain(providedDomain, boundaryDomain, allowDataOverflow) {\n  if (allowDataOverflow) {\n    // If the data are allowed to overflow - we're fine with whatever user provided\n    return providedDomain;\n  }\n  /*\n   * If the data are not allowed to overflow - we need to extend the domain.\n   * Means that effectively the user is allowed to make the domain larger\n   * but not smaller.\n   */\n  return [Math.min(providedDomain[0], boundaryDomain[0]), Math.max(providedDomain[1], boundaryDomain[1])];\n}\n\n/**\n * So Recharts allows users to provide their own domains,\n * but it also places some expectations on what the domain is.\n * We can improve on the typescript typing, but we also need a runtime test\n to observe that the user-provided domain is well-formed,\n * that is: an array with exactly two numbers.\n *\n * This function does not accept data as an argument.\n * This is to enable a performance optimization - if the domain is there,\n * and we know what it is without traversing all the data,\n * then we don't have to traverse all the data!\n *\n * If the user-provided domain is not well-formed,\n * this function will return undefined - in which case we should traverse the data to calculate the real domain.\n *\n * This function is for parsing the numerical domain only.\n *\n * @param userDomain external prop, user provided, before validation. Can have various shapes: array, function, special magical strings inside too.\n * @param allowDataOverflow boolean, provided by users. If true then the data domain wins\n *\n * @return [min, max] domain if it's well-formed; undefined if the domain is invalid\n */\nfunction numericalDomainSpecifiedWithoutRequiringData(userDomain, allowDataOverflow) {\n  if (!allowDataOverflow) {\n    // Cannot compute data overflow if the data is not provided\n    return undefined;\n  }\n  if (typeof userDomain === 'function') {\n    // The user function expects the data to be provided as an argument\n    return undefined;\n  }\n  if (Array.isArray(userDomain) && userDomain.length === 2) {\n    var [providedMin, providedMax] = userDomain;\n    var finalMin, finalMax;\n    if ((0,_isWellBehavedNumber__WEBPACK_IMPORTED_MODULE_0__.isWellBehavedNumber)(providedMin)) {\n      finalMin = providedMin;\n    } else if (typeof providedMin === 'function') {\n      // The user function expects the data to be provided as an argument\n      return undefined;\n    }\n    if ((0,_isWellBehavedNumber__WEBPACK_IMPORTED_MODULE_0__.isWellBehavedNumber)(providedMax)) {\n      finalMax = providedMax;\n    } else if (typeof providedMax === 'function') {\n      // The user function expects the data to be provided as an argument\n      return undefined;\n    }\n    var candidate = [finalMin, finalMax];\n    if (isWellFormedNumberDomain(candidate)) {\n      return candidate;\n    }\n  }\n  return undefined;\n}\n\n/**\n * So Recharts allows users to provide their own domains,\n * but it also places some expectations on what the domain is.\n * We can improve on the typescript typing, but we also need a runtime test\n * to observe that the user-provided domain is well-formed,\n * that is: an array with exactly two numbers.\n * If the user-provided domain is not well-formed,\n * this function will return undefined - in which case we should traverse the data to calculate the real domain.\n *\n * This function is for parsing the numerical domain only.\n *\n * You are probably thinking, why does domain need tick count?\n * Well it adjusts the domain based on where the \"nice ticks\" land, and nice ticks depend on the tick count.\n *\n * @param userDomain external prop, user provided, before validation. Can have various shapes: array, function, special magical strings inside too.\n * @param dataDomain calculated from data. Can be undefined, as an option for performance optimization\n * @param allowDataOverflow provided by users. If true then the data domain wins\n *\n * @return [min, max] domain if it's well-formed; undefined if the domain is invalid\n */\nfunction parseNumericalUserDomain(userDomain, dataDomain, allowDataOverflow) {\n  if (!allowDataOverflow && dataDomain == null) {\n    // Cannot compute data overflow if the data is not provided\n    return undefined;\n  }\n  if (typeof userDomain === 'function' && dataDomain != null) {\n    try {\n      var result = userDomain(dataDomain, allowDataOverflow);\n      if (isWellFormedNumberDomain(result)) {\n        return extendDomain(result, dataDomain, allowDataOverflow);\n      }\n    } catch (_unused) {\n      /* ignore the exception and compute domain from data later */\n    }\n  }\n  if (Array.isArray(userDomain) && userDomain.length === 2) {\n    var [providedMin, providedMax] = userDomain;\n    var finalMin, finalMax;\n    if (providedMin === 'auto') {\n      if (dataDomain != null) {\n        finalMin = Math.min(...dataDomain);\n      }\n    } else if ((0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.isNumber)(providedMin)) {\n      finalMin = providedMin;\n    } else if (typeof providedMin === 'function') {\n      try {\n        if (dataDomain != null) {\n          finalMin = providedMin(dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[0]);\n        }\n      } catch (_unused2) {\n        /* ignore the exception and compute domain from data later */\n      }\n    } else if (typeof providedMin === 'string' && _ChartUtils__WEBPACK_IMPORTED_MODULE_2__.MIN_VALUE_REG.test(providedMin)) {\n      var match = _ChartUtils__WEBPACK_IMPORTED_MODULE_2__.MIN_VALUE_REG.exec(providedMin);\n      if (match == null || dataDomain == null) {\n        finalMin = undefined;\n      } else {\n        var value = +match[1];\n        finalMin = dataDomain[0] - value;\n      }\n    } else {\n      finalMin = dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[0];\n    }\n    if (providedMax === 'auto') {\n      if (dataDomain != null) {\n        finalMax = Math.max(...dataDomain);\n      }\n    } else if ((0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.isNumber)(providedMax)) {\n      finalMax = providedMax;\n    } else if (typeof providedMax === 'function') {\n      try {\n        if (dataDomain != null) {\n          finalMax = providedMax(dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[1]);\n        }\n      } catch (_unused3) {\n        /* ignore the exception and compute domain from data later */\n      }\n    } else if (typeof providedMax === 'string' && _ChartUtils__WEBPACK_IMPORTED_MODULE_2__.MAX_VALUE_REG.test(providedMax)) {\n      var _match = _ChartUtils__WEBPACK_IMPORTED_MODULE_2__.MAX_VALUE_REG.exec(providedMax);\n      if (_match == null || dataDomain == null) {\n        finalMax = undefined;\n      } else {\n        var _value = +_match[1];\n        finalMax = dataDomain[1] + _value;\n      }\n    } else {\n      finalMax = dataDomain === null || dataDomain === void 0 ? void 0 : dataDomain[1];\n    }\n    var candidate = [finalMin, finalMax];\n    if (isWellFormedNumberDomain(candidate)) {\n      if (dataDomain == null) {\n        return candidate;\n      }\n      return extendDomain(candidate, dataDomain, allowDataOverflow);\n    }\n  }\n  return undefined;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/isDomainSpecifiedByUser.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/isWellBehavedNumber.js":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/isWellBehavedNumber.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPositiveNumber: () => (/* binding */ isPositiveNumber),\n/* harmony export */   isWellBehavedNumber: () => (/* binding */ isWellBehavedNumber)\n/* harmony export */ });\nfunction isWellBehavedNumber(n) {\n  return Number.isFinite(n);\n}\nfunction isPositiveNumber(n) {\n  return typeof n === 'number' && n > 0 && Number.isFinite(n);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVjaGFydHNAMy4xLjBfQHR5cGVzK3JlYWN0XzI0ZTRmZDVlYWZkZjlmNThkMDRhZTFiMmNlMWQ2YTE1L25vZGVfbW9kdWxlcy9yZWNoYXJ0cy9lczYvdXRpbC9pc1dlbGxCZWhhdmVkTnVtYmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWNoYXJ0c0AzLjEuMF9AdHlwZXMrcmVhY3RfMjRlNGZkNWVhZmRmOWY1OGQwNGFlMWIyY2UxZDZhMTVcXG5vZGVfbW9kdWxlc1xccmVjaGFydHNcXGVzNlxcdXRpbFxcaXNXZWxsQmVoYXZlZE51bWJlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaXNXZWxsQmVoYXZlZE51bWJlcihuKSB7XG4gIHJldHVybiBOdW1iZXIuaXNGaW5pdGUobik7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNQb3NpdGl2ZU51bWJlcihuKSB7XG4gIHJldHVybiB0eXBlb2YgbiA9PT0gJ251bWJlcicgJiYgbiA+IDAgJiYgTnVtYmVyLmlzRmluaXRlKG4pO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/isWellBehavedNumber.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/payload/getUniqPayload.js":
/*!******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/payload/getUniqPayload.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUniqPayload: () => (/* binding */ getUniqPayload)\n/* harmony export */ });\n/* harmony import */ var es_toolkit_compat_uniqBy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! es-toolkit/compat/uniqBy */ \"(pages-dir-browser)/../../node_modules/.pnpm/es-toolkit@1.39.7/node_modules/es-toolkit/compat/uniqBy.js\");\n/* harmony import */ var es_toolkit_compat_uniqBy__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(es_toolkit_compat_uniqBy__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * This is configuration option that decides how to filter for unique values only:\n *\n * - `false` means \"no filter\"\n * - `true` means \"use recharts default filter\"\n * - function means \"use return of this function as the default key\"\n */\n\nfunction getUniqPayload(payload, option, defaultUniqBy) {\n  if (option === true) {\n    return es_toolkit_compat_uniqBy__WEBPACK_IMPORTED_MODULE_0___default()(payload, defaultUniqBy);\n  }\n  if (typeof option === 'function') {\n    return es_toolkit_compat_uniqBy__WEBPACK_IMPORTED_MODULE_0___default()(payload, option);\n  }\n  return payload;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVjaGFydHNAMy4xLjBfQHR5cGVzK3JlYWN0XzI0ZTRmZDVlYWZkZjlmNThkMDRhZTFiMmNlMWQ2YTE1L25vZGVfbW9kdWxlcy9yZWNoYXJ0cy9lczYvdXRpbC9wYXlsb2FkL2dldFVuaXFQYXlsb2FkLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4Qzs7QUFFOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBLFdBQVcsK0RBQU07QUFDakI7QUFDQTtBQUNBLFdBQVcsK0RBQU07QUFDakI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWNoYXJ0c0AzLjEuMF9AdHlwZXMrcmVhY3RfMjRlNGZkNWVhZmRmOWY1OGQwNGFlMWIyY2UxZDZhMTVcXG5vZGVfbW9kdWxlc1xccmVjaGFydHNcXGVzNlxcdXRpbFxccGF5bG9hZFxcZ2V0VW5pcVBheWxvYWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHVuaXFCeSBmcm9tICdlcy10b29sa2l0L2NvbXBhdC91bmlxQnknO1xuXG4vKipcbiAqIFRoaXMgaXMgY29uZmlndXJhdGlvbiBvcHRpb24gdGhhdCBkZWNpZGVzIGhvdyB0byBmaWx0ZXIgZm9yIHVuaXF1ZSB2YWx1ZXMgb25seTpcbiAqXG4gKiAtIGBmYWxzZWAgbWVhbnMgXCJubyBmaWx0ZXJcIlxuICogLSBgdHJ1ZWAgbWVhbnMgXCJ1c2UgcmVjaGFydHMgZGVmYXVsdCBmaWx0ZXJcIlxuICogLSBmdW5jdGlvbiBtZWFucyBcInVzZSByZXR1cm4gb2YgdGhpcyBmdW5jdGlvbiBhcyB0aGUgZGVmYXVsdCBrZXlcIlxuICovXG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRVbmlxUGF5bG9hZChwYXlsb2FkLCBvcHRpb24sIGRlZmF1bHRVbmlxQnkpIHtcbiAgaWYgKG9wdGlvbiA9PT0gdHJ1ZSkge1xuICAgIHJldHVybiB1bmlxQnkocGF5bG9hZCwgZGVmYXVsdFVuaXFCeSk7XG4gIH1cbiAgaWYgKHR5cGVvZiBvcHRpb24gPT09ICdmdW5jdGlvbicpIHtcbiAgICByZXR1cm4gdW5pcUJ5KHBheWxvYWQsIG9wdGlvbik7XG4gIH1cbiAgcmV0dXJuIHBheWxvYWQ7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/payload/getUniqPayload.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/resolveDefaultProps.js":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/resolveDefaultProps.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveDefaultProps: () => (/* binding */ resolveDefaultProps)\n/* harmony export */ });\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * This function mimics the behavior of the `defaultProps` static property in React.\n * Functional components do not have a defaultProps property, so this function is useful to resolve default props.\n *\n * The common recommendation is to use ES6 destructuring with default values in the function signature,\n * but you need to be careful there and make sure you destructure all the individual properties\n * and not the whole object. See the test file for example.\n *\n * And because destructuring all properties one by one is a faff, and it's easy to miss one property,\n * this function exists.\n *\n * @param realProps - the props object passed to the component by the user\n * @param defaultProps - the default props object defined in the component by Recharts\n * @returns - the props object with all the default props resolved. All `undefined` values are replaced with the default value.\n */\nfunction resolveDefaultProps(realProps, defaultProps) {\n  /*\n   * To avoid mutating the original `realProps` object passed to the function, create a shallow copy of it.\n   * `resolvedProps` will be modified directly with the defaults.\n   */\n  var resolvedProps = _objectSpread({}, realProps);\n  /*\n   * Since the function guarantees `D extends Partial<T>`, this assignment is safe.\n   * It allows TypeScript to work with the well-defined `Partial<T>` type inside the loop,\n   * making subsequent type inference (especially for `dp[key]`) much more straightforward for the compiler.\n   * This is a key step to improve type safety *without* value assertions later.\n   */\n  var dp = defaultProps;\n  /*\n   * `Object.keys` doesn't preserve strong key types - it always returns Array<string>.\n   * However, due to the `D extends Partial<T>` constraint,\n   * we know these keys *must* also be valid keys of `T`.\n   * This assertion informs TypeScript of this relationship, avoiding type errors when using `key` to index `acc` (type T).\n   *\n   * Type assertions are not sound but in this case it's necessary\n   * as `Object.keys` does not do what we want it to do.\n   */\n  var keys = Object.keys(defaultProps);\n  var withDefaults = keys.reduce((acc, key) => {\n    if (acc[key] === undefined && dp[key] !== undefined) {\n      acc[key] = dp[key];\n    }\n    return acc;\n  }, resolvedProps);\n  /*\n   * And again type assertions are not safe but here we have done the runtime work\n   * so let's bypass the lack of static type safety and tell the compiler what happened.\n   */\n  return withDefaults;\n}\n\n/**\n * Helper type to extract the keys of T that are required.\n * It iterates through each key K in T. If Pick<T, K> cannot be assigned an empty object {},\n * it means K is required, so we keep K; otherwise, we discard it (never).\n * [keyof T] at the end creates a union of the kept keys.\n */\n\n/**\n * Helper type to extract the keys of T that are optional.\n * It iterates through each key K in T. If Pick<T, K> can be assigned an empty object {},\n * it means K is optional (or potentially missing), so we keep K; otherwise, we discard it (never).\n * [keyof T] at the end creates a union of the kept keys.\n */\n\n/**\n * Helper type to ensure keys of D exist in T.\n * For each key K in D, if K is also a key of T, keep the type D[K].\n * If K is NOT a key of T, map it to type `never`.\n * An object cannot have a property of type `never`, effectively disallowing extra keys.\n */\n\n/**\n * This type will take a source type `Props` and a default type `Defaults` and will return a new type\n * where all properties that are optional in `Props` but required in `Defaults` are made required in the result.\n * Properties that are required in `Props` and optional in `Defaults` will remain required.\n * Properties that are optional in both `Props` and `Defaults` will remain optional.\n *\n * This is useful for creating a type that represents the resolved props of a component with default props.\n *///# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/resolveDefaultProps.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/getNiceTickValues.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/getNiceTickValues.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateStep: () => (/* binding */ _calculateStep),\n/* harmony export */   getFormatStep: () => (/* binding */ getFormatStep),\n/* harmony export */   getNiceTickValues: () => (/* binding */ getNiceTickValues),\n/* harmony export */   getTickOfSingleValue: () => (/* binding */ getTickOfSingleValue),\n/* harmony export */   getTickValuesFixedDomain: () => (/* binding */ getTickValuesFixedDomain),\n/* harmony export */   getValidInterval: () => (/* binding */ getValidInterval)\n/* harmony export */ });\n/* harmony import */ var decimal_js_light__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! decimal.js-light */ \"(pages-dir-browser)/../../node_modules/.pnpm/decimal.js-light@2.5.1/node_modules/decimal.js-light/decimal.js\");\n/* harmony import */ var decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(decimal_js_light__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _util_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/util/utils.js\");\n/* harmony import */ var _util_arithmetic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/arithmetic */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/util/arithmetic.js\");\n/**\n * @fileOverview calculate tick values of scale\n * <AUTHOR> arcthur\n * @date 2015-09-17\n */\n\n\n\n\n/**\n * Calculate a interval of a minimum value and a maximum value\n *\n * @param  {Number} min       The minimum value\n * @param  {Number} max       The maximum value\n * @return {Array} An interval\n */\nvar getValidInterval = _ref => {\n  var [min, max] = _ref;\n  var [validMin, validMax] = [min, max];\n\n  // exchange\n  if (min > max) {\n    [validMin, validMax] = [max, min];\n  }\n  return [validMin, validMax];\n};\n\n/**\n * Calculate the step which is easy to understand between ticks, like 10, 20, 25\n *\n * @param  roughStep        The rough step calculated by dividing the difference by the tickCount\n * @param  allowDecimals    Allow the ticks to be decimals or not\n * @param  correctionFactor A correction factor\n * @return The step which is easy to understand between two ticks\n */\nvar getFormatStep = (roughStep, allowDecimals, correctionFactor) => {\n  if (roughStep.lte(0)) {\n    return new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(0);\n  }\n  var digitCount = (0,_util_arithmetic__WEBPACK_IMPORTED_MODULE_1__.getDigitCount)(roughStep.toNumber());\n  // The ratio between the rough step and the smallest number which has a bigger\n  // order of magnitudes than the rough step\n  var digitCountValue = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(10).pow(digitCount);\n  var stepRatio = roughStep.div(digitCountValue);\n  // When an integer and a float multiplied, the accuracy of result may be wrong\n  var stepRatioScale = digitCount !== 1 ? 0.05 : 0.1;\n  var amendStepRatio = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(Math.ceil(stepRatio.div(stepRatioScale).toNumber())).add(correctionFactor).mul(stepRatioScale);\n  var formatStep = amendStepRatio.mul(digitCountValue);\n  return allowDecimals ? new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(formatStep.toNumber()) : new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(Math.ceil(formatStep.toNumber()));\n};\n\n/**\n * calculate the ticks when the minimum value equals to the maximum value\n *\n * @param  value         The minimum value which is also the maximum value\n * @param  tickCount     The count of ticks\n * @param  allowDecimals Allow the ticks to be decimals or not\n * @return array of ticks\n */\nvar getTickOfSingleValue = (value, tickCount, allowDecimals) => {\n  var step = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(1);\n  // calculate the middle value of ticks\n  var middle = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(value);\n  if (!middle.isint() && allowDecimals) {\n    var absVal = Math.abs(value);\n    if (absVal < 1) {\n      // The step should be a float number when the difference is smaller than 1\n      step = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(10).pow((0,_util_arithmetic__WEBPACK_IMPORTED_MODULE_1__.getDigitCount)(value) - 1);\n      middle = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(Math.floor(middle.div(step).toNumber())).mul(step);\n    } else if (absVal > 1) {\n      // Return the maximum integer which is smaller than 'value' when 'value' is greater than 1\n      middle = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(Math.floor(value));\n    }\n  } else if (value === 0) {\n    middle = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(Math.floor((tickCount - 1) / 2));\n  } else if (!allowDecimals) {\n    middle = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(Math.floor(value));\n  }\n  var middleIndex = Math.floor((tickCount - 1) / 2);\n  var fn = (0,_util_utils__WEBPACK_IMPORTED_MODULE_2__.compose)((0,_util_utils__WEBPACK_IMPORTED_MODULE_2__.map)(n => middle.add(new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(n - middleIndex).mul(step)).toNumber()), _util_utils__WEBPACK_IMPORTED_MODULE_2__.range);\n  return fn(0, tickCount);\n};\n\n/**\n * Calculate the step\n *\n * @param  min              The minimum value of an interval\n * @param  max              The maximum value of an interval\n * @param  tickCount        The count of ticks\n * @param  allowDecimals    Allow the ticks to be decimals or not\n * @param  correctionFactor A correction factor\n * @return The step, minimum value of ticks, maximum value of ticks\n */\nvar _calculateStep = function calculateStep(min, max, tickCount, allowDecimals) {\n  var correctionFactor = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;\n  // dirty hack (for recharts' test)\n  if (!Number.isFinite((max - min) / (tickCount - 1))) {\n    return {\n      step: new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(0),\n      tickMin: new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(0),\n      tickMax: new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(0)\n    };\n  }\n\n  // The step which is easy to understand between two ticks\n  var step = getFormatStep(new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(max).sub(min).div(tickCount - 1), allowDecimals, correctionFactor);\n\n  // A medial value of ticks\n  var middle;\n\n  // When 0 is inside the interval, 0 should be a tick\n  if (min <= 0 && max >= 0) {\n    middle = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(0);\n  } else {\n    // calculate the middle value\n    middle = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(min).add(max).div(2);\n    // minus modulo value\n    middle = middle.sub(new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(middle).mod(step));\n  }\n  var belowCount = Math.ceil(middle.sub(min).div(step).toNumber());\n  var upCount = Math.ceil(new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(max).sub(middle).div(step).toNumber());\n  var scaleCount = belowCount + upCount + 1;\n  if (scaleCount > tickCount) {\n    // When more ticks need to cover the interval, step should be bigger.\n    return _calculateStep(min, max, tickCount, allowDecimals, correctionFactor + 1);\n  }\n  if (scaleCount < tickCount) {\n    // When less ticks can cover the interval, we should add some additional ticks\n    upCount = max > 0 ? upCount + (tickCount - scaleCount) : upCount;\n    belowCount = max > 0 ? belowCount : belowCount + (tickCount - scaleCount);\n  }\n  return {\n    step,\n    tickMin: middle.sub(new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(belowCount).mul(step)),\n    tickMax: middle.add(new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(upCount).mul(step))\n  };\n};\n\n/**\n * Calculate the ticks of an interval. Ticks can appear outside the interval\n * if it makes them more rounded and nice.\n *\n * @param tuple of [min,max] min: The minimum value, max: The maximum value\n * @param tickCount     The count of ticks\n * @param allowDecimals Allow the ticks to be decimals or not\n * @return array of ticks\n */\n\nfunction getNiceTickValuesFn(_ref2) {\n  var [min, max] = _ref2;\n  var tickCount = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 6;\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  // More than two ticks should be return\n  var count = Math.max(tickCount, 2);\n  var [cormin, cormax] = getValidInterval([min, max]);\n  if (cormin === -Infinity || cormax === Infinity) {\n    var _values = cormax === Infinity ? [cormin, ...(0,_util_utils__WEBPACK_IMPORTED_MODULE_2__.range)(0, tickCount - 1).map(() => Infinity)] : [...(0,_util_utils__WEBPACK_IMPORTED_MODULE_2__.range)(0, tickCount - 1).map(() => -Infinity), cormax];\n    return min > max ? (0,_util_utils__WEBPACK_IMPORTED_MODULE_2__.reverse)(_values) : _values;\n  }\n  if (cormin === cormax) {\n    return getTickOfSingleValue(cormin, tickCount, allowDecimals);\n  }\n\n  // Get the step between two ticks\n  var {\n    step,\n    tickMin,\n    tickMax\n  } = _calculateStep(cormin, cormax, count, allowDecimals, 0);\n  var values = (0,_util_arithmetic__WEBPACK_IMPORTED_MODULE_1__.rangeStep)(tickMin, tickMax.add(new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(0.1).mul(step)), step);\n  return min > max ? (0,_util_utils__WEBPACK_IMPORTED_MODULE_2__.reverse)(values) : values;\n}\n\n/**\n * Calculate the ticks of an interval.\n * Ticks will be constrained to the interval [min, max] even if it makes them less rounded and nice.\n *\n * @param tuple of [min,max] min: The minimum value, max: The maximum value\n * @param tickCount     The count of ticks. This function may return less than tickCount ticks if the interval is too small.\n * @param allowDecimals Allow the ticks to be decimals or not\n * @return array of ticks\n */\nfunction getTickValuesFixedDomainFn(_ref3, tickCount) {\n  var [min, max] = _ref3;\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  // More than two ticks should be return\n  var [cormin, cormax] = getValidInterval([min, max]);\n  if (cormin === -Infinity || cormax === Infinity) {\n    return [min, max];\n  }\n  if (cormin === cormax) {\n    return [cormin];\n  }\n  var count = Math.max(tickCount, 2);\n  var step = getFormatStep(new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(cormax).sub(cormin).div(count - 1), allowDecimals, 0);\n  var values = [...(0,_util_arithmetic__WEBPACK_IMPORTED_MODULE_1__.rangeStep)(new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(cormin), new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(cormax), step), cormax];\n  if (allowDecimals === false) {\n    /*\n     * allowDecimals is false means that we want to have integer ticks.\n     * The step is guaranteed to be an integer in the code above which is great start\n     * but when the first step is not an integer, it will start stepping from a decimal value anyway.\n     * So we need to round all the values to integers after the fact.\n     */\n    values = values.map(value => Math.round(value));\n  }\n  return min > max ? (0,_util_utils__WEBPACK_IMPORTED_MODULE_2__.reverse)(values) : values;\n}\nvar getNiceTickValues = (0,_util_utils__WEBPACK_IMPORTED_MODULE_2__.memoize)(getNiceTickValuesFn);\nvar getTickValuesFixedDomain = (0,_util_utils__WEBPACK_IMPORTED_MODULE_2__.memoize)(getTickValuesFixedDomainFn);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/getNiceTickValues.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/index.js":
/*!*******************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/index.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getNiceTickValues: () => (/* reexport safe */ _getNiceTickValues__WEBPACK_IMPORTED_MODULE_0__.getNiceTickValues),
/* harmony export */   getTickValuesFixedDomain: () => (/* reexport safe */ _getNiceTickValues__WEBPACK_IMPORTED_MODULE_0__.getTickValuesFixedDomain)
/* harmony export */ });
/* harmony import */ var _getNiceTickValues__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getNiceTickValues */ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/getNiceTickValues.js");


/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/util/arithmetic.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/util/arithmetic.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDigitCount: () => (/* binding */ getDigitCount),\n/* harmony export */   interpolateNumber: () => (/* binding */ interpolateNumber),\n/* harmony export */   rangeStep: () => (/* binding */ rangeStep),\n/* harmony export */   uninterpolateNumber: () => (/* binding */ uninterpolateNumber),\n/* harmony export */   uninterpolateTruncation: () => (/* binding */ uninterpolateTruncation)\n/* harmony export */ });\n/* harmony import */ var decimal_js_light__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! decimal.js-light */ \"(pages-dir-browser)/../../node_modules/.pnpm/decimal.js-light@2.5.1/node_modules/decimal.js-light/decimal.js\");\n/* harmony import */ var decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(decimal_js_light__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/util/utils.js\");\n/**\n * @fileOverview Some common arithmetic methods\n * <AUTHOR> * @date 2015-09-17\n */\n\n\n\n/**\n * Get the digit count of a number.\n * If the absolute value is in the interval [0.1, 1), the result is 0.\n * If the absolute value is in the interval [0.01, 0.1), the digit count is -1.\n * If the absolute value is in the interval [0.001, 0.01), the digit count is -2.\n *\n * @param  {Number} value The number\n * @return {Integer}      Digit count\n */\nfunction getDigitCount(value) {\n  var result;\n  if (value === 0) {\n    result = 1;\n  } else {\n    result = Math.floor(new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(value).abs().log(10).toNumber()) + 1;\n  }\n  return result;\n}\n\n/**\n * Get the data in the interval [start, end) with a fixed step.\n * Also handles JS calculation precision issues.\n *\n * @param  {Decimal} start Start point\n * @param  {Decimal} end   End point, not included\n * @param  {Decimal} step  Step size\n * @return {Array}         Array of numbers\n */\nfunction rangeStep(start, end, step) {\n  var num = new (decimal_js_light__WEBPACK_IMPORTED_MODULE_0___default())(start);\n  var i = 0;\n  var result = [];\n\n  // magic number to prevent infinite loop\n  while (num.lt(end) && i < 100000) {\n    result.push(num.toNumber());\n    num = num.add(step);\n    i++;\n  }\n  return result;\n}\n\n/**\n * Linear interpolation of numbers.\n *\n * @param  {Number} a  Endpoint of the domain\n * @param  {Number} b  Endpoint of the domain\n * @param  {Number} t  A value in [0, 1]\n * @return {Number}    A value in the domain\n */\nvar interpolateNumber = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.curry)((a, b, t) => {\n  var newA = +a;\n  var newB = +b;\n  return newA + t * (newB - newA);\n});\n\n/**\n * Inverse operation of linear interpolation.\n *\n * @param  {Number} a Endpoint of the domain\n * @param  {Number} b Endpoint of the domain\n * @param  {Number} x Can be considered as an output value after interpolation\n * @return {Number}   When x is in the range a ~ b, the return value is in [0, 1]\n */\nvar uninterpolateNumber = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.curry)((a, b, x) => {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return (x - a) / diff;\n});\n\n/**\n * Inverse operation of linear interpolation with truncation.\n *\n * @param  {Number} a Endpoint of the domain\n * @param  {Number} b Endpoint of the domain\n * @param  {Number} x Can be considered as an output value after interpolation\n * @return {Number}   When x is in the interval a ~ b, the return value is in [0, 1].\n *                    When x is not in the interval a ~ b, it will be truncated to the interval a ~ b.\n */\nvar uninterpolateTruncation = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.curry)((a, b, x) => {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return Math.max(0, Math.min(1, (x - a) / diff));\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/util/arithmetic.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/util/utils.js":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/util/utils.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLACE_HOLDER: () => (/* binding */ PLACE_HOLDER),\n/* harmony export */   compose: () => (/* binding */ compose),\n/* harmony export */   curry: () => (/* binding */ curry),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   range: () => (/* binding */ range),\n/* harmony export */   reverse: () => (/* binding */ reverse)\n/* harmony export */ });\nvar identity = i => i;\nvar PLACE_HOLDER = {\n  '@@functional/placeholder': true\n};\nvar isPlaceHolder = val => val === PLACE_HOLDER;\nvar curry0 = fn => function _curried() {\n  if (arguments.length === 0 || arguments.length === 1 && isPlaceHolder(arguments.length <= 0 ? undefined : arguments[0])) {\n    return _curried;\n  }\n  return fn(...arguments);\n};\nvar curryN = (n, fn) => {\n  if (n === 1) {\n    return fn;\n  }\n  return curry0(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var argsLength = args.filter(arg => arg !== PLACE_HOLDER).length;\n    if (argsLength >= n) {\n      return fn(...args);\n    }\n    return curryN(n - argsLength, curry0(function () {\n      for (var _len2 = arguments.length, restArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        restArgs[_key2] = arguments[_key2];\n      }\n      var newArgs = args.map(arg => isPlaceHolder(arg) ? restArgs.shift() : arg);\n      return fn(...newArgs, ...restArgs);\n    }));\n  });\n};\nvar curry = fn => curryN(fn.length, fn);\nvar range = (begin, end) => {\n  var arr = [];\n  for (var i = begin; i < end; ++i) {\n    arr[i - begin] = i;\n  }\n  return arr;\n};\nvar map = curry((fn, arr) => {\n  if (Array.isArray(arr)) {\n    return arr.map(fn);\n  }\n  return Object.keys(arr).map(key => arr[key]).map(fn);\n});\nvar compose = function compose() {\n  for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    args[_key3] = arguments[_key3];\n  }\n  if (!args.length) {\n    return identity;\n  }\n  var fns = args.reverse();\n  // first function can receive multiply arguments\n  var firstFn = fns[0];\n  var tailsFn = fns.slice(1);\n  return function () {\n    return tailsFn.reduce((res, fn) => fn(res), firstFn(...arguments));\n  };\n};\nvar reverse = arr => {\n  if (Array.isArray(arr)) {\n    return arr.reverse();\n  }\n\n  // can be string\n  return arr.split('').reverse().join('');\n};\nvar memoize = fn => {\n  var lastArgs = null;\n  var lastResult = null;\n  return function () {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    if (lastArgs && args.every((val, i) => {\n      var _lastArgs;\n      return val === ((_lastArgs = lastArgs) === null || _lastArgs === void 0 ? void 0 : _lastArgs[i]);\n    })) {\n      return lastResult;\n    }\n    lastArgs = args;\n    lastResult = fn(...args);\n    return lastResult;\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/scale/util/utils.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/tooltip/translate.js":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/tooltip/translate.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTooltipCSSClassName: () => (/* binding */ getTooltipCSSClassName),\n/* harmony export */   getTooltipTranslate: () => (/* binding */ getTooltipTranslate),\n/* harmony export */   getTooltipTranslateXY: () => (/* binding */ getTooltipTranslateXY),\n/* harmony export */   getTransformStyle: () => (/* binding */ getTransformStyle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(pages-dir-browser)/../../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _DataUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../DataUtils */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/DataUtils.js\");\n\n\nvar CSS_CLASS_PREFIX = 'recharts-tooltip-wrapper';\nvar TOOLTIP_HIDDEN = {\n  visibility: 'hidden'\n};\nfunction getTooltipCSSClassName(_ref) {\n  var {\n    coordinate,\n    translateX,\n    translateY\n  } = _ref;\n  return (0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(CSS_CLASS_PREFIX, {\n    [\"\".concat(CSS_CLASS_PREFIX, \"-right\")]: (0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.isNumber)(translateX) && coordinate && (0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.isNumber)(coordinate.x) && translateX >= coordinate.x,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-left\")]: (0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.isNumber)(translateX) && coordinate && (0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.isNumber)(coordinate.x) && translateX < coordinate.x,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-bottom\")]: (0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.isNumber)(translateY) && coordinate && (0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.isNumber)(coordinate.y) && translateY >= coordinate.y,\n    [\"\".concat(CSS_CLASS_PREFIX, \"-top\")]: (0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.isNumber)(translateY) && coordinate && (0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.isNumber)(coordinate.y) && translateY < coordinate.y\n  });\n}\nfunction getTooltipTranslateXY(_ref2) {\n  var {\n    allowEscapeViewBox,\n    coordinate,\n    key,\n    offsetTopLeft,\n    position,\n    reverseDirection,\n    tooltipDimension,\n    viewBox,\n    viewBoxDimension\n  } = _ref2;\n  if (position && (0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.isNumber)(position[key])) {\n    return position[key];\n  }\n  var negative = coordinate[key] - tooltipDimension - (offsetTopLeft > 0 ? offsetTopLeft : 0);\n  var positive = coordinate[key] + offsetTopLeft;\n  if (allowEscapeViewBox[key]) {\n    return reverseDirection[key] ? negative : positive;\n  }\n  var viewBoxKey = viewBox[key];\n  if (viewBoxKey == null) {\n    return 0;\n  }\n  if (reverseDirection[key]) {\n    var _tooltipBoundary = negative;\n    var _viewBoxBoundary = viewBoxKey;\n    if (_tooltipBoundary < _viewBoxBoundary) {\n      return Math.max(positive, viewBoxKey);\n    }\n    return Math.max(negative, viewBoxKey);\n  }\n  if (viewBoxDimension == null) {\n    return 0;\n  }\n  var tooltipBoundary = positive + tooltipDimension;\n  var viewBoxBoundary = viewBoxKey + viewBoxDimension;\n  if (tooltipBoundary > viewBoxBoundary) {\n    return Math.max(negative, viewBoxKey);\n  }\n  return Math.max(positive, viewBoxKey);\n}\nfunction getTransformStyle(_ref3) {\n  var {\n    translateX,\n    translateY,\n    useTranslate3d\n  } = _ref3;\n  return {\n    transform: useTranslate3d ? \"translate3d(\".concat(translateX, \"px, \").concat(translateY, \"px, 0)\") : \"translate(\".concat(translateX, \"px, \").concat(translateY, \"px)\")\n  };\n}\nfunction getTooltipTranslate(_ref4) {\n  var {\n    allowEscapeViewBox,\n    coordinate,\n    offsetTopLeft,\n    position,\n    reverseDirection,\n    tooltipBox,\n    useTranslate3d,\n    viewBox\n  } = _ref4;\n  var cssProperties, translateX, translateY;\n  if (tooltipBox.height > 0 && tooltipBox.width > 0 && coordinate) {\n    translateX = getTooltipTranslateXY({\n      allowEscapeViewBox,\n      coordinate,\n      key: 'x',\n      offsetTopLeft,\n      position,\n      reverseDirection,\n      tooltipDimension: tooltipBox.width,\n      viewBox,\n      viewBoxDimension: viewBox.width\n    });\n    translateY = getTooltipTranslateXY({\n      allowEscapeViewBox,\n      coordinate,\n      key: 'y',\n      offsetTopLeft,\n      position,\n      reverseDirection,\n      tooltipDimension: tooltipBox.height,\n      viewBox,\n      viewBoxDimension: viewBox.height\n    });\n    cssProperties = getTransformStyle({\n      translateX,\n      translateY,\n      useTranslate3d\n    });\n  } else {\n    cssProperties = TOOLTIP_HIDDEN;\n  }\n  return {\n    cssProperties,\n    cssClasses: getTooltipCSSClassName({\n      translateX,\n      translateY,\n      coordinate\n    })\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/tooltip/translate.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/types.js":
/*!*************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/types.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventKeys: () => (/* binding */ EventKeys),\n/* harmony export */   FilteredElementKeyMap: () => (/* binding */ FilteredElementKeyMap),\n/* harmony export */   SVGElementPropKeys: () => (/* binding */ SVGElementPropKeys),\n/* harmony export */   adaptEventHandlers: () => (/* binding */ adaptEventHandlers),\n/* harmony export */   adaptEventsOfChild: () => (/* binding */ adaptEventsOfChild)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * Determines how values are stacked:\n *\n * - `none` is the default, it adds values on top of each other. No smarts. Negative values will overlap.\n * - `expand` make it so that the values always add up to 1 - so the chart will look like a rectangle.\n * - `wiggle` and `silhouette` tries to keep the chart centered.\n * - `sign` stacks positive values above zero and negative values below zero. Similar to `none` but handles negatives.\n * - `positive` ignores all negative values, and then behaves like \\`none\\`.\n *\n * Also see https://d3js.org/d3-shape/stack#stack-offsets\n * (note that the `diverging` offset in d3 is named `sign` in recharts)\n */\n\n/**\n * @deprecated use either `CartesianLayout` or `PolarLayout` instead.\n * Mixing both charts families leads to ambiguity in the type system.\n * These two layouts share very few properties, so it is best to keep them separate.\n */\n\n/**\n * @deprecated do not use: too many properties, mixing too many concepts, cartesian and polar together, everything optional.\n */\n\n//\n// Event Handler Types -- Copied from @types/react/index.d.ts and adapted for Props.\n//\n\nvar SVGContainerPropKeys = ['viewBox', 'children'];\nvar SVGElementPropKeys = ['aria-activedescendant', 'aria-atomic', 'aria-autocomplete', 'aria-busy', 'aria-checked', 'aria-colcount', 'aria-colindex', 'aria-colspan', 'aria-controls', 'aria-current', 'aria-describedby', 'aria-details', 'aria-disabled', 'aria-errormessage', 'aria-expanded', 'aria-flowto', 'aria-haspopup', 'aria-hidden', 'aria-invalid', 'aria-keyshortcuts', 'aria-label', 'aria-labelledby', 'aria-level', 'aria-live', 'aria-modal', 'aria-multiline', 'aria-multiselectable', 'aria-orientation', 'aria-owns', 'aria-placeholder', 'aria-posinset', 'aria-pressed', 'aria-readonly', 'aria-relevant', 'aria-required', 'aria-roledescription', 'aria-rowcount', 'aria-rowindex', 'aria-rowspan', 'aria-selected', 'aria-setsize', 'aria-sort', 'aria-valuemax', 'aria-valuemin', 'aria-valuenow', 'aria-valuetext', 'className', 'color', 'height', 'id', 'lang', 'max', 'media', 'method', 'min', 'name', 'style',\n/*\n * removed 'type' SVGElementPropKey because we do not currently use any SVG elements\n * that can use it, and it conflicts with the recharts prop 'type'\n * https://github.com/recharts/recharts/pull/3327\n * https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/type\n */\n// 'type',\n'target', 'width', 'role', 'tabIndex', 'accentHeight', 'accumulate', 'additive', 'alignmentBaseline', 'allowReorder', 'alphabetic', 'amplitude', 'arabicForm', 'ascent', 'attributeName', 'attributeType', 'autoReverse', 'azimuth', 'baseFrequency', 'baselineShift', 'baseProfile', 'bbox', 'begin', 'bias', 'by', 'calcMode', 'capHeight', 'clip', 'clipPath', 'clipPathUnits', 'clipRule', 'colorInterpolation', 'colorInterpolationFilters', 'colorProfile', 'colorRendering', 'contentScriptType', 'contentStyleType', 'cursor', 'cx', 'cy', 'd', 'decelerate', 'descent', 'diffuseConstant', 'direction', 'display', 'divisor', 'dominantBaseline', 'dur', 'dx', 'dy', 'edgeMode', 'elevation', 'enableBackground', 'end', 'exponent', 'externalResourcesRequired', 'fill', 'fillOpacity', 'fillRule', 'filter', 'filterRes', 'filterUnits', 'floodColor', 'floodOpacity', 'focusable', 'fontFamily', 'fontSize', 'fontSizeAdjust', 'fontStretch', 'fontStyle', 'fontVariant', 'fontWeight', 'format', 'from', 'fx', 'fy', 'g1', 'g2', 'glyphName', 'glyphOrientationHorizontal', 'glyphOrientationVertical', 'glyphRef', 'gradientTransform', 'gradientUnits', 'hanging', 'horizAdvX', 'horizOriginX', 'href', 'ideographic', 'imageRendering', 'in2', 'in', 'intercept', 'k1', 'k2', 'k3', 'k4', 'k', 'kernelMatrix', 'kernelUnitLength', 'kerning', 'keyPoints', 'keySplines', 'keyTimes', 'lengthAdjust', 'letterSpacing', 'lightingColor', 'limitingConeAngle', 'local', 'markerEnd', 'markerHeight', 'markerMid', 'markerStart', 'markerUnits', 'markerWidth', 'mask', 'maskContentUnits', 'maskUnits', 'mathematical', 'mode', 'numOctaves', 'offset', 'opacity', 'operator', 'order', 'orient', 'orientation', 'origin', 'overflow', 'overlinePosition', 'overlineThickness', 'paintOrder', 'panose1', 'pathLength', 'patternContentUnits', 'patternTransform', 'patternUnits', 'pointerEvents', 'pointsAtX', 'pointsAtY', 'pointsAtZ', 'preserveAlpha', 'preserveAspectRatio', 'primitiveUnits', 'r', 'radius', 'refX', 'refY', 'renderingIntent', 'repeatCount', 'repeatDur', 'requiredExtensions', 'requiredFeatures', 'restart', 'result', 'rotate', 'rx', 'ry', 'seed', 'shapeRendering', 'slope', 'spacing', 'specularConstant', 'specularExponent', 'speed', 'spreadMethod', 'startOffset', 'stdDeviation', 'stemh', 'stemv', 'stitchTiles', 'stopColor', 'stopOpacity', 'strikethroughPosition', 'strikethroughThickness', 'string', 'stroke', 'strokeDasharray', 'strokeDashoffset', 'strokeLinecap', 'strokeLinejoin', 'strokeMiterlimit', 'strokeOpacity', 'strokeWidth', 'surfaceScale', 'systemLanguage', 'tableValues', 'targetX', 'targetY', 'textAnchor', 'textDecoration', 'textLength', 'textRendering', 'to', 'transform', 'u1', 'u2', 'underlinePosition', 'underlineThickness', 'unicode', 'unicodeBidi', 'unicodeRange', 'unitsPerEm', 'vAlphabetic', 'values', 'vectorEffect', 'version', 'vertAdvY', 'vertOriginX', 'vertOriginY', 'vHanging', 'vIdeographic', 'viewTarget', 'visibility', 'vMathematical', 'widths', 'wordSpacing', 'writingMode', 'x1', 'x2', 'x', 'xChannelSelector', 'xHeight', 'xlinkActuate', 'xlinkArcrole', 'xlinkHref', 'xlinkRole', 'xlinkShow', 'xlinkTitle', 'xlinkType', 'xmlBase', 'xmlLang', 'xmlns', 'xmlnsXlink', 'xmlSpace', 'y1', 'y2', 'y', 'yChannelSelector', 'z', 'zoomAndPan', 'ref', 'key', 'angle'];\nvar PolyElementKeys = ['points', 'pathLength'];\n\n/** svg element types that have specific attribute filtration requirements */\n\n/** map of svg element types to unique svg attributes that belong to that element */\nvar FilteredElementKeyMap = {\n  svg: SVGContainerPropKeys,\n  polygon: PolyElementKeys,\n  polyline: PolyElementKeys\n};\nvar EventKeys = ['dangerouslySetInnerHTML', 'onCopy', 'onCopyCapture', 'onCut', 'onCutCapture', 'onPaste', 'onPasteCapture', 'onCompositionEnd', 'onCompositionEndCapture', 'onCompositionStart', 'onCompositionStartCapture', 'onCompositionUpdate', 'onCompositionUpdateCapture', 'onFocus', 'onFocusCapture', 'onBlur', 'onBlurCapture', 'onChange', 'onChangeCapture', 'onBeforeInput', 'onBeforeInputCapture', 'onInput', 'onInputCapture', 'onReset', 'onResetCapture', 'onSubmit', 'onSubmitCapture', 'onInvalid', 'onInvalidCapture', 'onLoad', 'onLoadCapture', 'onError', 'onErrorCapture', 'onKeyDown', 'onKeyDownCapture', 'onKeyPress', 'onKeyPressCapture', 'onKeyUp', 'onKeyUpCapture', 'onAbort', 'onAbortCapture', 'onCanPlay', 'onCanPlayCapture', 'onCanPlayThrough', 'onCanPlayThroughCapture', 'onDurationChange', 'onDurationChangeCapture', 'onEmptied', 'onEmptiedCapture', 'onEncrypted', 'onEncryptedCapture', 'onEnded', 'onEndedCapture', 'onLoadedData', 'onLoadedDataCapture', 'onLoadedMetadata', 'onLoadedMetadataCapture', 'onLoadStart', 'onLoadStartCapture', 'onPause', 'onPauseCapture', 'onPlay', 'onPlayCapture', 'onPlaying', 'onPlayingCapture', 'onProgress', 'onProgressCapture', 'onRateChange', 'onRateChangeCapture', 'onSeeked', 'onSeekedCapture', 'onSeeking', 'onSeekingCapture', 'onStalled', 'onStalledCapture', 'onSuspend', 'onSuspendCapture', 'onTimeUpdate', 'onTimeUpdateCapture', 'onVolumeChange', 'onVolumeChangeCapture', 'onWaiting', 'onWaitingCapture', 'onAuxClick', 'onAuxClickCapture', 'onClick', 'onClickCapture', 'onContextMenu', 'onContextMenuCapture', 'onDoubleClick', 'onDoubleClickCapture', 'onDrag', 'onDragCapture', 'onDragEnd', 'onDragEndCapture', 'onDragEnter', 'onDragEnterCapture', 'onDragExit', 'onDragExitCapture', 'onDragLeave', 'onDragLeaveCapture', 'onDragOver', 'onDragOverCapture', 'onDragStart', 'onDragStartCapture', 'onDrop', 'onDropCapture', 'onMouseDown', 'onMouseDownCapture', 'onMouseEnter', 'onMouseLeave', 'onMouseMove', 'onMouseMoveCapture', 'onMouseOut', 'onMouseOutCapture', 'onMouseOver', 'onMouseOverCapture', 'onMouseUp', 'onMouseUpCapture', 'onSelect', 'onSelectCapture', 'onTouchCancel', 'onTouchCancelCapture', 'onTouchEnd', 'onTouchEndCapture', 'onTouchMove', 'onTouchMoveCapture', 'onTouchStart', 'onTouchStartCapture', 'onPointerDown', 'onPointerDownCapture', 'onPointerMove', 'onPointerMoveCapture', 'onPointerUp', 'onPointerUpCapture', 'onPointerCancel', 'onPointerCancelCapture', 'onPointerEnter', 'onPointerEnterCapture', 'onPointerLeave', 'onPointerLeaveCapture', 'onPointerOver', 'onPointerOverCapture', 'onPointerOut', 'onPointerOutCapture', 'onGotPointerCapture', 'onGotPointerCaptureCapture', 'onLostPointerCapture', 'onLostPointerCaptureCapture', 'onScroll', 'onScrollCapture', 'onWheel', 'onWheelCapture', 'onAnimationStart', 'onAnimationStartCapture', 'onAnimationEnd', 'onAnimationEndCapture', 'onAnimationIteration', 'onAnimationIterationCapture', 'onTransitionEnd', 'onTransitionEndCapture'];\n\n/** The type of easing function to use for animations */\n\n/** Specifies the duration of animation, the unit of this option is ms. */\n\n/**\n * This object defines the offset of the chart area and width and height and brush and ... it's a bit too much information all in one.\n * We use it internally but let's not expose it to the outside world.\n * If you are looking for this information, instead import `ChartOffset` or `PlotArea` from `recharts`.\n */\n\n/**\n * The domain of axis.\n * This is the definition\n *\n * Numeric domain is always defined by an array of exactly two values, for the min and the max of the axis.\n * Categorical domain is defined as array of all possible values.\n *\n * Can be specified in many ways:\n * - array of numbers\n * - with special strings like 'dataMin' and 'dataMax'\n * - with special string math like 'dataMin - 100'\n * - with keyword 'auto'\n * - or a function\n * - array of functions\n * - or a combination of the above\n */\n\n/**\n * NumberDomain is an evaluated {@link AxisDomain}.\n * Unlike {@link AxisDomain}, it has no variety - it's a tuple of two number.\n * This is after all the keywords and functions were evaluated and what is left is [min, max].\n *\n * Know that the min, max values are not guaranteed to be nice numbers - values like -Infinity or NaN are possible.\n *\n * There are also `category` axes that have different things than numbers in their domain.\n */\n\n/** The props definition of base axis */\n\n/** Defines how ticks are placed and whether / how tick collisions are handled.\n * 'preserveStart' keeps the left tick on collision and ensures that the first tick is always shown.\n * 'preserveEnd' keeps the right tick on collision and ensures that the last tick is always shown.\n * 'preserveStartEnd' keeps the left tick on collision and ensures that the first and last ticks always show.\n * 'equidistantPreserveStart' selects a number N such that every nTh tick will be shown without collision.\n */\n\n/**\n * Ticks can be any type when the axis is the type of category.\n *\n * Ticks must be numbers when the axis is the type of number.\n */\n\nvar adaptEventHandlers = (props, newHandler) => {\n  if (!props || typeof props === 'function' || typeof props === 'boolean') {\n    return null;\n  }\n  var inputProps = props;\n  if (/*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(props)) {\n    inputProps = props.props;\n  }\n  if (typeof inputProps !== 'object' && typeof inputProps !== 'function') {\n    return null;\n  }\n  var out = {};\n  Object.keys(inputProps).forEach(key => {\n    if (EventKeys.includes(key)) {\n      out[key] = newHandler || (e => inputProps[key](inputProps, e));\n    }\n  });\n  return out;\n};\nvar getEventHandlerOfChild = (originalHandler, data, index) => e => {\n  originalHandler(data, index, e);\n  return null;\n};\nvar adaptEventsOfChild = (props, data, index) => {\n  if (props === null || typeof props !== 'object' && typeof props !== 'function') {\n    return null;\n  }\n  var out = null;\n  Object.keys(props).forEach(key => {\n    var item = props[key];\n    if (EventKeys.includes(key) && typeof item === 'function') {\n      if (!out) out = {};\n      out[key] = getEventHandlerOfChild(item, data, index);\n    }\n  });\n  return out;\n};\n\n/**\n * 'axis' means that all graphical items belonging to this axis tick will be highlighted,\n * and all will be present in the tooltip.\n * Tooltip with 'axis' will display when hovering on the chart background.\n *\n * 'item' means only the one graphical item being hovered will show in the tooltip.\n * Tooltip with 'item' will display when hovering over individual graphical items.\n *\n * This is calculated internally;\n * charts have a `defaultTooltipEventType` and `validateTooltipEventTypes` options.\n *\n * Users then use <Tooltip shared={true} /> or <Tooltip shared={false} /> to control their preference,\n * and charts will then see what is allowed and what is not.\n */\n\n/**\n * These are the props we are going to pass to an `activeDot` if it is a function or a custom Component\n */\n\n/**\n * This is the type of `activeDot` prop on:\n * - Area\n * - Line\n * - Radar\n */\n\n// TODO we need two different range objects, one for polar and another for cartesian layouts\n\n/**\n * Simplified version of the MouseEvent so that we don't have to mock the whole thing in tests.\n *\n * This is meant to represent the React.MouseEvent\n * which is a wrapper on top of https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent\n */\n\n/**\n * Coordinates relative to the top-left corner of the chart.\n * Also include scale which means that a chart that's scaled will return the same coordinates as a chart that's not scaled.\n *///# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/types.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/useAnimationId.js":
/*!**********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/useAnimationId.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAnimationId: () => (/* binding */ useAnimationId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _DataUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DataUtils */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/DataUtils.js\");\n\n\n\n/**\n * This hook returns a unique animation id for the object input.\n * If input changes (as in, reference equality is different), the animation id will change.\n * If input does not change, the animation id will not change.\n *\n * This is useful for animations. The Animate component\n * does have a `shouldReAnimate` prop but that doesn't seem to be doing what the name implies.\n * Also, we don't always want to re-animate on every render;\n * we only want to re-animate when the input changes. Not the internal state (e.g. `isAnimating`).\n *\n * @param input The object to check for changes. Uses reference equality (=== operator)\n * @param prefix Optional prefix to use for the animation id\n * @returns A unique animation id\n */\nfunction useAnimationId(input) {\n  var prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'animation-';\n  var animationId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)((0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.uniqueId)(prefix));\n  var prevProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(input);\n  if (prevProps.current !== input) {\n    animationId.current = (0,_DataUtils__WEBPACK_IMPORTED_MODULE_1__.uniqueId)(prefix);\n    prevProps.current = input;\n  }\n  return animationId.current;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/useAnimationId.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/useElementOffset.js":
/*!************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/useElementOffset.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useElementOffset: () => (/* binding */ useElementOffset)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar EPS = 1;\n\n/**\n * TODO this documentation does not reflect what this hook is doing, update it.\n * Stores the `offsetHeight`, `offsetLeft`, `offsetTop`, and `offsetWidth` of a DOM element.\n */\n\n/**\n * Use this to listen to element layout changes.\n *\n * Very useful for reading actual sizes of DOM elements relative to the viewport.\n *\n * @param extraDependencies use this to trigger new DOM dimensions read when any of these change. Good for things like payload and label, that will re-render something down in the children array, but you want to read the layout box of a parent.\n * @returns [lastElementOffset, updateElementOffset] most recent value, and setter. Pass the setter to a DOM element ref like this: `<div ref={updateElementOffset}>`\n */\nfunction useElementOffset() {\n  var extraDependencies = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var [lastBoundingBox, setLastBoundingBox] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n    height: 0,\n    left: 0,\n    top: 0,\n    width: 0\n  });\n  var updateBoundingBox = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(node => {\n    if (node != null) {\n      var rect = node.getBoundingClientRect();\n      var box = {\n        height: rect.height,\n        left: rect.left,\n        top: rect.top,\n        width: rect.width\n      };\n      if (Math.abs(box.height - lastBoundingBox.height) > EPS || Math.abs(box.left - lastBoundingBox.left) > EPS || Math.abs(box.top - lastBoundingBox.top) > EPS || Math.abs(box.width - lastBoundingBox.width) > EPS) {\n        setLastBoundingBox({\n          height: box.height,\n          left: box.left,\n          top: box.top,\n          width: box.width\n        });\n      }\n    }\n  }, [lastBoundingBox.width, lastBoundingBox.height, lastBoundingBox.top, lastBoundingBox.left, ...extraDependencies]);\n  return [lastBoundingBox, updateBoundingBox];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/useElementOffset.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/useReportScale.js":
/*!**********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/useReportScale.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useReportScale: () => (/* binding */ useReportScale)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _state_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../state/hooks */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/state/hooks.js\");\n/* harmony import */ var _state_selectors_containerSelectors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../state/selectors/containerSelectors */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/state/selectors/containerSelectors.js\");\n/* harmony import */ var _state_layoutSlice__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../state/layoutSlice */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/state/layoutSlice.js\");\n/* harmony import */ var _isWellBehavedNumber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./isWellBehavedNumber */ \"(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/isWellBehavedNumber.js\");\n\n\n\n\n\nfunction useReportScale() {\n  var dispatch = (0,_state_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppDispatch)();\n  var [ref, setRef] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  var scale = (0,_state_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)(_state_selectors_containerSelectors__WEBPACK_IMPORTED_MODULE_2__.selectContainerScale);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (ref == null) {\n      return;\n    }\n    var rect = ref.getBoundingClientRect();\n    var newScale = rect.width / ref.offsetWidth;\n    if ((0,_isWellBehavedNumber__WEBPACK_IMPORTED_MODULE_3__.isWellBehavedNumber)(newScale) && newScale !== scale) {\n      dispatch((0,_state_layoutSlice__WEBPACK_IMPORTED_MODULE_4__.setScale)(newScale));\n    }\n  }, [ref, dispatch, scale]);\n  return setRef;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVjaGFydHNAMy4xLjBfQHR5cGVzK3JlYWN0XzI0ZTRmZDVlYWZkZjlmNThkMDRhZTFiMmNlMWQ2YTE1L25vZGVfbW9kdWxlcy9yZWNoYXJ0cy9lczYvdXRpbC91c2VSZXBvcnRTY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTRDO0FBQ29CO0FBQ2E7QUFDN0I7QUFDWTtBQUNyRDtBQUNQLGlCQUFpQiw0REFBYztBQUMvQixzQkFBc0IsK0NBQVE7QUFDOUIsY0FBYyw0REFBYyxDQUFDLHFGQUFvQjtBQUNqRCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEseUVBQW1CO0FBQzNCLGVBQWUsNERBQVE7QUFDdkI7QUFDQSxHQUFHO0FBQ0g7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVjaGFydHNAMy4xLjBfQHR5cGVzK3JlYWN0XzI0ZTRmZDVlYWZkZjlmNThkMDRhZTFiMmNlMWQ2YTE1XFxub2RlX21vZHVsZXNcXHJlY2hhcnRzXFxlczZcXHV0aWxcXHVzZVJlcG9ydFNjYWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VBcHBEaXNwYXRjaCwgdXNlQXBwU2VsZWN0b3IgfSBmcm9tICcuLi9zdGF0ZS9ob29rcyc7XG5pbXBvcnQgeyBzZWxlY3RDb250YWluZXJTY2FsZSB9IGZyb20gJy4uL3N0YXRlL3NlbGVjdG9ycy9jb250YWluZXJTZWxlY3RvcnMnO1xuaW1wb3J0IHsgc2V0U2NhbGUgfSBmcm9tICcuLi9zdGF0ZS9sYXlvdXRTbGljZSc7XG5pbXBvcnQgeyBpc1dlbGxCZWhhdmVkTnVtYmVyIH0gZnJvbSAnLi9pc1dlbGxCZWhhdmVkTnVtYmVyJztcbmV4cG9ydCBmdW5jdGlvbiB1c2VSZXBvcnRTY2FsZSgpIHtcbiAgdmFyIGRpc3BhdGNoID0gdXNlQXBwRGlzcGF0Y2goKTtcbiAgdmFyIFtyZWYsIHNldFJlZl0gPSB1c2VTdGF0ZShudWxsKTtcbiAgdmFyIHNjYWxlID0gdXNlQXBwU2VsZWN0b3Ioc2VsZWN0Q29udGFpbmVyU2NhbGUpO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChyZWYgPT0gbnVsbCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB2YXIgcmVjdCA9IHJlZi5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICB2YXIgbmV3U2NhbGUgPSByZWN0LndpZHRoIC8gcmVmLm9mZnNldFdpZHRoO1xuICAgIGlmIChpc1dlbGxCZWhhdmVkTnVtYmVyKG5ld1NjYWxlKSAmJiBuZXdTY2FsZSAhPT0gc2NhbGUpIHtcbiAgICAgIGRpc3BhdGNoKHNldFNjYWxlKG5ld1NjYWxlKSk7XG4gICAgfVxuICB9LCBbcmVmLCBkaXNwYXRjaCwgc2NhbGVdKTtcbiAgcmV0dXJuIHNldFJlZjtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/recharts@3.1.0_@types+react_24e4fd5eafdf9f58d04ae1b2ce1d6a15/node_modules/recharts/es6/util/useReportScale.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/redux-thunk@3.1.0_redux@5.0.1/node_modules/redux-thunk/dist/redux-thunk.mjs":
/*!************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/redux-thunk@3.1.0_redux@5.0.1/node_modules/redux-thunk/dist/redux-thunk.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thunk: () => (/* binding */ thunk),\n/* harmony export */   withExtraArgument: () => (/* binding */ withExtraArgument)\n/* harmony export */ });\n// src/index.ts\nfunction createThunkMiddleware(extraArgument) {\n  const middleware = ({ dispatch, getState }) => (next) => (action) => {\n    if (typeof action === \"function\") {\n      return action(dispatch, getState, extraArgument);\n    }\n    return next(action);\n  };\n  return middleware;\n}\nvar thunk = createThunkMiddleware();\nvar withExtraArgument = createThunkMiddleware;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVkdXgtdGh1bmtAMy4xLjBfcmVkdXhANS4wLjEvbm9kZV9tb2R1bGVzL3JlZHV4LXRodW5rL2Rpc3QvcmVkdXgtdGh1bmsubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBLHdCQUF3QixvQkFBb0I7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBSUUiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlZHV4LXRodW5rQDMuMS4wX3JlZHV4QDUuMC4xXFxub2RlX21vZHVsZXNcXHJlZHV4LXRodW5rXFxkaXN0XFxyZWR1eC10aHVuay5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2luZGV4LnRzXG5mdW5jdGlvbiBjcmVhdGVUaHVua01pZGRsZXdhcmUoZXh0cmFBcmd1bWVudCkge1xuICBjb25zdCBtaWRkbGV3YXJlID0gKHsgZGlzcGF0Y2gsIGdldFN0YXRlIH0pID0+IChuZXh0KSA9PiAoYWN0aW9uKSA9PiB7XG4gICAgaWYgKHR5cGVvZiBhY3Rpb24gPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgcmV0dXJuIGFjdGlvbihkaXNwYXRjaCwgZ2V0U3RhdGUsIGV4dHJhQXJndW1lbnQpO1xuICAgIH1cbiAgICByZXR1cm4gbmV4dChhY3Rpb24pO1xuICB9O1xuICByZXR1cm4gbWlkZGxld2FyZTtcbn1cbnZhciB0aHVuayA9IGNyZWF0ZVRodW5rTWlkZGxld2FyZSgpO1xudmFyIHdpdGhFeHRyYUFyZ3VtZW50ID0gY3JlYXRlVGh1bmtNaWRkbGV3YXJlO1xuZXhwb3J0IHtcbiAgdGh1bmssXG4gIHdpdGhFeHRyYUFyZ3VtZW50XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/redux-thunk@3.1.0_redux@5.0.1/node_modules/redux-thunk/dist/redux-thunk.mjs\n"));

/***/ })

}]);