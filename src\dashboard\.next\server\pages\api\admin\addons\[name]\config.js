"use strict";(()=>{var e={};e.id=9039,e.ids=[9039],e.modules={20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},49538:(e,t,r)=>{r.r(t),r.d(t,{config:()=>m,default:()=>h,routeModule:()=>j});var o={};r.r(o),r.d(o,{default:()=>p});var s=r(93433),a=r(20264),n=r(20584);let i=require("fs/promises");var d=r.n(i),u=r(33873),c=r.n(u),l=r(65542),f=r(94506);async function p(e,t){try{if(!await (0,l.getServerSession)(e,t,f.authOptions))return t.status(401).json({error:"Unauthorized"});let{name:r}=e.query;if(!r||"string"!=typeof r)return t.status(400).json({error:"Invalid addon name"});let o=process.cwd().includes("dashboard")?c().resolve(process.cwd(),"..",".."):process.cwd(),s=[c().join(o,"src","addons"),c().join(o,"404-bot","src","addons"),c().join(o,"dist","addons"),c().join(o,"404-bot","dist","addons")],a="";for(let e of s)try{await d().access(e),a=e;break}catch{continue}if(!a)return t.status(500).json({error:"Addons directory not found",checkedPaths:s});let n=c().join(a,r);a.includes("src")||a.includes("dist");let i=[c().join(n,"config.yml"),c().join(n,"config.example.yml"),c().join(n,"example","config.yml"),c().join(n,"example","config.example.yml")];if("GET"===e.method){try{await d().access(n)}catch{return t.status(404).json({error:`Addon directory not found: ${r}`,path:n})}for(let e of i)try{let r=await d().readFile(e,"utf-8");return t.status(200).json({config:r,path:e})}catch(e){continue}return t.status(404).json({error:"Configuration not found",checkedPaths:i,addonPath:n})}if("POST"===e.method){let{config:s}=e.body;if(!s)return t.status(400).json({error:"No configuration provided"});try{await d().access(n)}catch{return t.status(404).json({error:`Addon directory not found: ${r}`,path:n})}let a=i[0],u=c().join(o,"dist","addons"),l=c().join(u,r,"config.yml"),f=[a];try{await d().access(u);let e=c().join(u,r);try{await d().access(e),f.push(l)}catch{await d().mkdir(e,{recursive:!0}),f.push(l)}}catch{}let p=[],h=!1;for(let e of f)try{await d().writeFile(e,s,"utf-8"),p.push({path:e,success:!0})}catch(t){p.push({path:e,success:!1,error:t.message}),h=!0}if(h)return t.status(500).json({error:"Failed to save configuration to some locations",results:p});return t.status(200).json({message:"Configuration saved successfully to all locations",results:p})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:e.message||"Internal server error",stack:void 0})}}let h=(0,n.M)(o,"default"),m=(0,n.M)(o,"config"),j=new s.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/admin/addons/[name]/config",pathname:"/api/admin/addons/[name]/config",bundlePath:"",filename:""},userland:o})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(49538));module.exports=o})();