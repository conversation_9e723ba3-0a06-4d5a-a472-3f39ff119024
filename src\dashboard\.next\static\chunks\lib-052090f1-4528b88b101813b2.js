"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7170],{36392:(t,n,e)=>{function i(t){return function(){return t}}e.d(n,{Wc:()=>x,qr:()=>B,Yu:()=>G,IA:()=>z,Wi:()=>m,PG:()=>T,lU:()=>_,Lx:()=>to,nV:()=>tf,ux:()=>t_,Xf:()=>tg,GZ:()=>td,UP:()=>tm,dy:()=>tv,n8:()=>g,t$:()=>tC,qI:()=>tN,YW:()=>tT,e9:()=>tS,Re:()=>tk,rM:()=>tw,HR:()=>V,hK:()=>b,BV:()=>C,j:()=>k,yD:()=>A,N8:()=>F,ZK:()=>P,IJ:()=>O});let r=Math.cos,o=Math.sin,u=Math.sqrt,s=Math.PI,a=2*s;var h=e(87606);function c(t){let n=3;return t.digits=function(e){if(!arguments.length)return n;if(null==e)n=null;else{let t=Math.floor(e);if(!(t>=0))throw RangeError(`invalid digits: ${e}`);n=t}return t},()=>new h.wA(n)}function l(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function f(t){this._context=t}function _(t){return new f(t)}function p(t){return t[0]}function y(t){return t[1]}function g(t,n){var e=i(!0),r=null,o=_,u=null,s=c(a);function a(i){var a,h,c,f=(i=l(i)).length,_=!1;for(null==r&&(u=o(c=s())),a=0;a<=f;++a)!(a<f&&e(h=i[a],a,i))===_&&((_=!_)?u.lineStart():u.lineEnd()),_&&u.point(+t(h,a,i),+n(h,a,i));if(c)return u=null,c+""||null}return t="function"==typeof t?t:void 0===t?p:i(t),n="function"==typeof n?n:void 0===n?y:i(n),a.x=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),a):t},a.y=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),a):n},a.defined=function(t){return arguments.length?(e="function"==typeof t?t:i(!!t),a):e},a.curve=function(t){return arguments.length?(o=t,null!=r&&(u=o(r)),a):o},a.context=function(t){return arguments.length?(null==t?r=u=null:u=o(r=t),a):r},a}function x(t,n,e){var r=null,o=i(!0),u=null,s=_,a=null,h=c(f);function f(i){var c,f,_,p,y,g=(i=l(i)).length,x=!1,d=Array(g),v=Array(g);for(null==u&&(a=s(y=h())),c=0;c<=g;++c){if(!(c<g&&o(p=i[c],c,i))===x)if(x=!x)f=c,a.areaStart(),a.lineStart();else{for(a.lineEnd(),a.lineStart(),_=c-1;_>=f;--_)a.point(d[_],v[_]);a.lineEnd(),a.areaEnd()}x&&(d[c]=+t(p,c,i),v[c]=+n(p,c,i),a.point(r?+r(p,c,i):d[c],e?+e(p,c,i):v[c]))}if(y)return a=null,y+""||null}function x(){return g().defined(o).curve(s).context(u)}return t="function"==typeof t?t:void 0===t?p:i(+t),n="function"==typeof n?n:void 0===n?i(0):i(+n),e="function"==typeof e?e:void 0===e?y:i(+e),f.x=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),r=null,f):t},f.x0=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),f):t},f.x1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:i(+t),f):r},f.y=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),e=null,f):n},f.y0=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),f):n},f.y1=function(t){return arguments.length?(e=null==t?null:"function"==typeof t?t:i(+t),f):e},f.lineX0=f.lineY0=function(){return x().x(t).y(n)},f.lineY1=function(){return x().x(t).y(e)},f.lineX1=function(){return x().x(r).y(n)},f.defined=function(t){return arguments.length?(o="function"==typeof t?t:i(!!t),f):o},f.curve=function(t){return arguments.length?(s=t,null!=u&&(a=s(u)),f):s},f.context=function(t){return arguments.length?(null==t?u=a=null:a=s(u=t),f):u},f}function d(t){this._curve=t}Array.prototype.slice,f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n)}}},d.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,n){this._curve.point(n*Math.sin(t),-(n*Math.cos(t)))}};class v{constructor(t,n){this._context=t,this._x=n}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,n,t,n):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+n)/2,t,this._y0,t,n)}this._x0=t,this._y0=n}}function m(t){return new v(t,!0)}function T(t){return new v(t,!1)}function w(t){return t.source}function M(t){return t.target}u(3);let b={draw(t,n){let e=u(n/s);t.moveTo(e,0),t.arc(0,0,e,0,a)}},C={draw(t,n){let e=u(n/5)/2;t.moveTo(-3*e,-e),t.lineTo(-e,-e),t.lineTo(-e,-3*e),t.lineTo(e,-3*e),t.lineTo(e,-e),t.lineTo(3*e,-e),t.lineTo(3*e,e),t.lineTo(e,e),t.lineTo(e,3*e),t.lineTo(-e,3*e),t.lineTo(-e,e),t.lineTo(-3*e,e),t.closePath()}},N=u(1/3),S=2*N,k={draw(t,n){let e=u(n/S),i=e*N;t.moveTo(0,-e),t.lineTo(i,0),t.lineTo(0,e),t.lineTo(-i,0),t.closePath()}},A={draw(t,n){let e=u(n),i=-e/2;t.rect(i,i,e,e)}},U=o(s/10)/o(7*s/10),D=o(a/10)*U,E=-r(a/10)*U,F={draw(t,n){let e=u(.8908130915292852*n),i=D*e,s=E*e;t.moveTo(0,-e),t.lineTo(i,s);for(let n=1;n<5;++n){let u=a*n/5,h=r(u),c=o(u);t.lineTo(c*e,-h*e),t.lineTo(h*i-c*s,c*i+h*s)}t.closePath()}},Y=u(3),P={draw(t,n){let e=-u(n/(3*Y));t.moveTo(0,2*e),t.lineTo(-Y*e,-e),t.lineTo(Y*e,-e),t.closePath()}};u(3);let H=u(3)/2,L=1/u(12),q=(L/2+1)*3,O={draw(t,n){let e=u(n/q),i=e/2,r=e*L,o=e*L+e,s=-i;t.moveTo(i,r),t.lineTo(i,o),t.lineTo(s,o),t.lineTo(-.5*i-H*r,H*i+-.5*r),t.lineTo(-.5*i-H*o,H*i+-.5*o),t.lineTo(-.5*s-H*o,H*s+-.5*o),t.lineTo(-.5*i+H*r,-.5*r-H*i),t.lineTo(-.5*i+H*o,-.5*o-H*i),t.lineTo(-.5*s+H*o,-.5*o-H*s),t.closePath()}};function V(t,n){let e=null,r=c(o);function o(){let i;if(e||(e=i=r()),t.apply(this,arguments).draw(e,+n.apply(this,arguments)),i)return e=null,i+""||null}return t="function"==typeof t?t:i(t||b),n="function"==typeof n?n:i(void 0===n?64:+n),o.type=function(n){return arguments.length?(t="function"==typeof n?n:i(n),o):t},o.size=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),o):n},o.context=function(t){return arguments.length?(e=null==t?null:t,o):e},o}function I(){}function Z(t,n,e){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+e)/6)}function j(t){this._context=t}function B(t){return new j(t)}function W(t){this._context=t}function G(t){return new W(t)}function R(t){this._context=t}function z(t){return new R(t)}function X(t,n){this._basis=new j(t),this._beta=n}function J(t,n,e){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-e),t._x2,t._y2)}function Q(t,n){this._context=t,this._k=(1-n)/6}function $(t,n){this._context=t,this._k=(1-n)/6}function K(t,n){this._context=t,this._k=(1-n)/6}function tt(t,n,e){var i=t._x1,r=t._y1,o=t._x2,u=t._y2;if(t._l01_a>1e-12){var s=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,a=3*t._l01_a*(t._l01_a+t._l12_a);i=(i*s-t._x0*t._l12_2a+t._x2*t._l01_2a)/a,r=(r*s-t._y0*t._l12_2a+t._y2*t._l01_2a)/a}if(t._l23_a>1e-12){var h=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,c=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*h+t._x1*t._l23_2a-n*t._l12_2a)/c,u=(u*h+t._y1*t._l23_2a-e*t._l12_2a)/c}t._context.bezierCurveTo(i,r,o,u,t._x2,t._y2)}function tn(t,n){this._context=t,this._alpha=n}function te(t,n){this._context=t,this._alpha=n}function ti(t,n){this._context=t,this._alpha=n}function tr(t){this._context=t}function to(t){return new tr(t)}function tu(t,n,e){var i=t._x1-t._x0,r=n-t._x1,o=(t._y1-t._y0)/(i||r<0&&-0),u=(e-t._y1)/(r||i<0&&-0);return((o<0?-1:1)+(u<0?-1:1))*Math.min(Math.abs(o),Math.abs(u),.5*Math.abs((o*r+u*i)/(i+r)))||0}function ts(t,n){var e=t._x1-t._x0;return e?(3*(t._y1-t._y0)/e-n)/2:n}function ta(t,n,e){var i=t._x0,r=t._y0,o=t._x1,u=t._y1,s=(o-i)/3;t._context.bezierCurveTo(i+s,r+s*n,o-s,u-s*e,o,u)}function th(t){this._context=t}function tc(t){this._context=new tl(t)}function tl(t){this._context=t}function tf(t){return new th(t)}function t_(t){return new tc(t)}function tp(t){this._context=t}function ty(t){var n,e,i=t.length-1,r=Array(i),o=Array(i),u=Array(i);for(r[0]=0,o[0]=2,u[0]=t[0]+2*t[1],n=1;n<i-1;++n)r[n]=1,o[n]=4,u[n]=4*t[n]+2*t[n+1];for(r[i-1]=2,o[i-1]=7,u[i-1]=8*t[i-1]+t[i],n=1;n<i;++n)e=r[n]/o[n-1],o[n]-=e,u[n]-=e*u[n-1];for(r[i-1]=u[i-1]/o[i-1],n=i-2;n>=0;--n)r[n]=(u[n]-r[n+1])/o[n];for(n=0,o[i-1]=(t[i]+r[i-1])/2;n<i-1;++n)o[n]=2*t[n+1]-r[n+1];return[r,o]}function tg(t){return new tp(t)}function tx(t,n){this._context=t,this._t=n}function td(t){return new tx(t,.5)}function tv(t){return new tx(t,0)}function tm(t){return new tx(t,1)}function tT(t,n){if((r=t.length)>1)for(var e,i,r,o=1,u=t[n[0]],s=u.length;o<r;++o)for(i=u,u=t[n[o]],e=0;e<s;++e)u[e][1]+=u[e][0]=isNaN(i[e][1])?i[e][0]:i[e][1]}function tw(t){for(var n=t.length,e=Array(n);--n>=0;)e[n]=n;return e}function tM(t,n){return t[n]}function tb(t){let n=[];return n.key=t,n}function tC(){var t=i([]),n=tw,e=tT,r=tM;function o(i){var o,u,s=Array.from(t.apply(this,arguments),tb),a=s.length,h=-1;for(let t of i)for(o=0,++h;o<a;++o)(s[o][h]=[0,+r(t,s[o].key,h,i)]).data=t;for(o=0,u=l(n(s));o<a;++o)s[u[o]].index=o;return e(s,u),s}return o.keys=function(n){return arguments.length?(t="function"==typeof n?n:i(Array.from(n)),o):t},o.value=function(t){return arguments.length?(r="function"==typeof t?t:i(+t),o):r},o.order=function(t){return arguments.length?(n=null==t?tw:"function"==typeof t?t:i(Array.from(t)),o):n},o.offset=function(t){return arguments.length?(e=null==t?tT:t,o):e},o}function tN(t,n){if((i=t.length)>0){for(var e,i,r,o=0,u=t[0].length;o<u;++o){for(r=e=0;e<i;++e)r+=t[e][o][1]||0;if(r)for(e=0;e<i;++e)t[e][o][1]/=r}tT(t,n)}}function tS(t,n){if((e=t.length)>0){for(var e,i=0,r=t[n[0]],o=r.length;i<o;++i){for(var u=0,s=0;u<e;++u)s+=t[u][i][1]||0;r[i][1]+=r[i][0]=-s/2}tT(t,n)}}function tk(t,n){if((r=t.length)>0&&(i=(e=t[n[0]]).length)>0){for(var e,i,r,o=0,u=1;u<i;++u){for(var s=0,a=0,h=0;s<r;++s){for(var c=t[n[s]],l=c[u][1]||0,f=(l-(c[u-1][1]||0))/2,_=0;_<s;++_){var p=t[n[_]];f+=(p[u][1]||0)-(p[u-1][1]||0)}a+=l,h+=f*l}e[u-1][1]+=e[u-1][0]=o,a&&(o-=h/a)}e[u-1][1]+=e[u-1][0]=o,tT(t,n)}}j.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Z(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Z(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},W.prototype={areaStart:I,areaEnd:I,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=n;break;case 1:this._point=2,this._x3=t,this._y3=n;break;case 2:this._point=3,this._x4=t,this._y4=n,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:Z(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},R.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var e=(this._x0+4*this._x1+t)/6,i=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(e,i):this._context.moveTo(e,i);break;case 3:this._point=4;default:Z(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},X.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,e=t.length-1;if(e>0)for(var i,r=t[0],o=n[0],u=t[e]-r,s=n[e]-o,a=-1;++a<=e;)i=a/e,this._basis.point(this._beta*t[a]+(1-this._beta)*(r+i*u),this._beta*n[a]+(1-this._beta)*(o+i*s));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}},function t(n){function e(t){return 1===n?new j(t):new X(t,n)}return e.beta=function(n){return t(+n)},e}(.85),Q.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:J(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:J(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function e(t){return new Q(t,n)}return e.tension=function(n){return t(+n)},e}(0),$.prototype={areaStart:I,areaEnd:I,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:J(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function e(t){return new $(t,n)}return e.tension=function(n){return t(+n)},e}(0),K.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:J(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function e(t){return new K(t,n)}return e.tension=function(n){return t(+n)},e}(0),tn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t*=1,n*=1,this._point){var e=this._x2-t,i=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+i*i,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:tt(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function e(t){return n?new tn(t,n):new Q(t,0)}return e.alpha=function(n){return t(+n)},e}(.5),te.prototype={areaStart:I,areaEnd:I,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){if(t*=1,n*=1,this._point){var e=this._x2-t,i=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+i*i,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:tt(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function e(t){return n?new te(t,n):new $(t,0)}return e.alpha=function(n){return t(+n)},e}(.5),ti.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t*=1,n*=1,this._point){var e=this._x2-t,i=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+i*i,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:tt(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function e(t){return n?new ti(t,n):new K(t,0)}return e.alpha=function(n){return t(+n)},e}(.5),tr.prototype={areaStart:I,areaEnd:I,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,n){t*=1,n*=1,this._point?this._context.lineTo(t,n):(this._point=1,this._context.moveTo(t,n))}},th.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:ta(this,this._t0,ts(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var e=NaN;if(n*=1,(t*=1)!==this._x1||n!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,ta(this,ts(this,e=tu(this,t,n)),e);break;default:ta(this,this._t0,e=tu(this,t,n))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n,this._t0=e}}},(tc.prototype=Object.create(th.prototype)).point=function(t,n){th.prototype.point.call(this,n,t)},tl.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,e,i,r,o){this._context.bezierCurveTo(n,t,i,e,o,r)}},tp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,n=this._y,e=t.length;if(e)if(this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]),2===e)this._context.lineTo(t[1],n[1]);else for(var i=ty(t),r=ty(n),o=0,u=1;u<e;++o,++u)this._context.bezierCurveTo(i[0][o],r[0][o],i[1][o],r[1][o],t[u],n[u]);(this._line||0!==this._line&&1===e)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,n){this._x.push(+t),this._y.push(+n)}},tx.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,n),this._context.lineTo(t,n);else{var e=this._x*(1-this._t)+t*this._t;this._context.lineTo(e,this._y),this._context.lineTo(e,n)}}this._x=t,this._y=n}}},60427:(t,n,e)=>{e.d(n,{WH:()=>f,Mb:()=>function t(){var n=N(nz()(x));return n.copy=function(){return nW(n,t())},h.apply(n,arguments)},Cr:()=>function t(){var n=Y(nz()).domain([.1,1,10]);return n.copy=function(){return nW(n,t()).base(n.base())},h.apply(n,arguments)},yj:()=>nX,q9:()=>nJ,xh:()=>function t(){var n=L(nz());return n.copy=function(){return nW(n,t()).constant(n.constant())},h.apply(n,arguments)},jo:()=>function t(n){var e;function i(t){return null==t||isNaN(t*=1)?e:t}return i.invert=i,i.domain=i.range=function(t){return arguments.length?(n=Array.from(t,y),i):n.slice()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(n).unknown(e)},n=arguments.length?Array.from(n,y):[0,1],N(i)},U4:()=>c,m4:()=>function t(){var n=M();return n.copy=function(){return T(n,t())},a.apply(n,arguments),N(n)},ZE:()=>function t(){let n=Y(w()).domain([1,10]);return n.copy=()=>T(n,t()).base(n.base()),a.apply(n,arguments),n},UM:()=>l,hq:()=>_,RW:()=>Z,QL:()=>function t(){var n,e=[],i=[],r=[];function o(){var t=0,n=Math.max(1,i.length);for(r=Array(n-1);++t<n;)r[t-1]=(0,s.Z4)(e,t/n);return u}function u(t){return null==t||isNaN(t*=1)?n:i[(0,s.h1)(r,t)]}return u.invertExtent=function(t){var n=i.indexOf(t);return n<0?[NaN,NaN]:[n>0?r[n-1]:e[0],n<r.length?r[n]:e[e.length-1]]},u.domain=function(t){if(!arguments.length)return e.slice();for(let n of(e=[],t))null==n||isNaN(n*=1)||e.push(n);return e.sort(s.V_),o()},u.range=function(t){return arguments.length?(i=Array.from(t),o()):i.slice()},u.unknown=function(t){return arguments.length?(n=t,u):n},u.quantiles=function(){return r.slice()},u.copy=function(){return t().domain(e).range(i).unknown(n)},a.apply(u,arguments)},WT:()=>function t(){var n,e=0,i=1,r=1,o=[.5],u=[0,1];function h(t){return null!=t&&t<=t?u[(0,s.h1)(o,t,0,r)]:n}function c(){var t=-1;for(o=Array(r);++t<r;)o[t]=((t+1)*i-(t-r)*e)/(r+1);return h}return h.domain=function(t){return arguments.length?([e,i]=t,e*=1,i*=1,c()):[e,i]},h.range=function(t){return arguments.length?(r=(u=Array.from(t)).length-1,c()):u.slice()},h.invertExtent=function(t){var n=u.indexOf(t);return n<0?[NaN,NaN]:n<1?[e,o[0]]:n>=r?[o[r-1],i]:[o[n-1],o[n]]},h.unknown=function(t){return arguments.length&&(n=t),h},h.thresholds=function(){return o.slice()},h.copy=function(){return t().domain([e,i]).range(u).unknown(n)},a.apply(N(h),arguments)},af:()=>function t(){var n,e=M(),i=[0,1],r=!1;function o(t){var i,o=Math.sign(i=e(t))*Math.sqrt(Math.abs(i));return isNaN(o)?n:r?Math.round(o):o}return o.invert=function(t){return e.invert(B(t))},o.domain=function(t){return arguments.length?(e.domain(t),o):e.domain()},o.range=function(t){return arguments.length?(e.range((i=Array.from(t,y)).map(B)),o):i.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(r=!!t,o):r},o.clamp=function(t){return arguments.length?(e.clamp(t),o):e.clamp()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return t(e.domain(),i).round(r).clamp(e.clamp()).unknown(n)},a.apply(o,arguments),N(o)},ex:()=>function t(){var n=N(nB()(x));return n.copy=function(){return nW(n,t())},h.apply(n,arguments)},M3:()=>function t(){var n=Y(nB()).domain([1,10]);return n.copy=function(){return nW(n,t()).base(n.base())},h.apply(n,arguments)},ui:()=>nG,T:()=>function t(){var n=[],e=x;function i(t){if(null!=t&&!isNaN(t*=1))return e(((0,s.h1)(n,t,1)-1)/(n.length-1))}return i.domain=function(t){if(!arguments.length)return n.slice();for(let e of(n=[],t))null==e||isNaN(e*=1)||n.push(e);return n.sort(s.V_),i},i.interpolator=function(t){return arguments.length?(e=t,i):e},i.range=function(){return n.map((t,i)=>e(i/(n.length-1)))},i.quantiles=function(t){return Array.from({length:t+1},(e,i)=>(0,s.YV)(n,i/t))},i.copy=function(){return t(e).domain(n)},h.apply(i,arguments)},ye:()=>nR,nV:()=>function t(){var n=L(nB());return n.copy=function(){return nW(n,t()).constant(n.constant())},h.apply(n,arguments)},Bv:()=>j,aX:()=>function t(){var n=L(w());return n.copy=function(){return T(n,t()).constant(n.constant())},a.apply(n,arguments)},c3:()=>function t(){var n,e=[.5],i=[0,1],r=1;function o(t){return null!=t&&t<=t?i[(0,s.h1)(e,t,0,r)]:n}return o.domain=function(t){return arguments.length?(r=Math.min((e=Array.from(t)).length,i.length-1),o):e.slice()},o.range=function(t){return arguments.length?(i=Array.from(t),r=Math.min(e.length,i.length-1),o):i.slice()},o.invertExtent=function(t){var n=i.indexOf(t);return[e[n-1],e[n]]},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return t().domain(e).range(i).unknown(n)},a.apply(o,arguments)},w7:()=>nZ,Pp:()=>nj,Vr:()=>C});var i,r,o,u,s=e(44546);function a(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function h(t,n){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof n?this.interpolator(n):this.range(n)}return this}let c=Symbol("implicit");function l(){var t=new s.Bu,n=[],e=[],i=c;function r(r){let o=t.get(r);if(void 0===o){if(i!==c)return i;t.set(r,o=n.push(r)-1)}return e[o%e.length]}return r.domain=function(e){if(!arguments.length)return n.slice();for(let i of(n=[],t=new s.Bu,e))t.has(i)||t.set(i,n.push(i)-1);return r},r.range=function(t){return arguments.length?(e=Array.from(t),r):e.slice()},r.unknown=function(t){return arguments.length?(i=t,r):i},r.copy=function(){return l(n,e).unknown(i)},a.apply(r,arguments),r}function f(){var t,n,e=l().unknown(void 0),i=e.domain,r=e.range,o=0,u=1,h=!1,c=0,_=0,p=.5;function y(){var e=i().length,a=u<o,l=a?u:o,f=a?o:u;t=(f-l)/Math.max(1,e-c+2*_),h&&(t=Math.floor(t)),l+=(f-l-t*(e-c))*p,n=t*(1-c),h&&(l=Math.round(l),n=Math.round(n));var y=(0,s.y1)(e).map(function(n){return l+t*n});return r(a?y.reverse():y)}return delete e.unknown,e.domain=function(t){return arguments.length?(i(t),y()):i()},e.range=function(t){return arguments.length?([o,u]=t,o*=1,u*=1,y()):[o,u]},e.rangeRound=function(t){return[o,u]=t,o*=1,u*=1,h=!0,y()},e.bandwidth=function(){return n},e.step=function(){return t},e.round=function(t){return arguments.length?(h=!!t,y()):h},e.padding=function(t){return arguments.length?(c=Math.min(1,_=+t),y()):c},e.paddingInner=function(t){return arguments.length?(c=Math.min(1,t),y()):c},e.paddingOuter=function(t){return arguments.length?(_=+t,y()):_},e.align=function(t){return arguments.length?(p=Math.max(0,Math.min(1,t)),y()):p},e.copy=function(){return f(i(),[o,u]).round(h).paddingInner(c).paddingOuter(_).align(p)},a.apply(y(),arguments)}function _(){return function t(n){var e=n.copy;return n.padding=n.paddingOuter,delete n.paddingInner,delete n.paddingOuter,n.copy=function(){return t(e())},n}(f.apply(null,arguments).paddingInner(1))}var p=e(71928);function y(t){return+t}var g=[0,1];function x(t){return t}function d(t,n){var e;return(n-=t*=1)?function(e){return(e-t)/n}:(e=isNaN(n)?NaN:.5,function(){return e})}function v(t,n,e){var i=t[0],r=t[1],o=n[0],u=n[1];return r<i?(i=d(r,i),o=e(u,o)):(i=d(i,r),o=e(o,u)),function(t){return o(i(t))}}function m(t,n,e){var i=Math.min(t.length,n.length)-1,r=Array(i),o=Array(i),u=-1;for(t[i]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++u<i;)r[u]=d(t[u],t[u+1]),o[u]=e(n[u],n[u+1]);return function(n){var e=(0,s.h1)(t,n,1,i)-1;return o[e](r[e](n))}}function T(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function w(){var t,n,e,i,r,o,u=g,s=g,a=p.GW,h=x;function c(){var t,n,e,a=Math.min(u.length,s.length);return h!==x&&(t=u[0],n=u[a-1],t>n&&(e=t,t=n,n=e),h=function(e){return Math.max(t,Math.min(n,e))}),i=a>2?m:v,r=o=null,l}function l(n){return null==n||isNaN(n*=1)?e:(r||(r=i(u.map(t),s,a)))(t(h(n)))}return l.invert=function(e){return h(n((o||(o=i(s,u.map(t),p.Dj)))(e)))},l.domain=function(t){return arguments.length?(u=Array.from(t,y),c()):u.slice()},l.range=function(t){return arguments.length?(s=Array.from(t),c()):s.slice()},l.rangeRound=function(t){return s=Array.from(t),a=p.sH,c()},l.clamp=function(t){return arguments.length?(h=!!t||x,c()):h!==x},l.interpolate=function(t){return arguments.length?(a=t,c()):a},l.unknown=function(t){return arguments.length?(e=t,l):e},function(e,i){return t=e,n=i,c()}}function M(){return w()(x,x)}var b=e(20209);function C(t,n,e,i){var r,o=(0,s.sG)(t,n,e);switch((i=(0,b.Gp)(null==i?",f":i)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(n));return null!=i.precision||isNaN(r=(0,b.dT)(o,u))||(i.precision=r),(0,b.s)(i,u);case"":case"e":case"g":case"p":case"r":null!=i.precision||isNaN(r=(0,b.Pj)(o,Math.max(Math.abs(t),Math.abs(n))))||(i.precision=r-("e"===i.type));break;case"f":case"%":null!=i.precision||isNaN(r=(0,b.RT)(o))||(i.precision=r-("%"===i.type)*2)}return(0,b.GP)(i)}function N(t){var n=t.domain;return t.ticks=function(t){var e=n();return(0,s.Zc)(e[0],e[e.length-1],null==t?10:t)},t.tickFormat=function(t,e){var i=n();return C(i[0],i[i.length-1],null==t?10:t,e)},t.nice=function(e){null==e&&(e=10);var i,r,o=n(),u=0,a=o.length-1,h=o[u],c=o[a],l=10;for(c<h&&(r=h,h=c,c=r,r=u,u=a,a=r);l-- >0;){if((r=(0,s.lq)(h,c,e))===i)return o[u]=h,o[a]=c,n(o);if(r>0)h=Math.floor(h/r)*r,c=Math.ceil(c/r)*r;else if(r<0)h=Math.ceil(h*r)/r,c=Math.floor(c*r)/r;else break;i=r}return t},t}function S(t,n){t=t.slice();var e,i=0,r=t.length-1,o=t[i],u=t[r];return u<o&&(e=i,i=r,r=e,e=o,o=u,u=e),t[i]=n.floor(o),t[r]=n.ceil(u),t}function k(t){return Math.log(t)}function A(t){return Math.exp(t)}function U(t){return-Math.log(-t)}function D(t){return-Math.exp(-t)}function E(t){return isFinite(t)?+("1e"+t):t<0?0:t}function F(t){return(n,e)=>-t(-n,e)}function Y(t){let n,e,i=t(k,A),r=i.domain,o=10;function u(){var u,s;return n=(u=o)===Math.E?Math.log:10===u&&Math.log10||2===u&&Math.log2||(u=Math.log(u),t=>Math.log(t)/u),e=10===(s=o)?E:s===Math.E?Math.exp:t=>Math.pow(s,t),r()[0]<0?(n=F(n),e=F(e),t(U,D)):t(k,A),i}return i.base=function(t){return arguments.length?(o=+t,u()):o},i.domain=function(t){return arguments.length?(r(t),u()):r()},i.ticks=t=>{let i,u,a=r(),h=a[0],c=a[a.length-1],l=c<h;l&&([h,c]=[c,h]);let f=n(h),_=n(c),p=null==t?10:+t,y=[];if(!(o%1)&&_-f<p){if(f=Math.floor(f),_=Math.ceil(_),h>0){for(;f<=_;++f)for(i=1;i<o;++i)if(!((u=f<0?i/e(-f):i*e(f))<h)){if(u>c)break;y.push(u)}}else for(;f<=_;++f)for(i=o-1;i>=1;--i)if(!((u=f>0?i/e(-f):i*e(f))<h)){if(u>c)break;y.push(u)}2*y.length<p&&(y=(0,s.Zc)(h,c,p))}else y=(0,s.Zc)(f,_,Math.min(_-f,p)).map(e);return l?y.reverse():y},i.tickFormat=(t,r)=>{if(null==t&&(t=10),null==r&&(r=10===o?"s":","),"function"!=typeof r&&(o%1||null!=(r=(0,b.Gp)(r)).precision||(r.trim=!0),r=(0,b.GP)(r)),t===1/0)return r;let u=Math.max(1,o*t/i.ticks().length);return t=>{let i=t/e(Math.round(n(t)));return i*o<o-.5&&(i*=o),i<=u?r(t):""}},i.nice=()=>r(S(r(),{floor:t=>e(Math.floor(n(t))),ceil:t=>e(Math.ceil(n(t)))})),i}function P(t){return function(n){return Math.sign(n)*Math.log1p(Math.abs(n/t))}}function H(t){return function(n){return Math.sign(n)*Math.expm1(Math.abs(n))*t}}function L(t){var n=1,e=t(P(1),H(n));return e.constant=function(e){return arguments.length?t(P(n=+e),H(n)):n},N(e)}function q(t){return function(n){return n<0?-Math.pow(-n,t):Math.pow(n,t)}}function O(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function V(t){return t<0?-t*t:t*t}function I(t){var n=t(x,x),e=1;return n.exponent=function(n){return arguments.length?1==(e=+n)?t(x,x):.5===e?t(O,V):t(q(e),q(1/e)):e},N(n)}function Z(){var t=I(w());return t.copy=function(){return T(t,Z()).exponent(t.exponent())},a.apply(t,arguments),t}function j(){return Z.apply(null,arguments).exponent(.5)}function B(t){return Math.sign(t)*t*t}let W=new Date,G=new Date;function R(t,n,e,i){function r(n){return t(n=0==arguments.length?new Date:new Date(+n)),n}return r.floor=n=>(t(n=new Date(+n)),n),r.ceil=e=>(t(e=new Date(e-1)),n(e,1),t(e),e),r.round=t=>{let n=r(t),e=r.ceil(t);return t-n<e-t?n:e},r.offset=(t,e)=>(n(t=new Date(+t),null==e?1:Math.floor(e)),t),r.range=(e,i,o)=>{let u,s=[];if(e=r.ceil(e),o=null==o?1:Math.floor(o),!(e<i)||!(o>0))return s;do s.push(u=new Date(+e)),n(e,o),t(e);while(u<e&&e<i);return s},r.filter=e=>R(n=>{if(n>=n)for(;t(n),!e(n);)n.setTime(n-1)},(t,i)=>{if(t>=t)if(i<0)for(;++i<=0;)for(;n(t,-1),!e(t););else for(;--i>=0;)for(;n(t,1),!e(t););}),e&&(r.count=(n,i)=>(W.setTime(+n),G.setTime(+i),t(W),t(G),Math.floor(e(W,G))),r.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?r.filter(i?n=>i(n)%t==0:n=>r.count(0,n)%t==0):r:null),r}let z=R(()=>{},(t,n)=>{t.setTime(+t+n)},(t,n)=>n-t);z.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?R(n=>{n.setTime(Math.floor(n/t)*t)},(n,e)=>{n.setTime(+n+e*t)},(n,e)=>(e-n)/t):z:null,z.range;let X=R(t=>{t.setTime(t-t.getMilliseconds())},(t,n)=>{t.setTime(+t+1e3*n)},(t,n)=>(n-t)/1e3,t=>t.getUTCSeconds());X.range;let J=R(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,n)=>{t.setTime(+t+6e4*n)},(t,n)=>(n-t)/6e4,t=>t.getMinutes());J.range;let Q=R(t=>{t.setUTCSeconds(0,0)},(t,n)=>{t.setTime(+t+6e4*n)},(t,n)=>(n-t)/6e4,t=>t.getUTCMinutes());Q.range;let $=R(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,n)=>{t.setTime(+t+36e5*n)},(t,n)=>(n-t)/36e5,t=>t.getHours());$.range;let K=R(t=>{t.setUTCMinutes(0,0,0)},(t,n)=>{t.setTime(+t+36e5*n)},(t,n)=>(n-t)/36e5,t=>t.getUTCHours());K.range;let tt=R(t=>t.setHours(0,0,0,0),(t,n)=>t.setDate(t.getDate()+n),(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);tt.range;let tn=R(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/864e5,t=>t.getUTCDate()-1);tn.range;let te=R(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/864e5,t=>Math.floor(t/864e5));function ti(t){return R(n=>{n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)},(t,n)=>{t.setDate(t.getDate()+7*n)},(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}te.range;let tr=ti(0),to=ti(1),tu=ti(2),ts=ti(3),ta=ti(4),th=ti(5),tc=ti(6);function tl(t){return R(n=>{n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+7*n)},(t,n)=>(n-t)/6048e5)}tr.range,to.range,tu.range,ts.range,ta.range,th.range,tc.range;let tf=tl(0),t_=tl(1),tp=tl(2),ty=tl(3),tg=tl(4),tx=tl(5),td=tl(6);tf.range,t_.range,tp.range,ty.range,tg.range,tx.range,td.range;let tv=R(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,n)=>{t.setMonth(t.getMonth()+n)},(t,n)=>n.getMonth()-t.getMonth()+(n.getFullYear()-t.getFullYear())*12,t=>t.getMonth());tv.range;let tm=R(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCMonth(t.getUTCMonth()+n)},(t,n)=>n.getUTCMonth()-t.getUTCMonth()+(n.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());tm.range;let tT=R(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n)},(t,n)=>n.getFullYear()-t.getFullYear(),t=>t.getFullYear());tT.every=t=>isFinite(t=Math.floor(t))&&t>0?R(n=>{n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)},(n,e)=>{n.setFullYear(n.getFullYear()+e*t)}):null,tT.range;let tw=R(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n)},(t,n)=>n.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function tM(t,n,e,i,r,o){let u=[[X,1,1e3],[X,5,5e3],[X,15,15e3],[X,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[r,1,36e5],[r,3,108e5],[r,6,216e5],[r,12,432e5],[i,1,864e5],[i,2,1728e5],[e,1,6048e5],[n,1,2592e6],[n,3,7776e6],[t,1,31536e6]];function a(n,e,i){let r=Math.abs(e-n)/i,o=(0,s.yl)(([,,t])=>t).right(u,r);if(o===u.length)return t.every((0,s.sG)(n/31536e6,e/31536e6,i));if(0===o)return z.every(Math.max((0,s.sG)(n,e,i),1));let[a,h]=u[r/u[o-1][2]<u[o][2]/r?o-1:o];return a.every(h)}return[function(t,n,e){let i=n<t;i&&([t,n]=[n,t]);let r=e&&"function"==typeof e.range?e:a(t,n,e),o=r?r.range(t,+n+1):[];return i?o.reverse():o},a]}tw.every=t=>isFinite(t=Math.floor(t))&&t>0?R(n=>{n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)},(n,e)=>{n.setUTCFullYear(n.getUTCFullYear()+e*t)}):null,tw.range;let[tb,tC]=tM(tw,tm,tf,te,K,Q),[tN,tS]=tM(tT,tv,tr,tt,$,J);function tk(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return n.setFullYear(t.y),n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function tA(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return n.setUTCFullYear(t.y),n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function tU(t,n,e){return{y:t,m:n,d:e,H:0,M:0,S:0,L:0}}var tD={"-":"",_:" ",0:"0"},tE=/^\s*\d+/,tF=/^%/,tY=/[\\^$*+?|[\]().{}]/g;function tP(t,n,e){var i=t<0?"-":"",r=(i?-t:t)+"",o=r.length;return i+(o<e?Array(e-o+1).join(n)+r:r)}function tH(t){return t.replace(tY,"\\$&")}function tL(t){return RegExp("^(?:"+t.map(tH).join("|")+")","i")}function tq(t){return new Map(t.map((t,n)=>[t.toLowerCase(),n]))}function tO(t,n,e){var i=tE.exec(n.slice(e,e+1));return i?(t.w=+i[0],e+i[0].length):-1}function tV(t,n,e){var i=tE.exec(n.slice(e,e+1));return i?(t.u=+i[0],e+i[0].length):-1}function tI(t,n,e){var i=tE.exec(n.slice(e,e+2));return i?(t.U=+i[0],e+i[0].length):-1}function tZ(t,n,e){var i=tE.exec(n.slice(e,e+2));return i?(t.V=+i[0],e+i[0].length):-1}function tj(t,n,e){var i=tE.exec(n.slice(e,e+2));return i?(t.W=+i[0],e+i[0].length):-1}function tB(t,n,e){var i=tE.exec(n.slice(e,e+4));return i?(t.y=+i[0],e+i[0].length):-1}function tW(t,n,e){var i=tE.exec(n.slice(e,e+2));return i?(t.y=+i[0]+(+i[0]>68?1900:2e3),e+i[0].length):-1}function tG(t,n,e){var i=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return i?(t.Z=i[1]?0:-(i[2]+(i[3]||"00")),e+i[0].length):-1}function tR(t,n,e){var i=tE.exec(n.slice(e,e+1));return i?(t.q=3*i[0]-3,e+i[0].length):-1}function tz(t,n,e){var i=tE.exec(n.slice(e,e+2));return i?(t.m=i[0]-1,e+i[0].length):-1}function tX(t,n,e){var i=tE.exec(n.slice(e,e+2));return i?(t.d=+i[0],e+i[0].length):-1}function tJ(t,n,e){var i=tE.exec(n.slice(e,e+3));return i?(t.m=0,t.d=+i[0],e+i[0].length):-1}function tQ(t,n,e){var i=tE.exec(n.slice(e,e+2));return i?(t.H=+i[0],e+i[0].length):-1}function t$(t,n,e){var i=tE.exec(n.slice(e,e+2));return i?(t.M=+i[0],e+i[0].length):-1}function tK(t,n,e){var i=tE.exec(n.slice(e,e+2));return i?(t.S=+i[0],e+i[0].length):-1}function t0(t,n,e){var i=tE.exec(n.slice(e,e+3));return i?(t.L=+i[0],e+i[0].length):-1}function t1(t,n,e){var i=tE.exec(n.slice(e,e+6));return i?(t.L=Math.floor(i[0]/1e3),e+i[0].length):-1}function t2(t,n,e){var i=tF.exec(n.slice(e,e+1));return i?e+i[0].length:-1}function t3(t,n,e){var i=tE.exec(n.slice(e));return i?(t.Q=+i[0],e+i[0].length):-1}function t5(t,n,e){var i=tE.exec(n.slice(e));return i?(t.s=+i[0],e+i[0].length):-1}function t4(t,n){return tP(t.getDate(),n,2)}function t6(t,n){return tP(t.getHours(),n,2)}function t7(t,n){return tP(t.getHours()%12||12,n,2)}function t8(t,n){return tP(1+tt.count(tT(t),t),n,3)}function t9(t,n){return tP(t.getMilliseconds(),n,3)}function nt(t,n){return t9(t,n)+"000"}function nn(t,n){return tP(t.getMonth()+1,n,2)}function ne(t,n){return tP(t.getMinutes(),n,2)}function ni(t,n){return tP(t.getSeconds(),n,2)}function nr(t){var n=t.getDay();return 0===n?7:n}function no(t,n){return tP(tr.count(tT(t)-1,t),n,2)}function nu(t){var n=t.getDay();return n>=4||0===n?ta(t):ta.ceil(t)}function ns(t,n){return t=nu(t),tP(ta.count(tT(t),t)+(4===tT(t).getDay()),n,2)}function na(t){return t.getDay()}function nh(t,n){return tP(to.count(tT(t)-1,t),n,2)}function nc(t,n){return tP(t.getFullYear()%100,n,2)}function nl(t,n){return tP((t=nu(t)).getFullYear()%100,n,2)}function nf(t,n){return tP(t.getFullYear()%1e4,n,4)}function n_(t,n){var e=t.getDay();return tP((t=e>=4||0===e?ta(t):ta.ceil(t)).getFullYear()%1e4,n,4)}function np(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+tP(n/60|0,"0",2)+tP(n%60,"0",2)}function ny(t,n){return tP(t.getUTCDate(),n,2)}function ng(t,n){return tP(t.getUTCHours(),n,2)}function nx(t,n){return tP(t.getUTCHours()%12||12,n,2)}function nd(t,n){return tP(1+tn.count(tw(t),t),n,3)}function nv(t,n){return tP(t.getUTCMilliseconds(),n,3)}function nm(t,n){return nv(t,n)+"000"}function nT(t,n){return tP(t.getUTCMonth()+1,n,2)}function nw(t,n){return tP(t.getUTCMinutes(),n,2)}function nM(t,n){return tP(t.getUTCSeconds(),n,2)}function nb(t){var n=t.getUTCDay();return 0===n?7:n}function nC(t,n){return tP(tf.count(tw(t)-1,t),n,2)}function nN(t){var n=t.getUTCDay();return n>=4||0===n?tg(t):tg.ceil(t)}function nS(t,n){return t=nN(t),tP(tg.count(tw(t),t)+(4===tw(t).getUTCDay()),n,2)}function nk(t){return t.getUTCDay()}function nA(t,n){return tP(t_.count(tw(t)-1,t),n,2)}function nU(t,n){return tP(t.getUTCFullYear()%100,n,2)}function nD(t,n){return tP((t=nN(t)).getUTCFullYear()%100,n,2)}function nE(t,n){return tP(t.getUTCFullYear()%1e4,n,4)}function nF(t,n){var e=t.getUTCDay();return tP((t=e>=4||0===e?tg(t):tg.ceil(t)).getUTCFullYear()%1e4,n,4)}function nY(){return"+0000"}function nP(){return"%"}function nH(t){return+t}function nL(t){return Math.floor(t/1e3)}r=(i=function(t){var n=t.dateTime,e=t.date,i=t.time,r=t.periods,o=t.days,u=t.shortDays,s=t.months,a=t.shortMonths,h=tL(r),c=tq(r),l=tL(o),f=tq(o),_=tL(u),p=tq(u),y=tL(s),g=tq(s),x=tL(a),d=tq(a),v={a:function(t){return u[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return a[t.getMonth()]},B:function(t){return s[t.getMonth()]},c:null,d:t4,e:t4,f:nt,g:nl,G:n_,H:t6,I:t7,j:t8,L:t9,m:nn,M:ne,p:function(t){return r[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:nH,s:nL,S:ni,u:nr,U:no,V:ns,w:na,W:nh,x:null,X:null,y:nc,Y:nf,Z:np,"%":nP},m={a:function(t){return u[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return a[t.getUTCMonth()]},B:function(t){return s[t.getUTCMonth()]},c:null,d:ny,e:ny,f:nm,g:nD,G:nF,H:ng,I:nx,j:nd,L:nv,m:nT,M:nw,p:function(t){return r[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:nH,s:nL,S:nM,u:nb,U:nC,V:nS,w:nk,W:nA,x:null,X:null,y:nU,Y:nE,Z:nY,"%":nP},T={a:function(t,n,e){var i=_.exec(n.slice(e));return i?(t.w=p.get(i[0].toLowerCase()),e+i[0].length):-1},A:function(t,n,e){var i=l.exec(n.slice(e));return i?(t.w=f.get(i[0].toLowerCase()),e+i[0].length):-1},b:function(t,n,e){var i=x.exec(n.slice(e));return i?(t.m=d.get(i[0].toLowerCase()),e+i[0].length):-1},B:function(t,n,e){var i=y.exec(n.slice(e));return i?(t.m=g.get(i[0].toLowerCase()),e+i[0].length):-1},c:function(t,e,i){return b(t,n,e,i)},d:tX,e:tX,f:t1,g:tW,G:tB,H:tQ,I:tQ,j:tJ,L:t0,m:tz,M:t$,p:function(t,n,e){var i=h.exec(n.slice(e));return i?(t.p=c.get(i[0].toLowerCase()),e+i[0].length):-1},q:tR,Q:t3,s:t5,S:tK,u:tV,U:tI,V:tZ,w:tO,W:tj,x:function(t,n,i){return b(t,e,n,i)},X:function(t,n,e){return b(t,i,n,e)},y:tW,Y:tB,Z:tG,"%":t2};function w(t,n){return function(e){var i,r,o,u=[],s=-1,a=0,h=t.length;for(e instanceof Date||(e=new Date(+e));++s<h;)37===t.charCodeAt(s)&&(u.push(t.slice(a,s)),null!=(r=tD[i=t.charAt(++s)])?i=t.charAt(++s):r="e"===i?" ":"0",(o=n[i])&&(i=o(e,r)),u.push(i),a=s+1);return u.push(t.slice(a,s)),u.join("")}}function M(t,n){return function(e){var i,r,o=tU(1900,void 0,1);if(b(o,t,e+="",0)!=e.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!n||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(i=(r=(i=tA(tU(o.y,0,1))).getUTCDay())>4||0===r?t_.ceil(i):t_(i),i=tn.offset(i,(o.V-1)*7),o.y=i.getUTCFullYear(),o.m=i.getUTCMonth(),o.d=i.getUTCDate()+(o.w+6)%7):(i=(r=(i=tk(tU(o.y,0,1))).getDay())>4||0===r?to.ceil(i):to(i),i=tt.offset(i,(o.V-1)*7),o.y=i.getFullYear(),o.m=i.getMonth(),o.d=i.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:+("W"in o)),r="Z"in o?tA(tU(o.y,0,1)).getUTCDay():tk(tU(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(r+5)%7:o.w+7*o.U-(r+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,tA(o)):tk(o)}}function b(t,n,e,i){for(var r,o,u=0,s=n.length,a=e.length;u<s;){if(i>=a)return -1;if(37===(r=n.charCodeAt(u++))){if(!(o=T[(r=n.charAt(u++))in tD?n.charAt(u++):r])||(i=o(t,e,i))<0)return -1}else if(r!=e.charCodeAt(i++))return -1}return i}return v.x=w(e,v),v.X=w(i,v),v.c=w(n,v),m.x=w(e,m),m.X=w(i,m),m.c=w(n,m),{format:function(t){var n=w(t+="",v);return n.toString=function(){return t},n},parse:function(t){var n=M(t+="",!1);return n.toString=function(){return t},n},utcFormat:function(t){var n=w(t+="",m);return n.toString=function(){return t},n},utcParse:function(t){var n=M(t+="",!0);return n.toString=function(){return t},n}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,i.parse,o=i.utcFormat,u=i.utcParse;var nq="%Y-%m-%dT%H:%M:%S.%LZ";function nO(t){return new Date(t)}function nV(t){return t instanceof Date?+t:+new Date(+t)}function nI(t,n,e,i,r,o,u,s,a,h){var c=M(),l=c.invert,f=c.domain,_=h(".%L"),p=h(":%S"),y=h("%I:%M"),g=h("%I %p"),x=h("%a %d"),d=h("%b %d"),v=h("%B"),m=h("%Y");function w(t){return(a(t)<t?_:s(t)<t?p:u(t)<t?y:o(t)<t?g:i(t)<t?r(t)<t?x:d:e(t)<t?v:m)(t)}return c.invert=function(t){return new Date(l(t))},c.domain=function(t){return arguments.length?f(Array.from(t,nV)):f().map(nO)},c.ticks=function(n){var e=f();return t(e[0],e[e.length-1],null==n?10:n)},c.tickFormat=function(t,n){return null==n?w:h(n)},c.nice=function(t){var e=f();return t&&"function"==typeof t.range||(t=n(e[0],e[e.length-1],null==t?10:t)),t?f(S(e,t)):c},c.copy=function(){return T(c,nI(t,n,e,i,r,o,u,s,a,h))},c}function nZ(){return a.apply(nI(tN,tS,tT,tv,tr,tt,$,J,X,r).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function nj(){return a.apply(nI(tb,tC,tw,tm,tf,tn,K,Q,X,o).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function nB(){var t,n,e,i,r,o=0,u=1,s=x,a=!1;function h(n){return null==n||isNaN(n*=1)?r:s(0===e?.5:(n=(i(n)-t)*e,a?Math.max(0,Math.min(1,n)):n))}function c(t){return function(n){var e,i;return arguments.length?([e,i]=n,s=t(e,i),h):[s(0),s(1)]}}return h.domain=function(r){return arguments.length?([o,u]=r,t=i(o*=1),n=i(u*=1),e=t===n?0:1/(n-t),h):[o,u]},h.clamp=function(t){return arguments.length?(a=!!t,h):a},h.interpolator=function(t){return arguments.length?(s=t,h):s},h.range=c(p.GW),h.rangeRound=c(p.sH),h.unknown=function(t){return arguments.length?(r=t,h):r},function(r){return i=r,t=r(o),n=r(u),e=t===n?0:1/(n-t),h}}function nW(t,n){return n.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function nG(){var t=I(nB());return t.copy=function(){return nW(t,nG()).exponent(t.exponent())},h.apply(t,arguments)}function nR(){return nG.apply(null,arguments).exponent(.5)}function nz(){var t,n,e,i,r,o,u,s=0,a=.5,h=1,c=1,l=x,f=!1;function _(t){return isNaN(t*=1)?u:(t=.5+((t=+o(t))-n)*(c*t<c*n?i:r),l(f?Math.max(0,Math.min(1,t)):t))}function y(t){return function(n){var e,i,r;return arguments.length?([e,i,r]=n,l=(0,p.$B)(t,[e,i,r]),_):[l(0),l(.5),l(1)]}}return _.domain=function(u){return arguments.length?([s,a,h]=u,t=o(s*=1),n=o(a*=1),e=o(h*=1),i=t===n?0:.5/(n-t),r=n===e?0:.5/(e-n),c=n<t?-1:1,_):[s,a,h]},_.clamp=function(t){return arguments.length?(f=!!t,_):f},_.interpolator=function(t){return arguments.length?(l=t,_):l},_.range=y(p.GW),_.rangeRound=y(p.sH),_.unknown=function(t){return arguments.length?(u=t,_):u},function(u){return o=u,t=u(s),n=u(a),e=u(h),i=t===n?0:.5/(n-t),r=n===e?0:.5/(e-n),c=n<t?-1:1,_}}function nX(){var t=I(nz());return t.copy=function(){return nW(t,nX()).exponent(t.exponent())},h.apply(t,arguments)}function nJ(){return nX.apply(null,arguments).exponent(.5)}Date.prototype.toISOString||o(nq),+new Date("2000-01-01T00:00:00.000Z")||u(nq)},68108:(t,n,e)=>{e.d(n,{tB:()=>_,wR:()=>m,O1:()=>g});var i,r,o=0,u=0,s=0,a=0,h=0,c=0,l="object"==typeof performance&&performance.now?performance:Date,f="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function _(){return h||(f(p),h=l.now()+c)}function p(){h=0}function y(){this._call=this._time=this._next=null}function g(t,n,e){var i=new y;return i.restart(t,n,e),i}function x(){h=(a=l.now())+c,o=u=0;try{_(),++o;for(var t,n=i;n;)(t=h-n._time)>=0&&n._call.call(void 0,t),n=n._next;--o}finally{o=0,function(){for(var t,n,e=i,o=1/0;e;)e._call?(o>e._time&&(o=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:i=n);r=t,v(o)}(),h=0}}function d(){var t=l.now(),n=t-a;n>1e3&&(c-=n,a=t)}function v(t){!o&&(u&&(u=clearTimeout(u)),t-h>24?(t<1/0&&(u=setTimeout(x,t-l.now()-c)),s&&(s=clearInterval(s))):(s||(a=l.now(),s=setInterval(d,1e3)),o=1,f(x)))}function m(t,n,e){var i=new y;return n=null==n?0:+n,i.restart(e=>{i.stop(),t(e+n)},n,e),i}y.prototype=g.prototype={constructor:y,restart:function(t,n,e){if("function"!=typeof t)throw TypeError("callback is not a function");e=(null==e?_():+e)+(null==n?0:+n),this._next||r===this||(r?r._next=this:i=this,r=this),this._call=t,this._time=e,v()},stop:function(){this._call&&(this._call=null,this._time=1/0,v())}}},96472:(t,n,e)=>{e.d(n,{jN:()=>l,MF:()=>o,Wn:()=>z,Lt:()=>W,r1:()=>B,gD:()=>a,XM:()=>c,iF:()=>b});var i="http://www.w3.org/1999/xhtml";let r={svg:"http://www.w3.org/2000/svg",xhtml:i,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function o(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),r.hasOwnProperty(n)?{space:r[n],local:t}:t}function u(t){var n=o(t);return(n.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===i&&n.documentElement.namespaceURI===i?n.createElement(t):n.createElementNS(e,t)}})(n)}function s(){}function a(t){return null==t?s:function(){return this.querySelector(t)}}function h(){return[]}function c(t){return null==t?h:function(){return this.querySelectorAll(t)}}function l(t){return function(){return this.matches(t)}}function f(t){return function(n){return n.matches(t)}}var _=Array.prototype.find;function p(){return this.firstElementChild}var y=Array.prototype.filter;function g(){return Array.from(this.children)}function x(t){return Array(t.length)}function d(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function v(t,n,e,i,r,o){for(var u,s=0,a=n.length,h=o.length;s<h;++s)(u=n[s])?(u.__data__=o[s],i[s]=u):e[s]=new d(t,o[s]);for(;s<a;++s)(u=n[s])&&(r[s]=u)}function m(t,n,e,i,r,o,u){var s,a,h,c=new Map,l=n.length,f=o.length,_=Array(l);for(s=0;s<l;++s)(a=n[s])&&(_[s]=h=u.call(a,a.__data__,s,n)+"",c.has(h)?r[s]=a:c.set(h,a));for(s=0;s<f;++s)h=u.call(t,o[s],s,o)+"",(a=c.get(h))?(i[s]=a,a.__data__=o[s],c.delete(h)):e[s]=new d(t,o[s]);for(s=0;s<l;++s)(a=n[s])&&c.get(_[s])===a&&(r[s]=a)}function T(t){return t.__data__}function w(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function M(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function b(t,n){return t.style.getPropertyValue(n)||M(t).getComputedStyle(t,null).getPropertyValue(n)}function C(t){return t.trim().split(/^|\s+/)}function N(t){return t.classList||new S(t)}function S(t){this._node=t,this._names=C(t.getAttribute("class")||"")}function k(t,n){for(var e=N(t),i=-1,r=n.length;++i<r;)e.add(n[i])}function A(t,n){for(var e=N(t),i=-1,r=n.length;++i<r;)e.remove(n[i])}function U(){this.textContent=""}function D(){this.innerHTML=""}function E(){this.nextSibling&&this.parentNode.appendChild(this)}function F(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Y(){return null}function P(){var t=this.parentNode;t&&t.removeChild(this)}function H(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function L(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function q(t){return function(){var n=this.__on;if(n){for(var e,i=0,r=-1,o=n.length;i<o;++i)(e=n[i],t.type&&e.type!==t.type||e.name!==t.name)?n[++r]=e:this.removeEventListener(e.type,e.listener,e.options);++r?n.length=r:delete this.__on}}}function O(t,n,e){return function(){var i,r=this.__on,o=function(t){n.call(this,t,this.__data__)};if(r){for(var u=0,s=r.length;u<s;++u)if((i=r[u]).type===t.type&&i.name===t.name){this.removeEventListener(i.type,i.listener,i.options),this.addEventListener(i.type,i.listener=o,i.options=e),i.value=n;return}}this.addEventListener(t.type,o,e),i={type:t.type,name:t.name,value:n,listener:o,options:e},r?r.push(i):this.__on=[i]}}function V(t,n,e){var i=M(t),r=i.CustomEvent;"function"==typeof r?r=new r(n,e):(r=i.document.createEvent("Event"),e?(r.initEvent(n,e.bubbles,e.cancelable),r.detail=e.detail):r.initEvent(n,!1,!1)),t.dispatchEvent(r)}d.prototype={constructor:d,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}},S.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var I=[null];function Z(t,n){this._groups=t,this._parents=n}function j(){return new Z([[document.documentElement]],I)}Z.prototype=j.prototype={constructor:Z,select:function(t){"function"!=typeof t&&(t=a(t));for(var n=this._groups,e=n.length,i=Array(e),r=0;r<e;++r)for(var o,u,s=n[r],h=s.length,c=i[r]=Array(h),l=0;l<h;++l)(o=s[l])&&(u=t.call(o,o.__data__,l,s))&&("__data__"in o&&(u.__data__=o.__data__),c[l]=u);return new Z(i,this._parents)},selectAll:function(t){if("function"==typeof t){var n;n=t,t=function(){var t;return t=n.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=c(t);for(var e=this._groups,i=e.length,r=[],o=[],u=0;u<i;++u)for(var s,a=e[u],h=a.length,l=0;l<h;++l)(s=a[l])&&(r.push(t.call(s,s.__data__,l,a)),o.push(s));return new Z(r,o)},selectChild:function(t){var n;return this.select(null==t?p:(n="function"==typeof t?t:f(t),function(){return _.call(this.children,n)}))},selectChildren:function(t){var n;return this.selectAll(null==t?g:(n="function"==typeof t?t:f(t),function(){return y.call(this.children,n)}))},filter:function(t){"function"!=typeof t&&(t=l(t));for(var n=this._groups,e=n.length,i=Array(e),r=0;r<e;++r)for(var o,u=n[r],s=u.length,a=i[r]=[],h=0;h<s;++h)(o=u[h])&&t.call(o,o.__data__,h,u)&&a.push(o);return new Z(i,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,T);var e=n?m:v,i=this._parents,r=this._groups;"function"!=typeof t&&(d=t,t=function(){return d});for(var o=r.length,u=Array(o),s=Array(o),a=Array(o),h=0;h<o;++h){var c=i[h],l=r[h],f=l.length,_="object"==typeof(x=t.call(c,c&&c.__data__,h,i))&&"length"in x?x:Array.from(x),p=_.length,y=s[h]=Array(p),g=u[h]=Array(p);e(c,l,y,g,a[h]=Array(f),_,n);for(var x,d,w,M,b=0,C=0;b<p;++b)if(w=y[b]){for(b>=C&&(C=b+1);!(M=g[C])&&++C<p;);w._next=M||null}}return(u=new Z(u,i))._enter=s,u._exit=a,u},enter:function(){return new Z(this._enter||this._groups.map(x),this._parents)},exit:function(){return new Z(this._exit||this._groups.map(x),this._parents)},join:function(t,n,e){var i=this.enter(),r=this,o=this.exit();return"function"==typeof t?(i=t(i))&&(i=i.selection()):i=i.append(t+""),null!=n&&(r=n(r))&&(r=r.selection()),null==e?o.remove():e(o),i&&r?i.merge(r).order():r},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,i=n._groups,r=e.length,o=i.length,u=Math.min(r,o),s=Array(r),a=0;a<u;++a)for(var h,c=e[a],l=i[a],f=c.length,_=s[a]=Array(f),p=0;p<f;++p)(h=c[p]||l[p])&&(_[p]=h);for(;a<r;++a)s[a]=e[a];return new Z(s,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var i,r=t[n],o=r.length-1,u=r[o];--o>=0;)(i=r[o])&&(u&&4^i.compareDocumentPosition(u)&&u.parentNode.insertBefore(i,u),u=i);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=w);for(var e=this._groups,i=e.length,r=Array(i),o=0;o<i;++o){for(var u,s=e[o],a=s.length,h=r[o]=Array(a),c=0;c<a;++c)(u=s[c])&&(h[c]=u);h.sort(n)}return new Z(r,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var i=t[n],r=0,o=i.length;r<o;++r){var u=i[r];if(u)return u}return null},size:function(){let t=0;for(let n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,i=n.length;e<i;++e)for(var r,o=n[e],u=0,s=o.length;u<s;++u)(r=o[u])&&t.call(r,r.__data__,u,o);return this},attr:function(t,n){var e=o(t);if(arguments.length<2){var i=this.node();return e.local?i.getAttributeNS(e.space,e.local):i.getAttribute(e)}return this.each((null==n?e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?e.local?function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}:function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}:e.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(e,n))},style:function(t,n,e){return arguments.length>1?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(t,n,e){return function(){var i=n.apply(this,arguments);null==i?this.style.removeProperty(t):this.style.setProperty(t,i,e)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):b(this.node(),t)},property:function(t,n){return arguments.length>1?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=C(t+"");if(arguments.length<2){for(var i=N(this.node()),r=-1,o=e.length;++r<o;)if(!i.contains(e[r]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?k:A)(this,t)}}:n?function(t){return function(){k(this,t)}}:function(t){return function(){A(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?U:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?D:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(E)},lower:function(){return this.each(F)},append:function(t){var n="function"==typeof t?t:u(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:u(t),i=null==n?Y:"function"==typeof n?n:a(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),i.apply(this,arguments)||null)})},remove:function(){return this.each(P)},clone:function(t){return this.select(t?L:H)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var i,r,o=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}),u=o.length;if(arguments.length<2){var s=this.node().__on;if(s){for(var a,h=0,c=s.length;h<c;++h)for(i=0,a=s[h];i<u;++i)if((r=o[i]).type===a.type&&r.name===a.name)return a.value}return}for(i=0,s=n?O:q;i<u;++i)this.each(s(o[i],n,e));return this},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return V(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return V(this,t,n)}})(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var i,r=t[n],o=0,u=r.length;o<u;++o)(i=r[o])&&(yield i)}};let B=j;function W(t){return"string"==typeof t?new Z([[document.querySelector(t)]],[document.documentElement]):new Z([[t]],I)}var G=0;function R(){this._="@"+(++G).toString(36)}function z(t,n){if(t=function(t){let n;for(;n=t.sourceEvent;)t=n;return t}(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var i=e.createSVGPoint();return i.x=t.clientX,i.y=t.clientY,[(i=i.matrixTransform(n.getScreenCTM().inverse())).x,i.y]}if(n.getBoundingClientRect){var r=n.getBoundingClientRect();return[t.clientX-r.left-n.clientLeft,t.clientY-r.top-n.clientTop]}}return[t.pageX,t.pageY]}R.prototype=(function(){return new R}).prototype={constructor:R,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}}}}]);