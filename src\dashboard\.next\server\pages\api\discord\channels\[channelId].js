"use strict";(()=>{var e={};e.id=9685,e.ids=[9685],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},35645:(e,t,r)=>{r.r(t),r.d(t,{config:()=>h,default:()=>c,routeModule:()=>p});var s={};r.r(s),r.d(s,{default:()=>l});var a=r(93433),n=r(20264),o=r(20584),i=r(15806),u=r(94506),d=r(98580);async function l(e,t){try{let r=await (0,i.getServerSession)(e,t,u.authOptions);if(!r?.user)return t.status(401).json({error:"Unauthorized"});if(!r.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});let{guildId:s,token:a}=d.dashboardConfig.bot;if(!a||!s)return t.status(500).json({error:"Bot configuration missing"});let{channelId:n}=e.query;if(!n||"string"!=typeof n)return t.status(400).json({error:"Channel ID is required"});if("PATCH"===e.method)try{let r=await fetch(`https://discord.com/api/v10/channels/${n}`,{method:"PATCH",headers:{Authorization:`Bot ${a}`,"Content-Type":"application/json"},body:JSON.stringify(e.body)});if(!r.ok){let e;try{e=await r.json()}catch{e=await r.text()}return t.status(r.status).json(e)}let s=await r.json();return t.status(200).json(s)}catch(e){return t.status(500).json({error:"Failed to update channel"})}if("DELETE"===e.method)try{let e=await fetch(`https://discord.com/api/v10/channels/${n}`,{method:"DELETE",headers:{Authorization:`Bot ${a}`}});if(!e.ok){let r;try{r=await e.json()}catch{r=await e.text()}return t.status(e.status).json(r)}return t.status(200).json({message:"Channel deleted successfully"})}catch(e){return t.status(500).json({error:"Failed to delete channel"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let c=(0,o.M)(s,"default"),h=(0,o.M)(s,"config"),p=new a.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/discord/channels/[channelId]",pathname:"/api/discord/channels/[channelId]",bundlePath:"",filename:""},userland:s})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(35645));module.exports=s})();