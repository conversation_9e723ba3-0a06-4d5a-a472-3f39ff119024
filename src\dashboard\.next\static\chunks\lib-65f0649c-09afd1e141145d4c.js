"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9450],{3065:(e,t,r)=>{r.d(t,{$7:()=>s,Ru:()=>d,uZ:()=>u});var a=r(63047),i=r(93042),n=r(18080),o=r(62560),l=r(56199),c=r(36025),u=(0,a.VP)("keyDown"),d=(0,a.VP)("focus"),s=(0,a.Nc)();s.startListening({actionCreator:u,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:a}=r.tooltip,u=e.payload;if("ArrowRight"===u||"ArrowLeft"===u||"Enter"===u){var d=Number((0,c.P)(a,(0,n.n4)(r))),s=(0,n.R4)(r);if("Enter"===u){var v=(0,o.pg)(r,"axis","hover",String(a.index));t.dispatch((0,i.o4)({active:!a.active,activeIndex:a.index,activeDataKey:a.dataKey,activeCoordinate:v}));return}var p=d+("ArrowRight"===u?1:-1)*("left-to-right"===(0,l._y)(r)?1:-1);if(null!=s&&!(p>=s.length)&&!(p<0)){var f=(0,o.pg)(r,"axis","hover",String(p));t.dispatch((0,i.o4)({active:!0,activeIndex:p.toString(),activeDataKey:void 0,activeCoordinate:f}))}}}}}),s.startListening({actionCreator:d,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:a}=r.tooltip;if(!a.active&&null==a.index){var n=(0,o.pg)(r,"axis","hover",String("0"));t.dispatch((0,i.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:n}))}}}})},4638:(e,t,r)=>{r.d(t,{x:()=>a});var a=e=>e.options.tooltipPayloadSearcher},6714:(e,t,r)=>{r.d(t,{J:()=>a});var a=e=>e.tooltip},7972:(e,t,r)=>{r.d(t,{o:()=>a});var a=(e,t,r,a,i,n,o,l)=>{if(null!=n&&null!=l){var c=o[0],u=null==c?void 0:l(c.positions,n);if(null!=u)return u;var d=null==i?void 0:i[Number(n)];if(d)if("horizontal"===r)return{x:d.coordinate,y:(a.top+t)/2};else return{x:(a.left+e)/2,y:d.coordinate}}}},13831:(e,t,r)=>{r.d(t,{$g:()=>o,Hw:()=>n,Td:()=>c,au:()=>l,xH:()=>i});var a=r(92735),i=e=>e.options.defaultTooltipEventType,n=e=>e.options.validateTooltipEventTypes;function o(e,t,r){if(null==e)return t;var a=e?"axis":"item";return null==r?t:r.includes(a)?a:t}function l(e,t){return o(t,i(e),n(e))}function c(e){return(0,a.G)(t=>l(t,e))}},17649:(e,t,r)=>{r.d(t,{dl:()=>c,lJ:()=>l,uN:()=>n});var a=r(63047),i=r(93833);function n(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.M8)(r))return null==e?void 0:e[r]}}var o=(0,a.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=o.reducer,{createEventEmitter:c}=o.actions},18080:(e,t,r)=>{r.d(t,{BZ:()=>en,eE:()=>eu,Xb:()=>eo,A2:()=>ei,yn:()=>ed,Dn:()=>M,gL:()=>U,fl:()=>Y,R4:()=>q,Re:()=>w,n4:()=>D});var a=r(73474),i=r(56199),n=r(14710),o=r(33053),l=r(78760),c=r(79294),u=r(93833),d=r(56030),s=r(13831),v=r(59146),p=r(85812),f=r(36025),y=r(7972),h=r(68101),m=r(22146),g=r(48674),b=r(4638),x=r(6714),z=r(38597),w=e=>{var t=(0,n.fz)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},O=e=>e.tooltip.settings.axisId,M=e=>{var t=w(e),r=O(e);return(0,i.Hd)(e,t,r)},I=(0,a.Mz)([M,n.fz,i.um,c.iO,w],i.sr),A=(0,a.Mz)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),P=(0,a.Mz)([w,O],i.eo),j=(0,a.Mz)([A,M,P],i.ec),k=(0,a.Mz)([j],i.rj),D=(0,a.Mz)([k,l.LF],i.Nk),S=(0,a.Mz)([D,M,j],i.fb),E=(0,a.Mz)([M],i.S5),K=(0,a.Mz)([D,j,c.eC],i.MK),C=(0,a.Mz)([K,l.LF,w],i.pM),N=(0,a.Mz)([j],i.IO),L=(0,a.Mz)([D,M,N,w],i.kz),H=(0,a.Mz)([i.Kr,w,O],i.P9),T=(0,a.Mz)([H,w],i.Oz),G=(0,a.Mz)([i.gT,w,O],i.P9),Z=(0,a.Mz)([G,w],i.q),F=(0,a.Mz)([i.$X,w,O],i.P9),B=(0,a.Mz)([F,w],i.bb),R=(0,a.Mz)([T,B,Z],i.yi),_=(0,a.Mz)([M,E,C,L,R],i.wL),J=(0,a.Mz)([M,n.fz,D,S,c.eC,w,_],i.tP),X=(0,a.Mz)([J,M,I],i.xp),$=(0,a.Mz)([M,J,X,w],i.g1),V=e=>{var t=w(e),r=O(e);return(0,i.D5)(e,t,r,!1)},U=(0,a.Mz)([M,V],d.I),Y=(0,a.Mz)([M,I,$,U],i.Qn),W=(0,a.Mz)([n.fz,S,M,w],i.tF),Q=(0,a.Mz)([n.fz,S,M,w],i.iv),q=(0,a.Mz)([n.fz,M,I,Y,V,W,Q,w],(e,t,r,a,i,n,l,c)=>{if(t){var{type:d}=t,s=(0,o._L)(e,c);if(a){var v="scaleBand"===r&&a.bandwidth?a.bandwidth()/2:2,p="category"===d&&a.bandwidth?a.bandwidth()/v:0;return(p="angleAxis"===c&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,u.sA)(i[0]-i[1])*p:p,s&&l)?l.map((e,t)=>({coordinate:a(e)+p,value:e,index:t,offset:p})):a.domain().map((e,t)=>({coordinate:a(e)+p,value:n?n[e]:e,index:t,offset:p}))}}}),ee=(0,a.Mz)([s.xH,s.Hw,e=>e.tooltip.settings],(e,t,r)=>(0,s.$g)(r.shared,e,t)),et=e=>e.tooltip.settings.trigger,er=e=>e.tooltip.settings.defaultIndex,ea=(0,a.Mz)([x.J,ee,et,er],p.i),ei=(0,a.Mz)([ea,D],f.P),en=(0,a.Mz)([q,ei],v.E),eo=(0,a.Mz)([ea],e=>{if(e)return e.dataKey}),el=(0,a.Mz)([x.J,ee,et,er],g.q),ec=(0,a.Mz)([h.Lp,h.A$,n.fz,m.HZ,q,er,el,b.x],y.o),eu=(0,a.Mz)([ea,ec],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),ed=(0,a.Mz)([ea],e=>e.active),es=(0,a.Mz)([el,ei,l.LF,M,en,b.x,ee],z.N);(0,a.Mz)([es],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))})},22146:(e,t,r)=>{r.d(t,{c2:()=>m,HZ:()=>y,Ds:()=>h});var a=r(73474),i=r(56797),n=r.n(i),o=r(10014),l=r.n(o),c=e=>e.legend.settings;(0,a.Mz)([e=>e.legend.payload,c],(e,t)=>{var{itemSorter:r}=t,a=e.flat(1);return r?l()(a,r):a});var u=r(33053),d=r(68101),s=r(84288),v=r(37661);function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var a,i,n;a=e,i=t,n=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=(0,a.Mz)([d.Lp,d.A$,d.HK,e=>e.brush.height,s.h,s.W,c,e=>e.legend.size],(e,t,r,a,i,o,l,c)=>{var d=o.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var a="number"==typeof t.width?t.width:v.tQ;return f(f({},e),{},{[r]:e[r]+a})}return e},{left:r.left||0,right:r.right||0}),s=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:f(f({},e),{},{[r]:n()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),p=f(f({},s),d),y=p.bottom;p.bottom+=a;var h=e-(p=(0,u.s0)(p,l,c)).left-p.right,m=t-p.top-p.bottom;return f(f({brushBottom:y},p),{},{width:Math.max(h,0),height:Math.max(m,0)})}),h=(0,a.Mz)(y,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),m=(0,a.Mz)(d.Lp,d.A$,(e,t)=>({x:0,y:0,width:e,height:t}))},23168:(e,t,r)=>{r.d(t,{C:()=>l,U:()=>c});var a=r(73474),i=r(22146),n=r(68101),o=r(93833),l=e=>e.brush,c=(0,a.Mz)([l,i.HZ,n.HK],(e,t,r)=>({height:e.height,x:(0,o.Et)(e.x)?e.x:t.left,y:(0,o.Et)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.Et)(e.width)?e.width:t.width}))},27494:(e,t,r)=>{r.d(t,{E:()=>N});var a=r(63047),i=r(17649),n=r(93042),o=r(59707),l=r(34975),c=r(41381);function u(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var d=r(10674),s=r(26596),v=(0,a.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,a.ss)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,a.ss)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,a.ss)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:p,removeDot:f,addArea:y,removeArea:h,addLine:m,removeLine:g}=v.actions,b=v.reducer,x=r(54791),z=r(35746),w=r(36885),O=r(74201),M=(0,a.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,O.h4)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,O.h4)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:I,removeRadiusAxis:A,addAngleAxis:P,removeAngleAxis:j}=M.actions,k=M.reducer,D=r(69015),S=r(3065),E=r(55419),K=r(88346),C=(0,a.HY)({brush:x.rT,cartesianAxis:d.CA,chartData:o.LV,graphicalItems:s.iZ,layout:l.Vp,legend:z.CU,options:i.lJ,polarAxis:k,polarOptions:D.J,referenceElements:b,rootProps:w.vE,tooltip:n.En}),N=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,a.U1)({reducer:C,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([c.YF.middleware,c.fP.middleware,S.$7.middleware,E.x.middleware,K.k.middleware]),devTools:{serialize:{replacer:u},name:"recharts-".concat(t)}})}},34975:(e,t,r)=>{r.d(t,{B_:()=>i,JK:()=>n,Vp:()=>c,gX:()=>o,hF:()=>l});var a=(0,r(63047).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:i,setLayout:n,setChartSize:o,setScale:l}=a.actions,c=a.reducer},35746:(e,t,r)=>{r.d(t,{CU:()=>d,Lx:()=>c,u3:()=>u});var a=r(63047),i=r(74201),n=(0,a.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,i.h4)(t.payload))},removeLegendPayload(e,t){var r=(0,a.ss)(e).payload.indexOf((0,i.h4)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:l,addLegendPayload:c,removeLegendPayload:u}=n.actions,d=n.reducer},36025:(e,t,r)=>{r.d(t,{P:()=>i});var a=r(6944),i=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,a.H)(i))return r;var n=Infinity;return t.length>0&&(n=t.length-1),String(Math.max(0,Math.min(i,n)))}},36885:(e,t,r)=>{r.d(t,{mZ:()=>l,vE:()=>o});var a=r(63047),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},n=(0,a.Z0)({name:"rootProps",initialState:i,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:i.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),o=n.reducer,{updateOptions:l}=n.actions},38597:(e,t,r)=>{r.d(t,{N:()=>l});var a=r(93833),i=r(33053);function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var a,i,n;a=e,i=t,n=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(e,t,r,n,l,c,u)=>{if(null!=t&&null!=c){var{chartData:d,computedData:s,dataStartIndex:v,dataEndIndex:p}=r;return e.reduce((e,r)=>{var f,y,h,m,g,{dataDefinedOnItem:b,settings:x}=r,z=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((f=b,y=d,null!=f?f:y),v,p),w=null!=(h=null==x?void 0:x.dataKey)?h:null==n?void 0:n.dataKey,O=null==x?void 0:x.nameKey;return Array.isArray(m=null!=n&&n.dataKey&&Array.isArray(z)&&!Array.isArray(z[0])&&"axis"===u?(0,a.eP)(z,n.dataKey,l):c(z,t,s,O))?m.forEach(t=>{var r=o(o({},x),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,i.GF)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,i.kr)(t.payload,t.dataKey),name:t.name}))}):e.push((0,i.GF)({tooltipEntrySettings:x,dataKey:w,payload:m,value:(0,i.kr)(m,w),name:null!=(g=(0,i.kr)(m,O))?g:null==x?void 0:x.name})),e},[])}}},41381:(e,t,r)=>{r.d(t,{YF:()=>u,dj:()=>d,fP:()=>s,ky:()=>c});var a=r(63047),i=r(93042),n=r(82527),o=r(13831),l=r(32037),c=(0,a.VP)("mouseClick"),u=(0,a.Nc)();u.startListening({actionCreator:c,effect:(e,t)=>{var r=e.payload,a=(0,n.g)(t.getState(),(0,l.w)(r));(null==a?void 0:a.activeIndex)!=null&&t.dispatch((0,i.jF)({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}});var d=(0,a.VP)("mouseMove"),s=(0,a.Nc)();s.startListening({actionCreator:d,effect:(e,t)=>{var r=e.payload,a=t.getState(),c=(0,o.au)(a,a.tooltip.settings.shared),u=(0,n.g)(a,(0,l.w)(r));"axis"===c&&((null==u?void 0:u.activeIndex)!=null?t.dispatch((0,i.Nt)({activeIndex:u.activeIndex,activeDataKey:void 0,activeCoordinate:u.activeCoordinate})):t.dispatch((0,i.xS)()))}})},46216:(e,t,r)=>{r.d(t,{d:()=>o});var a=r(73474),i=r(66145),n=r(68101),o=(0,a.Mz)([i.G,n.Lp,n.A$],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}})},48278:(e,t,r)=>{r.d(t,{l:()=>p,z:()=>v});var a=r(94285),i=r(58255),n=r(67204),o=r(93833),l=["x","y"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(null,arguments)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var a,i,n;a=e,i=t,n=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s(e,t){var{x:r,y:a}=e,i=function(e,t){if(null==e)return{};var r,a,i=function(e,t){if(null==e)return{};var r={};for(var a in e)if(({}).hasOwnProperty.call(e,a)){if(-1!==t.indexOf(a))continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)r=n[a],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,l),n=parseInt("".concat(r),10),o=parseInt("".concat(a),10),c=parseInt("".concat(t.height||i.height),10),u=parseInt("".concat(t.width||i.width),10);return d(d(d(d(d({},t),i),n?{x:n}:{}),o?{y:o}:{}),{},{height:c,width:u,name:t.name,radius:t.radius})}function v(e){return a.createElement(n.y,c({shapeType:"rectangle",propTransformer:s,activeClassName:"recharts-active-bar"},e))}var p=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,a)=>{if((0,o.Et)(e))return e;var n=(0,o.Et)(r)||(0,o.uy)(r);return n?e(r,a):(n||(0,i.A)(!1),t)}}},48674:(e,t,r)=>{r.d(t,{q:()=>a});var a=(e,t,r,a)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=a?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})}},49507:(e,t,r)=>{r.d(t,{Be:()=>h,Cv:()=>w,D0:()=>M,Dc:()=>O,Gl:()=>m});var a=r(73474),i=r(68101),n=r(22146),o=r(41529),l=r(93833),c=r(95193),u=r(45380),d=r(56030),s=r(14710),v={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:c.c.angleAxisId,includeHidden:!1,name:void 0,reversed:c.c.reversed,scale:c.c.scale,tick:c.c.tick,tickCount:void 0,ticks:void 0,type:c.c.type,unit:void 0},p={allowDataOverflow:u.j.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.j.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.j.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.j.scale,tick:u.j.tick,tickCount:u.j.tickCount,ticks:void 0,type:u.j.type,unit:void 0},f={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:c.c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.c.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.c.scale,tick:c.c.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:u.j.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.j.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.j.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.j.scale,tick:u.j.tick,tickCount:u.j.tickCount,ticks:void 0,type:"category",unit:void 0},h=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?f:v,m=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?y:p,g=e=>e.polarOptions,b=(0,a.Mz)([i.Lp,i.A$,n.HZ],o.lY),x=(0,a.Mz)([g,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.innerRadius,t,0)}),z=(0,a.Mz)([g,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.outerRadius,t,.8*t)}),w=(0,a.Mz)([g],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,a.Mz)([h,w],d.I);var O=(0,a.Mz)([b,x,z],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,a.Mz)([m,O],d.I);var M=(0,a.Mz)([s.fz,g,x,z,i.Lp,i.A$],(e,t,r,a,i,n)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=a){var{cx:o,cy:c,startAngle:u,endAngle:d}=t;return{cx:(0,l.F4)(o,i,i/2),cy:(0,l.F4)(c,n,n/2),innerRadius:r,outerRadius:a,startAngle:u,endAngle:d,clockWise:!1}}})},49771:(e,t,r)=>{r.d(t,{Ez:()=>D,EX:()=>E});var a=r(73474),i=r(23740),n=r(78760),o=r(22146),l=r(33053),c=r(56199),u=r(14710),d=r(70262),s=r(87379),v=r(79294),p=e=>e.graphicalItems.polarItems,f=(0,a.Mz)([d.N,s.E],c.eo),y=(0,a.Mz)([p,c.DP,f],c.ec),h=(0,a.Mz)([y],c.rj),m=(0,a.Mz)([h,n.z3],c.Nk),g=(0,a.Mz)([m,c.DP,y],c.fb),b=(0,a.Mz)([m,c.DP,y],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var a;return{value:(0,l.kr)(e,null!=(a=t.dataKey)?a:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,l.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),x=()=>void 0,z=(0,a.Mz)([c.DP,c.AV,x,b,x],c.wL),w=(0,a.Mz)([c.DP,u.fz,m,g,v.eC,d.N,z],c.tP),O=(0,a.Mz)([w,c.DP,c.xM],c.xp);function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){var a,i,n;a=e,i=t,n=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,a.Mz)([c.DP,w,O,d.N],c.g1);var A=(e,t)=>t,P=[],j=(e,t,r)=>(null==r?void 0:r.length)===0?P:r,k=(0,a.Mz)([n.z3,A,j],(e,t,r)=>{var a,{chartData:i}=e;if((a=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&a.length||null==r||(a=r.map(e=>I(I({},t.presentationProps),e.props))),null!=a)return a}),D=(0,a.Mz)([k,A,j],(e,t,r)=>{if(null!=e)return e.map((e,a)=>{var i,n,o=(0,l.kr)(e,t.nameKey,t.name);return n=null!=r&&null!=(i=r[a])&&null!=(i=i.props)&&i.fill?r[a].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,l.uM)(o,t.dataKey),color:n,payload:e,type:t.legendType}})}),S=(0,a.Mz)([p,A],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),E=(0,a.Mz)([k,S,j,o.HZ],(e,t,r,a)=>{if(null!=t&&null!=e)return(0,i.L)({offset:a,pieSettings:t,displayedData:e,cells:r})})},56030:(e,t,r)=>{r.d(t,{I:()=>a});var a=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t}},56199:(e,t,r)=>{r.d(t,{$X:()=>ep,AV:()=>ew,BQ:()=>e2,CR:()=>tr,D5:()=>eF,DP:()=>K,Gx:()=>ta,Hd:()=>C,IO:()=>F,KR:()=>e3,Kr:()=>ec,L$:()=>e4,Lw:()=>eW,MK:()=>W,Nk:()=>J,Oz:()=>ey,P9:()=>eu,PU:()=>P,Qn:()=>eD,Rl:()=>j,S5:()=>eo,TC:()=>Q,ZB:()=>te,Zi:()=>tt,_y:()=>tn,bb:()=>eb,cd:()=>k,ec:()=>G,eo:()=>L,fb:()=>$,g1:()=>eK,gT:()=>es,iV:()=>eR,iv:()=>e9,kz:()=>et,ld:()=>H,pM:()=>q,q:()=>em,rj:()=>R,sf:()=>D,sr:()=>ej,tF:()=>e6,tP:()=>eA,um:()=>N,wL:()=>eO,wP:()=>e7,xM:()=>ek,xp:()=>eS,yi:()=>el});var a=r(73474),i=r(45256),n=r.n(i),o=r(95222),l=r(14710),c=r(33053),u=r(78760),d=r(33048),s=r(93833),v=r(6944),p=r(7501),f=r(68101),y=r(84288),h=r(22146),m=r(23168),g=r(79294),b=r(49507),x=r(70262),z=r(87379),w=r(56030),O=r(37661);function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){var a,i,n;a=e,i=t,n=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var A=[0,"auto"],P={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},j=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?P:r},k={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:A,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:O.tQ},D=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?k:r},S={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},E=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?S:r},K=(e,t,r)=>{switch(t){case"xAxis":return j(e,r);case"yAxis":return D(e,r);case"zAxis":return E(e,r);case"angleAxis":return(0,b.Be)(e,r);case"radiusAxis":return(0,b.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},C=(e,t,r)=>{switch(t){case"xAxis":return j(e,r);case"yAxis":return D(e,r);case"angleAxis":return(0,b.Be)(e,r);case"radiusAxis":return(0,b.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},N=e=>e.graphicalItems.countOfBars>0;function L(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var H=e=>e.graphicalItems.cartesianItems,T=(0,a.Mz)([x.N,z.E],L),G=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),Z=(0,a.Mz)([H,K,T],G),F=e=>e.filter(e=>void 0===e.stackId),B=(0,a.Mz)([Z],F),R=e=>e.map(e=>e.data).filter(Boolean).flat(1),_=(0,a.Mz)([Z],R),J=(e,t)=>{var{chartData:r=[],dataStartIndex:a,dataEndIndex:i}=t;return e.length>0?e:r.slice(a,i+1)},X=(0,a.Mz)([_,u.HS],J),$=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,c.kr)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,c.kr)(e,t)}))):e.map(e=>({value:e})),V=(0,a.Mz)([X,K,Z],$);function U(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function Y(e){return e.filter(e=>(0,s.vh)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,s.M8)(e))}var W=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[a,i]=t,n=i.map(e=>e.dataKey);return[a,{stackedData:(0,c.yy)(e,n,r),graphicalItems:i}]})),Q=(0,a.Mz)([X,Z,g.eC],W),q=(e,t,r)=>{var{dataStartIndex:a,dataEndIndex:i}=t;if("zAxis"!==r){var n=(0,c.Mk)(e,a,i);if(null==n||0!==n[0]||0!==n[1])return n}},ee=(0,a.Mz)([Q,u.LF,x.N],q),et=(e,t,r,a)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,n,o=null==(i=r.errorBars)?void 0:i.filter(e=>U(a,e)),l=(0,c.kr)(e,null!=(n=t.dataKey)?n:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||(0,s.M8)(t)||!r.length?[]:Y(r.flatMap(r=>{var a,i,n=(0,c.kr)(e,r.dataKey);if(Array.isArray(n)?[a,i]=n:a=i=n,(0,v.H)(a)&&(0,v.H)(i))return[t-a,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,c.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),er=(0,a.Mz)(X,K,B,x.N,et);function ea(e){var{value:t}=e;if((0,s.vh)(t)||t instanceof Date)return t}var ei=e=>{var t=Y(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},en=(e,t,r)=>{var a=e.map(ea).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,s.CG)(a))?n()(0,e.length):t.allowDuplicatedCategory?a:Array.from(new Set(a))},eo=e=>{var t;if(null==e||!("domain"in e))return A;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=Y(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:A},el=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var a=t.filter(Boolean);if(0!==a.length){var i=a.flat();return[Math.min(...i),Math.max(...i)]}},ec=e=>e.referenceElements.dots,eu=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),ed=(0,a.Mz)([ec,x.N,z.E],eu),es=e=>e.referenceElements.areas,ev=(0,a.Mz)([es,x.N,z.E],eu),ep=e=>e.referenceElements.lines,ef=(0,a.Mz)([ep,x.N,z.E],eu),ey=(e,t)=>{var r=Y(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},eh=(0,a.Mz)(ed,x.N,ey),em=(e,t)=>{var r=Y(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},eg=(0,a.Mz)([ev,x.N],em),eb=(e,t)=>{var r=Y(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ex=(0,a.Mz)(ef,x.N,eb),ez=(0,a.Mz)(eh,ex,eg,(e,t,r)=>el(e,r,t)),ew=(0,a.Mz)([K],eo),eO=(e,t,r,a,i)=>{var n=(0,d.f5)(t,e.allowDataOverflow);return null!=n?n:(0,d.v1)(t,el(r,i,ei(a)),e.allowDataOverflow)},eM=(0,a.Mz)([K,ew,ee,er,ez],eO),eI=[0,1],eA=(e,t,r,a,i,o,l)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:u,type:d}=e,s=(0,c._L)(t,o);return s&&null==u?n()(0,r.length):"category"===d?en(a,e,s):"expand"===i?eI:l}},eP=(0,a.Mz)([K,l.fz,X,V,g.eC,x.N,eM],eA),ej=(e,t,r,a,i)=>{if(null!=e){var{scale:n,type:l}=e;if("auto"===n)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===l&&a&&(a.indexOf("LineChart")>=0||a.indexOf("AreaChart")>=0||a.indexOf("ComposedChart")>=0&&!r)?"point":"category"===l?"band":"linear";if("string"==typeof n){var c="scale".concat((0,s.Zb)(n));return c in o?c:"point"}}},ek=(0,a.Mz)([K,l.fz,N,g.iO,x.N],ej);function eD(e,t,r,a){if(null!=r&&null!=a){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(a);var i=function(e){if(null!=e){if(e in o)return o[e]();var t="scale".concat((0,s.Zb)(e));if(t in o)return o[t]()}}(t);if(null!=i){var n=i.domain(r).range(a);return(0,c.YB)(n),n}}}var eS=(e,t,r)=>{var a=eo(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(a)&&("auto"===a[0]||"auto"===a[1])&&(0,d.JH)(e))return(0,p.d)(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&(0,d.JH)(e))return(0,p.M)(e,t.tickCount,t.allowDecimals)}},eE=(0,a.Mz)([eP,C,ek],eS),eK=(e,t,r,a)=>"angleAxis"!==a&&(null==e?void 0:e.type)==="number"&&(0,d.JH)(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,eC=(0,a.Mz)([K,eP,eE,x.N],eK),eN=(0,a.Mz)(V,K,(e,t)=>{if(t&&"number"===t.type){var r=1/0,a=Array.from(Y(e.map(e=>e.value))).sort((e,t)=>e-t);if(a.length<2)return 1/0;var i=a[a.length-1]-a[0];if(0===i)return 1/0;for(var n=0;n<a.length-1;n++)r=Math.min(r,a[n+1]-a[n]);return r/i}}),eL=(0,a.Mz)(eN,l.fz,g.gY,h.HZ,(e,t,r,a)=>a,(e,t,r,a,i)=>{if(!(0,v.H)(e))return 0;var n="vertical"===t?a.height:a.width;if("gap"===i)return e*n/2;if("no-gap"===i){var o=(0,s.F4)(r,e*n),l=e*n/2;return l-o-(l-o)/n*o}return 0}),eH=(0,a.Mz)(j,(e,t)=>{var r=j(e,t);return null==r||"string"!=typeof r.padding?0:eL(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,a,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(a=i.right)?a:0)+t}}),eT=(0,a.Mz)(D,(e,t)=>{var r=D(e,t);return null==r||"string"!=typeof r.padding?0:eL(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,a,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(a=i.bottom)?a:0)+t}}),eG=(0,a.Mz)([h.HZ,eH,m.U,m.C,(e,t,r)=>r],(e,t,r,a,i)=>{var{padding:n}=a;return i?[n.left,r.width-n.right]:[e.left+t.left,e.left+e.width-t.right]}),eZ=(0,a.Mz)([h.HZ,l.fz,eT,m.U,m.C,(e,t,r)=>r],(e,t,r,a,i,n)=>{var{padding:o}=i;return n?[a.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),eF=(e,t,r,a)=>{var i;switch(t){case"xAxis":return eG(e,r,a);case"yAxis":return eZ(e,r,a);case"zAxis":return null==(i=E(e,r))?void 0:i.range;case"angleAxis":return(0,b.Cv)(e);case"radiusAxis":return(0,b.Dc)(e,r);default:return}},eB=(0,a.Mz)([K,eF],w.I),eR=(0,a.Mz)([K,ek,eC,eB],eD);function e_(e,t){return e.id<t.id?-1:+(e.id>t.id)}(0,a.Mz)(Z,x.N,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>U(t,e)));var eJ=(e,t)=>t,eX=(e,t,r)=>r,e$=(0,a.Mz)(y.h,eJ,eX,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(e_)),eV=(0,a.Mz)(y.W,eJ,eX,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(e_)),eU=(e,t)=>({width:e.width,height:t.height}),eY=(e,t)=>({width:"number"==typeof t.width?t.width:O.tQ,height:e.height}),eW=(0,a.Mz)(h.HZ,j,eU),eQ=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},eq=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},e0=(0,a.Mz)(f.A$,h.HZ,e$,eJ,eX,(e,t,r,a,i)=>{var n,o={};return r.forEach(r=>{var l=eU(t,r);null==n&&(n=eQ(t,a,e));var c="top"===a&&!i||"bottom"===a&&i;o[r.id]=n-Number(c)*l.height,n+=(c?-1:1)*l.height}),o}),e1=(0,a.Mz)(f.Lp,h.HZ,eV,eJ,eX,(e,t,r,a,i)=>{var n,o={};return r.forEach(r=>{var l=eY(t,r);null==n&&(n=eq(t,a,e));var c="left"===a&&!i||"right"===a&&i;o[r.id]=n-Number(c)*l.width,n+=(c?-1:1)*l.width}),o}),e4=(e,t)=>{var r=(0,h.HZ)(e),a=j(e,t);if(null!=a){var i=e0(e,a.orientation,a.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},e3=(e,t)=>{var r=(0,h.HZ)(e),a=D(e,t);if(null!=a){var i=e1(e,a.orientation,a.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},e7=(0,a.Mz)(h.HZ,D,(e,t)=>({width:"number"==typeof t.width?t.width:O.tQ,height:e.height})),e2=(e,t,r)=>{switch(t){case"xAxis":return eW(e,r).width;case"yAxis":return e7(e,r).height;default:return}},e6=(e,t,r,a)=>{if(null!=r){var{allowDuplicatedCategory:i,type:n,dataKey:o}=r,l=(0,c._L)(e,a),u=t.map(e=>e.value);if(o&&l&&"category"===n&&i&&(0,s.CG)(u))return u}},e8=(0,a.Mz)([l.fz,V,K,x.N],e6),e9=(e,t,r,a)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:n}=r;if((0,c._L)(e,a)&&("number"===i||"auto"!==n))return t.map(e=>e.value)}},e5=(0,a.Mz)([l.fz,V,C,x.N],e9),te=(0,a.Mz)([l.fz,(e,t,r)=>{switch(t){case"xAxis":return j(e,r);case"yAxis":return D(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},ek,eR,e8,e5,eF,eE,x.N],(e,t,r,a,i,n,o,l,u)=>{if(null==t)return null;var d=(0,c._L)(e,u);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:u,categoricalDomain:n,duplicateDomain:i,isCategorical:d,niceTicks:l,range:o,realScaleType:r,scale:a}}),tt=(0,a.Mz)([l.fz,C,ek,eR,eE,eF,e8,e5,x.N],(e,t,r,a,i,n,o,l,u)=>{if(null!=t&&null!=a){var d=(0,c._L)(e,u),{type:v,ticks:p,tickCount:f}=t,y="scaleBand"===r&&"function"==typeof a.bandwidth?a.bandwidth()/2:2,h="category"===v&&a.bandwidth?a.bandwidth()/y:0;h="angleAxis"===u&&null!=n&&n.length>=2?2*(0,s.sA)(n[0]-n[1])*h:h;var m=p||i;return m?m.map((e,t)=>({index:t,coordinate:a(o?o.indexOf(e):e)+h,value:e,offset:h})).filter(e=>!(0,s.M8)(e.coordinate)):d&&l?l.map((e,t)=>({coordinate:a(e)+h,value:e,index:t,offset:h})):a.ticks?a.ticks(f).map(e=>({coordinate:a(e)+h,value:e,offset:h})):a.domain().map((e,t)=>({coordinate:a(e)+h,value:o?o[e]:e,index:t,offset:h}))}}),tr=(0,a.Mz)([l.fz,C,eR,eF,e8,e5,x.N],(e,t,r,a,i,n,o)=>{if(null!=t&&null!=r&&null!=a&&a[0]!==a[1]){var l=(0,c._L)(e,o),{tickCount:u}=t,d=0;return(d="angleAxis"===o&&(null==a?void 0:a.length)>=2?2*(0,s.sA)(a[0]-a[1])*d:d,l&&n)?n.map((e,t)=>({coordinate:r(e)+d,value:e,index:t,offset:d})):r.ticks?r.ticks(u).map(e=>({coordinate:r(e)+d,value:e,offset:d})):r.domain().map((e,t)=>({coordinate:r(e)+d,value:i?i[e]:e,index:t,offset:d}))}}),ta=(0,a.Mz)(K,eR,(e,t)=>{if(null!=e&&null!=t)return I(I({},e),{},{scale:t})}),ti=(0,a.Mz)([K,ek,eP,eB],eD);(0,a.Mz)((e,t,r)=>E(e,r),ti,(e,t)=>{if(null!=e&&null!=t)return I(I({},e),{},{scale:t})});var tn=(0,a.Mz)([l.fz,y.h,y.W],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},59146:(e,t,r)=>{r.d(t,{E:()=>i});var a=r(93833),i=(e,t)=>{var r,i=Number(t);if(!(0,a.M8)(i)&&null!=t)return i>=0?null==e||null==(r=e[i])?void 0:r.value:void 0}},62560:(e,t,r)=>{r.d(t,{BZ:()=>K,aX:()=>L,dS:()=>E,dp:()=>k,fW:()=>w,pg:()=>S,r1:()=>A,u9:()=>C,yn:()=>N});var a=r(73474),i=r(10014),n=r.n(i),o=r(92735),l=r(33053),c=r(78760),u=r(18080),d=r(79294),s=r(14710),v=r(22146),p=r(68101),f=r(59146),y=r(85812),h=r(36025),m=r(7972),g=r(48674),b=r(4638),x=r(6714),z=r(38597),w=()=>(0,o.G)(d.iO),O=(e,t)=>t,M=(e,t,r)=>r,I=(e,t,r,a)=>a,A=(0,a.Mz)(u.R4,e=>n()(e,e=>e.coordinate)),P=(0,a.Mz)([x.J,O,M,I],y.i),j=(0,a.Mz)([P,u.n4],h.P),k=(e,t,r)=>{if(null!=t){var a=(0,x.J)(e);return"axis"===t?"hover"===r?a.axisInteraction.hover.dataKey:a.axisInteraction.click.dataKey:"hover"===r?a.itemInteraction.hover.dataKey:a.itemInteraction.click.dataKey}},D=(0,a.Mz)([x.J,O,M,I],g.q),S=(0,a.Mz)([p.Lp,p.A$,s.fz,v.HZ,u.R4,I,D,b.x],m.o),E=(0,a.Mz)([P,S],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),K=(0,a.Mz)(u.R4,j,f.E),C=(0,a.Mz)([D,j,c.LF,u.Dn,K,b.x,O],z.N),N=(0,a.Mz)([P],e=>({isActive:e.active,activeIndex:e.index})),L=(e,t,r,a,i,n,o,c)=>{if(e&&t&&a&&i&&n){var u=(0,l.r4)(e.chartX,e.chartY,t,r,c);if(u){var d=(0,l.SW)(u,t),s=(0,l.gH)(d,o,n,a,i),v=(0,l.bk)(t,n,s,u);return{activeIndex:String(s),activeCoordinate:v}}}}},66145:(e,t,r)=>{r.d(t,{G:()=>n});var a=r(73474),i=r(22146),n=(0,a.Mz)([i.HZ],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}})},67204:(e,t,r)=>{r.d(t,{y:()=>h});var a=r(94285),i=r(63074),n=r.n(i),o=r(6498),l=r(98707),c=r(94863),u=r(39712),d=r(81090),s=["option","shapeType","propTransformer","activeClassName","isActive"];function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){var a,i,n;a=e,i=t,n=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function f(e,t){return p(p({},t),e)}function y(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return a.createElement(o.M,r);case"trapezoid":return a.createElement(l.j,r);case"sector":return a.createElement(c.h,r);case"symbols":if("symbols"===t)return a.createElement(d.i,r);break;default:return null}}function h(e){var t,{option:r,shapeType:i,propTransformer:o=f,activeClassName:l="recharts-active-shape",isActive:c}=e,d=function(e,t){if(null==e)return{};var r,a,i=function(e,t){if(null==e)return{};var r={};for(var a in e)if(({}).hasOwnProperty.call(e,a)){if(-1!==t.indexOf(a))continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)r=n[a],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,s);if((0,a.isValidElement)(r))t=(0,a.cloneElement)(r,p(p({},d),(0,a.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(d);else if(n()(r)&&"boolean"!=typeof r){var v=o(r,d);t=a.createElement(y,{shapeType:i,elementProps:v})}else t=a.createElement(y,{shapeType:i,elementProps:d});return c?a.createElement(u.W,{className:l},t):t}},68101:(e,t,r)=>{r.d(t,{A$:()=>i,HK:()=>o,Lp:()=>a,et:()=>n});var a=e=>e.layout.width,i=e=>e.layout.height,n=e=>e.layout.scale,o=e=>e.layout.margin},69015:(e,t,r)=>{r.d(t,{J:()=>n,U:()=>i});var a=(0,r(63047).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:i}=a.actions,n=a.reducer},70262:(e,t,r)=>{r.d(t,{N:()=>a});var a=(e,t)=>t},71931:(e,t,r)=>{r.d(t,{OS:()=>I});var a=r(73474),i=r(56199),n=r(93833),o=r(33053),l=r(2965),c=r(14710),u=r(78760),d=r(22146),s=r(79294),v=r(6944);function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var a,i,n;a=e,i=t,n=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=(e,t,r,a,i)=>i,h=(e,t,r)=>{var a=null!=r?r:e;if(!(0,n.uy)(a))return(0,n.F4)(a,t,0)},m=(0,a.Mz)([c.fz,i.ld,(e,t)=>t,(e,t,r)=>r,(e,t,r,a)=>a],(e,t,r,a,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===a).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function g(e){return null!=e.stackId&&null!=e.dataKey}var b=(0,a.Mz)([m,s.x3,(e,t,r)=>"horizontal"===(0,c.fz)(e)?(0,i.BQ)(e,"xAxis",t):(0,i.BQ)(e,"yAxis",r)],(e,t,r)=>{var a=e.filter(g),i=e.filter(e=>null==e.stackId);return[...Object.entries(a.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[a,i]=e;return{stackId:a,dataKeys:i.map(e=>e.dataKey),barSize:h(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:h(t,r,e.barSize)}))]}),x=(e,t,r,a)=>{var n,l;return"horizontal"===(0,c.fz)(e)?(n=(0,i.Gx)(e,"xAxis",t,a),l=(0,i.CR)(e,"xAxis",t,a)):(n=(0,i.Gx)(e,"yAxis",r,a),l=(0,i.CR)(e,"yAxis",r,a)),(0,o.Hj)(n,l)},z=(0,a.Mz)([b,s.JN,s._5,s.gY,(e,t,r,a,l)=>{var u,d,v,p,f=(0,c.fz)(e),y=(0,s.JN)(e),{maxBarSize:h}=l,m=(0,n.uy)(h)?y:h;return"horizontal"===f?(v=(0,i.Gx)(e,"xAxis",t,a),p=(0,i.CR)(e,"xAxis",t,a)):(v=(0,i.Gx)(e,"yAxis",r,a),p=(0,i.CR)(e,"yAxis",r,a)),null!=(u=null!=(d=(0,o.Hj)(v,p,!0))?d:m)?u:0},x,(e,t,r,a,i)=>i.maxBarSize],(e,t,r,a,i,o,l)=>{var c=function(e,t,r,a,i){var o,l=a.length;if(!(l<1)){var c=(0,n.F4)(e,r,0,!0),u=[];if((0,v.H)(a[0].barSize)){var d=!1,s=r/l,p=a.reduce((e,t)=>e+(t.barSize||0),0);(p+=(l-1)*c)>=r&&(p-=(l-1)*c,c=0),p>=r&&s>0&&(d=!0,s*=.9,p=l*s);var f={offset:((r-p)/2|0)-c,size:0};o=a.reduce((e,t)=>{var r,a=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:f.offset+f.size+c,size:d?s:null!=(r=t.barSize)?r:0}}];return f=a[a.length-1].position,a},u)}else{var y=(0,n.F4)(t,r,0,!0);r-2*y-(l-1)*c<=0&&(c=0);var h=(r-2*y-(l-1)*c)/l;h>1&&(h>>=0);var m=(0,v.H)(i)?Math.min(h,i):h;o=a.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:y+(h+c)*r+(h-m)/2,size:m}}],u)}return o}}(r,a,i!==o?i:o,e,(0,n.uy)(l)?t:l);return i!==o&&null!=c&&(c=c.map(e=>f(f({},e),{},{position:f(f({},e.position),{},{offset:e.position.offset-i/2})}))),c}),w=(0,a.Mz)([z,y],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),O=(0,a.Mz)([i.ld,y],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),M=(0,a.Mz)([(e,t,r,a)=>"horizontal"===(0,c.fz)(e)?(0,i.TC)(e,"yAxis",r,a):(0,i.TC)(e,"xAxis",t,a),y],(e,t)=>{if(!e||(null==t?void 0:t.dataKey)==null)return;var{stackId:r}=t;if(null!=r){var a=e[r];if(a){var{stackedData:i}=a;if(i)return i.find(e=>e.key===t.dataKey)}}}),I=(0,a.Mz)([d.HZ,(e,t,r,a)=>(0,i.Gx)(e,"xAxis",t,a),(e,t,r,a)=>(0,i.Gx)(e,"yAxis",r,a),(e,t,r,a)=>(0,i.CR)(e,"xAxis",t,a),(e,t,r,a)=>(0,i.CR)(e,"yAxis",r,a),w,c.fz,u.HS,x,M,O,(e,t,r,a,i,n)=>n],(e,t,r,a,i,n,o,c,u,d,s,v)=>{var p,{chartData:f,dataStartIndex:y,dataEndIndex:h}=c;if(null!=s&&null!=n&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=a&&null!=i&&null!=u){var{data:m}=s;if(null!=(p=null!=m&&m.length>0?m:null==f?void 0:f.slice(y,h+1)))return(0,l.L)({layout:o,barSettings:s,pos:n,bandSize:u,xAxis:t,yAxis:r,xAxisTicks:a,yAxisTicks:i,stackedData:d,displayedData:p,offset:e,cells:v})}})},78760:(e,t,r)=>{r.d(t,{HS:()=>o,LF:()=>i,z3:()=>n});var a=r(73474),i=e=>e.chartData,n=(0,a.Mz)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),o=(e,t,r,a)=>a?n(e):i(e)},79294:(e,t,r)=>{r.d(t,{JN:()=>a,_5:()=>i,eC:()=>l,gY:()=>n,hX:()=>d,iO:()=>c,lZ:()=>u,pH:()=>s,x3:()=>o});var a=e=>e.rootProps.maxBarSize,i=e=>e.rootProps.barGap,n=e=>e.rootProps.barCategoryGap,o=e=>e.rootProps.barSize,l=e=>e.rootProps.stackOffset,c=e=>e.options.chartName,u=e=>e.rootProps.syncId,d=e=>e.rootProps.syncMethod,s=e=>e.options.eventEmitter},82527:(e,t,r)=>{r.d(t,{g:()=>u});var a=r(73474),i=r(14710),n=r(18080),o=r(22146),l=r(62560),c=r(49507),u=(0,a.Mz)([(e,t)=>t,i.fz,c.D0,n.Re,n.gL,n.R4,l.r1,o.HZ],l.aX)},84288:(e,t,r)=>{r.d(t,{W:()=>n,h:()=>i});var a=r(73474),i=(0,a.Mz)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),n=(0,a.Mz)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},85812:(e,t,r)=>{r.d(t,{i:()=>o});var a=r(93042);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function n(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var a,i,n;a=e,i=t,n=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o=(e,t,r,i)=>{if(null==t)return a.k_;var o=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==o)return a.k_;if(o.active)return o;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var l=!0===e.settings.active;if(null!=o.index){if(l)return n(n({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return n(n({},a.k_),{},{coordinate:o.coordinate})}},87379:(e,t,r)=>{r.d(t,{E:()=>a});var a=(e,t,r)=>r},88346:(e,t,r)=>{r.d(t,{e:()=>f,k:()=>y});var a=r(63047),i=r(93042),n=r(82527),o=r(32037),l=r(13831),c=r(37661),u=r(73474),d=r(4638),s=r(6714),v=(0,u.Mz)([s.J],e=>e.tooltipItemPayloads),p=(0,u.Mz)([v,d.x,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,a)=>{var i=e.find(e=>e.settings.dataKey===a);if(null!=i){var{positions:n}=i;if(null!=n)return t(n,r)}}),f=(0,a.VP)("touchMove"),y=(0,a.Nc)();y.startListening({actionCreator:f,effect:(e,t)=>{var r=e.payload,a=t.getState(),u=(0,l.au)(a,a.tooltip.settings.shared);if("axis"===u){var d=(0,n.g)(a,(0,o.w)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==d?void 0:d.activeIndex)!=null&&t.dispatch((0,i.Nt)({activeIndex:d.activeIndex,activeDataKey:void 0,activeCoordinate:d.activeCoordinate}))}else if("item"===u){var s,v=r.touches[0],f=document.elementFromPoint(v.clientX,v.clientY);if(!f||!f.getAttribute)return;var y=f.getAttribute(c.F0),h=null!=(s=f.getAttribute(c.um))?s:void 0,m=p(t.getState(),y,h);t.dispatch((0,i.RD)({activeDataKey:h,activeIndex:y,activeCoordinate:m}))}}})},91507:(e,t,r)=>{function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function i(e,t,r){var a;return(t="symbol"==typeof(a=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?a:a+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.d(t,{bx:()=>o});class n{static create(e){return new n(e)}constructor(e){this.scale=e}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],a=t[t.length-1];return r<=a?e>=r&&e<=a:e>=a&&e<=r}}i(n,"EPS",1e-4);var o=function(e){var{width:t,height:r}=e,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(a%180+180)%180*Math.PI/180,n=Math.atan(r/t);return Math.abs(i>n&&i<Math.PI-n?r/Math.sin(i):t/Math.cos(i))}},92735:(e,t,r)=>{r.d(t,{G:()=>s,j:()=>l});var a=r(79007),i=r(94285),n=r(75764),o=e=>e,l=()=>{var e=(0,i.useContext)(n.E);return e?e.store.dispatch:o},c=()=>{},u=()=>c,d=(e,t)=>e===t;function s(e){var t=(0,i.useContext)(n.E);return(0,a.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:u,t?t.store.getState:c,t?t.store.getState:c,t?e:c,d)}},92860:(e,t,r)=>{r.d(t,{l3:()=>y,m7:()=>h});var a=r(94285),i=r(92735),n=r(79294),o=r(5449),l=r(17649),c=r(93042),u=r(62560),d=r(18080);function s(e){return e.tooltip.syncInteraction}var v=r(14710),p=r(59707),f=()=>{};function y(){var e,t,r,u,s,y,h,m,g,b,x,z=(0,i.j)();(0,a.useEffect)(()=>{z((0,l.dl)())},[z]),e=(0,i.G)(n.lZ),t=(0,i.G)(n.pH),r=(0,i.j)(),u=(0,i.G)(n.hX),s=(0,i.G)(d.R4),y=(0,v.WX)(),h=(0,v.sk)(),m=(0,i.G)(e=>e.rootProps.className),(0,a.useEffect)(()=>{if(null==e)return f;var a=(a,i,n)=>{if(t!==n&&e===a){if("index"===u)return void r(i);if(null!=s){if("function"==typeof u){var o,l=u(s,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});o=s[l]}else"value"===u&&(o=s.find(e=>String(e.value)===i.payload.label));var{coordinate:d}=i.payload;if(null==o||!1===i.payload.active||null==d||null==h)return void r((0,c.E1)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:v,y:p}=d,f=Math.min(v,h.x+h.width),m=Math.min(p,h.y+h.height),g={x:"horizontal"===y?o.coordinate:f,y:"horizontal"===y?m:o.coordinate};r((0,c.E1)({active:i.payload.active,coordinate:g,dataKey:i.payload.dataKey,index:String(o.index),label:i.payload.label}))}}};return o.kA.on(o.NK,a),()=>{o.kA.off(o.NK,a)}},[m,r,t,e,u,s,y,h]),g=(0,i.G)(n.lZ),b=(0,i.G)(n.pH),x=(0,i.j)(),(0,a.useEffect)(()=>{if(null==g)return f;var e=(e,t,r)=>{b!==r&&g===e&&x((0,p.M)(t))};return o.kA.on(o.gN,e),()=>{o.kA.off(o.gN,e)}},[x,b,g])}function h(e,t,r,l,d,v){var p=(0,i.G)(r=>(0,u.dp)(r,e,t)),f=(0,i.G)(n.pH),y=(0,i.G)(n.lZ),h=(0,i.G)(n.hX),m=(0,i.G)(s),g=null==m?void 0:m.active;(0,a.useEffect)(()=>{if(!g&&null!=y&&null!=f){var e=(0,c.E1)({active:v,coordinate:r,dataKey:p,index:d,label:"number"==typeof l?String(l):l});o.kA.emit(o.NK,y,e,f)}},[g,r,p,d,l,f,y,h,v])}},93042:(e,t,r)=>{r.d(t,{E1:()=>h,En:()=>g,Ix:()=>l,ML:()=>p,Nt:()=>f,RD:()=>d,UF:()=>u,XB:()=>c,jF:()=>y,k_:()=>n,o4:()=>m,oP:()=>s,xS:()=>v});var a=r(63047),i=r(74201),n={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,a.Z0)({name:"tooltip",initialState:{itemInteraction:{click:n,hover:n},axisInteraction:{click:n,hover:n},keyboardInteraction:n,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,i.h4)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,a.ss)(e).tooltipItemPayloads.indexOf((0,i.h4)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:c,setTooltipSettingsState:u,setActiveMouseOverItemIndex:d,mouseLeaveItem:s,mouseLeaveChart:v,setActiveClickItemIndex:p,setMouseOverAxisIndex:f,setMouseClickAxisIndex:y,setSyncInteraction:h,setKeyboardInteraction:m}=o.actions,g=o.reducer}}]);