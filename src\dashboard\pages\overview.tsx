// @ts-nocheck
import { Box, Heading, Text, Icon, HStack, Flex, Wrap, WrapItem, SimpleGrid, Stat, StatLabel, StatNumber, StatHelpText, Card, CardBody, VStack, Skeleton, useToast } from '@chakra-ui/react';
import { FaQuoteLeft, FaQuoteRight, FaRobot } from 'react-icons/fa';
import { FiUsers, FiMessageSquare, FiActivity, FiServer, FiTrendingUp, FiAlertCircle } from 'react-icons/fi';
import Layout from '../components/Layout';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './api/auth/[...nextauth]';
import { CARD_CONFIGS } from '../config/cards';
import { keyframes } from '@emotion/react';
import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { useSession } from 'next-auth/react';
import { OverviewCard } from '../components/OverviewCard';
import useExperimentalFeatures from '../hooks/useExperimentalFeatures';
import NextLink from 'next/link';
import { Link as ChakraLink } from '@chakra-ui/react';
import React from 'react';

// Hidden message animation
const glowKeyframes = keyframes`
  0% { opacity: 0.3; }
  50% { opacity: 0.7; }
  100% { opacity: 0.3; }
`;

export default function Overview() {
  const { data: session } = useSession();
  const [analyticsData, setAnalyticsData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const toast = useToast();

  // Experimental feature access state
  const {
    hasAccess: experimentalAccess,
    isDeveloper: isExperimentalDeveloper,
    isLoading: isExperimentalLoading,
  } = useExperimentalFeatures();

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const [serverRes, botRes] = await Promise.all([
          fetch('/api/analytics/server'),
          fetch('/api/analytics/bot')
        ]);

        if (!serverRes.ok || !botRes.ok) {
          throw new Error('Failed to fetch analytics');
        }

        const [serverData, botData] = await Promise.all([
          serverRes.json(),
          botRes.json()
        ]);

        setAnalyticsData({
          serverStats: serverData.serverStats,
          botStats: botData.botStats,
        });
      } catch (error) {
        console.error('Error fetching analytics:', error);
        toast({
          title: 'Error',
          description: 'Failed to load analytics data',
          status: 'error',
          duration: 5000,
        });
        // Fallback to mock data
        setAnalyticsData({
          serverStats: {
            totalMembers: 0,
            onlineMembers: 0,
            totalChannels: 0,
            totalRoles: 0,
          },
          botStats: {
            commandsToday: 0,
            uptime: 'Unknown',
            responseTime: '0ms',
            activeAddons: 0,
            inactiveAddons: 0,
          }
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalytics();
  }, [toast]);

  const quotes = [
    '"Talk is cheap. Show me the code." – Linus Torvalds',
    '"Programs must be written for people to read, and only incidentally for machines to execute." – Harold Abelson',
    '"Any fool can write code that a computer can understand. Good programmers write code that humans can understand." – Martin Fowler',
    '"First, solve the problem. Then, write the code." – John Johnson',
    '"404 Chill Not Found? Keep calm and debug on." – Unknown',
    "It's not a bug – it's an undocumented feature.",
    '"The best error message is the one that never shows up." – Thomas Fuchs',
    "Code is like humor. When you have to explain it, it's bad.",
    '"Experience is the name everyone gives to their mistakes." – Oscar Wilde',
    '"In order to be irreplaceable, one must always be different." – Coco Chanel',
  ];

  // Use a stable quote selection based on the day of the month
  const getQuoteOfTheDay = () => {
    const today = new Date();
    const dayOfMonth = today.getDate(); // 1-31
    return quotes[dayOfMonth % quotes.length];
  };

  const quoteOfTheDay = getQuoteOfTheDay();

  // Filter cards based on user role (excluding experimental features)
  const filteredCards = CARD_CONFIGS.filter(card => {
    if (card.requiredRole === 'admin') {
      return (session?.user as any)?.isAdmin;
    }
    if (card.requiredRole === 'moderator') {
      return (session?.user as any)?.isModerator;
    }
    // Exclude both overview and experimental cards
    return card.id !== 'overview' && card.id !== 'experimental';
  });

  // Chart data
  const channelDistribution = analyticsData ? [
    { name: 'Text', value: analyticsData.serverStats.textChannels || 0, color: '#4299E1' },
    { name: 'Voice', value: analyticsData.serverStats.voiceChannels || 0, color: '#48BB78' },
    { name: 'Categories', value: analyticsData.serverStats.categories || 0, color: '#9F7AEA' },
  ] : [];

  const orderedDays = ['Mon','Tue','Wed','Thu','Fri','Sat','Sun'];
  const defaultWeekly = orderedDays.map(day => ({ day, commands: 0, joins: 0, leaves: 0 }));

  const weeklyActivity = analyticsData ? orderedDays.map(day => {
    const botEntry = analyticsData.botStats?.weeklyActivity?.find((e:any) => e.day === day) || {};
    const memberEntry = analyticsData.serverStats?.weeklyMembers?.find((e:any) => e.day === day) || {};
    return {
      day,
      commands: botEntry.commands || 0,
      joins: memberEntry.joins || 0,
      leaves: memberEntry.leaves || 0,
    };
  }) : defaultWeekly;

  return (
    <Layout>
      <Box
        p={8}
        position="relative"
        _before={{
          content: '""',
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '100%',
          height: '100%',
          background: 'radial-gradient(circle at center, rgba(66, 153, 225, 0.1) 0%, transparent 70%)',
          pointerEvents: 'none',
        }}
      >
        {/* Hidden oracle message */}
        <Box
          position="absolute"
          top={0}
          left={0}
          width="100%"
          height="100%"
          pointerEvents="none"
          opacity={0.05}
          sx={{
            '@keyframes glow': {
              '0%': { opacity: 0.03 },
              '50%': { opacity: 0.07 },
              '100%': { opacity: 0.03 }
            },
            animation: 'glow 4s infinite'
          }}
          fontSize="3xl"
          fontFamily="monospace"
          color="blue.200"
          textAlign="center"
          pt={20}
        >
          ORACLE
        </Box>

        {/* Analytics Section */}
        <VStack spacing={8} mb={8}>
          <Heading size="lg" textAlign="center" display="flex" alignItems="center" justifyContent="center">
            <span style={{ marginRight: '0.5rem' }} role="img" aria-label="chart">📊</span>
            <Box as="span" bgGradient="linear(to-r, blue.300, purple.400)" bgClip="text">Server Analytics</Box>
          </Heading>
          
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} w="full">
            {isLoading ? (
              // Loading skeletons
              Array.from({ length: 4 }).map((_, i) => (
                <Card key={i} bg="whiteAlpha.100" backdropFilter="blur(10px)">
                  <CardBody>
                    <Skeleton height="80px" />
                  </CardBody>
                </Card>
              ))
            ) : (
              <>
                <Card bg="whiteAlpha.100" backdropFilter="blur(10px)">
                  <CardBody>
                    <Stat>
                      <HStack>
                        <Icon as={FiUsers} color="blue.400" boxSize={6} />
                        <StatLabel color="gray.300">Total Members</StatLabel>
                      </HStack>
                      <StatNumber color="white" fontSize="2xl">{analyticsData?.serverStats.totalMembers.toLocaleString() || '0'}</StatNumber>
                      <StatHelpText color="green.400">
                        <Icon as={FiTrendingUp} mr={1} />
                        {analyticsData?.serverStats.onlineMembers || '0'} online
                      </StatHelpText>
                      <StatHelpText color="green.300">
                        +{analyticsData?.serverStats.newMembersToday || 0} joined
                      </StatHelpText>
                      <StatHelpText color="red.400">
                        -{analyticsData?.serverStats.leftMembersToday || 0} left
                      </StatHelpText>
                    </Stat>
                  </CardBody>
                </Card>

                <Card bg="whiteAlpha.100" backdropFilter="blur(10px)">
                  <CardBody>
                    <Stat>
                      <HStack>
                        <Icon as={FiMessageSquare} color="green.400" boxSize={6} />
                        <StatLabel color="gray.300">Channels</StatLabel>
                      </HStack>
                      <StatNumber color="white" fontSize="2xl">{analyticsData?.serverStats.totalChannels || '0'}</StatNumber>
                      <StatHelpText color="gray.400">
                        {analyticsData?.serverStats.totalRoles || '0'} roles
                      </StatHelpText>
                    </Stat>
                  </CardBody>
                </Card>

                <Card bg="whiteAlpha.100" backdropFilter="blur(10px)">
                  <CardBody>
                    <Stat>
                      <HStack>
                        <Icon as={FiActivity} color="purple.400" boxSize={6} />
                        <StatLabel color="gray.300">Commands Today</StatLabel>
                      </HStack>
                      <StatNumber color="white" fontSize="2xl">{analyticsData?.botStats.commandsToday || '0'}</StatNumber>
                      <StatHelpText color="gray.400">
                        {analyticsData?.botStats.responseTime || '0ms'} avg response
                      </StatHelpText>
                    </Stat>
                  </CardBody>
                </Card>

                <Card bg="whiteAlpha.100" backdropFilter="blur(10px)">
                  <CardBody>
                    <Stat>
                      <HStack>
                        <Icon as={FiServer} color="orange.400" boxSize={6} />
                        <StatLabel color="gray.300">Bot Uptime</StatLabel>
                      </HStack>
                      <StatNumber color="white" fontSize="xl">{analyticsData?.botStats.uptime || 'Unknown'}</StatNumber>
                      <StatHelpText color="green.400">
                        {analyticsData?.botStats.activeAddons || '0'} addons active
                      </StatHelpText>
                      <StatHelpText color="red.400">
                        {analyticsData?.botStats.inactiveAddons || '0'} addons inactive
                      </StatHelpText>
                    </Stat>
                  </CardBody>
                </Card>

                {/* Add error count card if there are errors */}
                {analyticsData?.botStats.errorsToday > 0 && (
                  <ChakraLink as={NextLink} href="/admin/errors" _hover={{ textDecoration: 'none' }} w="full">
                    <Card bg="whiteAlpha.100" backdropFilter="blur(10px)" borderColor="red.400" borderWidth="1px" cursor="pointer" _hover={{ transform: 'translateY(-4px)', boxShadow: '0 4px 12px rgba(0,0,0,0.2)', borderColor: 'red.500' }}>
                      <CardBody>
                        <Stat>
                          <HStack>
                            <Icon as={FiAlertCircle} color="red.400" boxSize={6} />
                            <StatLabel color="gray.300">Errors Today</StatLabel>
                          </HStack>
                          <StatNumber color="red.400" fontSize="2xl">{analyticsData.botStats.errorsToday}</StatNumber>
                          <StatHelpText color="red.300">
                            Needs attention
                          </StatHelpText>
                        </Stat>
                      </CardBody>
                    </Card>
                  </ChakraLink>
                )}
              </>
            )}
          </SimpleGrid>
        </VStack>

        {/* Charts Section */}
        {!isLoading && analyticsData && (
          <VStack spacing={8} mb={8}>
            <Heading size="lg" textAlign="center" display="flex" alignItems="center" justifyContent="center">
              <span style={{ marginRight: '0.5rem' }} role="img" aria-label="graph">📈</span>
              <Box as="span" bgGradient="linear(to-r, blue.300, purple.400)" bgClip="text">Activity Overview</Box>
            </Heading>
            
            <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={8} w="full">
              {/* Channel Distribution Chart */}
              <Card bg="whiteAlpha.100" backdropFilter="blur(10px)">
                <CardBody>
                  <VStack spacing={4}>
                    <Heading size="md" color="white">Channel Distribution</Heading>
                    <Box h="200px" w="full">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={channelDistribution}
                            cx="50%"
                            cy="50%"
                            innerRadius={40}
                            outerRadius={80}
                            paddingAngle={5}
                            dataKey="value"
                          >
                            {channelDistribution.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip 
                            wrapperStyle={{ backgroundColor: 'transparent' }}
                            contentStyle={{ 
                              backgroundColor: 'rgba(26, 32, 44, 0.9)', 
                              border: '1px solid rgba(255,255,255,0.2)',
                              borderRadius: '8px',
                              color: '#fff'
                            }}
                            itemStyle={{ color: '#fff' }}
                            labelStyle={{ color: '#fff' }}
                          />
                        </PieChart>
                      </ResponsiveContainer>
                    </Box>
                    <HStack spacing={4} justify="center">
                      {channelDistribution.map((item, index) => (
                        <HStack key={index} spacing={2}>
                          <Box w="3" h="3" bg={item.color} rounded="full" />
                          <Text fontSize="sm" color="gray.300">{item.name}: {item.value}</Text>
                        </HStack>
                      ))}
                    </HStack>
                  </VStack>
                </CardBody>
              </Card>

              {/* Weekly Activity Chart */}
              <Card bg="whiteAlpha.100" backdropFilter="blur(10px)">
                <CardBody>
                  <VStack spacing={4}>
                    <Heading size="md" color="white">Weekly Activity</Heading>
                    <Box h="200px" w="full">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={weeklyActivity}>
                          <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                          <XAxis 
                            dataKey="day" 
                            axisLine={false}
                            tickLine={false}
                            tick={{ fill: '#A0AEC0', fontSize: 12 }}
                          />
                          <YAxis 
                            axisLine={false}
                            tickLine={false}
                            tick={{ fill: '#A0AEC0', fontSize: 12 }}
                          />
                          <Tooltip 
                            wrapperStyle={{ backgroundColor: 'transparent' }}
                            contentStyle={{ 
                              backgroundColor: 'rgba(26, 32, 44, 0.9)', 
                              border: '1px solid rgba(255,255,255,0.2)',
                              borderRadius: '8px',
                              color: '#fff'
                            }}
                            itemStyle={{ color: '#fff' }}
                            labelStyle={{ color: '#fff' }}
                            cursor={{ fill: 'rgba(255,255,255,0.08)' }}
                          />
                          <Bar dataKey="commands" fill="#4299E1" name="Commands" />
                          <Bar dataKey="joins" fill="#48BB78" name="Joins" />
                          <Bar dataKey="leaves" fill="#E53E3E" name="Leaves" />
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  </VStack>
                </CardBody>
              </Card>
            </SimpleGrid>
          </VStack>
        )}

        {/* Additional overview cards disabled per user request */}
        <Wrap spacing="24px" justify="start">
          {filteredCards.map(card => (
            <WrapItem key={card.id} flex="1 0 260px">
              <Box onClick={() =>
                window.dispatchEvent(new CustomEvent('colorClick', { detail: card.color }))
              }>
                <OverviewCard {...card}/>
              </Box>
            </WrapItem>
          ))}
        </Wrap>
      </Box>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);
  if (!session) {
    return {
      redirect: {
        destination: '/signin',
        permanent: false,
      },
    };
  }
  return { props: {} };
}; 