"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/analytics/bot";
exports.ids = ["pages/api/analytics/bot"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fanalytics%2Fbot&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Canalytics%5Cbot.ts&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fanalytics%2Fbot&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Canalytics%5Cbot.ts&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_analytics_bot_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\analytics\\bot.ts */ \"(api-node)/./pages/api/analytics/bot.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_analytics_bot_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_analytics_bot_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/analytics/bot\",\n        pathname: \"/api/analytics/bot\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_analytics_bot_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fanalytics%2Fbot&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Canalytics%5Cbot.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/analytics/bot.ts":
/*!************************************!*\
  !*** ./pages/api/analytics/bot.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// Reuse connection pattern\nlet cachedClient = null;\nconst mongoUrl = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.database?.url || 'mongodb://localhost:27017';\nconst dbName = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.database?.name || 'discord_bot';\nasync function getDb() {\n    if (!cachedClient) {\n        cachedClient = await mongodb__WEBPACK_IMPORTED_MODULE_3__.MongoClient.connect(mongoUrl, {\n            ..._core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.database?.options || {}\n        });\n    }\n    return cachedClient.db(dbName);\n}\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session) {\n            return res.status(401).json({\n                error: 'Unauthorized'\n            });\n        }\n        const db = await getDb();\n        // Get today's date range\n        const today = new Date();\n        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());\n        const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);\n        // Query command usage from logs or command_usage collection\n        const commandsToday = await db.collection('command_logs').countDocuments({\n            timestamp: {\n                $gte: startOfDay,\n                $lt: endOfDay\n            }\n        }).catch(()=>0); // Fallback if collection doesn't exist\n        // Helper functions for path resolution\n        const fs = __webpack_require__(/*! fs */ \"fs\");\n        const path = __webpack_require__(/*! path */ \"path\");\n        const YAML = __webpack_require__(/*! yaml */ \"yaml\");\n        const locateConfig = ()=>{\n            const attempts = [\n                '404-bot/config.yml',\n                'config.yml',\n                '../config.yml',\n                '../../config.yml',\n                '../../../config.yml',\n                '../../../../config.yml'\n            ].map((rel)=>path.resolve(process.cwd(), rel));\n            let found = attempts.find((p)=>fs.existsSync(p));\n            if (!found) {\n                const dirBased = path.resolve(__dirname, '../../../../../../../config.yml');\n                if (fs.existsSync(dirBased)) found = dirBased;\n            }\n            if (!found) throw new Error('config.yml not found');\n            return found;\n        };\n        const locateAddonsDir = ()=>{\n            const attempts = [\n                '404-bot/src/addons',\n                'src/addons',\n                '../src/addons',\n                '../../src/addons',\n                '../../../src/addons',\n                '../../../../src/addons'\n            ].map((rel)=>path.resolve(process.cwd(), rel));\n            let found = attempts.find((p)=>fs.existsSync(p));\n            if (!found) {\n                const dirBased = path.resolve(__dirname, '../../../../../../../src/addons');\n                if (fs.existsSync(dirBased)) found = dirBased;\n            }\n            if (!found) throw new Error('Addons directory not found');\n            return found;\n        };\n        // Get active addons count from config.yml and directory structure\n        let activeAddons = 0;\n        let inactiveAddons = 0;\n        try {\n            // Read config file\n            const configPath = locateConfig();\n            const configFile = fs.readFileSync(configPath, 'utf8');\n            const config = YAML.parse(configFile);\n            const addonsGloballyEnabled = config.addons?.enabled !== false;\n            const disabled = config.addons?.disabled ?? [];\n            if (addonsGloballyEnabled) {\n                // Get addons directory\n                const addonsDir = locateAddonsDir();\n                const addonDirs = fs.readdirSync(addonsDir, {\n                    withFileTypes: true\n                }).filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n                const activeDirs = addonDirs.filter((dir)=>!disabled.includes(dir));\n                const inactiveDirs = addonDirs.filter((dir)=>disabled.includes(dir));\n                console.log('Analytics API - Detected addon directories:', {\n                    addonDirs,\n                    activeDirs,\n                    inactiveDirs,\n                    disabled\n                }); // Debug log\n                activeAddons = activeDirs.length;\n                inactiveAddons = inactiveDirs.length;\n            }\n        } catch (err) {\n            console.error('Error counting addons:', err);\n            activeAddons = 0;\n            inactiveAddons = 0; // Fallback\n        }\n        // Calculate uptime\n        const botStartTime = await db.collection('bot_status').findOne({\n            key: 'start_time'\n        }).catch(()=>null);\n        let uptime = '0d 0h 0m';\n        if (botStartTime?.timestamp) {\n            const uptimeMs = Date.now() - new Date(botStartTime.timestamp).getTime();\n            const days = Math.floor(uptimeMs / (1000 * 60 * 60 * 24));\n            const hours = Math.floor(uptimeMs % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n            const minutes = Math.floor(uptimeMs % (1000 * 60 * 60) / (1000 * 60));\n            uptime = `${days}d ${hours}h ${minutes}m`;\n        } else {\n            // If no start time recorded, estimate based on process uptime\n            const uptimeSeconds = process.uptime();\n            const days = Math.floor(uptimeSeconds / (60 * 60 * 24));\n            const hours = Math.floor(uptimeSeconds % (60 * 60 * 24) / (60 * 60));\n            const minutes = Math.floor(uptimeSeconds % (60 * 60) / 60);\n            uptime = `${days}d ${hours}h ${minutes}m`;\n        }\n        // Get average response time from recent commands\n        const recentCommands = await db.collection('command_logs').find({\n            timestamp: {\n                $gte: new Date(Date.now() - 60 * 60 * 1000)\n            },\n            responseTime: {\n                $exists: true\n            }\n        }).limit(100).toArray().catch(()=>[]);\n        const avgResponseTime = recentCommands.length > 0 ? Math.round(recentCommands.reduce((sum, cmd)=>sum + (cmd.responseTime || 0), 0) / recentCommands.length) : 45; // Default fallback\n        // Get error count today\n        const errorsToday = await db.collection('error_logs').countDocuments({\n            timestamp: {\n                $gte: startOfDay,\n                $lt: endOfDay\n            }\n        }).catch(()=>0);\n        // Weekly activity (commands & unique members per day for the last 7 days)\n        const sevenDaysAgo = new Date();\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6); // include today\n        const logsLastWeek = await db.collection('command_logs').find({\n            timestamp: {\n                $gte: sevenDaysAgo\n            }\n        }).toArray().catch(()=>[]);\n        const daysMap = {\n            Mon: {\n                commands: 0,\n                members: new Set()\n            },\n            Tue: {\n                commands: 0,\n                members: new Set()\n            },\n            Wed: {\n                commands: 0,\n                members: new Set()\n            },\n            Thu: {\n                commands: 0,\n                members: new Set()\n            },\n            Fri: {\n                commands: 0,\n                members: new Set()\n            },\n            Sat: {\n                commands: 0,\n                members: new Set()\n            },\n            Sun: {\n                commands: 0,\n                members: new Set()\n            }\n        };\n        const dayLabels = [\n            'Sun',\n            'Mon',\n            'Tue',\n            'Wed',\n            'Thu',\n            'Fri',\n            'Sat'\n        ];\n        for (const log of logsLastWeek){\n            const d = new Date(log.timestamp);\n            const label = dayLabels[d.getDay()];\n            if (!daysMap[label]) continue; // should exist\n            daysMap[label].commands += 1;\n            if (log.userId) daysMap[label].members.add(log.userId);\n        }\n        const orderedDays = [\n            'Mon',\n            'Tue',\n            'Wed',\n            'Thu',\n            'Fri',\n            'Sat',\n            'Sun'\n        ];\n        const weeklyActivity = orderedDays.map((day)=>({\n                day,\n                commands: daysMap[day].commands,\n                members: daysMap[day].members.size\n            }));\n        const botStats = {\n            commandsToday,\n            uptime,\n            responseTime: `${avgResponseTime}ms`,\n            activeAddons,\n            inactiveAddons,\n            weeklyActivity,\n            errorsToday,\n            status: 'online'\n        };\n        res.status(200).json({\n            botStats\n        });\n    } catch (error) {\n        console.error('Error fetching bot analytics:', error);\n        res.status(500).json({\n            error: 'Failed to fetch bot analytics',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/analytics/bot.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_d3-sc","lib-node_modules_pnpm_d3-time-","lib-node_modules_pnpm_dec","lib-node_modules_pnpm_e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_immer_10_1_1_node_modules_immer_dist_immer_mjs-806fdd73","lib-node_modules_pnpm_i","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_react-c","lib-node_modules_pnpm_react-red","lib-node_modules_pnpm_rea","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-5c215c87","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-bf697780","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-334e6784","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-d89a0eeb","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-4c6b09db","lib-node_modules_pnpm_recharts_3_1_0__types_react_24e4fd5eafdf9f58d04ae1b2ce1d6a15_node_modules_rech-d29869f5","lib-node_modules_pnpm_rec","lib-node_modules_pnpm_redux_","lib-node_modules_pnpm_r","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fanalytics%2Fbot&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Canalytics%5Cbot.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();