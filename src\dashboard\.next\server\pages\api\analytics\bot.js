"use strict";(()=>{var e={};e.id=3659,e.ids=[3659],e.modules={5332:(e,t,o)=>{o.r(t),o.d(t,{config:()=>w,default:()=>g,routeModule:()=>b});var a={};o.r(a),o.d(a,{default:()=>p});var n=o(93433),r=o(20264),s=o(20584),i=o(15806),d=o(94506),m=o(98580),l=o(12518);let c=null,u=m.dashboardConfig.database?.url||"mongodb://localhost:27017",f=m.dashboardConfig.database?.name||"discord_bot";async function h(){return c||(c=await l.MongoClient.connect(u,{...m.dashboardConfig.database?.options||{}})),c.db(f)}async function p(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{if(!await (0,i.getServerSession)(e,t,d.authOptions))return t.status(401).json({error:"Unauthorized"});let a=await h(),n=new Date,r=new Date(n.getFullYear(),n.getMonth(),n.getDate()),s=new Date(r.getTime()+864e5),m=await a.collection("command_logs").countDocuments({timestamp:{$gte:r,$lt:s}}).catch(()=>0),l=o(29021),c=o(33873),u=o(72115),f=0,p=0;try{let e=(()=>{let e=["404-bot/config.yml","config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>c.resolve(process.cwd(),e)).find(e=>l.existsSync(e));if(!e){let t=c.resolve(__dirname,"../../../../../../../config.yml");l.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");return e})(),t=l.readFileSync(e,"utf8"),o=u.parse(t),a=o.addons?.enabled!==!1,n=o.addons?.disabled??[];if(a){let e=(()=>{let e=["404-bot/src/addons","src/addons","../src/addons","../../src/addons","../../../src/addons","../../../../src/addons"].map(e=>c.resolve(process.cwd(),e)).find(e=>l.existsSync(e));if(!e){let t=c.resolve(__dirname,"../../../../../../../src/addons");l.existsSync(t)&&(e=t)}if(!e)throw Error("Addons directory not found");return e})(),t=l.readdirSync(e,{withFileTypes:!0}).filter(e=>e.isDirectory()).map(e=>e.name),o=t.filter(e=>!n.includes(e)),a=t.filter(e=>n.includes(e));f=o.length,p=a.length}}catch(e){f=0,p=0}let g=await a.collection("bot_status").findOne({key:"start_time"}).catch(()=>null),w="0d 0h 0m";if(g?.timestamp){let e=Date.now()-new Date(g.timestamp).getTime(),t=Math.floor(e/864e5),o=Math.floor(e%864e5/36e5),a=Math.floor(e%36e5/6e4);w=`${t}d ${o}h ${a}m`}else{let e=process.uptime(),t=Math.floor(e/86400),o=Math.floor(e%86400/3600),a=Math.floor(e%3600/60);w=`${t}d ${o}h ${a}m`}let b=await a.collection("command_logs").find({timestamp:{$gte:new Date(Date.now()-36e5)},responseTime:{$exists:!0}}).limit(100).toArray().catch(()=>[]),y=b.length>0?Math.round(b.reduce((e,t)=>e+(t.responseTime||0),0)/b.length):45,S=await a.collection("error_logs").countDocuments({timestamp:{$gte:r,$lt:s}}).catch(()=>0),x=new Date;x.setDate(x.getDate()-6);let M=await a.collection("command_logs").find({timestamp:{$gte:x}}).toArray().catch(()=>[]),D={Mon:{commands:0,members:new Set},Tue:{commands:0,members:new Set},Wed:{commands:0,members:new Set},Thu:{commands:0,members:new Set},Fri:{commands:0,members:new Set},Sat:{commands:0,members:new Set},Sun:{commands:0,members:new Set}},$=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"];for(let e of M){let t=$[new Date(e.timestamp).getDay()];D[t]&&(D[t].commands+=1,e.userId&&D[t].members.add(e.userId))}let T=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"].map(e=>({day:e,commands:D[e].commands,members:D[e].members.size})),v={commandsToday:m,uptime:w,responseTime:`${y}ms`,activeAddons:f,inactiveAddons:p,weeklyActivity:T,errorsToday:S,status:"online"};t.status(200).json({botStats:v})}catch(e){t.status(500).json({error:"Failed to fetch bot analytics",details:e instanceof Error?e.message:"Unknown error"})}}let g=(0,s.M)(a,"default"),w=(0,s.M)(a,"config"),b=new n.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/analytics/bot",pathname:"/api/analytics/bot",bundlePath:"",filename:""},userland:a})},12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=e=>t(t.s=e),a=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>o(5332));module.exports=a})();