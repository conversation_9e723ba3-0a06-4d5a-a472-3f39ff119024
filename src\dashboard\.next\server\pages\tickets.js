"use strict";(()=>{var e={};e.id=8111,e.ids=[636,3220,8111],e.modules={4722:e=>{e.exports=require("next-auth/react")},8732:e=>{e.exports=require("react/jsx-runtime")},11912:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{$m:()=>d.$,$n:()=>i.$,MJ:()=>a.MJ,TM:()=>j.T,aF:()=>o.aF,cw:()=>c.c,dj:()=>g.d,jl:()=>h.j,l6:()=>x.l,lR:()=>n.l,mH:()=>p.m,rQ:()=>u.r,s_:()=>l.s});var i=r(77502),a=r(23678),n=r(63957),o=r(75460),c=r(42929),l=r(7394),d=r(89164),h=r(87346),u=r(95148),p=r(12725),x=r(29742),j=r(37506),g=r(5978),S=e([i,a,n,o,c,l,d,h,u,p,x,j,g]);[i,a,n,o,c,l,d,h,u,p,x,j,g]=S.then?(await S)():S,s()}catch(e){s(e)}})},14078:e=>{e.exports=import("swr")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},22326:e=>{e.exports=require("react-dom")},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},30067:e=>{e.exports=import("@emotion/styled")},33873:e=>{e.exports=require("path")},34486:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>j,default:()=>h,getServerSideProps:()=>x,getStaticPaths:()=>p,getStaticProps:()=>u,reportWebVitals:()=>g,routeModule:()=>y,unstable_getServerProps:()=>k,unstable_getServerSideProps:()=>T,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>m,unstable_getStaticProps:()=>S});var i=r(1292),a=r(58834),n=r(40786),o=r(83567),c=r(8077),l=r(42218),d=e([c,l]);[c,l]=d.then?(await d)():d;let h=(0,n.M)(l,"default"),u=(0,n.M)(l,"getStaticProps"),p=(0,n.M)(l,"getStaticPaths"),x=(0,n.M)(l,"getServerSideProps"),j=(0,n.M)(l,"config"),g=(0,n.M)(l,"reportWebVitals"),S=(0,n.M)(l,"unstable_getStaticProps"),m=(0,n.M)(l,"unstable_getStaticPaths"),b=(0,n.M)(l,"unstable_getStaticParams"),k=(0,n.M)(l,"unstable_getServerProps"),T=(0,n.M)(l,"unstable_getServerSideProps"),y=new i.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/tickets",pathname:"/tickets",bundlePath:"",filename:""},components:{App:c.default,Document:o.default},userland:l});s()}catch(e){s(e)}})},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42218:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>p,getServerSideProps:()=>x});var i=r(8732),a=r(75279),n=r(82015),o=r(81011),c=r(84173),l=r(49535),d=r(15806),h=r(92546),u=e([a,o,c]);function p({isAdmin:e}){let[t,r]=(0,n.useState)([]),[s,d]=(0,n.useState)(!0),{isOpen:h,onOpen:u,onClose:p}=(0,a.useDisclosure)(),x=async()=>{d(!0);try{let e=await fetch("/api/discord/tickets");if(!e.ok)throw Error("Failed to fetch tickets");let t=await e.json();r(t)}catch(e){}finally{d(!1)}};return(0,i.jsx)(o.A,{children:(0,i.jsxs)(a.Container,{maxW:"container.xl",py:8,children:[(0,i.jsxs)(a.VStack,{spacing:8,align:"stretch",children:[(0,i.jsxs)(a.Box,{bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"blue.400",boxShadow:"0 0 15px rgba(66, 153, 225, 0.4)",textAlign:"center",children:[(0,i.jsx)(a.Heading,{size:"2xl",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",mb:4,children:"Support Tickets"}),(0,i.jsx)(a.Text,{color:"gray.300",fontSize:"lg",children:"Open new tickets or review existing ones"})]}),(0,i.jsx)(a.HStack,{justify:"flex-end",children:(0,i.jsx)(a.Button,{leftIcon:(0,i.jsx)(a.Icon,{as:l.GGD}),colorScheme:"blue",onClick:u,children:"Create Ticket"})}),(0,i.jsx)(a.Box,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",rounded:"lg",p:4,children:s?(0,i.jsx)(a.HStack,{justify:"center",py:10,children:(0,i.jsx)(a.Spinner,{size:"lg"})}):0===t.length?(0,i.jsx)(a.Text,{textAlign:"center",py:10,color:"gray.300",children:"You have no tickets yet."}):(0,i.jsxs)(a.Table,{variant:"simple",children:[(0,i.jsx)(a.Thead,{children:(0,i.jsxs)(a.Tr,{children:[(0,i.jsx)(a.Th,{children:"ID"}),(0,i.jsx)(a.Th,{children:"Reason"}),(0,i.jsx)(a.Th,{children:"Status"}),(0,i.jsx)(a.Th,{children:"Created"}),(0,i.jsx)(a.Th,{children:"Actions"})]})}),(0,i.jsx)(a.Tbody,{children:t.map(t=>(0,i.jsxs)(a.Tr,{children:[(0,i.jsx)(a.Td,{children:t._id.toString().slice(-6)}),(0,i.jsx)(a.Td,{maxW:"300px",children:(0,i.jsx)(a.Text,{isTruncated:!0,title:t.reason,children:t.reason||"No reason provided"})}),(0,i.jsx)(a.Td,{children:(0,i.jsx)(a.Badge,{colorScheme:"open"===t.status?"green":"red",children:t.status})}),(0,i.jsx)(a.Td,{children:new Date(t.createdAt).toLocaleString()}),(0,i.jsx)(a.Td,{children:(0,i.jsxs)(a.HStack,{spacing:2,children:[t.discordLink&&(0,i.jsx)(a.Button,{as:"a",href:t.discordLink,target:"_blank",size:"sm",leftIcon:(0,i.jsx)(a.Icon,{as:l.HaR}),children:"Discord"}),"open"===t.status&&(0,i.jsx)(a.Button,{size:"sm",colorScheme:"yellow",onClick:async()=>{if(window.confirm("Close this ticket?"))try{if(!(await fetch(`/api/discord/tickets/${t._id}`,{method:"PATCH"})).ok)throw Error("Failed to close ticket");x()}catch(e){}},children:"Close"}),e&&"closed"===t.status&&(0,i.jsx)(a.Button,{size:"sm",colorScheme:"red",onClick:async()=>{if(window.confirm("Delete this ticket? This is irreversible."))try{if(!(await fetch(`/api/discord/tickets/${t._id}`,{method:"DELETE"})).ok)throw Error("Failed to delete ticket");x()}catch(e){}},children:"Delete"}),"closed"===t.status&&(0,i.jsx)(a.Button,{as:"a",href:`/api/discord/tickets/${t._id}/transcript`,size:"sm",colorScheme:"green",leftIcon:(0,i.jsx)(a.Icon,{as:l.HaR}),target:"_blank",children:"Transcript"})]})})]},t._id))})]})})]}),(0,i.jsx)(c.A,{isOpen:h,onClose:p,onSuccess:x})]})})}[a,o,c]=u.then?(await u)():u;let x=async e=>{let t=await (0,d.getServerSession)(e.req,e.res,h.N);return t?{props:{isAdmin:t.user.isAdmin||!1}}:{redirect:{destination:"/signin",permanent:!1}}};s()}catch(e){s(e)}})},49535:(e,t,r)=>{r.d(t,{GGD:()=>s.FiPlus,HaR:()=>s.FiExternalLink});var s=r(64960)},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},74075:e=>{e.exports=require("zlib")},75279:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{Badge:()=>i.E,Box:()=>a.a,Button:()=>n.$,Container:()=>o.m,HStack:()=>c.z,Heading:()=>l.D,Icon:()=>d.I,Spinner:()=>h.y,Table:()=>u.X,Tbody:()=>p.N,Td:()=>x.Td,Text:()=>j.E,Th:()=>g.Th,Thead:()=>S.d,Tr:()=>m.Tr,VStack:()=>b.T,useDisclosure:()=>k.j});var i=r(25392),a=r(45200),n=r(77502),o=r(64304),c=r(55197),l=r(30519),d=r(50792),h=r(90088),u=r(88468),p=r(46196),x=r(54474),j=r(87378),g=r(29838),S=r(50938),m=r(82548),b=r(17335),k=r(66646);r(9436),r(25035);var T=e([i,a,n,o,c,l,d,h,u,p,x,j,g,S,m,b]);[i,a,n,o,c,l,d,h,u,p,x,j,g,S,m,b]=T.then?(await T)():T,s()}catch(e){s(e)}})},82015:e=>{e.exports=require("react")},84173:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{A:()=>c});var i=r(8732),a=r(11912),n=r(82015),o=e([a]);function c({isOpen:e,onClose:t,onSuccess:r}){let s=(0,a.dj)(),[o,c]=(0,n.useState)(""),[l,d]=(0,n.useState)("support"),[h,u]=(0,n.useState)(!1),p=async()=>{u(!0);try{let e=await fetch("/api/discord/tickets",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({reason:o,category:l})});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to create ticket")}s({title:"Ticket Created",description:"Your support ticket has been opened.",status:"success",duration:3e3}),c(""),d("support"),r(),t()}catch(e){s({title:"Error",description:e.message||"Failed to create ticket",status:"error",duration:5e3})}finally{u(!1)}};return(0,i.jsxs)(a.aF,{isOpen:e,onClose:t,size:"lg",scrollBehavior:"inside",children:[(0,i.jsx)(a.mH,{backdropFilter:"blur(10px)"}),(0,i.jsxs)(a.$m,{bg:"gray.800",children:[(0,i.jsx)(a.rQ,{children:"Create Support Ticket"}),(0,i.jsx)(a.s_,{}),(0,i.jsxs)(a.cw,{children:[(0,i.jsxs)(a.MJ,{mb:4,children:[(0,i.jsx)(a.lR,{children:"Category"}),(0,i.jsxs)(a.l6,{value:l,onChange:e=>d(e.target.value),children:[(0,i.jsx)("option",{value:"support",children:"Support"}),(0,i.jsx)("option",{value:"18plus",children:"18+"}),(0,i.jsx)("option",{value:"other",children:"Other"})]})]}),(0,i.jsxs)(a.MJ,{children:[(0,i.jsx)(a.lR,{children:"Describe your issue"}),(0,i.jsx)(a.TM,{placeholder:"I need help with...",value:o,onChange:e=>c(e.target.value),rows:5})]})]}),(0,i.jsxs)(a.jl,{children:[(0,i.jsx)(a.$n,{mr:3,variant:"ghost",onClick:t,children:"Cancel"}),(0,i.jsx)(a.$n,{colorScheme:"blue",onClick:p,isLoading:h,children:"Create Ticket"})]})]})]})}a=(o.then?(await o)():o)[0],s()}catch(e){s(e)}})},88455:e=>{e.exports=import("@emotion/react")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2457,9784,6021,3786,8740,9498,2142,1283,589,6185,4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(34486));module.exports=s})();