"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[142],{90142:(e,r,s)=>{s.r(r),s.d(r,{default:()=>b});var l=s(94513),o=s(79028),t=s(64349),n=s(1871),i=s(29484),a=s(84748),c=s(5130),d=s(35440),p=s(3037),h=s(88142),x=s(84622);s(75632),s(82273);var u=s(99500),m=s(58686),j=s(38262),f=s(14470);function b(){let{hasAccess:e,isLoading:r,isDeveloper:s,isTester:b}=(0,j.default)(),w=(0,m.useRouter)(),{isOpen:y,onOpen:g,onClose:E}=(0,x.j)(),k=(0,h.dU)("yellow.100","yellow.900"),C=(0,h.dU)("yellow.300","yellow.700"),v=(0,h.dU)("yellow.800","yellow.100");if(r)return(0,l.jsx)(o.a,{p:4,bg:k,borderRadius:"md",borderWidth:"1px",borderColor:C,children:(0,l.jsxs)(n.z,{spacing:3,children:[(0,l.jsx)(i.I,{as:u.Pcn,color:v}),(0,l.jsx)(c.E,{color:v,fontWeight:"medium",children:"Experimental Features"}),(0,l.jsx)(a.y,{size:"sm",color:v})]})});let z=()=>{s?w.push("/admin/experimental-applications"):e?w.push("/experimental"):g()};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(o.a,{p:4,bg:k,borderRadius:"md",borderWidth:"1px",borderColor:C,cursor:"pointer",transition:"all 0.2s",_hover:{transform:"translateY(-2px)",boxShadow:"md"},onClick:z,children:(0,l.jsxs)(p.T,{align:"stretch",spacing:3,children:[(0,l.jsxs)(n.z,{children:[(0,l.jsx)(i.I,{as:u.Pcn,color:v}),(0,l.jsx)(c.E,{color:v,fontWeight:"medium",children:"Experimental Features"}),(0,l.jsx)(d.m,{label:s?"Developer Access":b?"Tester Access":"Applicant",placement:"top",children:(0,l.jsxs)(c.E,{fontSize:"xs",color:v,opacity:.8,children:["(",s?"\uD83D\uDC68‍\uD83D\uDCBB":b?"\uD83E\uDDEA":"\uD83D\uDCE5",")"]})})]}),(0,l.jsx)(c.E,{fontSize:"sm",color:v,opacity:.8,children:"Try out new features that are still in development. These may not work as expected."}),(0,l.jsx)(t.$,{size:"sm",colorScheme:"yellow",variant:"solid",onClick:z,alignSelf:"start",children:s?"Manage Features":e?"Open Features":"Request Access"})]})}),(0,l.jsx)(f.A,{isOpen:y,onClose:E})]})}}}]);