"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9114],{20209:(t,e,n)=>{function r(t,e){if((n=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var n,r=t.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+t.slice(n+1)]}function i(t){return(t=r(Math.abs(t)))?t[1]:NaN}n.d(e,{GP:()=>u,s:()=>l,Gp:()=>f,RT:()=>m,dT:()=>x,Pj:()=>b});var a,o,u,l,s=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function f(t){var e;if(!(e=s.exec(t)))throw Error("invalid format: "+t);return new c({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function c(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function h(t,e){var n=r(t,e);if(!n)return t+"";var i=n[0],a=n[1];return a<0?"0."+Array(-a).join("0")+i:i.length>a+1?i.slice(0,a+1)+"."+i.slice(a+1):i+Array(a-i.length+2).join("0")}f.prototype=c.prototype,c.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let d={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>h(100*t,e),r:h,s:function(t,e){var n=r(t,e);if(!n)return t+"";var i=n[0],o=n[1],u=o-(a=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,l=i.length;return u===l?i:u>l?i+Array(u-l+1).join("0"):u>0?i.slice(0,u)+"."+i.slice(u):"0."+Array(1-u).join("0")+r(t,Math.max(0,e+u-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function p(t){return t}var g=Array.prototype.map,y=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function m(t){return Math.max(0,-i(Math.abs(t)))}function x(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(i(e)/3)))-i(Math.abs(t)))}function b(t,e){return Math.max(0,i(e=Math.abs(e)-(t=Math.abs(t)))-i(t))+1}u=(o=function(t){var e,n,r,o=void 0===t.grouping||void 0===t.thousands?p:(e=g.call(t.grouping,Number),n=t.thousands+"",function(t,r){for(var i=t.length,a=[],o=0,u=e[0],l=0;i>0&&u>0&&(l+u+1>r&&(u=Math.max(1,r-l)),a.push(t.substring(i-=u,i+u)),!((l+=u+1)>r));)u=e[o=(o+1)%e.length];return a.reverse().join(n)}),u=void 0===t.currency?"":t.currency[0]+"",l=void 0===t.currency?"":t.currency[1]+"",s=void 0===t.decimal?".":t.decimal+"",c=void 0===t.numerals?p:(r=g.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return r[+t]})}),h=void 0===t.percent?"%":t.percent+"",m=void 0===t.minus?"−":t.minus+"",x=void 0===t.nan?"NaN":t.nan+"";function b(t){var e=(t=f(t)).fill,n=t.align,r=t.sign,i=t.symbol,p=t.zero,g=t.width,b=t.comma,v=t.precision,_=t.trim,w=t.type;"n"===w?(b=!0,w="g"):d[w]||(void 0===v&&(v=12),_=!0,w="g"),(p||"0"===e&&"="===n)&&(p=!0,e="0",n="=");var M="$"===i?u:"#"===i&&/[boxX]/.test(w)?"0"+w.toLowerCase():"",$="$"===i?l:/[%p]/.test(w)?h:"",k=d[w],N=/[defgprs%]/.test(w);function z(t){var i,u,l,f=M,h=$;if("c"===w)h=k(t)+h,t="";else{var d=(t*=1)<0||1/t<0;if(t=isNaN(t)?x:k(Math.abs(t),v),_&&(t=function(t){t:for(var e,n=t.length,r=1,i=-1;r<n;++r)switch(t[r]){case".":i=e=r;break;case"0":0===i&&(i=r),e=r;break;default:if(!+t[r])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),d&&0==+t&&"+"!==r&&(d=!1),f=(d?"("===r?r:m:"-"===r||"("===r?"":r)+f,h=("s"===w?y[8+a/3]:"")+h+(d&&"("===r?")":""),N){for(i=-1,u=t.length;++i<u;)if(48>(l=t.charCodeAt(i))||l>57){h=(46===l?s+t.slice(i+1):t.slice(i))+h,t=t.slice(0,i);break}}}b&&!p&&(t=o(t,1/0));var z=f.length+t.length+h.length,j=z<g?Array(g-z+1).join(e):"";switch(b&&p&&(t=o(j+t,j.length?g-h.length:1/0),j=""),n){case"<":t=f+t+h+j;break;case"=":t=f+j+t+h;break;case"^":t=j.slice(0,z=j.length>>1)+f+t+h+j.slice(z);break;default:t=j+f+t+h}return c(t)}return v=void 0===v?6:/[gprs]/.test(w)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),z.toString=function(){return t+""},z}return{format:b,formatPrefix:function(t,e){var n=b(((t=f(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(i(e)/3))),a=Math.pow(10,-r),o=y[8+r/3];return function(t){return n(a*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,l=o.formatPrefix},37211:(t,e,n)=>{var r=n(13341),i={"text/plain":"Text","text/html":"Url",default:"Text"};t.exports=function(t,e){var n,a,o,u,l,s,f,c,h=!1;e||(e={}),o=e.debug||!1;try{if(l=r(),s=document.createRange(),f=document.getSelection(),(c=document.createElement("span")).textContent=t,c.ariaHidden="true",c.style.all="unset",c.style.position="fixed",c.style.top=0,c.style.clip="rect(0, 0, 0, 0)",c.style.whiteSpace="pre",c.style.webkitUserSelect="text",c.style.MozUserSelect="text",c.style.msUserSelect="text",c.style.userSelect="text",c.addEventListener("copy",function(n){if(n.stopPropagation(),e.format)if(n.preventDefault(),void 0===n.clipboardData){o&&console.warn("unable to use e.clipboardData"),o&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var r=i[e.format]||i.default;window.clipboardData.setData(r,t)}else n.clipboardData.clearData(),n.clipboardData.setData(e.format,t);e.onCopy&&(n.preventDefault(),e.onCopy(n.clipboardData))}),document.body.appendChild(c),s.selectNodeContents(c),f.addRange(s),!document.execCommand("copy"))throw Error("copy command was unsuccessful");h=!0}catch(r){o&&console.error("unable to copy using execCommand: ",r),o&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(e.format||"text",t),e.onCopy&&e.onCopy(window.clipboardData),h=!0}catch(r){o&&console.error("unable to copy using clipboardData: ",r),o&&console.error("falling back to prompt"),n="message"in e?e.message:"Copy to clipboard: #{key}, Enter",a=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",u=n.replace(/#{\s*key\s*}/g,a),window.prompt(u,t)}}finally{f&&("function"==typeof f.removeRange?f.removeRange(s):f.removeAllRanges()),c&&document.body.removeChild(c),l()}return h}},44546:(t,e,n)=>{function r(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function i(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function a(t){let e,n,a;function u(t,r,i=0,a=t.length){if(i<a){if(0!==e(r,r))return a;do{let e=i+a>>>1;0>n(t[e],r)?i=e+1:a=e}while(i<a)}return i}return 2!==t.length?(e=r,n=(e,n)=>r(t(e),n),a=(e,n)=>t(e)-n):(e=t===r||t===i?t:o,n=t,a=t),{left:u,center:function(t,e,n=0,r=t.length){let i=u(t,e,n,r-1);return i>n&&a(t[i-1],e)>-a(t[i],e)?i-1:i},right:function(t,r,i=0,a=t.length){if(i<a){if(0!==e(r,r))return a;do{let e=i+a>>>1;0>=n(t[e],r)?i=e+1:a=e}while(i<a)}return i}}}function o(){return 0}function u(t){return null===t?NaN:+t}n.d(e,{Bu:()=>y.B,V_:()=>r,h1:()=>f,yl:()=>a,YV:()=>E,Z4:()=>q,y1:()=>A,lq:()=>$,sG:()=>k,Zc:()=>M});let l=a(r),s=l.right;l.left,a(u).center;let f=s;function c(t){return function(e,n,r=n){if(!((n*=1)>=0))throw RangeError("invalid rx");if(!((r*=1)>=0))throw RangeError("invalid ry");let{data:i,width:a,height:o}=e;if(!((a=Math.floor(a))>=0))throw RangeError("invalid width");if(!((o=Math.floor(void 0!==o?o:i.length/a))>=0))throw RangeError("invalid height");if(!a||!o||!n&&!r)return e;let u=n&&t(n),l=r&&t(r),s=i.slice();return u&&l?(h(u,s,i,a,o),h(u,i,s,a,o),h(u,s,i,a,o),d(l,i,s,a,o),d(l,s,i,a,o),d(l,i,s,a,o)):u?(h(u,i,s,a,o),h(u,s,i,a,o),h(u,i,s,a,o)):l&&(d(l,i,s,a,o),d(l,s,i,a,o),d(l,i,s,a,o)),e}}function h(t,e,n,r,i){for(let a=0,o=r*i;a<o;)t(e,n,a,a+=r,1)}function d(t,e,n,r,i){for(let a=0,o=r*i;a<r;++a)t(e,n,a,a+o,r)}function p(t){let e=Math.floor(t);if(e===t){var n=t;let e=2*n+1;return(t,r,i,a,o)=>{if(!((a-=o)>=i))return;let u=n*r[i],l=o*n;for(let t=i,e=i+l;t<e;t+=o)u+=r[Math.min(a,t)];for(let n=i,s=a;n<=s;n+=o)u+=r[Math.min(a,n+l)],t[n]=u/e,u-=r[Math.max(i,n-l)]}}let r=t-e,i=2*t+1;return(t,n,a,o,u)=>{if(!((o-=u)>=a))return;let l=e*n[a],s=u*e,f=s+u;for(let t=a,e=a+s;t<e;t+=u)l+=n[Math.min(o,t)];for(let e=a,c=o;e<=c;e+=u)l+=n[Math.min(o,e+s)],t[e]=(l+r*(n[Math.max(a,e-f)]+n[Math.min(o,e+f)]))/i,l-=n[Math.max(a,e-s)]}}c(p),c(function(t){let e=p(t);return(t,n,r,i,a)=>{e(t,n,(r<<=2)+0,(i<<=2)+0,a<<=2),e(t,n,r+1,i+1,a),e(t,n,r+2,i+2,a),e(t,n,r+3,i+3,a)}});var g,y=n(42354);function m(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}var x=Array.prototype;x.slice,x.map;let b=Math.sqrt(50),v=Math.sqrt(10),_=Math.sqrt(2);function w(t,e,n){let r,i,a,o=(e-t)/Math.max(0,n),u=Math.floor(Math.log10(o)),l=o/Math.pow(10,u),s=l>=b?10:l>=v?5:l>=_?2:1;return(u<0?(r=Math.round(t*(a=Math.pow(10,-u)/s)),i=Math.round(e*a),r/a<t&&++r,i/a>e&&--i,a=-a):(r=Math.round(t/(a=Math.pow(10,u)*s)),i=Math.round(e/a),r*a<t&&++r,i*a>e&&--i),i<r&&.5<=n&&n<2)?w(t,e,2*n):[r,i,a]}function M(t,e,n){if(e*=1,t*=1,!((n*=1)>0))return[];if(t===e)return[t];let r=e<t,[i,a,o]=r?w(e,t,n):w(t,e,n);if(!(a>=i))return[];let u=a-i+1,l=Array(u);if(r)if(o<0)for(let t=0;t<u;++t)l[t]=-((a-t)/o);else for(let t=0;t<u;++t)l[t]=(a-t)*o;else if(o<0)for(let t=0;t<u;++t)l[t]=-((i+t)/o);else for(let t=0;t<u;++t)l[t]=(i+t)*o;return l}function $(t,e,n){return w(t*=1,e*=1,n*=1)[2]}function k(t,e,n){e*=1,t*=1,n*=1;let r=e<t,i=r?$(e,t,n):$(t,e,n);return(r?-1:1)*(i<0?-(1/i):i)}function N(t,e){let n;if(void 0===e)for(let e of t)null!=e&&(n<e||void 0===n&&e>=e)&&(n=e);else{let r=-1;for(let i of t)null!=(i=e(i,++r,t))&&(n<i||void 0===n&&i>=i)&&(n=i)}return n}function z(t,e){let n;if(void 0===e)for(let e of t)null!=e&&(n>e||void 0===n&&e>=e)&&(n=e);else{let r=-1;for(let i of t)null!=(i=e(i,++r,t))&&(n>i||void 0===n&&i>=i)&&(n=i)}return n}function j(t,e,n){let r=t[e];t[e]=t[n],t[n]=r}function E(t,e,n){if(!(!(i=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let n=-1;for(let r of t)null!=(r=e(r,++n,t))&&(r*=1)>=r&&(yield r)}}(t,n))).length)||isNaN(e*=1))){if(e<=0||i<2)return z(t);if(e>=1)return N(t);var i,a=(i-1)*e,o=Math.floor(a),u=N((function t(e,n,i=0,a=1/0,o){if(n=Math.floor(n),i=Math.floor(Math.max(0,i)),a=Math.floor(Math.min(e.length-1,a)),!(i<=n&&n<=a))return e;for(o=void 0===o?m:function(t=r){if(t===r)return m;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,n)=>{let r=t(e,n);return r||0===r?r:(0===t(n,n))-(0===t(e,e))}}(o);a>i;){if(a-i>600){let r=a-i+1,u=n-i+1,l=Math.log(r),s=.5*Math.exp(2*l/3),f=.5*Math.sqrt(l*s*(r-s)/r)*(u-r/2<0?-1:1),c=Math.max(i,Math.floor(n-u*s/r+f)),h=Math.min(a,Math.floor(n+(r-u)*s/r+f));t(e,n,c,h,o)}let r=e[n],u=i,l=a;for(j(e,i,n),o(e[a],r)>0&&j(e,i,a);u<l;){for(j(e,u,l),++u,--l;0>o(e[u],r);)++u;for(;o(e[l],r)>0;)--l}0===o(e[i],r)?j(e,i,l):j(e,++l,a),l<=n&&(i=l+1),n<=l&&(a=l-1)}return e})(t,o).subarray(0,o+1));return u+(z(t.subarray(o+1))-u)*(a-o)}}function q(t,e,n=u){if(!(!(r=t.length)||isNaN(e*=1))){if(e<=0||r<2)return+n(t[0],0,t);if(e>=1)return+n(t[r-1],r-1,t);var r,i=(r-1)*e,a=Math.floor(i),o=+n(t[a],a,t);return o+(n(t[a+1],a+1,t)-o)*(i-a)}}function A(t,e,n){t*=1,e*=1,n=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+n;for(var r=-1,i=0|Math.max(0,Math.ceil((e-t)/n)),a=Array(i);++r<i;)a[r]=t+r*n;return a}g=Math.random},47096:(t,e,n)=>{n.d(e,{J:()=>u});var r={value:()=>{}};function i(){for(var t,e=0,n=arguments.length,r={};e<n;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new a(r)}function a(t){this._=t}function o(t,e,n){for(var i=0,a=t.length;i<a;++i)if(t[i].name===e){t[i]=r,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=n&&t.push({name:e,value:n}),t}a.prototype=i.prototype={constructor:a,on:function(t,e){var n,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:e}}),a=-1,u=i.length;if(arguments.length<2){for(;++a<u;)if((n=(t=i[a]).type)&&(n=function(t,e){for(var n,r=0,i=t.length;r<i;++r)if((n=t[r]).name===e)return n.value}(r[n],t.name)))return n;return}if(null!=e&&"function"!=typeof e)throw Error("invalid callback: "+e);for(;++a<u;)if(n=(t=i[a]).type)r[n]=o(r[n],t.name,e);else if(null==e)for(n in r)r[n]=o(r[n],t.name,null);return this},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new a(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,r,i=Array(n),a=0;a<n;++a)i[a]=arguments[a+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],a=0,n=r.length;a<n;++a)r[a].value.apply(e,i)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,a=r.length;i<a;++i)r[i].value.apply(e,n)}};let u=i},61289:(t,e,n)=>{function r(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}n.d(e,{oR:()=>r}),function t(e){function n(t){return Math.pow(t,e)}return e*=1,n.exponent=t,n}(3),function t(e){function n(t){return 1-Math.pow(1-t,e)}return e*=1,n.exponent=t,n}(3),function t(e){function n(t){return((t*=2)<=1?Math.pow(t,e):2-Math.pow(2-t,e))/2}return e*=1,n.exponent=t,n}(3);var i=Math.PI;function a(t){return(Math.pow(2,-10*t)-9765625e-10)*1.0009775171065494}var o=4/11,u=1/(4/11)/(4/11);(function t(e){function n(t){return(t*=1)*t*(e*(t-1)+t)}return e*=1,n.overshoot=t,n})(1.70158),function t(e){function n(t){return--t*t*((t+1)*e+t)+1}return e*=1,n.overshoot=t,n}(1.70158),function t(e){function n(t){return((t*=2)<1?t*t*((e+1)*t-e):(t-=2)*t*((e+1)*t+e)+2)/2}return e*=1,n.overshoot=t,n}(1.70158);var l=2*Math.PI;(function t(e,n){var r=Math.asin(1/(e=Math.max(1,e)))*(n/=l);function i(t){return e*a(- --t)*Math.sin((r-t)/n)}return i.amplitude=function(e){return t(e,n*l)},i.period=function(n){return t(e,n)},i})(1,.3),function t(e,n){var r=Math.asin(1/(e=Math.max(1,e)))*(n/=l);function i(t){return 1-e*a(t*=1)*Math.sin((t+r)/n)}return i.amplitude=function(e){return t(e,n*l)},i.period=function(n){return t(e,n)},i}(1,.3),function t(e,n){var r=Math.asin(1/(e=Math.max(1,e)))*(n/=l);function i(t){return((t=2*t-1)<0?e*a(-t)*Math.sin((r-t)/n):2-e*a(t)*Math.sin((r+t)/n))/2}return i.amplitude=function(e){return t(e,n*l)},i.period=function(n){return t(e,n)},i}(1,.3)},68056:(t,e,n)=>{function r(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function i(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function a(){}n.d(e,{yW:()=>b,UB:()=>J,aq:()=>Z,KI:()=>S,Qh:()=>M});var o="\\s*([+-]?\\d+)\\s*",u="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",l="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",s=/^#([0-9a-f]{3,8})$/,f=RegExp(`^rgb\\(${o},${o},${o}\\)$`),c=RegExp(`^rgb\\(${l},${l},${l}\\)$`),h=RegExp(`^rgba\\(${o},${o},${o},${u}\\)$`),d=RegExp(`^rgba\\(${l},${l},${l},${u}\\)$`),p=RegExp(`^hsl\\(${u},${l},${l}\\)$`),g=RegExp(`^hsla\\(${u},${l},${l},${u}\\)$`),y={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function m(){return this.rgb().formatHex()}function x(){return this.rgb().formatRgb()}function b(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=s.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?v(e):3===n?new $(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?_(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?_(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=f.exec(t))?new $(e[1],e[2],e[3],1):(e=c.exec(t))?new $(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=h.exec(t))?_(e[1],e[2],e[3],e[4]):(e=d.exec(t))?_(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=p.exec(t))?q(e[1],e[2]/100,e[3]/100,1):(e=g.exec(t))?q(e[1],e[2]/100,e[3]/100,e[4]):y.hasOwnProperty(t)?v(y[t]):"transparent"===t?new $(NaN,NaN,NaN,0):null}function v(t){return new $(t>>16&255,t>>8&255,255&t,1)}function _(t,e,n,r){return r<=0&&(t=e=n=NaN),new $(t,e,n,r)}function w(t){return(t instanceof a||(t=b(t)),t)?new $((t=t.rgb()).r,t.g,t.b,t.opacity):new $}function M(t,e,n,r){return 1==arguments.length?w(t):new $(t,e,n,null==r?1:r)}function $(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function k(){return`#${E(this.r)}${E(this.g)}${E(this.b)}`}function N(){let t=z(this.opacity);return`${1===t?"rgb(":"rgba("}${j(this.r)}, ${j(this.g)}, ${j(this.b)}${1===t?")":`, ${t})`}`}function z(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function j(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function E(t){return((t=j(t))<16?"0":"")+t.toString(16)}function q(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new D(t,e,n,r)}function A(t){if(t instanceof D)return new D(t.h,t.s,t.l,t.opacity);if(t instanceof a||(t=b(t)),!t)return new D;if(t instanceof D)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),o=Math.max(e,n,r),u=NaN,l=o-i,s=(o+i)/2;return l?(u=e===o?(n-r)/l+(n<r)*6:n===o?(r-e)/l+2:(e-n)/l+4,l/=s<.5?o+i:2-o-i,u*=60):l=s>0&&s<1?0:u,new D(u,l,s,t.opacity)}function S(t,e,n,r){return 1==arguments.length?A(t):new D(t,e,n,null==r?1:r)}function D(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function C(t){return(t=(t||0)%360)<0?t+360:t}function R(t){return Math.max(0,Math.min(1,t||0))}function I(t,e,n){return(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)*255}r(a,b,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:m,formatHex:m,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return A(this).formatHsl()},formatRgb:x,toString:x}),r($,M,i(a,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new $(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new $(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new $(j(this.r),j(this.g),j(this.b),z(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:k,formatHex:k,formatHex8:function(){return`#${E(this.r)}${E(this.g)}${E(this.b)}${E((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:N,toString:N})),r(D,S,i(a,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new D(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new D(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new $(I(t>=240?t-240:t+120,i,r),I(t,i,r),I(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new D(C(this.h),R(this.s),R(this.l),z(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=z(this.opacity);return`${1===t?"hsl(":"hsla("}${C(this.h)}, ${100*R(this.s)}%, ${100*R(this.l)}%${1===t?")":`, ${t})`}`}}));let P=Math.PI/180,T=180/Math.PI,X=4/29,L=6/29,H=6/29*3*(6/29),O=6/29*(6/29)*(6/29);function Y(t){if(t instanceof F)return new F(t.l,t.a,t.b,t.opacity);if(t instanceof G)return K(t);t instanceof $||(t=w(t));var e,n,r=W(t.r),i=W(t.g),a=W(t.b),o=U((.2225045*r+.7168786*i+.0606169*a)/1);return r===i&&i===a?e=n=o:(e=U((.4360747*r+.3850649*i+.1430804*a)/.96422),n=U((.0139322*r+.0971045*i+.7141733*a)/.82521)),new F(116*o-16,500*(e-o),200*(o-n),t.opacity)}function F(t,e,n,r){this.l=+t,this.a=+e,this.b=+n,this.opacity=+r}function U(t){return t>O?Math.pow(t,1/3):t/H+X}function B(t){return t>L?t*t*t:H*(t-X)}function Q(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function W(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function Z(t,e,n,r){return 1==arguments.length?function(t){if(t instanceof G)return new G(t.h,t.c,t.l,t.opacity);if(t instanceof F||(t=Y(t)),0===t.a&&0===t.b)return new G(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*T;return new G(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}(t):new G(t,e,n,null==r?1:r)}function G(t,e,n,r){this.h=+t,this.c=+e,this.l=+n,this.opacity=+r}function K(t){if(isNaN(t.h))return new F(t.l,0,0,t.opacity);var e=t.h*P;return new F(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}r(F,function(t,e,n,r){return 1==arguments.length?Y(t):new F(t,e,n,null==r?1:r)},i(a,{brighter(t){return new F(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new F(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,n=isNaN(this.b)?t:t-this.b/200;return e=.96422*B(e),new $(Q(3.1338561*e-1.6168667*(t=+B(t))-.4906146*(n=.82521*B(n))),Q(-.9787684*e+1.9161415*t+.033454*n),Q(.0719453*e-.2289914*t+1.4052427*n),this.opacity)}})),r(G,Z,i(a,{brighter(t){return new G(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new G(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return K(this).rgb()}}));var V=-1.78277*.29227-.1347134789;function J(t,e,n,r){return 1==arguments.length?function(t){if(t instanceof tt)return new tt(t.h,t.s,t.l,t.opacity);t instanceof $||(t=w(t));var e=t.r/255,n=t.g/255,r=t.b/255,i=(V*r+-1.7884503806*e-3.5172982438*n)/(V+-1.7884503806-3.5172982438),a=r-i,o=-((1.97294*(n-i)- -.29227*a)/.90649),u=Math.sqrt(o*o+a*a)/(1.97294*i*(1-i)),l=u?Math.atan2(o,a)*T-120:NaN;return new tt(l<0?l+360:l,u,i,t.opacity)}(t):new tt(t,e,n,null==r?1:r)}function tt(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}r(tt,J,i(a,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*P,e=+this.l,n=isNaN(this.s)?0:this.s*e*(1-e),r=Math.cos(t),i=Math.sin(t);return new $(255*(e+n*(-.14861*r+1.78277*i)),255*(e+n*(-.29227*r+-.90649*i)),255*(e+1.97294*r*n),this.opacity)}}))},71928:(t,e,n)=>{n.d(e,{GW:()=>y,Dj:()=>h,Zr:()=>f,sH:()=>m,zl:()=>g,TE:()=>w,pN:()=>M,p7:()=>k,$B:()=>E});var r,i=n(68056);function a(t,e,n,r,i){var a=t*t,o=a*t;return((1-3*t+3*a-o)*e+(4-6*a+3*o)*n+(1+3*t+3*a-3*o)*r+o*i)/6}let o=t=>()=>t;function u(t,e){return function(n){return t+n*e}}function l(t,e){var n=e-t;return n?u(t,n>180||n<-180?n-360*Math.round(n/360):n):o(isNaN(t)?e:t)}function s(t,e){var n=e-t;return n?u(t,n):o(isNaN(t)?e:t)}let f=function t(e){var n,r=1==(n=+e)?s:function(t,e){var r,i,a;return e-t?(r=t,i=e,r=Math.pow(r,a=n),i=Math.pow(i,a)-r,a=1/a,function(t){return Math.pow(r+t*i,a)}):o(isNaN(t)?e:t)};function a(t,e){var n=r((t=(0,i.Qh)(t)).r,(e=(0,i.Qh)(e)).r),a=r(t.g,e.g),o=r(t.b,e.b),u=s(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=a(e),t.b=o(e),t.opacity=u(e),t+""}}return a.gamma=t,a}(1);function c(t){return function(e){var n,r,a=e.length,o=Array(a),u=Array(a),l=Array(a);for(n=0;n<a;++n)r=(0,i.Qh)(e[n]),o[n]=r.r||0,u[n]=r.g||0,l[n]=r.b||0;return o=t(o),u=t(u),l=t(l),r.opacity=1,function(t){return r.r=o(t),r.g=u(t),r.b=l(t),r+""}}}c(function(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),i=t[r],o=t[r+1],u=r>0?t[r-1]:2*i-o,l=r<e-1?t[r+2]:2*o-i;return a((n-r/e)*e,u,i,o,l)}}),c(function(t){var e=t.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*e),i=t[(r+e-1)%e],o=t[r%e],u=t[(r+1)%e],l=t[(r+2)%e];return a((n-r/e)*e,i,o,u,l)}});function h(t,e){return t*=1,e*=1,function(n){return t*(1-n)+e*n}}var d=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,p=RegExp(d.source,"g");function g(t,e){var n,r,i,a,o,u=d.lastIndex=p.lastIndex=0,l=-1,s=[],f=[];for(t+="",e+="";(i=d.exec(t))&&(a=p.exec(e));)(o=a.index)>u&&(o=e.slice(u,o),s[l]?s[l]+=o:s[++l]=o),(i=i[0])===(a=a[0])?s[l]?s[l]+=a:s[++l]=a:(s[++l]=null,f.push({i:l,x:h(i,a)})),u=p.lastIndex;return u<e.length&&(o=e.slice(u),s[l]?s[l]+=o:s[++l]=o),s.length<2?f[0]?(n=f[0].x,function(t){return n(t)+""}):(r=e,function(){return r}):(e=f.length,function(t){for(var n,r=0;r<e;++r)s[(n=f[r]).i]=n.x(t);return s.join("")})}function y(t,e){var n,r,a=typeof e;return null==e||"boolean"===a?o(e):("number"===a?h:"string"===a?(r=(0,i.yW)(e))?(e=r,f):g:e instanceof i.yW?f:e instanceof Date?function(t,e){var n=new Date;return t*=1,e*=1,function(r){return n.setTime(t*(1-r)+e*r),n}}:!ArrayBuffer.isView(n=e)||n instanceof DataView?Array.isArray(e)?function(t,e){var n,r=e?e.length:0,i=t?Math.min(r,t.length):0,a=Array(i),o=Array(r);for(n=0;n<i;++n)a[n]=y(t[n],e[n]);for(;n<r;++n)o[n]=e[n];return function(t){for(n=0;n<i;++n)o[n]=a[n](t);return o}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var n,r={},i={};for(n in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)n in t?r[n]=y(t[n],e[n]):i[n]=e[n];return function(t){for(n in r)i[n]=r[n](t);return i}}:h:function(t,e){e||(e=[]);var n,r=t?Math.min(e.length,t.length):0,i=e.slice();return function(a){for(n=0;n<r;++n)i[n]=t[n]*(1-a)+e[n]*a;return i}})(t,e)}function m(t,e){return t*=1,e*=1,function(n){return Math.round(t*(1-n)+e*n)}}var x=180/Math.PI,b={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function v(t,e,n,r,i,a){var o,u,l;return(o=Math.sqrt(t*t+e*e))&&(t/=o,e/=o),(l=t*n+e*r)&&(n-=t*l,r-=e*l),(u=Math.sqrt(n*n+r*r))&&(n/=u,r/=u,l/=u),t*r<e*n&&(t=-t,e=-e,l=-l,o=-o),{translateX:i,translateY:a,rotate:Math.atan2(e,t)*x,skewX:Math.atan(l)*x,scaleX:o,scaleY:u}}function _(t,e,n,r){function i(t){return t.length?t.pop()+" ":""}return function(a,o){var u,l,s,f,c=[],d=[];return a=t(a),o=t(o),!function(t,r,i,a,o,u){if(t!==i||r!==a){var l=o.push("translate(",null,e,null,n);u.push({i:l-4,x:h(t,i)},{i:l-2,x:h(r,a)})}else(i||a)&&o.push("translate("+i+e+a+n)}(a.translateX,a.translateY,o.translateX,o.translateY,c,d),u=a.rotate,l=o.rotate,u!==l?(u-l>180?l+=360:l-u>180&&(u+=360),d.push({i:c.push(i(c)+"rotate(",null,r)-2,x:h(u,l)})):l&&c.push(i(c)+"rotate("+l+r),s=a.skewX,f=o.skewX,s!==f?d.push({i:c.push(i(c)+"skewX(",null,r)-2,x:h(s,f)}):f&&c.push(i(c)+"skewX("+f+r),!function(t,e,n,r,a,o){if(t!==n||e!==r){var u=a.push(i(a)+"scale(",null,",",null,")");o.push({i:u-4,x:h(t,n)},{i:u-2,x:h(e,r)})}else(1!==n||1!==r)&&a.push(i(a)+"scale("+n+","+r+")")}(a.scaleX,a.scaleY,o.scaleX,o.scaleY,c,d),a=o=null,function(t){for(var e,n=-1,r=d.length;++n<r;)c[(e=d[n]).i]=e.x(t);return c.join("")}}}var w=_(function(t){let e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?b:v(e.a,e.b,e.c,e.d,e.e,e.f)},"px, ","px)","deg)"),M=_(function(t){return null==t?b:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",t),t=r.transform.baseVal.consolidate())?v((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):b},", ",")",")");function $(t){return((t=Math.exp(t))+1/t)/2}let k=function t(e,n,r){function i(t,i){var a,o,u=t[0],l=t[1],s=t[2],f=i[0],c=i[1],h=i[2],d=f-u,p=c-l,g=d*d+p*p;if(g<1e-12)o=Math.log(h/s)/e,a=function(t){return[u+t*d,l+t*p,s*Math.exp(e*t*o)]};else{var y=Math.sqrt(g),m=(h*h-s*s+r*g)/(2*s*n*y),x=(h*h-s*s-r*g)/(2*h*n*y),b=Math.log(Math.sqrt(m*m+1)-m);o=(Math.log(Math.sqrt(x*x+1)-x)-b)/e,a=function(t){var r,i,a=t*o,f=$(b),c=s/(n*y)*(f*(((r=Math.exp(2*(r=e*a+b)))-1)/(r+1))-((i=Math.exp(i=b))-1/i)/2);return[u+c*d,l+c*p,s*f/$(e*a+b)]}}return a.duration=1e3*o*e/Math.SQRT2,a}return i.rho=function(e){var n=Math.max(.001,+e),r=n*n;return t(n,r,r*r)},i}(Math.SQRT2,2,4);function N(t){return function(e,n){var r=t((e=(0,i.KI)(e)).h,(n=(0,i.KI)(n)).h),a=s(e.s,n.s),o=s(e.l,n.l),u=s(e.opacity,n.opacity);return function(t){return e.h=r(t),e.s=a(t),e.l=o(t),e.opacity=u(t),e+""}}}function z(t){return function(e,n){var r=t((e=(0,i.aq)(e)).h,(n=(0,i.aq)(n)).h),a=s(e.c,n.c),o=s(e.l,n.l),u=s(e.opacity,n.opacity);return function(t){return e.h=r(t),e.c=a(t),e.l=o(t),e.opacity=u(t),e+""}}}function j(t){return function e(n){function r(e,r){var a=t((e=(0,i.UB)(e)).h,(r=(0,i.UB)(r)).h),o=s(e.s,r.s),u=s(e.l,r.l),l=s(e.opacity,r.opacity);return function(t){return e.h=a(t),e.s=o(t),e.l=u(Math.pow(t,n)),e.opacity=l(t),e+""}}return n*=1,r.gamma=e,r}(1)}function E(t,e){void 0===e&&(e=t,t=y);for(var n=0,r=e.length-1,i=e[0],a=Array(r<0?0:r);n<r;)a[n]=t(i,i=e[++n]);return function(t){var e=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return a[e](t-e)}}N(l),N(s),z(l),z(s),j(l),j(s)},86423:(t,e,n)=>{n.d(e,{$E:()=>m,XD:()=>s,EH:()=>f});var r=n(47096),i=n(96472);let a={passive:!1},o={capture:!0,passive:!1};function u(t){t.stopImmediatePropagation()}function l(t){t.preventDefault(),t.stopImmediatePropagation()}function s(t){var e=t.document.documentElement,n=(0,i.Lt)(t).on("dragstart.drag",l,o);"onselectstart"in e?n.on("selectstart.drag",l,o):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}function f(t,e){var n=t.document.documentElement,r=(0,i.Lt)(t).on("dragstart.drag",null);e&&(r.on("click.drag",l,o),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}let c=t=>()=>t;function h(t,{sourceEvent:e,subject:n,target:r,identifier:i,active:a,x:o,y:u,dx:l,dy:s,dispatch:f}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:a,enumerable:!0,configurable:!0},x:{value:o,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:s,enumerable:!0,configurable:!0},_:{value:f}})}function d(t){return!t.ctrlKey&&!t.button}function p(){return this.parentNode}function g(t,e){return null==e?{x:t.x,y:t.y}:e}function y(){return navigator.maxTouchPoints||"ontouchstart"in this}function m(){var t,e,n,m,x=d,b=p,v=g,_=y,w={},M=(0,r.J)("start","drag","end"),$=0,k=0;function N(t){t.on("mousedown.drag",z).filter(_).on("touchstart.drag",q).on("touchmove.drag",A,a).on("touchend.drag touchcancel.drag",S).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function z(r,a){if(!m&&x.call(this,r,a)){var l=D(this,b.call(this,r,a),r,a,"mouse");l&&((0,i.Lt)(r.view).on("mousemove.drag",j,o).on("mouseup.drag",E,o),s(r.view),u(r),n=!1,t=r.clientX,e=r.clientY,l("start",r))}}function j(r){if(l(r),!n){var i=r.clientX-t,a=r.clientY-e;n=i*i+a*a>k}w.mouse("drag",r)}function E(t){(0,i.Lt)(t.view).on("mousemove.drag mouseup.drag",null),f(t.view,n),l(t),w.mouse("end",t)}function q(t,e){if(x.call(this,t,e)){var n,r,i=t.changedTouches,a=b.call(this,t,e),o=i.length;for(n=0;n<o;++n)(r=D(this,a,t,e,i[n].identifier,i[n]))&&(u(t),r("start",t,i[n]))}}function A(t){var e,n,r=t.changedTouches,i=r.length;for(e=0;e<i;++e)(n=w[r[e].identifier])&&(l(t),n("drag",t,r[e]))}function S(t){var e,n,r=t.changedTouches,i=r.length;for(m&&clearTimeout(m),m=setTimeout(function(){m=null},500),e=0;e<i;++e)(n=w[r[e].identifier])&&(u(t),n("end",t,r[e]))}function D(t,e,n,r,a,o){var u,l,s,f=M.copy(),c=(0,i.Wn)(o||n,e);if(null!=(s=v.call(t,new h("beforestart",{sourceEvent:n,target:N,identifier:a,active:$,x:c[0],y:c[1],dx:0,dy:0,dispatch:f}),r)))return u=s.x-c[0]||0,l=s.y-c[1]||0,function n(o,d,p){var g,y=c;switch(o){case"start":w[a]=n,g=$++;break;case"end":delete w[a],--$;case"drag":c=(0,i.Wn)(p||d,e),g=$}f.call(o,t,new h(o,{sourceEvent:d,subject:s,target:N,identifier:a,active:g,x:c[0]+u,y:c[1]+l,dx:c[0]-y[0],dy:c[1]-y[1],dispatch:f}),r)}}return N.filter=function(t){return arguments.length?(x="function"==typeof t?t:c(!!t),N):x},N.container=function(t){return arguments.length?(b="function"==typeof t?t:c(t),N):b},N.subject=function(t){return arguments.length?(v="function"==typeof t?t:c(t),N):v},N.touchable=function(t){return arguments.length?(_="function"==typeof t?t:c(!!t),N):_},N.on=function(){var t=M.on.apply(M,arguments);return t===M?N:t},N.clickDistance=function(t){return arguments.length?(k=(t*=1)*t,N):Math.sqrt(k)},N}h.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t}},87606:(t,e,n)=>{n.d(e,{wA:()=>u});let r=Math.PI,i=2*r,a=i-1e-6;function o(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=arguments[e]+t[e]}class u{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?o:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return o;let n=10**e;return function(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=Math.round(arguments[e]*n)/n+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,n,r){this._append`Q${+t},${+e},${this._x1=+n},${this._y1=+r}`}bezierCurveTo(t,e,n,r,i,a){this._append`C${+t},${+e},${+n},${+r},${this._x1=+i},${this._y1=+a}`}arcTo(t,e,n,i,a){if(t*=1,e*=1,n*=1,i*=1,(a*=1)<0)throw Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,l=n-t,s=i-e,f=o-t,c=u-e,h=f*f+c*c;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(h>1e-6)if(Math.abs(c*l-s*f)>1e-6&&a){let d=n-o,p=i-u,g=l*l+s*s,y=Math.sqrt(g),m=Math.sqrt(h),x=a*Math.tan((r-Math.acos((g+h-(d*d+p*p))/(2*y*m)))/2),b=x/m,v=x/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*f},${e+b*c}`,this._append`A${a},${a},0,0,${+(c*d>f*p)},${this._x1=t+v*l},${this._y1=e+v*s}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,n,o,u,l){if(t*=1,e*=1,n*=1,l=!!l,n<0)throw Error(`negative radius: ${n}`);let s=n*Math.cos(o),f=n*Math.sin(o),c=t+s,h=e+f,d=1^l,p=l?o-u:u-o;null===this._x1?this._append`M${c},${h}`:(Math.abs(this._x1-c)>1e-6||Math.abs(this._y1-h)>1e-6)&&this._append`L${c},${h}`,n&&(p<0&&(p=p%i+i),p>a?this._append`A${n},${n},0,1,${d},${t-s},${e-f}A${n},${n},0,1,${d},${this._x1=c},${this._y1=h}`:p>1e-6&&this._append`A${n},${n},0,${+(p>=r)},${d},${this._x1=t+n*Math.cos(u)},${this._y1=e+n*Math.sin(u)}`)}rect(t,e,n,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${n*=1}v${+r}h${-n}Z`}toString(){return this._}}u.prototype},90413:(t,e,n)=>{function r(t,e,n){return Math.min(Math.max(t,n),e)}n.d(e,{Du:()=>a,No:()=>y,nj:()=>m});class i extends Error{constructor(t){super(`Failed to parse color: "${t}"`)}}function a(t){if("string"!=typeof t)throw new i(t);if("transparent"===t.trim().toLowerCase())return[0,0,0,0];let e=t.trim();e=d.test(t)?function(t){let e=u[function(t){let e=5381,n=t.length;for(;n;)e=33*e^t.charCodeAt(--n);return(e>>>0)%2341}(t.toLowerCase().trim())];if(!e)throw new i(t);return`#${e}`}(t):t;let n=s.exec(e);if(n){let t=Array.from(n).slice(1);return[...t.slice(0,3).map(t=>parseInt(l(t,2),16)),parseInt(l(t[3]||"f",2),16)/255]}let a=f.exec(e);if(a){let t=Array.from(a).slice(1);return[...t.slice(0,3).map(t=>parseInt(t,16)),parseInt(t[3]||"ff",16)/255]}let o=c.exec(e);if(o){let t=Array.from(o).slice(1);return[...t.slice(0,3).map(t=>parseInt(t,10)),parseFloat(t[3]||"1")]}let p=h.exec(e);if(p){let[e,n,a,o]=Array.from(p).slice(1).map(parseFloat);if(r(0,100,n)!==n||r(0,100,a)!==a)throw new i(t);return[...g(e,n,a),Number.isNaN(o)?1:o]}throw new i(t)}let o=t=>parseInt(t.replace(/_/g,""),36),u="1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm".split(" ").reduce((t,e)=>{let n=o(e.substring(0,3)),r=o(e.substring(3)).toString(16),i="";for(let t=0;t<6-r.length;t++)i+="0";return t[n]=`${i}${r}`,t},{}),l=(t,e)=>Array.from(Array(e)).map(()=>t).join(""),s=RegExp(`^#${l("([a-f0-9])",3)}([a-f0-9])?$`,"i"),f=RegExp(`^#${l("([a-f0-9]{2})",3)}([a-f0-9]{2})?$`,"i"),c=RegExp(`^rgba?\\(\\s*(\\d+)\\s*${l(",\\s*(\\d+)\\s*",2)}(?:,\\s*([\\d.]+))?\\s*\\)$`,"i"),h=/^hsla?\(\s*([\d.]+)\s*,\s*([\d.]+)%\s*,\s*([\d.]+)%(?:\s*,\s*([\d.]+))?\s*\)$/i,d=/^[a-z]+$/i,p=t=>Math.round(255*t),g=(t,e,n)=>{let r=n/100;if(0===e)return[r,r,r].map(p);let i=(t%360+360)%360/60,a=e/100*(1-Math.abs(2*r-1)),o=a*(1-Math.abs(i%2-1)),u=0,l=0,s=0;i>=0&&i<1?(u=a,l=o):i>=1&&i<2?(u=o,l=a):i>=2&&i<3?(l=a,s=o):i>=3&&i<4?(l=o,s=a):i>=4&&i<5?(u=o,s=a):i>=5&&i<6&&(u=a,s=o);let f=r-a/2;return[u+f,l+f,s+f].map(p)};function y(t,e){var n,i,o,u;let[l,s,f,c]=a(t);return n=l,i=s,o=f,u=c-e,`rgba(${r(0,255,n).toFixed()}, ${r(0,255,i).toFixed()}, ${r(0,255,o).toFixed()}, ${parseFloat(r(0,1,u).toFixed(3))})`}function m(t){let[e,n,i,o]=a(t),u=t=>{let e=r(0,255,t).toString(16);return 1===e.length?`0${e}`:e};return`#${u(e)}${u(n)}${u(i)}${o<1?u(Math.round(255*o)):""}`}}}]);