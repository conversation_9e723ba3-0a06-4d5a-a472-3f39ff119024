"use strict";exports.id=26,exports.ids=[26],exports.modules={68515:(e,o,r)=>{r.a(e,async(e,s)=>{try{r.d(o,{A:()=>j});var t=r(8732),l=r(82015),n=r(66909),a=r(93161),i=r(64641),c=r(3001),d=e([n,a,c]);[n,a,c]=d.then?(await d)():d;let x={user:[{name:"{user.id}",description:"User ID for authentication",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username for requests",icon:"\uD83D\uDC64"},{name:"{user.token}",description:"User auth token",icon:"\uD83D\uDD11"},{name:"{user.email}",description:"User email address",icon:"\uD83D\uDCE7"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDFE0"},{name:"{server.name}",description:"Server name",icon:"\uD83D\uDCDD"},{name:"{server.memberCount}",description:"Member count",icon:"\uD83D\uDC65"},{name:"{server.region}",description:"Server region",icon:"\uD83C\uDF0D"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83D\uDCAC"},{name:"{message.content}",description:"Message content",icon:"\uD83D\uDCDD"},{name:"{message.channelId}",description:"Channel ID",icon:"\uD83D\uDCFA"},{name:"{message.authorId}",description:"Author ID",icon:"\uD83D\uDC64"}],response:[{name:"{response.data}",description:"Full response data",icon:"\uD83D\uDCCA"},{name:"{response.status}",description:"HTTP status code",icon:"\uD83D\uDD22"},{name:"{response.headers}",description:"Response headers",icon:"\uD83D\uDCCB"},{name:"{response.error}",description:"Error message if failed",icon:"❌"}],time:[{name:"{time.now}",description:"Current timestamp",icon:"⏰"},{name:"{time.iso}",description:"ISO timestamp",icon:"\uD83D\uDCC5"},{name:"{time.unix}",description:"Unix timestamp",icon:"\uD83D\uDD50"}],random:[{name:"{random.uuid}",description:"Random UUID",icon:"\uD83C\uDFB2"},{name:"{random.number}",description:"Random number",icon:"\uD83D\uDD22"},{name:"{random.string}",description:"Random string",icon:"\uD83D\uDD24"}]},u=[{value:"GET",label:"GET",description:"Retrieve data from server",color:"green"},{value:"POST",label:"POST",description:"Send data to create new resource",color:"blue"},{value:"PUT",label:"PUT",description:"Update existing resource",color:"orange"},{value:"PATCH",label:"PATCH",description:"Partially update resource",color:"yellow"},{value:"DELETE",label:"DELETE",description:"Remove resource from server",color:"red"},{value:"HEAD",label:"HEAD",description:"Get headers only",color:"purple"},{value:"OPTIONS",label:"OPTIONS",description:"Get allowed methods",color:"gray"}],h=[{value:"json",label:"JSON",description:"JavaScript Object Notation"},{value:"form",label:"Form Data",description:"URL-encoded form data"},{value:"text",label:"Plain Text",description:"Raw text content"},{value:"xml",label:"XML",description:"Extensible Markup Language"}],p=[{value:"ignore",label:"Ignore Errors",description:"Continue flow on API errors"},{value:"log",label:"Log Errors",description:"Log errors but continue"},{value:"throw",label:"Throw Errors",description:"Stop flow on API errors"},{value:"retry",label:"Retry on Error",description:"Retry failed requests"}],m=[{key:"Authorization",value:"Bearer {user.token}"},{key:"Content-Type",value:"application/json"},{key:"User-Agent",value:"Discord Bot API Client"},{key:"Accept",value:"application/json"},{key:"X-API-Key",value:"{api.key}"}],b=(0,l.memo)(({data:e,selected:o,id:r,updateNodeData:s})=>{let{currentScheme:d}=(0,c.DP)(),{isOpen:b,onOpen:j,onClose:g}=(0,a.useDisclosure)(),S=(0,a.useToast)(),[f,y]=(0,l.useState)(()=>({method:"GET",headers:[],bodyType:"json",timeout:5e3,errorHandling:"log",retryCount:0,retryDelay:1e3,followRedirects:!0,validateSSL:!0,...e})),[T,v]=(0,l.useState)(!1),[C,k]=(0,l.useState)(null),[z,I]=(0,l.useState)(null),[w,A]=(0,l.useState)([]),[R,P]=(0,l.useState)(!1),H=e=>{y(o=>({...o,...e}))},D=e=>{navigator.clipboard.writeText(e),S({title:"Copied!",description:`Variable ${e} copied to clipboard`,status:"success",duration:2e3,isClosable:!0})},B=async()=>{if(!f.url){I("Please enter a URL first"),S({title:"Test Failed",description:"Please enter a URL first",status:"error",duration:3e3,isClosable:!0});return}v(!0),I(null),k(null);try{let e,o={};f.headers?.forEach(e=>{e.key&&e.value&&(o[e.key]=e.value)}),f.body&&("POST"===f.method||"PUT"===f.method||"PATCH"===f.method)&&("json"===f.bodyType?o["Content-Type"]="application/json":"form"===f.bodyType?o["Content-Type"]="application/x-www-form-urlencoded":"xml"===f.bodyType&&(o["Content-Type"]="application/xml"));let r={method:f.method||"GET",headers:o};if(f.body&&("POST"===f.method||"PUT"===f.method||"PATCH"===f.method))if("json"===f.bodyType)try{JSON.parse(f.body),r.body=f.body}catch(e){throw Error("Invalid JSON in request body")}else r.body=f.body;let s=await fetch(f.url,r),t=await s.text();try{e=JSON.parse(t)}catch(o){e=t}if(!s.ok)throw Error(`HTTP ${s.status}: ${s.statusText}`);k({status:s.status,statusText:s.statusText,headers:Object.fromEntries(s.headers.entries()),data:e});let l=F(e);A(l),S({title:"API Test Successful!",description:`Request completed with status ${s.status}`,status:"success",duration:3e3,isClosable:!0})}catch(o){let e=o instanceof Error?o.message:"Request failed";I(e),S({title:"API Test Failed",description:e,status:"error",duration:5e3,isClosable:!0})}finally{v(!1)}},F=(e,o="")=>{let r=[];return e&&"object"==typeof e&&(Array.isArray(e)?e.forEach((e,s)=>{let t=o?`${o}.${s}`:`${s}`;r.push(t),"object"==typeof e&&null!==e&&r.push(...F(e,t))}):Object.keys(e).forEach(s=>{let t=o?`${o}.${s}`:s;r.push(t),"object"==typeof e[s]&&null!==e[s]&&r.push(...F(e[s],t))})),r},L=(e,o,r)=>{let s=[...f.headers||[]];s[e][o]=r,H({headers:s})},V=e=>{let o=(f.headers||[]).filter((o,r)=>r!==e);H({headers:o})},E=e=>{let o=[...f.headers||[],e];H({headers:o})};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(a.Box,{bg:d.colors.surface,border:`2px solid ${o?"#06b6d4":d.colors.border}`,borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,t.jsx)(n.Handle,{type:"target",position:n.Position.Top,style:{background:"#06b6d4",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,t.jsxs)(a.VStack,{spacing:1,align:"stretch",children:[(0,t.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,t.jsxs)(a.HStack,{spacing:1,children:[(0,t.jsx)(a.Box,{bg:"teal.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,t.jsx)(i.VeH,{})}),(0,t.jsx)(a.Text,{fontSize:"xs",fontWeight:"bold",color:d.colors.text,children:"API Request"})]}),(0,t.jsx)(a.IconButton,{icon:(0,t.jsx)(i.VSk,{}),size:"xs",variant:"ghost",onClick:j,"aria-label":"Configure API request"})]}),(0,t.jsx)(a.Box,{children:(0,t.jsxs)(a.HStack,{spacing:1,children:[f.method&&(0,t.jsx)(a.Text,{fontSize:"xs",children:(e=>{switch(e){case"GET":return"\uD83D\uDCE5";case"POST":return"\uD83D\uDCE4";case"PUT":return"\uD83D\uDD04";case"PATCH":return"✏️";case"DELETE":return"\uD83D\uDDD1️";default:return"\uD83C\uDF10"}})(f.method)}),(0,t.jsxs)(a.Text,{fontSize:"xs",color:d.colors.text,noOfLines:1,children:[f.method||"GET"," Request"]})]})}),f.url&&(0,t.jsx)(a.Box,{children:(0,t.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,noOfLines:1,children:f.url.length>25?f.url.substring(0,25)+"...":f.url})}),(0,t.jsxs)(a.HStack,{spacing:1,flexWrap:"wrap",children:[f.method&&(0,t.jsx)(a.Badge,{size:"xs",colorScheme:(e=>{let o=u.find(o=>o.value===e);return o?.color||"gray"})(f.method),children:f.method}),(f.headers?.length??0)>0&&(0,t.jsxs)(a.Badge,{size:"xs",colorScheme:"blue",children:[f.headers?.length," header",(f.headers?.length??0)!==1?"s":""]}),f.saveToVariable&&(0,t.jsx)(a.Badge,{size:"xs",colorScheme:"green",children:"Saves Data"})]})]}),(0,t.jsx)(n.Handle,{type:"source",position:n.Position.Bottom,style:{background:"#06b6d4",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,t.jsxs)(a.Modal,{isOpen:b,onClose:()=>{s&&r&&s(r,f),g()},size:"6xl",children:[(0,t.jsx)(a.ModalOverlay,{bg:"blackAlpha.600"}),(0,t.jsxs)(a.ModalContent,{bg:d.colors.background,border:"2px solid",borderColor:"teal.400",maxW:"1400px",children:[(0,t.jsx)(a.ModalHeader,{color:d.colors.text,children:"\uD83C\uDF10 Configure API Request"}),(0,t.jsx)(a.ModalCloseButton,{}),(0,t.jsx)(a.ModalBody,{pb:6,children:(0,t.jsxs)(a.VStack,{spacing:6,align:"stretch",children:[(0,t.jsxs)(a.Box,{children:[(0,t.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,t.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Available Variables"}),(0,t.jsxs)(a.Button,{size:"sm",variant:"ghost",leftIcon:R?(0,t.jsx)(i._NO,{}):(0,t.jsx)(i.Vap,{}),onClick:()=>P(!R),children:[R?"Hide":"Show"," Variables"]})]}),(0,t.jsxs)(a.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,t.jsx)(a.AlertIcon,{}),(0,t.jsx)(a.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your API requests! Click any variable below to copy it. Variables are replaced with actual values when the request executes."})]}),(0,t.jsx)(a.Collapse,{in:R,animateOpacity:!0,children:(0,t.jsx)(a.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,t.jsx)(a.Accordion,{allowMultiple:!0,children:Object.entries(x).map(([e,o])=>(0,t.jsxs)(a.AccordionItem,{border:"none",children:[(0,t.jsxs)(a.AccordionButton,{px:0,py:2,children:[(0,t.jsx)(a.Box,{flex:"1",textAlign:"left",children:(0,t.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,textTransform:"capitalize",children:[e," Variables"]})}),(0,t.jsx)(a.AccordionIcon,{})]}),(0,t.jsx)(a.AccordionPanel,{px:0,py:2,children:(0,t.jsx)(a.VStack,{spacing:2,align:"stretch",children:o.map(e=>(0,t.jsxs)(a.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>D(e.name),children:[(0,t.jsx)(a.Text,{fontSize:"sm",children:e.icon}),(0,t.jsx)(a.Code,{fontSize:"xs",colorScheme:"teal",children:e.name}),(0,t.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,flex:"1",children:e.description}),(0,t.jsx)(a.IconButton,{icon:(0,t.jsx)(i.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),D(e.name)}})]},e.name))})})]},e))})})})]}),(0,t.jsx)(a.Divider,{}),(0,t.jsxs)(a.Tabs,{variant:"enclosed",colorScheme:"teal",children:[(0,t.jsxs)(a.TabList,{children:[(0,t.jsx)(a.Tab,{children:"Request"}),(0,t.jsx)(a.Tab,{children:"Headers"}),(0,t.jsx)(a.Tab,{children:"Body"}),(0,t.jsx)(a.Tab,{children:"Settings"}),(0,t.jsx)(a.Tab,{children:"Test"})]}),(0,t.jsxs)(a.TabPanels,{children:[(0,t.jsx)(a.TabPanel,{children:(0,t.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,t.jsxs)(a.FormControl,{isRequired:!0,children:[(0,t.jsx)(a.FormLabel,{color:d.colors.text,children:"Request URL"}),(0,t.jsx)(a.Input,{value:f.url||"",onChange:e=>H({url:e.target.value}),placeholder:"https://api.example.com/data or {server.webhook.url}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]}),(0,t.jsxs)(a.FormControl,{children:[(0,t.jsx)(a.FormLabel,{color:d.colors.text,children:"HTTP Method"}),(0,t.jsx)(a.Select,{value:f.method||"GET",onChange:e=>H({method:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:u.map(e=>(0,t.jsxs)("option",{value:e.value,children:[e.label," - ",e.description]},e.value))})]}),(0,t.jsxs)(a.FormControl,{children:[(0,t.jsx)(a.FormLabel,{color:d.colors.text,children:"Save Response To Variable"}),(0,t.jsx)(a.Input,{value:f.saveToVariable||"",onChange:e=>H({saveToVariable:e.target.value}),placeholder:"response_data (access with {response_data.field})",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,t.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"Variable name to store the API response. Leave empty if you don't need the response."})]}),(0,t.jsxs)(a.FormControl,{children:[(0,t.jsx)(a.FormLabel,{color:d.colors.text,children:"Description"}),(0,t.jsx)(a.Textarea,{value:f.description||"",onChange:e=>H({description:e.target.value}),placeholder:"Describe what this API request does",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"})]})]})}),(0,t.jsx)(a.TabPanel,{children:(0,t.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,t.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,t.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Request Headers"}),(0,t.jsx)(a.Button,{leftIcon:(0,t.jsx)(i.GGD,{}),onClick:()=>{H({headers:[...f.headers||[],{key:"",value:""}]})},colorScheme:"teal",size:"sm",children:"Add Header"})]}),(0,t.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,t.jsx)(a.AlertIcon,{}),(0,t.jsx)(a.AlertDescription,{fontSize:"sm",children:"Headers provide additional information about the request. Common headers are automatically set based on content type."})]}),(0,t.jsxs)(a.Box,{children:[(0,t.jsx)(a.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,mb:2,children:"Quick Add Common Headers:"}),(0,t.jsx)(a.Wrap,{spacing:2,children:m.map((e,o)=>(0,t.jsx)(a.WrapItem,{children:(0,t.jsx)(a.Button,{size:"sm",variant:"outline",onClick:()=>E(e),leftIcon:(0,t.jsx)(i.GGD,{}),children:e.key})},o))})]}),(0,t.jsxs)(a.VStack,{spacing:3,align:"stretch",children:[f.headers?.map((e,o)=>(0,t.jsxs)(a.Box,{p:3,bg:d.colors.surface,borderRadius:"md",border:"1px solid",borderColor:d.colors.border,children:[(0,t.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,t.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:["Header ",o+1]}),(0,t.jsx)(a.IconButton,{icon:(0,t.jsx)(i.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>V(o),"aria-label":"Remove header"})]}),(0,t.jsxs)(a.SimpleGrid,{columns:2,spacing:3,children:[(0,t.jsxs)(a.FormControl,{children:[(0,t.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Header Name"}),(0,t.jsx)(a.Input,{value:e.key,onChange:e=>L(o,"key",e.target.value),placeholder:"Authorization, Content-Type, etc.",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]}),(0,t.jsxs)(a.FormControl,{children:[(0,t.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Header Value"}),(0,t.jsx)(a.Input,{value:e.value,onChange:e=>L(o,"value",e.target.value),placeholder:"Bearer {user.token}, application/json, etc.",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]})]},o)),(!f.headers||0===f.headers.length)&&(0,t.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,t.jsx)(a.AlertIcon,{}),(0,t.jsx)(a.AlertDescription,{children:"No custom headers configured. Default headers will be set automatically based on request type."})]})]})]})}),(0,t.jsx)(a.TabPanel,{children:(0,t.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,t.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,t.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Request Body"}),(0,t.jsx)(a.Text,{fontSize:"sm",color:d.colors.textSecondary,children:"Only used for POST, PUT, PATCH requests"})]}),(0,t.jsxs)(a.FormControl,{children:[(0,t.jsx)(a.FormLabel,{color:d.colors.text,children:"Body Type"}),(0,t.jsx)(a.Select,{value:f.bodyType||"json",onChange:e=>H({bodyType:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:h.map(e=>(0,t.jsxs)("option",{value:e.value,children:[e.label," - ",e.description]},e.value))})]}),(0,t.jsxs)(a.FormControl,{children:[(0,t.jsx)(a.FormLabel,{color:d.colors.text,children:"Request Body"}),(0,t.jsx)(a.Textarea,{value:f.body||"",onChange:e=>H({body:e.target.value}),placeholder:"json"===f.bodyType?'{"key": "value", "user": "{user.id}"}':"form"===f.bodyType?"key=value&user={user.id}":"Raw text content with {variables}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"200px",fontFamily:"monospace",fontSize:"sm"}),(0,t.jsxs)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:["json"===f.bodyType&&"Must be valid JSON format","form"===f.bodyType&&"Use key=value&key2=value2 format","text"===f.bodyType&&"Plain text content"]})]}),(0,t.jsxs)(a.Alert,{status:"warning",borderRadius:"md",children:[(0,t.jsx)(a.AlertIcon,{}),(0,t.jsxs)(a.Box,{children:[(0,t.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:"\uD83D\uDCA1 Body Examples:"}),(0,t.jsxs)(a.VStack,{align:"start",spacing:1,fontSize:"sm",fontFamily:"monospace",children:[(0,t.jsxs)(a.Text,{children:["JSON: ",'{"message": "{message.content}", "user_id": "{user.id}"}']}),(0,t.jsxs)(a.Text,{children:["Form: user_id=","{user.id}","&message=","{message.content}"]}),(0,t.jsxs)(a.Text,{children:["Text: User ","{user.username}"," said: ","{message.content}"]})]})]})]})]})}),(0,t.jsx)(a.TabPanel,{children:(0,t.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,t.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Advanced Settings"}),(0,t.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,t.jsxs)(a.FormControl,{children:[(0,t.jsx)(a.FormLabel,{color:d.colors.text,children:"Timeout (milliseconds)"}),(0,t.jsxs)(a.NumberInput,{value:f.timeout||5e3,onChange:e=>H({timeout:parseInt(e)||5e3}),min:1e3,max:6e4,children:[(0,t.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,t.jsxs)(a.NumberInputStepper,{children:[(0,t.jsx)(a.NumberIncrementStepper,{}),(0,t.jsx)(a.NumberDecrementStepper,{})]})]}),(0,t.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"Maximum time to wait for response (5000ms = 5 seconds)"})]}),(0,t.jsxs)(a.FormControl,{children:[(0,t.jsx)(a.FormLabel,{color:d.colors.text,children:"Error Handling"}),(0,t.jsx)(a.Select,{value:f.errorHandling||"log",onChange:e=>H({errorHandling:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:p.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,t.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:p.find(e=>e.value===f.errorHandling)?.description})]})]}),"retry"===f.errorHandling&&(0,t.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,t.jsxs)(a.FormControl,{children:[(0,t.jsx)(a.FormLabel,{color:d.colors.text,children:"Retry Count"}),(0,t.jsxs)(a.NumberInput,{value:f.retryCount||0,onChange:e=>H({retryCount:parseInt(e)||0}),min:0,max:5,children:[(0,t.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,t.jsxs)(a.NumberInputStepper,{children:[(0,t.jsx)(a.NumberIncrementStepper,{}),(0,t.jsx)(a.NumberDecrementStepper,{})]})]})]}),(0,t.jsxs)(a.FormControl,{children:[(0,t.jsx)(a.FormLabel,{color:d.colors.text,children:"Retry Delay (ms)"}),(0,t.jsxs)(a.NumberInput,{value:f.retryDelay||1e3,onChange:e=>H({retryDelay:parseInt(e)||1e3}),min:500,max:1e4,children:[(0,t.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,t.jsxs)(a.NumberInputStepper,{children:[(0,t.jsx)(a.NumberIncrementStepper,{}),(0,t.jsx)(a.NumberDecrementStepper,{})]})]})]})]}),(0,t.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,t.jsxs)(a.HStack,{spacing:4,children:[(0,t.jsx)(a.Switch,{isChecked:f.followRedirects,onChange:e=>H({followRedirects:e.target.checked}),colorScheme:"teal"}),(0,t.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,t.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Follow Redirects"}),(0,t.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Automatically follow HTTP redirects (3xx responses)"})]})]}),(0,t.jsxs)(a.HStack,{spacing:4,children:[(0,t.jsx)(a.Switch,{isChecked:f.validateSSL,onChange:e=>H({validateSSL:e.target.checked}),colorScheme:"red"}),(0,t.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,t.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Validate SSL Certificates"}),(0,t.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Verify SSL certificates (disable only for testing)"})]})]})]})]})}),(0,t.jsx)(a.TabPanel,{children:(0,t.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,t.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,t.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Test API Request"}),(0,t.jsx)(a.Button,{leftIcon:T?(0,t.jsx)(a.Spinner,{size:"sm"}):(0,t.jsx)(i.aze,{}),onClick:B,colorScheme:"teal",isLoading:T,loadingText:"Testing...",isDisabled:!f.url,children:"Test Request"})]}),(0,t.jsxs)(a.Alert,{status:"warning",borderRadius:"md",children:[(0,t.jsx)(a.AlertIcon,{}),(0,t.jsx)(a.AlertDescription,{fontSize:"sm",children:"⚠️ This will make a real API request! Variables will not be replaced during testing. Make sure your URL and credentials are correct."})]}),z&&(0,t.jsxs)(a.Alert,{status:"error",borderRadius:"md",children:[(0,t.jsx)(a.AlertIcon,{}),(0,t.jsxs)(a.Box,{children:[(0,t.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:"Request Failed"}),(0,t.jsx)(a.Text,{fontSize:"sm",children:z})]})]}),C&&(0,t.jsxs)(a.Box,{children:[(0,t.jsx)(a.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,mb:2,children:"Response:"}),(0,t.jsx)(a.Box,{p:4,bg:d.colors.surface,borderRadius:"md",border:"1px solid",borderColor:d.colors.border,maxH:"400px",overflowY:"auto",children:(0,t.jsxs)(a.VStack,{spacing:3,align:"stretch",children:[(0,t.jsxs)(a.HStack,{justify:"space-between",children:[(0,t.jsxs)(a.Badge,{colorScheme:"green",size:"lg",children:[C.status," ",C.statusText]}),(0,t.jsxs)(a.HStack,{spacing:2,children:[(0,t.jsx)(i.YrT,{color:"green"}),(0,t.jsx)(a.Text,{fontSize:"sm",color:"green.500",children:"Success"})]})]}),(0,t.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Response Data:"}),(0,t.jsx)(a.Box,{bg:d.colors.background,p:3,borderRadius:"md",fontFamily:"monospace",fontSize:"xs",overflowX:"auto",children:(0,t.jsx)("pre",{children:JSON.stringify(C.data,null,2)})}),w.length>0&&(0,t.jsxs)(a.Box,{children:[(0,t.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,mb:2,children:"Available Response Variables:"}),(0,t.jsxs)(a.VStack,{spacing:1,align:"stretch",maxH:"150px",overflowY:"auto",children:[w.slice(0,20).map(e=>(0,t.jsxs)(a.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>D(`{response.${e}}`),children:[(0,t.jsx)(a.Code,{fontSize:"xs",colorScheme:"teal",children:`{response.${e}}`}),(0,t.jsx)(a.IconButton,{icon:(0,t.jsx)(i.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable"})]},e)),w.length>20&&(0,t.jsxs)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:["...and ",w.length-20," more variables"]})]})]})]})})]})]})})]})]}),(0,t.jsx)(a.Button,{colorScheme:"teal",onClick:()=>{e.url=f.url,e.method=f.method,e.headers=f.headers,e.body=f.body,e.bodyType=f.bodyType,e.timeout=f.timeout,e.saveToVariable=f.saveToVariable,e.errorHandling=f.errorHandling,e.description=f.description,e.retryCount=f.retryCount,e.retryDelay=f.retryDelay,e.followRedirects=f.followRedirects,e.validateSSL=f.validateSSL,e.label=`${f.method||"GET"} Request`,g()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});b.displayName="ApiRequestNode";let j=b;s()}catch(e){s(e)}})}};