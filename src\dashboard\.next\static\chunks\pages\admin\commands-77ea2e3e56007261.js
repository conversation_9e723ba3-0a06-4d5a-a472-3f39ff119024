(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6178],{25149:(e,r,o)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/commands",function(){return o(38171)}])},38171:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>O});var a=o(94513),n=o(72468),t=o(81139),s=o(24490),l=o(31840),i=o(29607),c=o(43700),d=o(79028),h=o(64349),m=o(5142),g=o(28365),u=o(7476),p=o(49892),x=o(1871),b=o(78813),j=o(29484),f=o(59220),w=o(37846),y=o(82939),C=o(35339),S=o(30301),v=o(24941),E=o(55206),k=o(5130),_=o(3037),z=o(75138),F=o(7836),A=o(84622);o(75632),o(82273);var T=o(94285),D=o(97119),L=o(35044);let I={moderation:{color:"red",gradient:{from:"rgba(245, 101, 101, 0.4)",to:"rgba(245, 101, 101, 0.1)"}},example:{color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"}},tickets:{color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"}},"voice-mistress":{color:"pink",gradient:{from:"rgba(237, 137, 179, 0.4)",to:"rgba(237, 137, 179, 0.1)"}},"welcome-goodbye":{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"}},Unknown:{color:"gray",gradient:{from:"rgba(113, 128, 150, 0.4)",to:"rgba(113, 128, 150, 0.1)"}}},N=e=>{let{command:r,onDelete:o,onToggle:n}=e,t=I[r.addon]||I.Unknown,[s,l]=(0,T.useState)(!1),i=async()=>{l(!0),await n(r,!r.enabled),l(!1)};return(0,a.jsx)(m.Z,{bg:"linear-gradient(135deg, ".concat(t.gradient.from,", ").concat(t.gradient.to,")"),backdropFilter:"blur(10px)",borderWidth:2,borderColor:"".concat(t.color,".400"),rounded:"xl",overflow:"hidden",transition:"all 0.2s",opacity:r.enabled?1:.7,_hover:{transform:"translateY(-2px)",boxShadow:"0 4px 20px ".concat(t.gradient.from)},children:(0,a.jsx)(g.b,{children:(0,a.jsxs)(_.T,{align:"stretch",spacing:4,children:[(0,a.jsxs)(x.z,{justify:"space-between",children:[(0,a.jsxs)(x.z,{children:[(0,a.jsx)(j.I,{as:L.FiCommand,color:"".concat(t.color,".400"),boxSize:5}),(0,a.jsxs)(b.D,{size:"md",color:"white",children:["/",r.name]})]}),(0,a.jsxs)(_.T,{spacing:1,align:"end",children:[(0,a.jsx)(c.E,{colorScheme:t.color,size:"sm",children:r.category}),(0,a.jsx)(c.E,{variant:"outline",colorScheme:"gray",size:"sm",children:r.scope})]})]}),(0,a.jsx)(k.E,{color:"gray.300",fontSize:"sm",noOfLines:2,children:r.description}),(0,a.jsxs)(k.E,{color:"gray.400",fontSize:"xs",children:["ID: ",r.id]}),(0,a.jsxs)(x.z,{justify:"space-between",children:[(0,a.jsx)(E.d,{isChecked:r.enabled,onChange:i,isDisabled:s,colorScheme:t.color}),(0,a.jsx)(h.$,{size:"sm",leftIcon:(0,a.jsx)(j.I,{as:L.FiTrash2}),variant:"ghost",colorScheme:t.color,onClick:()=>o(r),children:"Remove"})]})]})})})};function O(){let[e,r]=(0,T.useState)([]),[o,m]=(0,T.useState)([]),[g,x]=(0,T.useState)(""),[E,O]=(0,T.useState)("all"),[R,W]=(0,T.useState)(!0),[U,$]=(0,T.useState)(!1),[P,B]=(0,T.useState)(null),{isOpen:M,onOpen:X,onClose:G}=(0,A.j)(),J=(0,T.useRef)(null),Q=(0,F.d)(),Y=Array.from(new Set(e.map(e=>e.category))).sort();(0,T.useEffect)(()=>{let r=e;g.trim()&&(r=r.filter(e=>e.name.toLowerCase().includes(g.toLowerCase())||e.description.toLowerCase().includes(g.toLowerCase())||e.category.toLowerCase().includes(g.toLowerCase()))),"all"!==E&&(r=r.filter(e=>e.category===E)),m(r)},[e,g,E]);let Z=async()=>{try{W(!0);let e=await fetch("/api/admin/commands");if(e.ok){let o=await e.json();r(o)}else throw Error("Failed to fetch commands")}catch(e){Q({title:"Error",description:e.message||"Failed to fetch commands",status:"error",duration:5e3})}finally{W(!1)}};(0,T.useEffect)(()=>{Z()},[]);let q=async()=>{$(!0),await Z(),$(!1)},H=async e=>{B(e),X()},K=async(o,a)=>{try{if(!(await fetch("/api/admin/commands",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({commandId:o.id,enabled:a})})).ok)throw Error("Failed to update command state");r(e.map(e=>e.id===o.id?{...e,enabled:a}:e)),Q({title:"Success",description:"Command /".concat(o.name," has been ").concat(a?"enabled":"disabled"),status:"success",duration:3e3})}catch(e){Q({title:"Error",description:e.message||"Failed to update command state",status:"error",duration:5e3})}},V=async()=>{if(P)try{if(!(await fetch("/api/admin/commands?commandId=".concat(P.id,"&scope=").concat(P.scope),{method:"DELETE"})).ok)throw Error("Failed to delete command");r(e.filter(e=>e.id!==P.id)),Q({title:"Success",description:"Command /".concat(P.name," has been removed"),status:"success",duration:3e3})}catch(e){Q({title:"Error",description:e.message||"Failed to delete command",status:"error",duration:5e3})}finally{G(),B(null)}};return(0,a.jsx)(D.A,{children:(0,a.jsxs)(u.m,{maxW:"7xl",py:6,children:[(0,a.jsxs)(_.T,{spacing:6,align:"stretch",children:[(0,a.jsxs)(p.s,{direction:{base:"column",lg:"row"},justify:"space-between",align:{base:"start",lg:"center"},gap:4,children:[(0,a.jsxs)(d.a,{children:[(0,a.jsx)(b.D,{size:"lg",mb:2,bgGradient:"linear(to-r, pink.500, purple.500)",bgClip:"text",children:"Bot Commands"}),(0,a.jsxs)(k.E,{color:"gray.400",children:["Manage your Discord bot's slash commands (",o.length," of ",e.length,")"]})]}),(0,a.jsx)(h.$,{leftIcon:(0,a.jsx)(j.I,{as:L.FiRefreshCw}),colorScheme:"purple",variant:"outline",onClick:q,isLoading:U,children:"Refresh"})]}),(0,a.jsxs)(p.s,{direction:{base:"column",md:"row"},gap:4,children:[(0,a.jsxs)(w.M,{flex:1,children:[(0,a.jsx)(y.W,{pointerEvents:"none",children:(0,a.jsx)(j.I,{as:L.FiSearch,color:"gray.400"})}),(0,a.jsx)(f.p,{placeholder:"Search commands...",value:g,onChange:e=>x(e.target.value),bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",_hover:{borderColor:"whiteAlpha.300"},_focus:{borderColor:"purple.400",boxShadow:"0 0 0 1px var(--chakra-colors-purple-400)"}})]}),(0,a.jsxs)(C.l,{value:E,onChange:e=>O(e.target.value),w:{base:"full",md:"200px"},bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",_hover:{borderColor:"whiteAlpha.300"},_focus:{borderColor:"purple.400",boxShadow:"0 0 0 1px var(--chakra-colors-purple-400)"},children:[(0,a.jsx)("option",{value:"all",children:"All Categories"}),Y.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsx)(z.B,{spacing:3,children:Y.map(r=>{let o=e.filter(e=>e.category===r).length,n=I[r.toLowerCase()]||I.Unknown;return(0,a.jsx)(z.Q,{children:(0,a.jsxs)(c.E,{colorScheme:n.color,variant:E===r?"solid":"outline",cursor:"pointer",onClick:()=>O(E===r?"all":r),px:3,py:1,rounded:"full",children:[r," (",o,")"]})},r)})}),R?(0,a.jsx)(S.r,{columns:{base:1,md:2,lg:3},spacing:6,children:[1,2,3].map(e=>(0,a.jsx)(v.E,{height:"200px",rounded:"xl"},e))}):(0,a.jsx)(S.r,{columns:{base:1,md:2,lg:3},spacing:6,children:o.map(e=>(0,a.jsx)(N,{command:e,onDelete:H,onToggle:K},e.id))}),!R&&0===o.length&&(0,a.jsxs)(d.a,{textAlign:"center",py:12,children:[(0,a.jsx)(j.I,{as:L.FiFilter,boxSize:12,color:"gray.400",mb:4}),(0,a.jsx)(b.D,{size:"md",color:"gray.400",mb:2,children:"No commands found"}),(0,a.jsx)(k.E,{color:"gray.500",children:"Try adjusting your search or category filter"})]})]}),(0,a.jsx)(n.Lt,{isOpen:M,leastDestructiveRef:J,onClose:G,children:(0,a.jsx)(i.m,{children:(0,a.jsxs)(n.EO,{bg:"gray.800",borderColor:"whiteAlpha.200",borderWidth:1,children:[(0,a.jsx)(l.r,{fontSize:"lg",fontWeight:"bold",children:"Delete Command"}),(0,a.jsxs)(t.c,{children:["Are you sure you want to remove the command /",null==P?void 0:P.name,"? This action cannot be undone."]}),(0,a.jsxs)(s.j,{children:[(0,a.jsx)(h.$,{ref:J,onClick:G,children:"Cancel"}),(0,a.jsx)(h.$,{colorScheme:"red",onClick:V,ml:3,children:"Delete"})]})]})})})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,636,7398,1203,8792],()=>r(25149)),_N_E=e.O()}]);