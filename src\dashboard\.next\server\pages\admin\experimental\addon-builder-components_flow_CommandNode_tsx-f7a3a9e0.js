"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "pages/admin/experimental/addon-builder-components_flow_CommandNode_tsx-f7a3a9e0";
exports.ids = ["pages/admin/experimental/addon-builder-components_flow_CommandNode_tsx-f7a3a9e0"];
exports.modules = {

/***/ "(pages-dir-node)/./components/flow/CommandNode.tsx":
/*!*****************************************!*\
  !*** ./components/flow/CommandNode.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var reactflow__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactflow */ \"reactflow\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionButton,AccordionIcon,AccordionItem,AccordionPanel,Alert,AlertDescription,AlertIcon,Badge,Box,Button,Checkbox,CheckboxGroup,Code,Collapse,Divider,FormControl,FormLabel,HStack,IconButton,Input,InputGroup,InputLeftAddon,Modal,ModalBody,ModalCloseButton,ModalContent,ModalHeader,ModalOverlay,NumberDecrementStepper,NumberIncrementStepper,NumberInput,NumberInputField,NumberInputStepper,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure!=!@chakra-ui/react */ \"(pages-dir-node)/__barrel_optimize__?names=Accordion,AccordionButton,AccordionIcon,AccordionItem,AccordionPanel,Alert,AlertDescription,AlertIcon,Badge,Box,Button,Checkbox,CheckboxGroup,Code,Collapse,Divider,FormControl,FormLabel,HStack,IconButton,Input,InputGroup,InputLeftAddon,Modal,ModalBody,ModalCloseButton,ModalContent,ModalHeader,ModalOverlay,NumberDecrementStepper,NumberIncrementStepper,NumberInput,NumberInputField,NumberInputStepper,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiCopy,FiEye,FiEyeOff,FiMinus,FiPlus,FiSettings,FiTrash2,FiZap!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiCopy,FiEye,FiEyeOff,FiMinus,FiPlus,FiSettings,FiTrash2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/ThemeContext */ \"(pages-dir-node)/./contexts/ThemeContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([reactflow__WEBPACK_IMPORTED_MODULE_2__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__, _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__]);\n([reactflow__WEBPACK_IMPORTED_MODULE_2__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__, _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n// Available variables for command context\nconst commandVariables = {\n    command: [\n        {\n            name: '{command.name}',\n            description: 'Command name that was executed',\n            icon: '⚡'\n        },\n        {\n            name: '{command.user}',\n            description: 'User who executed the command',\n            icon: '👤'\n        },\n        {\n            name: '{command.channel}',\n            description: 'Channel where command was executed',\n            icon: '📺'\n        },\n        {\n            name: '{command.server}',\n            description: 'Server where command was executed',\n            icon: '🏠'\n        },\n        {\n            name: '{command.timestamp}',\n            description: 'When the command was executed',\n            icon: '⏰'\n        }\n    ],\n    options: [\n        {\n            name: '{option.name}',\n            description: 'Value of a specific option',\n            icon: '🔧'\n        },\n        {\n            name: '{option.user}',\n            description: 'User option value',\n            icon: '👤'\n        },\n        {\n            name: '{option.channel}',\n            description: 'Channel option value',\n            icon: '📺'\n        },\n        {\n            name: '{option.role}',\n            description: 'Role option value',\n            icon: '🎭'\n        },\n        {\n            name: '{option.string}',\n            description: 'String option value',\n            icon: '💬'\n        },\n        {\n            name: '{option.number}',\n            description: 'Number option value',\n            icon: '🔢'\n        },\n        {\n            name: '{option.boolean}',\n            description: 'Boolean option value',\n            icon: '✅'\n        }\n    ],\n    user: [\n        {\n            name: '{user.id}',\n            description: 'User ID',\n            icon: '🆔'\n        },\n        {\n            name: '{user.username}',\n            description: 'Username',\n            icon: '👤'\n        },\n        {\n            name: '{user.displayName}',\n            description: 'Display Name',\n            icon: '📝'\n        },\n        {\n            name: '{user.tag}',\n            description: 'User Tag (username#0000)',\n            icon: '🏷️'\n        },\n        {\n            name: '{user.mention}',\n            description: 'User Mention (<@id>)',\n            icon: '📢'\n        },\n        {\n            name: '{user.avatar}',\n            description: 'Avatar URL',\n            icon: '🖼️'\n        },\n        {\n            name: '{user.roles}',\n            description: 'User Roles',\n            icon: '🎭'\n        },\n        {\n            name: '{user.permissions}',\n            description: 'User Permissions',\n            icon: '🔐'\n        },\n        {\n            name: '{user.joinedAt}',\n            description: 'Server Join Date',\n            icon: '🚪'\n        }\n    ],\n    channel: [\n        {\n            name: '{channel.id}',\n            description: 'Channel ID',\n            icon: '🆔'\n        },\n        {\n            name: '{channel.name}',\n            description: 'Channel Name',\n            icon: '📺'\n        },\n        {\n            name: '{channel.mention}',\n            description: 'Channel Mention (<#id>)',\n            icon: '📢'\n        },\n        {\n            name: '{channel.type}',\n            description: 'Channel Type',\n            icon: '📋'\n        },\n        {\n            name: '{channel.topic}',\n            description: 'Channel Topic',\n            icon: '💬'\n        },\n        {\n            name: '{channel.memberCount}',\n            description: 'Member Count',\n            icon: '👥'\n        }\n    ],\n    server: [\n        {\n            name: '{server.id}',\n            description: 'Server ID',\n            icon: '🆔'\n        },\n        {\n            name: '{server.name}',\n            description: 'Server Name',\n            icon: '🏠'\n        },\n        {\n            name: '{server.icon}',\n            description: 'Server Icon URL',\n            icon: '🖼️'\n        },\n        {\n            name: '{server.memberCount}',\n            description: 'Member Count',\n            icon: '👥'\n        },\n        {\n            name: '{server.owner}',\n            description: 'Server Owner',\n            icon: '👑'\n        },\n        {\n            name: '{server.boostLevel}',\n            description: 'Server Boost Level',\n            icon: '🚀'\n        }\n    ]\n};\nconst permissionsList = [\n    'ADMINISTRATOR',\n    'MANAGE_GUILD',\n    'MANAGE_ROLES',\n    'MANAGE_CHANNELS',\n    'KICK_MEMBERS',\n    'BAN_MEMBERS',\n    'MANAGE_MESSAGES',\n    'EMBED_LINKS',\n    'ATTACH_FILES',\n    'READ_MESSAGE_HISTORY',\n    'MENTION_EVERYONE',\n    'USE_EXTERNAL_EMOJIS',\n    'CONNECT',\n    'SPEAK',\n    'MUTE_MEMBERS',\n    'DEAFEN_MEMBERS',\n    'MOVE_MEMBERS',\n    'USE_VAD',\n    'CHANGE_NICKNAME',\n    'MANAGE_NICKNAMES',\n    'MANAGE_WEBHOOKS',\n    'MANAGE_EMOJIS',\n    'MODERATE_MEMBERS',\n    'VIEW_AUDIT_LOG',\n    'MANAGE_EVENTS',\n    'MANAGE_THREADS',\n    'CREATE_PUBLIC_THREADS',\n    'CREATE_PRIVATE_THREADS',\n    'USE_EXTERNAL_STICKERS',\n    'SEND_MESSAGES_IN_THREADS',\n    'START_EMBEDDED_ACTIVITIES'\n];\nconst optionTypes = [\n    {\n        value: 'string',\n        label: '📝 String - Text input'\n    },\n    {\n        value: 'integer',\n        label: '🔢 Integer - Whole number'\n    },\n    {\n        value: 'number',\n        label: '🔢 Number - Decimal number'\n    },\n    {\n        value: 'boolean',\n        label: '✅ Boolean - True/False'\n    },\n    {\n        value: 'user',\n        label: '👤 User - Discord user'\n    },\n    {\n        value: 'channel',\n        label: '📺 Channel - Discord channel'\n    },\n    {\n        value: 'role',\n        label: '🎭 Role - Discord role'\n    },\n    {\n        value: 'mentionable',\n        label: '📢 Mentionable - User or role'\n    },\n    {\n        value: 'attachment',\n        label: '📎 Attachment - File upload'\n    }\n];\nconst CommandNode = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(({ data, selected, id, updateNodeData: updateParentNodeData })=>{\n    const { currentScheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const { isOpen, onOpen, onClose } = (0,_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useDisclosure)();\n    const [nodeData, setNodeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"CommandNode.useState\": ()=>({\n                guildOnly: false,\n                adminOnly: false,\n                allowDMs: false,\n                cooldown: 0,\n                options: [],\n                category: 'general',\n                examples: [],\n                permissions: [],\n                ephemeral: false,\n                deferReply: false,\n                ...data\n            })\n    }[\"CommandNode.useState\"]);\n    const [showVariables, setShowVariables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateNodeData = (updates)=>{\n        setNodeData((prev)=>({\n                ...prev,\n                ...updates\n            }));\n    };\n    const handleModalClose = ()=>{\n        // Update parent nodes array when modal closes\n        if (updateParentNodeData && id) {\n            updateParentNodeData(id, nodeData);\n        }\n        onClose();\n    };\n    const addOption = ()=>{\n        const newOption = {\n            name: '',\n            description: '',\n            type: 'string',\n            required: false,\n            choices: []\n        };\n        updateNodeData({\n            options: [\n                ...nodeData.options || [],\n                newOption\n            ]\n        });\n    };\n    const updateOption = (index, updates)=>{\n        const newOptions = [\n            ...nodeData.options || []\n        ];\n        newOptions[index] = {\n            ...newOptions[index],\n            ...updates\n        };\n        updateNodeData({\n            options: newOptions\n        });\n    };\n    const removeOption = (index)=>{\n        const newOptions = (nodeData.options || []).filter((_, i)=>i !== index);\n        updateNodeData({\n            options: newOptions\n        });\n    };\n    const addChoice = (optionIndex)=>{\n        const newOptions = [\n            ...nodeData.options || []\n        ];\n        if (!newOptions[optionIndex].choices) {\n            newOptions[optionIndex].choices = [];\n        }\n        newOptions[optionIndex].choices.push({\n            name: '',\n            value: ''\n        });\n        updateNodeData({\n            options: newOptions\n        });\n    };\n    const updateChoice = (optionIndex, choiceIndex, field, value)=>{\n        const newOptions = [\n            ...nodeData.options || []\n        ];\n        if (newOptions[optionIndex].choices) {\n            newOptions[optionIndex].choices[choiceIndex][field] = value;\n            updateNodeData({\n                options: newOptions\n            });\n        }\n    };\n    const removeChoice = (optionIndex, choiceIndex)=>{\n        const newOptions = [\n            ...nodeData.options || []\n        ];\n        if (newOptions[optionIndex].choices) {\n            newOptions[optionIndex].choices = newOptions[optionIndex].choices.filter((_, i)=>i !== choiceIndex);\n            updateNodeData({\n                options: newOptions\n            });\n        }\n    };\n    const copyVariable = (variable)=>{\n        navigator.clipboard.writeText(variable);\n    };\n    const renderVariablesList = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Collapse, {\n            in: showVariables,\n            animateOpacity: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                bg: currentScheme.colors.surface,\n                border: \"1px solid\",\n                borderColor: currentScheme.colors.border,\n                borderRadius: \"md\",\n                p: 4,\n                mt: 3,\n                maxH: \"400px\",\n                overflowY: \"auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Accordion, {\n                    allowMultiple: true,\n                    children: Object.entries(commandVariables).map(([category, variables])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AccordionItem, {\n                            border: \"none\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AccordionButton, {\n                                    px: 0,\n                                    py: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                            flex: \"1\",\n                                            textAlign: \"left\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                fontSize: \"sm\",\n                                                fontWeight: \"bold\",\n                                                color: currentScheme.colors.text,\n                                                textTransform: \"capitalize\",\n                                                children: [\n                                                    category,\n                                                    \" Variables\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AccordionIcon, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AccordionPanel, {\n                                    px: 0,\n                                    py: 2,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                        spacing: 2,\n                                        align: \"stretch\",\n                                        children: variables.map((variable)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                spacing: 2,\n                                                p: 2,\n                                                bg: currentScheme.colors.background,\n                                                borderRadius: \"md\",\n                                                cursor: \"pointer\",\n                                                _hover: {\n                                                    bg: currentScheme.colors.surface\n                                                },\n                                                onClick: ()=>copyVariable(variable.name),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                        fontSize: \"sm\",\n                                                        children: variable.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Code, {\n                                                        fontSize: \"xs\",\n                                                        colorScheme: \"blue\",\n                                                        children: variable.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                        fontSize: \"xs\",\n                                                        color: currentScheme.colors.textSecondary,\n                                                        flex: \"1\",\n                                                        children: variable.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCopy, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        size: \"xs\",\n                                                        variant: \"ghost\",\n                                                        \"aria-label\": \"Copy variable\",\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            copyVariable(variable.name);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, variable.name, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n            lineNumber: 260,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                bg: currentScheme.colors.surface,\n                border: `2px solid ${selected ? '#3b82f6' : currentScheme.colors.border}`,\n                borderRadius: \"md\",\n                p: 2,\n                minW: \"140px\",\n                maxW: \"180px\",\n                boxShadow: \"sm\",\n                position: \"relative\",\n                _hover: {\n                    boxShadow: 'md',\n                    transform: 'translateY(-1px)'\n                },\n                transition: \"all 0.2s\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_2__.Handle, {\n                        type: \"target\",\n                        position: reactflow__WEBPACK_IMPORTED_MODULE_2__.Position.Top,\n                        style: {\n                            background: '#3b82f6',\n                            border: `2px solid ${currentScheme.colors.surface}`,\n                            width: '12px',\n                            height: '12px',\n                            top: '-6px',\n                            left: '50%',\n                            transform: 'translateX(-50%)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                        spacing: 1,\n                        align: \"stretch\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                justify: \"space-between\",\n                                align: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                        spacing: 1,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                                bg: \"blue.500\",\n                                                color: \"white\",\n                                                borderRadius: \"full\",\n                                                p: 0.5,\n                                                fontSize: \"xs\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiZap, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                fontSize: \"xs\",\n                                                fontWeight: \"bold\",\n                                                color: currentScheme.colors.text,\n                                                children: \"Command\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSettings, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        size: \"xs\",\n                                        variant: \"ghost\",\n                                        onClick: onOpen,\n                                        \"aria-label\": \"Configure command\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    fontSize: \"xs\",\n                                    color: currentScheme.colors.text,\n                                    noOfLines: 1,\n                                    children: [\n                                        \"/\",\n                                        nodeData.commandName || 'unnamed'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, undefined),\n                            nodeData.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    fontSize: \"xs\",\n                                    color: currentScheme.colors.textSecondary,\n                                    noOfLines: 1,\n                                    children: nodeData.description.length > 25 ? nodeData.description.substring(0, 25) + '...' : nodeData.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                spacing: 1,\n                                flexWrap: \"wrap\",\n                                children: [\n                                    (nodeData.options?.length ?? 0) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"blue\",\n                                        children: [\n                                            nodeData.options?.length,\n                                            \" option\",\n                                            (nodeData.options?.length ?? 0) !== 1 ? 's' : ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    nodeData.adminOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"red\",\n                                        children: \"Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    nodeData.cooldown && nodeData.cooldown > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"orange\",\n                                        children: [\n                                            nodeData.cooldown,\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_2__.Handle, {\n                        type: \"source\",\n                        position: reactflow__WEBPACK_IMPORTED_MODULE_2__.Position.Bottom,\n                        style: {\n                            background: '#3b82f6',\n                            border: `2px solid ${currentScheme.colors.surface}`,\n                            width: '12px',\n                            height: '12px',\n                            bottom: '-6px',\n                            left: '50%',\n                            transform: 'translateX(-50%)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Modal, {\n                isOpen: isOpen,\n                onClose: handleModalClose,\n                size: \"4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalOverlay, {\n                        bg: \"blackAlpha.600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalContent, {\n                        bg: currentScheme.colors.background,\n                        border: \"2px solid\",\n                        borderColor: \"blue.400\",\n                        maxW: \"1200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalHeader, {\n                                color: currentScheme.colors.text,\n                                children: \"⚡ Configure Command\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalCloseButton, {}, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.ModalBody, {\n                                pb: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                    spacing: 6,\n                                    align: \"stretch\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                    justify: \"space-between\",\n                                                    align: \"center\",\n                                                    mb: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                            fontSize: \"sm\",\n                                                            fontWeight: \"bold\",\n                                                            color: currentScheme.colors.text,\n                                                            children: \"Available Variables\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"ghost\",\n                                                            leftIcon: showVariables ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiEyeOff, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 47\n                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiEye, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 62\n                                                            }, void 0),\n                                                            onClick: ()=>setShowVariables(!showVariables),\n                                                            children: [\n                                                                showVariables ? 'Hide' : 'Show',\n                                                                \" Variables\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                                                    status: \"info\",\n                                                    borderRadius: \"md\",\n                                                    mb: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertIcon, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                                            fontSize: \"sm\",\n                                                            children: \"\\uD83D\\uDCA1 Use variables in your command responses! Click any variable below to copy it. Variables are replaced with actual values when your command runs.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                renderVariablesList()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Divider, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                                            variant: \"enclosed\",\n                                            colorScheme: \"blue\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.TabList, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                                            children: \"Basic Info\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                                            children: \"Options\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                                            children: \"Permissions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Tab, {\n                                                            children: \"Advanced\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.TabPanels, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                spacing: 4,\n                                                                align: \"stretch\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.SimpleGrid, {\n                                                                        columns: 2,\n                                                                        spacing: 4,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                                isRequired: true,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                        color: currentScheme.colors.text,\n                                                                                        children: \"Command Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 478,\n                                                                                        columnNumber: 21\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.InputGroup, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.InputLeftAddon, {\n                                                                                                bg: currentScheme.colors.surface,\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"/\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 480,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                value: nodeData.commandName || '',\n                                                                                                onChange: (e)=>updateNodeData({\n                                                                                                        commandName: e.target.value\n                                                                                                    }),\n                                                                                                placeholder: \"ping\",\n                                                                                                bg: currentScheme.colors.background,\n                                                                                                color: currentScheme.colors.text,\n                                                                                                borderColor: currentScheme.colors.border\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 483,\n                                                                                                columnNumber: 23\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 479,\n                                                                                        columnNumber: 21\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 477,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                        color: currentScheme.colors.text,\n                                                                                        children: \"Category\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 495,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                                                        value: nodeData.category || 'general',\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                category: e.target.value\n                                                                                            }),\n                                                                                        bg: currentScheme.colors.background,\n                                                                                        color: currentScheme.colors.text,\n                                                                                        borderColor: currentScheme.colors.border,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"general\",\n                                                                                                children: \"General\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 503,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"moderation\",\n                                                                                                children: \"Moderation\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 504,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"fun\",\n                                                                                                children: \"Fun\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 505,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"utility\",\n                                                                                                children: \"Utility\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 506,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"admin\",\n                                                                                                children: \"Admin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 507,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"info\",\n                                                                                                children: \"Info\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 508,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"music\",\n                                                                                                children: \"Music\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 509,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"games\",\n                                                                                                children: \"Games\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 510,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 496,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 19\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                        isRequired: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                color: currentScheme.colors.text,\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 516,\n                                                                                columnNumber: 21\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                                                value: nodeData.description || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        description: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"What does this command do?\",\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                minH: \"80px\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 517,\n                                                                                columnNumber: 21\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                color: currentScheme.colors.text,\n                                                                                children: \"Usage Examples\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 529,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                                                value: nodeData.examples?.join('\\n') || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        examples: e.target.value.split('\\n').filter((ex)=>ex.trim())\n                                                                                    }),\n                                                                                placeholder: `/ping\\n/ping server\\n/ping {user.mention}`,\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                minH: \"80px\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                fontSize: \"xs\",\n                                                                                color: currentScheme.colors.textSecondary,\n                                                                                mt: 1,\n                                                                                children: \"One example per line\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 539,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 19\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                spacing: 4,\n                                                                align: \"stretch\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                        justify: \"space-between\",\n                                                                        align: \"center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                fontSize: \"lg\",\n                                                                                fontWeight: \"bold\",\n                                                                                color: currentScheme.colors.text,\n                                                                                children: \"Command Options\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 550,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPlus, {}, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                    lineNumber: 554,\n                                                                                    columnNumber: 37\n                                                                                }, void 0),\n                                                                                onClick: addOption,\n                                                                                colorScheme: \"blue\",\n                                                                                size: \"sm\",\n                                                                                children: \"Add Option\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 553,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                                                                        status: \"info\",\n                                                                        borderRadius: \"md\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertIcon, {}, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 564,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                                                                fontSize: \"sm\",\n                                                                                children: \"Options are parameters users can provide with your command. They appear as autocomplete fields in Discord.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 565,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                        spacing: 4,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            nodeData.options?.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                                                                    p: 4,\n                                                                                    bg: currentScheme.colors.surface,\n                                                                                    borderRadius: \"md\",\n                                                                                    border: \"1px solid\",\n                                                                                    borderColor: currentScheme.colors.border,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                            justify: \"space-between\",\n                                                                                            align: \"center\",\n                                                                                            mb: 3,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                    fontSize: \"md\",\n                                                                                                    fontWeight: \"bold\",\n                                                                                                    color: currentScheme.colors.text,\n                                                                                                    children: [\n                                                                                                        \"Option \",\n                                                                                                        index + 1\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 581,\n                                                                                                    columnNumber: 31\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                                                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTrash2, {}, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                        lineNumber: 585,\n                                                                                                        columnNumber: 39\n                                                                                                    }, void 0),\n                                                                                                    size: \"sm\",\n                                                                                                    colorScheme: \"red\",\n                                                                                                    variant: \"ghost\",\n                                                                                                    onClick: ()=>removeOption(index),\n                                                                                                    \"aria-label\": \"Remove option\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 584,\n                                                                                                    columnNumber: 25\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                            lineNumber: 580,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                            spacing: 3,\n                                                                                            align: \"stretch\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.SimpleGrid, {\n                                                                                                    columns: 2,\n                                                                                                    spacing: 3,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                                                            isRequired: true,\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                                                    fontSize: \"sm\",\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    children: \"Option Name\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 597,\n                                                                                                                    columnNumber: 35\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                                    value: option.name,\n                                                                                                                    onChange: (e)=>updateOption(index, {\n                                                                                                                            name: e.target.value\n                                                                                                                        }),\n                                                                                                                    placeholder: \"user\",\n                                                                                                                    bg: currentScheme.colors.background,\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    borderColor: currentScheme.colors.border,\n                                                                                                                    size: \"sm\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 598,\n                                                                                                                    columnNumber: 35\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 596,\n                                                                                                            columnNumber: 33\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                                                            isRequired: true,\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                                                    fontSize: \"sm\",\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    children: \"Option Type\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 610,\n                                                                                                                    columnNumber: 35\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                                                                                                    value: option.type,\n                                                                                                                    onChange: (e)=>updateOption(index, {\n                                                                                                                            type: e.target.value\n                                                                                                                        }),\n                                                                                                                    bg: currentScheme.colors.background,\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    borderColor: currentScheme.colors.border,\n                                                                                                                    size: \"sm\",\n                                                                                                                    children: optionTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                                            value: type.value,\n                                                                                                                            children: type.label\n                                                                                                                        }, type.value, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                            lineNumber: 620,\n                                                                                                                            columnNumber: 39\n                                                                                                                        }, undefined))\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 611,\n                                                                                                                    columnNumber: 27\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 609,\n                                                                                                            columnNumber: 33\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 595,\n                                                                                                    columnNumber: 31\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                                            fontSize: \"sm\",\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            children: \"Description\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 629,\n                                                                                                            columnNumber: 25\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                            value: option.description,\n                                                                                                            onChange: (e)=>updateOption(index, {\n                                                                                                                    description: e.target.value\n                                                                                                                }),\n                                                                                                            placeholder: \"The user to ping\",\n                                                                                                            bg: currentScheme.colors.background,\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            borderColor: currentScheme.colors.border,\n                                                                                                            size: \"sm\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 630,\n                                                                                                            columnNumber: 25\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 628,\n                                                                                                    columnNumber: 25\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                                            isChecked: option.required,\n                                                                                                            onChange: (e)=>updateOption(index, {\n                                                                                                                    required: e.target.checked\n                                                                                                                }),\n                                                                                                            colorScheme: \"blue\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 642,\n                                                                                                            columnNumber: 33\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                            fontSize: \"sm\",\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            children: \"Required option\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 647,\n                                                                                                            columnNumber: 33\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 641,\n                                                                                                    columnNumber: 31\n                                                                                                }, undefined),\n                                                                                                (option.type === 'string' || option.type === 'integer' || option.type === 'number') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                                            justify: \"space-between\",\n                                                                                                            align: \"center\",\n                                                                                                            mb: 2,\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                                    fontSize: \"sm\",\n                                                                                                                    fontWeight: \"bold\",\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    children: \"Predefined Choices (Optional)\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 655,\n                                                                                                                    columnNumber: 37\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                                                    size: \"xs\",\n                                                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPlus, {}, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                        lineNumber: 660,\n                                                                                                                        columnNumber: 31\n                                                                                                                    }, void 0),\n                                                                                                                    onClick: ()=>addChoice(index),\n                                                                                                                    colorScheme: \"blue\",\n                                                                                                                    variant: \"ghost\",\n                                                                                                                    children: \"Add Choice\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 658,\n                                                                                                                    columnNumber: 19\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 654,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                                            spacing: 2,\n                                                                                                            align: \"stretch\",\n                                                                                                            children: option.choices?.map((choice, choiceIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                                                    spacing: 2,\n                                                                                                                    children: [\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                                            value: choice.name,\n                                                                                                                            onChange: (e)=>updateChoice(index, choiceIndex, 'name', e.target.value),\n                                                                                                                            placeholder: \"Choice name\",\n                                                                                                                            bg: currentScheme.colors.background,\n                                                                                                                            color: currentScheme.colors.text,\n                                                                                                                            borderColor: currentScheme.colors.border,\n                                                                                                                            size: \"sm\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                            lineNumber: 672,\n                                                                                                                            columnNumber: 41\n                                                                                                                        }, undefined),\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                                            value: choice.value,\n                                                                                                                            onChange: (e)=>updateChoice(index, choiceIndex, 'value', e.target.value),\n                                                                                                                            placeholder: \"Choice value\",\n                                                                                                                            bg: currentScheme.colors.background,\n                                                                                                                            color: currentScheme.colors.text,\n                                                                                                                            borderColor: currentScheme.colors.border,\n                                                                                                                            size: \"sm\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                            lineNumber: 681,\n                                                                                                                            columnNumber: 41\n                                                                                                                        }, undefined),\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                                                                                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiMinus, {}, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                                lineNumber: 691,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, void 0),\n                                                                                                                            size: \"sm\",\n                                                                                                                            colorScheme: \"red\",\n                                                                                                                            variant: \"ghost\",\n                                                                                                                            onClick: ()=>removeChoice(index, choiceIndex),\n                                                                                                                            \"aria-label\": \"Remove choice\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                            lineNumber: 690,\n                                                                                                                            columnNumber: 41\n                                                                                                                        }, undefined)\n                                                                                                                    ]\n                                                                                                                }, choiceIndex, true, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 671,\n                                                                                                                    columnNumber: 39\n                                                                                                                }, undefined))\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 669,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 653,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                            lineNumber: 594,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, index, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                    lineNumber: 572,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)),\n                                                                            (!nodeData.options || nodeData.options.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                                                                                status: \"info\",\n                                                                                borderRadius: \"md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertIcon, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 709,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                                                                        children: \"No options configured. Your command will work without any parameters.\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 710,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                spacing: 4,\n                                                                align: \"stretch\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                        fontSize: \"lg\",\n                                                                        fontWeight: \"bold\",\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Command Permissions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                                                                        status: \"warning\",\n                                                                        borderRadius: \"md\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertIcon, {}, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 727,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                                                                fontSize: \"sm\",\n                                                                                children: \"Be careful with permissions! Overly restrictive permissions can prevent legitimate users from using your command.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 728,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                        spacing: 4,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                spacing: 4,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                        isChecked: nodeData.adminOnly,\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                adminOnly: e.target.checked\n                                                                                            }),\n                                                                                        colorScheme: \"red\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 735,\n                                                                                        columnNumber: 23\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                        align: \"start\",\n                                                                                        spacing: 0,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                fontSize: \"sm\",\n                                                                                                fontWeight: \"bold\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"Admin Only\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 741,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.textSecondary,\n                                                                                                children: \"Only server administrators can use this command\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 744,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 740,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 734,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                spacing: 4,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                        isChecked: nodeData.guildOnly,\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                guildOnly: e.target.checked\n                                                                                            }),\n                                                                                        colorScheme: \"blue\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 751,\n                                                                                        columnNumber: 23\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                        align: \"start\",\n                                                                                        spacing: 0,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                fontSize: \"sm\",\n                                                                                                fontWeight: \"bold\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"Server Only\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 757,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.textSecondary,\n                                                                                                children: \"Command can only be used in servers, not DMs\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 760,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 756,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 750,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                spacing: 4,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                        isChecked: nodeData.allowDMs,\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                allowDMs: e.target.checked\n                                                                                            }),\n                                                                                        colorScheme: \"green\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 767,\n                                                                                        columnNumber: 23\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                        align: \"start\",\n                                                                                        spacing: 0,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                fontSize: \"sm\",\n                                                                                                fontWeight: \"bold\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"Allow DMs\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 773,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.textSecondary,\n                                                                                                children: \"Command can be used in direct messages\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 776,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 772,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Divider, {}, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 783,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                fontSize: \"md\",\n                                                                                fontWeight: \"bold\",\n                                                                                color: currentScheme.colors.text,\n                                                                                mb: 3,\n                                                                                children: \"Required Permissions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 786,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                fontSize: \"sm\",\n                                                                                color: currentScheme.colors.textSecondary,\n                                                                                mb: 3,\n                                                                                children: \"Select the Discord permissions users need to use this command\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 789,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.CheckboxGroup, {\n                                                                                value: nodeData.permissions || [],\n                                                                                onChange: (value)=>updateNodeData({\n                                                                                        permissions: value\n                                                                                    }),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.SimpleGrid, {\n                                                                                    columns: 3,\n                                                                                    spacing: 2,\n                                                                                    children: permissionsList.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Checkbox, {\n                                                                                            value: permission,\n                                                                                            colorScheme: \"blue\",\n                                                                                            size: \"sm\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: permission.replace(/_/g, ' ').toLowerCase().replace(/\\b\\w/g, (l)=>l.toUpperCase())\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 804,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        }, permission, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                            lineNumber: 798,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                    lineNumber: 796,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 792,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.TabPanel, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                spacing: 4,\n                                                                align: \"stretch\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                        fontSize: \"lg\",\n                                                                        fontWeight: \"bold\",\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Advanced Settings\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 818,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                                color: currentScheme.colors.text,\n                                                                                children: \"Cooldown (seconds)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 823,\n                                                                                columnNumber: 23\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.NumberInput, {\n                                                                                value: nodeData.cooldown || 0,\n                                                                                onChange: (valueString)=>updateNodeData({\n                                                                                        cooldown: parseInt(valueString) || 0\n                                                                                    }),\n                                                                                min: 0,\n                                                                                max: 3600,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.NumberInputField, {\n                                                                                        bg: currentScheme.colors.background,\n                                                                                        color: currentScheme.colors.text,\n                                                                                        borderColor: currentScheme.colors.border\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 830,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.NumberInputStepper, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.NumberIncrementStepper, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 836,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.NumberDecrementStepper, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 837,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 835,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 824,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                fontSize: \"xs\",\n                                                                                color: currentScheme.colors.textSecondary,\n                                                                                mt: 1,\n                                                                                children: \"How long users must wait between uses (0 = no cooldown)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 840,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 822,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                        spacing: 4,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                spacing: 4,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                        isChecked: nodeData.ephemeral,\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                ephemeral: e.target.checked\n                                                                                            }),\n                                                                                        colorScheme: \"blue\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 847,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                        align: \"start\",\n                                                                                        spacing: 0,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                fontSize: \"sm\",\n                                                                                                fontWeight: \"bold\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"Ephemeral Response\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 853,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.textSecondary,\n                                                                                                children: \"Command response is only visible to the user who ran it\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 856,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 852,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.HStack, {\n                                                                                spacing: 4,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                        isChecked: nodeData.deferReply,\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                deferReply: e.target.checked\n                                                                                            }),\n                                                                                        colorScheme: \"orange\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 863,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.VStack, {\n                                                                                        align: \"start\",\n                                                                                        spacing: 0,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                fontSize: \"sm\",\n                                                                                                fontWeight: \"bold\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"Defer Reply\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 869,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.textSecondary,\n                                                                                                children: 'Show \"thinking...\" message while processing (for slow commands)'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 872,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 868,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 862,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 845,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            colorScheme: \"blue\",\n                                            onClick: ()=>{\n                                                // Save the configuration\n                                                data.commandName = nodeData.commandName;\n                                                data.description = nodeData.description;\n                                                data.options = nodeData.options;\n                                                data.permissions = nodeData.permissions;\n                                                data.cooldown = nodeData.cooldown;\n                                                data.guildOnly = nodeData.guildOnly;\n                                                data.adminOnly = nodeData.adminOnly;\n                                                data.allowDMs = nodeData.allowDMs;\n                                                data.category = nodeData.category;\n                                                data.examples = nodeData.examples;\n                                                data.ephemeral = nodeData.ephemeral;\n                                                data.deferReply = nodeData.deferReply;\n                                                data.label = nodeData.commandName ? `/${nodeData.commandName}` : 'Command';\n                                                onClose();\n                                            },\n                                            size: \"lg\",\n                                            width: \"full\",\n                                            children: \"Save Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 883,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n});\nCommandNode.displayName = 'CommandNode';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommandNode);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/flow/CommandNode.tsx\n");

/***/ })

};
;