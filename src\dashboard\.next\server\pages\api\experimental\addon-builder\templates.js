"use strict";(()=>{var e={};e.id=6484,e.ids=[6484],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},93757:(e,t,n)=>{n.r(t),n.d(t,{config:()=>u,default:()=>c,routeModule:()=>p});var i={};n.r(i),n.d(i,{default:()=>l});var o=n(93433),a=n(20264),r=n(20584),s=n(15806),d=n(94506);let m=[{id:"basic-utility",name:"Basic Utility",description:"A simple addon with common utility commands like ping, info, and user lookup.",category:"Utility",config:{name:"my-utility-addon",version:"1.0.0",description:"A utility addon with basic commands for server management and information.",author:"",commands:[{name:"ping",description:"Check the bot's latency and status",type:"slash",permissions:[],cooldown:3e3,enabled:!0,code:`const start = Date.now();
await interaction.reply({ content: 'Pinging...', ephemeral: true });
const latency = Date.now() - start;

const embed = new EmbedBuilder()
  .setColor(0x0099FF)
  .setTitle('🏓 Pong!')
  .addFields(
    { name: 'Latency', value: \`\${latency}ms\`, inline: true },
    { name: 'API Latency', value: \`\${bot.client.ws.ping}ms\`, inline: true }
  )
  .setTimestamp();

await interaction.editReply({ content: null, embeds: [embed] });`},{name:"serverinfo",description:"Display information about the current server",type:"slash",permissions:[],cooldown:5e3,enabled:!0,code:`const guild = interaction.guild;
if (!guild) {
  await interaction.reply({ content: 'This command can only be used in a server!', ephemeral: true });
  return;
}

const embed = new EmbedBuilder()
  .setColor(0x0099FF)
  .setTitle(\`📊 \${guild.name}\`)
  .setThumbnail(guild.iconURL())
  .addFields(
    { name: 'Owner', value: \`<@\${guild.ownerId}>\`, inline: true },
    { name: 'Members', value: guild.memberCount.toString(), inline: true },
    { name: 'Created', value: guild.createdAt.toDateString(), inline: true },
    { name: 'Boost Level', value: guild.premiumTier.toString(), inline: true },
    { name: 'Boosts', value: guild.premiumSubscriptionCount?.toString() || '0', inline: true }
  )
  .setTimestamp();

await interaction.reply({ embeds: [embed] });`}],events:[{name:"ready",once:!0,code:`const logger = Logger.createAddonLogger('my-utility-addon');
logger.info(\`\${bot.client.user?.tag} is ready with utility commands!\`);`}],settings:{embedColor:"#0099FF"}}},{id:"moderation-basic",name:"Basic Moderation",description:"Essential moderation commands for server management.",category:"Moderation",config:{name:"my-moderation-addon",version:"1.0.0",description:"A moderation addon with basic commands for managing your Discord server.",author:"",commands:[{name:"kick",description:"Kick a member from the server",type:"slash",permissions:["KICK_MEMBERS"],cooldown:5e3,enabled:!0,code:`// Check if user has permission
if (!interaction.member?.permissions.has('KICK_MEMBERS')) {
  await interaction.reply({ content: 'You need the Kick Members permission to use this command!', ephemeral: true });
  return;
}

const user = interaction.options.getUser('user');
const reason = interaction.options.getString('reason') || 'No reason provided';

if (!user) {
  await interaction.reply({ content: 'Please specify a user to kick!', ephemeral: true });
  return;
}

try {
  const member = await interaction.guild?.members.fetch(user.id);
  if (!member) {
    await interaction.reply({ content: 'User not found in this server!', ephemeral: true });
    return;
  }

  await member.kick(reason);
  
  const embed = new EmbedBuilder()
    .setColor(0xFF6B6B)
    .setTitle('👢 Member Kicked')
    .addFields(
      { name: 'User', value: \`\${user.tag} (\${user.id})\`, inline: true },
      { name: 'Reason', value: reason, inline: true },
      { name: 'Moderator', value: interaction.user.tag, inline: true }
    )
    .setTimestamp();

  await interaction.reply({ embeds: [embed] });
} catch (error) {
  await interaction.reply({ content: 'Failed to kick the user. They may have higher permissions than me.', ephemeral: true });
}`},{name:"clear",description:"Delete a specified number of messages",type:"slash",permissions:["MANAGE_MESSAGES"],cooldown:5e3,enabled:!0,code:`// Check if user has permission
if (!interaction.member?.permissions.has('MANAGE_MESSAGES')) {
  await interaction.reply({ content: 'You need the Manage Messages permission to use this command!', ephemeral: true });
  return;
}

const amount = interaction.options.getInteger('amount') || 1;

if (amount < 1 || amount > 100) {
  await interaction.reply({ content: 'Please specify a number between 1 and 100!', ephemeral: true });
  return;
}

try {
  const channel = interaction.channel;
  if (!channel || !channel.isTextBased()) {
    await interaction.reply({ content: 'This command can only be used in text channels!', ephemeral: true });
    return;
  }

  await channel.bulkDelete(amount, true);
  
  await interaction.reply({ 
    content: \`✅ Successfully deleted \${amount} message(s)!\`, 
    ephemeral: true 
  });
} catch (error) {
  await interaction.reply({ 
    content: 'Failed to delete messages. Messages may be older than 14 days.', 
    ephemeral: true 
  });
}`}],events:[{name:"guildMemberAdd",once:!1,code:`const member = args[0];
const logger = Logger.createAddonLogger('my-moderation-addon');
logger.info(\`New member joined: \${member.user.tag} (\${member.id})\`);

// You can add welcome message logic here`}],settings:{embedColor:"#FF6B6B"}}},{id:"fun-commands",name:"Fun Commands",description:"Entertainment commands to engage your community.",category:"Entertainment",config:{name:"my-fun-addon",version:"1.0.0",description:"A fun addon with entertainment commands for your Discord server.",author:"",commands:[{name:"8ball",description:"Ask the magic 8-ball a question",type:"slash",permissions:[],cooldown:3e3,enabled:!0,code:`const question = interaction.options.getString('question');
if (!question) {
  await interaction.reply({ content: 'Please ask a question!', ephemeral: true });
  return;
}

const responses = [
  'It is certain', 'It is decidedly so', 'Without a doubt', 'Yes definitely',
  'You may rely on it', 'As I see it, yes', 'Most likely', 'Outlook good',
  'Yes', 'Signs point to yes', 'Reply hazy, try again', 'Ask again later',
  'Better not tell you now', 'Cannot predict now', 'Concentrate and ask again',
  'Don\\'t count on it', 'My reply is no', 'My sources say no',
  'Outlook not so good', 'Very doubtful'
];

const response = responses[Math.floor(Math.random() * responses.length)];

const embed = new EmbedBuilder()
  .setColor(0x7B68EE)
  .setTitle('🎱 Magic 8-Ball')
  .addFields(
    { name: 'Question', value: question },
    { name: 'Answer', value: response }
  )
  .setTimestamp();

await interaction.reply({ embeds: [embed] });`},{name:"dice",description:"Roll a dice with specified sides",type:"slash",permissions:[],cooldown:2e3,enabled:!0,code:`const sides = interaction.options.getInteger('sides') || 6;

if (sides < 2 || sides > 100) {
  await interaction.reply({ content: 'Please specify a number of sides between 2 and 100!', ephemeral: true });
  return;
}

const result = Math.floor(Math.random() * sides) + 1;

const embed = new EmbedBuilder()
  .setColor(0x00D4AA)
  .setTitle('🎲 Dice Roll')
  .addFields(
    { name: 'Sides', value: sides.toString(), inline: true },
    { name: 'Result', value: result.toString(), inline: true }
  )
  .setTimestamp();

await interaction.reply({ embeds: [embed] });`}],events:[{name:"messageCreate",once:!1,code:`const message = args[0];
// React to messages containing certain keywords
if (message.author.bot) return;

const content = message.content.toLowerCase();
if (content.includes('good bot')) {
  message.react('❤️');
} else if (content.includes('bad bot')) {
  message.react('😢');
}`}],settings:{embedColor:"#7B68EE"}}},{id:"blank-template",name:"Blank Template",description:"Start from scratch with a minimal addon structure.",category:"Starter",config:{name:"my-custom-addon",version:"1.0.0",description:"My custom Discord bot addon built with the Addon Builder.",author:"",commands:[],events:[],settings:{embedColor:"#0099FF"}}}];async function l(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});if(!await (0,s.getServerSession)(e,t,d.authOptions))return t.status(401).json({error:"Unauthorized"});try{let n=e.query.category,i=m;n&&"all"!==n&&(i=m.filter(e=>e.category.toLowerCase()===n.toLowerCase()));let o=Array.from(new Set(m.map(e=>e.category)));t.status(200).json({templates:i,categories:o})}catch(e){t.status(500).json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"})}}let c=(0,r.M)(i,"default"),u=(0,r.M)(i,"config"),p=new o.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/experimental/addon-builder/templates",pathname:"/api/experimental/addon-builder/templates",bundlePath:"",filename:""},userland:i})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var n=e=>t(t.s=e),i=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>n(93757));module.exports=i})();