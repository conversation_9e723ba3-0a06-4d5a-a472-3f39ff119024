"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-7974549f"],{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    BuildError: function() {\n        return BuildError;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\"));\nconst _stripansi = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/compiled/strip-ansi */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/strip-ansi/index.js\"));\nconst _terminal = __webpack_require__(/*! ../components/terminal */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/terminal/index.js\");\nconst _erroroverlaylayout = __webpack_require__(/*! ../components/errors/error-overlay-layout/error-overlay-layout */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js\");\nconst getErrorTextFromBuildErrorMessage = (multiLineMessage)=>{\n    const lines = multiLineMessage.split('\\n');\n    // The multi-line build error message looks like:\n    // <file path>:<line number>:<column number>\n    // <error message>\n    // <error code frame of compiler or bundler>\n    // e.g.\n    // ./path/to/file.js:1:1\n    // SyntaxError: ...\n    // > 1 | con st foo =\n    // ...\n    return (0, _stripansi.default)(lines[1] || '');\n};\nconst BuildError = function BuildError(param) {\n    let { message, ...props } = param;\n    const noop = (0, _react.useCallback)(()=>{}, []);\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    const formattedMessage = (0, _react.useMemo)(()=>getErrorTextFromBuildErrorMessage(message) || 'Failed to compile', [\n        message\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_erroroverlaylayout.ErrorOverlayLayout, {\n        errorType: \"Build Error\",\n        errorMessage: formattedMessage,\n        onClose: noop,\n        error: error,\n        footerMessage: \"This error occurred during the build process and can only be dismissed by fixing the error.\",\n        ...props,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_terminal.Terminal, {\n            content: message\n        })\n    });\n};\n_c = BuildError;\nconst styles = \"\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=build-error.js.map\nvar _c;\n$RefreshReg$(_c, \"BuildError\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/build-error.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    Errors: function() {\n        return Errors;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nconst _overlay = __webpack_require__(/*! ../components/overlay */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/overlay/index.js\");\nconst _runtimeerror = __webpack_require__(/*! ./runtime-error */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js\");\nconst _errorsource = __webpack_require__(/*! ../../../../../shared/lib/error-source */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/shared/lib/error-source.js\");\nconst _hotlinkedtext = __webpack_require__(/*! ../components/hot-linked-text */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hot-linked-text/index.js\");\nconst _componentstackpseudohtml = __webpack_require__(/*! ./runtime-error/component-stack-pseudo-html */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js\");\nconst _hydrationerrorinfo = __webpack_require__(/*! ../../../errors/hydration-error-info */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/errors/hydration-error-info.js\");\nconst _consoleerror = __webpack_require__(/*! ../../../errors/console-error */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/errors/console-error.js\");\nconst _errortelemetryutils = __webpack_require__(/*! ../../../../../lib/error-telemetry-utils */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/error-telemetry-utils.js\");\nconst _erroroverlaylayout = __webpack_require__(/*! ../components/errors/error-overlay-layout/error-overlay-layout */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/error-overlay-layout/error-overlay-layout.js\");\nconst _ishydrationerror = __webpack_require__(/*! ../../../is-hydration-error */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/is-hydration-error.js\");\nfunction isNextjsLink(text) {\n    return text.startsWith('https://nextjs.org');\n}\nfunction ErrorDescription(param) {\n    let { error, hydrationWarning } = param;\n    const unhandledErrorType = (0, _consoleerror.isConsoleError)(error) ? (0, _consoleerror.getConsoleErrorType)(error) : null;\n    const isConsoleErrorStringMessage = unhandledErrorType === 'string';\n    // If the error is:\n    // - hydration warning\n    // - captured console error or unhandled rejection\n    // skip displaying the error name\n    const title = isConsoleErrorStringMessage || hydrationWarning ? '' : error.name + ': ';\n    const environmentName = 'environmentName' in error ? error.environmentName : '';\n    const envPrefix = environmentName ? \"[ \" + environmentName + \" ] \" : '';\n    // The environment name will be displayed as a label, so remove it\n    // from the message (e.g. \"[ Server ] hello world\" -> \"hello world\").\n    let message = error.message;\n    if (message.startsWith(envPrefix)) {\n        message = message.slice(envPrefix.length);\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            title,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                text: hydrationWarning || message,\n                matcher: isNextjsLink\n            })\n        ]\n    });\n}\n_c = ErrorDescription;\nfunction getErrorType(error) {\n    if ((0, _consoleerror.isConsoleError)(error)) {\n        return 'Console Error';\n    }\n    return 'Runtime Error';\n}\nfunction Errors(param) {\n    let { runtimeErrors, debugInfo, onClose, ...props } = param;\n    var _activeError_componentStackFrames;\n    const dialogResizerRef = (0, _react.useRef)(null);\n    (0, _react.useEffect)(()=>{\n        // Close the error overlay when pressing escape\n        function handleKeyDown(event) {\n            if (event.key === 'Escape') {\n                onClose();\n            }\n        }\n        document.addEventListener('keydown', handleKeyDown);\n        return ()=>document.removeEventListener('keydown', handleKeyDown);\n    }, [\n        onClose\n    ]);\n    const isLoading = (0, _react.useMemo)(()=>{\n        return runtimeErrors.length < 1;\n    }, [\n        runtimeErrors.length\n    ]);\n    const [activeIdx, setActiveIndex] = (0, _react.useState)(0);\n    const activeError = (0, _react.useMemo)(()=>{\n        var _runtimeErrors_activeIdx;\n        return (_runtimeErrors_activeIdx = runtimeErrors[activeIdx]) != null ? _runtimeErrors_activeIdx : null;\n    }, [\n        activeIdx,\n        runtimeErrors\n    ]);\n    if (isLoading) {\n        // TODO: better loading state\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_overlay.Overlay, {});\n    }\n    if (!activeError) {\n        return null;\n    }\n    const error = activeError.error;\n    const isServerError = [\n        'server',\n        'edge-server'\n    ].includes((0, _errorsource.getErrorSource)(error) || '');\n    const errorType = getErrorType(error);\n    const errorDetails = error.details || {};\n    const notes = errorDetails.notes || '';\n    const [warningTemplate, serverContent, clientContent] = errorDetails.warning || [\n        null,\n        '',\n        ''\n    ];\n    const hydrationErrorType = (0, _hydrationerrorinfo.getHydrationWarningType)(warningTemplate);\n    const hydrationWarning = warningTemplate ? warningTemplate.replace('%s', serverContent).replace('%s', clientContent).replace('%s', '') // remove the %s for stack\n    .replace(/%s$/, '') // If there's still a %s at the end, remove it\n    .replace(/^Warning: /, '').replace(/^Error: /, '') : null;\n    const errorCode = (0, _errortelemetryutils.extractNextErrorCode)(error);\n    const footerMessage = isServerError ? 'This error happened while generating the page. Any console logs will be displayed in the terminal window.' : undefined;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_erroroverlaylayout.ErrorOverlayLayout, {\n        errorCode: errorCode,\n        errorType: errorType,\n        errorMessage: /*#__PURE__*/ (0, _jsxruntime.jsx)(ErrorDescription, {\n            error: error,\n            hydrationWarning: hydrationWarning\n        }),\n        onClose: isServerError ? undefined : onClose,\n        debugInfo: debugInfo,\n        error: error,\n        runtimeErrors: runtimeErrors,\n        activeIdx: activeIdx,\n        setActiveIndex: setActiveIndex,\n        footerMessage: footerMessage,\n        dialogResizerRef: dialogResizerRef,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                className: \"error-overlay-notes-container\",\n                children: [\n                    notes ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                            id: \"nextjs__container_errors__notes\",\n                            className: \"nextjs__container_errors__notes\",\n                            children: notes\n                        })\n                    }) : null,\n                    hydrationWarning ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"p\", {\n                        id: \"nextjs__container_errors__link\",\n                        className: \"nextjs__container_errors__link\",\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hotlinkedtext.HotlinkedText, {\n                            text: \"See more info here: \" + _ishydrationerror.NEXTJS_HYDRATION_ERROR_LINK\n                        })\n                    }) : null\n                ]\n            }),\n            hydrationWarning && (((_activeError_componentStackFrames = activeError.componentStackFrames) == null ? void 0 : _activeError_componentStackFrames.length) || !!errorDetails.reactOutputComponentDiff) ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_componentstackpseudohtml.PseudoHtmlDiff, {\n                className: \"nextjs__container_errors__component-stack\",\n                hydrationMismatchType: hydrationErrorType,\n                firstContent: serverContent,\n                secondContent: clientContent,\n                reactOutputComponentDiff: errorDetails.reactOutputComponentDiff || ''\n            }) : null,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n                fallback: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                    \"data-nextjs-error-suspended\": true\n                }),\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_runtimeerror.RuntimeError, {\n                    error: activeError,\n                    dialogResizerRef: dialogResizerRef\n                }, activeError.id.toString())\n            })\n        ]\n    });\n}\n_c1 = Errors;\nconst styles = \"\\n  .nextjs-error-with-static {\\n    bottom: calc(16px * 4.5);\\n  }\\n  p.nextjs__container_errors__link {\\n    font-size: var(--size-14);\\n  }\\n  p.nextjs__container_errors__notes {\\n    color: var(--color-stack-notes);\\n    font-size: var(--size-14);\\n    line-height: 1.5;\\n  }\\n  .nextjs-container-errors-body > h2:not(:first-child) {\\n    margin-top: calc(16px + 8px);\\n  }\\n  .nextjs-container-errors-body > h2 {\\n    color: var(--color-title-color);\\n    margin-bottom: 8px;\\n    font-size: var(--size-20);\\n  }\\n  .nextjs-toast-errors-parent {\\n    cursor: pointer;\\n    transition: transform 0.2s ease;\\n  }\\n  .nextjs-toast-errors-parent:hover {\\n    transform: scale(1.1);\\n  }\\n  .nextjs-toast-errors {\\n    display: flex;\\n    align-items: center;\\n    justify-content: flex-start;\\n  }\\n  .nextjs-toast-errors > svg {\\n    margin-right: 8px;\\n  }\\n  .nextjs-toast-hide-button {\\n    margin-left: 24px;\\n    border: none;\\n    background: none;\\n    color: var(--color-ansi-bright-white);\\n    padding: 0;\\n    transition: opacity 0.25s ease;\\n    opacity: 0.7;\\n  }\\n  .nextjs-toast-hide-button:hover {\\n    opacity: 1;\\n  }\\n  .nextjs__container_errors_inspect_copy_button {\\n    cursor: pointer;\\n    background: none;\\n    border: none;\\n    color: var(--color-ansi-bright-white);\\n    font-size: var(--size-24);\\n    padding: 0;\\n    margin: 0;\\n    margin-left: 8px;\\n    transition: opacity 0.25s ease;\\n  }\\n  .nextjs__container_errors__error_title {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    margin-bottom: 14px;\\n  }\\n  .error-overlay-notes-container {\\n    margin: 8px 2px;\\n  }\\n  .error-overlay-notes-container p {\\n    white-space: pre-wrap;\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=errors.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"ErrorDescription\");\n$RefreshReg$(_c1, \"Errors\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/errors.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js":
/*!*************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js ***!
  \*************************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PSEUDO_HTML_DIFF_STYLES: function() {\n        return PSEUDO_HTML_DIFF_STYLES;\n    },\n    PseudoHtmlDiff: function() {\n        return _diffview.PseudoHtmlDiff;\n    }\n});\nconst _diffview = __webpack_require__(/*! ../../components/hydration-diff/diff-view */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/hydration-diff/diff-view.js\");\nconst PSEUDO_HTML_DIFF_STYLES = \"\\n  [data-nextjs-container-errors-pseudo-html] {\\n    padding: 8px 0;\\n    margin: 8px 0;\\n    border: 1px solid var(--color-gray-400);\\n    background: var(--color-background-200);\\n    color: var(--color-syntax-constant);\\n    font-family: var(--font-stack-monospace);\\n    font-size: var(--size-12);\\n    line-height: 1.33em; /* 16px in 12px font size */\\n    border-radius: var(--rounded-md-2);\\n  }\\n  [data-nextjs-container-errors-pseudo-html-line] {\\n    display: inline-block;\\n    width: 100%;\\n    padding-left: 40px;\\n    line-height: calc(5 / 3);\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='error'] {\\n    background: var(--color-amber-100);\\n    box-shadow: 2px 0 0 0 var(--color-amber-900) inset;\\n    font-weight: bold;\\n  }\\n  [data-nextjs-container-errors-pseudo-html-collapse-button] {\\n    all: unset;\\n    margin-left: 12px;\\n    &:focus {\\n      outline: none;\\n    }\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='add'] {\\n    background: var(--color-green-300);\\n  }\\n  [data-nextjs-container-errors-pseudo-html-line-sign] {\\n    margin-left: calc(24px * -1);\\n    margin-right: 24px;\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='add']\\n    [data-nextjs-container-errors-pseudo-html-line-sign] {\\n    color: var(--color-green-900);\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='remove'] {\\n    background: var(--color-red-300);\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='remove']\\n    [data-nextjs-container-errors-pseudo-html-line-sign] {\\n    color: var(--color-red-900);\\n    margin-left: calc(24px * -1);\\n    margin-right: 24px;\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff='error']\\n    [data-nextjs-container-errors-pseudo-html-line-sign] {\\n    color: var(--color-amber-900);\\n  }\\n  \\n  [data-nextjs-container-errors-pseudo-html--hint] {\\n    display: inline-block;\\n    font-size: 0;\\n    height: 0;\\n  }\\n  [data-nextjs-container-errors-pseudo-html--tag-adjacent='false'] {\\n    color: var(--color-accents-1);\\n  }\\n  .nextjs__container_errors__component-stack {\\n    margin: 0;\\n  }\\n  [data-nextjs-container-errors-pseudo-html-collapse='true']\\n    .nextjs__container_errors__component-stack\\n    code {\\n    max-height: 120px;\\n    mask-image: linear-gradient(to bottom,rgba(0,0,0,0) 0%,black 10%);\\n    padding-bottom: 40px;\\n  }\\n  .nextjs__container_errors__component-stack code {\\n    display: block;\\n    width: 100%;\\n    white-space: pre-wrap;\\n    scroll-snap-type: y mandatory;\\n    overflow-y: hidden;\\n  }\\n  [data-nextjs-container-errors-pseudo-html--diff] {\\n    scroll-snap-align: center;\\n  }\\n  .error-overlay-hydration-error-diff-plus-icon {\\n    color: var(--color-green-900);\\n  }\\n  .error-overlay-hydration-error-diff-minus-icon {\\n    color: var(--color-red-900);\\n  }\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=component-stack-pseudo-html.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js":
/*!***************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js ***!
  \***************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    RuntimeError: function() {\n        return RuntimeError;\n    },\n    styles: function() {\n        return styles;\n    }\n});\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nconst _codeframe = __webpack_require__(/*! ../../components/code-frame/code-frame */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/code-frame/code-frame.js\");\nconst _callstack = __webpack_require__(/*! ../../components/errors/call-stack/call-stack */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/components/errors/call-stack/call-stack.js\");\nconst _componentstackpseudohtml = __webpack_require__(/*! ./component-stack-pseudo-html */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/component-stack-pseudo-html.js\");\nconst _geterrorbytype = __webpack_require__(/*! ../../../utils/get-error-by-type */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js\");\nfunction RuntimeError(param) {\n    let { error, dialogResizerRef } = param;\n    const frames = (0, _geterrorbytype.useFrames)(error);\n    const firstFrame = (0, _react.useMemo)(()=>{\n        const firstFirstPartyFrameIndex = frames.findIndex((entry)=>!entry.ignored && Boolean(entry.originalCodeFrame) && Boolean(entry.originalStackFrame));\n        var _frames_firstFirstPartyFrameIndex;\n        return (_frames_firstFirstPartyFrameIndex = frames[firstFirstPartyFrameIndex]) != null ? _frames_firstFirstPartyFrameIndex : null;\n    }, [\n        frames\n    ]);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            firstFrame && /*#__PURE__*/ (0, _jsxruntime.jsx)(_codeframe.CodeFrame, {\n                stackFrame: firstFrame.originalStackFrame,\n                codeFrame: firstFrame.originalCodeFrame\n            }),\n            frames.length > 0 && /*#__PURE__*/ (0, _jsxruntime.jsx)(_callstack.CallStack, {\n                dialogResizerRef: dialogResizerRef,\n                frames: frames\n            })\n        ]\n    });\n}\n_c = RuntimeError;\nconst styles = \"\\n  \" + _componentstackpseudohtml.PSEUDO_HTML_DIFF_STYLES + \"\\n\";\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=index.js.map\nvar _c;\n$RefreshReg$(_c, \"RuntimeError\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/render-error.js":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/render-error.js ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"RenderError\", ({\n    enumerable: true,\n    get: function() {\n        return RenderError;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\nconst _shared = __webpack_require__(/*! ../../../shared */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/shared.js\");\nconst _geterrorbytype = __webpack_require__(/*! ../../../utils/get-error-by-type */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/utils/get-error-by-type.js\");\nfunction getErrorSignature(ev) {\n    const { event } = ev;\n    // eslint-disable-next-line default-case -- TypeScript checks this\n    switch(event.type){\n        case _shared.ACTION_UNHANDLED_ERROR:\n        case _shared.ACTION_UNHANDLED_REJECTION:\n            {\n                return event.reason.name + \"::\" + event.reason.message + \"::\" + event.reason.stack;\n            }\n    }\n}\nconst RenderError = (props)=>{\n    const { state } = props;\n    const isBuildError = !!state.buildError;\n    if (isBuildError) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(RenderBuildError, {\n            ...props\n        });\n    } else {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(RenderRuntimeError, {\n            ...props\n        });\n    }\n};\n_c = RenderError;\nconst RenderRuntimeError = (param)=>{\n    let { children, state, isAppDir } = param;\n    const { errors } = state;\n    const [lookups, setLookups] = (0, _react.useState)({});\n    const [runtimeErrors, nextError] = (0, _react.useMemo)(()=>{\n        let ready = [];\n        let next = null;\n        // Ensure errors are displayed in the order they occurred in:\n        for(let idx = 0; idx < errors.length; ++idx){\n            const e = errors[idx];\n            const { id } = e;\n            if (id in lookups) {\n                ready.push(lookups[id]);\n                continue;\n            }\n            // Check for duplicate errors\n            if (idx > 0) {\n                const prev = errors[idx - 1];\n                if (getErrorSignature(prev) === getErrorSignature(e)) {\n                    continue;\n                }\n            }\n            next = e;\n            break;\n        }\n        return [\n            ready,\n            next\n        ];\n    }, [\n        errors,\n        lookups\n    ]);\n    (0, _react.useEffect)(()=>{\n        if (nextError == null) {\n            return;\n        }\n        let mounted = true;\n        (0, _geterrorbytype.getErrorByType)(nextError, isAppDir).then((resolved)=>{\n            if (mounted) {\n                // We don't care if the desired error changed while we were resolving,\n                // thus we're not tracking it using a ref. Once the work has been done,\n                // we'll store it.\n                setLookups((m)=>({\n                        ...m,\n                        [resolved.id]: resolved\n                    }));\n            }\n        });\n        return ()=>{\n            mounted = false;\n        };\n    }, [\n        nextError,\n        isAppDir\n    ]);\n    const totalErrorCount = errors.filter((err, idx)=>{\n        const prev = errors[idx - 1];\n        // Check for duplicates\n        if (idx > 0) return getErrorSignature(prev) !== getErrorSignature(err);\n        return true;\n    }).length;\n    return children({\n        runtimeErrors,\n        totalErrorCount\n    });\n};\n_c1 = RenderRuntimeError;\nconst RenderBuildError = (param)=>{\n    let { children } = param;\n    return children({\n        runtimeErrors: [],\n        // Build errors and missing root layout tags persist until fixed,\n        // so we can set a fixed error count of 1\n        totalErrorCount: 1\n    });\n};\n_c2 = RenderBuildError;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=render-error.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"RenderError\");\n$RefreshReg$(_c1, \"RenderRuntimeError\");\n$RefreshReg$(_c2, \"RenderBuildError\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/react-dev-overlay/ui/container/runtime-error/render-error.js\n"));

/***/ })

}]);