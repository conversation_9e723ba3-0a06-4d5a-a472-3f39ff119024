"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1126],{31337:(e,o,r)=>{r.d(o,{A:()=>m});var l=r(94513),n=r(94285),s=r(6179),a=r(54881),t=r(98509),c=r(27263);let i=[{value:"sendMessage",label:"\uD83D\uDCAC Send Message",category:"Message"},{value:"sendEmbed",label:"\uD83D\uDCCB Send Embed",category:"Message"},{value:"editMessage",label:"✏️ Edit Message",category:"Message"},{value:"deleteMessage",label:"\uD83D\uDDD1️ Delete Message",category:"Message"},{value:"addReaction",label:"\uD83D\uDC4D Add Reaction",category:"Message"},{value:"removeReaction",label:"\uD83D\uDC4E Remove Reaction",category:"Message"},{value:"addRole",label:"\uD83C\uDFAD Add Role",category:"Roles"},{value:"removeRole",label:"\uD83C\uDFAD Remove Role",category:"Roles"},{value:"kickUser",label:"\uD83D\uDC62 Kick User",category:"Moderation"},{value:"banUser",label:"\uD83D\uDD28 Ban User",category:"Moderation"},{value:"timeoutUser",label:"⏰ Timeout User",category:"Moderation"},{value:"unbanUser",label:"\uD83D\uDD13 Unban User",category:"Moderation"},{value:"createChannel",label:"\uD83D\uDCFA Create Channel",category:"Channel"},{value:"deleteChannel",label:"\uD83D\uDDD1️ Delete Channel",category:"Channel"},{value:"lockChannel",label:"\uD83D\uDD12 Lock Channel",category:"Channel"},{value:"unlockChannel",label:"\uD83D\uDD13 Unlock Channel",category:"Channel"},{value:"sendDM",label:"\uD83D\uDCEC Send DM",category:"Message"},{value:"createThread",label:"\uD83E\uDDF5 Create Thread",category:"Channel"},{value:"pinMessage",label:"\uD83D\uDCCC Pin Message",category:"Message"},{value:"unpinMessage",label:"\uD83D\uDCCC Unpin Message",category:"Message"}],d={user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.tag}",description:"User Tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{user.mention}",description:"User Mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{user.avatar}",description:"Avatar URL",icon:"\uD83D\uDDBC️"},{name:"{user.createdAt}",description:"Account Creation Date",icon:"\uD83D\uDCC5"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"},{name:"{user.roles}",description:"User Roles",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions",icon:"\uD83D\uDD10"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel Mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel Topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{channel.createdAt}",description:"Channel Creation Date",icon:"\uD83D\uDCC5"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server Icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{server.createdAt}",description:"Server Creation Date",icon:"\uD83D\uDCC5"},{name:"{server.owner}",description:"Server Owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server Boost Level",icon:"\uD83D\uDE80"},{name:"{server.boostCount}",description:"Server Boost Count",icon:"\uD83D\uDC8E"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83C\uDD94"},{name:"{message.content}",description:"Message Content",icon:"\uD83D\uDCAC"},{name:"{message.author}",description:"Message Author",icon:"\uD83D\uDC64"},{name:"{message.channel}",description:"Message Channel",icon:"\uD83D\uDCFA"},{name:"{message.createdAt}",description:"Message Creation Date",icon:"\uD83D\uDCC5"},{name:"{message.editedAt}",description:"Message Edit Date",icon:"✏️"},{name:"{message.reactions}",description:"Message Reactions",icon:"\uD83D\uDC4D"},{name:"{message.attachments}",description:"Message Attachments",icon:"\uD83D\uDCCE"}],api:[{name:"{response.data}",description:"API Response Data",icon:"\uD83D\uDCCA"},{name:"{response.status}",description:"HTTP Status Code",icon:"\uD83D\uDD22"},{name:"{response.headers}",description:"Response Headers",icon:"\uD83D\uDCCB"},{name:"{response.message}",description:"Response Message",icon:"\uD83D\uDCAC"},{name:"{response.error}",description:"Error Message",icon:"❌"}],random:[{name:"{random.number}",description:"Random Number (1-100)",icon:"\uD83C\uDFB2"},{name:"{random.uuid}",description:"Random UUID",icon:"\uD83C\uDD94"},{name:"{random.choice}",description:"Random Choice from Array",icon:"\uD83C\uDFAF"},{name:"{random.color}",description:"Random Hex Color",icon:"\uD83C\uDFA8"}],date:[{name:"{date.now}",description:"Current Date/Time",icon:"⏰"},{name:"{date.today}",description:"Today's Date",icon:"\uD83D\uDCC5"},{name:"{date.timestamp}",description:"Unix Timestamp",icon:"\uD83D\uDD50"},{name:"{date.iso}",description:"ISO Date String",icon:"\uD83D\uDCDD"}]},h=(0,n.memo)(e=>{var o,r,h,m,x,u,b,p,g,j,v,C,S,f,y,k,T,F,D,M;let{data:R,selected:I,id:z,updateNodeData:L}=e,{currentScheme:w}=(0,c.DP)(),{isOpen:A,onOpen:U,onClose:B}=(0,a.useDisclosure)(),[V,H]=(0,n.useState)(()=>({embed:{fields:[],author:{name:""},footer:{text:""}},...R})),[N,E]=(0,n.useState)(!1),[W,O]=(0,n.useState)(!1),[P,_]=(0,n.useState)(null),[G,X]=(0,n.useState)(!1),Y=e=>{H(o=>({...o,...e}))},J=e=>{var o;return(null==(o=i.find(o=>o.value===e))?void 0:o.label)||e},K=e=>{navigator.clipboard.writeText(e)},q=async()=>{if(!P&&!G){X(!0);try{let e=await fetch("/api/admin/experimental/addon-builder/guild-data");if(e.ok){let o=await e.json();_({channels:o.channels,roles:o.roles,members:o.members})}}catch(e){}finally{X(!1)}}};(0,n.useEffect)(()=>{A&&q()},[A]);let Q=(e,o,r)=>{var l;let n=[...(null==(l=V.embed)?void 0:l.fields)||[]];n[e]={...n[e],[o]:r},Y({embed:{...V.embed,fields:n}})},Z=e=>{var o;let r=((null==(o=V.embed)?void 0:o.fields)||[]).filter((o,r)=>r!==e);Y({embed:{...V.embed,fields:r}})};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(a.Box,{bg:w.colors.surface,border:"2px solid ".concat(I?"#a855f7":w.colors.border),borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,l.jsx)(s.h7,{type:"target",position:s.yX.Top,style:{background:"#a855f7",border:"2px solid ".concat(w.colors.surface),width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,l.jsxs)(a.VStack,{spacing:1,align:"stretch",children:[(0,l.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,l.jsxs)(a.HStack,{spacing:1,children:[(0,l.jsx)(a.Box,{bg:"purple.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,l.jsx)(t.x_j,{})}),(0,l.jsx)(a.Text,{fontSize:"xs",fontWeight:"bold",color:w.colors.text,children:"Action"})]}),(0,l.jsx)(a.IconButton,{icon:(0,l.jsx)(t.VSk,{}),size:"xs",variant:"ghost",onClick:U,"aria-label":"Configure action"})]}),(0,l.jsx)(a.Box,{children:(0,l.jsxs)(a.HStack,{spacing:1,children:[V.actionType&&(0,l.jsx)(a.Text,{fontSize:"xs",children:(e=>{let o=i.find(o=>o.value===e);return(null==o?void 0:o.label.split(" ")[0])||"\uD83C\uDFAF"})(V.actionType)}),(0,l.jsx)(a.Text,{fontSize:"xs",color:w.colors.text,noOfLines:1,children:V.actionType?J(V.actionType).split(" ").slice(1).join(" "):"Select Action"})]})}),V.message&&(0,l.jsx)(a.Box,{children:(0,l.jsx)(a.Text,{fontSize:"xs",color:w.colors.textSecondary,noOfLines:1,children:V.message.length>20?V.message.substring(0,20)+"...":V.message})}),(0,l.jsxs)(a.HStack,{spacing:1,flexWrap:"wrap",children:[V.channel&&(0,l.jsxs)(a.Badge,{size:"xs",colorScheme:"purple",children:["#",V.channel]}),V.role&&(0,l.jsxs)(a.Badge,{size:"xs",colorScheme:"purple",children:["@",V.role]}),(null==(o=V.embed)?void 0:o.title)&&(0,l.jsx)(a.Badge,{size:"xs",colorScheme:"blue",children:"\uD83D\uDCCB Embed"})]})]})]}),(0,l.jsxs)(a.Modal,{isOpen:A,onClose:()=>{L&&z&&L(z,V),B()},size:"4xl",children:[(0,l.jsx)(a.ModalOverlay,{bg:"blackAlpha.600"}),(0,l.jsxs)(a.ModalContent,{bg:w.colors.background,border:"2px solid",borderColor:"purple.400",maxW:"1200px",children:[(0,l.jsx)(a.ModalHeader,{color:w.colors.text,children:"\uD83C\uDFAF Configure Action"}),(0,l.jsx)(a.ModalCloseButton,{}),(0,l.jsx)(a.ModalBody,{pb:6,children:(0,l.jsxs)(a.VStack,{spacing:6,align:"stretch",children:[(0,l.jsxs)(a.Box,{children:[(0,l.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,l.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:w.colors.text,children:"Available Variables"}),(0,l.jsxs)(a.Button,{size:"sm",variant:"ghost",leftIcon:N?(0,l.jsx)(t._NO,{}):(0,l.jsx)(t.Vap,{}),onClick:()=>E(!N),children:[N?"Hide":"Show"," Variables"]})]}),(0,l.jsxs)(a.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,l.jsx)(a.AlertIcon,{}),(0,l.jsx)(a.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your actions! Click any variable below to copy it. Variables are replaced with actual values when your addon runs."})]}),(0,l.jsx)(a.Collapse,{in:N,animateOpacity:!0,children:(0,l.jsx)(a.Box,{bg:w.colors.surface,border:"1px solid",borderColor:w.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,l.jsx)(a.Accordion,{allowMultiple:!0,children:Object.entries(d).map(e=>{let[o,r]=e;return(0,l.jsxs)(a.AccordionItem,{border:"none",children:[(0,l.jsxs)(a.AccordionButton,{px:0,py:2,children:[(0,l.jsx)(a.Box,{flex:"1",textAlign:"left",children:(0,l.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:w.colors.text,textTransform:"capitalize",children:[o," Variables"]})}),(0,l.jsx)(a.AccordionIcon,{})]}),(0,l.jsx)(a.AccordionPanel,{px:0,py:2,children:(0,l.jsx)(a.VStack,{spacing:2,align:"stretch",children:r.map(e=>(0,l.jsxs)(a.HStack,{spacing:2,p:2,bg:w.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:w.colors.surface},onClick:()=>K(e.name),children:[(0,l.jsx)(a.Text,{fontSize:"sm",children:e.icon}),(0,l.jsx)(a.Code,{fontSize:"xs",colorScheme:"blue",children:e.name}),(0,l.jsx)(a.Text,{fontSize:"xs",color:w.colors.textSecondary,flex:"1",children:e.description}),(0,l.jsx)(a.IconButton,{icon:(0,l.jsx)(t.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),K(e.name)}})]},e.name))})})]},o)})})})})]}),(0,l.jsx)(a.Divider,{}),(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Action Type"}),(0,l.jsx)(a.Select,{value:V.actionType||"",onChange:e=>Y({actionType:e.target.value}),placeholder:"Select an action type",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,children:Object.entries(i.reduce((e,o)=>(e[o.category]||(e[o.category]=[]),e[o.category].push(o),e),{})).map(e=>{let[o,r]=e;return(0,l.jsx)("optgroup",{label:o,children:r.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))},o)})})]}),V.actionType&&(0,l.jsx)(l.Fragment,{children:"sendEmbed"===V.actionType?(0,l.jsxs)(a.Tabs,{variant:"enclosed",colorScheme:"purple",children:[(0,l.jsxs)(a.TabList,{children:[(0,l.jsx)(a.Tab,{children:"Embed Builder"}),(0,l.jsx)(a.Tab,{children:"Preview"})]}),(0,l.jsxs)(a.TabPanels,{children:[(0,l.jsx)(a.TabPanel,{children:(0,l.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Channel"}),(0,l.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,l.jsx)(a.Select,{value:V.channel||"",onChange:e=>Y({channel:e.target.value}),placeholder:G?"Loading channels...":"Select a channel",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,isDisabled:G,children:null==P?void 0:P.channels.filter(e=>"text"===e.type).map(e=>(0,l.jsxs)("option",{value:e.name,children:["#",e.name]},e.id))}),(0,l.jsx)(a.Input,{value:V.channel||"",onChange:e=>Y({channel:e.target.value}),placeholder:"Or type: general or {channel.name}",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,size:"sm"})]})]}),(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Message Content (appears above embed)"}),(0,l.jsx)(a.Textarea,{value:V.message||"",onChange:e=>Y({message:e.target.value}),placeholder:"Hello {user.username}! This text appears above the embed...",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,minH:"100px"})]}),(0,l.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Embed Title"}),(0,l.jsx)(a.Input,{value:(null==(r=V.embed)?void 0:r.title)||"",onChange:e=>Y({embed:{...V.embed,title:e.target.value}}),placeholder:"Welcome to {server.name}!",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border})]}),(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Embed Color"}),(0,l.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,l.jsxs)(a.HStack,{spacing:2,children:[(0,l.jsx)(a.Input,{type:"color",value:(null==(h=V.embed)?void 0:h.color)||"#5865F2",onChange:e=>Y({embed:{...V.embed,color:e.target.value}}),w:"60px",h:"40px",p:1,bg:w.colors.background,borderColor:w.colors.border}),(0,l.jsx)(a.Input,{value:(null==(m=V.embed)?void 0:m.color)||"",onChange:e=>Y({embed:{...V.embed,color:e.target.value}}),placeholder:"#5865F2",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,flex:"1"})]}),(0,l.jsx)(a.HStack,{spacing:1,flexWrap:"wrap",children:["#5865F2","#57F287","#FEE75C","#EB459E","#ED4245","#FF6B35","#00ADB5","#9B59B6"].map(e=>(0,l.jsx)(a.Button,{size:"xs",bg:e,w:"30px",h:"20px",minW:"30px",p:0,onClick:()=>Y({embed:{...V.embed,color:e}}),_hover:{transform:"scale(1.1)"},border:"1px solid",borderColor:w.colors.border},e))})]})]})]}),(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Embed Description"}),(0,l.jsx)(a.Textarea,{value:(null==(x=V.embed)?void 0:x.description)||"",onChange:e=>Y({embed:{...V.embed,description:e.target.value}}),placeholder:"This is the description that appears inside the embed...",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,minH:"100px"})]}),(0,l.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Thumbnail URL"}),(0,l.jsx)(a.Input,{value:(null==(u=V.embed)?void 0:u.thumbnail)||"",onChange:e=>Y({embed:{...V.embed,thumbnail:e.target.value}}),placeholder:"https://example.com/image.png",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border})]}),(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Image URL"}),(0,l.jsx)(a.Input,{value:(null==(b=V.embed)?void 0:b.image)||"",onChange:e=>Y({embed:{...V.embed,image:e.target.value}}),placeholder:"https://example.com/image.png",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border})]})]}),(0,l.jsxs)(a.Box,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Author"}),(0,l.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,l.jsx)(a.Input,{value:(null==(g=V.embed)||null==(p=g.author)?void 0:p.name)||"",onChange:e=>{var o;return Y({embed:{...V.embed,author:{...null==(o=V.embed)?void 0:o.author,name:e.target.value}}})},placeholder:"Author name",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border}),(0,l.jsxs)(a.SimpleGrid,{columns:2,spacing:2,children:[(0,l.jsx)(a.Input,{value:(null==(v=V.embed)||null==(j=v.author)?void 0:j.url)||"",onChange:e=>{var o;return Y({embed:{...V.embed,author:{...null==(o=V.embed)?void 0:o.author,url:e.target.value}}})},placeholder:"Author URL",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border}),(0,l.jsx)(a.Input,{value:(null==(S=V.embed)||null==(C=S.author)?void 0:C.iconUrl)||"",onChange:e=>{var o;return Y({embed:{...V.embed,author:{...null==(o=V.embed)?void 0:o.author,iconUrl:e.target.value}}})},placeholder:"Author icon URL",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border})]})]})]}),(0,l.jsxs)(a.Box,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Footer"}),(0,l.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,l.jsx)(a.Input,{value:(null==(y=V.embed)||null==(f=y.footer)?void 0:f.text)||"",onChange:e=>{var o;return Y({embed:{...V.embed,footer:{...null==(o=V.embed)?void 0:o.footer,text:e.target.value}}})},placeholder:"Footer text",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border}),(0,l.jsx)(a.Input,{value:(null==(T=V.embed)||null==(k=T.footer)?void 0:k.iconUrl)||"",onChange:e=>{var o;return Y({embed:{...V.embed,footer:{...null==(o=V.embed)?void 0:o.footer,iconUrl:e.target.value}}})},placeholder:"Footer icon URL",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border})]})]}),(0,l.jsxs)(a.Box,{children:[(0,l.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,mb:0,children:"Embed Fields"}),(0,l.jsx)(a.Button,{size:"sm",leftIcon:(0,l.jsx)(t.GGD,{}),onClick:()=>{var e;let o=(null==(e=V.embed)?void 0:e.fields)||[];Y({embed:{...V.embed,fields:[...o,{name:"",value:"",inline:!1}]}})},colorScheme:"blue",children:"Add Field"})]}),(0,l.jsx)(a.VStack,{spacing:3,align:"stretch",children:null==(D=V.embed)||null==(F=D.fields)?void 0:F.map((e,o)=>(0,l.jsxs)(a.Box,{p:3,bg:w.colors.surface,borderRadius:"md",border:"1px solid",borderColor:w.colors.border,children:[(0,l.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,l.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:w.colors.text,children:["Field ",o+1]}),(0,l.jsx)(a.IconButton,{icon:(0,l.jsx)(t.IXo,{}),size:"xs",colorScheme:"red",variant:"ghost",onClick:()=>Z(o),"aria-label":"Remove field"})]}),(0,l.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,l.jsx)(a.Input,{value:e.name,onChange:e=>Q(o,"name",e.target.value),placeholder:"Field name",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border}),(0,l.jsx)(a.Textarea,{value:e.value,onChange:e=>Q(o,"value",e.target.value),placeholder:"Field value",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,minH:"80px"}),(0,l.jsxs)(a.HStack,{children:[(0,l.jsx)(a.Switch,{isChecked:e.inline||!1,onChange:e=>Q(o,"inline",e.target.checked),colorScheme:"purple"}),(0,l.jsx)(a.Text,{fontSize:"sm",color:w.colors.text,children:"Display inline"})]})]})]},o))})]}),(0,l.jsxs)(a.HStack,{children:[(0,l.jsx)(a.Switch,{isChecked:(null==(M=V.embed)?void 0:M.timestamp)||!1,onChange:e=>Y({embed:{...V.embed,timestamp:e.target.checked}}),colorScheme:"purple"}),(0,l.jsx)(a.Text,{fontSize:"sm",color:w.colors.text,children:"Show current timestamp"})]})]})}),(0,l.jsx)(a.TabPanel,{children:(0,l.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,l.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:w.colors.text,children:"Embed Preview"}),(0,l.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,l.jsx)(a.AlertIcon,{}),(0,l.jsx)(a.AlertDescription,{fontSize:"sm",children:"This shows your message content (above) and embed (below) as they will appear in Discord. Variables will be replaced with actual values when sent."})]}),(()=>{var e,o;let r=V.embed||{};return(0,l.jsxs)(a.VStack,{spacing:3,align:"stretch",maxW:"500px",children:[V.message&&(0,l.jsxs)(a.Box,{bg:w.colors.surface,border:"1px solid",borderColor:w.colors.border,borderRadius:"md",p:3,children:[(0,l.jsx)(a.Text,{fontSize:"sm",color:w.colors.text,fontWeight:"medium",children:"\uD83D\uDCE9 Message Content:"}),(0,l.jsx)(a.Text,{fontSize:"sm",color:w.colors.text,mt:1,children:V.message})]}),(0,l.jsxs)(a.Box,{bg:w.colors.surface,border:"1px solid",borderColor:w.colors.border,borderRadius:"md",p:4,borderLeft:"4px solid ".concat(r.color||"#5865F2"),children:[(null==(e=r.author)?void 0:e.name)&&(0,l.jsxs)(a.HStack,{spacing:2,mb:2,children:[r.author.iconUrl&&(0,l.jsx)(a.Box,{w:6,h:6,borderRadius:"full",bg:"gray.300",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"xs",children:"\uD83D\uDC64"}),(0,l.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:w.colors.text,children:r.author.name})]}),r.title&&(0,l.jsx)(a.Text,{fontSize:"md",fontWeight:"bold",color:w.colors.text,mb:2,children:r.title}),r.description&&(0,l.jsx)(a.Text,{fontSize:"sm",color:w.colors.textSecondary,mb:3,children:r.description}),r.fields&&r.fields.length>0&&(0,l.jsx)(a.VStack,{spacing:2,align:"stretch",mb:3,children:r.fields.map((e,o)=>(0,l.jsxs)(a.Box,{children:[(0,l.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:w.colors.text,children:e.name}),(0,l.jsx)(a.Text,{fontSize:"sm",color:w.colors.textSecondary,children:e.value})]},o))}),(null==(o=r.footer)?void 0:o.text)&&(0,l.jsxs)(a.HStack,{spacing:2,mt:3,pt:2,borderTop:"1px solid",borderColor:w.colors.border,children:[r.footer.iconUrl&&(0,l.jsx)(a.Box,{w:4,h:4,borderRadius:"full",bg:"gray.300",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"xs",children:"ℹ️"}),(0,l.jsx)(a.Text,{fontSize:"xs",color:w.colors.textSecondary,children:r.footer.text})]}),r.timestamp&&(0,l.jsxs)(a.Text,{fontSize:"xs",color:w.colors.textSecondary,mt:2,children:["\uD83D\uDD52 ",new Date().toLocaleString()]})]})]})})()]})})]})]}):(0,l.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[("sendMessage"===V.actionType||"sendDM"===V.actionType)&&(0,l.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Message Content"}),(0,l.jsx)(a.Textarea,{value:V.message||"",onChange:e=>Y({message:e.target.value}),placeholder:"Hello {user.username}! Welcome to {server.name}!",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,minH:"100px"})]}),"sendDM"!==V.actionType&&(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Channel"}),(0,l.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,l.jsx)(a.Select,{value:V.channel||"",onChange:e=>Y({channel:e.target.value}),placeholder:G?"Loading channels...":"Select a channel",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,isDisabled:G,children:null==P?void 0:P.channels.filter(e=>"text"===e.type).map(e=>(0,l.jsxs)("option",{value:e.name,children:["#",e.name]},e.id))}),(0,l.jsx)(a.Input,{value:V.channel||"",onChange:e=>Y({channel:e.target.value}),placeholder:"Or type: general or {channel.name}",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,size:"sm"})]})]})]}),("addRole"===V.actionType||"removeRole"===V.actionType)&&(0,l.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Role Name"}),(0,l.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,l.jsx)(a.Select,{value:V.role||"",onChange:e=>Y({role:e.target.value}),placeholder:G?"Loading roles...":"Select a role",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,isDisabled:G,children:null==P?void 0:P.roles.map(e=>(0,l.jsxs)("option",{value:e.name,children:["@",e.name]},e.id))}),(0,l.jsx)(a.Input,{value:V.role||"",onChange:e=>Y({role:e.target.value}),placeholder:"Or type: Member or {user.role}",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,size:"sm"})]})]}),(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Reason"}),(0,l.jsx)(a.Input,{value:V.reason||"",onChange:e=>Y({reason:e.target.value}),placeholder:"Role updated by bot",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border})]})]}),("kickUser"===V.actionType||"banUser"===V.actionType)&&(0,l.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"User"}),(0,l.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,l.jsx)(a.Select,{value:V.user||"",onChange:e=>Y({user:e.target.value}),placeholder:G?"Loading members...":"Select a user",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,isDisabled:G,children:null==P?void 0:P.members.map(e=>(0,l.jsxs)("option",{value:e.username,children:[e.displayName," (@",e.username,")"]},e.id))}),(0,l.jsx)(a.Input,{value:V.user||"",onChange:e=>Y({user:e.target.value}),placeholder:"Or type: username or {user.id}",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,size:"sm"})]})]}),(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Reason"}),(0,l.jsx)(a.Input,{value:V.reason||"",onChange:e=>Y({reason:e.target.value}),placeholder:"Violation of server rules",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border})]}),"banUser"===V.actionType&&(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Delete Message History"}),(0,l.jsx)(a.Switch,{isChecked:V.deleteMessages||!1,onChange:e=>Y({deleteMessages:e.target.checked}),colorScheme:"purple"})]})]}),"timeoutUser"===V.actionType&&(0,l.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"User"}),(0,l.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,l.jsx)(a.Select,{value:V.user||"",onChange:e=>Y({user:e.target.value}),placeholder:G?"Loading members...":"Select a user",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,isDisabled:G,children:null==P?void 0:P.members.map(e=>(0,l.jsxs)("option",{value:e.username,children:[e.displayName," (@",e.username,")"]},e.id))}),(0,l.jsx)(a.Input,{value:V.user||"",onChange:e=>Y({user:e.target.value}),placeholder:"Or type: username or {user.id}",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,size:"sm"})]})]}),(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Duration (minutes)"}),(0,l.jsxs)(a.NumberInput,{value:V.duration||10,onChange:e=>Y({duration:parseInt(e)||10}),min:1,max:40320,children:[(0,l.jsx)(a.NumberInputField,{bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border}),(0,l.jsxs)(a.NumberInputStepper,{children:[(0,l.jsx)(a.NumberIncrementStepper,{}),(0,l.jsx)(a.NumberDecrementStepper,{})]})]})]}),(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Reason"}),(0,l.jsx)(a.Input,{value:V.reason||"",onChange:e=>Y({reason:e.target.value}),placeholder:"Timeout for spam",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border})]})]}),"addReaction"===V.actionType&&(0,l.jsx)(a.VStack,{spacing:4,align:"stretch",children:(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Reaction (emoji)"}),(0,l.jsx)(a.Input,{value:V.reaction||"",onChange:e=>Y({reaction:e.target.value}),placeholder:"\uD83D\uDC4D or :thumbsup:",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border})]})}),"createChannel"===V.actionType&&(0,l.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Channel Name"}),(0,l.jsx)(a.Input,{value:V.channelName||"",onChange:e=>Y({channelName:e.target.value}),placeholder:"new-channel",bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border})]}),(0,l.jsxs)(a.FormControl,{children:[(0,l.jsx)(a.FormLabel,{color:w.colors.text,children:"Channel Type"}),(0,l.jsxs)(a.Select,{value:V.channelType||"text",onChange:e=>Y({channelType:e.target.value}),bg:w.colors.background,color:w.colors.text,borderColor:w.colors.border,children:[(0,l.jsx)("option",{value:"text",children:"Text Channel"}),(0,l.jsx)("option",{value:"voice",children:"Voice Channel"}),(0,l.jsx)("option",{value:"category",children:"Category"})]})]})]})]})}),(0,l.jsx)(a.Button,{colorScheme:"purple",onClick:()=>{R.actionType=V.actionType,R.message=V.message,R.channel=V.channel,R.role=V.role,R.user=V.user,R.embed=V.embed,R.reason=V.reason,R.duration=V.duration,R.deleteMessages=V.deleteMessages,R.reaction=V.reaction,R.channelName=V.channelName,R.channelType=V.channelType,R.label=V.actionType?J(V.actionType):"Action",B()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});h.displayName="ActionNode";let m=h}}]);