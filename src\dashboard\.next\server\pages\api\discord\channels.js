"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/discord/channels";
exports.ids = ["pages/api/discord/channels"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\discord\\channels.ts */ \"(api-node)/./pages/api/discord/channels.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/discord/channels\",\n        pathname: \"/api/discord/channels\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/discord/channels.ts":
/*!***************************************!*\
  !*** ./pages/api/discord/channels.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\n// Discord channel type mapping\nconst CHANNEL_TYPES = {\n    GUILD_TEXT: 0,\n    GUILD_VOICE: 2,\n    GUILD_CATEGORY: 4,\n    GUILD_ANNOUNCEMENT: 5,\n    GUILD_ANNOUNCEMENT_THREAD: 10,\n    GUILD_PUBLIC_THREAD: 11,\n    GUILD_PRIVATE_THREAD: 12,\n    GUILD_STAGE_VOICE: 13,\n    GUILD_FORUM: 15\n};\n// Reverse mapping for type conversion\nconst CHANNEL_TYPE_NAMES = {\n    0: 'GUILD_TEXT',\n    2: 'GUILD_VOICE',\n    4: 'GUILD_CATEGORY',\n    5: 'GUILD_ANNOUNCEMENT',\n    10: 'GUILD_ANNOUNCEMENT_THREAD',\n    11: 'GUILD_PUBLIC_THREAD',\n    12: 'GUILD_PRIVATE_THREAD',\n    13: 'GUILD_STAGE_VOICE',\n    15: 'GUILD_FORUM'\n};\nasync function handler(req, res) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session?.user) {\n            return res.status(401).json({\n                error: 'Unauthorized'\n            });\n        }\n        // For sensitive operations like creating channels we still require admin.\n        const isAdmin = session.user.isAdmin;\n        const { guildId, token } = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot;\n        if (!token || !guildId) {\n            console.error('Missing bot configuration');\n            return res.status(500).json({\n                error: 'Bot configuration missing'\n            });\n        }\n        if (req.method === 'GET') {\n            try {\n                const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/channels`, {\n                    headers: {\n                        Authorization: `Bot ${token}`\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error('Failed to fetch channels');\n                }\n                const channels = await response.json();\n                // Format channels based on the request source\n                const formattedChannels = channels.map((channel)=>{\n                    // Base channel format\n                    const formattedChannel = {\n                        id: channel.id,\n                        name: channel.name,\n                        type: CHANNEL_TYPE_NAMES[channel.type] || 'unknown',\n                        position: channel.position\n                    };\n                    // Add parent_id if it exists\n                    if (channel.parent_id) {\n                        formattedChannel.parent_id = channel.parent_id;\n                    }\n                    // Add raw type for the channel management page\n                    formattedChannel.raw_type = channel.type;\n                    // Add additional properties based on channel type\n                    if (channel.type === CHANNEL_TYPES.GUILD_TEXT) {\n                        formattedChannel.topic = channel.topic;\n                        formattedChannel.nsfw = channel.nsfw;\n                        formattedChannel.rate_limit_per_user = channel.rate_limit_per_user;\n                    } else if (channel.type === CHANNEL_TYPES.GUILD_VOICE) {\n                        formattedChannel.bitrate = channel.bitrate;\n                        formattedChannel.user_limit = channel.user_limit;\n                    }\n                    return formattedChannel;\n                });\n                return res.status(200).json(formattedChannels);\n            } catch (error) {\n                console.error('Error fetching channels:', error);\n                return res.status(500).json({\n                    error: 'Failed to fetch channels'\n                });\n            }\n        }\n        if (req.method === 'POST') {\n            // Creating channels requires admin permissions\n            if (!isAdmin) {\n                return res.status(403).json({\n                    error: 'Forbidden - Admin access required'\n                });\n            }\n            try {\n                const { name, type, topic, nsfw, bitrate, userLimit, parent, position, rateLimitPerUser } = req.body;\n                console.log('Received channel creation request:', req.body);\n                // Validate required fields\n                if (!name || typeof type !== 'number' && typeof type !== 'string') {\n                    console.log('Validation failed:', {\n                        name,\n                        type\n                    });\n                    return res.status(400).json({\n                        error: 'Name and type are required'\n                    });\n                }\n                // Convert string type to numeric if needed\n                let numericType = typeof type === 'number' ? type : CHANNEL_TYPES[type];\n                console.log('Channel type conversion:', {\n                    original: type,\n                    converted: numericType\n                });\n                if (typeof numericType !== 'number') {\n                    return res.status(400).json({\n                        error: 'Invalid channel type'\n                    });\n                }\n                // Prepare channel data based on type\n                const channelData = {\n                    name,\n                    type: numericType,\n                    position: position || 0\n                };\n                // Add type-specific properties\n                if (numericType === CHANNEL_TYPES.GUILD_TEXT) {\n                    if (topic) channelData.topic = topic;\n                    channelData.nsfw = Boolean(nsfw);\n                    if (typeof rateLimitPerUser === 'number' && !isNaN(rateLimitPerUser)) {\n                        channelData.rate_limit_per_user = rateLimitPerUser;\n                    }\n                } else if (numericType === CHANNEL_TYPES.GUILD_VOICE) {\n                    if (typeof bitrate === 'number' && !isNaN(bitrate)) {\n                        channelData.bitrate = bitrate;\n                    }\n                    if (typeof userLimit === 'number' && !isNaN(userLimit)) {\n                        channelData.user_limit = userLimit;\n                    }\n                }\n                // Add parent category if specified\n                if (parent && numericType !== CHANNEL_TYPES.GUILD_CATEGORY) {\n                    channelData.parent_id = parent;\n                }\n                console.log('Creating channel with data:', channelData);\n                const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/channels`, {\n                    method: 'POST',\n                    headers: {\n                        Authorization: `Bot ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(channelData)\n                });\n                if (!response.ok) {\n                    let error;\n                    try {\n                        error = await response.json();\n                    } catch  {\n                        error = await response.text();\n                    }\n                    console.error('Discord API error:', error);\n                    return res.status(response.status).json(error);\n                }\n                const newChannel = await response.json();\n                return res.status(201).json(newChannel);\n            } catch (error) {\n                console.error('Error creating channel:', error);\n                return res.status(500).json({\n                    error: 'Failed to create channel'\n                });\n            }\n        }\n        if (req.method === 'PATCH') {\n            // Editing channels requires admin permissions\n            if (!isAdmin) {\n                return res.status(403).json({\n                    error: 'Forbidden - Admin access required'\n                });\n            }\n            try {\n                const channelId = req.query.channelId;\n                if (!channelId) {\n                    return res.status(400).json({\n                        error: 'Channel ID is required'\n                    });\n                }\n                const response = await fetch(`https://discord.com/api/v10/channels/${channelId}`, {\n                    method: 'PATCH',\n                    headers: {\n                        Authorization: `Bot ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(req.body)\n                });\n                if (!response.ok) {\n                    let error;\n                    try {\n                        error = await response.json();\n                    } catch  {\n                        error = await response.text();\n                    }\n                    console.error('Discord API error:', error);\n                    return res.status(response.status).json(error);\n                }\n                const updatedChannel = await response.json();\n                return res.status(200).json(updatedChannel);\n            } catch (error) {\n                console.error('Error updating channel:', error);\n                return res.status(500).json({\n                    error: 'Failed to update channel'\n                });\n            }\n        }\n        if (req.method === 'DELETE') {\n            // Deleting channels requires admin permissions\n            if (!isAdmin) {\n                return res.status(403).json({\n                    error: 'Forbidden - Admin access required'\n                });\n            }\n            try {\n                const channelId = req.query.channelId;\n                if (!channelId) {\n                    return res.status(400).json({\n                        error: 'Channel ID is required'\n                    });\n                }\n                const response = await fetch(`https://discord.com/api/v10/channels/${channelId}`, {\n                    method: 'DELETE',\n                    headers: {\n                        Authorization: `Bot ${token}`\n                    }\n                });\n                if (!response.ok) {\n                    let error;\n                    try {\n                        error = await response.json();\n                    } catch  {\n                        error = await response.text();\n                    }\n                    console.error('Discord API error:', error);\n                    return res.status(response.status).json(error);\n                }\n                return res.status(200).json({\n                    message: 'Channel deleted successfully'\n                });\n            } catch (error) {\n                console.error('Error deleting channel:', error);\n                return res.status(500).json({\n                    error: 'Failed to delete channel'\n                });\n            }\n        }\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    } catch (error) {\n        console.error('Error in channel handler:', error);\n        return res.status(500).json({\n            error: 'Internal server error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/discord/channels.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();