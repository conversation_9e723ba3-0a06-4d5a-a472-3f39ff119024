"use strict";(()=>{var t={};t.id=5088,t.ids=[5088],t.modules={15806:t=>{t.exports=require("next-auth/next")},20396:t=>{t.exports=require("next-auth/providers/discord")},29021:t=>{t.exports=require("fs")},33873:t=>{t.exports=require("path")},42875:(t,e,r)=>{r.r(e),r.d(e,{config:()=>_,default:()=>h,routeModule:()=>f});var s={};r.r(s),r.d(s,{default:()=>p});var n=r(93433),a=r(20264),i=r(20584),o=r(15806),u=r(94506),d=r(98580);let l={GUILD_TEXT:0,GUILD_VOICE:2,GUILD_CATEGORY:4,GUILD_ANNOUNCEMENT:5,GUILD_ANNOUNCEMENT_THREAD:10,GUILD_PUBLIC_THREAD:11,GUILD_PRIVATE_THREAD:12,GUILD_STAGE_VOICE:13,GUILD_FORUM:15},c={0:"GUILD_TEXT",2:"GUILD_VOICE",4:"GUILD_CATEGORY",5:"GUILD_ANNOUNCEMENT",10:"GUILD_ANNOUNCEMENT_THREAD",11:"GUILD_PUBLIC_THREAD",12:"GUILD_PRIVATE_THREAD",13:"GUILD_STAGE_VOICE",15:"GUILD_FORUM"};async function p(t,e){try{let r=await (0,o.getServerSession)(t,e,u.authOptions);if(!r?.user)return e.status(401).json({error:"Unauthorized"});let s=r.user.isAdmin,{guildId:n,token:a}=d.dashboardConfig.bot;if(!a||!n)return e.status(500).json({error:"Bot configuration missing"});if("GET"===t.method)try{let t=await fetch(`https://discord.com/api/v10/guilds/${n}/channels`,{headers:{Authorization:`Bot ${a}`}});if(!t.ok)throw Error("Failed to fetch channels");let r=(await t.json()).map(t=>{let e={id:t.id,name:t.name,type:c[t.type]||"unknown",position:t.position};return t.parent_id&&(e.parent_id=t.parent_id),e.raw_type=t.type,t.type===l.GUILD_TEXT?(e.topic=t.topic,e.nsfw=t.nsfw,e.rate_limit_per_user=t.rate_limit_per_user):t.type===l.GUILD_VOICE&&(e.bitrate=t.bitrate,e.user_limit=t.user_limit),e});return e.status(200).json(r)}catch(t){return e.status(500).json({error:"Failed to fetch channels"})}if("POST"===t.method){if(!s)return e.status(403).json({error:"Forbidden - Admin access required"});try{let{name:r,type:s,topic:i,nsfw:o,bitrate:u,userLimit:d,parent:c,position:p,rateLimitPerUser:h}=t.body;if(!r||"number"!=typeof s&&"string"!=typeof s)return e.status(400).json({error:"Name and type are required"});let _="number"==typeof s?s:l[s];if("number"!=typeof _)return e.status(400).json({error:"Invalid channel type"});let f={name:r,type:_,position:p||0};_===l.GUILD_TEXT?(i&&(f.topic=i),f.nsfw=!!o,"number"!=typeof h||isNaN(h)||(f.rate_limit_per_user=h)):_===l.GUILD_VOICE&&("number"!=typeof u||isNaN(u)||(f.bitrate=u),"number"!=typeof d||isNaN(d)||(f.user_limit=d)),c&&_!==l.GUILD_CATEGORY&&(f.parent_id=c);let m=await fetch(`https://discord.com/api/v10/guilds/${n}/channels`,{method:"POST",headers:{Authorization:`Bot ${a}`,"Content-Type":"application/json"},body:JSON.stringify(f)});if(!m.ok){let t;try{t=await m.json()}catch{t=await m.text()}return e.status(m.status).json(t)}let I=await m.json();return e.status(201).json(I)}catch(t){return e.status(500).json({error:"Failed to create channel"})}}if("PATCH"===t.method){if(!s)return e.status(403).json({error:"Forbidden - Admin access required"});try{let r=t.query.channelId;if(!r)return e.status(400).json({error:"Channel ID is required"});let s=await fetch(`https://discord.com/api/v10/channels/${r}`,{method:"PATCH",headers:{Authorization:`Bot ${a}`,"Content-Type":"application/json"},body:JSON.stringify(t.body)});if(!s.ok){let t;try{t=await s.json()}catch{t=await s.text()}return e.status(s.status).json(t)}let n=await s.json();return e.status(200).json(n)}catch(t){return e.status(500).json({error:"Failed to update channel"})}}if("DELETE"===t.method){if(!s)return e.status(403).json({error:"Forbidden - Admin access required"});try{let r=t.query.channelId;if(!r)return e.status(400).json({error:"Channel ID is required"});let s=await fetch(`https://discord.com/api/v10/channels/${r}`,{method:"DELETE",headers:{Authorization:`Bot ${a}`}});if(!s.ok){let t;try{t=await s.json()}catch{t=await s.text()}return e.status(s.status).json(t)}return e.status(200).json({message:"Channel deleted successfully"})}catch(t){return e.status(500).json({error:"Failed to delete channel"})}}return e.status(405).json({error:"Method not allowed"})}catch(t){return e.status(500).json({error:"Internal server error"})}}let h=(0,i.M)(s,"default"),_=(0,i.M)(s,"config"),f=new n.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/discord/channels",pathname:"/api/discord/channels",bundlePath:"",filename:""},userland:s})},65542:t=>{t.exports=require("next-auth")},72115:t=>{t.exports=require("yaml")},75600:t=>{t.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var e=require("../../../webpack-api-runtime.js");e.C(t);var r=t=>e(e.s=t),s=e.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(42875));module.exports=s})();