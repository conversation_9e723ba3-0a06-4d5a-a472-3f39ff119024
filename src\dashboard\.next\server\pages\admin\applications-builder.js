"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/admin/applications-builder";
exports.ids = ["pages/admin/applications-builder"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Capplications-builder.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Capplications-builder.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\admin\\applications-builder.tsx */ \"(pages-dir-node)/./pages/admin/applications-builder.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/admin/applications-builder\",\n        pathname: \"/admin/applications-builder\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_admin_applications_builder_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjVfcmVhY3QtZG9tQDE5LjEuMF9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTJnBhZ2U9JTJGYWRtaW4lMkZhcHBsaWNhdGlvbnMtYnVpbGRlciZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTVDYWRtaW4lNUNhcHBsaWNhdGlvbnMtYnVpbGRlci50c3gmYWJzb2x1dGVBcHBQYXRoPXByaXZhdGUtbmV4dC1wYWdlcyUyRl9hcHAmYWJzb2x1dGVEb2N1bWVudFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2RvY3VtZW50Jm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdGO0FBQ2hDO0FBQ0U7QUFDMUQ7QUFDeUQ7QUFDVjtBQUMvQztBQUNxRTtBQUNyRTtBQUNBLGlFQUFlLHdFQUFLLENBQUMsa0VBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sdUJBQXVCLHdFQUFLLENBQUMsa0VBQVE7QUFDckMsdUJBQXVCLHdFQUFLLENBQUMsa0VBQVE7QUFDckMsMkJBQTJCLHdFQUFLLENBQUMsa0VBQVE7QUFDekMsZUFBZSx3RUFBSyxDQUFDLGtFQUFRO0FBQzdCLHdCQUF3Qix3RUFBSyxDQUFDLGtFQUFRO0FBQzdDO0FBQ08sZ0NBQWdDLHdFQUFLLENBQUMsa0VBQVE7QUFDOUMsZ0NBQWdDLHdFQUFLLENBQUMsa0VBQVE7QUFDOUMsaUNBQWlDLHdFQUFLLENBQUMsa0VBQVE7QUFDL0MsZ0NBQWdDLHdFQUFLLENBQUMsa0VBQVE7QUFDOUMsb0NBQW9DLHdFQUFLLENBQUMsa0VBQVE7QUFDekQ7QUFDTyx3QkFBd0Isa0dBQWdCO0FBQy9DO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxhQUFhLDhEQUFXO0FBQ3hCLGtCQUFrQixtRUFBZ0I7QUFDbEMsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVELGlDIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgYXBwIGFuZCBkb2N1bWVudCBtb2R1bGVzLlxuaW1wb3J0ICogYXMgZG9jdW1lbnQgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fZG9jdW1lbnRcIjtcbmltcG9ydCAqIGFzIGFwcCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19hcHBcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzXFxcXGFkbWluXFxcXGFwcGxpY2F0aW9ucy1idWlsZGVyLnRzeFwiO1xuLy8gUmUtZXhwb3J0IHRoZSBjb21wb25lbnQgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsICdkZWZhdWx0Jyk7XG4vLyBSZS1leHBvcnQgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U3RhdGljUHJvcHMnKTtcbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U3RhdGljUGF0aHMnKTtcbmV4cG9ydCBjb25zdCBnZXRTZXJ2ZXJTaWRlUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgJ2dldFNlcnZlclNpZGVQcm9wcycpO1xuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCAnY29uZmlnJyk7XG5leHBvcnQgY29uc3QgcmVwb3J0V2ViVml0YWxzID0gaG9pc3QodXNlcmxhbmQsICdyZXBvcnRXZWJWaXRhbHMnKTtcbi8vIFJlLWV4cG9ydCBsZWdhY3kgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUHJvcHMnKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUGF0aHMnKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXJhbXMgPSBob2lzdCh1c2VybGFuZCwgJ3Vuc3RhYmxlX2dldFN0YXRpY1BhcmFtcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclByb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTZXJ2ZXJQcm9wcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzJyk7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc1JvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFUyxcbiAgICAgICAgcGFnZTogXCIvYWRtaW4vYXBwbGljYXRpb25zLWJ1aWxkZXJcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FkbWluL2FwcGxpY2F0aW9ucy1idWlsZGVyXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJ1xuICAgIH0sXG4gICAgY29tcG9uZW50czoge1xuICAgICAgICAvLyBkZWZhdWx0IGV4cG9ydCBtaWdodCBub3QgZXhpc3Qgd2hlbiBvcHRpbWl6ZWQgZm9yIGRhdGEgb25seVxuICAgICAgICBBcHA6IGFwcC5kZWZhdWx0LFxuICAgICAgICBEb2N1bWVudDogZG9jdW1lbnQuZGVmYXVsdFxuICAgIH0sXG4gICAgdXNlcmxhbmRcbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWdlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Capplications-builder.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/admin/applications-builder.tsx":
/*!**********************************************!*\
  !*** ./pages/admin/applications-builder.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ApplicationsBuilder)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Card,CardBody,Divider,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,Menu,MenuButton,MenuItem,MenuList,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure,useToast!=!@chakra-ui/react */ \"(pages-dir-node)/__barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Card,CardBody,Divider,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,Menu,MenuButton,MenuItem,MenuList,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaChevronDown,FaClipboardList,FaCog,FaEdit,FaEye,FaLayerGroup,FaPalette,FaPlus,FaRocket,FaTrash,FaWrench!=!react-icons/fa */ \"(pages-dir-node)/__barrel_optimize__?names=FaChevronDown,FaClipboardList,FaCog,FaEdit,FaEye,FaLayerGroup,FaPalette,FaPlus,FaRocket,FaTrash,FaWrench!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/Layout */ \"(pages-dir-node)/./components/Layout.tsx\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Layout__WEBPACK_IMPORTED_MODULE_2__, _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_Layout__WEBPACK_IMPORTED_MODULE_2__, _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst QUESTION_TYPES = [\n    {\n        value: 'text',\n        label: 'Short Text',\n        icon: _barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaEdit\n    },\n    {\n        value: 'textarea',\n        label: 'Long Text',\n        icon: _barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaClipboardList\n    },\n    {\n        value: 'select',\n        label: 'Dropdown',\n        icon: _barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChevronDown\n    },\n    {\n        value: 'radio',\n        label: 'Multiple Choice',\n        icon: _barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaLayerGroup\n    },\n    {\n        value: 'checkbox',\n        label: 'Checkboxes',\n        icon: _barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaLayerGroup\n    },\n    {\n        value: 'number',\n        label: 'Number',\n        icon: _barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaWrench\n    },\n    {\n        value: 'email',\n        label: 'Email',\n        icon: _barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaRocket\n    }\n];\nconst COLOR_SCHEMES = [\n    'blue',\n    'green',\n    'red',\n    'purple',\n    'orange',\n    'pink',\n    'teal',\n    'cyan',\n    'yellow'\n];\nfunction ApplicationsBuilder() {\n    const [applications, setApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedApp, setSelectedApp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { isOpen, onOpen, onClose } = (0,_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useDisclosure)();\n    const { isOpen: isPreviewOpen, onOpen: onPreviewOpen, onClose: onPreviewClose } = (0,_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useDisclosure)();\n    const toast = (0,_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApplicationsBuilder.useEffect\": ()=>{\n            fetchApplications();\n        }\n    }[\"ApplicationsBuilder.useEffect\"], []);\n    const fetchApplications = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch('/api/admin/applications-builder');\n            if (response.ok) {\n                const data = await response.json();\n                setApplications(data.applications || []);\n            }\n        } catch (error) {\n            console.error('Error fetching applications:', error);\n            toast({\n                title: 'Error',\n                description: 'Failed to load applications',\n                status: 'error',\n                duration: 3000\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createNewApplication = ()=>{\n        const newApp = {\n            id: `app-${Date.now()}`,\n            title: 'New Application',\n            description: 'Description for new application',\n            color: 'blue',\n            icon: 'FaClipboardList',\n            enabled: false,\n            questions: [],\n            settings: {\n                allowMultipleSubmissions: false,\n                requireApproval: true,\n                autoResponse: false,\n                notificationChannels: []\n            }\n        };\n        console.log('Creating new application:', newApp);\n        setSelectedApp(newApp);\n        onOpen();\n    };\n    const createFromTemplate = (template)=>{\n        const newApp = {\n            ...template,\n            id: `app-${Date.now()}`,\n            enabled: false\n        };\n        setSelectedApp(newApp);\n        onOpen();\n    };\n    const editApplication = (app)=>{\n        setSelectedApp(app);\n        onOpen();\n    };\n    const deleteApplication = async (appId)=>{\n        try {\n            const response = await fetch(`/api/admin/applications-builder/${appId}`, {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                setApplications((prev)=>prev.filter((app)=>app.id !== appId));\n                toast({\n                    title: 'Success',\n                    description: 'Application deleted successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            console.error('Error deleting application:', error);\n            toast({\n                title: 'Error',\n                description: 'Failed to delete application',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const saveApplication = async (application)=>{\n        setIsSaving(true);\n        try {\n            console.log('Saving application:', application);\n            const response = await fetch('/api/admin/applications-builder', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(application)\n            });\n            console.log('Save response status:', response.status);\n            if (response.ok) {\n                const result = await response.json();\n                console.log('Save result:', result);\n                await fetchApplications();\n                onClose();\n                toast({\n                    title: 'Success',\n                    description: 'Application saved successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            } else {\n                const errorData = await response.json();\n                console.error('Save error response:', errorData);\n                throw new Error(errorData.error || 'Failed to save application');\n            }\n        } catch (error) {\n            console.error('Error saving application:', error);\n            toast({\n                title: 'Error',\n                description: error instanceof Error ? error.message : 'Failed to save application',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const toggleApplicationStatus = async (appId, enabled)=>{\n        try {\n            const app = applications.find((a)=>a.id === appId);\n            if (!app) {\n                console.error('Application not found:', appId);\n                return;\n            }\n            console.log('Toggling application status:', appId, 'to', enabled);\n            const updatedApp = {\n                ...app,\n                enabled\n            };\n            const response = await fetch('/api/admin/applications-builder', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(updatedApp)\n            });\n            console.log('Toggle response status:', response.status);\n            if (response.ok) {\n                await fetchApplications();\n                toast({\n                    title: 'Success',\n                    description: `Application ${enabled ? 'activated' : 'deactivated'} successfully`,\n                    status: 'success',\n                    duration: 3000\n                });\n            } else {\n                const errorData = await response.json();\n                console.error('Toggle error response:', errorData);\n                throw new Error(errorData.error || 'Failed to update application status');\n            }\n        } catch (error) {\n            console.error('Error toggling application status:', error);\n            toast({\n                title: 'Error',\n                description: error instanceof Error ? error.message : 'Failed to update application status',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                p: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                    align: \"stretch\",\n                    spacing: 6,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                            justify: \"space-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                    align: \"start\",\n                                    spacing: 1,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                                            size: \"lg\",\n                                            children: \"Applications Builder\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            color: \"gray.600\",\n                                            _dark: {\n                                                color: 'gray.300'\n                                            },\n                                            children: \"Create and manage custom application forms for your server\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    colorScheme: \"blue\",\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaPlus, {}, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    onClick: createNewApplication,\n                                    children: \"Create Application\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                            index: activeTab,\n                            onChange: setActiveTab,\n                            variant: \"enclosed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.TabList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                            children: \"Applications\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                            children: \"Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                            children: \"Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.TabPanels, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                                align: \"stretch\",\n                                                spacing: 4,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                                        status: \"info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.AlertIcon, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                children: \"Build custom application forms with drag-and-drop ease. Create different types of applications for your server members.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            md: 2,\n                                                            lg: 3\n                                                        },\n                                                        spacing: 4,\n                                                        children: applications.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                                borderTop: \"4px solid\",\n                                                                borderTopColor: `${app.color}.500`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.CardBody, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                                                        align: \"stretch\",\n                                                                        spacing: 3,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                                                                justify: \"space-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                                                                        align: \"start\",\n                                                                                        spacing: 1,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                                                                                                size: \"md\",\n                                                                                                children: app.title\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                                lineNumber: 304,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                                                fontSize: \"sm\",\n                                                                                                color: \"gray.600\",\n                                                                                                _dark: {\n                                                                                                    color: 'gray.400'\n                                                                                                },\n                                                                                                children: app.description\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                                lineNumber: 305,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                        lineNumber: 303,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                                        colorScheme: app.enabled ? 'green' : 'gray',\n                                                                                        children: app.enabled ? 'Active' : 'Inactive'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                        lineNumber: 309,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                                fontSize: \"sm\",\n                                                                                color: \"gray.500\",\n                                                                                children: [\n                                                                                    app.questions.length,\n                                                                                    \" questions configured\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 314,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                                                                spacing: 2,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        colorScheme: \"blue\",\n                                                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaEdit, {}, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                            lineNumber: 322,\n                                                                                            columnNumber: 43\n                                                                                        }, void 0),\n                                                                                        onClick: ()=>editApplication(app),\n                                                                                        children: \"Edit\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                        lineNumber: 319,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                                        size: \"sm\",\n                                                                                        variant: \"outline\",\n                                                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaEye, {}, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                            lineNumber: 330,\n                                                                                            columnNumber: 43\n                                                                                        }, void 0),\n                                                                                        onClick: ()=>{\n                                                                                            setSelectedApp(app);\n                                                                                            onPreviewOpen();\n                                                                                        },\n                                                                                        children: \"Preview\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                        lineNumber: 327,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Menu, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.MenuButton, {\n                                                                                                as: _barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton,\n                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaCog, {}, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                                    lineNumber: 339,\n                                                                                                    columnNumber: 67\n                                                                                                }, void 0),\n                                                                                                size: \"sm\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                                lineNumber: 339,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.MenuList, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.MenuItem, {\n                                                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaCog, {}, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                                            lineNumber: 342,\n                                                                                                            columnNumber: 43\n                                                                                                        }, void 0),\n                                                                                                        onClick: ()=>toggleApplicationStatus(app.id, !app.enabled),\n                                                                                                        children: app.enabled ? 'Deactivate' : 'Activate'\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                                        lineNumber: 341,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.MenuItem, {\n                                                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrash, {}, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                                            lineNumber: 348,\n                                                                                                            columnNumber: 43\n                                                                                                        }, void 0),\n                                                                                                        color: \"red.500\",\n                                                                                                        onClick: ()=>deleteApplication(app.id),\n                                                                                                        children: \"Delete\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                                        lineNumber: 347,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                                lineNumber: 340,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                        lineNumber: 338,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 318,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, app.id, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                                align: \"stretch\",\n                                                spacing: 4,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                                        status: \"info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.AlertIcon, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                children: \"Pre-built templates to get you started quickly. Choose from common application types.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            md: 2,\n                                                            lg: 3\n                                                        },\n                                                        spacing: 4,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TemplateGallery, {\n                                                            onSelectTemplate: createFromTemplate\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                                align: \"stretch\",\n                                                spacing: 4,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                                        status: \"info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.AlertIcon, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                children: \"Global settings for all applications. Configure defaults and system-wide preferences.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.CardBody, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                                                align: \"stretch\",\n                                                                spacing: 4,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                                                                        size: \"md\",\n                                                                        children: \"Global Settings\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                children: \"Default Auto-Response\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 396,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Switch, {}, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 397,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                children: \"Require Email Verification\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Switch, {}, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                children: \"Application Cooldown (days)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                type: \"number\",\n                                                                                defaultValue: 30\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 405,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                isOpen: isOpen,\n                onClose: onClose,\n                size: \"6xl\",\n                scrollBehavior: \"inside\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalOverlay, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Icon, {\n                                            as: _barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaPalette,\n                                            color: \"blue.500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            children: [\n                                                selectedApp?.id.startsWith('app-') ? 'Create' : 'Edit',\n                                                \" Application\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalCloseButton, {}, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalBody, {\n                                children: selectedApp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ApplicationEditor, {\n                                    application: selectedApp,\n                                    onSave: saveApplication,\n                                    onCancel: onClose,\n                                    isSaving: isSaving\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                isOpen: isPreviewOpen,\n                onClose: onPreviewClose,\n                size: \"4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalOverlay, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Icon, {\n                                            as: _barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaEye,\n                                            color: \"green.500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            children: [\n                                                \"Preview: \",\n                                                selectedApp?.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalCloseButton, {}, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalBody, {\n                                children: selectedApp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ApplicationPreview, {\n                                    application: selectedApp\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.ModalFooter, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: onPreviewClose,\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                lineNumber: 442,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, this);\n}\n// Application Editor Component\nfunction ApplicationEditor({ application, onSave, onCancel, isSaving = false }) {\n    const [app, setApp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(application);\n    const [activeEditorTab, setActiveEditorTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const updateApp = (updates)=>{\n        setApp((prev)=>({\n                ...prev,\n                ...updates\n            }));\n    };\n    const addQuestion = ()=>{\n        const newQuestion = {\n            id: `q-${Date.now()}`,\n            type: 'text',\n            label: 'New Question',\n            required: false\n        };\n        setApp((prev)=>({\n                ...prev,\n                questions: [\n                    ...prev.questions,\n                    newQuestion\n                ]\n            }));\n    };\n    const updateQuestion = (questionId, updates)=>{\n        setApp((prev)=>({\n                ...prev,\n                questions: prev.questions.map((q)=>q.id === questionId ? {\n                        ...q,\n                        ...updates\n                    } : q)\n            }));\n    };\n    const deleteQuestion = (questionId)=>{\n        setApp((prev)=>({\n                ...prev,\n                questions: prev.questions.filter((q)=>q.id !== questionId)\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n        align: \"stretch\",\n        spacing: 4,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                index: activeEditorTab,\n                onChange: setActiveEditorTab,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.TabList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                children: \"Basic Info\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                children: \"Questions\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Tab, {\n                                children: \"Settings\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.TabPanels, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.TabPanel, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                    align: \"stretch\",\n                                    spacing: 4,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                    children: \"Application Title\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                    value: app.title,\n                                                    onChange: (e)=>updateApp({\n                                                            title: e.target.value\n                                                        }),\n                                                    placeholder: \"Enter application title\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                    value: app.description,\n                                                    onChange: (e)=>updateApp({\n                                                            description: e.target.value\n                                                        }),\n                                                    placeholder: \"Enter application description\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                    children: \"Color Scheme\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                                    spacing: 2,\n                                                    children: COLOR_SCHEMES.map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Box, {\n                                                            w: 8,\n                                                            h: 8,\n                                                            bg: `${color}.500`,\n                                                            rounded: \"md\",\n                                                            cursor: \"pointer\",\n                                                            border: app.color === color ? \"3px solid\" : \"1px solid\",\n                                                            borderColor: app.color === color ? \"white\" : \"gray.300\",\n                                                            onClick: ()=>updateApp({\n                                                                    color\n                                                                })\n                                                        }, color, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Switch, {\n                                                            isChecked: app.enabled,\n                                                            onChange: (e)=>updateApp({\n                                                                    enabled: e.target.checked\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                            children: app.enabled ? 'Active' : 'Inactive'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.TabPanel, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                    align: \"stretch\",\n                                    spacing: 4,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                            justify: \"space-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    fontWeight: \"bold\",\n                                                    children: [\n                                                        \"Questions (\",\n                                                        app.questions.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    size: \"sm\",\n                                                    colorScheme: \"blue\",\n                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaPlus, {}, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 29\n                                                    }, void 0),\n                                                    onClick: addQuestion,\n                                                    children: \"Add Question\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 15\n                                        }, this),\n                                        app.questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                                borderLeft: \"4px solid\",\n                                                borderLeftColor: \"blue.500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.CardBody, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                                        align: \"stretch\",\n                                                        spacing: 3,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                                                justify: \"space-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                        flex: 1,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                children: \"Question Label\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 598,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                value: question.label,\n                                                                                onChange: (e)=>updateQuestion(question.id, {\n                                                                                        label: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"Enter question label\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 599,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                        lineNumber: 597,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                        w: \"200px\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                                children: \"Type\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 606,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                                value: question.type,\n                                                                                onChange: (e)=>updateQuestion(question.id, {\n                                                                                        type: e.target.value\n                                                                                    }),\n                                                                                children: QUESTION_TYPES.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: type.value,\n                                                                                        children: type.label\n                                                                                    }, type.value, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                        lineNumber: 612,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 607,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                                                        \"aria-label\": \"Delete question\",\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrash, {}, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                            lineNumber: 620,\n                                                                            columnNumber: 33\n                                                                        }, void 0),\n                                                                        colorScheme: \"red\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>deleteQuestion(question.id)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                        children: \"Placeholder\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                        value: question.placeholder || '',\n                                                                        onChange: (e)=>updateQuestion(question.id, {\n                                                                                placeholder: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Enter placeholder text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Switch, {\n                                                                                isChecked: question.required,\n                                                                                onChange: (e)=>updateQuestion(question.id, {\n                                                                                        required: e.target.checked\n                                                                                    })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 639,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                                children: \"Required\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                        lineNumber: 638,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, question.id, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.TabPanel, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                    align: \"stretch\",\n                                    spacing: 4,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Switch, {\n                                                        isChecked: app.settings.allowMultipleSubmissions,\n                                                        onChange: (e)=>updateApp({\n                                                                settings: {\n                                                                    ...app.settings,\n                                                                    allowMultipleSubmissions: e.target.checked\n                                                                }\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        children: \"Allow Multiple Submissions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Switch, {\n                                                        isChecked: app.settings.requireApproval,\n                                                        onChange: (e)=>updateApp({\n                                                                settings: {\n                                                                    ...app.settings,\n                                                                    requireApproval: e.target.checked\n                                                                }\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        children: \"Require Manual Approval\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Switch, {\n                                                        isChecked: app.settings.autoResponse,\n                                                        onChange: (e)=>updateApp({\n                                                                settings: {\n                                                                    ...app.settings,\n                                                                    autoResponse: e.target.checked\n                                                                }\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        children: \"Send Auto-Response\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                justify: \"flex-end\",\n                spacing: 4,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        onClick: onCancel,\n                        isDisabled: isSaving,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 698,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        colorScheme: \"blue\",\n                        onClick: ()=>onSave(app),\n                        isLoading: isSaving,\n                        loadingText: \"Saving...\",\n                        children: \"Save Application\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 701,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                lineNumber: 697,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n        lineNumber: 515,\n        columnNumber: 5\n    }, this);\n}\n// Template Gallery Component\nfunction TemplateGallery({ onSelectTemplate }) {\n    const templates = [\n        {\n            id: 'template-moderator',\n            title: 'Moderator Application',\n            description: 'Standard moderator application with experience and scenario questions',\n            color: 'blue',\n            icon: 'FaUserShield',\n            enabled: false,\n            questions: [\n                {\n                    id: 'q1',\n                    type: 'text',\n                    label: 'What is your Discord username?',\n                    required: true\n                },\n                {\n                    id: 'q2',\n                    type: 'number',\n                    label: 'How old are you?',\n                    required: true\n                },\n                {\n                    id: 'q3',\n                    type: 'text',\n                    label: 'What timezone are you in?',\n                    required: true\n                },\n                {\n                    id: 'q4',\n                    type: 'number',\n                    label: 'How many hours per week can you dedicate to moderation?',\n                    required: true\n                },\n                {\n                    id: 'q5',\n                    type: 'textarea',\n                    label: 'Why do you want to be a moderator?',\n                    required: true\n                },\n                {\n                    id: 'q6',\n                    type: 'textarea',\n                    label: 'Do you have any previous moderation experience?',\n                    required: false\n                },\n                {\n                    id: 'q7',\n                    type: 'textarea',\n                    label: 'How would you handle a heated argument between two members?',\n                    required: true\n                }\n            ],\n            settings: {\n                allowMultipleSubmissions: false,\n                requireApproval: true,\n                autoResponse: true,\n                notificationChannels: []\n            }\n        },\n        {\n            id: 'template-developer',\n            title: 'Developer Application',\n            description: 'Technical application for developer positions',\n            color: 'green',\n            icon: 'FaCode',\n            enabled: false,\n            questions: [\n                {\n                    id: 'q1',\n                    type: 'text',\n                    label: 'Full Name',\n                    required: true\n                },\n                {\n                    id: 'q2',\n                    type: 'email',\n                    label: 'Email Address',\n                    required: true\n                },\n                {\n                    id: 'q3',\n                    type: 'textarea',\n                    label: 'Tell us about your programming experience',\n                    required: true\n                },\n                {\n                    id: 'q4',\n                    type: 'select',\n                    label: 'Primary Programming Language',\n                    required: true,\n                    options: [\n                        'JavaScript',\n                        'Python',\n                        'Java',\n                        'C#',\n                        'Go',\n                        'Other'\n                    ]\n                },\n                {\n                    id: 'q5',\n                    type: 'textarea',\n                    label: 'Describe a challenging project you worked on',\n                    required: true\n                },\n                {\n                    id: 'q6',\n                    type: 'text',\n                    label: 'GitHub/Portfolio URL',\n                    required: false\n                },\n                {\n                    id: 'q7',\n                    type: 'radio',\n                    label: 'Are you available for full-time work?',\n                    required: true,\n                    options: [\n                        'Yes',\n                        'No',\n                        'Part-time only'\n                    ]\n                }\n            ],\n            settings: {\n                allowMultipleSubmissions: true,\n                requireApproval: true,\n                autoResponse: true,\n                notificationChannels: []\n            }\n        },\n        {\n            id: 'template-event-host',\n            title: 'Event Host Application',\n            description: 'Application for community event organizers',\n            color: 'purple',\n            icon: 'FaCalendar',\n            enabled: false,\n            questions: [\n                {\n                    id: 'q1',\n                    type: 'text',\n                    label: 'Discord Username',\n                    required: true\n                },\n                {\n                    id: 'q2',\n                    type: 'text',\n                    label: 'Preferred Name',\n                    required: true\n                },\n                {\n                    id: 'q3',\n                    type: 'textarea',\n                    label: 'What types of events would you like to host?',\n                    required: true\n                },\n                {\n                    id: 'q4',\n                    type: 'textarea',\n                    label: 'Do you have experience organizing events?',\n                    required: false\n                },\n                {\n                    id: 'q5',\n                    type: 'select',\n                    label: 'How often would you like to host events?',\n                    required: true,\n                    options: [\n                        'Weekly',\n                        'Bi-weekly',\n                        'Monthly',\n                        'As needed'\n                    ]\n                },\n                {\n                    id: 'q6',\n                    type: 'textarea',\n                    label: 'Describe an event idea you have',\n                    required: true\n                }\n            ],\n            settings: {\n                allowMultipleSubmissions: false,\n                requireApproval: true,\n                autoResponse: true,\n                notificationChannels: []\n            }\n        },\n        {\n            id: 'template-support',\n            title: 'Support Team Application',\n            description: 'Customer support and help desk application',\n            color: 'orange',\n            icon: 'FaHeadset',\n            enabled: false,\n            questions: [\n                {\n                    id: 'q1',\n                    type: 'text',\n                    label: 'Discord Username',\n                    required: true\n                },\n                {\n                    id: 'q2',\n                    type: 'text',\n                    label: 'Age',\n                    required: true\n                },\n                {\n                    id: 'q3',\n                    type: 'text',\n                    label: 'Timezone',\n                    required: true\n                },\n                {\n                    id: 'q4',\n                    type: 'textarea',\n                    label: 'Why do you want to join the support team?',\n                    required: true\n                },\n                {\n                    id: 'q5',\n                    type: 'checkbox',\n                    label: 'Which areas can you help with?',\n                    required: true,\n                    options: [\n                        'Technical Issues',\n                        'Account Problems',\n                        'General Questions',\n                        'Bug Reports',\n                        'Feature Requests'\n                    ]\n                },\n                {\n                    id: 'q6',\n                    type: 'textarea',\n                    label: 'How would you help a frustrated user?',\n                    required: true\n                }\n            ],\n            settings: {\n                allowMultipleSubmissions: false,\n                requireApproval: true,\n                autoResponse: true,\n                notificationChannels: []\n            }\n        },\n        {\n            id: 'template-content',\n            title: 'Content Creator Application',\n            description: 'Application for content creators and influencers',\n            color: 'pink',\n            icon: 'FaVideo',\n            enabled: false,\n            questions: [\n                {\n                    id: 'q1',\n                    type: 'text',\n                    label: 'Creator Name/Handle',\n                    required: true\n                },\n                {\n                    id: 'q2',\n                    type: 'email',\n                    label: 'Contact Email',\n                    required: true\n                },\n                {\n                    id: 'q3',\n                    type: 'select',\n                    label: 'Primary Content Platform',\n                    required: true,\n                    options: [\n                        'YouTube',\n                        'Twitch',\n                        'TikTok',\n                        'Instagram',\n                        'Twitter',\n                        'Other'\n                    ]\n                },\n                {\n                    id: 'q4',\n                    type: 'text',\n                    label: 'Channel/Profile URL',\n                    required: true\n                },\n                {\n                    id: 'q5',\n                    type: 'textarea',\n                    label: 'What type of content do you create?',\n                    required: true\n                },\n                {\n                    id: 'q6',\n                    type: 'number',\n                    label: 'How many followers/subscribers do you have?',\n                    required: false\n                },\n                {\n                    id: 'q7',\n                    type: 'textarea',\n                    label: 'How would you promote our community?',\n                    required: true\n                }\n            ],\n            settings: {\n                allowMultipleSubmissions: true,\n                requireApproval: true,\n                autoResponse: true,\n                notificationChannels: []\n            }\n        },\n        {\n            id: 'template-beta',\n            title: 'Beta Tester Application',\n            description: 'Application for beta testing programs',\n            color: 'teal',\n            icon: 'FaFlask',\n            enabled: false,\n            questions: [\n                {\n                    id: 'q1',\n                    type: 'text',\n                    label: 'Discord Username',\n                    required: true\n                },\n                {\n                    id: 'q2',\n                    type: 'textarea',\n                    label: 'What interests you about beta testing?',\n                    required: true\n                },\n                {\n                    id: 'q3',\n                    type: 'textarea',\n                    label: 'Do you have experience finding and reporting bugs?',\n                    required: false\n                },\n                {\n                    id: 'q4',\n                    type: 'select',\n                    label: 'How much time can you dedicate to testing?',\n                    required: true,\n                    options: [\n                        '1-2 hours/week',\n                        '3-5 hours/week',\n                        '6-10 hours/week',\n                        '10+ hours/week'\n                    ]\n                },\n                {\n                    id: 'q5',\n                    type: 'checkbox',\n                    label: 'Which platforms do you have access to?',\n                    required: true,\n                    options: [\n                        'Windows',\n                        'Mac',\n                        'Linux',\n                        'iOS',\n                        'Android',\n                        'Web Browser'\n                    ]\n                }\n            ],\n            settings: {\n                allowMultipleSubmissions: false,\n                requireApproval: true,\n                autoResponse: true,\n                notificationChannels: []\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                borderTop: \"4px solid\",\n                borderTopColor: `${template.color}.500`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.CardBody, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                        align: \"stretch\",\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                                align: \"start\",\n                                spacing: 1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                                        size: \"md\",\n                                        children: template.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                        lineNumber: 830,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.600\",\n                                        _dark: {\n                                            color: 'gray.400'\n                                        },\n                                        children: template.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                        lineNumber: 831,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 829,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                fontSize: \"sm\",\n                                color: \"gray.500\",\n                                children: [\n                                    template.questions.length,\n                                    \" pre-configured questions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 836,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                size: \"sm\",\n                                colorScheme: template.color,\n                                onClick: ()=>onSelectTemplate(template),\n                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaClipboardList_FaCog_FaEdit_FaEye_FaLayerGroup_FaPalette_FaPlus_FaRocket_FaTrash_FaWrench_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaRocket, {}, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 27\n                                }, void 0),\n                                children: \"Use Template\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 840,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 828,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                    lineNumber: 827,\n                    columnNumber: 11\n                }, this)\n            }, template.id, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                lineNumber: 826,\n                columnNumber: 9\n            }, this))\n    }, void 0, false);\n}\n// Application Preview Component\nfunction ApplicationPreview({ application }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n        align: \"stretch\",\n        spacing: 4,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                align: \"center\",\n                spacing: 2,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Heading, {\n                        size: \"lg\",\n                        children: application.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 861,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        color: \"gray.600\",\n                        _dark: {\n                            color: 'gray.400'\n                        },\n                        children: application.description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 862,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                lineNumber: 860,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Divider, {}, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                lineNumber: 867,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.VStack, {\n                align: \"stretch\",\n                spacing: 4,\n                children: application.questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                        isRequired: question.required,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                children: question.label\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 872,\n                                columnNumber: 13\n                            }, this),\n                            question.type === 'text' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                placeholder: question.placeholder\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 874,\n                                columnNumber: 15\n                            }, this),\n                            question.type === 'textarea' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                placeholder: question.placeholder\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 877,\n                                columnNumber: 15\n                            }, this),\n                            question.type === 'select' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                placeholder: question.placeholder,\n                                children: question.options?.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: option,\n                                        children: option\n                                    }, option, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                        lineNumber: 882,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 880,\n                                columnNumber: 15\n                            }, this),\n                            question.type === 'number' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                type: \"number\",\n                                placeholder: question.placeholder\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 887,\n                                columnNumber: 15\n                            }, this),\n                            question.type === 'email' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                type: \"email\",\n                                placeholder: question.placeholder\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                                lineNumber: 890,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, question.id, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                        lineNumber: 871,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                lineNumber: 869,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AlertIcon_Badge_Box_Button_Card_CardBody_Divider_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_Menu_MenuButton_MenuItem_MenuList_Modal_ModalBody_ModalCloseButton_ModalContent_ModalFooter_ModalHeader_ModalOverlay_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                colorScheme: application.color,\n                size: \"lg\",\n                isDisabled: true,\n                children: \"Submit Application (Preview)\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n                lineNumber: 896,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\applications-builder.tsx\",\n        lineNumber: 859,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/admin/applications-builder.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Card,CardBody,Divider,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,Menu,MenuButton,MenuItem,MenuList,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Card,CardBody,Divider,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,Menu,MenuButton,MenuItem,MenuList,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* reexport safe */ _alert_alert_mjs__WEBPACK_IMPORTED_MODULE_0__.Alert),\n/* harmony export */   AlertIcon: () => (/* reexport safe */ _alert_alert_icon_mjs__WEBPACK_IMPORTED_MODULE_1__.AlertIcon),\n/* harmony export */   Badge: () => (/* reexport safe */ _badge_badge_mjs__WEBPACK_IMPORTED_MODULE_2__.Badge),\n/* harmony export */   Box: () => (/* reexport safe */ _box_box_mjs__WEBPACK_IMPORTED_MODULE_3__.Box),\n/* harmony export */   Button: () => (/* reexport safe */ _button_button_mjs__WEBPACK_IMPORTED_MODULE_4__.Button),\n/* harmony export */   Card: () => (/* reexport safe */ _card_card_mjs__WEBPACK_IMPORTED_MODULE_5__.Card),\n/* harmony export */   CardBody: () => (/* reexport safe */ _card_card_body_mjs__WEBPACK_IMPORTED_MODULE_6__.CardBody),\n/* harmony export */   Divider: () => (/* reexport safe */ _divider_divider_mjs__WEBPACK_IMPORTED_MODULE_7__.Divider),\n/* harmony export */   FormControl: () => (/* reexport safe */ _form_control_form_control_mjs__WEBPACK_IMPORTED_MODULE_8__.FormControl),\n/* harmony export */   FormLabel: () => (/* reexport safe */ _form_control_form_label_mjs__WEBPACK_IMPORTED_MODULE_9__.FormLabel),\n/* harmony export */   HStack: () => (/* reexport safe */ _stack_h_stack_mjs__WEBPACK_IMPORTED_MODULE_10__.HStack),\n/* harmony export */   Heading: () => (/* reexport safe */ _typography_heading_mjs__WEBPACK_IMPORTED_MODULE_11__.Heading),\n/* harmony export */   Icon: () => (/* reexport safe */ _icon_icon_mjs__WEBPACK_IMPORTED_MODULE_12__.Icon),\n/* harmony export */   IconButton: () => (/* reexport safe */ _button_icon_button_mjs__WEBPACK_IMPORTED_MODULE_13__.IconButton),\n/* harmony export */   Input: () => (/* reexport safe */ _input_input_mjs__WEBPACK_IMPORTED_MODULE_14__.Input),\n/* harmony export */   Menu: () => (/* reexport safe */ _menu_menu_mjs__WEBPACK_IMPORTED_MODULE_15__.Menu),\n/* harmony export */   MenuButton: () => (/* reexport safe */ _menu_menu_button_mjs__WEBPACK_IMPORTED_MODULE_16__.MenuButton),\n/* harmony export */   MenuItem: () => (/* reexport safe */ _menu_menu_item_mjs__WEBPACK_IMPORTED_MODULE_17__.MenuItem),\n/* harmony export */   MenuList: () => (/* reexport safe */ _menu_menu_list_mjs__WEBPACK_IMPORTED_MODULE_18__.MenuList),\n/* harmony export */   Modal: () => (/* reexport safe */ _modal_modal_mjs__WEBPACK_IMPORTED_MODULE_19__.Modal),\n/* harmony export */   ModalBody: () => (/* reexport safe */ _modal_modal_body_mjs__WEBPACK_IMPORTED_MODULE_20__.ModalBody),\n/* harmony export */   ModalCloseButton: () => (/* reexport safe */ _modal_modal_close_button_mjs__WEBPACK_IMPORTED_MODULE_21__.ModalCloseButton),\n/* harmony export */   ModalContent: () => (/* reexport safe */ _modal_modal_content_mjs__WEBPACK_IMPORTED_MODULE_22__.ModalContent),\n/* harmony export */   ModalFooter: () => (/* reexport safe */ _modal_modal_footer_mjs__WEBPACK_IMPORTED_MODULE_23__.ModalFooter),\n/* harmony export */   ModalHeader: () => (/* reexport safe */ _modal_modal_header_mjs__WEBPACK_IMPORTED_MODULE_24__.ModalHeader),\n/* harmony export */   ModalOverlay: () => (/* reexport safe */ _modal_modal_overlay_mjs__WEBPACK_IMPORTED_MODULE_25__.ModalOverlay),\n/* harmony export */   Select: () => (/* reexport safe */ _select_select_mjs__WEBPACK_IMPORTED_MODULE_26__.Select),\n/* harmony export */   SimpleGrid: () => (/* reexport safe */ _grid_simple_grid_mjs__WEBPACK_IMPORTED_MODULE_27__.SimpleGrid),\n/* harmony export */   Switch: () => (/* reexport safe */ _switch_switch_mjs__WEBPACK_IMPORTED_MODULE_28__.Switch),\n/* harmony export */   Tab: () => (/* reexport safe */ _tabs_tab_mjs__WEBPACK_IMPORTED_MODULE_29__.Tab),\n/* harmony export */   TabList: () => (/* reexport safe */ _tabs_tab_list_mjs__WEBPACK_IMPORTED_MODULE_30__.TabList),\n/* harmony export */   TabPanel: () => (/* reexport safe */ _tabs_tab_panel_mjs__WEBPACK_IMPORTED_MODULE_31__.TabPanel),\n/* harmony export */   TabPanels: () => (/* reexport safe */ _tabs_tab_panels_mjs__WEBPACK_IMPORTED_MODULE_32__.TabPanels),\n/* harmony export */   Tabs: () => (/* reexport safe */ _tabs_tabs_mjs__WEBPACK_IMPORTED_MODULE_33__.Tabs),\n/* harmony export */   Text: () => (/* reexport safe */ _typography_text_mjs__WEBPACK_IMPORTED_MODULE_34__.Text),\n/* harmony export */   Textarea: () => (/* reexport safe */ _textarea_textarea_mjs__WEBPACK_IMPORTED_MODULE_35__.Textarea),\n/* harmony export */   VStack: () => (/* reexport safe */ _stack_v_stack_mjs__WEBPACK_IMPORTED_MODULE_36__.VStack),\n/* harmony export */   useToast: () => (/* reexport safe */ _toast_use_toast_mjs__WEBPACK_IMPORTED_MODULE_37__.useToast)\n/* harmony export */ });\n/* harmony import */ var _alert_alert_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./alert/alert.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/alert/alert.mjs\");\n/* harmony import */ var _alert_alert_icon_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./alert/alert-icon.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/alert/alert-icon.mjs\");\n/* harmony import */ var _badge_badge_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./badge/badge.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/badge/badge.mjs\");\n/* harmony import */ var _box_box_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./box/box.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _button_button_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./button/button.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _card_card_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./card/card.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/card/card.mjs\");\n/* harmony import */ var _card_card_body_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./card/card-body.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/card/card-body.mjs\");\n/* harmony import */ var _divider_divider_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./divider/divider.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _form_control_form_control_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./form-control/form-control.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/form-control/form-control.mjs\");\n/* harmony import */ var _form_control_form_label_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./form-control/form-label.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/form-control/form-label.mjs\");\n/* harmony import */ var _stack_h_stack_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./stack/h-stack.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/stack/h-stack.mjs\");\n/* harmony import */ var _typography_heading_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./typography/heading.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/typography/heading.mjs\");\n/* harmony import */ var _icon_icon_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./icon/icon.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/icon/icon.mjs\");\n/* harmony import */ var _button_icon_button_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./button/icon-button.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/button/icon-button.mjs\");\n/* harmony import */ var _input_input_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./input/input.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/input/input.mjs\");\n/* harmony import */ var _menu_menu_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./menu/menu.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/menu/menu.mjs\");\n/* harmony import */ var _menu_menu_button_mjs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./menu/menu-button.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/menu/menu-button.mjs\");\n/* harmony import */ var _menu_menu_item_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./menu/menu-item.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/menu/menu-item.mjs\");\n/* harmony import */ var _menu_menu_list_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./menu/menu-list.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/menu/menu-list.mjs\");\n/* harmony import */ var _modal_modal_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./modal/modal.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _modal_modal_body_mjs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./modal/modal-body.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _modal_modal_close_button_mjs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./modal/modal-close-button.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _modal_modal_content_mjs__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./modal/modal-content.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _modal_modal_footer_mjs__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./modal/modal-footer.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/modal/modal-footer.mjs\");\n/* harmony import */ var _modal_modal_header_mjs__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./modal/modal-header.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _modal_modal_overlay_mjs__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./modal/modal-overlay.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _select_select_mjs__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./select/select.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/select/select.mjs\");\n/* harmony import */ var _grid_simple_grid_mjs__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./grid/simple-grid.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/grid/simple-grid.mjs\");\n/* harmony import */ var _switch_switch_mjs__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./switch/switch.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/switch/switch.mjs\");\n/* harmony import */ var _tabs_tab_mjs__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./tabs/tab.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/tabs/tab.mjs\");\n/* harmony import */ var _tabs_tab_list_mjs__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./tabs/tab-list.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/tabs/tab-list.mjs\");\n/* harmony import */ var _tabs_tab_panel_mjs__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./tabs/tab-panel.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/tabs/tab-panel.mjs\");\n/* harmony import */ var _tabs_tab_panels_mjs__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./tabs/tab-panels.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/tabs/tab-panels.mjs\");\n/* harmony import */ var _tabs_tabs_mjs__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./tabs/tabs.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/tabs/tabs.mjs\");\n/* harmony import */ var _typography_text_mjs__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./typography/text.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _textarea_textarea_mjs__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! ./textarea/textarea.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/textarea/textarea.mjs\");\n/* harmony import */ var _stack_v_stack_mjs__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./stack/v-stack.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/stack/v-stack.mjs\");\n/* harmony import */ var _toast_use_toast_mjs__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! ./toast/use-toast.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _barrel_optimize_names_useDisclosure_wildcard_chakra_ui_hooks__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=useDisclosure&wildcard!=!@chakra-ui/hooks */ \"(pages-dir-node)/__barrel_optimize__?names=useDisclosure&wildcard!=!../../node_modules/.pnpm/@chakra-ui+hooks@2.4.5_react@19.1.0/node_modules/@chakra-ui/hooks/dist/esm/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _barrel_optimize_names_useDisclosure_wildcard_chakra_ui_hooks__WEBPACK_IMPORTED_MODULE_38__) if([\"default\",\"Alert\",\"AlertIcon\",\"Badge\",\"Box\",\"Button\",\"Card\",\"CardBody\",\"Divider\",\"FormControl\",\"FormLabel\",\"HStack\",\"Heading\",\"Icon\",\"IconButton\",\"Input\",\"Menu\",\"MenuButton\",\"MenuItem\",\"MenuList\",\"Modal\",\"ModalBody\",\"ModalCloseButton\",\"ModalContent\",\"ModalFooter\",\"ModalHeader\",\"ModalOverlay\",\"Select\",\"SimpleGrid\",\"Switch\",\"Tab\",\"TabList\",\"TabPanel\",\"TabPanels\",\"Tabs\",\"Text\",\"Textarea\",\"VStack\",\"useToast\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _barrel_optimize_names_useDisclosure_wildcard_chakra_ui_hooks__WEBPACK_IMPORTED_MODULE_38__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _barrel_optimize_names_useDisclosure_wildcard_chakra_ui_styled_system__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=useDisclosure&wildcard!=!@chakra-ui/styled-system */ \"(pages-dir-node)/__barrel_optimize__?names=useDisclosure&wildcard!=!../../node_modules/.pnpm/@chakra-ui+styled-system@2.12.4_react@19.1.0/node_modules/@chakra-ui/styled-system/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_useDisclosure_wildcard_chakra_ui_styled_system__WEBPACK_IMPORTED_MODULE_39___default = /*#__PURE__*/__webpack_require__.n(_barrel_optimize_names_useDisclosure_wildcard_chakra_ui_styled_system__WEBPACK_IMPORTED_MODULE_39__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _barrel_optimize_names_useDisclosure_wildcard_chakra_ui_styled_system__WEBPACK_IMPORTED_MODULE_39__) if([\"default\",\"Alert\",\"AlertIcon\",\"Badge\",\"Box\",\"Button\",\"Card\",\"CardBody\",\"Divider\",\"FormControl\",\"FormLabel\",\"HStack\",\"Heading\",\"Icon\",\"IconButton\",\"Input\",\"Menu\",\"MenuButton\",\"MenuItem\",\"MenuList\",\"Modal\",\"ModalBody\",\"ModalCloseButton\",\"ModalContent\",\"ModalFooter\",\"ModalHeader\",\"ModalOverlay\",\"Select\",\"SimpleGrid\",\"Switch\",\"Tab\",\"TabList\",\"TabPanel\",\"TabPanels\",\"Tabs\",\"Text\",\"Textarea\",\"VStack\",\"useToast\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _barrel_optimize_names_useDisclosure_wildcard_chakra_ui_styled_system__WEBPACK_IMPORTED_MODULE_39__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _barrel_optimize_names_useDisclosure_wildcard_chakra_ui_theme__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=useDisclosure&wildcard!=!@chakra-ui/theme */ \"(pages-dir-node)/__barrel_optimize__?names=useDisclosure&wildcard!=!../../node_modules/.pnpm/@chakra-ui+theme@3.4.9_@cha_b8cd1a62b09e57ef8a09978ec144879f/node_modules/@chakra-ui/theme/dist/esm/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _barrel_optimize_names_useDisclosure_wildcard_chakra_ui_theme__WEBPACK_IMPORTED_MODULE_40__) if([\"default\",\"Alert\",\"AlertIcon\",\"Badge\",\"Box\",\"Button\",\"Card\",\"CardBody\",\"Divider\",\"FormControl\",\"FormLabel\",\"HStack\",\"Heading\",\"Icon\",\"IconButton\",\"Input\",\"Menu\",\"MenuButton\",\"MenuItem\",\"MenuList\",\"Modal\",\"ModalBody\",\"ModalCloseButton\",\"ModalContent\",\"ModalFooter\",\"ModalHeader\",\"ModalOverlay\",\"Select\",\"SimpleGrid\",\"Switch\",\"Tab\",\"TabList\",\"TabPanel\",\"TabPanels\",\"Tabs\",\"Text\",\"Textarea\",\"VStack\",\"useToast\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _barrel_optimize_names_useDisclosure_wildcard_chakra_ui_theme__WEBPACK_IMPORTED_MODULE_40__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_alert_alert_mjs__WEBPACK_IMPORTED_MODULE_0__, _alert_alert_icon_mjs__WEBPACK_IMPORTED_MODULE_1__, _badge_badge_mjs__WEBPACK_IMPORTED_MODULE_2__, _box_box_mjs__WEBPACK_IMPORTED_MODULE_3__, _button_button_mjs__WEBPACK_IMPORTED_MODULE_4__, _card_card_mjs__WEBPACK_IMPORTED_MODULE_5__, _card_card_body_mjs__WEBPACK_IMPORTED_MODULE_6__, _divider_divider_mjs__WEBPACK_IMPORTED_MODULE_7__, _form_control_form_control_mjs__WEBPACK_IMPORTED_MODULE_8__, _form_control_form_label_mjs__WEBPACK_IMPORTED_MODULE_9__, _stack_h_stack_mjs__WEBPACK_IMPORTED_MODULE_10__, _typography_heading_mjs__WEBPACK_IMPORTED_MODULE_11__, _icon_icon_mjs__WEBPACK_IMPORTED_MODULE_12__, _button_icon_button_mjs__WEBPACK_IMPORTED_MODULE_13__, _input_input_mjs__WEBPACK_IMPORTED_MODULE_14__, _menu_menu_mjs__WEBPACK_IMPORTED_MODULE_15__, _menu_menu_button_mjs__WEBPACK_IMPORTED_MODULE_16__, _menu_menu_item_mjs__WEBPACK_IMPORTED_MODULE_17__, _menu_menu_list_mjs__WEBPACK_IMPORTED_MODULE_18__, _modal_modal_mjs__WEBPACK_IMPORTED_MODULE_19__, _modal_modal_body_mjs__WEBPACK_IMPORTED_MODULE_20__, _modal_modal_close_button_mjs__WEBPACK_IMPORTED_MODULE_21__, _modal_modal_content_mjs__WEBPACK_IMPORTED_MODULE_22__, _modal_modal_footer_mjs__WEBPACK_IMPORTED_MODULE_23__, _modal_modal_header_mjs__WEBPACK_IMPORTED_MODULE_24__, _modal_modal_overlay_mjs__WEBPACK_IMPORTED_MODULE_25__, _select_select_mjs__WEBPACK_IMPORTED_MODULE_26__, _grid_simple_grid_mjs__WEBPACK_IMPORTED_MODULE_27__, _switch_switch_mjs__WEBPACK_IMPORTED_MODULE_28__, _tabs_tab_mjs__WEBPACK_IMPORTED_MODULE_29__, _tabs_tab_list_mjs__WEBPACK_IMPORTED_MODULE_30__, _tabs_tab_panel_mjs__WEBPACK_IMPORTED_MODULE_31__, _tabs_tab_panels_mjs__WEBPACK_IMPORTED_MODULE_32__, _tabs_tabs_mjs__WEBPACK_IMPORTED_MODULE_33__, _typography_text_mjs__WEBPACK_IMPORTED_MODULE_34__, _textarea_textarea_mjs__WEBPACK_IMPORTED_MODULE_35__, _stack_v_stack_mjs__WEBPACK_IMPORTED_MODULE_36__, _toast_use_toast_mjs__WEBPACK_IMPORTED_MODULE_37__]);\n([_alert_alert_mjs__WEBPACK_IMPORTED_MODULE_0__, _alert_alert_icon_mjs__WEBPACK_IMPORTED_MODULE_1__, _badge_badge_mjs__WEBPACK_IMPORTED_MODULE_2__, _box_box_mjs__WEBPACK_IMPORTED_MODULE_3__, _button_button_mjs__WEBPACK_IMPORTED_MODULE_4__, _card_card_mjs__WEBPACK_IMPORTED_MODULE_5__, _card_card_body_mjs__WEBPACK_IMPORTED_MODULE_6__, _divider_divider_mjs__WEBPACK_IMPORTED_MODULE_7__, _form_control_form_control_mjs__WEBPACK_IMPORTED_MODULE_8__, _form_control_form_label_mjs__WEBPACK_IMPORTED_MODULE_9__, _stack_h_stack_mjs__WEBPACK_IMPORTED_MODULE_10__, _typography_heading_mjs__WEBPACK_IMPORTED_MODULE_11__, _icon_icon_mjs__WEBPACK_IMPORTED_MODULE_12__, _button_icon_button_mjs__WEBPACK_IMPORTED_MODULE_13__, _input_input_mjs__WEBPACK_IMPORTED_MODULE_14__, _menu_menu_mjs__WEBPACK_IMPORTED_MODULE_15__, _menu_menu_button_mjs__WEBPACK_IMPORTED_MODULE_16__, _menu_menu_item_mjs__WEBPACK_IMPORTED_MODULE_17__, _menu_menu_list_mjs__WEBPACK_IMPORTED_MODULE_18__, _modal_modal_mjs__WEBPACK_IMPORTED_MODULE_19__, _modal_modal_body_mjs__WEBPACK_IMPORTED_MODULE_20__, _modal_modal_close_button_mjs__WEBPACK_IMPORTED_MODULE_21__, _modal_modal_content_mjs__WEBPACK_IMPORTED_MODULE_22__, _modal_modal_footer_mjs__WEBPACK_IMPORTED_MODULE_23__, _modal_modal_header_mjs__WEBPACK_IMPORTED_MODULE_24__, _modal_modal_overlay_mjs__WEBPACK_IMPORTED_MODULE_25__, _select_select_mjs__WEBPACK_IMPORTED_MODULE_26__, _grid_simple_grid_mjs__WEBPACK_IMPORTED_MODULE_27__, _switch_switch_mjs__WEBPACK_IMPORTED_MODULE_28__, _tabs_tab_mjs__WEBPACK_IMPORTED_MODULE_29__, _tabs_tab_list_mjs__WEBPACK_IMPORTED_MODULE_30__, _tabs_tab_panel_mjs__WEBPACK_IMPORTED_MODULE_31__, _tabs_tab_panels_mjs__WEBPACK_IMPORTED_MODULE_32__, _tabs_tabs_mjs__WEBPACK_IMPORTED_MODULE_33__, _typography_text_mjs__WEBPACK_IMPORTED_MODULE_34__, _textarea_textarea_mjs__WEBPACK_IMPORTED_MODULE_35__, _stack_v_stack_mjs__WEBPACK_IMPORTED_MODULE_36__, _toast_use_toast_mjs__WEBPACK_IMPORTED_MODULE_37__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUFsZXJ0LEFsZXJ0SWNvbixCYWRnZSxCb3gsQnV0dG9uLENhcmQsQ2FyZEJvZHksRGl2aWRlcixGb3JtQ29udHJvbCxGb3JtTGFiZWwsSFN0YWNrLEhlYWRpbmcsSWNvbixJY29uQnV0dG9uLElucHV0LE1lbnUsTWVudUJ1dHRvbixNZW51SXRlbSxNZW51TGlzdCxNb2RhbCxNb2RhbEJvZHksTW9kYWxDbG9zZUJ1dHRvbixNb2RhbENvbnRlbnQsTW9kYWxGb290ZXIsTW9kYWxIZWFkZXIsTW9kYWxPdmVybGF5LFNlbGVjdCxTaW1wbGVHcmlkLFN3aXRjaCxUYWIsVGFiTGlzdCxUYWJQYW5lbCxUYWJQYW5lbHMsVGFicyxUZXh0LFRleHRhcmVhLFZTdGFjayx1c2VEaXNjbG9zdXJlLHVzZVRvYXN0IT0hLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BjaGFrcmEtdWkrcmVhY3RAMi4xMC45X0BlbV81MmRkNjhjZDJkMGJhZWVmN2ExMThhM2Q5MWYzMTEyNy9ub2RlX21vZHVsZXMvQGNoYWtyYS11aS9yZWFjdC9kaXN0L2VzbS9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3lDO0FBQ1M7QUFDVDtBQUNOO0FBQ1M7QUFDTjtBQUNTO0FBQ0E7QUFDYztBQUNKO0FBQ2I7QUFDTTtBQUNaO0FBQ2U7QUFDWjtBQUNIO0FBQ2E7QUFDSjtBQUNBO0FBQ047QUFDUztBQUNlO0FBQ1Q7QUFDRjtBQUNBO0FBQ0U7QUFDWjtBQUNPO0FBQ1A7QUFDUjtBQUNTO0FBQ0U7QUFDRTtBQUNYO0FBQ007QUFDTTtBQUNOO0FBQ0k7QUFDbUM7QUFDUSIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGNoYWtyYS11aStyZWFjdEAyLjEwLjlfQGVtXzUyZGQ2OGNkMmQwYmFlZWY3YTExOGEzZDkxZjMxMTI3XFxub2RlX21vZHVsZXNcXEBjaGFrcmEtdWlcXHJlYWN0XFxkaXN0XFxlc21cXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IEFsZXJ0IH0gZnJvbSBcIi4vYWxlcnQvYWxlcnQubWpzXCJcbmV4cG9ydCB7IEFsZXJ0SWNvbiB9IGZyb20gXCIuL2FsZXJ0L2FsZXJ0LWljb24ubWpzXCJcbmV4cG9ydCB7IEJhZGdlIH0gZnJvbSBcIi4vYmFkZ2UvYmFkZ2UubWpzXCJcbmV4cG9ydCB7IEJveCB9IGZyb20gXCIuL2JveC9ib3gubWpzXCJcbmV4cG9ydCB7IEJ1dHRvbiB9IGZyb20gXCIuL2J1dHRvbi9idXR0b24ubWpzXCJcbmV4cG9ydCB7IENhcmQgfSBmcm9tIFwiLi9jYXJkL2NhcmQubWpzXCJcbmV4cG9ydCB7IENhcmRCb2R5IH0gZnJvbSBcIi4vY2FyZC9jYXJkLWJvZHkubWpzXCJcbmV4cG9ydCB7IERpdmlkZXIgfSBmcm9tIFwiLi9kaXZpZGVyL2RpdmlkZXIubWpzXCJcbmV4cG9ydCB7IEZvcm1Db250cm9sIH0gZnJvbSBcIi4vZm9ybS1jb250cm9sL2Zvcm0tY29udHJvbC5tanNcIlxuZXhwb3J0IHsgRm9ybUxhYmVsIH0gZnJvbSBcIi4vZm9ybS1jb250cm9sL2Zvcm0tbGFiZWwubWpzXCJcbmV4cG9ydCB7IEhTdGFjayB9IGZyb20gXCIuL3N0YWNrL2gtc3RhY2subWpzXCJcbmV4cG9ydCB7IEhlYWRpbmcgfSBmcm9tIFwiLi90eXBvZ3JhcGh5L2hlYWRpbmcubWpzXCJcbmV4cG9ydCB7IEljb24gfSBmcm9tIFwiLi9pY29uL2ljb24ubWpzXCJcbmV4cG9ydCB7IEljb25CdXR0b24gfSBmcm9tIFwiLi9idXR0b24vaWNvbi1idXR0b24ubWpzXCJcbmV4cG9ydCB7IElucHV0IH0gZnJvbSBcIi4vaW5wdXQvaW5wdXQubWpzXCJcbmV4cG9ydCB7IE1lbnUgfSBmcm9tIFwiLi9tZW51L21lbnUubWpzXCJcbmV4cG9ydCB7IE1lbnVCdXR0b24gfSBmcm9tIFwiLi9tZW51L21lbnUtYnV0dG9uLm1qc1wiXG5leHBvcnQgeyBNZW51SXRlbSB9IGZyb20gXCIuL21lbnUvbWVudS1pdGVtLm1qc1wiXG5leHBvcnQgeyBNZW51TGlzdCB9IGZyb20gXCIuL21lbnUvbWVudS1saXN0Lm1qc1wiXG5leHBvcnQgeyBNb2RhbCB9IGZyb20gXCIuL21vZGFsL21vZGFsLm1qc1wiXG5leHBvcnQgeyBNb2RhbEJvZHkgfSBmcm9tIFwiLi9tb2RhbC9tb2RhbC1ib2R5Lm1qc1wiXG5leHBvcnQgeyBNb2RhbENsb3NlQnV0dG9uIH0gZnJvbSBcIi4vbW9kYWwvbW9kYWwtY2xvc2UtYnV0dG9uLm1qc1wiXG5leHBvcnQgeyBNb2RhbENvbnRlbnQgfSBmcm9tIFwiLi9tb2RhbC9tb2RhbC1jb250ZW50Lm1qc1wiXG5leHBvcnQgeyBNb2RhbEZvb3RlciB9IGZyb20gXCIuL21vZGFsL21vZGFsLWZvb3Rlci5tanNcIlxuZXhwb3J0IHsgTW9kYWxIZWFkZXIgfSBmcm9tIFwiLi9tb2RhbC9tb2RhbC1oZWFkZXIubWpzXCJcbmV4cG9ydCB7IE1vZGFsT3ZlcmxheSB9IGZyb20gXCIuL21vZGFsL21vZGFsLW92ZXJsYXkubWpzXCJcbmV4cG9ydCB7IFNlbGVjdCB9IGZyb20gXCIuL3NlbGVjdC9zZWxlY3QubWpzXCJcbmV4cG9ydCB7IFNpbXBsZUdyaWQgfSBmcm9tIFwiLi9ncmlkL3NpbXBsZS1ncmlkLm1qc1wiXG5leHBvcnQgeyBTd2l0Y2ggfSBmcm9tIFwiLi9zd2l0Y2gvc3dpdGNoLm1qc1wiXG5leHBvcnQgeyBUYWIgfSBmcm9tIFwiLi90YWJzL3RhYi5tanNcIlxuZXhwb3J0IHsgVGFiTGlzdCB9IGZyb20gXCIuL3RhYnMvdGFiLWxpc3QubWpzXCJcbmV4cG9ydCB7IFRhYlBhbmVsIH0gZnJvbSBcIi4vdGFicy90YWItcGFuZWwubWpzXCJcbmV4cG9ydCB7IFRhYlBhbmVscyB9IGZyb20gXCIuL3RhYnMvdGFiLXBhbmVscy5tanNcIlxuZXhwb3J0IHsgVGFicyB9IGZyb20gXCIuL3RhYnMvdGFicy5tanNcIlxuZXhwb3J0IHsgVGV4dCB9IGZyb20gXCIuL3R5cG9ncmFwaHkvdGV4dC5tanNcIlxuZXhwb3J0IHsgVGV4dGFyZWEgfSBmcm9tIFwiLi90ZXh0YXJlYS90ZXh0YXJlYS5tanNcIlxuZXhwb3J0IHsgVlN0YWNrIH0gZnJvbSBcIi4vc3RhY2svdi1zdGFjay5tanNcIlxuZXhwb3J0IHsgdXNlVG9hc3QgfSBmcm9tIFwiLi90b2FzdC91c2UtdG9hc3QubWpzXCJcbmV4cG9ydCAqIGZyb20gXCJfX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPXVzZURpc2Nsb3N1cmUmd2lsZGNhcmQhPSFAY2hha3JhLXVpL2hvb2tzXCJcbmV4cG9ydCAqIGZyb20gXCJfX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPXVzZURpc2Nsb3N1cmUmd2lsZGNhcmQhPSFAY2hha3JhLXVpL3N0eWxlZC1zeXN0ZW1cIlxuZXhwb3J0ICogZnJvbSBcIl9fYmFycmVsX29wdGltaXplX18/bmFtZXM9dXNlRGlzY2xvc3VyZSZ3aWxkY2FyZCE9IUBjaGFrcmEtdWkvdGhlbWVcIiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=Alert,AlertIcon,Badge,Box,Button,Card,CardBody,Divider,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,Menu,MenuButton,MenuItem,MenuList,Modal,ModalBody,ModalCloseButton,ModalContent,ModalFooter,ModalHeader,ModalOverlay,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FaChevronDown,FaClipboardList,FaCog,FaEdit,FaEye,FaLayerGroup,FaPalette,FaPlus,FaRocket,FaTrash,FaWrench!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FaChevronDown,FaClipboardList,FaCog,FaEdit,FaEye,FaLayerGroup,FaPalette,FaPlus,FaRocket,FaTrash,FaWrench!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs */ "(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/react");;

/***/ }),

/***/ "@emotion/styled":
/*!**********************************!*\
  !*** external "@emotion/styled" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("@emotion/styled");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "swr":
/*!**********************!*\
  !*** external "swr" ***!
  \**********************/
/***/ ((module) => {

module.exports = import("swr");;

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c","chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-4215ff4a","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-fdcfc49e","chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9","chakra-node_modules_pnpm_chakra-ui_theme-","chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_fa_index_mjs-b1cc012-b4687165","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_f","lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons"], () => (__webpack_exec__("(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Fapplications-builder&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Capplications-builder.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();