"use strict";exports.id=9176,exports.ids=[9176],exports.modules={176:(t,e,n)=>{n.d(e,{$:()=>s});var o=n(58546),r=n(22908),i=n(43125),a=n(27075);let s={pan:{Feature:r.f},drag:{Feature:o.w,ProjectionNode:a.P,MeasureLayout:i.$}}},6242:(t,e,n)=>{n.d(e,{W:()=>l});var o=n(73400),r=n(75974),i=n(21966);class a extends i.X{constructor(t){super(t),t.animationState||(t.animationState=(0,r.L)(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();(0,o.N)(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let s=0;class u extends i.X{constructor(){super(...arguments),this.id=s++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;let o=this.node.animationState.setActive("exit",!t);e&&!t&&o.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let l={animation:{Feature:a},exit:{Feature:u}}},15322:(t,e,n)=>{n.d(e,{Z:()=>x});var o=n(8732);n(78487);var r=n(82015),i=n(97667),a=n(99914),s=n(94520),u=n(7421),l=n(96345),d=n(94168),p=n(38590),c=n(19948),m=n(65182),h=n(68965),f=n(87558),v=n(73912),g=n(73819),w=n(13451),y=n(69045);function x({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:x,Component:b}){function C(t,c){var m,C,S;let P,E={...(0,r.useContext)(s.Q),...t,layoutId:function({layoutId:t}){let e=(0,r.useContext)(i.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:R}=E,T=(0,l.z)(t),M=x(t,R);if(!R&&d.B){C=0,S=0,(0,r.useContext)(a.Y).strict;let t=function(t){let{drag:e,layout:n}=p.B;if(!e&&!n)return{};let o={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?o.MeasureLayout:void 0,ProjectionNode:o.ProjectionNode}}(E);P=t.MeasureLayout,T.visualElement=function(t,e,n,o,i){let{visualElement:l}=(0,r.useContext)(u.A),d=(0,r.useContext)(a.Y),p=(0,r.useContext)(g.t),c=(0,r.useContext)(s.Q).reducedMotion,m=(0,r.useRef)(null);o=o||d.renderer,!m.current&&o&&(m.current=o(t,{visualState:e,parent:l,props:n,presenceContext:p,blockInitialAnimation:!!p&&!1===p.initial,reducedMotionConfig:c}));let x=m.current,b=(0,r.useContext)(w.N);x&&!x.projection&&i&&("html"===x.type||"svg"===x.type)&&function(t,e,n,o){let{layoutId:r,layout:i,drag:a,dragConstraints:s,layoutScroll:u,layoutRoot:l,layoutCrossfade:d}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:i,alwaysMeasureLayout:!!a||s&&(0,h.X)(s),visualElement:t,animationType:"string"==typeof i?i:"both",initialPromotionConfig:o,crossfade:d,layoutScroll:u,layoutRoot:l})}(m.current,n,i,b);let C=(0,r.useRef)(!1);(0,r.useInsertionEffect)(()=>{x&&C.current&&x.update(n,p)});let S=n[v.n],P=(0,r.useRef)(!!S&&!window.MotionHandoffIsComplete?.(S)&&window.MotionHasOptimisedAnimation?.(S));return(0,y.E)(()=>{x&&(C.current=!0,window.MotionIsMounted=!0,x.updateFeatures(),f.k2.render(x.render),P.current&&x.animationState&&x.animationState.animateChanges())}),(0,r.useEffect)(()=>{x&&(!P.current&&x.animationState&&x.animationState.animateChanges(),P.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(S)}),P.current=!1))}),x}(b,M,E,e,t.ProjectionNode)}return(0,o.jsxs)(u.A.Provider,{value:T,children:[P&&T.visualElement?(0,o.jsx)(P,{visualElement:T.visualElement,...E}):null,n(b,t,(m=T.visualElement,(0,r.useCallback)(t=>{t&&M.onMount&&M.onMount(t),m&&(t?m.mount(t):m.unmount()),c&&("function"==typeof c?c(t):(0,h.X)(c)&&(c.current=t))},[m])),M,R,T.visualElement)]})}t&&(0,c.Y)(t),C.displayName=`motion.${"string"==typeof b?b:`create(${b.displayName??b.name??""})`}`;let S=(0,r.forwardRef)(C);return S[m.o]=b,S}},17957:(t,e,n)=>{n.d(e,{z:()=>i});var o=n(87558),r=n(53819);function i(t,{layout:e,layoutId:n}){return o.fu.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!r.H[t]||"opacity"===t)}},19948:(t,e,n)=>{n.d(e,{Y:()=>r});var o=n(38590);function r(t){for(let e in t)o.B[e]={...o.B[e],...t[e]}}},21966:(t,e,n)=>{n.d(e,{X:()=>o});class o{constructor(t){this.isMounted=!1,this.node=t}update(){}}},22580:(t,e,n)=>{n.d(e,{T:()=>p});var o=n(82015),r=n(73400),i=n(7421),a=n(73819),s=n(11395),u=n(36981),l=n(42643),d=n(90074);let p=t=>(e,n)=>{let p=(0,o.useContext)(i.A),c=(0,o.useContext)(a.t),m=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},n,o,i){return{latestValues:function(t,e,n,o){let i={},a=o(t,{});for(let t in a)i[t]=(0,d.u)(a[t]);let{initial:l,animate:p}=t,c=(0,s.e)(t),m=(0,s.O)(t);e&&m&&!c&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===p&&(p=e.animate));let h=!!n&&!1===n.initial,f=(h=h||!1===l)?p:l;if(f&&"boolean"!=typeof f&&!(0,r.N)(f)){let e=Array.isArray(f)?f:[f];for(let n=0;n<e.length;n++){let o=(0,u.a)(t,e[n]);if(o){let{transitionEnd:t,transition:e,...n}=o;for(let t in n){let e=n[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(i[t]=e)}for(let e in t)i[e]=t[e]}}}return i}(n,o,i,t),renderState:e()}})(t,e,p,c);return n?m():(0,l.M)(m)}},32658:(t,e,n)=>{n.d(e,{S:()=>r});let o=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||o.has(t)}},38590:(t,e,n)=>{n.d(e,{B:()=>r});let o={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},r={};for(let t in o)r[t]={isEnabled:e=>o[t].some(t=>!!e[t])}},42280:(t,e,n)=>{n.d(e,{Z:()=>i});var o=n(27075),r=n(43125);let i={layout:{ProjectionNode:o.P,MeasureLayout:r.$}}},43125:(t,e,n)=>{n.d(e,{$:()=>f});var o=n(8732),r=n(87558),i=n(82015),a=n(93030),s=n(97667),u=n(13451),l=n(307),d=n(21244),p=n(93963),c=n(53819);let m=!1;class h extends(7311==n.j?i.Component:null){componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:o}=this.props,{projection:r}=t;(0,c.$)(v),r&&(e.group&&e.group.add(r),n&&n.register&&o&&n.register(r),m&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),l.w.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:n,drag:o,isPresent:i}=this.props,{projection:a}=n;return a&&(a.isPresent=i,m=!0,o||t.layoutDependency!==e||void 0===e||t.isPresent!==i?a.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?a.promote():a.relegate()||r.Gt.postRender(()=>{let t=a.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),r.k2.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(o),n&&n.deregister&&n.deregister(o))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function f(t){let[e,n]=(0,a.xQ)(),r=(0,i.useContext)(s.L);return(0,o.jsx)(h,{...t,layoutGroup:r,switchLayoutGroup:(0,i.useContext)(u.N),isPresent:e,safeToRemove:n})}let v={borderRadius:{...d.P,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:d.P,borderTopRightRadius:d.P,borderBottomLeftRadius:d.P,borderBottomRightRadius:d.P,boxShadow:p._}},65182:(t,e,n)=>{n.d(e,{o:()=>o});let o=Symbol.for("motionComponentSymbol")},70122:(t,e,n)=>{n.d(e,{n:()=>m});var o=n(9690),r=n(35508),i=n(68015),a=n(21966);let s=new WeakMap,u=new WeakMap,l=t=>{let e=s.get(t.target);e&&e(t)},d=t=>{t.forEach(l)},p={some:0,all:1};class c extends a.X{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:o="some",once:r}=t,i={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof o?o:p[o]};return function(t,e,n){let o=function({root:t,...e}){let n=t||document;u.has(n)||u.set(n,{});let o=u.get(n),r=JSON.stringify(e);return o[r]||(o[r]=new IntersectionObserver(d,{root:t,...e})),o[r]}(e);return s.set(t,n),o.observe(t),()=>{s.delete(t),o.unobserve(t)}}(this.node.current,i,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:n,onViewportLeave:o}=this.node.getProps(),i=e?n:o;i&&i(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}let m={inView:{Feature:c},tap:{Feature:i.H},focus:{Feature:r.c},hover:{Feature:o.e}}},94094:(t,e,n)=>{n.d(e,{Nyo:()=>o.N,PY1:()=>r.P,tFS:()=>i.tF,xQ_:()=>i.xQ});var o=n(91426);n(72190),n(11169),n(56761),n(42736);var r=n(30743);n(22695),n(62),n(6242),n(22580),n(2196),n(38054),n(76868),n(94168),n(77731),n(69045),n(65057),n(25039),n(17423),n(55229),n(35964),n(23627),n(4505),n(54556),n(51921),n(33266),n(88950),n(66346),n(10945),n(66280),n(85079),n(47976),n(90074),n(4864),n(98871),n(78487),n(44042),n(8232),n(22928),n(89429),n(72222);var i=n(93030);n(29771),n(83950),n(36118),n(15322),n(65182),n(32658),n(53819),n(71037),n(40784),n(41951),n(73093),n(82465),n(19773),n(12357),n(18660),n(80596),n(69806),n(73912),n(74161),n(97667),n(94520),n(7421),n(73819),n(13451),n(38801),n(80762),n(42420),n(70858),n(87797),n(96250),n(88231),n(46315),n(28594),n(68298),n(44962),n(8914),n(23622),n(87558)}};