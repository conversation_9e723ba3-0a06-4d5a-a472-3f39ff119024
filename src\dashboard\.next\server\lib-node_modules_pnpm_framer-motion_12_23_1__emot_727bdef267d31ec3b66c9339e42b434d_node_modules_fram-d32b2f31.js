"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31";
exports.ids = ["lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mixValues: () => (/* binding */ mixValues)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n\n\n\nconst borders = [\"TopLeft\", \"TopRight\", \"BottomLeft\", \"BottomRight\"];\nconst numBorders = borders.length;\nconst asNumber = (value) => typeof value === \"string\" ? parseFloat(value) : value;\nconst isPx = (value) => typeof value === \"number\" || motion_dom__WEBPACK_IMPORTED_MODULE_0__.px.test(value);\nfunction mixValues(target, follow, lead, progress, shouldCrossfadeOpacity, isOnlyMember) {\n    if (shouldCrossfadeOpacity) {\n        target.opacity = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(0, lead.opacity ?? 1, easeCrossfadeIn(progress));\n        target.opacityExit = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(follow.opacity ?? 1, 0, easeCrossfadeOut(progress));\n    }\n    else if (isOnlyMember) {\n        target.opacity = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(follow.opacity ?? 1, lead.opacity ?? 1, progress);\n    }\n    /**\n     * Mix border radius\n     */\n    for (let i = 0; i < numBorders; i++) {\n        const borderLabel = `border${borders[i]}Radius`;\n        let followRadius = getRadius(follow, borderLabel);\n        let leadRadius = getRadius(lead, borderLabel);\n        if (followRadius === undefined && leadRadius === undefined)\n            continue;\n        followRadius || (followRadius = 0);\n        leadRadius || (leadRadius = 0);\n        const canMix = followRadius === 0 ||\n            leadRadius === 0 ||\n            isPx(followRadius) === isPx(leadRadius);\n        if (canMix) {\n            target[borderLabel] = Math.max((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(asNumber(followRadius), asNumber(leadRadius), progress), 0);\n            if (motion_dom__WEBPACK_IMPORTED_MODULE_0__.percent.test(leadRadius) || motion_dom__WEBPACK_IMPORTED_MODULE_0__.percent.test(followRadius)) {\n                target[borderLabel] += \"%\";\n            }\n        }\n        else {\n            target[borderLabel] = leadRadius;\n        }\n    }\n    /**\n     * Mix rotation\n     */\n    if (follow.rotate || lead.rotate) {\n        target.rotate = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(follow.rotate || 0, lead.rotate || 0, progress);\n    }\n}\nfunction getRadius(values, radiusName) {\n    return values[radiusName] !== undefined\n        ? values[radiusName]\n        : values.borderRadius;\n}\n// /**\n//  * We only want to mix the background color if there's a follow element\n//  * that we're not crossfading opacity between. For instance with switch\n//  * AnimateSharedLayout animations, this helps the illusion of a continuous\n//  * element being animated but also cuts down on the number of paints triggered\n//  * for elements where opacity is doing that work for us.\n//  */\n// if (\n//     !hasFollowElement &&\n//     latestLeadValues.backgroundColor &&\n//     latestFollowValues.backgroundColor\n// ) {\n//     /**\n//      * This isn't ideal performance-wise as mixColor is creating a new function every frame.\n//      * We could probably create a mixer that runs at the start of the animation but\n//      * the idea behind the crossfader is that it runs dynamically between two potentially\n//      * changing targets (ie opacity or borderRadius may be animating independently via variants)\n//      */\n//     leadState.backgroundColor = followState.backgroundColor = mixColor(\n//         latestFollowValues.backgroundColor as string,\n//         latestLeadValues.backgroundColor as string\n//     )(p)\n// }\nconst easeCrossfadeIn = /*@__PURE__*/ compress(0, 0.5, motion_utils__WEBPACK_IMPORTED_MODULE_1__.circOut);\nconst easeCrossfadeOut = /*@__PURE__*/ compress(0.5, 0.95, motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop);\nfunction compress(min, max, easing) {\n    return (p) => {\n        // Could replace ifs with clamp\n        if (p < min)\n            return 0;\n        if (p > max)\n            return 1;\n        return easing((0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.progress)(min, max, p));\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertBoundingBoxToBox: () => (/* binding */ convertBoundingBoxToBox),\n/* harmony export */   convertBoxToBoundingBox: () => (/* binding */ convertBoxToBoundingBox),\n/* harmony export */   transformBoxPoints: () => (/* binding */ transformBoxPoints)\n/* harmony export */ });\n/**\n * Bounding boxes tend to be defined as top, left, right, bottom. For various operations\n * it's easier to consider each axis individually. This function returns a bounding box\n * as a map of single-axis min/max values.\n */\nfunction convertBoundingBoxToBox({ top, left, right, bottom, }) {\n    return {\n        x: { min: left, max: right },\n        y: { min: top, max: bottom },\n    };\n}\nfunction convertBoxToBoundingBox({ x, y }) {\n    return { top: y.min, right: x.max, bottom: y.max, left: x.min };\n}\n/**\n * Applies a TransformPoint function to a bounding box. TransformPoint is usually a function\n * provided by Framer to allow measured points to be corrected for device scaling. This is used\n * when measuring DOM elements and DOM event points.\n */\nfunction transformBoxPoints(point, transformPoint) {\n    if (!transformPoint)\n        return point;\n    const topLeft = transformPoint({ x: point.left, y: point.top });\n    const bottomRight = transformPoint({ x: point.right, y: point.bottom });\n    return {\n        top: topLeft.y,\n        left: topLeft.x,\n        bottom: bottomRight.y,\n        right: bottomRight.x,\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/copy.mjs":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/copy.mjs ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copyAxisDeltaInto: () => (/* binding */ copyAxisDeltaInto),\n/* harmony export */   copyAxisInto: () => (/* binding */ copyAxisInto),\n/* harmony export */   copyBoxInto: () => (/* binding */ copyBoxInto)\n/* harmony export */ });\n/**\n * Reset an axis to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisInto(axis, originAxis) {\n    axis.min = originAxis.min;\n    axis.max = originAxis.max;\n}\n/**\n * Reset a box to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyBoxInto(box, originBox) {\n    copyAxisInto(box.x, originBox.x);\n    copyAxisInto(box.y, originBox.y);\n}\n/**\n * Reset a delta to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisDeltaInto(delta, originDelta) {\n    delta.translate = originDelta.translate;\n    delta.scale = originDelta.scale;\n    delta.originPoint = originDelta.originPoint;\n    delta.origin = originDelta.origin;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi9nZW9tZXRyeS9jb3B5Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3RCIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxwcm9qZWN0aW9uXFxnZW9tZXRyeVxcY29weS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZXNldCBhbiBheGlzIHRvIHRoZSBwcm92aWRlZCBvcmlnaW4gYm94LlxuICpcbiAqIFRoaXMgaXMgYSBtdXRhdGl2ZSBvcGVyYXRpb24uXG4gKi9cbmZ1bmN0aW9uIGNvcHlBeGlzSW50byhheGlzLCBvcmlnaW5BeGlzKSB7XG4gICAgYXhpcy5taW4gPSBvcmlnaW5BeGlzLm1pbjtcbiAgICBheGlzLm1heCA9IG9yaWdpbkF4aXMubWF4O1xufVxuLyoqXG4gKiBSZXNldCBhIGJveCB0byB0aGUgcHJvdmlkZWQgb3JpZ2luIGJveC5cbiAqXG4gKiBUaGlzIGlzIGEgbXV0YXRpdmUgb3BlcmF0aW9uLlxuICovXG5mdW5jdGlvbiBjb3B5Qm94SW50byhib3gsIG9yaWdpbkJveCkge1xuICAgIGNvcHlBeGlzSW50byhib3gueCwgb3JpZ2luQm94LngpO1xuICAgIGNvcHlBeGlzSW50byhib3gueSwgb3JpZ2luQm94LnkpO1xufVxuLyoqXG4gKiBSZXNldCBhIGRlbHRhIHRvIHRoZSBwcm92aWRlZCBvcmlnaW4gYm94LlxuICpcbiAqIFRoaXMgaXMgYSBtdXRhdGl2ZSBvcGVyYXRpb24uXG4gKi9cbmZ1bmN0aW9uIGNvcHlBeGlzRGVsdGFJbnRvKGRlbHRhLCBvcmlnaW5EZWx0YSkge1xuICAgIGRlbHRhLnRyYW5zbGF0ZSA9IG9yaWdpbkRlbHRhLnRyYW5zbGF0ZTtcbiAgICBkZWx0YS5zY2FsZSA9IG9yaWdpbkRlbHRhLnNjYWxlO1xuICAgIGRlbHRhLm9yaWdpblBvaW50ID0gb3JpZ2luRGVsdGEub3JpZ2luUG9pbnQ7XG4gICAgZGVsdGEub3JpZ2luID0gb3JpZ2luRGVsdGEub3JpZ2luO1xufVxuXG5leHBvcnQgeyBjb3B5QXhpc0RlbHRhSW50bywgY29weUF4aXNJbnRvLCBjb3B5Qm94SW50byB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/copy.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyAxisDelta: () => (/* binding */ applyAxisDelta),\n/* harmony export */   applyBoxDelta: () => (/* binding */ applyBoxDelta),\n/* harmony export */   applyPointDelta: () => (/* binding */ applyPointDelta),\n/* harmony export */   applyTreeDeltas: () => (/* binding */ applyTreeDeltas),\n/* harmony export */   scalePoint: () => (/* binding */ scalePoint),\n/* harmony export */   transformAxis: () => (/* binding */ transformAxis),\n/* harmony export */   transformBox: () => (/* binding */ transformBox),\n/* harmony export */   translateAxis: () => (/* binding */ translateAxis)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/has-transform.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs\");\n\n\n\n/**\n * Scales a point based on a factor and an originPoint\n */\nfunction scalePoint(point, scale, originPoint) {\n    const distanceFromOrigin = point - originPoint;\n    const scaled = scale * distanceFromOrigin;\n    return originPoint + scaled;\n}\n/**\n * Applies a translate/scale delta to a point\n */\nfunction applyPointDelta(point, translate, scale, originPoint, boxScale) {\n    if (boxScale !== undefined) {\n        point = scalePoint(point, boxScale, originPoint);\n    }\n    return scalePoint(point, scale, originPoint) + translate;\n}\n/**\n * Applies a translate/scale delta to an axis\n */\nfunction applyAxisDelta(axis, translate = 0, scale = 1, originPoint, boxScale) {\n    axis.min = applyPointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = applyPointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Applies a translate/scale delta to a box\n */\nfunction applyBoxDelta(box, { x, y }) {\n    applyAxisDelta(box.x, x.translate, x.scale, x.originPoint);\n    applyAxisDelta(box.y, y.translate, y.scale, y.originPoint);\n}\nconst TREE_SCALE_SNAP_MIN = 0.999999999999;\nconst TREE_SCALE_SNAP_MAX = 1.0000000000001;\n/**\n * Apply a tree of deltas to a box. We do this to calculate the effect of all the transforms\n * in a tree upon our box before then calculating how to project it into our desired viewport-relative box\n *\n * This is the final nested loop within updateLayoutDelta for future refactoring\n */\nfunction applyTreeDeltas(box, treeScale, treePath, isSharedTransition = false) {\n    const treeLength = treePath.length;\n    if (!treeLength)\n        return;\n    // Reset the treeScale\n    treeScale.x = treeScale.y = 1;\n    let node;\n    let delta;\n    for (let i = 0; i < treeLength; i++) {\n        node = treePath[i];\n        delta = node.projectionDelta;\n        /**\n         * TODO: Prefer to remove this, but currently we have motion components with\n         * display: contents in Framer.\n         */\n        const { visualElement } = node.options;\n        if (visualElement &&\n            visualElement.props.style &&\n            visualElement.props.style.display === \"contents\") {\n            continue;\n        }\n        if (isSharedTransition &&\n            node.options.layoutScroll &&\n            node.scroll &&\n            node !== node.root) {\n            transformBox(box, {\n                x: -node.scroll.offset.x,\n                y: -node.scroll.offset.y,\n            });\n        }\n        if (delta) {\n            // Incoporate each ancestor's scale into a culmulative treeScale for this component\n            treeScale.x *= delta.x.scale;\n            treeScale.y *= delta.y.scale;\n            // Apply each ancestor's calculated delta into this component's recorded layout box\n            applyBoxDelta(box, delta);\n        }\n        if (isSharedTransition && (0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_0__.hasTransform)(node.latestValues)) {\n            transformBox(box, node.latestValues);\n        }\n    }\n    /**\n     * Snap tree scale back to 1 if it's within a non-perceivable threshold.\n     * This will help reduce useless scales getting rendered.\n     */\n    if (treeScale.x < TREE_SCALE_SNAP_MAX &&\n        treeScale.x > TREE_SCALE_SNAP_MIN) {\n        treeScale.x = 1.0;\n    }\n    if (treeScale.y < TREE_SCALE_SNAP_MAX &&\n        treeScale.y > TREE_SCALE_SNAP_MIN) {\n        treeScale.y = 1.0;\n    }\n}\nfunction translateAxis(axis, distance) {\n    axis.min = axis.min + distance;\n    axis.max = axis.max + distance;\n}\n/**\n * Apply a transform to an axis from the latest resolved motion values.\n * This function basically acts as a bridge between a flat motion value map\n * and applyAxisDelta\n */\nfunction transformAxis(axis, axisTranslate, axisScale, boxScale, axisOrigin = 0.5) {\n    const originPoint = (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.mixNumber)(axis.min, axis.max, axisOrigin);\n    // Apply the axis delta to the final axis\n    applyAxisDelta(axis, axisTranslate, axisScale, originPoint, boxScale);\n}\n/**\n * Apply a transform to a box from the latest resolved motion values.\n */\nfunction transformBox(box, transform) {\n    transformAxis(box.x, transform.x, transform.scaleX, transform.scale, transform.originX);\n    transformAxis(box.y, transform.y, transform.scaleY, transform.scale, transform.originY);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcAxisDelta: () => (/* binding */ calcAxisDelta),\n/* harmony export */   calcBoxDelta: () => (/* binding */ calcBoxDelta),\n/* harmony export */   calcLength: () => (/* binding */ calcLength),\n/* harmony export */   calcRelativeAxis: () => (/* binding */ calcRelativeAxis),\n/* harmony export */   calcRelativeAxisPosition: () => (/* binding */ calcRelativeAxisPosition),\n/* harmony export */   calcRelativeBox: () => (/* binding */ calcRelativeBox),\n/* harmony export */   calcRelativePosition: () => (/* binding */ calcRelativePosition),\n/* harmony export */   isNear: () => (/* binding */ isNear)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n\n\nconst SCALE_PRECISION = 0.0001;\nconst SCALE_MIN = 1 - SCALE_PRECISION;\nconst SCALE_MAX = 1 + SCALE_PRECISION;\nconst TRANSLATE_PRECISION = 0.01;\nconst TRANSLATE_MIN = 0 - TRANSLATE_PRECISION;\nconst TRANSLATE_MAX = 0 + TRANSLATE_PRECISION;\nfunction calcLength(axis) {\n    return axis.max - axis.min;\n}\nfunction isNear(value, target, maxDistance) {\n    return Math.abs(value - target) <= maxDistance;\n}\nfunction calcAxisDelta(delta, source, target, origin = 0.5) {\n    delta.origin = origin;\n    delta.originPoint = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(source.min, source.max, delta.origin);\n    delta.scale = calcLength(target) / calcLength(source);\n    delta.translate =\n        (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(target.min, target.max, delta.origin) - delta.originPoint;\n    if ((delta.scale >= SCALE_MIN && delta.scale <= SCALE_MAX) ||\n        isNaN(delta.scale)) {\n        delta.scale = 1.0;\n    }\n    if ((delta.translate >= TRANSLATE_MIN &&\n        delta.translate <= TRANSLATE_MAX) ||\n        isNaN(delta.translate)) {\n        delta.translate = 0.0;\n    }\n}\nfunction calcBoxDelta(delta, source, target, origin) {\n    calcAxisDelta(delta.x, source.x, target.x, origin ? origin.originX : undefined);\n    calcAxisDelta(delta.y, source.y, target.y, origin ? origin.originY : undefined);\n}\nfunction calcRelativeAxis(target, relative, parent) {\n    target.min = parent.min + relative.min;\n    target.max = target.min + calcLength(relative);\n}\nfunction calcRelativeBox(target, relative, parent) {\n    calcRelativeAxis(target.x, relative.x, parent.x);\n    calcRelativeAxis(target.y, relative.y, parent.y);\n}\nfunction calcRelativeAxisPosition(target, layout, parent) {\n    target.min = layout.min - parent.min;\n    target.max = target.min + calcLength(layout);\n}\nfunction calcRelativePosition(target, layout, parent) {\n    calcRelativeAxisPosition(target.x, layout.x, parent.x);\n    calcRelativeAxisPosition(target.y, layout.y, parent.y);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeAxisDelta: () => (/* binding */ removeAxisDelta),\n/* harmony export */   removeAxisTransforms: () => (/* binding */ removeAxisTransforms),\n/* harmony export */   removeBoxTransforms: () => (/* binding */ removeBoxTransforms),\n/* harmony export */   removePointDelta: () => (/* binding */ removePointDelta)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(pages-dir-node)/../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _delta_apply_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./delta-apply.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs\");\n\n\n\n/**\n * Remove a delta from a point. This is essentially the steps of applyPointDelta in reverse\n */\nfunction removePointDelta(point, translate, scale, originPoint, boxScale) {\n    point -= translate;\n    point = (0,_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_0__.scalePoint)(point, 1 / scale, originPoint);\n    if (boxScale !== undefined) {\n        point = (0,_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_0__.scalePoint)(point, 1 / boxScale, originPoint);\n    }\n    return point;\n}\n/**\n * Remove a delta from an axis. This is essentially the steps of applyAxisDelta in reverse\n */\nfunction removeAxisDelta(axis, translate = 0, scale = 1, origin = 0.5, boxScale, originAxis = axis, sourceAxis = axis) {\n    if (motion_dom__WEBPACK_IMPORTED_MODULE_1__.percent.test(translate)) {\n        translate = parseFloat(translate);\n        const relativeProgress = (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.mixNumber)(sourceAxis.min, sourceAxis.max, translate / 100);\n        translate = relativeProgress - sourceAxis.min;\n    }\n    if (typeof translate !== \"number\")\n        return;\n    let originPoint = (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.mixNumber)(originAxis.min, originAxis.max, origin);\n    if (axis === originAxis)\n        originPoint -= translate;\n    axis.min = removePointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = removePointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Remove a transforms from an axis. This is essentially the steps of applyAxisTransforms in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeAxisTransforms(axis, transforms, [key, scaleKey, originKey], origin, sourceAxis) {\n    removeAxisDelta(axis, transforms[key], transforms[scaleKey], transforms[originKey], transforms.scale, origin, sourceAxis);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Remove a transforms from an box. This is essentially the steps of applyAxisBox in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeBoxTransforms(box, transforms, originBox, sourceBox) {\n    removeAxisTransforms(box.x, transforms, xKeys, originBox ? originBox.x : undefined, sourceBox ? sourceBox.x : undefined);\n    removeAxisTransforms(box.y, transforms, yKeys, originBox ? originBox.y : undefined, sourceBox ? sourceBox.y : undefined);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/models.mjs":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/models.mjs ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAxis: () => (/* binding */ createAxis),\n/* harmony export */   createAxisDelta: () => (/* binding */ createAxisDelta),\n/* harmony export */   createBox: () => (/* binding */ createBox),\n/* harmony export */   createDelta: () => (/* binding */ createDelta)\n/* harmony export */ });\nconst createAxisDelta = () => ({\n    translate: 0,\n    scale: 1,\n    origin: 0,\n    originPoint: 0,\n});\nconst createDelta = () => ({\n    x: createAxisDelta(),\n    y: createAxisDelta(),\n});\nconst createAxis = () => ({ min: 0, max: 0 });\nconst createBox = () => ({\n    x: createAxis(),\n    y: createAxis(),\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcHJvamVjdGlvbi9nZW9tZXRyeS9tb2RlbHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCw0QkFBNEIsZ0JBQWdCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRThEIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxmcmFtZXItbW90aW9uQDEyLjIzLjFfQGVtb3RfNzI3YmRlZjI2N2QzMWVjM2I2NmM5MzM5ZTQyYjQzNGRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHByb2plY3Rpb25cXGdlb21ldHJ5XFxtb2RlbHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGNyZWF0ZUF4aXNEZWx0YSA9ICgpID0+ICh7XG4gICAgdHJhbnNsYXRlOiAwLFxuICAgIHNjYWxlOiAxLFxuICAgIG9yaWdpbjogMCxcbiAgICBvcmlnaW5Qb2ludDogMCxcbn0pO1xuY29uc3QgY3JlYXRlRGVsdGEgPSAoKSA9PiAoe1xuICAgIHg6IGNyZWF0ZUF4aXNEZWx0YSgpLFxuICAgIHk6IGNyZWF0ZUF4aXNEZWx0YSgpLFxufSk7XG5jb25zdCBjcmVhdGVBeGlzID0gKCkgPT4gKHsgbWluOiAwLCBtYXg6IDAgfSk7XG5jb25zdCBjcmVhdGVCb3ggPSAoKSA9PiAoe1xuICAgIHg6IGNyZWF0ZUF4aXMoKSxcbiAgICB5OiBjcmVhdGVBeGlzKCksXG59KTtcblxuZXhwb3J0IHsgY3JlYXRlQXhpcywgY3JlYXRlQXhpc0RlbHRhLCBjcmVhdGVCb3gsIGNyZWF0ZURlbHRhIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/models.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/utils.mjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/utils.mjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aspectRatio: () => (/* binding */ aspectRatio),\n/* harmony export */   axisDeltaEquals: () => (/* binding */ axisDeltaEquals),\n/* harmony export */   axisEquals: () => (/* binding */ axisEquals),\n/* harmony export */   axisEqualsRounded: () => (/* binding */ axisEqualsRounded),\n/* harmony export */   boxEquals: () => (/* binding */ boxEquals),\n/* harmony export */   boxEqualsRounded: () => (/* binding */ boxEqualsRounded),\n/* harmony export */   isDeltaZero: () => (/* binding */ isDeltaZero)\n/* harmony export */ });\n/* harmony import */ var _delta_calc_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./delta-calc.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\");\n\n\nfunction isAxisDeltaZero(delta) {\n    return delta.translate === 0 && delta.scale === 1;\n}\nfunction isDeltaZero(delta) {\n    return isAxisDeltaZero(delta.x) && isAxisDeltaZero(delta.y);\n}\nfunction axisEquals(a, b) {\n    return a.min === b.min && a.max === b.max;\n}\nfunction boxEquals(a, b) {\n    return axisEquals(a.x, b.x) && axisEquals(a.y, b.y);\n}\nfunction axisEqualsRounded(a, b) {\n    return (Math.round(a.min) === Math.round(b.min) &&\n        Math.round(a.max) === Math.round(b.max));\n}\nfunction boxEqualsRounded(a, b) {\n    return axisEqualsRounded(a.x, b.x) && axisEqualsRounded(a.y, b.y);\n}\nfunction aspectRatio(box) {\n    return (0,_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_0__.calcLength)(box.x) / (0,_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_0__.calcLength)(box.y);\n}\nfunction axisDeltaEquals(a, b) {\n    return (a.translate === b.translate &&\n        a.scale === b.scale &&\n        a.originPoint === b.originPoint);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/utils.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mixValues: () => (/* binding */ mixValues)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"../../node_modules/.pnpm/motion-utils@12.23.1/node_modules/motion-utils/dist/es/index.mjs\");\n\n\n\nconst borders = [\"TopLeft\", \"TopRight\", \"BottomLeft\", \"BottomRight\"];\nconst numBorders = borders.length;\nconst asNumber = (value) => typeof value === \"string\" ? parseFloat(value) : value;\nconst isPx = (value) => typeof value === \"number\" || motion_dom__WEBPACK_IMPORTED_MODULE_0__.px.test(value);\nfunction mixValues(target, follow, lead, progress, shouldCrossfadeOpacity, isOnlyMember) {\n    if (shouldCrossfadeOpacity) {\n        target.opacity = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(0, lead.opacity ?? 1, easeCrossfadeIn(progress));\n        target.opacityExit = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(follow.opacity ?? 1, 0, easeCrossfadeOut(progress));\n    }\n    else if (isOnlyMember) {\n        target.opacity = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(follow.opacity ?? 1, lead.opacity ?? 1, progress);\n    }\n    /**\n     * Mix border radius\n     */\n    for (let i = 0; i < numBorders; i++) {\n        const borderLabel = `border${borders[i]}Radius`;\n        let followRadius = getRadius(follow, borderLabel);\n        let leadRadius = getRadius(lead, borderLabel);\n        if (followRadius === undefined && leadRadius === undefined)\n            continue;\n        followRadius || (followRadius = 0);\n        leadRadius || (leadRadius = 0);\n        const canMix = followRadius === 0 ||\n            leadRadius === 0 ||\n            isPx(followRadius) === isPx(leadRadius);\n        if (canMix) {\n            target[borderLabel] = Math.max((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(asNumber(followRadius), asNumber(leadRadius), progress), 0);\n            if (motion_dom__WEBPACK_IMPORTED_MODULE_0__.percent.test(leadRadius) || motion_dom__WEBPACK_IMPORTED_MODULE_0__.percent.test(followRadius)) {\n                target[borderLabel] += \"%\";\n            }\n        }\n        else {\n            target[borderLabel] = leadRadius;\n        }\n    }\n    /**\n     * Mix rotation\n     */\n    if (follow.rotate || lead.rotate) {\n        target.rotate = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(follow.rotate || 0, lead.rotate || 0, progress);\n    }\n}\nfunction getRadius(values, radiusName) {\n    return values[radiusName] !== undefined\n        ? values[radiusName]\n        : values.borderRadius;\n}\n// /**\n//  * We only want to mix the background color if there's a follow element\n//  * that we're not crossfading opacity between. For instance with switch\n//  * AnimateSharedLayout animations, this helps the illusion of a continuous\n//  * element being animated but also cuts down on the number of paints triggered\n//  * for elements where opacity is doing that work for us.\n//  */\n// if (\n//     !hasFollowElement &&\n//     latestLeadValues.backgroundColor &&\n//     latestFollowValues.backgroundColor\n// ) {\n//     /**\n//      * This isn't ideal performance-wise as mixColor is creating a new function every frame.\n//      * We could probably create a mixer that runs at the start of the animation but\n//      * the idea behind the crossfader is that it runs dynamically between two potentially\n//      * changing targets (ie opacity or borderRadius may be animating independently via variants)\n//      */\n//     leadState.backgroundColor = followState.backgroundColor = mixColor(\n//         latestFollowValues.backgroundColor as string,\n//         latestLeadValues.backgroundColor as string\n//     )(p)\n// }\nconst easeCrossfadeIn = /*@__PURE__*/ compress(0, 0.5, motion_utils__WEBPACK_IMPORTED_MODULE_1__.circOut);\nconst easeCrossfadeOut = /*@__PURE__*/ compress(0.5, 0.95, motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop);\nfunction compress(min, max, easing) {\n    return (p) => {\n        // Could replace ifs with clamp\n        if (p < min)\n            return 0;\n        if (p > max)\n            return 1;\n        return easing((0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.progress)(min, max, p));\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertBoundingBoxToBox: () => (/* binding */ convertBoundingBoxToBox),\n/* harmony export */   convertBoxToBoundingBox: () => (/* binding */ convertBoxToBoundingBox),\n/* harmony export */   transformBoxPoints: () => (/* binding */ transformBoxPoints)\n/* harmony export */ });\n/**\n * Bounding boxes tend to be defined as top, left, right, bottom. For various operations\n * it's easier to consider each axis individually. This function returns a bounding box\n * as a map of single-axis min/max values.\n */\nfunction convertBoundingBoxToBox({ top, left, right, bottom, }) {\n    return {\n        x: { min: left, max: right },\n        y: { min: top, max: bottom },\n    };\n}\nfunction convertBoxToBoundingBox({ x, y }) {\n    return { top: y.min, right: x.max, bottom: y.max, left: x.min };\n}\n/**\n * Applies a TransformPoint function to a bounding box. TransformPoint is usually a function\n * provided by Framer to allow measured points to be corrected for device scaling. This is used\n * when measuring DOM elements and DOM event points.\n */\nfunction transformBoxPoints(point, transformPoint) {\n    if (!transformPoint)\n        return point;\n    const topLeft = transformPoint({ x: point.left, y: point.top });\n    const bottomRight = transformPoint({ x: point.right, y: point.bottom });\n    return {\n        top: topLeft.y,\n        left: topLeft.x,\n        bottom: bottomRight.y,\n        right: bottomRight.x,\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/copy.mjs":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/copy.mjs ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copyAxisDeltaInto: () => (/* binding */ copyAxisDeltaInto),\n/* harmony export */   copyAxisInto: () => (/* binding */ copyAxisInto),\n/* harmony export */   copyBoxInto: () => (/* binding */ copyBoxInto)\n/* harmony export */ });\n/**\n * Reset an axis to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisInto(axis, originAxis) {\n    axis.min = originAxis.min;\n    axis.max = originAxis.max;\n}\n/**\n * Reset a box to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyBoxInto(box, originBox) {\n    copyAxisInto(box.x, originBox.x);\n    copyAxisInto(box.y, originBox.y);\n}\n/**\n * Reset a delta to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisDeltaInto(delta, originDelta) {\n    delta.translate = originDelta.translate;\n    delta.scale = originDelta.scale;\n    delta.originPoint = originDelta.originPoint;\n    delta.origin = originDelta.origin;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3Byb2plY3Rpb24vZ2VvbWV0cnkvY29weS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFd0QiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccHJvamVjdGlvblxcZ2VvbWV0cnlcXGNvcHkubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUmVzZXQgYW4gYXhpcyB0byB0aGUgcHJvdmlkZWQgb3JpZ2luIGJveC5cbiAqXG4gKiBUaGlzIGlzIGEgbXV0YXRpdmUgb3BlcmF0aW9uLlxuICovXG5mdW5jdGlvbiBjb3B5QXhpc0ludG8oYXhpcywgb3JpZ2luQXhpcykge1xuICAgIGF4aXMubWluID0gb3JpZ2luQXhpcy5taW47XG4gICAgYXhpcy5tYXggPSBvcmlnaW5BeGlzLm1heDtcbn1cbi8qKlxuICogUmVzZXQgYSBib3ggdG8gdGhlIHByb3ZpZGVkIG9yaWdpbiBib3guXG4gKlxuICogVGhpcyBpcyBhIG11dGF0aXZlIG9wZXJhdGlvbi5cbiAqL1xuZnVuY3Rpb24gY29weUJveEludG8oYm94LCBvcmlnaW5Cb3gpIHtcbiAgICBjb3B5QXhpc0ludG8oYm94LngsIG9yaWdpbkJveC54KTtcbiAgICBjb3B5QXhpc0ludG8oYm94LnksIG9yaWdpbkJveC55KTtcbn1cbi8qKlxuICogUmVzZXQgYSBkZWx0YSB0byB0aGUgcHJvdmlkZWQgb3JpZ2luIGJveC5cbiAqXG4gKiBUaGlzIGlzIGEgbXV0YXRpdmUgb3BlcmF0aW9uLlxuICovXG5mdW5jdGlvbiBjb3B5QXhpc0RlbHRhSW50byhkZWx0YSwgb3JpZ2luRGVsdGEpIHtcbiAgICBkZWx0YS50cmFuc2xhdGUgPSBvcmlnaW5EZWx0YS50cmFuc2xhdGU7XG4gICAgZGVsdGEuc2NhbGUgPSBvcmlnaW5EZWx0YS5zY2FsZTtcbiAgICBkZWx0YS5vcmlnaW5Qb2ludCA9IG9yaWdpbkRlbHRhLm9yaWdpblBvaW50O1xuICAgIGRlbHRhLm9yaWdpbiA9IG9yaWdpbkRlbHRhLm9yaWdpbjtcbn1cblxuZXhwb3J0IHsgY29weUF4aXNEZWx0YUludG8sIGNvcHlBeGlzSW50bywgY29weUJveEludG8gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/copy.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyAxisDelta: () => (/* binding */ applyAxisDelta),\n/* harmony export */   applyBoxDelta: () => (/* binding */ applyBoxDelta),\n/* harmony export */   applyPointDelta: () => (/* binding */ applyPointDelta),\n/* harmony export */   applyTreeDeltas: () => (/* binding */ applyTreeDeltas),\n/* harmony export */   scalePoint: () => (/* binding */ scalePoint),\n/* harmony export */   transformAxis: () => (/* binding */ transformAxis),\n/* harmony export */   transformBox: () => (/* binding */ transformBox),\n/* harmony export */   translateAxis: () => (/* binding */ translateAxis)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/has-transform.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs\");\n\n\n\n/**\n * Scales a point based on a factor and an originPoint\n */\nfunction scalePoint(point, scale, originPoint) {\n    const distanceFromOrigin = point - originPoint;\n    const scaled = scale * distanceFromOrigin;\n    return originPoint + scaled;\n}\n/**\n * Applies a translate/scale delta to a point\n */\nfunction applyPointDelta(point, translate, scale, originPoint, boxScale) {\n    if (boxScale !== undefined) {\n        point = scalePoint(point, boxScale, originPoint);\n    }\n    return scalePoint(point, scale, originPoint) + translate;\n}\n/**\n * Applies a translate/scale delta to an axis\n */\nfunction applyAxisDelta(axis, translate = 0, scale = 1, originPoint, boxScale) {\n    axis.min = applyPointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = applyPointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Applies a translate/scale delta to a box\n */\nfunction applyBoxDelta(box, { x, y }) {\n    applyAxisDelta(box.x, x.translate, x.scale, x.originPoint);\n    applyAxisDelta(box.y, y.translate, y.scale, y.originPoint);\n}\nconst TREE_SCALE_SNAP_MIN = 0.999999999999;\nconst TREE_SCALE_SNAP_MAX = 1.0000000000001;\n/**\n * Apply a tree of deltas to a box. We do this to calculate the effect of all the transforms\n * in a tree upon our box before then calculating how to project it into our desired viewport-relative box\n *\n * This is the final nested loop within updateLayoutDelta for future refactoring\n */\nfunction applyTreeDeltas(box, treeScale, treePath, isSharedTransition = false) {\n    const treeLength = treePath.length;\n    if (!treeLength)\n        return;\n    // Reset the treeScale\n    treeScale.x = treeScale.y = 1;\n    let node;\n    let delta;\n    for (let i = 0; i < treeLength; i++) {\n        node = treePath[i];\n        delta = node.projectionDelta;\n        /**\n         * TODO: Prefer to remove this, but currently we have motion components with\n         * display: contents in Framer.\n         */\n        const { visualElement } = node.options;\n        if (visualElement &&\n            visualElement.props.style &&\n            visualElement.props.style.display === \"contents\") {\n            continue;\n        }\n        if (isSharedTransition &&\n            node.options.layoutScroll &&\n            node.scroll &&\n            node !== node.root) {\n            transformBox(box, {\n                x: -node.scroll.offset.x,\n                y: -node.scroll.offset.y,\n            });\n        }\n        if (delta) {\n            // Incoporate each ancestor's scale into a culmulative treeScale for this component\n            treeScale.x *= delta.x.scale;\n            treeScale.y *= delta.y.scale;\n            // Apply each ancestor's calculated delta into this component's recorded layout box\n            applyBoxDelta(box, delta);\n        }\n        if (isSharedTransition && (0,_utils_has_transform_mjs__WEBPACK_IMPORTED_MODULE_0__.hasTransform)(node.latestValues)) {\n            transformBox(box, node.latestValues);\n        }\n    }\n    /**\n     * Snap tree scale back to 1 if it's within a non-perceivable threshold.\n     * This will help reduce useless scales getting rendered.\n     */\n    if (treeScale.x < TREE_SCALE_SNAP_MAX &&\n        treeScale.x > TREE_SCALE_SNAP_MIN) {\n        treeScale.x = 1.0;\n    }\n    if (treeScale.y < TREE_SCALE_SNAP_MAX &&\n        treeScale.y > TREE_SCALE_SNAP_MIN) {\n        treeScale.y = 1.0;\n    }\n}\nfunction translateAxis(axis, distance) {\n    axis.min = axis.min + distance;\n    axis.max = axis.max + distance;\n}\n/**\n * Apply a transform to an axis from the latest resolved motion values.\n * This function basically acts as a bridge between a flat motion value map\n * and applyAxisDelta\n */\nfunction transformAxis(axis, axisTranslate, axisScale, boxScale, axisOrigin = 0.5) {\n    const originPoint = (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.mixNumber)(axis.min, axis.max, axisOrigin);\n    // Apply the axis delta to the final axis\n    applyAxisDelta(axis, axisTranslate, axisScale, originPoint, boxScale);\n}\n/**\n * Apply a transform to a box from the latest resolved motion values.\n */\nfunction transformBox(box, transform) {\n    transformAxis(box.x, transform.x, transform.scaleX, transform.scale, transform.originX);\n    transformAxis(box.y, transform.y, transform.scaleY, transform.scale, transform.originY);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcAxisDelta: () => (/* binding */ calcAxisDelta),\n/* harmony export */   calcBoxDelta: () => (/* binding */ calcBoxDelta),\n/* harmony export */   calcLength: () => (/* binding */ calcLength),\n/* harmony export */   calcRelativeAxis: () => (/* binding */ calcRelativeAxis),\n/* harmony export */   calcRelativeAxisPosition: () => (/* binding */ calcRelativeAxisPosition),\n/* harmony export */   calcRelativeBox: () => (/* binding */ calcRelativeBox),\n/* harmony export */   calcRelativePosition: () => (/* binding */ calcRelativePosition),\n/* harmony export */   isNear: () => (/* binding */ isNear)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n\n\nconst SCALE_PRECISION = 0.0001;\nconst SCALE_MIN = 1 - SCALE_PRECISION;\nconst SCALE_MAX = 1 + SCALE_PRECISION;\nconst TRANSLATE_PRECISION = 0.01;\nconst TRANSLATE_MIN = 0 - TRANSLATE_PRECISION;\nconst TRANSLATE_MAX = 0 + TRANSLATE_PRECISION;\nfunction calcLength(axis) {\n    return axis.max - axis.min;\n}\nfunction isNear(value, target, maxDistance) {\n    return Math.abs(value - target) <= maxDistance;\n}\nfunction calcAxisDelta(delta, source, target, origin = 0.5) {\n    delta.origin = origin;\n    delta.originPoint = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(source.min, source.max, delta.origin);\n    delta.scale = calcLength(target) / calcLength(source);\n    delta.translate =\n        (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(target.min, target.max, delta.origin) - delta.originPoint;\n    if ((delta.scale >= SCALE_MIN && delta.scale <= SCALE_MAX) ||\n        isNaN(delta.scale)) {\n        delta.scale = 1.0;\n    }\n    if ((delta.translate >= TRANSLATE_MIN &&\n        delta.translate <= TRANSLATE_MAX) ||\n        isNaN(delta.translate)) {\n        delta.translate = 0.0;\n    }\n}\nfunction calcBoxDelta(delta, source, target, origin) {\n    calcAxisDelta(delta.x, source.x, target.x, origin ? origin.originX : undefined);\n    calcAxisDelta(delta.y, source.y, target.y, origin ? origin.originY : undefined);\n}\nfunction calcRelativeAxis(target, relative, parent) {\n    target.min = parent.min + relative.min;\n    target.max = target.min + calcLength(relative);\n}\nfunction calcRelativeBox(target, relative, parent) {\n    calcRelativeAxis(target.x, relative.x, parent.x);\n    calcRelativeAxis(target.y, relative.y, parent.y);\n}\nfunction calcRelativeAxisPosition(target, layout, parent) {\n    target.min = layout.min - parent.min;\n    target.max = target.min + calcLength(layout);\n}\nfunction calcRelativePosition(target, layout, parent) {\n    calcRelativeAxisPosition(target.x, layout.x, parent.x);\n    calcRelativeAxisPosition(target.y, layout.y, parent.y);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeAxisDelta: () => (/* binding */ removeAxisDelta),\n/* harmony export */   removeAxisTransforms: () => (/* binding */ removeAxisTransforms),\n/* harmony export */   removeBoxTransforms: () => (/* binding */ removeBoxTransforms),\n/* harmony export */   removePointDelta: () => (/* binding */ removePointDelta)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"../../node_modules/.pnpm/motion-dom@12.23.1/node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var _delta_apply_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./delta-apply.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs\");\n\n\n\n/**\n * Remove a delta from a point. This is essentially the steps of applyPointDelta in reverse\n */\nfunction removePointDelta(point, translate, scale, originPoint, boxScale) {\n    point -= translate;\n    point = (0,_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_0__.scalePoint)(point, 1 / scale, originPoint);\n    if (boxScale !== undefined) {\n        point = (0,_delta_apply_mjs__WEBPACK_IMPORTED_MODULE_0__.scalePoint)(point, 1 / boxScale, originPoint);\n    }\n    return point;\n}\n/**\n * Remove a delta from an axis. This is essentially the steps of applyAxisDelta in reverse\n */\nfunction removeAxisDelta(axis, translate = 0, scale = 1, origin = 0.5, boxScale, originAxis = axis, sourceAxis = axis) {\n    if (motion_dom__WEBPACK_IMPORTED_MODULE_1__.percent.test(translate)) {\n        translate = parseFloat(translate);\n        const relativeProgress = (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.mixNumber)(sourceAxis.min, sourceAxis.max, translate / 100);\n        translate = relativeProgress - sourceAxis.min;\n    }\n    if (typeof translate !== \"number\")\n        return;\n    let originPoint = (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.mixNumber)(originAxis.min, originAxis.max, origin);\n    if (axis === originAxis)\n        originPoint -= translate;\n    axis.min = removePointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = removePointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Remove a transforms from an axis. This is essentially the steps of applyAxisTransforms in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeAxisTransforms(axis, transforms, [key, scaleKey, originKey], origin, sourceAxis) {\n    removeAxisDelta(axis, transforms[key], transforms[scaleKey], transforms[originKey], transforms.scale, origin, sourceAxis);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Remove a transforms from an box. This is essentially the steps of applyAxisBox in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeBoxTransforms(box, transforms, originBox, sourceBox) {\n    removeAxisTransforms(box.x, transforms, xKeys, originBox ? originBox.x : undefined, sourceBox ? sourceBox.x : undefined);\n    removeAxisTransforms(box.y, transforms, yKeys, originBox ? originBox.y : undefined, sourceBox ? sourceBox.y : undefined);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/models.mjs":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/models.mjs ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAxis: () => (/* binding */ createAxis),\n/* harmony export */   createAxisDelta: () => (/* binding */ createAxisDelta),\n/* harmony export */   createBox: () => (/* binding */ createBox),\n/* harmony export */   createDelta: () => (/* binding */ createDelta)\n/* harmony export */ });\nconst createAxisDelta = () => ({\n    translate: 0,\n    scale: 1,\n    origin: 0,\n    originPoint: 0,\n});\nconst createDelta = () => ({\n    x: createAxisDelta(),\n    y: createAxisDelta(),\n});\nconst createAxis = () => ({ min: 0, max: 0 });\nconst createBox = () => ({\n    x: createAxis(),\n    y: createAxis(),\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZyYW1lci1tb3Rpb25AMTIuMjMuMV9AZW1vdF83MjdiZGVmMjY3ZDMxZWMzYjY2YzkzMzllNDJiNDM0ZC9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3Byb2plY3Rpb24vZ2VvbWV0cnkvbW9kZWxzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsNEJBQTRCLGdCQUFnQjtBQUM1QztBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUU4RCIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWVyLW1vdGlvbkAxMi4yMy4xX0BlbW90XzcyN2JkZWYyNjdkMzFlYzNiNjZjOTMzOWU0MmI0MzRkXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxwcm9qZWN0aW9uXFxnZW9tZXRyeVxcbW9kZWxzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjcmVhdGVBeGlzRGVsdGEgPSAoKSA9PiAoe1xuICAgIHRyYW5zbGF0ZTogMCxcbiAgICBzY2FsZTogMSxcbiAgICBvcmlnaW46IDAsXG4gICAgb3JpZ2luUG9pbnQ6IDAsXG59KTtcbmNvbnN0IGNyZWF0ZURlbHRhID0gKCkgPT4gKHtcbiAgICB4OiBjcmVhdGVBeGlzRGVsdGEoKSxcbiAgICB5OiBjcmVhdGVBeGlzRGVsdGEoKSxcbn0pO1xuY29uc3QgY3JlYXRlQXhpcyA9ICgpID0+ICh7IG1pbjogMCwgbWF4OiAwIH0pO1xuY29uc3QgY3JlYXRlQm94ID0gKCkgPT4gKHtcbiAgICB4OiBjcmVhdGVBeGlzKCksXG4gICAgeTogY3JlYXRlQXhpcygpLFxufSk7XG5cbmV4cG9ydCB7IGNyZWF0ZUF4aXMsIGNyZWF0ZUF4aXNEZWx0YSwgY3JlYXRlQm94LCBjcmVhdGVEZWx0YSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/models.mjs\n");

/***/ }),

/***/ "../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/utils.mjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/utils.mjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aspectRatio: () => (/* binding */ aspectRatio),\n/* harmony export */   axisDeltaEquals: () => (/* binding */ axisDeltaEquals),\n/* harmony export */   axisEquals: () => (/* binding */ axisEquals),\n/* harmony export */   axisEqualsRounded: () => (/* binding */ axisEqualsRounded),\n/* harmony export */   boxEquals: () => (/* binding */ boxEquals),\n/* harmony export */   boxEqualsRounded: () => (/* binding */ boxEqualsRounded),\n/* harmony export */   isDeltaZero: () => (/* binding */ isDeltaZero)\n/* harmony export */ });\n/* harmony import */ var _delta_calc_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./delta-calc.mjs */ \"../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs\");\n\n\nfunction isAxisDeltaZero(delta) {\n    return delta.translate === 0 && delta.scale === 1;\n}\nfunction isDeltaZero(delta) {\n    return isAxisDeltaZero(delta.x) && isAxisDeltaZero(delta.y);\n}\nfunction axisEquals(a, b) {\n    return a.min === b.min && a.max === b.max;\n}\nfunction boxEquals(a, b) {\n    return axisEquals(a.x, b.x) && axisEquals(a.y, b.y);\n}\nfunction axisEqualsRounded(a, b) {\n    return (Math.round(a.min) === Math.round(b.min) &&\n        Math.round(a.max) === Math.round(b.max));\n}\nfunction boxEqualsRounded(a, b) {\n    return axisEqualsRounded(a.x, b.x) && axisEqualsRounded(a.y, b.y);\n}\nfunction aspectRatio(box) {\n    return (0,_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_0__.calcLength)(box.x) / (0,_delta_calc_mjs__WEBPACK_IMPORTED_MODULE_0__.calcLength)(box.y);\n}\nfunction axisDeltaEquals(a, b) {\n    return (a.translate === b.translate &&\n        a.scale === b.scale &&\n        a.originPoint === b.originPoint);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/framer-motion@12.23.1_@emot_727bdef267d31ec3b66c9339e42b434d/node_modules/framer-motion/dist/es/projection/geometry/utils.mjs\n");

/***/ })

};
;