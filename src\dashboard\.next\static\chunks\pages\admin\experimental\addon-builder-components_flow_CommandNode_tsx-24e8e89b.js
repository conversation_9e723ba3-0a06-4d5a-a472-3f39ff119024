"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/admin/experimental/addon-builder-components_flow_CommandNode_tsx-24e8e89b"],{

/***/ "(pages-dir-browser)/./components/flow/CommandNode.tsx":
/*!*****************************************!*\
  !*** ./components/flow/CommandNode.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var reactflow__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactflow */ \"(pages-dir-browser)/../../node_modules/.pnpm/reactflow@11.11.4_@types+re_67d5462d77eed4181e6818cd1ca61a06/node_modules/reactflow/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionButton,AccordionIcon,AccordionItem,AccordionPanel,Alert,AlertDescription,AlertIcon,Badge,Box,Button,Checkbox,CheckboxGroup,Code,Collapse,Divider,FormControl,FormLabel,HStack,IconButton,Input,InputGroup,InputLeftAddon,Modal,ModalBody,ModalCloseButton,ModalContent,ModalHeader,ModalOverlay,NumberDecrementStepper,NumberIncrementStepper,NumberInput,NumberInputField,NumberInputStepper,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure!=!@chakra-ui/react */ \"(pages-dir-browser)/__barrel_optimize__?names=Accordion,AccordionButton,AccordionIcon,AccordionItem,AccordionPanel,Alert,AlertDescription,AlertIcon,Badge,Box,Button,Checkbox,CheckboxGroup,Code,Collapse,Divider,FormControl,FormLabel,HStack,IconButton,Input,InputGroup,InputLeftAddon,Modal,ModalBody,ModalCloseButton,ModalContent,ModalHeader,ModalOverlay,NumberDecrementStepper,NumberIncrementStepper,NumberInput,NumberInputField,NumberInputStepper,Select,SimpleGrid,Switch,Tab,TabList,TabPanel,TabPanels,Tabs,Text,Textarea,VStack,useDisclosure!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiCopy,FiEye,FiEyeOff,FiMinus,FiPlus,FiSettings,FiTrash2,FiZap!=!react-icons/fi */ \"(pages-dir-browser)/__barrel_optimize__?names=FiCopy,FiEye,FiEyeOff,FiMinus,FiPlus,FiSettings,FiTrash2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/ThemeContext */ \"(pages-dir-browser)/./contexts/ThemeContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n// Available variables for command context\nconst commandVariables = {\n    command: [\n        {\n            name: '{command.name}',\n            description: 'Command name that was executed',\n            icon: '⚡'\n        },\n        {\n            name: '{command.user}',\n            description: 'User who executed the command',\n            icon: '👤'\n        },\n        {\n            name: '{command.channel}',\n            description: 'Channel where command was executed',\n            icon: '📺'\n        },\n        {\n            name: '{command.server}',\n            description: 'Server where command was executed',\n            icon: '🏠'\n        },\n        {\n            name: '{command.timestamp}',\n            description: 'When the command was executed',\n            icon: '⏰'\n        }\n    ],\n    options: [\n        {\n            name: '{option.name}',\n            description: 'Value of a specific option',\n            icon: '🔧'\n        },\n        {\n            name: '{option.user}',\n            description: 'User option value',\n            icon: '👤'\n        },\n        {\n            name: '{option.channel}',\n            description: 'Channel option value',\n            icon: '📺'\n        },\n        {\n            name: '{option.role}',\n            description: 'Role option value',\n            icon: '🎭'\n        },\n        {\n            name: '{option.string}',\n            description: 'String option value',\n            icon: '💬'\n        },\n        {\n            name: '{option.number}',\n            description: 'Number option value',\n            icon: '🔢'\n        },\n        {\n            name: '{option.boolean}',\n            description: 'Boolean option value',\n            icon: '✅'\n        }\n    ],\n    user: [\n        {\n            name: '{user.id}',\n            description: 'User ID',\n            icon: '🆔'\n        },\n        {\n            name: '{user.username}',\n            description: 'Username',\n            icon: '👤'\n        },\n        {\n            name: '{user.displayName}',\n            description: 'Display Name',\n            icon: '📝'\n        },\n        {\n            name: '{user.tag}',\n            description: 'User Tag (username#0000)',\n            icon: '🏷️'\n        },\n        {\n            name: '{user.mention}',\n            description: 'User Mention (<@id>)',\n            icon: '📢'\n        },\n        {\n            name: '{user.avatar}',\n            description: 'Avatar URL',\n            icon: '🖼️'\n        },\n        {\n            name: '{user.roles}',\n            description: 'User Roles',\n            icon: '🎭'\n        },\n        {\n            name: '{user.permissions}',\n            description: 'User Permissions',\n            icon: '🔐'\n        },\n        {\n            name: '{user.joinedAt}',\n            description: 'Server Join Date',\n            icon: '🚪'\n        }\n    ],\n    channel: [\n        {\n            name: '{channel.id}',\n            description: 'Channel ID',\n            icon: '🆔'\n        },\n        {\n            name: '{channel.name}',\n            description: 'Channel Name',\n            icon: '📺'\n        },\n        {\n            name: '{channel.mention}',\n            description: 'Channel Mention (<#id>)',\n            icon: '📢'\n        },\n        {\n            name: '{channel.type}',\n            description: 'Channel Type',\n            icon: '📋'\n        },\n        {\n            name: '{channel.topic}',\n            description: 'Channel Topic',\n            icon: '💬'\n        },\n        {\n            name: '{channel.memberCount}',\n            description: 'Member Count',\n            icon: '👥'\n        }\n    ],\n    server: [\n        {\n            name: '{server.id}',\n            description: 'Server ID',\n            icon: '🆔'\n        },\n        {\n            name: '{server.name}',\n            description: 'Server Name',\n            icon: '🏠'\n        },\n        {\n            name: '{server.icon}',\n            description: 'Server Icon URL',\n            icon: '🖼️'\n        },\n        {\n            name: '{server.memberCount}',\n            description: 'Member Count',\n            icon: '👥'\n        },\n        {\n            name: '{server.owner}',\n            description: 'Server Owner',\n            icon: '👑'\n        },\n        {\n            name: '{server.boostLevel}',\n            description: 'Server Boost Level',\n            icon: '🚀'\n        }\n    ]\n};\nconst permissionsList = [\n    'ADMINISTRATOR',\n    'MANAGE_GUILD',\n    'MANAGE_ROLES',\n    'MANAGE_CHANNELS',\n    'KICK_MEMBERS',\n    'BAN_MEMBERS',\n    'MANAGE_MESSAGES',\n    'EMBED_LINKS',\n    'ATTACH_FILES',\n    'READ_MESSAGE_HISTORY',\n    'MENTION_EVERYONE',\n    'USE_EXTERNAL_EMOJIS',\n    'CONNECT',\n    'SPEAK',\n    'MUTE_MEMBERS',\n    'DEAFEN_MEMBERS',\n    'MOVE_MEMBERS',\n    'USE_VAD',\n    'CHANGE_NICKNAME',\n    'MANAGE_NICKNAMES',\n    'MANAGE_WEBHOOKS',\n    'MANAGE_EMOJIS',\n    'MODERATE_MEMBERS',\n    'VIEW_AUDIT_LOG',\n    'MANAGE_EVENTS',\n    'MANAGE_THREADS',\n    'CREATE_PUBLIC_THREADS',\n    'CREATE_PRIVATE_THREADS',\n    'USE_EXTERNAL_STICKERS',\n    'SEND_MESSAGES_IN_THREADS',\n    'START_EMBEDDED_ACTIVITIES'\n];\nconst optionTypes = [\n    {\n        value: 'string',\n        label: '📝 String - Text input'\n    },\n    {\n        value: 'integer',\n        label: '🔢 Integer - Whole number'\n    },\n    {\n        value: 'number',\n        label: '🔢 Number - Decimal number'\n    },\n    {\n        value: 'boolean',\n        label: '✅ Boolean - True/False'\n    },\n    {\n        value: 'user',\n        label: '👤 User - Discord user'\n    },\n    {\n        value: 'channel',\n        label: '📺 Channel - Discord channel'\n    },\n    {\n        value: 'role',\n        label: '🎭 Role - Discord role'\n    },\n    {\n        value: 'mentionable',\n        label: '📢 Mentionable - User or role'\n    },\n    {\n        value: 'attachment',\n        label: '📎 Attachment - File upload'\n    }\n];\nconst CommandNode = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s((param)=>{\n    let { data, selected, id, updateNodeData: updateParentNodeData } = param;\n    var _nodeData_options, _nodeData_options1, _nodeData_options2, _nodeData_examples, _nodeData_options3;\n    _s();\n    const { currentScheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const { isOpen, onOpen, onClose } = (0,_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)();\n    const [nodeData, setNodeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"CommandNode.useState\": ()=>({\n                guildOnly: false,\n                adminOnly: false,\n                allowDMs: false,\n                cooldown: 0,\n                options: [],\n                category: 'general',\n                examples: [],\n                permissions: [],\n                ephemeral: false,\n                deferReply: false,\n                ...data\n            })\n    }[\"CommandNode.useState\"]);\n    const [showVariables, setShowVariables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateNodeData = (updates)=>{\n        setNodeData((prev)=>({\n                ...prev,\n                ...updates\n            }));\n    };\n    const handleModalClose = ()=>{\n        // Update parent nodes array when modal closes\n        if (updateParentNodeData && id) {\n            updateParentNodeData(id, nodeData);\n        }\n        onClose();\n    };\n    const addOption = ()=>{\n        const newOption = {\n            name: '',\n            description: '',\n            type: 'string',\n            required: false,\n            choices: []\n        };\n        updateNodeData({\n            options: [\n                ...nodeData.options || [],\n                newOption\n            ]\n        });\n    };\n    const updateOption = (index, updates)=>{\n        const newOptions = [\n            ...nodeData.options || []\n        ];\n        newOptions[index] = {\n            ...newOptions[index],\n            ...updates\n        };\n        updateNodeData({\n            options: newOptions\n        });\n    };\n    const removeOption = (index)=>{\n        const newOptions = (nodeData.options || []).filter((_, i)=>i !== index);\n        updateNodeData({\n            options: newOptions\n        });\n    };\n    const addChoice = (optionIndex)=>{\n        const newOptions = [\n            ...nodeData.options || []\n        ];\n        if (!newOptions[optionIndex].choices) {\n            newOptions[optionIndex].choices = [];\n        }\n        newOptions[optionIndex].choices.push({\n            name: '',\n            value: ''\n        });\n        updateNodeData({\n            options: newOptions\n        });\n    };\n    const updateChoice = (optionIndex, choiceIndex, field, value)=>{\n        const newOptions = [\n            ...nodeData.options || []\n        ];\n        if (newOptions[optionIndex].choices) {\n            newOptions[optionIndex].choices[choiceIndex][field] = value;\n            updateNodeData({\n                options: newOptions\n            });\n        }\n    };\n    const removeChoice = (optionIndex, choiceIndex)=>{\n        const newOptions = [\n            ...nodeData.options || []\n        ];\n        if (newOptions[optionIndex].choices) {\n            newOptions[optionIndex].choices = newOptions[optionIndex].choices.filter((_, i)=>i !== choiceIndex);\n            updateNodeData({\n                options: newOptions\n            });\n        }\n    };\n    const copyVariable = (variable)=>{\n        navigator.clipboard.writeText(variable);\n    };\n    const renderVariablesList = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Collapse, {\n            in: showVariables,\n            animateOpacity: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                bg: currentScheme.colors.surface,\n                border: \"1px solid\",\n                borderColor: currentScheme.colors.border,\n                borderRadius: \"md\",\n                p: 4,\n                mt: 3,\n                maxH: \"400px\",\n                overflowY: \"auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Accordion, {\n                    allowMultiple: true,\n                    children: Object.entries(commandVariables).map((param)=>{\n                        let [category, variables] = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AccordionItem, {\n                            border: \"none\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AccordionButton, {\n                                    px: 0,\n                                    py: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                            flex: \"1\",\n                                            textAlign: \"left\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                fontSize: \"sm\",\n                                                fontWeight: \"bold\",\n                                                color: currentScheme.colors.text,\n                                                textTransform: \"capitalize\",\n                                                children: [\n                                                    category,\n                                                    \" Variables\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AccordionIcon, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AccordionPanel, {\n                                    px: 0,\n                                    py: 2,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                        spacing: 2,\n                                        align: \"stretch\",\n                                        children: variables.map((variable)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                spacing: 2,\n                                                p: 2,\n                                                bg: currentScheme.colors.background,\n                                                borderRadius: \"md\",\n                                                cursor: \"pointer\",\n                                                _hover: {\n                                                    bg: currentScheme.colors.surface\n                                                },\n                                                onClick: ()=>copyVariable(variable.name),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                        fontSize: \"sm\",\n                                                        children: variable.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Code, {\n                                                        fontSize: \"xs\",\n                                                        colorScheme: \"blue\",\n                                                        children: variable.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                        fontSize: \"xs\",\n                                                        color: currentScheme.colors.textSecondary,\n                                                        flex: \"1\",\n                                                        children: variable.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.IconButton, {\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCopy, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        size: \"xs\",\n                                                        variant: \"ghost\",\n                                                        \"aria-label\": \"Copy variable\",\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            copyVariable(variable.name);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, variable.name, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n            lineNumber: 260,\n            columnNumber: 5\n        }, undefined);\n    var _nodeData_options_length, _nodeData_options_length1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                bg: currentScheme.colors.surface,\n                border: \"2px solid \".concat(selected ? '#3b82f6' : currentScheme.colors.border),\n                borderRadius: \"md\",\n                p: 2,\n                minW: \"140px\",\n                maxW: \"180px\",\n                boxShadow: \"sm\",\n                position: \"relative\",\n                _hover: {\n                    boxShadow: 'md',\n                    transform: 'translateY(-1px)'\n                },\n                transition: \"all 0.2s\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_5__.Handle, {\n                        type: \"target\",\n                        position: reactflow__WEBPACK_IMPORTED_MODULE_5__.Position.Top,\n                        style: {\n                            background: '#3b82f6',\n                            border: \"2px solid \".concat(currentScheme.colors.surface),\n                            width: '12px',\n                            height: '12px',\n                            top: '-6px',\n                            left: '50%',\n                            transform: 'translateX(-50%)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                        spacing: 1,\n                        align: \"stretch\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                justify: \"space-between\",\n                                align: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                        spacing: 1,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                                bg: \"blue.500\",\n                                                color: \"white\",\n                                                borderRadius: \"full\",\n                                                p: 0.5,\n                                                fontSize: \"xs\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiZap, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                fontSize: \"xs\",\n                                                fontWeight: \"bold\",\n                                                color: currentScheme.colors.text,\n                                                children: \"Command\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.IconButton, {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiSettings, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        size: \"xs\",\n                                        variant: \"ghost\",\n                                        onClick: onOpen,\n                                        \"aria-label\": \"Configure command\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    fontSize: \"xs\",\n                                    color: currentScheme.colors.text,\n                                    noOfLines: 1,\n                                    children: [\n                                        \"/\",\n                                        nodeData.commandName || 'unnamed'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, undefined),\n                            nodeData.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    fontSize: \"xs\",\n                                    color: currentScheme.colors.textSecondary,\n                                    noOfLines: 1,\n                                    children: nodeData.description.length > 25 ? nodeData.description.substring(0, 25) + '...' : nodeData.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                spacing: 1,\n                                flexWrap: \"wrap\",\n                                children: [\n                                    ((_nodeData_options_length = (_nodeData_options = nodeData.options) === null || _nodeData_options === void 0 ? void 0 : _nodeData_options.length) !== null && _nodeData_options_length !== void 0 ? _nodeData_options_length : 0) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"blue\",\n                                        children: [\n                                            (_nodeData_options1 = nodeData.options) === null || _nodeData_options1 === void 0 ? void 0 : _nodeData_options1.length,\n                                            \" option\",\n                                            ((_nodeData_options_length1 = (_nodeData_options2 = nodeData.options) === null || _nodeData_options2 === void 0 ? void 0 : _nodeData_options2.length) !== null && _nodeData_options_length1 !== void 0 ? _nodeData_options_length1 : 0) !== 1 ? 's' : ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    nodeData.adminOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"red\",\n                                        children: \"Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    nodeData.cooldown && nodeData.cooldown > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        size: \"xs\",\n                                        colorScheme: \"orange\",\n                                        children: [\n                                            nodeData.cooldown,\n                                            \"s\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reactflow__WEBPACK_IMPORTED_MODULE_5__.Handle, {\n                        type: \"source\",\n                        position: reactflow__WEBPACK_IMPORTED_MODULE_5__.Position.Bottom,\n                        style: {\n                            background: '#3b82f6',\n                            border: \"2px solid \".concat(currentScheme.colors.surface),\n                            width: '12px',\n                            height: '12px',\n                            bottom: '-6px',\n                            left: '50%',\n                            transform: 'translateX(-50%)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n                isOpen: isOpen,\n                onClose: handleModalClose,\n                size: \"4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalOverlay, {\n                        bg: \"blackAlpha.600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalContent, {\n                        bg: currentScheme.colors.background,\n                        border: \"2px solid\",\n                        borderColor: \"blue.400\",\n                        maxW: \"1200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalHeader, {\n                                color: currentScheme.colors.text,\n                                children: \"⚡ Configure Command\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalCloseButton, {}, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalBody, {\n                                pb: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                    spacing: 6,\n                                    align: \"stretch\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                    justify: \"space-between\",\n                                                    align: \"center\",\n                                                    mb: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                            fontSize: \"sm\",\n                                                            fontWeight: \"bold\",\n                                                            color: currentScheme.colors.text,\n                                                            children: \"Available Variables\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"ghost\",\n                                                            leftIcon: showVariables ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiEyeOff, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 47\n                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiEye, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 62\n                                                            }, void 0),\n                                                            onClick: ()=>setShowVariables(!showVariables),\n                                                            children: [\n                                                                showVariables ? 'Hide' : 'Show',\n                                                                \" Variables\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                                                    status: \"info\",\n                                                    borderRadius: \"md\",\n                                                    mb: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertIcon, {}, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                                                            fontSize: \"sm\",\n                                                            children: \"\\uD83D\\uDCA1 Use variables in your command responses! Click any variable below to copy it. Variables are replaced with actual values when your command runs.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                renderVariablesList()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Divider, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                            variant: \"enclosed\",\n                                            colorScheme: \"blue\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.TabList, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                                            children: \"Basic Info\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                                            children: \"Options\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                                            children: \"Permissions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                                            children: \"Advanced\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.TabPanels, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                spacing: 4,\n                                                                align: \"stretch\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.SimpleGrid, {\n                                                                        columns: 2,\n                                                                        spacing: 4,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                                isRequired: true,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                        color: currentScheme.colors.text,\n                                                                                        children: \"Command Name\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 478,\n                                                                                        columnNumber: 21\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.InputGroup, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.InputLeftAddon, {\n                                                                                                bg: currentScheme.colors.surface,\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"/\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 480,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                value: nodeData.commandName || '',\n                                                                                                onChange: (e)=>updateNodeData({\n                                                                                                        commandName: e.target.value\n                                                                                                    }),\n                                                                                                placeholder: \"ping\",\n                                                                                                bg: currentScheme.colors.background,\n                                                                                                color: currentScheme.colors.text,\n                                                                                                borderColor: currentScheme.colors.border\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 483,\n                                                                                                columnNumber: 23\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 479,\n                                                                                        columnNumber: 21\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 477,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                        color: currentScheme.colors.text,\n                                                                                        children: \"Category\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 495,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                                                        value: nodeData.category || 'general',\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                category: e.target.value\n                                                                                            }),\n                                                                                        bg: currentScheme.colors.background,\n                                                                                        color: currentScheme.colors.text,\n                                                                                        borderColor: currentScheme.colors.border,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"general\",\n                                                                                                children: \"General\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 503,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"moderation\",\n                                                                                                children: \"Moderation\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 504,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"fun\",\n                                                                                                children: \"Fun\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 505,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"utility\",\n                                                                                                children: \"Utility\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 506,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"admin\",\n                                                                                                children: \"Admin\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 507,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"info\",\n                                                                                                children: \"Info\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 508,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"music\",\n                                                                                                children: \"Music\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 509,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"games\",\n                                                                                                children: \"Games\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 510,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 496,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 19\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                        isRequired: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                color: currentScheme.colors.text,\n                                                                                children: \"Description\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 516,\n                                                                                columnNumber: 21\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                                                                value: nodeData.description || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        description: e.target.value\n                                                                                    }),\n                                                                                placeholder: \"What does this command do?\",\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                minH: \"80px\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 517,\n                                                                                columnNumber: 21\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                color: currentScheme.colors.text,\n                                                                                children: \"Usage Examples\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 529,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                                                                value: ((_nodeData_examples = nodeData.examples) === null || _nodeData_examples === void 0 ? void 0 : _nodeData_examples.join('\\n')) || '',\n                                                                                onChange: (e)=>updateNodeData({\n                                                                                        examples: e.target.value.split('\\n').filter((ex)=>ex.trim())\n                                                                                    }),\n                                                                                placeholder: \"/ping\\n/ping server\\n/ping {user.mention}\",\n                                                                                bg: currentScheme.colors.background,\n                                                                                color: currentScheme.colors.text,\n                                                                                borderColor: currentScheme.colors.border,\n                                                                                minH: \"80px\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                fontSize: \"xs\",\n                                                                                color: currentScheme.colors.textSecondary,\n                                                                                mt: 1,\n                                                                                children: \"One example per line\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 539,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 19\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                spacing: 4,\n                                                                align: \"stretch\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                        justify: \"space-between\",\n                                                                        align: \"center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                fontSize: \"lg\",\n                                                                                fontWeight: \"bold\",\n                                                                                color: currentScheme.colors.text,\n                                                                                children: \"Command Options\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 550,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPlus, {}, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                    lineNumber: 554,\n                                                                                    columnNumber: 37\n                                                                                }, void 0),\n                                                                                onClick: addOption,\n                                                                                colorScheme: \"blue\",\n                                                                                size: \"sm\",\n                                                                                children: \"Add Option\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 553,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                                                                        status: \"info\",\n                                                                        borderRadius: \"md\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertIcon, {}, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 564,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                                                                                fontSize: \"sm\",\n                                                                                children: \"Options are parameters users can provide with your command. They appear as autocomplete fields in Discord.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 565,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                        spacing: 4,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            (_nodeData_options3 = nodeData.options) === null || _nodeData_options3 === void 0 ? void 0 : _nodeData_options3.map((option, index)=>{\n                                                                                var _option_choices;\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                                                                    p: 4,\n                                                                                    bg: currentScheme.colors.surface,\n                                                                                    borderRadius: \"md\",\n                                                                                    border: \"1px solid\",\n                                                                                    borderColor: currentScheme.colors.border,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                            justify: \"space-between\",\n                                                                                            align: \"center\",\n                                                                                            mb: 3,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                    fontSize: \"md\",\n                                                                                                    fontWeight: \"bold\",\n                                                                                                    color: currentScheme.colors.text,\n                                                                                                    children: [\n                                                                                                        \"Option \",\n                                                                                                        index + 1\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 581,\n                                                                                                    columnNumber: 31\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.IconButton, {\n                                                                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTrash2, {}, void 0, false, {\n                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                        lineNumber: 585,\n                                                                                                        columnNumber: 39\n                                                                                                    }, void 0),\n                                                                                                    size: \"sm\",\n                                                                                                    colorScheme: \"red\",\n                                                                                                    variant: \"ghost\",\n                                                                                                    onClick: ()=>removeOption(index),\n                                                                                                    \"aria-label\": \"Remove option\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 584,\n                                                                                                    columnNumber: 25\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                            lineNumber: 580,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                            spacing: 3,\n                                                                                            align: \"stretch\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.SimpleGrid, {\n                                                                                                    columns: 2,\n                                                                                                    spacing: 3,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                                                            isRequired: true,\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                                                    fontSize: \"sm\",\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    children: \"Option Name\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 597,\n                                                                                                                    columnNumber: 35\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                                    value: option.name,\n                                                                                                                    onChange: (e)=>updateOption(index, {\n                                                                                                                            name: e.target.value\n                                                                                                                        }),\n                                                                                                                    placeholder: \"user\",\n                                                                                                                    bg: currentScheme.colors.background,\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    borderColor: currentScheme.colors.border,\n                                                                                                                    size: \"sm\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 598,\n                                                                                                                    columnNumber: 35\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 596,\n                                                                                                            columnNumber: 33\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                                                            isRequired: true,\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                                                    fontSize: \"sm\",\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    children: \"Option Type\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 610,\n                                                                                                                    columnNumber: 35\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                                                                                    value: option.type,\n                                                                                                                    onChange: (e)=>updateOption(index, {\n                                                                                                                            type: e.target.value\n                                                                                                                        }),\n                                                                                                                    bg: currentScheme.colors.background,\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    borderColor: currentScheme.colors.border,\n                                                                                                                    size: \"sm\",\n                                                                                                                    children: optionTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                                            value: type.value,\n                                                                                                                            children: type.label\n                                                                                                                        }, type.value, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                            lineNumber: 620,\n                                                                                                                            columnNumber: 39\n                                                                                                                        }, undefined))\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 611,\n                                                                                                                    columnNumber: 27\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 609,\n                                                                                                            columnNumber: 33\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 595,\n                                                                                                    columnNumber: 31\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                                            fontSize: \"sm\",\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            children: \"Description\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 629,\n                                                                                                            columnNumber: 25\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                            value: option.description,\n                                                                                                            onChange: (e)=>updateOption(index, {\n                                                                                                                    description: e.target.value\n                                                                                                                }),\n                                                                                                            placeholder: \"The user to ping\",\n                                                                                                            bg: currentScheme.colors.background,\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            borderColor: currentScheme.colors.border,\n                                                                                                            size: \"sm\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 630,\n                                                                                                            columnNumber: 25\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 628,\n                                                                                                    columnNumber: 25\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                                                            isChecked: option.required,\n                                                                                                            onChange: (e)=>updateOption(index, {\n                                                                                                                    required: e.target.checked\n                                                                                                                }),\n                                                                                                            colorScheme: \"blue\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 642,\n                                                                                                            columnNumber: 33\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                            fontSize: \"sm\",\n                                                                                                            color: currentScheme.colors.text,\n                                                                                                            children: \"Required option\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 647,\n                                                                                                            columnNumber: 33\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 641,\n                                                                                                    columnNumber: 31\n                                                                                                }, undefined),\n                                                                                                (option.type === 'string' || option.type === 'integer' || option.type === 'number') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                                            justify: \"space-between\",\n                                                                                                            align: \"center\",\n                                                                                                            mb: 2,\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                                    fontSize: \"sm\",\n                                                                                                                    fontWeight: \"bold\",\n                                                                                                                    color: currentScheme.colors.text,\n                                                                                                                    children: \"Predefined Choices (Optional)\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 655,\n                                                                                                                    columnNumber: 37\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                                    size: \"xs\",\n                                                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPlus, {}, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                        lineNumber: 660,\n                                                                                                                        columnNumber: 31\n                                                                                                                    }, void 0),\n                                                                                                                    onClick: ()=>addChoice(index),\n                                                                                                                    colorScheme: \"blue\",\n                                                                                                                    variant: \"ghost\",\n                                                                                                                    children: \"Add Choice\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 658,\n                                                                                                                    columnNumber: 19\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 654,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                                            spacing: 2,\n                                                                                                            align: \"stretch\",\n                                                                                                            children: (_option_choices = option.choices) === null || _option_choices === void 0 ? void 0 : _option_choices.map((choice, choiceIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                                                    spacing: 2,\n                                                                                                                    children: [\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                                            value: choice.name,\n                                                                                                                            onChange: (e)=>updateChoice(index, choiceIndex, 'name', e.target.value),\n                                                                                                                            placeholder: \"Choice name\",\n                                                                                                                            bg: currentScheme.colors.background,\n                                                                                                                            color: currentScheme.colors.text,\n                                                                                                                            borderColor: currentScheme.colors.border,\n                                                                                                                            size: \"sm\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                            lineNumber: 672,\n                                                                                                                            columnNumber: 41\n                                                                                                                        }, undefined),\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                                            value: choice.value,\n                                                                                                                            onChange: (e)=>updateChoice(index, choiceIndex, 'value', e.target.value),\n                                                                                                                            placeholder: \"Choice value\",\n                                                                                                                            bg: currentScheme.colors.background,\n                                                                                                                            color: currentScheme.colors.text,\n                                                                                                                            borderColor: currentScheme.colors.border,\n                                                                                                                            size: \"sm\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                            lineNumber: 681,\n                                                                                                                            columnNumber: 41\n                                                                                                                        }, undefined),\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.IconButton, {\n                                                                                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCopy_FiEye_FiEyeOff_FiMinus_FiPlus_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiMinus, {}, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                                lineNumber: 691,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, void 0),\n                                                                                                                            size: \"sm\",\n                                                                                                                            colorScheme: \"red\",\n                                                                                                                            variant: \"ghost\",\n                                                                                                                            onClick: ()=>removeChoice(index, choiceIndex),\n                                                                                                                            \"aria-label\": \"Remove choice\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                            lineNumber: 690,\n                                                                                                                            columnNumber: 41\n                                                                                                                        }, undefined)\n                                                                                                                    ]\n                                                                                                                }, choiceIndex, true, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                                    lineNumber: 671,\n                                                                                                                    columnNumber: 39\n                                                                                                                }, undefined))\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                            lineNumber: 669,\n                                                                                                            columnNumber: 35\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                    lineNumber: 653,\n                                                                                                    columnNumber: 33\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                            lineNumber: 594,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, index, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                    lineNumber: 572,\n                                                                                    columnNumber: 27\n                                                                                }, undefined);\n                                                                            }),\n                                                                            (!nodeData.options || nodeData.options.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                                                                                status: \"info\",\n                                                                                borderRadius: \"md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertIcon, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 709,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                                                                                        children: \"No options configured. Your command will work without any parameters.\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 710,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                spacing: 4,\n                                                                align: \"stretch\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                        fontSize: \"lg\",\n                                                                        fontWeight: \"bold\",\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Command Permissions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                                                                        status: \"warning\",\n                                                                        borderRadius: \"md\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertIcon, {}, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 727,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                                                                                fontSize: \"sm\",\n                                                                                children: \"Be careful with permissions! Overly restrictive permissions can prevent legitimate users from using your command.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 728,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                        spacing: 4,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                spacing: 4,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                                        isChecked: nodeData.adminOnly,\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                adminOnly: e.target.checked\n                                                                                            }),\n                                                                                        colorScheme: \"red\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 735,\n                                                                                        columnNumber: 23\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                        align: \"start\",\n                                                                                        spacing: 0,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                fontSize: \"sm\",\n                                                                                                fontWeight: \"bold\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"Admin Only\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 741,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.textSecondary,\n                                                                                                children: \"Only server administrators can use this command\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 744,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 740,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 734,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                spacing: 4,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                                        isChecked: nodeData.guildOnly,\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                guildOnly: e.target.checked\n                                                                                            }),\n                                                                                        colorScheme: \"blue\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 751,\n                                                                                        columnNumber: 23\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                        align: \"start\",\n                                                                                        spacing: 0,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                fontSize: \"sm\",\n                                                                                                fontWeight: \"bold\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"Server Only\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 757,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.textSecondary,\n                                                                                                children: \"Command can only be used in servers, not DMs\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 760,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 756,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 750,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                spacing: 4,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                                        isChecked: nodeData.allowDMs,\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                allowDMs: e.target.checked\n                                                                                            }),\n                                                                                        colorScheme: \"green\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 767,\n                                                                                        columnNumber: 23\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                        align: \"start\",\n                                                                                        spacing: 0,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                fontSize: \"sm\",\n                                                                                                fontWeight: \"bold\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"Allow DMs\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 773,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.textSecondary,\n                                                                                                children: \"Command can be used in direct messages\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 776,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 772,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 766,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Divider, {}, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 783,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                fontSize: \"md\",\n                                                                                fontWeight: \"bold\",\n                                                                                color: currentScheme.colors.text,\n                                                                                mb: 3,\n                                                                                children: \"Required Permissions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 786,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                fontSize: \"sm\",\n                                                                                color: currentScheme.colors.textSecondary,\n                                                                                mb: 3,\n                                                                                children: \"Select the Discord permissions users need to use this command\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 789,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.CheckboxGroup, {\n                                                                                value: nodeData.permissions || [],\n                                                                                onChange: (value)=>updateNodeData({\n                                                                                        permissions: value\n                                                                                    }),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.SimpleGrid, {\n                                                                                    columns: 3,\n                                                                                    spacing: 2,\n                                                                                    children: permissionsList.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                                                            value: permission,\n                                                                                            colorScheme: \"blue\",\n                                                                                            size: \"sm\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: permission.replace(/_/g, ' ').toLowerCase().replace(/\\b\\w/g, (l)=>l.toUpperCase())\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 804,\n                                                                                                columnNumber: 33\n                                                                                            }, undefined)\n                                                                                        }, permission, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                            lineNumber: 798,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                    lineNumber: 796,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 792,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                spacing: 4,\n                                                                align: \"stretch\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                        fontSize: \"lg\",\n                                                                        fontWeight: \"bold\",\n                                                                        color: currentScheme.colors.text,\n                                                                        children: \"Advanced Settings\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 818,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                                                                color: currentScheme.colors.text,\n                                                                                children: \"Cooldown (seconds)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 823,\n                                                                                columnNumber: 23\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.NumberInput, {\n                                                                                value: nodeData.cooldown || 0,\n                                                                                onChange: (valueString)=>updateNodeData({\n                                                                                        cooldown: parseInt(valueString) || 0\n                                                                                    }),\n                                                                                min: 0,\n                                                                                max: 3600,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.NumberInputField, {\n                                                                                        bg: currentScheme.colors.background,\n                                                                                        color: currentScheme.colors.text,\n                                                                                        borderColor: currentScheme.colors.border\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 830,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.NumberInputStepper, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.NumberIncrementStepper, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 836,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.NumberDecrementStepper, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 837,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 835,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 824,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                fontSize: \"xs\",\n                                                                                color: currentScheme.colors.textSecondary,\n                                                                                mt: 1,\n                                                                                children: \"How long users must wait between uses (0 = no cooldown)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 840,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 822,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                        spacing: 4,\n                                                                        align: \"stretch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                spacing: 4,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                                        isChecked: nodeData.ephemeral,\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                ephemeral: e.target.checked\n                                                                                            }),\n                                                                                        colorScheme: \"blue\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 847,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                        align: \"start\",\n                                                                                        spacing: 0,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                fontSize: \"sm\",\n                                                                                                fontWeight: \"bold\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"Ephemeral Response\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 853,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.textSecondary,\n                                                                                                children: \"Command response is only visible to the user who ran it\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 856,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 852,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                                                spacing: 4,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                                                        isChecked: nodeData.deferReply,\n                                                                                        onChange: (e)=>updateNodeData({\n                                                                                                deferReply: e.target.checked\n                                                                                            }),\n                                                                                        colorScheme: \"orange\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 863,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                                                                                        align: \"start\",\n                                                                                        spacing: 0,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                fontSize: \"sm\",\n                                                                                                fontWeight: \"bold\",\n                                                                                                color: currentScheme.colors.text,\n                                                                                                children: \"Defer Reply\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 869,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                                                                fontSize: \"xs\",\n                                                                                                color: currentScheme.colors.textSecondary,\n                                                                                                children: 'Show \"thinking...\" message while processing (for slow commands)'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                                lineNumber: 872,\n                                                                                                columnNumber: 29\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                        lineNumber: 868,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                                lineNumber: 862,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                        lineNumber: 845,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            colorScheme: \"blue\",\n                                            onClick: ()=>{\n                                                // Save the configuration\n                                                data.commandName = nodeData.commandName;\n                                                data.description = nodeData.description;\n                                                data.options = nodeData.options;\n                                                data.permissions = nodeData.permissions;\n                                                data.cooldown = nodeData.cooldown;\n                                                data.guildOnly = nodeData.guildOnly;\n                                                data.adminOnly = nodeData.adminOnly;\n                                                data.allowDMs = nodeData.allowDMs;\n                                                data.category = nodeData.category;\n                                                data.examples = nodeData.examples;\n                                                data.ephemeral = nodeData.ephemeral;\n                                                data.deferReply = nodeData.deferReply;\n                                                data.label = nodeData.commandName ? \"/\".concat(nodeData.commandName) : 'Command';\n                                                onClose();\n                                            },\n                                            size: \"lg\",\n                                            width: \"full\",\n                                            children: \"Save Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                            lineNumber: 883,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\flow\\\\CommandNode.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"pR9I4jNtRtxaXVFCiuHVna31Lys=\", false, function() {\n    return [\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useTheme,\n        _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useDisclosure\n    ];\n})), \"pR9I4jNtRtxaXVFCiuHVna31Lys=\", false, function() {\n    return [\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.useTheme,\n        _barrel_optimize_names_Accordion_AccordionButton_AccordionIcon_AccordionItem_AccordionPanel_Alert_AlertDescription_AlertIcon_Badge_Box_Button_Checkbox_CheckboxGroup_Code_Collapse_Divider_FormControl_FormLabel_HStack_IconButton_Input_InputGroup_InputLeftAddon_Modal_ModalBody_ModalCloseButton_ModalContent_ModalHeader_ModalOverlay_NumberDecrementStepper_NumberIncrementStepper_NumberInput_NumberInputField_NumberInputStepper_Select_SimpleGrid_Switch_Tab_TabList_TabPanel_TabPanels_Tabs_Text_Textarea_VStack_useDisclosure_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useDisclosure\n    ];\n});\n_c1 = CommandNode;\nCommandNode.displayName = 'CommandNode';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommandNode);\nvar _c, _c1;\n$RefreshReg$(_c, \"CommandNode$memo\");\n$RefreshReg$(_c1, \"CommandNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/flow/CommandNode.tsx\n"));

/***/ })

}]);