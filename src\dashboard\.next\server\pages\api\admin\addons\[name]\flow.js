"use strict";(()=>{var e={};e.id=3753,e.ids=[3753],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},98774:(e,r,s)=>{s.r(r),s.d(r,{config:()=>j,default:()=>m,routeModule:()=>w});var t={};s.r(t),s.d(t,{default:()=>f});var n=s(93433),o=s(20264),a=s(20584),d=s(15806),i=s(94506),u=s(29021),l=s.n(u),c=s(33873),p=s.n(c);async function f(e,r){let s=await (0,d.getServerSession)(e,r,i.authOptions);if(!s)return r.status(401).json({error:"Unauthorized"});if(!s.user.isAdmin)return r.status(403).json({error:"Forbidden - Admin access required"});let{name:t}=e.query;if(!t||"string"!=typeof t)return r.status(400).json({error:"Invalid addon name"});if("GET"!==e.method)return r.status(405).json({error:"Method not allowed"});try{let e=function(e){let r=process.cwd().includes("dashboard")?p().resolve(process.cwd(),"..",".."):process.cwd(),s=e.replace(/\s+/g,"-");for(let t of[p().join(r,"src","addons",e),p().join(r,"src","addons",s),p().join(process.cwd(),"src","addons",e),p().join(process.cwd(),"src","addons",s),p().join(process.cwd(),"..","..","src","addons",e),p().join(process.cwd(),"..","..","src","addons",s)])if(l().existsSync(t))return t;return null}(t);if(!e)return r.status(404).json({error:"Addon not found"});let s=p().join(e,"flow.json");if(!l().existsSync(s))return r.status(404).json({error:"Original flow data not found",details:"This addon was built before flow recovery was implemented"});let n=l().readFileSync(s,"utf8"),o=JSON.parse(n);return r.status(200).json(o)}catch(e){return r.status(500).json({error:"Internal server error",details:e.message})}}let m=(0,a.M)(t,"default"),j=(0,a.M)(t,"config"),w=new n.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/admin/addons/[name]/flow",pathname:"/api/admin/addons/[name]/flow",bundlePath:"",filename:""},userland:t})}};var r=require("../../../../../webpack-api-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>s(98774));module.exports=t})();