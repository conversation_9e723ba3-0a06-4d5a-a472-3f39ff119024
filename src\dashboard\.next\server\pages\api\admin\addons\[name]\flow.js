"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/addons/[name]/flow";
exports.ids = ["pages/api/admin/addons/[name]/flow"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Faddons%2F%5Bname%5D%2Fflow&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Caddons%5C%5Bname%5D%5Cflow.ts&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Faddons%2F%5Bname%5D%2Fflow&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Caddons%5C%5Bname%5D%5Cflow.ts&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_addons_name_flow_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\addons\\[name]\\flow.ts */ \"(api-node)/./pages/api/admin/addons/[name]/flow.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_addons_name_flow_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_addons_name_flow_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/addons/[name]/flow\",\n        pathname: \"/api/admin/addons/[name]/flow\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_admin_addons_name_flow_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Faddons%2F%5Bname%5D%2Fflow&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Caddons%5C%5Bname%5D%5Cflow.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/admin/addons/[name]/flow.ts":
/*!***********************************************!*\
  !*** ./pages/api/admin/addons/[name]/flow.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// Helper function to find project root (folder containing package.json)\nfunction findProjectRoot(start = process.cwd()) {\n    let dir = start;\n    while(dir !== path__WEBPACK_IMPORTED_MODULE_3___default().parse(dir).root){\n        if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(path__WEBPACK_IMPORTED_MODULE_3___default().join(dir, 'package.json'))) return dir;\n        dir = path__WEBPACK_IMPORTED_MODULE_3___default().dirname(dir);\n    }\n    return start; // fallback\n}\n// Helper function to find addon path\nfunction findAddonPath(name) {\n    const projectRoot = process.cwd().includes('dashboard') ? path__WEBPACK_IMPORTED_MODULE_3___default().resolve(process.cwd(), '..', '..') : process.cwd();\n    const nameSlug = name.replace(/\\s+/g, '-');\n    const possiblePaths = [\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(projectRoot, 'src', 'addons', name),\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(projectRoot, 'src', 'addons', nameSlug),\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'src', 'addons', name),\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'src', 'addons', nameSlug),\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), '..', '..', 'src', 'addons', name),\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), '..', '..', 'src', 'addons', nameSlug)\n    ];\n    for (const addonPath of possiblePaths){\n        if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(addonPath)) {\n            return addonPath;\n        }\n    }\n    return null;\n}\nasync function handler(req, res) {\n    // Check authentication\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    if (!session) {\n        return res.status(401).json({\n            error: 'Unauthorized'\n        });\n    }\n    // Check admin permission\n    const isAdmin = session.user.isAdmin;\n    if (!isAdmin) {\n        return res.status(403).json({\n            error: 'Forbidden - Admin access required'\n        });\n    }\n    const { name } = req.query;\n    if (!name || typeof name !== 'string') {\n        return res.status(400).json({\n            error: 'Invalid addon name'\n        });\n    }\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        // Find the addon path\n        const addonPath = findAddonPath(name);\n        if (!addonPath) {\n            return res.status(404).json({\n                error: 'Addon not found'\n            });\n        }\n        // Check for flow.json file\n        const flowPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(addonPath, 'flow.json');\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(flowPath)) {\n            return res.status(404).json({\n                error: 'Original flow data not found',\n                details: 'This addon was built before flow recovery was implemented'\n            });\n        }\n        // Read and return the flow data\n        const flowData = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(flowPath, 'utf8');\n        const parsedFlow = JSON.parse(flowData);\n        return res.status(200).json(parsedFlow);\n    } catch (error) {\n        console.error('Error loading flow data:', error);\n        return res.status(500).json({\n            error: 'Internal server error',\n            details: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/admin/addons/[name]/flow.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Faddons%2F%5Bname%5D%2Fflow&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Caddons%5C%5Bname%5D%5Cflow.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();