"use strict";(()=>{var e={};e.id=5756,e.ids=[5756],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},18152:(e,r,t)=>{t.r(r),t.d(r,{config:()=>g,default:()=>b,routeModule:()=>x});var o={};t.r(o),t.d(o,{default:()=>h});var s=t(93433),n=t(20264),a=t(20584),i=t(15806),d=t(94506),u=t(98580),l=t(12518);let c=null,p=u.dashboardConfig.database?.url||"mongodb://localhost:27017",f=u.dashboardConfig.database?.name||"discord_bot";async function m(){return c||(c=await l.MongoClient.connect(p,{...u.dashboardConfig.database?.options||{}})),c.db(f)}async function h(e,r){let{id:t}=e.query;if(!t||"string"!=typeof t)return r.status(400).json({error:"Invalid error ID"});try{let o=await (0,i.getServerSession)(e,r,d.authOptions);if(!o)return r.status(401).json({error:"Unauthorized"});if(!o.user.isAdmin)return r.status(403).json({error:"Forbidden - Admin access required"});let s=await m();if("PATCH"===e.method){let{resolved:n}=e.body,a=await s.collection("error_logs").updateOne({_id:new l.ObjectId(t)},{$set:{resolved:n,resolvedAt:n?new Date:null,resolvedBy:n?o.user.id:null}});if(0===a.matchedCount)return r.status(404).json({error:"Error not found"});r.status(200).json({success:!0})}else if("DELETE"===e.method){let e=await s.collection("error_logs").deleteOne({_id:new l.ObjectId(t)});if(0===e.deletedCount)return r.status(404).json({error:"Error not found"});r.status(200).json({success:!0})}else r.status(405).json({error:"Method not allowed"})}catch(e){r.status(500).json({error:"Internal server error",details:e.message})}}let b=(0,a.M)(o,"default"),g=(0,a.M)(o,"config"),x=new s.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/admin/errors/[id]",pathname:"/api/admin/errors/[id]",bundlePath:"",filename:""},userland:o})},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(18152));module.exports=o})();