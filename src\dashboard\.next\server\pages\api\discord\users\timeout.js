"use strict";(()=>{var e={};e.id=7672,e.ids=[7672],e.modules={224:e=>{e.exports=import("@discordjs/rest")},2081:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>p,default:()=>d,routeModule:()=>c});var i=r(93433),o=r(20264),a=r(20584),n=r(73911),u=e([n]);n=(u.then?(await u)():u)[0];let d=(0,a.M)(n,"default"),p=(0,a.M)(n,"config"),c=new i.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/discord/users/timeout",pathname:"/api/discord/users/timeout",bundlePath:"",filename:""},userland:n});s()}catch(e){s(e)}})},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},33915:e=>{e.exports=import("discord-api-types/v10")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},73911:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>p});var i=r(15806),o=r(94506),a=r(224),n=r(33915),u=r(20381),d=e([a,n]);async function p(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{if(!await (0,i.getServerSession)(e,t,o.authOptions)){let r=e.headers.authorization;if(!r||!r.startsWith("Bearer "))return t.status(401).json({error:"Unauthorized"})}let{userId:r,duration:s,reason:d}=e.body;if(!r||!s)return t.status(400).json({error:"Missing required fields"});let p=new a.REST({version:"10"}).setToken(u._.DISCORD_BOT_TOKEN),c=u._.DISCORD_GUILD_ID;await p.patch(n.Routes.guildMember(c,r),{body:{communication_disabled_until:new Date(Date.now()+s).toISOString(),reason:d||"No reason provided"}}),t.status(200).json({success:!0})}catch(e){t.status(500).json({error:"Failed to timeout user"})}}[a,n]=d.then?(await d)():d,s()}catch(e){s(e)}})},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(2081));module.exports=s})();