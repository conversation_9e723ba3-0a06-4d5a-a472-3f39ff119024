"use strict";(()=>{var e={};e.id=4604,e.ids=[4604],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},28920:(e,t,r)=>{r.r(t),r.d(t,{config:()=>h,default:()=>c,routeModule:()=>p});var s={};r.r(s),r.d(s,{default:()=>d});var o=r(93433),n=r(20264),a=r(20584),i=r(15806),u=r(94506),l=r(98580);async function d(e,t){try{let r=await (0,i.getServerSession)(e,t,u.authOptions);if(!r?.user)return t.status(401).json({error:"Unauthorized"});let{guildId:s,token:o}=l.dashboardConfig.bot;if(!o||!s)return t.status(500).json({error:"Bot configuration missing"});let{channelId:n}=e.query;if(!n||"string"!=typeof n)return t.status(400).json({error:"Channel ID is required"});if("POST"===e.method)try{let{content:r,embeds:s,components:a,flags:i}=e.body;if(!r&&(!s||0===s.length)&&(!a||0===a.length))return t.status(400).json({error:"Message must contain content, embeds, or components"});s?.length>0&&s.forEach(e=>{e.color&&"string"==typeof e.color&&e.color.startsWith("#")&&(e.color=parseInt(e.color.replace("#",""),16)),Object.keys(e).forEach(t=>{(""===e[t]||null===e[t]||void 0===e[t])&&delete e[t]}),Array.isArray(e.fields)&&0===e.fields.length&&delete e.fields});let u={};r&&(u.content=r),s?.length>0&&(u.embeds=s),a?.length>0&&(u.components=a),"number"==typeof i&&(u.flags=i);let l=await fetch(`https://discord.com/api/v10/channels/${n}/messages`,{method:"POST",headers:{Authorization:`Bot ${o}`,"Content-Type":"application/json"},body:JSON.stringify(u)});if(!l.ok){let e;try{e=await l.json()}catch{e=await l.text()}return t.status(l.status).json(e)}let d=await l.json();return t.status(200).json(d)}catch(e){return t.status(500).json({error:"Failed to send message"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let c=(0,a.M)(s,"default"),h=(0,a.M)(s,"config"),p=new o.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/discord/channels/[channelId]/messages",pathname:"/api/discord/channels/[channelId]/messages",bundlePath:"",filename:""},userland:s})},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(28920));module.exports=s})();