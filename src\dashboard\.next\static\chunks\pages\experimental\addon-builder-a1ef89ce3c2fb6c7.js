(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8963],{21191:(e,n,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/experimental/addon-builder",function(){return s(63616)}])},63616:(e,n,s)=>{"use strict";s.r(n),s.d(n,{__N_SSP:()=>el,default:()=>ea});var r=s(94513),l=s(67116),a=s(39905),i=s(1648),o=s(79028),t=s(95903),c=s(78813),d=s(84748),h=s(3037),m=s(97119),u=s(94285),x=s(81420),j=s(11895),p=s(22416),g=s(62306),v=s(64389),C=s(72468),b=s(81139),f=s(24490),y=s(31840),w=s(29607),E=s(19237),S=s(43700),z=s(64349),k=s(42910),T=s(52080),A=s(36468),F=s(31862),_=s(15975),D=s(1871),O=s(59220),R=s(46949),N=s(95066),$=s(53083),M=s(35624),J=s(35339),q=s(30301),I=s(93493),P=s(91140),Y=s(57688),B=s(21181),L=s(56858),G=s(5130),H=s(52442),Q=s(88142),W=s(7836),K=s(84622);s(75632),s(82273);var U=s(99500),V=s(53424),X=s(58686);let Z={name:"",description:"",type:"slash",permissions:[],cooldown:3e3,enabled:!0,code:"// Your command code here\nawait interaction.reply({\n  content: 'Hello from your new command!',\n  ephemeral: true\n});"},ee={name:"ready",once:!0,code:"// Your event code here\nconsole.log('Bot is ready!');"},en=["ready","messageCreate","interactionCreate","guildMemberAdd","guildMemberRemove","voiceStateUpdate","messageReactionAdd","messageReactionRemove","channelCreate","channelDelete"];function es(){var e;let{data:n}=(0,V.useSession)(),s=(0,X.useRouter)(),m=(0,W.d)(),{isOpen:es,onOpen:er,onClose:el}=(0,K.j)(),{isOpen:ea,onOpen:ei,onClose:eo}=(0,K.j)(),{isOpen:et,onOpen:ec,onClose:ed}=(0,K.j)(),[eh,em]=(0,u.useState)(""),[eu,ex]=(0,u.useState)(!1),[ej,ep]=(0,u.useState)([]),[eg,ev]=(0,u.useState)([]),[eC,eb]=(0,u.useState)(!1),ef=u.useRef(null),[ey,ew]=(0,u.useState)({name:"",version:"1.0.0",description:"",author:(null==n||null==(e=n.user)?void 0:e.name)||"",commands:[],events:[],settings:{embedColor:"#0099FF"}}),eE=(0,Q.dU)("white","gray.800"),eS=(0,Q.dU)("gray.200","gray.600"),ez=(0,u.useCallback)((e,n)=>{ew(s=>({...s,[e]:n}))},[]),ek=(0,u.useCallback)((e,n)=>{ew(s=>({...s,settings:{...s.settings,[e]:n}}))},[]),eT=(0,u.useCallback)(()=>{ew(e=>({...e,commands:[...e.commands,{...Z}]}))},[]),eA=(0,u.useCallback)((e,n,s)=>{ew(r=>({...r,commands:r.commands.map((r,l)=>l===e?{...r,[n]:s}:r)}))},[]),eF=(0,u.useCallback)(e=>{ew(n=>({...n,commands:n.commands.filter((n,s)=>s!==e)}))},[]),e_=(0,u.useCallback)(()=>{ew(e=>({...e,events:[...e.events,{...ee}]}))},[]),eD=(0,u.useCallback)((e,n,s)=>{ew(r=>({...r,events:r.events.map((r,l)=>l===e?{...r,[n]:s}:r)}))},[]),eO=(0,u.useCallback)(e=>{ew(n=>({...n,events:n.events.filter((n,s)=>s!==e)}))},[]),eR=(0,u.useCallback)(()=>{var e;let n=[];return(!ey.name||ey.name.length<2)&&n.push("Addon name must be at least 2 characters long"),/^[a-z0-9-]+$/.test(ey.name)||n.push("Addon name must contain only lowercase letters, numbers, and hyphens"),ey.version&&/^\d+\.\d+\.\d+$/.test(ey.version)||n.push("Version must be in semver format (e.g., 1.0.0)"),(!ey.description||ey.description.length<10)&&n.push("Description must be at least 10 characters long"),(!ey.author||ey.author.length<2)&&n.push("Author name must be at least 2 characters long"),(null==(e=ey.settings)?void 0:e.embedColor)&&/^#[0-9a-fA-F]{6}$/.test(ey.settings.embedColor)||n.push("Embed color must be a valid hex color (e.g., #0099FF)"),ey.commands.forEach((e,s)=>{e.name&&/^[a-z0-9-]+$/.test(e.name)||n.push("Command ".concat(s+1,": Invalid name format")),(!e.description||e.description.length<1)&&n.push("Command ".concat(s+1,": Description is required")),(!e.code||e.code.trim().length<10)&&n.push("Command ".concat(s+1,": Code implementation is required"))}),ey.events.forEach((e,s)=>{e.name||n.push("Event ".concat(s+1,": Name is required")),(!e.code||e.code.trim().length<5)&&n.push("Event ".concat(s+1,": Code implementation is required"))}),n},[ey]),eN=(0,u.useCallback)(()=>{let e=eR();if(e.length>0)return void ep(e);em("// ".concat(ey.name," - Generated Addon Preview\n// This is a preview of your addon's main structure\n\nexport default {\n  info: {\n    name: \"").concat(ey.name,'",\n    version: "').concat(ey.version,'",\n    description: "').concat(ey.description,'",\n    author: "').concat(ey.author,'"\n  },\n\n  commands: ').concat(ey.commands.length," command").concat(1!==ey.commands.length?"s":"",",\n  events: ").concat(ey.events.length," event").concat(1!==ey.events.length?"s":"",',\n  \n  settings: {\n    embedColor: "').concat(ey.settings.embedColor,'"\n  }\n};\n\n// Commands: ').concat(ey.commands.map(e=>e.name).join(", ")||"None","\n// Events: ").concat(ey.events.map(e=>e.name).join(", ")||"None","\n")),er()},[ey,eR,er]),e$=(0,u.useCallback)(async()=>{eb(!0);try{let e=await fetch("/api/experimental/addon-builder/templates"),n=await e.json();if(e.ok)ev(n.templates);else throw Error(n.error||"Failed to load templates")}catch(e){m({title:"Error Loading Templates",description:e instanceof Error?e.message:"Failed to load templates",status:"error",duration:3e3,isClosable:!0})}finally{eb(!1)}},[m]),eM=(0,u.useCallback)(e=>{var s;let r={...e.config};r.author=(null==n||null==(s=n.user)?void 0:s.name)||r.author,ew(r),eo(),m({title:"Template Loaded!",description:"".concat(e.name," template has been applied."),status:"success",duration:3e3,isClosable:!0})},[n,eo,m]),eJ=(0,u.useCallback)(async()=>{let e=eR();if(e.length>0)return void ep(e);ex(!0),ep([]);try{var s;let e=await fetch("/api/experimental/addon-builder/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(ey)}),r=await e.json();if(!e.ok)throw Error(r.error||"Failed to create addon");m({title:"Addon Created Successfully!",description:"".concat(ey.name," has been created with ").concat(r.files.length," files."),status:"success",duration:5e3,isClosable:!0}),ew({name:"",version:"1.0.0",description:"",author:(null==n||null==(s=n.user)?void 0:s.name)||"",commands:[],events:[],settings:{embedColor:"#0099FF"}})}catch(e){m({title:"Error Creating Addon",description:e instanceof Error?e.message:"An unknown error occurred",status:"error",duration:5e3,isClosable:!0})}finally{ex(!1)}},[ey,eR,m,n]),eq=(0,u.useCallback)(()=>{var e;""!==ey.name.trim()||""!==ey.description.trim()||ey.commands.length>0||ey.events.length>0||ey.author!==((null==n||null==(e=n.user)?void 0:e.name)||"")?ec():s.push("/admin/addons")},[ey,n,ec,s]),eI=(0,u.useCallback)(()=>{ed(),s.push("/admin/addons")},[ed,s]);return(0,r.jsxs)(o.a,{maxW:"4xl",mx:"auto",p:6,children:[(0,r.jsxs)(h.T,{spacing:6,align:"stretch",children:[(0,r.jsxs)(o.a,{textAlign:"center",children:[(0,r.jsx)(c.D,{size:"lg",mb:2,children:"\uD83D\uDEE0️ Addon Builder"}),(0,r.jsx)(G.E,{color:"gray.500",mb:4,children:"Create custom addons for your Discord bot with a visual interface"}),(0,r.jsxs)(D.z,{spacing:3,justify:"center",children:[(0,r.jsx)(z.$,{leftIcon:(0,r.jsx)(U.QVr,{}),onClick:eq,colorScheme:"gray",variant:"outline",size:"sm",children:"Go Back"}),(0,r.jsx)(z.$,{leftIcon:(0,r.jsx)(U.FSj,{}),onClick:()=>{e$(),ei()},colorScheme:"purple",variant:"outline",size:"sm",children:"Start from Template"})]})]}),ej.length>0&&(0,r.jsxs)(l.F,{status:"error",children:[(0,r.jsx)(i._,{}),(0,r.jsxs)(o.a,{children:[(0,r.jsx)(E.X,{children:"Validation Errors:"}),(0,r.jsx)(a.T,{children:(0,r.jsx)(h.T,{align:"start",spacing:1,children:ej.map((e,n)=>(0,r.jsxs)(G.E,{fontSize:"sm",children:["• ",e]},n))})})]})]}),(0,r.jsxs)(L.t,{colorScheme:"blue",variant:"enclosed",children:[(0,r.jsxs)(P.w,{children:[(0,r.jsx)(I.o,{children:"Basic Info"}),(0,r.jsxs)(I.o,{children:["Commands (",ey.commands.length,")"]}),(0,r.jsxs)(I.o,{children:["Events (",ey.events.length,")"]}),(0,r.jsx)(I.o,{children:"Settings"})]}),(0,r.jsxs)(B.T,{children:[(0,r.jsx)(Y.K,{children:(0,r.jsxs)(h.T,{spacing:4,align:"stretch",children:[(0,r.jsxs)(F.MJ,{isRequired:!0,children:[(0,r.jsx)(_.l,{children:"Addon Name"}),(0,r.jsx)(O.p,{value:ey.name,onChange:e=>ez("name",e.target.value.toLowerCase().replace(/[^a-z0-9-]/g,"")),placeholder:"my-awesome-addon"}),(0,r.jsx)(G.E,{fontSize:"sm",color:"gray.500",children:"Only lowercase letters, numbers, and hyphens allowed"})]}),(0,r.jsxs)(D.z,{spacing:4,children:[(0,r.jsxs)(F.MJ,{isRequired:!0,children:[(0,r.jsx)(_.l,{children:"Version"}),(0,r.jsx)(O.p,{value:ey.version,onChange:e=>ez("version",e.target.value),placeholder:"1.0.0"})]}),(0,r.jsxs)(F.MJ,{isRequired:!0,children:[(0,r.jsx)(_.l,{children:"Author"}),(0,r.jsx)(O.p,{value:ey.author,onChange:e=>ez("author",e.target.value),placeholder:"Your Name"})]})]}),(0,r.jsxs)(F.MJ,{isRequired:!0,children:[(0,r.jsx)(_.l,{children:"Description"}),(0,r.jsx)(H.T,{value:ey.description,onChange:e=>ez("description",e.target.value),placeholder:"A brief description of what your addon does...",minH:"100px"})]})]})}),(0,r.jsx)(Y.K,{children:(0,r.jsxs)(h.T,{spacing:4,align:"stretch",children:[(0,r.jsxs)(D.z,{justify:"space-between",children:[(0,r.jsx)(c.D,{size:"md",children:"Commands"}),(0,r.jsx)(z.$,{leftIcon:(0,r.jsx)(U.OiG,{}),onClick:eT,colorScheme:"blue",children:"Add Command"})]}),0===ey.commands.length?(0,r.jsxs)(l.F,{status:"info",children:[(0,r.jsx)(i._,{}),(0,r.jsx)(a.T,{children:"No commands yet. Add your first command to get started!"})]}):(0,r.jsx)(x.n,{allowMultiple:!0,children:ey.commands.map((e,n)=>(0,r.jsxs)(g.A,{children:[(0,r.jsxs)(j.J,{children:[(0,r.jsxs)(o.a,{flex:"1",textAlign:"left",children:[(0,r.jsxs)(D.z,{children:[(0,r.jsx)(G.E,{fontWeight:"bold",children:e.name||"Command ".concat(n+1)}),(0,r.jsx)(S.E,{colorScheme:e.enabled?"green":"gray",children:e.enabled?"Enabled":"Disabled"})]}),(0,r.jsx)(G.E,{fontSize:"sm",color:"gray.500",children:e.description||"No description"})]}),(0,r.jsx)(p.Q,{})]}),(0,r.jsx)(v.v,{pb:4,children:(0,r.jsxs)(h.T,{spacing:4,align:"stretch",children:[(0,r.jsxs)(D.z,{children:[(0,r.jsxs)(F.MJ,{isRequired:!0,children:[(0,r.jsx)(_.l,{children:"Command Name"}),(0,r.jsx)(O.p,{value:e.name,onChange:e=>eA(n,"name",e.target.value.toLowerCase().replace(/[^a-z0-9-]/g,"")),placeholder:"ping"})]}),(0,r.jsxs)(F.MJ,{children:[(0,r.jsx)(_.l,{children:"Cooldown (ms)"}),(0,r.jsxs)(M.Q7,{value:e.cooldown,onChange:(e,s)=>eA(n,"cooldown",s),min:1e3,max:3e5,children:[(0,r.jsx)(M.OO,{}),(0,r.jsxs)(M.lw,{children:[(0,r.jsx)(M.Q0,{}),(0,r.jsx)(M.Sh,{})]})]})]})]}),(0,r.jsxs)(F.MJ,{isRequired:!0,children:[(0,r.jsx)(_.l,{children:"Description"}),(0,r.jsx)(O.p,{value:e.description,onChange:e=>eA(n,"description",e.target.value),placeholder:"Shows bot ping"})]}),(0,r.jsxs)(F.MJ,{children:[(0,r.jsx)(_.l,{children:"Command Code"}),(0,r.jsx)(H.T,{value:e.code,onChange:e=>eA(n,"code",e.target.value),placeholder:"// Your command implementation here",minH:"200px",fontFamily:"mono",fontSize:"sm"})]}),(0,r.jsxs)(D.z,{children:[(0,r.jsx)(k.S,{isChecked:e.enabled,onChange:e=>eA(n,"enabled",e.target.checked),children:"Enabled"}),(0,r.jsx)(z.$,{size:"sm",variant:"outline",colorScheme:"red",leftIcon:(0,r.jsx)(U.qbC,{}),onClick:()=>eF(n),children:"Remove"})]})]})})]},n))})]})}),(0,r.jsx)(Y.K,{children:(0,r.jsxs)(h.T,{spacing:4,align:"stretch",children:[(0,r.jsxs)(D.z,{justify:"space-between",children:[(0,r.jsx)(c.D,{size:"md",children:"Events"}),(0,r.jsx)(z.$,{leftIcon:(0,r.jsx)(U.OiG,{}),onClick:e_,colorScheme:"blue",children:"Add Event"})]}),0===ey.events.length?(0,r.jsxs)(l.F,{status:"info",children:[(0,r.jsx)(i._,{}),(0,r.jsx)(a.T,{children:"No events yet. Add event handlers to respond to Discord events!"})]}):(0,r.jsx)(x.n,{allowMultiple:!0,children:ey.events.map((e,n)=>(0,r.jsxs)(g.A,{children:[(0,r.jsxs)(j.J,{children:[(0,r.jsx)(o.a,{flex:"1",textAlign:"left",children:(0,r.jsxs)(D.z,{children:[(0,r.jsx)(G.E,{fontWeight:"bold",children:e.name||"Event ".concat(n+1)}),(0,r.jsx)(S.E,{colorScheme:e.once?"blue":"green",children:e.once?"Once":"Recurring"})]})}),(0,r.jsx)(p.Q,{})]}),(0,r.jsx)(v.v,{pb:4,children:(0,r.jsxs)(h.T,{spacing:4,align:"stretch",children:[(0,r.jsxs)(D.z,{children:[(0,r.jsxs)(F.MJ,{isRequired:!0,children:[(0,r.jsx)(_.l,{children:"Event Name"}),(0,r.jsxs)(J.l,{value:e.name,onChange:e=>eD(n,"name",e.target.value),children:[(0,r.jsx)("option",{value:"",children:"Select an event"}),en.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)(F.MJ,{children:[(0,r.jsx)(_.l,{children:"Trigger Type"}),(0,r.jsxs)(J.l,{value:e.once?"once":"recurring",onChange:e=>eD(n,"once","once"===e.target.value),children:[(0,r.jsx)("option",{value:"once",children:"Once"}),(0,r.jsx)("option",{value:"recurring",children:"Recurring"})]})]})]}),(0,r.jsxs)(F.MJ,{children:[(0,r.jsx)(_.l,{children:"Event Code"}),(0,r.jsx)(H.T,{value:e.code,onChange:e=>eD(n,"code",e.target.value),placeholder:"// Your event handler code here",minH:"200px",fontFamily:"mono",fontSize:"sm"})]}),(0,r.jsx)(z.$,{size:"sm",variant:"outline",colorScheme:"red",leftIcon:(0,r.jsx)(U.qbC,{}),onClick:()=>eO(n),alignSelf:"flex-start",children:"Remove Event"})]})})]},n))})]})}),(0,r.jsx)(Y.K,{children:(0,r.jsxs)(h.T,{spacing:4,align:"stretch",children:[(0,r.jsx)(c.D,{size:"md",children:"Settings"}),(0,r.jsxs)(F.MJ,{children:[(0,r.jsx)(_.l,{children:"Embed Color"}),(0,r.jsxs)(D.z,{children:[(0,r.jsx)(O.p,{type:"color",value:ey.settings.embedColor,onChange:e=>ek("embedColor",e.target.value),w:"80px"}),(0,r.jsx)(O.p,{value:ey.settings.embedColor,onChange:e=>ek("embedColor",e.target.value),placeholder:"#0099FF"})]})]}),(0,r.jsxs)(l.F,{status:"info",children:[(0,r.jsx)(i._,{}),(0,r.jsx)(a.T,{children:"More settings will be added in future updates!"})]})]})})]})]}),(0,r.jsx)(A.c,{}),(0,r.jsxs)(D.z,{spacing:4,justify:"center",children:[(0,r.jsx)(z.$,{leftIcon:(0,r.jsx)(U.Ny1,{}),onClick:eN,variant:"outline",colorScheme:"blue",children:"Preview"}),(0,r.jsx)(z.$,{leftIcon:(0,r.jsx)(U.uoG,{}),onClick:eJ,colorScheme:"blue",size:"lg",isLoading:eu,loadingText:"Creating...",children:"Create Addon"})]})]}),(0,r.jsxs)(R.aF,{isOpen:es,onClose:el,size:"xl",children:[(0,r.jsx)(w.m,{}),(0,r.jsxs)($.$,{children:[(0,r.jsx)(y.r,{children:"Addon Preview"}),(0,r.jsx)(N.s,{}),(0,r.jsx)(b.c,{children:(0,r.jsx)(T.C,{as:"pre",p:4,fontSize:"sm",overflow:"auto",maxH:"400px",children:eh})}),(0,r.jsx)(f.j,{children:(0,r.jsx)(z.$,{onClick:el,children:"Close"})})]})]}),(0,r.jsxs)(R.aF,{isOpen:ea,onClose:eo,size:"4xl",children:[(0,r.jsx)(w.m,{}),(0,r.jsxs)($.$,{children:[(0,r.jsx)(y.r,{children:"Choose a Template"}),(0,r.jsx)(N.s,{}),(0,r.jsx)(b.c,{children:eC?(0,r.jsx)(t.o,{py:8,children:(0,r.jsxs)(h.T,{spacing:4,children:[(0,r.jsx)(d.y,{size:"lg"}),(0,r.jsx)(G.E,{children:"Loading templates..."})]})}):(0,r.jsxs)(h.T,{spacing:4,align:"stretch",children:[(0,r.jsx)(G.E,{color:"gray.500",children:"Start with a pre-built template to save time and learn best practices."}),(0,r.jsx)(q.r,{columns:{base:1,md:2},spacing:4,children:eg.map(e=>(0,r.jsx)(o.a,{bg:eE,border:"1px",borderColor:eS,borderRadius:"md",p:4,cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",transform:"translateY(-2px)",boxShadow:"md"},onClick:()=>eM(e),children:(0,r.jsxs)(h.T,{align:"start",spacing:3,children:[(0,r.jsxs)(D.z,{justify:"space-between",w:"full",children:[(0,r.jsx)(c.D,{size:"sm",children:e.name}),(0,r.jsx)(S.E,{colorScheme:"blue",size:"sm",children:e.category})]}),(0,r.jsx)(G.E,{fontSize:"sm",color:"gray.500",children:e.description}),(0,r.jsxs)(D.z,{spacing:2,children:[(0,r.jsxs)(S.E,{variant:"outline",size:"xs",children:[e.config.commands.length," commands"]}),(0,r.jsxs)(S.E,{variant:"outline",size:"xs",children:[e.config.events.length," events"]})]})]})},e.id))})]})}),(0,r.jsx)(f.j,{children:(0,r.jsx)(z.$,{onClick:eo,children:"Cancel"})})]})]}),(0,r.jsx)(C.Lt,{isOpen:et,leastDestructiveRef:ef,onClose:ed,isCentered:!0,children:(0,r.jsx)(w.m,{children:(0,r.jsxs)(C.EO,{children:[(0,r.jsx)(y.r,{fontSize:"lg",fontWeight:"bold",children:"Leave Addon Builder?"}),(0,r.jsx)(b.c,{children:"You have unsaved changes to your addon. Are you sure you want to go back? All your work will be lost."}),(0,r.jsxs)(f.j,{children:[(0,r.jsx)(z.$,{ref:ef,onClick:ed,children:"Cancel"}),(0,r.jsx)(z.$,{colorScheme:"red",onClick:eI,ml:3,children:"Yes, Go Back"})]})]})})})]})}var er=s(38262),el=!0;function ea(){let{hasAccess:e,isLoading:n,reason:s}=(0,er.default)();return n?(0,r.jsx)(m.A,{children:(0,r.jsx)(t.o,{p:8,children:(0,r.jsxs)(h.T,{spacing:4,children:[(0,r.jsx)(d.y,{size:"xl"}),(0,r.jsx)(c.D,{size:"md",children:"Checking access..."})]})})}):e?(0,r.jsx)(m.A,{children:(0,r.jsxs)(h.T,{spacing:8,p:8,align:"stretch",maxW:"100%",overflow:"hidden",children:[(0,r.jsxs)(o.a,{textAlign:"center",children:[(0,r.jsx)(c.D,{size:"xl",mb:2,children:"⚗️ Addon Builder"}),(0,r.jsxs)(l.F,{status:"info",mb:4,children:[(0,r.jsx)(i._,{}),(0,r.jsxs)(a.T,{children:[(0,r.jsx)("strong",{children:"Experimental Feature:"})," This is a beta feature for creating custom Discord bot addons. Use with caution and report any issues you encounter."]})]})]}),(0,r.jsx)(es,{})]})}):(0,r.jsx)(m.A,{children:(0,r.jsx)(o.a,{p:8,children:(0,r.jsxs)(l.F,{status:"warning",children:[(0,r.jsx)(i._,{}),(0,r.jsxs)(a.T,{children:["You need experimental features access to use the Addon Builder.","unauthenticated"===s&&" Please sign in first.","no_access"===s&&" Please apply for experimental features access from the overview page."]})]})})})}}},e=>{var n=n=>e(e.s=n);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,636,7398,1203,8792],()=>n(21191)),_N_E=e.O()}]);