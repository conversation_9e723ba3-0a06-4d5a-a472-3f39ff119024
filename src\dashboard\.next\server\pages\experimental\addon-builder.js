"use strict";(()=>{var e={};e.id=8963,e.ids=[636,3220,8963],e.modules={2380:(e,n,t)=>{t.a(e,async(e,r)=>{try{t.r(n),t.d(n,{default:()=>u,getServerSideProps:()=>m});var s=t(8732),o=t(15806),a=t(92546),l=t(88105),i=t(81011),c=t(41908),d=t(31749),h=e([l,i,c]);function u(){let{hasAccess:e,isLoading:n,reason:t}=(0,d.default)();return n?(0,s.jsx)(i.A,{children:(0,s.jsx)(l.ov,{p:8,children:(0,s.jsxs)(l.Tk,{spacing:4,children:[(0,s.jsx)(l.y$,{size:"xl"}),(0,s.jsx)(l.DZ,{size:"md",children:"Checking access..."})]})})}):e?(0,s.jsx)(i.A,{children:(0,s.jsxs)(l.Tk,{spacing:8,p:8,align:"stretch",maxW:"100%",overflow:"hidden",children:[(0,s.jsxs)(l.az,{textAlign:"center",children:[(0,s.jsx)(l.DZ,{size:"xl",mb:2,children:"⚗️ Addon Builder"}),(0,s.jsxs)(l.Fc,{status:"info",mb:4,children:[(0,s.jsx)(l._0,{}),(0,s.jsxs)(l.TN,{children:[(0,s.jsx)("strong",{children:"Experimental Feature:"})," This is a beta feature for creating custom Discord bot addons. Use with caution and report any issues you encounter."]})]})]}),(0,s.jsx)(c.A,{})]})}):(0,s.jsx)(i.A,{children:(0,s.jsx)(l.az,{p:8,children:(0,s.jsxs)(l.Fc,{status:"warning",children:[(0,s.jsx)(l._0,{}),(0,s.jsxs)(l.TN,{children:["You need experimental features access to use the Addon Builder.","unauthenticated"===t&&" Please sign in first.","no_access"===t&&" Please apply for experimental features access from the overview page."]})]})})})}[l,i,c]=h.then?(await h)():h;let m=async e=>await (0,o.getServerSession)(e.req,e.res,a.N)?{props:{}}:{redirect:{destination:"/api/auth/signin?callbackUrl=%2Fexperimental%2Faddon-builder",permanent:!1}};r()}catch(e){r(e)}})},3105:(e,n,t)=>{t.a(e,async(e,r)=>{try{t.d(n,{Accordion:()=>s.n,AccordionButton:()=>o.J,AccordionIcon:()=>a.Q,AccordionItem:()=>l.A,AccordionPanel:()=>i.v,Alert:()=>c.F,AlertDescription:()=>d.T,AlertDialog:()=>h.Lt,AlertDialogBody:()=>u.c,AlertDialogContent:()=>h.EO,AlertDialogFooter:()=>m.j,AlertDialogHeader:()=>x.r,AlertDialogOverlay:()=>p.m,AlertIcon:()=>j._,AlertTitle:()=>g.X,Badge:()=>b.E,Box:()=>v.a,Button:()=>C.$,Center:()=>S.o,Checkbox:()=>f.S,Code:()=>y.C,Divider:()=>A.c,FormControl:()=>k.MJ,FormLabel:()=>F.l,HStack:()=>w.z,Heading:()=>T.D,Input:()=>B.p,Modal:()=>D.aF,ModalBody:()=>u.c,ModalCloseButton:()=>M.s,ModalContent:()=>I.$,ModalFooter:()=>m.j,ModalHeader:()=>x.r,ModalOverlay:()=>p.m,NumberDecrementStepper:()=>z.Sh,NumberIncrementStepper:()=>z.Q0,NumberInput:()=>z.Q7,NumberInputField:()=>z.OO,NumberInputStepper:()=>z.lw,Select:()=>P.l,SimpleGrid:()=>q.r,Spinner:()=>E.y,Tab:()=>H.o,TabList:()=>$.w,TabPanel:()=>N.K,TabPanels:()=>O.T,Tabs:()=>L.t,Text:()=>V.E,Textarea:()=>R.T,VStack:()=>_.T,useColorModeValue:()=>G.dU,useDisclosure:()=>W.j,useToast:()=>Y.d});var s=t(83080),o=t(96997),a=t(87164),l=t(1226),i=t(35583),c=t(5128),d=t(76331),h=t(70288),u=t(42929),m=t(87346),x=t(95148),p=t(12725),j=t(31772),g=t(40575),b=t(25392),v=t(45200),C=t(77502),S=t(5448),f=t(76776),y=t(29180),A=t(464),k=t(23678),F=t(63957),w=t(55197),T=t(30519),B=t(15376),D=t(75460),M=t(7394),I=t(89164),z=t(71342),P=t(29742),q=t(67981),E=t(90088),H=t(8399),$=t(81248),N=t(46596),O=t(92279),L=t(64450),V=t(87378),R=t(37506),_=t(17335),G=t(72982),Y=t(5978),W=t(66646);t(9436),t(25035);var Q=e([s,o,a,l,i,c,d,h,u,m,x,p,j,g,b,v,C,S,f,y,A,k,F,w,T,B,D,M,I,z,P,q,E,H,$,N,O,L,V,R,_,Y]);[s,o,a,l,i,c,d,h,u,m,x,p,j,g,b,v,C,S,f,y,A,k,F,w,T,B,D,M,I,z,P,q,E,H,$,N,O,L,V,R,_,Y]=Q.then?(await Q)():Q,r()}catch(e){r(e)}})},4722:e=>{e.exports=require("next-auth/react")},8732:e=>{e.exports=require("react/jsx-runtime")},10918:(e,n,t)=>{t.a(e,async(e,r)=>{try{t.r(n),t.d(n,{config:()=>p,default:()=>h,getServerSideProps:()=>x,getStaticPaths:()=>m,getStaticProps:()=>u,reportWebVitals:()=>j,routeModule:()=>f,unstable_getServerProps:()=>C,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>g});var s=t(1292),o=t(58834),a=t(40786),l=t(83567),i=t(8077),c=t(2380),d=e([i,c]);[i,c]=d.then?(await d)():d;let h=(0,a.M)(c,"default"),u=(0,a.M)(c,"getStaticProps"),m=(0,a.M)(c,"getStaticPaths"),x=(0,a.M)(c,"getServerSideProps"),p=(0,a.M)(c,"config"),j=(0,a.M)(c,"reportWebVitals"),g=(0,a.M)(c,"unstable_getStaticProps"),b=(0,a.M)(c,"unstable_getStaticPaths"),v=(0,a.M)(c,"unstable_getStaticParams"),C=(0,a.M)(c,"unstable_getServerProps"),S=(0,a.M)(c,"unstable_getServerSideProps"),f=new s.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/experimental/addon-builder",pathname:"/experimental/addon-builder",bundlePath:"",filename:""},components:{App:i.default,Document:l.default},userland:c});r()}catch(e){r(e)}})},14078:e=>{e.exports=import("swr")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},22326:e=>{e.exports=require("react-dom")},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},30067:e=>{e.exports=import("@emotion/styled")},33873:e=>{e.exports=require("path")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},41908:(e,n,t)=>{t.a(e,async(e,r)=>{try{t.d(n,{A:()=>u});var s=t(8732),o=t(82015),a=t.n(o),l=t(3105),i=t(67066),c=t(4722),d=t(88358),h=e([l]);l=(h.then?(await h)():h)[0];let m={name:"",description:"",type:"slash",permissions:[],cooldown:3e3,enabled:!0,code:`// Your command code here
await interaction.reply({
  content: 'Hello from your new command!',
  ephemeral: true
});`},x={name:"ready",once:!0,code:`// Your event code here
console.log('Bot is ready!');`},p=["ready","messageCreate","interactionCreate","guildMemberAdd","guildMemberRemove","voiceStateUpdate","messageReactionAdd","messageReactionRemove","channelCreate","channelDelete"];function u(){let{data:e}=(0,c.useSession)(),n=(0,d.useRouter)(),t=(0,l.useToast)(),{isOpen:r,onOpen:h,onClose:u}=(0,l.useDisclosure)(),{isOpen:j,onOpen:g,onClose:b}=(0,l.useDisclosure)(),{isOpen:v,onOpen:C,onClose:S}=(0,l.useDisclosure)(),[f,y]=(0,o.useState)(""),[A,k]=(0,o.useState)(!1),[F,w]=(0,o.useState)([]),[T,B]=(0,o.useState)([]),[D,M]=(0,o.useState)(!1),I=a().useRef(null),[z,P]=(0,o.useState)({name:"",version:"1.0.0",description:"",author:e?.user?.name||"",commands:[],events:[],settings:{embedColor:"#0099FF"}}),q=(0,l.useColorModeValue)("white","gray.800"),E=(0,l.useColorModeValue)("gray.200","gray.600"),H=(0,o.useCallback)((e,n)=>{P(t=>({...t,[e]:n}))},[]),$=(0,o.useCallback)((e,n)=>{P(t=>({...t,settings:{...t.settings,[e]:n}}))},[]),N=(0,o.useCallback)(()=>{P(e=>({...e,commands:[...e.commands,{...m}]}))},[]),O=(0,o.useCallback)((e,n,t)=>{P(r=>({...r,commands:r.commands.map((r,s)=>s===e?{...r,[n]:t}:r)}))},[]),L=(0,o.useCallback)(e=>{P(n=>({...n,commands:n.commands.filter((n,t)=>t!==e)}))},[]),V=(0,o.useCallback)(()=>{P(e=>({...e,events:[...e.events,{...x}]}))},[]),R=(0,o.useCallback)((e,n,t)=>{P(r=>({...r,events:r.events.map((r,s)=>s===e?{...r,[n]:t}:r)}))},[]),_=(0,o.useCallback)(e=>{P(n=>({...n,events:n.events.filter((n,t)=>t!==e)}))},[]),G=(0,o.useCallback)(()=>{let e=[];return(!z.name||z.name.length<2)&&e.push("Addon name must be at least 2 characters long"),/^[a-z0-9-]+$/.test(z.name)||e.push("Addon name must contain only lowercase letters, numbers, and hyphens"),z.version&&/^\d+\.\d+\.\d+$/.test(z.version)||e.push("Version must be in semver format (e.g., 1.0.0)"),(!z.description||z.description.length<10)&&e.push("Description must be at least 10 characters long"),(!z.author||z.author.length<2)&&e.push("Author name must be at least 2 characters long"),z.settings?.embedColor&&/^#[0-9a-fA-F]{6}$/.test(z.settings.embedColor)||e.push("Embed color must be a valid hex color (e.g., #0099FF)"),z.commands.forEach((n,t)=>{n.name&&/^[a-z0-9-]+$/.test(n.name)||e.push(`Command ${t+1}: Invalid name format`),(!n.description||n.description.length<1)&&e.push(`Command ${t+1}: Description is required`),(!n.code||n.code.trim().length<10)&&e.push(`Command ${t+1}: Code implementation is required`)}),z.events.forEach((n,t)=>{n.name||e.push(`Event ${t+1}: Name is required`),(!n.code||n.code.trim().length<5)&&e.push(`Event ${t+1}: Code implementation is required`)}),e},[z]),Y=(0,o.useCallback)(()=>{let e=G();if(e.length>0)return void w(e);let n=`// ${z.name} - Generated Addon Preview
// This is a preview of your addon's main structure

export default {
  info: {
    name: "${z.name}",
    version: "${z.version}",
    description: "${z.description}",
    author: "${z.author}"
  },

  commands: ${z.commands.length} command${1!==z.commands.length?"s":""},
  events: ${z.events.length} event${1!==z.events.length?"s":""},
  
  settings: {
    embedColor: "${z.settings.embedColor}"
  }
};

// Commands: ${z.commands.map(e=>e.name).join(", ")||"None"}
// Events: ${z.events.map(e=>e.name).join(", ")||"None"}
`;y(n),h()},[z,G,h]),W=(0,o.useCallback)(async()=>{M(!0);try{let e=await fetch("/api/experimental/addon-builder/templates"),n=await e.json();if(e.ok)B(n.templates);else throw Error(n.error||"Failed to load templates")}catch(e){t({title:"Error Loading Templates",description:e instanceof Error?e.message:"Failed to load templates",status:"error",duration:3e3,isClosable:!0})}finally{M(!1)}},[t]),Q=(0,o.useCallback)(n=>{let r={...n.config};r.author=e?.user?.name||r.author,P(r),b(),t({title:"Template Loaded!",description:`${n.name} template has been applied.`,status:"success",duration:3e3,isClosable:!0})},[e,b,t]),U=(0,o.useCallback)(async()=>{let n=G();if(n.length>0)return void w(n);k(!0),w([]);try{let n=await fetch("/api/experimental/addon-builder/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(z)}),r=await n.json();if(!n.ok)throw Error(r.error||"Failed to create addon");t({title:"Addon Created Successfully!",description:`${z.name} has been created with ${r.files.length} files.`,status:"success",duration:5e3,isClosable:!0}),P({name:"",version:"1.0.0",description:"",author:e?.user?.name||"",commands:[],events:[],settings:{embedColor:"#0099FF"}})}catch(e){t({title:"Error Creating Addon",description:e instanceof Error?e.message:"An unknown error occurred",status:"error",duration:5e3,isClosable:!0})}finally{k(!1)}},[z,G,t,e]),J=(0,o.useCallback)(()=>{""!==z.name.trim()||""!==z.description.trim()||z.commands.length>0||z.events.length>0||z.author!==(e?.user?.name||"")?C():n.push("/admin/addons")},[z,e,C,n]),Z=(0,o.useCallback)(()=>{S(),n.push("/admin/addons")},[S,n]);return(0,s.jsxs)(l.Box,{maxW:"4xl",mx:"auto",p:6,children:[(0,s.jsxs)(l.VStack,{spacing:6,align:"stretch",children:[(0,s.jsxs)(l.Box,{textAlign:"center",children:[(0,s.jsx)(l.Heading,{size:"lg",mb:2,children:"\uD83D\uDEE0️ Addon Builder"}),(0,s.jsx)(l.Text,{color:"gray.500",mb:4,children:"Create custom addons for your Discord bot with a visual interface"}),(0,s.jsxs)(l.HStack,{spacing:3,justify:"center",children:[(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(i.QVr,{}),onClick:J,colorScheme:"gray",variant:"outline",size:"sm",children:"Go Back"}),(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(i.FSj,{}),onClick:()=>{W(),g()},colorScheme:"purple",variant:"outline",size:"sm",children:"Start from Template"})]})]}),F.length>0&&(0,s.jsxs)(l.Alert,{status:"error",children:[(0,s.jsx)(l.AlertIcon,{}),(0,s.jsxs)(l.Box,{children:[(0,s.jsx)(l.AlertTitle,{children:"Validation Errors:"}),(0,s.jsx)(l.AlertDescription,{children:(0,s.jsx)(l.VStack,{align:"start",spacing:1,children:F.map((e,n)=>(0,s.jsxs)(l.Text,{fontSize:"sm",children:["• ",e]},n))})})]})]}),(0,s.jsxs)(l.Tabs,{colorScheme:"blue",variant:"enclosed",children:[(0,s.jsxs)(l.TabList,{children:[(0,s.jsx)(l.Tab,{children:"Basic Info"}),(0,s.jsxs)(l.Tab,{children:["Commands (",z.commands.length,")"]}),(0,s.jsxs)(l.Tab,{children:["Events (",z.events.length,")"]}),(0,s.jsx)(l.Tab,{children:"Settings"})]}),(0,s.jsxs)(l.TabPanels,{children:[(0,s.jsx)(l.TabPanel,{children:(0,s.jsxs)(l.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(l.FormControl,{isRequired:!0,children:[(0,s.jsx)(l.FormLabel,{children:"Addon Name"}),(0,s.jsx)(l.Input,{value:z.name,onChange:e=>H("name",e.target.value.toLowerCase().replace(/[^a-z0-9-]/g,"")),placeholder:"my-awesome-addon"}),(0,s.jsx)(l.Text,{fontSize:"sm",color:"gray.500",children:"Only lowercase letters, numbers, and hyphens allowed"})]}),(0,s.jsxs)(l.HStack,{spacing:4,children:[(0,s.jsxs)(l.FormControl,{isRequired:!0,children:[(0,s.jsx)(l.FormLabel,{children:"Version"}),(0,s.jsx)(l.Input,{value:z.version,onChange:e=>H("version",e.target.value),placeholder:"1.0.0"})]}),(0,s.jsxs)(l.FormControl,{isRequired:!0,children:[(0,s.jsx)(l.FormLabel,{children:"Author"}),(0,s.jsx)(l.Input,{value:z.author,onChange:e=>H("author",e.target.value),placeholder:"Your Name"})]})]}),(0,s.jsxs)(l.FormControl,{isRequired:!0,children:[(0,s.jsx)(l.FormLabel,{children:"Description"}),(0,s.jsx)(l.Textarea,{value:z.description,onChange:e=>H("description",e.target.value),placeholder:"A brief description of what your addon does...",minH:"100px"})]})]})}),(0,s.jsx)(l.TabPanel,{children:(0,s.jsxs)(l.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(l.HStack,{justify:"space-between",children:[(0,s.jsx)(l.Heading,{size:"md",children:"Commands"}),(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(i.OiG,{}),onClick:N,colorScheme:"blue",children:"Add Command"})]}),0===z.commands.length?(0,s.jsxs)(l.Alert,{status:"info",children:[(0,s.jsx)(l.AlertIcon,{}),(0,s.jsx)(l.AlertDescription,{children:"No commands yet. Add your first command to get started!"})]}):(0,s.jsx)(l.Accordion,{allowMultiple:!0,children:z.commands.map((e,n)=>(0,s.jsxs)(l.AccordionItem,{children:[(0,s.jsxs)(l.AccordionButton,{children:[(0,s.jsxs)(l.Box,{flex:"1",textAlign:"left",children:[(0,s.jsxs)(l.HStack,{children:[(0,s.jsx)(l.Text,{fontWeight:"bold",children:e.name||`Command ${n+1}`}),(0,s.jsx)(l.Badge,{colorScheme:e.enabled?"green":"gray",children:e.enabled?"Enabled":"Disabled"})]}),(0,s.jsx)(l.Text,{fontSize:"sm",color:"gray.500",children:e.description||"No description"})]}),(0,s.jsx)(l.AccordionIcon,{})]}),(0,s.jsx)(l.AccordionPanel,{pb:4,children:(0,s.jsxs)(l.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(l.HStack,{children:[(0,s.jsxs)(l.FormControl,{isRequired:!0,children:[(0,s.jsx)(l.FormLabel,{children:"Command Name"}),(0,s.jsx)(l.Input,{value:e.name,onChange:e=>O(n,"name",e.target.value.toLowerCase().replace(/[^a-z0-9-]/g,"")),placeholder:"ping"})]}),(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"Cooldown (ms)"}),(0,s.jsxs)(l.NumberInput,{value:e.cooldown,onChange:(e,t)=>O(n,"cooldown",t),min:1e3,max:3e5,children:[(0,s.jsx)(l.NumberInputField,{}),(0,s.jsxs)(l.NumberInputStepper,{children:[(0,s.jsx)(l.NumberIncrementStepper,{}),(0,s.jsx)(l.NumberDecrementStepper,{})]})]})]})]}),(0,s.jsxs)(l.FormControl,{isRequired:!0,children:[(0,s.jsx)(l.FormLabel,{children:"Description"}),(0,s.jsx)(l.Input,{value:e.description,onChange:e=>O(n,"description",e.target.value),placeholder:"Shows bot ping"})]}),(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"Command Code"}),(0,s.jsx)(l.Textarea,{value:e.code,onChange:e=>O(n,"code",e.target.value),placeholder:"// Your command implementation here",minH:"200px",fontFamily:"mono",fontSize:"sm"})]}),(0,s.jsxs)(l.HStack,{children:[(0,s.jsx)(l.Checkbox,{isChecked:e.enabled,onChange:e=>O(n,"enabled",e.target.checked),children:"Enabled"}),(0,s.jsx)(l.Button,{size:"sm",variant:"outline",colorScheme:"red",leftIcon:(0,s.jsx)(i.qbC,{}),onClick:()=>L(n),children:"Remove"})]})]})})]},n))})]})}),(0,s.jsx)(l.TabPanel,{children:(0,s.jsxs)(l.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(l.HStack,{justify:"space-between",children:[(0,s.jsx)(l.Heading,{size:"md",children:"Events"}),(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(i.OiG,{}),onClick:V,colorScheme:"blue",children:"Add Event"})]}),0===z.events.length?(0,s.jsxs)(l.Alert,{status:"info",children:[(0,s.jsx)(l.AlertIcon,{}),(0,s.jsx)(l.AlertDescription,{children:"No events yet. Add event handlers to respond to Discord events!"})]}):(0,s.jsx)(l.Accordion,{allowMultiple:!0,children:z.events.map((e,n)=>(0,s.jsxs)(l.AccordionItem,{children:[(0,s.jsxs)(l.AccordionButton,{children:[(0,s.jsx)(l.Box,{flex:"1",textAlign:"left",children:(0,s.jsxs)(l.HStack,{children:[(0,s.jsx)(l.Text,{fontWeight:"bold",children:e.name||`Event ${n+1}`}),(0,s.jsx)(l.Badge,{colorScheme:e.once?"blue":"green",children:e.once?"Once":"Recurring"})]})}),(0,s.jsx)(l.AccordionIcon,{})]}),(0,s.jsx)(l.AccordionPanel,{pb:4,children:(0,s.jsxs)(l.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(l.HStack,{children:[(0,s.jsxs)(l.FormControl,{isRequired:!0,children:[(0,s.jsx)(l.FormLabel,{children:"Event Name"}),(0,s.jsxs)(l.Select,{value:e.name,onChange:e=>R(n,"name",e.target.value),children:[(0,s.jsx)("option",{value:"",children:"Select an event"}),p.map(e=>(0,s.jsx)("option",{value:e,children:e},e))]})]}),(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"Trigger Type"}),(0,s.jsxs)(l.Select,{value:e.once?"once":"recurring",onChange:e=>R(n,"once","once"===e.target.value),children:[(0,s.jsx)("option",{value:"once",children:"Once"}),(0,s.jsx)("option",{value:"recurring",children:"Recurring"})]})]})]}),(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"Event Code"}),(0,s.jsx)(l.Textarea,{value:e.code,onChange:e=>R(n,"code",e.target.value),placeholder:"// Your event handler code here",minH:"200px",fontFamily:"mono",fontSize:"sm"})]}),(0,s.jsx)(l.Button,{size:"sm",variant:"outline",colorScheme:"red",leftIcon:(0,s.jsx)(i.qbC,{}),onClick:()=>_(n),alignSelf:"flex-start",children:"Remove Event"})]})})]},n))})]})}),(0,s.jsx)(l.TabPanel,{children:(0,s.jsxs)(l.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(l.Heading,{size:"md",children:"Settings"}),(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"Embed Color"}),(0,s.jsxs)(l.HStack,{children:[(0,s.jsx)(l.Input,{type:"color",value:z.settings.embedColor,onChange:e=>$("embedColor",e.target.value),w:"80px"}),(0,s.jsx)(l.Input,{value:z.settings.embedColor,onChange:e=>$("embedColor",e.target.value),placeholder:"#0099FF"})]})]}),(0,s.jsxs)(l.Alert,{status:"info",children:[(0,s.jsx)(l.AlertIcon,{}),(0,s.jsx)(l.AlertDescription,{children:"More settings will be added in future updates!"})]})]})})]})]}),(0,s.jsx)(l.Divider,{}),(0,s.jsxs)(l.HStack,{spacing:4,justify:"center",children:[(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(i.Ny1,{}),onClick:Y,variant:"outline",colorScheme:"blue",children:"Preview"}),(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(i.uoG,{}),onClick:U,colorScheme:"blue",size:"lg",isLoading:A,loadingText:"Creating...",children:"Create Addon"})]})]}),(0,s.jsxs)(l.Modal,{isOpen:r,onClose:u,size:"xl",children:[(0,s.jsx)(l.ModalOverlay,{}),(0,s.jsxs)(l.ModalContent,{children:[(0,s.jsx)(l.ModalHeader,{children:"Addon Preview"}),(0,s.jsx)(l.ModalCloseButton,{}),(0,s.jsx)(l.ModalBody,{children:(0,s.jsx)(l.Code,{as:"pre",p:4,fontSize:"sm",overflow:"auto",maxH:"400px",children:f})}),(0,s.jsx)(l.ModalFooter,{children:(0,s.jsx)(l.Button,{onClick:u,children:"Close"})})]})]}),(0,s.jsxs)(l.Modal,{isOpen:j,onClose:b,size:"4xl",children:[(0,s.jsx)(l.ModalOverlay,{}),(0,s.jsxs)(l.ModalContent,{children:[(0,s.jsx)(l.ModalHeader,{children:"Choose a Template"}),(0,s.jsx)(l.ModalCloseButton,{}),(0,s.jsx)(l.ModalBody,{children:D?(0,s.jsx)(l.Center,{py:8,children:(0,s.jsxs)(l.VStack,{spacing:4,children:[(0,s.jsx)(l.Spinner,{size:"lg"}),(0,s.jsx)(l.Text,{children:"Loading templates..."})]})}):(0,s.jsxs)(l.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(l.Text,{color:"gray.500",children:"Start with a pre-built template to save time and learn best practices."}),(0,s.jsx)(l.SimpleGrid,{columns:{base:1,md:2},spacing:4,children:T.map(e=>(0,s.jsx)(l.Box,{bg:q,border:"1px",borderColor:E,borderRadius:"md",p:4,cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",transform:"translateY(-2px)",boxShadow:"md"},onClick:()=>Q(e),children:(0,s.jsxs)(l.VStack,{align:"start",spacing:3,children:[(0,s.jsxs)(l.HStack,{justify:"space-between",w:"full",children:[(0,s.jsx)(l.Heading,{size:"sm",children:e.name}),(0,s.jsx)(l.Badge,{colorScheme:"blue",size:"sm",children:e.category})]}),(0,s.jsx)(l.Text,{fontSize:"sm",color:"gray.500",children:e.description}),(0,s.jsxs)(l.HStack,{spacing:2,children:[(0,s.jsxs)(l.Badge,{variant:"outline",size:"xs",children:[e.config.commands.length," commands"]}),(0,s.jsxs)(l.Badge,{variant:"outline",size:"xs",children:[e.config.events.length," events"]})]})]})},e.id))})]})}),(0,s.jsx)(l.ModalFooter,{children:(0,s.jsx)(l.Button,{onClick:b,children:"Cancel"})})]})]}),(0,s.jsx)(l.AlertDialog,{isOpen:v,leastDestructiveRef:I,onClose:S,isCentered:!0,children:(0,s.jsx)(l.AlertDialogOverlay,{children:(0,s.jsxs)(l.AlertDialogContent,{children:[(0,s.jsx)(l.AlertDialogHeader,{fontSize:"lg",fontWeight:"bold",children:"Leave Addon Builder?"}),(0,s.jsx)(l.AlertDialogBody,{children:"You have unsaved changes to your addon. Are you sure you want to go back? All your work will be lost."}),(0,s.jsxs)(l.AlertDialogFooter,{children:[(0,s.jsx)(l.Button,{ref:I,onClick:S,children:"Cancel"}),(0,s.jsx)(l.Button,{colorScheme:"red",onClick:Z,ml:3,children:"Yes, Go Back"})]})]})})})]})}r()}catch(e){r(e)}})},65542:e=>{e.exports=require("next-auth")},67066:(e,n,t)=>{t.d(n,{FSj:()=>r.FSj,Ny1:()=>r.Ny1,OiG:()=>r.OiG,QVr:()=>r.QVr,qbC:()=>r.qbC,uoG:()=>r.uoG});var r=t(48648)},72115:e=>{e.exports=require("yaml")},74075:e=>{e.exports=require("zlib")},82015:e=>{e.exports=require("react")},88105:(e,n,t)=>{t.a(e,async(e,r)=>{try{t.d(n,{DZ:()=>c.D,Fc:()=>s.F,TN:()=>o.T,Tk:()=>h.T,_0:()=>a._,az:()=>l.a,ov:()=>i.o,y$:()=>d.y});var s=t(5128),o=t(76331),a=t(31772),l=t(45200),i=t(5448),c=t(30519),d=t(90088),h=t(17335),u=e([s,o,a,l,i,c,d,h]);[s,o,a,l,i,c,d,h]=u.then?(await u)():u,r()}catch(e){r(e)}})},88455:e=>{e.exports=import("@emotion/react")}};var n=require("../../webpack-runtime.js");n.C(e);var t=e=>n(n.s=e),r=n.X(0,[2457,9784,6021,3786,8740,9498,2142,1283,589,6185,4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(10918));module.exports=r})();