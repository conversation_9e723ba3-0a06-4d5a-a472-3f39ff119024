"use strict";exports.id=5129,exports.ids=[5129],exports.modules={15129:(e,s,i)=>{i.a(e,async(e,r)=>{try{i.r(s),i.d(s,{default:()=>a});var n=i(8732),o=i(46637),l=i(82015),t=i(87615),c=e([o]);o=(c.then?(await c)():c)[0];let E={General:{icon:t.pcC,permissions:["ADMINISTRATOR","VIEW_AUDIT_LOG","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","MANAGE_EMOJIS_AND_STICKERS","MANAGE_WEBHOOKS","VIEW_CHANNEL"]},Text:{icon:t.mEP,permissions:["SEND_MESSAGES","EMBED_LINKS","ATTACH_FILES","ADD_REACTIONS","USE_EXTERNAL_EMOJIS","MENTION_EVERYONE","MANAGE_MESSAGES","READ_MESSAGE_HISTORY"]},Voice:{icon:t.o77,permissions:["CONNECT","SPEAK","STREAM","USE_VAD","PRIORITY_SPEAKER","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS"]},Members:{icon:t.cfS,permissions:["KICK_MEMBERS","BAN_MEMBERS","CHANGE_NICKNAME","MANAGE_NICKNAMES","CREATE_INSTANT_INVITE"]}};function a({isOpen:e,onClose:s,onSuccess:i}){let r=(0,o.dj)(),[t,c]=(0,l.useState)(!1),[a,d]=(0,l.useState)({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1}),h=async()=>{if(!a.name.trim())return void r({title:"Error",description:"Role name is required",status:"error",duration:3e3});c(!0);try{let e=await fetch("/api/discord/roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to create role")}r({title:"Success",description:"Role created successfully",status:"success",duration:3e3}),d({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1}),i(),s()}catch(e){r({title:"Error",description:e.message||"Failed to create role",status:"error",duration:5e3})}finally{c(!1)}},A=e=>{d(s=>({...s,permissions:s.permissions.includes(e)?s.permissions.filter(s=>s!==e):[...s.permissions,e]}))},S=(e,s)=>{d(i=>({...i,[e]:s}))};return(0,n.jsxs)(o.aF,{isOpen:e,onClose:s,size:"xl",scrollBehavior:"inside",children:[(0,n.jsx)(o.mH,{backdropFilter:"blur(10px)"}),(0,n.jsxs)(o.$m,{bg:"gray.800",children:[(0,n.jsx)(o.rQ,{children:"Create New Role"}),(0,n.jsx)(o.s_,{}),(0,n.jsx)(o.cw,{children:(0,n.jsxs)(o.Tk,{spacing:6,children:[(0,n.jsxs)(o.MJ,{isRequired:!0,children:[(0,n.jsx)(o.lR,{children:"Role Name"}),(0,n.jsx)(o.pd,{placeholder:"Enter role name",value:a.name,onChange:e=>S("name",e.target.value)})]}),(0,n.jsxs)(o.MJ,{children:[(0,n.jsx)(o.lR,{children:"Role Color"}),(0,n.jsx)(o.pd,{type:"color",value:a.color,onChange:e=>S("color",e.target.value)})]}),(0,n.jsx)(o.MJ,{children:(0,n.jsxs)(o.zt,{spacing:4,children:[(0,n.jsx)(o.Sc,{isChecked:a.hoist,onChange:e=>S("hoist",e.target.checked),children:"Display role separately"}),(0,n.jsx)(o.Sc,{isChecked:a.mentionable,onChange:e=>S("mentionable",e.target.checked),children:"Allow anyone to @mention"})]})}),(0,n.jsx)(o.cG,{}),(0,n.jsx)(o.EY,{fontSize:"lg",fontWeight:"bold",alignSelf:"flex-start",children:"Permissions"}),Object.entries(E).map(([e,s])=>(0,n.jsxs)(o.az,{w:"full",children:[(0,n.jsxs)(o.zt,{mb:2,children:[(0,n.jsx)(o.In,{as:s.icon}),(0,n.jsx)(o.EY,{fontWeight:"semibold",children:e})]}),(0,n.jsx)(o.rS,{columns:2,spacing:2,children:s.permissions.map(e=>(0,n.jsx)(o.Sc,{isChecked:a.permissions.includes(e),onChange:()=>A(e),children:e.split("_").map(e=>e.charAt(0)+e.slice(1).toLowerCase()).join(" ")},e))})]},e))]})}),(0,n.jsxs)(o.jl,{children:[(0,n.jsx)(o.$n,{variant:"ghost",mr:3,onClick:s,children:"Cancel"}),(0,n.jsx)(o.$n,{colorScheme:"blue",onClick:h,isLoading:t,children:"Create Role"})]})]})]})}r()}catch(e){r(e)}})}};