import {
  Box,
  Icon,
  Text,
  Tooltip,
  VStack,
  HStack,
  Button,
  useColorModeValue,
  Spinner,
  useDisclosure,
} from '@chakra-ui/react';
import { FaCog } from 'react-icons/fa';
import { useRouter } from 'next/router';
import useExperimentalFeatures from '../hooks/useExperimentalFeatures';
import ExperimentalApplicationForm from './ExperimentalApplicationForm';

export default function ExperimentalFeatures() {
  const { hasAccess, isLoading, isDeveloper, isTester } = useExperimentalFeatures();
  const router = useRouter();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const bgColor = useColorModeValue('yellow.100', 'yellow.900');
  const borderColor = useColorModeValue('yellow.300', 'yellow.700');
  const textColor = useColorModeValue('yellow.800', 'yellow.100');

  if (isLoading) {
    return (
      <Box
        p={4}
        bg={bgColor}
        borderRadius="md"
        borderWidth="1px"
        borderColor={borderColor}
      >
        <HStack spacing={3}>
          <Icon as={FaCog} color={textColor} />
          <Text color={textColor} fontWeight="medium">Experimental Features</Text>
          <Spinner size="sm" color={textColor} />
        </HStack>
      </Box>
    );
  }

  const handleClick = () => {
    if (isDeveloper) {
      router.push('/admin/experimental-applications');
    } else if (hasAccess) {
      // User has access - open features page
      router.push('/experimental');
    } else {
      // Open application form for non-developers
      onOpen();
    }
  };

  return (
    <>
      <Box
        p={4}
        bg={bgColor}
        borderRadius="md"
        borderWidth="1px"
        borderColor={borderColor}
        cursor="pointer"
        transition="all 0.2s"
        _hover={{
          transform: 'translateY(-2px)',
          boxShadow: 'md'
        }}
        onClick={handleClick}
      >
        <VStack align="stretch" spacing={3}>
          <HStack>
            <Icon as={FaCog} color={textColor} />
            <Text color={textColor} fontWeight="medium">
              Experimental Features
            </Text>
            <Tooltip 
              label={isDeveloper ? 'Developer Access' : isTester ? 'Tester Access' : 'Applicant'} 
              placement="top"
            >
              <Text fontSize="xs" color={textColor} opacity={0.8}>
                ({isDeveloper ? '👨‍💻' : isTester ? '🧪' : '📥'})
              </Text>
            </Tooltip>
          </HStack>
          <Text fontSize="sm" color={textColor} opacity={0.8}>
            Try out new features that are still in development. These may not work as expected.
          </Text>
          <Button size="sm" colorScheme="yellow" variant="solid" onClick={handleClick} alignSelf="start">
            {isDeveloper ? 'Manage Features' : hasAccess ? 'Open Features' : 'Request Access'}
          </Button>
        </VStack>
      </Box>

      {/* Application Form */}
      <ExperimentalApplicationForm isOpen={isOpen} onClose={onClose} />
    </>
  );
} 