"use strict";exports.id=856,exports.ids=[856],exports.modules={20856:(e,s,n)=>{n.a(e,async(e,r)=>{try{n.r(s),n.d(s,{default:()=>c});var t=n(8732),a=n(30631),i=n(82015),l=e([a]);a=(l.then?(await l)():l)[0];let d={GUILD_TEXT:0,GUILD_VOICE:2,GUILD_CATEGORY:4};function c({isOpen:e,onClose:s,onSuccess:n}){let r=(0,a.dj)(),[l,c]=(0,i.useState)(!1),[h,o]=(0,i.useState)([]),[x,j]=(0,i.useState)({name:"",type:"GUILD_TEXT",parent:"",topic:"",nsfw:!1,rateLimitPerUser:0,position:0,bitrate:64e3,userLimit:0}),p=(e,s)=>{j(n=>({...n,[e]:s}))},u=async()=>{try{if(c(!0),!x.name.trim())return void r({title:"Error",description:"Channel name is required",status:"error",duration:3e3});let e=x.name.toLowerCase().replace(/\s+/g,"-"),t=d[x.type],a=await fetch("/api/discord/channels",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...x,name:e,type:t})});if(!a.ok){let e=await a.text(),s="Failed to create channel";try{let n=JSON.parse(e);s=n.message||n.error||s}catch(n){s=e}throw Error(s)}r({title:"Success",description:"Channel created successfully",status:"success",duration:3e3}),n?.(),s()}catch(e){r({title:"Error",description:e.message||"Failed to create channel",status:"error",duration:5e3})}finally{c(!1)}};return(0,t.jsxs)(a.aF,{isOpen:e,onClose:s,size:"xl",children:[(0,t.jsx)(a.mH,{backdropFilter:"blur(10px)"}),(0,t.jsxs)(a.$m,{bg:"gray.800",border:"1px",borderColor:"blue.500",children:[(0,t.jsx)(a.rQ,{children:"Create Channel"}),(0,t.jsx)(a.s_,{}),(0,t.jsx)(a.cw,{children:(0,t.jsxs)(a.BJ,{spacing:4,children:[(0,t.jsxs)(a.MJ,{isRequired:!0,children:[(0,t.jsx)(a.lR,{children:"Channel Name"}),(0,t.jsx)(a.pd,{placeholder:"Enter channel name",value:x.name,onChange:e=>p("name",e.target.value)}),(0,t.jsx)(a.eK,{children:"Channel name will be automatically formatted (lowercase, hyphens instead of spaces)"})]}),(0,t.jsxs)(a.MJ,{children:[(0,t.jsx)(a.lR,{children:"Channel Type"}),(0,t.jsxs)(a.l6,{value:x.type,onChange:e=>p("type",e.target.value),children:[(0,t.jsx)("option",{value:"GUILD_TEXT",children:"Text Channel"}),(0,t.jsx)("option",{value:"GUILD_VOICE",children:"Voice Channel"}),(0,t.jsx)("option",{value:"GUILD_CATEGORY",children:"Category"})]})]}),"GUILD_CATEGORY"!==x.type&&(0,t.jsxs)(a.MJ,{children:[(0,t.jsx)(a.lR,{children:"Parent Category"}),(0,t.jsxs)(a.l6,{placeholder:"Select category",value:x.parent,onChange:e=>p("parent",e.target.value),children:[(0,t.jsx)("option",{value:"",children:"None"}),h.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),"GUILD_TEXT"===x.type&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(a.MJ,{children:[(0,t.jsx)(a.lR,{children:"Channel Topic"}),(0,t.jsx)(a.pd,{placeholder:"Enter channel topic",value:x.topic,onChange:e=>p("topic",e.target.value)})]}),(0,t.jsxs)(a.MJ,{children:[(0,t.jsx)(a.lR,{children:"Slowmode (seconds)"}),(0,t.jsxs)(a.Q7,{min:0,max:21600,value:x.rateLimitPerUser,onChange:e=>p("rateLimitPerUser",parseInt(e)),children:[(0,t.jsx)(a.OO,{}),(0,t.jsxs)(a.lw,{children:[(0,t.jsx)(a.Q0,{}),(0,t.jsx)(a.Sh,{})]})]}),(0,t.jsx)(a.eK,{children:"Set how long users must wait between sending messages (0 to disable)"})]}),(0,t.jsxs)(a.MJ,{display:"flex",alignItems:"center",children:[(0,t.jsx)(a.lR,{mb:"0",children:"Age-Restricted (NSFW)"}),(0,t.jsx)(a.dO,{isChecked:x.nsfw,onChange:e=>p("nsfw",e.target.checked)})]})]}),"GUILD_VOICE"===x.type&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(a.MJ,{children:[(0,t.jsx)(a.lR,{children:"Bitrate (kbps)"}),(0,t.jsxs)(a.Q7,{min:8,max:96,value:x.bitrate/1e3,onChange:e=>p("bitrate",1e3*parseInt(e)),children:[(0,t.jsx)(a.OO,{}),(0,t.jsxs)(a.lw,{children:[(0,t.jsx)(a.Q0,{}),(0,t.jsx)(a.Sh,{})]})]})]}),(0,t.jsxs)(a.MJ,{children:[(0,t.jsx)(a.lR,{children:"User Limit"}),(0,t.jsxs)(a.Q7,{min:0,max:99,value:x.userLimit,onChange:e=>p("userLimit",parseInt(e)),children:[(0,t.jsx)(a.OO,{}),(0,t.jsxs)(a.lw,{children:[(0,t.jsx)(a.Q0,{}),(0,t.jsx)(a.Sh,{})]})]}),(0,t.jsx)(a.eK,{children:"Set to 0 for unlimited users"})]})]}),(0,t.jsxs)(a.MJ,{children:[(0,t.jsx)(a.lR,{children:"Position"}),(0,t.jsxs)(a.Q7,{min:0,value:x.position,onChange:e=>p("position",parseInt(e)),children:[(0,t.jsx)(a.OO,{}),(0,t.jsxs)(a.lw,{children:[(0,t.jsx)(a.Q0,{}),(0,t.jsx)(a.Sh,{})]})]}),(0,t.jsx)(a.eK,{children:"Channel position in the list (0 = top)"})]})]})}),(0,t.jsxs)(a.jl,{children:[(0,t.jsx)(a.$n,{variant:"ghost",mr:3,onClick:s,children:"Cancel"}),(0,t.jsx)(a.$n,{colorScheme:"blue",onClick:u,isLoading:l,loadingText:"Creating...",children:"Create Channel"})]})]})]})}r()}catch(e){r(e)}})},30631:(e,s,n)=>{n.a(e,async(e,r)=>{try{n.d(s,{$m:()=>o.$,$n:()=>t.$,BJ:()=>C.B,MJ:()=>a.MJ,OO:()=>u.OO,Q0:()=>u.Q0,Q7:()=>u.Q7,Sh:()=>u.Sh,aF:()=>c.aF,cw:()=>d.c,dO:()=>g.d,dj:()=>y.d,eK:()=>a.eK,jl:()=>x.j,l6:()=>m.l,lR:()=>i.l,lw:()=>u.lw,mH:()=>p.m,pd:()=>l.p,rQ:()=>j.r,s_:()=>h.s});var t=n(77502),a=n(23678),i=n(63957),l=n(15376),c=n(75460),d=n(42929),h=n(7394),o=n(89164),x=n(87346),j=n(95148),p=n(12725),u=n(71342),m=n(29742),C=n(5712),g=n(24046),y=n(5978),O=e([t,a,i,l,c,d,h,o,x,j,p,u,m,C,g,y]);[t,a,i,l,c,d,h,o,x,j,p,u,m,C,g,y]=O.then?(await O)():O,r()}catch(e){r(e)}})}};