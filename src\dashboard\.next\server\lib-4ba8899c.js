"use strict";exports.id=713,exports.ids=[713],exports.modules={3106:(t,e,r)=>{r.d(e,{J:()=>a});var a=t=>t.tooltip},8522:(t,e,r)=>{r.d(e,{q:()=>a});var a=(t,e,r,a)=>{var i;return"axis"===e?t.tooltipItemPayloads:0===t.tooltipItemPayloads.length?[]:null==(i="hover"===r?t.itemInteraction.hover.dataKey:t.itemInteraction.click.dataKey)&&null!=a?[t.tooltipItemPayloads[0]]:t.tooltipItemPayloads.filter(t=>{var e;return(null==(e=t.settings)?void 0:e.dataKey)===i})}},8801:(t,e,r)=>{r.d(e,{Be:()=>m,Cv:()=>M,D0:()=>A,Dc:()=>w,Gl:()=>h});var a=r(87242),i=r(99391),n=r(78813),o=r(39683),l=r(77331),u=r(5827),d=r(7872),s=r(98246),c=r(28158),v={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:u.c.angleAxisId,includeHidden:!1,name:void 0,reversed:u.c.reversed,scale:u.c.scale,tick:u.c.tick,tickCount:void 0,ticks:void 0,type:u.c.type,unit:void 0},f={allowDataOverflow:d.j.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:d.j.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:d.j.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:d.j.scale,tick:d.j.tick,tickCount:d.j.tickCount,ticks:void 0,type:d.j.type,unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:u.c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.c.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.c.scale,tick:u.c.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:d.j.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:d.j.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:d.j.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:d.j.scale,tick:d.j.tick,tickCount:d.j.tickCount,ticks:void 0,type:"category",unit:void 0},m=(t,e)=>null!=t.polarAxis.angleAxis[e]?t.polarAxis.angleAxis[e]:"radial"===t.layout.layoutType?p:v,h=(t,e)=>null!=t.polarAxis.radiusAxis[e]?t.polarAxis.radiusAxis[e]:"radial"===t.layout.layoutType?y:f,g=t=>t.polarOptions,x=(0,a.Mz)([i.Lp,i.A$,n.HZ],o.lY),b=(0,a.Mz)([g,x],(t,e)=>{if(null!=t)return(0,l.F4)(t.innerRadius,e,0)}),z=(0,a.Mz)([g,x],(t,e)=>{if(null!=t)return(0,l.F4)(t.outerRadius,e,.8*e)}),M=(0,a.Mz)([g],t=>{if(null==t)return[0,0];var{startAngle:e,endAngle:r}=t;return[e,r]});(0,a.Mz)([m,M],s.I);var w=(0,a.Mz)([x,b,z],(t,e,r)=>{if(null!=t&&null!=e&&null!=r)return[e,r]});(0,a.Mz)([h,w],s.I);var A=(0,a.Mz)([c.fz,g,b,z,i.Lp,i.A$],(t,e,r,a,i,n)=>{if(("centric"===t||"radial"===t)&&null!=e&&null!=r&&null!=a){var{cx:o,cy:u,startAngle:d,endAngle:s}=e;return{cx:(0,l.F4)(o,i,i/2),cy:(0,l.F4)(u,n,n/2),innerRadius:r,outerRadius:a,startAngle:d,endAngle:s,clockWise:!1}}})},10476:(t,e,r)=>{r.d(e,{W:()=>n,h:()=>i});var a=r(87242),i=(0,a.Mz)(t=>t.cartesianAxis.xAxis,t=>Object.values(t)),n=(0,a.Mz)(t=>t.cartesianAxis.yAxis,t=>Object.values(t))},14996:(t,e,r)=>{r.d(e,{d:()=>o});var a=r(87242),i=r(78987),n=r(99391),o=(0,a.Mz)([i.G,n.Lp,n.A$],(t,e,r)=>{if(t&&null!=e&&null!=r)return{x:t.left,y:t.top,width:Math.max(0,e-t.left-t.right),height:Math.max(0,r-t.top-t.bottom)}})},18154:(t,e,r)=>{r.d(e,{CU:()=>s,Lx:()=>u,u3:()=>d});var a=r(78456),i=r(7555),n=(0,a.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(t,e){t.size.width=e.payload.width,t.size.height=e.payload.height},setLegendSettings(t,e){t.settings.align=e.payload.align,t.settings.layout=e.payload.layout,t.settings.verticalAlign=e.payload.verticalAlign,t.settings.itemSorter=e.payload.itemSorter},addLegendPayload(t,e){t.payload.push((0,i.h4)(e.payload))},removeLegendPayload(t,e){var r=(0,a.ss)(t).payload.indexOf((0,i.h4)(e.payload));r>-1&&t.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:l,addLegendPayload:u,removeLegendPayload:d}=n.actions,s=n.reducer},28237:(t,e,r)=>{r.d(e,{g:()=>d});var a=r(87242),i=r(28158),n=r(77741),o=r(78813),l=r(97900),u=r(8801),d=(0,a.Mz)([(t,e)=>e,i.fz,u.D0,n.Re,n.gL,n.R4,l.r1,o.HZ],l.aX)},28995:(t,e,r)=>{r.d(e,{$7:()=>c,Ru:()=>s,uZ:()=>d});var a=r(78456),i=r(95322),n=r(77741),o=r(97900),l=r(68181),u=r(82115),d=(0,a.VP)("keyDown"),s=(0,a.VP)("focus"),c=(0,a.Nc)();c.startListening({actionCreator:d,effect:(t,e)=>{var r=e.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:a}=r.tooltip,d=t.payload;if("ArrowRight"===d||"ArrowLeft"===d||"Enter"===d){var s=Number((0,u.P)(a,(0,n.n4)(r))),c=(0,n.R4)(r);if("Enter"===d){var v=(0,o.pg)(r,"axis","hover",String(a.index));e.dispatch((0,i.o4)({active:!a.active,activeIndex:a.index,activeDataKey:a.dataKey,activeCoordinate:v}));return}var f=s+("ArrowRight"===d?1:-1)*("left-to-right"===(0,l._y)(r)?1:-1);if(null!=c&&!(f>=c.length)&&!(f<0)){var p=(0,o.pg)(r,"axis","hover",String(f));e.dispatch((0,i.o4)({active:!0,activeIndex:f.toString(),activeDataKey:void 0,activeCoordinate:p}))}}}}}),c.startListening({actionCreator:s,effect:(t,e)=>{var r=e.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:a}=r.tooltip;if(!a.active&&null==a.index){var n=(0,o.pg)(r,"axis","hover",String("0"));e.dispatch((0,i.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:n}))}}}})},34797:(t,e,r)=>{r.d(e,{G:()=>c,j:()=>l});var a=r(51053),i=r(82015),n=r(7536),o=t=>t,l=()=>{var t=(0,i.useContext)(n.E);return t?t.store.dispatch:o},u=()=>{},d=()=>u,s=(t,e)=>t===e;function c(t){var e=(0,i.useContext)(n.E);return(0,a.useSyncExternalStoreWithSelector)(e?e.subscription.addNestedSub:d,e?e.store.getState:u,e?e.store.getState:u,e?t:u,s)}},36336:(t,e,r)=>{r.d(e,{E:()=>N});var a=r(78456),i=r(58971),n=r(95322),o=r(87769),l=r(48493),u=r(61055);function d(t,e){return e instanceof HTMLElement?"HTMLElement <".concat(e.tagName,' class="').concat(e.className,'">'):e===window?"global.window":e}var s=r(42714),c=r(57472),v=(0,a.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(t,e)=>{t.dots.push(e.payload)},removeDot:(t,e)=>{var r=(0,a.ss)(t).dots.findIndex(t=>t===e.payload);-1!==r&&t.dots.splice(r,1)},addArea:(t,e)=>{t.areas.push(e.payload)},removeArea:(t,e)=>{var r=(0,a.ss)(t).areas.findIndex(t=>t===e.payload);-1!==r&&t.areas.splice(r,1)},addLine:(t,e)=>{t.lines.push(e.payload)},removeLine:(t,e)=>{var r=(0,a.ss)(t).lines.findIndex(t=>t===e.payload);-1!==r&&t.lines.splice(r,1)}}}),{addDot:f,removeDot:p,addArea:y,removeArea:m,addLine:h,removeLine:g}=v.actions,x=v.reducer,b=r(21237),z=r(18154),M=r(66735),w=r(7555),A=(0,a.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(t,e){t.radiusAxis[e.payload.id]=(0,w.h4)(e.payload)},removeRadiusAxis(t,e){delete t.radiusAxis[e.payload.id]},addAngleAxis(t,e){t.angleAxis[e.payload.id]=(0,w.h4)(e.payload)},removeAngleAxis(t,e){delete t.angleAxis[e.payload.id]}}}),{addRadiusAxis:O,removeRadiusAxis:k,addAngleAxis:P,removeAngleAxis:j}=A.actions,D=A.reducer,I=r(96517),S=r(28995),C=r(99225),K=r(38987),E=(0,a.HY)({brush:b.rT,cartesianAxis:s.CA,chartData:o.LV,graphicalItems:c.iZ,layout:l.Vp,legend:z.CU,options:i.lJ,polarAxis:D,polarOptions:I.J,referenceElements:x,rootProps:M.vE,tooltip:n.En}),N=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,a.U1)({reducer:E,preloadedState:t,middleware:t=>t({serializableCheck:!1}).concat([u.YF.middleware,u.fP.middleware,S.$7.middleware,C.x.middleware,K.k.middleware]),devTools:{serialize:{replacer:d},name:"recharts-".concat(e)}})}},42367:(t,e,r)=>{r.d(e,{u:()=>l});var a=r(87242),i=r(49126),n=r(3106),o=(0,a.Mz)([n.J],t=>t.tooltipItemPayloads),l=(0,a.Mz)([o,i.x,(t,e,r)=>e,(t,e,r)=>r],(t,e,r,a)=>{var i=t.find(t=>t.settings.dataKey===a);if(null!=i){var{positions:n}=i;if(null!=n)return e(n,r)}})},44626:(t,e,r)=>{r.d(e,{E:()=>i});var a=r(77331),i=(t,e)=>{var r,i=Number(e);if(!(0,a.M8)(i)&&null!=e)return i>=0?null==t||null==(r=t[i])?void 0:r.value:void 0}},48493:(t,e,r)=>{r.d(e,{B_:()=>i,JK:()=>n,Vp:()=>u,gX:()=>o,hF:()=>l});var a=(0,r(78456).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(t,e){t.layoutType=e.payload},setChartSize(t,e){t.width=e.payload.width,t.height=e.payload.height},setMargin(t,e){t.margin.top=e.payload.top,t.margin.right=e.payload.right,t.margin.bottom=e.payload.bottom,t.margin.left=e.payload.left},setScale(t,e){t.scale=e.payload}}}),{setMargin:i,setLayout:n,setChartSize:o,setScale:l}=a.actions,u=a.reducer},49126:(t,e,r)=>{r.d(e,{x:()=>a});var a=t=>t.options.tooltipPayloadSearcher},58971:(t,e,r)=>{r.d(e,{dl:()=>u,lJ:()=>l,uN:()=>n});var a=r(78456),i=r(77331);function n(t,e){if(e){var r=Number.parseInt(e,10);if(!(0,i.M8)(r))return null==t?void 0:t[r]}}var o=(0,a.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:t=>{null==t.eventEmitter&&(t.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=o.reducer,{createEventEmitter:u}=o.actions},61055:(t,e,r)=>{r.d(e,{YF:()=>d,dj:()=>s,fP:()=>c,ky:()=>u});var a=r(78456),i=r(95322),n=r(28237),o=r(82645),l=r(88223),u=(0,a.VP)("mouseClick"),d=(0,a.Nc)();d.startListening({actionCreator:u,effect:(t,e)=>{var r=t.payload,a=(0,n.g)(e.getState(),(0,l.w)(r));(null==a?void 0:a.activeIndex)!=null&&e.dispatch((0,i.jF)({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}});var s=(0,a.VP)("mouseMove"),c=(0,a.Nc)();c.startListening({actionCreator:s,effect:(t,e)=>{var r=t.payload,a=e.getState(),u=(0,o.au)(a,a.tooltip.settings.shared),d=(0,n.g)(a,(0,l.w)(r));"axis"===u&&((null==d?void 0:d.activeIndex)!=null?e.dispatch((0,i.Nt)({activeIndex:d.activeIndex,activeDataKey:void 0,activeCoordinate:d.activeCoordinate})):e.dispatch((0,i.xS)()))}})},66580:(t,e,r)=>{r.d(e,{HS:()=>o,LF:()=>i,z3:()=>n});var a=r(87242),i=t=>t.chartData,n=(0,a.Mz)([i],t=>{var e=null!=t.chartData?t.chartData.length-1:0;return{chartData:t.chartData,computedData:t.computedData,dataEndIndex:e,dataStartIndex:0}}),o=(t,e,r,a)=>a?n(t):i(t)},66735:(t,e,r)=>{r.d(e,{mZ:()=>l,vE:()=>o});var a=r(78456),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},n=(0,a.Z0)({name:"rootProps",initialState:i,reducers:{updateOptions:(t,e)=>{var r;t.accessibilityLayer=e.payload.accessibilityLayer,t.barCategoryGap=e.payload.barCategoryGap,t.barGap=null!=(r=e.payload.barGap)?r:i.barGap,t.barSize=e.payload.barSize,t.maxBarSize=e.payload.maxBarSize,t.stackOffset=e.payload.stackOffset,t.syncId=e.payload.syncId,t.syncMethod=e.payload.syncMethod,t.className=e.payload.className}}}),o=n.reducer,{updateOptions:l}=n.actions},68181:(t,e,r)=>{r.d(e,{$X:()=>tf,AV:()=>tM,BQ:()=>t5,CR:()=>er,D5:()=>tB,DP:()=>K,Gx:()=>ea,Hd:()=>E,IO:()=>B,KR:()=>t2,Kr:()=>tu,L$:()=>t8,Lw:()=>tY,MK:()=>Y,Nk:()=>$,Oz:()=>ty,P9:()=>td,PU:()=>P,Qn:()=>tI,Rl:()=>j,S5:()=>to,TC:()=>W,ZB:()=>et,Zi:()=>ee,_y:()=>en,bb:()=>tx,cd:()=>D,ec:()=>G,eo:()=>H,fb:()=>U,g1:()=>tK,gT:()=>tc,iV:()=>tR,iv:()=>t6,kz:()=>te,ld:()=>L,pM:()=>q,q:()=>th,rj:()=>R,sf:()=>I,sr:()=>tj,tF:()=>t4,tP:()=>tk,um:()=>N,wL:()=>tw,wP:()=>t7,xM:()=>tD,xp:()=>tS,yi:()=>tl});var a=r(87242),i=r(97812),n=r.n(i),o=r(5150),l=r(28158),u=r(29655),d=r(66580),s=r(47268),c=r(77331),v=r(4236),f=r(76885),p=r(99391),y=r(10476),m=r(78813),h=r(99660),g=r(87654),x=r(8801),b=r(97118),z=r(74321),M=r(98246),w=r(13111);function A(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,a)}return r}function O(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?A(Object(r),!0).forEach(function(e){var a,i,n;a=t,i=e,n=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var k=[0,"auto"],P={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},j=(t,e)=>{var r=t.cartesianAxis.xAxis[e];return null==r?P:r},D={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:k,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:w.tQ},I=(t,e)=>{var r=t.cartesianAxis.yAxis[e];return null==r?D:r},S={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},C=(t,e)=>{var r=t.cartesianAxis.zAxis[e];return null==r?S:r},K=(t,e,r)=>{switch(e){case"xAxis":return j(t,r);case"yAxis":return I(t,r);case"zAxis":return C(t,r);case"angleAxis":return(0,x.Be)(t,r);case"radiusAxis":return(0,x.Gl)(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},E=(t,e,r)=>{switch(e){case"xAxis":return j(t,r);case"yAxis":return I(t,r);case"angleAxis":return(0,x.Be)(t,r);case"radiusAxis":return(0,x.Gl)(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},N=t=>t.graphicalItems.countOfBars>0;function H(t,e){return r=>{switch(t){case"xAxis":return"xAxisId"in r&&r.xAxisId===e;case"yAxis":return"yAxisId"in r&&r.yAxisId===e;case"zAxis":return"zAxisId"in r&&r.zAxisId===e;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===e;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===e;default:return!1}}}var L=t=>t.graphicalItems.cartesianItems,Z=(0,a.Mz)([b.N,z.E],H),G=(t,e,r)=>t.filter(r).filter(t=>(null==e?void 0:e.includeHidden)===!0||!t.hide),T=(0,a.Mz)([L,K,Z],G),B=t=>t.filter(t=>void 0===t.stackId),F=(0,a.Mz)([T],B),R=t=>t.map(t=>t.data).filter(Boolean).flat(1),J=(0,a.Mz)([T],R),$=(t,e)=>{var{chartData:r=[],dataStartIndex:a,dataEndIndex:i}=e;return t.length>0?t:r.slice(a,i+1)},_=(0,a.Mz)([J,d.HS],$),U=(t,e,r)=>(null==e?void 0:e.dataKey)!=null?t.map(t=>({value:(0,u.kr)(t,e.dataKey)})):r.length>0?r.map(t=>t.dataKey).flatMap(e=>t.map(t=>({value:(0,u.kr)(t,e)}))):t.map(t=>({value:t})),V=(0,a.Mz)([_,K,T],U);function Q(t,e){switch(t){case"xAxis":return"x"===e.direction;case"yAxis":return"y"===e.direction;default:return!1}}function X(t){return t.filter(t=>(0,c.vh)(t)||t instanceof Date).map(Number).filter(t=>!1===(0,c.M8)(t))}var Y=(t,e,r)=>Object.fromEntries(Object.entries(e.reduce((t,e)=>(null==e.stackId||(null==t[e.stackId]&&(t[e.stackId]=[]),t[e.stackId].push(e)),t),{})).map(e=>{var[a,i]=e,n=i.map(t=>t.dataKey);return[a,{stackedData:(0,u.yy)(t,n,r),graphicalItems:i}]})),W=(0,a.Mz)([_,T,g.eC],Y),q=(t,e,r)=>{var{dataStartIndex:a,dataEndIndex:i}=e;if("zAxis"!==r){var n=(0,u.Mk)(t,a,i);if(null==n||0!==n[0]||0!==n[1])return n}},tt=(0,a.Mz)([W,d.LF,b.N],q),te=(t,e,r,a)=>r.length>0?t.flatMap(t=>r.flatMap(r=>{var i,n,o=null==(i=r.errorBars)?void 0:i.filter(t=>Q(a,t)),l=(0,u.kr)(t,null!=(n=e.dataKey)?n:r.dataKey);return{value:l,errorDomain:function(t,e,r){return!r||"number"!=typeof e||(0,c.M8)(e)||!r.length?[]:X(r.flatMap(r=>{var a,i,n=(0,u.kr)(t,r.dataKey);if(Array.isArray(n)?[a,i]=n:a=i=n,(0,v.H)(a)&&(0,v.H)(i))return[e-a,e+i]}))}(t,l,o)}})).filter(Boolean):(null==e?void 0:e.dataKey)!=null?t.map(t=>({value:(0,u.kr)(t,e.dataKey),errorDomain:[]})):t.map(t=>({value:t,errorDomain:[]})),tr=(0,a.Mz)(_,K,F,b.N,te);function ta(t){var{value:e}=t;if((0,c.vh)(e)||e instanceof Date)return e}var ti=t=>{var e=X(t.flatMap(t=>[t.value,t.errorDomain]).flat(1));if(0!==e.length)return[Math.min(...e),Math.max(...e)]},tn=(t,e,r)=>{var a=t.map(ta).filter(t=>null!=t);return r&&(null==e.dataKey||e.allowDuplicatedCategory&&(0,c.CG)(a))?n()(0,t.length):e.allowDuplicatedCategory?a:Array.from(new Set(a))},to=t=>{var e;if(null==t||!("domain"in t))return k;if(null!=t.domain)return t.domain;if(null!=t.ticks){if("number"===t.type){var r=X(t.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===t.type)return t.ticks.map(String)}return null!=(e=null==t?void 0:t.domain)?e:k},tl=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var a=e.filter(Boolean);if(0!==a.length){var i=a.flat();return[Math.min(...i),Math.max(...i)]}},tu=t=>t.referenceElements.dots,td=(t,e,r)=>t.filter(t=>"extendDomain"===t.ifOverflow).filter(t=>"xAxis"===e?t.xAxisId===r:t.yAxisId===r),ts=(0,a.Mz)([tu,b.N,z.E],td),tc=t=>t.referenceElements.areas,tv=(0,a.Mz)([tc,b.N,z.E],td),tf=t=>t.referenceElements.lines,tp=(0,a.Mz)([tf,b.N,z.E],td),ty=(t,e)=>{var r=X(t.map(t=>"xAxis"===e?t.x:t.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},tm=(0,a.Mz)(ts,b.N,ty),th=(t,e)=>{var r=X(t.flatMap(t=>["xAxis"===e?t.x1:t.y1,"xAxis"===e?t.x2:t.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},tg=(0,a.Mz)([tv,b.N],th),tx=(t,e)=>{var r=X(t.map(t=>"xAxis"===e?t.x:t.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},tb=(0,a.Mz)(tp,b.N,tx),tz=(0,a.Mz)(tm,tb,tg,(t,e,r)=>tl(t,r,e)),tM=(0,a.Mz)([K],to),tw=(t,e,r,a,i)=>{var n=(0,s.f5)(e,t.allowDataOverflow);return null!=n?n:(0,s.v1)(e,tl(r,i,ti(a)),t.allowDataOverflow)},tA=(0,a.Mz)([K,tM,tt,tr,tz],tw),tO=[0,1],tk=(t,e,r,a,i,o,l)=>{if(null!=t&&null!=r&&0!==r.length){var{dataKey:d,type:s}=t,c=(0,u._L)(e,o);return c&&null==d?n()(0,r.length):"category"===s?tn(a,t,c):"expand"===i?tO:l}},tP=(0,a.Mz)([K,l.fz,_,V,g.eC,b.N,tA],tk),tj=(t,e,r,a,i)=>{if(null!=t){var{scale:n,type:l}=t;if("auto"===n)return"radial"===e&&"radiusAxis"===i?"band":"radial"===e&&"angleAxis"===i?"linear":"category"===l&&a&&(a.indexOf("LineChart")>=0||a.indexOf("AreaChart")>=0||a.indexOf("ComposedChart")>=0&&!r)?"point":"category"===l?"band":"linear";if("string"==typeof n){var u="scale".concat((0,c.Zb)(n));return u in o?u:"point"}}},tD=(0,a.Mz)([K,l.fz,N,g.iO,b.N],tj);function tI(t,e,r,a){if(null!=r&&null!=a){if("function"==typeof t.scale)return t.scale.copy().domain(r).range(a);var i=function(t){if(null!=t){if(t in o)return o[t]();var e="scale".concat((0,c.Zb)(t));if(e in o)return o[e]()}}(e);if(null!=i){var n=i.domain(r).range(a);return(0,u.YB)(n),n}}}var tS=(t,e,r)=>{var a=to(e);if("auto"===r||"linear"===r){if(null!=e&&e.tickCount&&Array.isArray(a)&&("auto"===a[0]||"auto"===a[1])&&(0,s.JH)(t))return(0,f.d)(t,e.tickCount,e.allowDecimals);if(null!=e&&e.tickCount&&"number"===e.type&&(0,s.JH)(t))return(0,f.M)(t,e.tickCount,e.allowDecimals)}},tC=(0,a.Mz)([tP,E,tD],tS),tK=(t,e,r,a)=>"angleAxis"!==a&&(null==t?void 0:t.type)==="number"&&(0,s.JH)(e)&&Array.isArray(r)&&r.length>0?[Math.min(e[0],r[0]),Math.max(e[1],r[r.length-1])]:e,tE=(0,a.Mz)([K,tP,tC,b.N],tK),tN=(0,a.Mz)(V,K,(t,e)=>{if(e&&"number"===e.type){var r=1/0,a=Array.from(X(t.map(t=>t.value))).sort((t,e)=>t-e);if(a.length<2)return 1/0;var i=a[a.length-1]-a[0];if(0===i)return 1/0;for(var n=0;n<a.length-1;n++)r=Math.min(r,a[n+1]-a[n]);return r/i}}),tH=(0,a.Mz)(tN,l.fz,g.gY,m.HZ,(t,e,r,a)=>a,(t,e,r,a,i)=>{if(!(0,v.H)(t))return 0;var n="vertical"===e?a.height:a.width;if("gap"===i)return t*n/2;if("no-gap"===i){var o=(0,c.F4)(r,t*n),l=t*n/2;return l-o-(l-o)/n*o}return 0}),tL=(0,a.Mz)(j,(t,e)=>{var r=j(t,e);return null==r||"string"!=typeof r.padding?0:tH(t,"xAxis",e,r.padding)},(t,e)=>{if(null==t)return{left:0,right:0};var r,a,{padding:i}=t;return"string"==typeof i?{left:e,right:e}:{left:(null!=(r=i.left)?r:0)+e,right:(null!=(a=i.right)?a:0)+e}}),tZ=(0,a.Mz)(I,(t,e)=>{var r=I(t,e);return null==r||"string"!=typeof r.padding?0:tH(t,"yAxis",e,r.padding)},(t,e)=>{if(null==t)return{top:0,bottom:0};var r,a,{padding:i}=t;return"string"==typeof i?{top:e,bottom:e}:{top:(null!=(r=i.top)?r:0)+e,bottom:(null!=(a=i.bottom)?a:0)+e}}),tG=(0,a.Mz)([m.HZ,tL,h.U,h.C,(t,e,r)=>r],(t,e,r,a,i)=>{var{padding:n}=a;return i?[n.left,r.width-n.right]:[t.left+e.left,t.left+t.width-e.right]}),tT=(0,a.Mz)([m.HZ,l.fz,tZ,h.U,h.C,(t,e,r)=>r],(t,e,r,a,i,n)=>{var{padding:o}=i;return n?[a.height-o.bottom,o.top]:"horizontal"===e?[t.top+t.height-r.bottom,t.top+r.top]:[t.top+r.top,t.top+t.height-r.bottom]}),tB=(t,e,r,a)=>{var i;switch(e){case"xAxis":return tG(t,r,a);case"yAxis":return tT(t,r,a);case"zAxis":return null==(i=C(t,r))?void 0:i.range;case"angleAxis":return(0,x.Cv)(t);case"radiusAxis":return(0,x.Dc)(t,r);default:return}},tF=(0,a.Mz)([K,tB],M.I),tR=(0,a.Mz)([K,tD,tE,tF],tI);function tJ(t,e){return t.id<e.id?-1:+(t.id>e.id)}(0,a.Mz)(T,b.N,(t,e)=>t.flatMap(t=>{var e;return null!=(e=t.errorBars)?e:[]}).filter(t=>Q(e,t)));var t$=(t,e)=>e,t_=(t,e,r)=>r,tU=(0,a.Mz)(y.h,t$,t_,(t,e,r)=>t.filter(t=>t.orientation===e).filter(t=>t.mirror===r).sort(tJ)),tV=(0,a.Mz)(y.W,t$,t_,(t,e,r)=>t.filter(t=>t.orientation===e).filter(t=>t.mirror===r).sort(tJ)),tQ=(t,e)=>({width:t.width,height:e.height}),tX=(t,e)=>({width:"number"==typeof e.width?e.width:w.tQ,height:t.height}),tY=(0,a.Mz)(m.HZ,j,tQ),tW=(t,e,r)=>{switch(e){case"top":return t.top;case"bottom":return r-t.bottom;default:return 0}},tq=(t,e,r)=>{switch(e){case"left":return t.left;case"right":return r-t.right;default:return 0}},t0=(0,a.Mz)(p.A$,m.HZ,tU,t$,t_,(t,e,r,a,i)=>{var n,o={};return r.forEach(r=>{var l=tQ(e,r);null==n&&(n=tW(e,a,t));var u="top"===a&&!i||"bottom"===a&&i;o[r.id]=n-Number(u)*l.height,n+=(u?-1:1)*l.height}),o}),t1=(0,a.Mz)(p.Lp,m.HZ,tV,t$,t_,(t,e,r,a,i)=>{var n,o={};return r.forEach(r=>{var l=tX(e,r);null==n&&(n=tq(e,a,t));var u="left"===a&&!i||"right"===a&&i;o[r.id]=n-Number(u)*l.width,n+=(u?-1:1)*l.width}),o}),t8=(t,e)=>{var r=(0,m.HZ)(t),a=j(t,e);if(null!=a){var i=t0(t,a.orientation,a.mirror)[e];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},t2=(t,e)=>{var r=(0,m.HZ)(t),a=I(t,e);if(null!=a){var i=t1(t,a.orientation,a.mirror)[e];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},t7=(0,a.Mz)(m.HZ,I,(t,e)=>({width:"number"==typeof e.width?e.width:w.tQ,height:t.height})),t5=(t,e,r)=>{switch(e){case"xAxis":return tY(t,r).width;case"yAxis":return t7(t,r).height;default:return}},t4=(t,e,r,a)=>{if(null!=r){var{allowDuplicatedCategory:i,type:n,dataKey:o}=r,l=(0,u._L)(t,a),d=e.map(t=>t.value);if(o&&l&&"category"===n&&i&&(0,c.CG)(d))return d}},t9=(0,a.Mz)([l.fz,V,K,b.N],t4),t6=(t,e,r,a)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:n}=r;if((0,u._L)(t,a)&&("number"===i||"auto"!==n))return e.map(t=>t.value)}},t3=(0,a.Mz)([l.fz,V,E,b.N],t6),et=(0,a.Mz)([l.fz,(t,e,r)=>{switch(e){case"xAxis":return j(t,r);case"yAxis":return I(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},tD,tR,t9,t3,tB,tC,b.N],(t,e,r,a,i,n,o,l,d)=>{if(null==e)return null;var s=(0,u._L)(t,d);return{angle:e.angle,interval:e.interval,minTickGap:e.minTickGap,orientation:e.orientation,tick:e.tick,tickCount:e.tickCount,tickFormatter:e.tickFormatter,ticks:e.ticks,type:e.type,unit:e.unit,axisType:d,categoricalDomain:n,duplicateDomain:i,isCategorical:s,niceTicks:l,range:o,realScaleType:r,scale:a}}),ee=(0,a.Mz)([l.fz,E,tD,tR,tC,tB,t9,t3,b.N],(t,e,r,a,i,n,o,l,d)=>{if(null!=e&&null!=a){var s=(0,u._L)(t,d),{type:v,ticks:f,tickCount:p}=e,y="scaleBand"===r&&"function"==typeof a.bandwidth?a.bandwidth()/2:2,m="category"===v&&a.bandwidth?a.bandwidth()/y:0;m="angleAxis"===d&&null!=n&&n.length>=2?2*(0,c.sA)(n[0]-n[1])*m:m;var h=f||i;return h?h.map((t,e)=>({index:e,coordinate:a(o?o.indexOf(t):t)+m,value:t,offset:m})).filter(t=>!(0,c.M8)(t.coordinate)):s&&l?l.map((t,e)=>({coordinate:a(t)+m,value:t,index:e,offset:m})):a.ticks?a.ticks(p).map(t=>({coordinate:a(t)+m,value:t,offset:m})):a.domain().map((t,e)=>({coordinate:a(t)+m,value:o?o[t]:t,index:e,offset:m}))}}),er=(0,a.Mz)([l.fz,E,tR,tB,t9,t3,b.N],(t,e,r,a,i,n,o)=>{if(null!=e&&null!=r&&null!=a&&a[0]!==a[1]){var l=(0,u._L)(t,o),{tickCount:d}=e,s=0;return(s="angleAxis"===o&&(null==a?void 0:a.length)>=2?2*(0,c.sA)(a[0]-a[1])*s:s,l&&n)?n.map((t,e)=>({coordinate:r(t)+s,value:t,index:e,offset:s})):r.ticks?r.ticks(d).map(t=>({coordinate:r(t)+s,value:t,offset:s})):r.domain().map((t,e)=>({coordinate:r(t)+s,value:i?i[t]:t,index:e,offset:s}))}}),ea=(0,a.Mz)(K,tR,(t,e)=>{if(null!=t&&null!=e)return O(O({},t),{},{scale:e})}),ei=(0,a.Mz)([K,tD,tP,tF],tI);(0,a.Mz)((t,e,r)=>C(t,r),ei,(t,e)=>{if(null!=t&&null!=e)return O(O({},t),{},{scale:e})});var en=(0,a.Mz)([l.fz,y.h,y.W],(t,e,r)=>{switch(t){case"horizontal":return e.some(t=>t.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(t=>t.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},74321:(t,e,r)=>{r.d(e,{E:()=>a});var a=(t,e,r)=>r},77741:(t,e,r)=>{r.d(e,{BZ:()=>tn,eE:()=>td,Xb:()=>to,A2:()=>ti,yn:()=>ts,Dn:()=>A,gL:()=>Q,fl:()=>X,R4:()=>q,Re:()=>M,n4:()=>I});var a=r(87242),i=r(68181),n=r(28158),o=r(29655),l=r(66580),u=r(87654),d=r(77331),s=r(98246),c=r(82645),v=r(44626),f=r(98736),p=r(82115),y=r(99520),m=r(99391),h=r(78813),g=r(8522),x=r(49126),b=r(3106),z=r(95903),M=t=>{var e=(0,n.fz)(t);return"horizontal"===e?"xAxis":"vertical"===e?"yAxis":"centric"===e?"angleAxis":"radiusAxis"},w=t=>t.tooltip.settings.axisId,A=t=>{var e=M(t),r=w(t);return(0,i.Hd)(t,e,r)},O=(0,a.Mz)([A,n.fz,i.um,u.iO,M],i.sr),k=(0,a.Mz)([t=>t.graphicalItems.cartesianItems,t=>t.graphicalItems.polarItems],(t,e)=>[...t,...e]),P=(0,a.Mz)([M,w],i.eo),j=(0,a.Mz)([k,A,P],i.ec),D=(0,a.Mz)([j],i.rj),I=(0,a.Mz)([D,l.LF],i.Nk),S=(0,a.Mz)([I,A,j],i.fb),C=(0,a.Mz)([A],i.S5),K=(0,a.Mz)([I,j,u.eC],i.MK),E=(0,a.Mz)([K,l.LF,M],i.pM),N=(0,a.Mz)([j],i.IO),H=(0,a.Mz)([I,A,N,M],i.kz),L=(0,a.Mz)([i.Kr,M,w],i.P9),Z=(0,a.Mz)([L,M],i.Oz),G=(0,a.Mz)([i.gT,M,w],i.P9),T=(0,a.Mz)([G,M],i.q),B=(0,a.Mz)([i.$X,M,w],i.P9),F=(0,a.Mz)([B,M],i.bb),R=(0,a.Mz)([Z,F,T],i.yi),J=(0,a.Mz)([A,C,E,H,R],i.wL),$=(0,a.Mz)([A,n.fz,I,S,u.eC,M,J],i.tP),_=(0,a.Mz)([$,A,O],i.xp),U=(0,a.Mz)([A,$,_,M],i.g1),V=t=>{var e=M(t),r=w(t);return(0,i.D5)(t,e,r,!1)},Q=(0,a.Mz)([A,V],s.I),X=(0,a.Mz)([A,O,U,Q],i.Qn),Y=(0,a.Mz)([n.fz,S,A,M],i.tF),W=(0,a.Mz)([n.fz,S,A,M],i.iv),q=(0,a.Mz)([n.fz,A,O,X,V,Y,W,M],(t,e,r,a,i,n,l,u)=>{if(e){var{type:s}=e,c=(0,o._L)(t,u);if(a){var v="scaleBand"===r&&a.bandwidth?a.bandwidth()/2:2,f="category"===s&&a.bandwidth?a.bandwidth()/v:0;return(f="angleAxis"===u&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,d.sA)(i[0]-i[1])*f:f,c&&l)?l.map((t,e)=>({coordinate:a(t)+f,value:t,index:e,offset:f})):a.domain().map((t,e)=>({coordinate:a(t)+f,value:n?n[t]:t,index:e,offset:f}))}}}),tt=(0,a.Mz)([c.xH,c.Hw,t=>t.tooltip.settings],(t,e,r)=>(0,c.$g)(r.shared,t,e)),te=t=>t.tooltip.settings.trigger,tr=t=>t.tooltip.settings.defaultIndex,ta=(0,a.Mz)([b.J,tt,te,tr],f.i),ti=(0,a.Mz)([ta,I],p.P),tn=(0,a.Mz)([q,ti],v.E),to=(0,a.Mz)([ta],t=>{if(t)return t.dataKey}),tl=(0,a.Mz)([b.J,tt,te,tr],g.q),tu=(0,a.Mz)([m.Lp,m.A$,n.fz,h.HZ,q,tr,tl,x.x],y.o),td=(0,a.Mz)([ta,tu],(t,e)=>null!=t&&t.coordinate?t.coordinate:e),ts=(0,a.Mz)([ta],t=>t.active),tc=(0,a.Mz)([tl,ti,l.LF,A,tn,x.x,tt],z.N);(0,a.Mz)([tc],t=>{if(null!=t)return Array.from(new Set(t.map(t=>t.payload).filter(t=>null!=t)))})},77780:(t,e,r)=>{r.d(e,{Ez:()=>I,EX:()=>C});var a=r(87242),i=r(49528),n=r(66580),o=r(78813),l=r(29655),u=r(68181),d=r(28158),s=r(97118),c=r(74321),v=r(87654),f=t=>t.graphicalItems.polarItems,p=(0,a.Mz)([s.N,c.E],u.eo),y=(0,a.Mz)([f,u.DP,p],u.ec),m=(0,a.Mz)([y],u.rj),h=(0,a.Mz)([m,n.z3],u.Nk),g=(0,a.Mz)([h,u.DP,y],u.fb),x=(0,a.Mz)([h,u.DP,y],(t,e,r)=>r.length>0?t.flatMap(t=>r.flatMap(r=>{var a;return{value:(0,l.kr)(t,null!=(a=e.dataKey)?a:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==e?void 0:e.dataKey)!=null?t.map(t=>({value:(0,l.kr)(t,e.dataKey),errorDomain:[]})):t.map(t=>({value:t,errorDomain:[]}))),b=()=>void 0,z=(0,a.Mz)([u.DP,u.AV,b,x,b],u.wL),M=(0,a.Mz)([u.DP,d.fz,h,g,v.eC,s.N,z],u.tP),w=(0,a.Mz)([M,u.DP,u.xM],u.xp);function A(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,a)}return r}function O(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?A(Object(r),!0).forEach(function(e){var a,i,n;a=t,i=e,n=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}(0,a.Mz)([u.DP,M,w,s.N],u.g1);var k=(t,e)=>e,P=[],j=(t,e,r)=>(null==r?void 0:r.length)===0?P:r,D=(0,a.Mz)([n.z3,k,j],(t,e,r)=>{var a,{chartData:i}=t;if((a=(null==e?void 0:e.data)!=null&&e.data.length>0?e.data:i)&&a.length||null==r||(a=r.map(t=>O(O({},e.presentationProps),t.props))),null!=a)return a}),I=(0,a.Mz)([D,k,j],(t,e,r)=>{if(null!=t)return t.map((t,a)=>{var i,n,o=(0,l.kr)(t,e.nameKey,e.name);return n=null!=r&&null!=(i=r[a])&&null!=(i=i.props)&&i.fill?r[a].props.fill:"object"==typeof t&&null!=t&&"fill"in t?t.fill:e.fill,{value:(0,l.uM)(o,e.dataKey),color:n,payload:t,type:e.legendType}})}),S=(0,a.Mz)([f,k],(t,e)=>{if(t.some(t=>"pie"===t.type&&e.dataKey===t.dataKey&&e.data===t.data))return e}),C=(0,a.Mz)([D,S,j,o.HZ],(t,e,r,a)=>{if(null!=e&&null!=t)return(0,i.L)({offset:a,pieSettings:e,displayedData:t,cells:r})})},78813:(t,e,r)=>{r.d(e,{c2:()=>h,HZ:()=>y,Ds:()=>m});var a=r(87242),i=r(67063),n=r.n(i),o=r(87206),l=r.n(o),u=t=>t.legend.settings;(0,a.Mz)([t=>t.legend.payload,u],(t,e)=>{var{itemSorter:r}=e,a=t.flat(1);return r?l()(a,r):a});var d=r(29655),s=r(99391),c=r(10476),v=r(13111);function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,a)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var a,i,n;a=t,i=e,n=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var y=(0,a.Mz)([s.Lp,s.A$,s.HK,t=>t.brush.height,c.h,c.W,u,t=>t.legend.size],(t,e,r,a,i,o,l,u)=>{var s=o.reduce((t,e)=>{var{orientation:r}=e;if(!e.mirror&&!e.hide){var a="number"==typeof e.width?e.width:v.tQ;return p(p({},t),{},{[r]:t[r]+a})}return t},{left:r.left||0,right:r.right||0}),c=i.reduce((t,e)=>{var{orientation:r}=e;return e.mirror||e.hide?t:p(p({},t),{},{[r]:n()(t,"".concat(r))+e.height})},{top:r.top||0,bottom:r.bottom||0}),f=p(p({},c),s),y=f.bottom;f.bottom+=a;var m=t-(f=(0,d.s0)(f,l,u)).left-f.right,h=e-f.top-f.bottom;return p(p({brushBottom:y},f),{},{width:Math.max(m,0),height:Math.max(h,0)})}),m=(0,a.Mz)(y,t=>({x:t.left,y:t.top,width:t.width,height:t.height})),h=(0,a.Mz)(s.Lp,s.A$,(t,e)=>({x:0,y:0,width:t,height:e}))},78987:(t,e,r)=>{r.d(e,{G:()=>n});var a=r(87242),i=r(78813),n=(0,a.Mz)([i.HZ],t=>{if(t)return{top:t.top,bottom:t.bottom,left:t.left,right:t.right}})},82115:(t,e,r)=>{r.d(e,{P:()=>i});var a=r(4236),i=(t,e)=>{var r=null==t?void 0:t.index;if(null==r)return null;var i=Number(r);if(!(0,a.H)(i))return r;var n=Infinity;return e.length>0&&(n=e.length-1),String(Math.max(0,Math.min(i,n)))}},82645:(t,e,r)=>{r.d(e,{$g:()=>o,Hw:()=>n,Td:()=>u,au:()=>l,xH:()=>i});var a=r(34797),i=t=>t.options.defaultTooltipEventType,n=t=>t.options.validateTooltipEventTypes;function o(t,e,r){if(null==t)return e;var a=t?"axis":"item";return null==r?e:r.includes(a)?a:e}function l(t,e){return o(e,i(t),n(t))}function u(t){return(0,a.G)(e=>l(e,t))}},87654:(t,e,r)=>{r.d(e,{JN:()=>a,_5:()=>i,eC:()=>l,gY:()=>n,hX:()=>s,iO:()=>u,lZ:()=>d,pH:()=>c,x3:()=>o});var a=t=>t.rootProps.maxBarSize,i=t=>t.rootProps.barGap,n=t=>t.rootProps.barCategoryGap,o=t=>t.rootProps.barSize,l=t=>t.rootProps.stackOffset,u=t=>t.options.chartName,d=t=>t.rootProps.syncId,s=t=>t.rootProps.syncMethod,c=t=>t.options.eventEmitter},93849:(t,e,r)=>{r.d(e,{OS:()=>O});var a=r(87242),i=r(68181),n=r(77331),o=r(29655),l=r(73262),u=r(28158),d=r(66580),s=r(78813),c=r(87654),v=r(4236);function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,a)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var a,i,n;a=t,i=e,n=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var y=(t,e,r,a,i)=>i,m=(t,e,r)=>{var a=null!=r?r:t;if(!(0,n.uy)(a))return(0,n.F4)(a,e,0)},h=(0,a.Mz)([u.fz,i.ld,(t,e)=>e,(t,e,r)=>r,(t,e,r,a)=>a],(t,e,r,a,i)=>e.filter(e=>"horizontal"===t?e.xAxisId===r:e.yAxisId===a).filter(t=>t.isPanorama===i).filter(t=>!1===t.hide).filter(t=>"bar"===t.type));function g(t){return null!=t.stackId&&null!=t.dataKey}var x=(0,a.Mz)([h,c.x3,(t,e,r)=>"horizontal"===(0,u.fz)(t)?(0,i.BQ)(t,"xAxis",e):(0,i.BQ)(t,"yAxis",r)],(t,e,r)=>{var a=t.filter(g),i=t.filter(t=>null==t.stackId);return[...Object.entries(a.reduce((t,e)=>(t[e.stackId]||(t[e.stackId]=[]),t[e.stackId].push(e),t),{})).map(t=>{var[a,i]=t;return{stackId:a,dataKeys:i.map(t=>t.dataKey),barSize:m(e,r,i[0].barSize)}}),...i.map(t=>({stackId:void 0,dataKeys:[t.dataKey].filter(t=>null!=t),barSize:m(e,r,t.barSize)}))]}),b=(t,e,r,a)=>{var n,l;return"horizontal"===(0,u.fz)(t)?(n=(0,i.Gx)(t,"xAxis",e,a),l=(0,i.CR)(t,"xAxis",e,a)):(n=(0,i.Gx)(t,"yAxis",r,a),l=(0,i.CR)(t,"yAxis",r,a)),(0,o.Hj)(n,l)},z=(0,a.Mz)([x,c.JN,c._5,c.gY,(t,e,r,a,l)=>{var d,s,v,f,p=(0,u.fz)(t),y=(0,c.JN)(t),{maxBarSize:m}=l,h=(0,n.uy)(m)?y:m;return"horizontal"===p?(v=(0,i.Gx)(t,"xAxis",e,a),f=(0,i.CR)(t,"xAxis",e,a)):(v=(0,i.Gx)(t,"yAxis",r,a),f=(0,i.CR)(t,"yAxis",r,a)),null!=(d=null!=(s=(0,o.Hj)(v,f,!0))?s:h)?d:0},b,(t,e,r,a,i)=>i.maxBarSize],(t,e,r,a,i,o,l)=>{var u=function(t,e,r,a,i){var o,l=a.length;if(!(l<1)){var u=(0,n.F4)(t,r,0,!0),d=[];if((0,v.H)(a[0].barSize)){var s=!1,c=r/l,f=a.reduce((t,e)=>t+(e.barSize||0),0);(f+=(l-1)*u)>=r&&(f-=(l-1)*u,u=0),f>=r&&c>0&&(s=!0,c*=.9,f=l*c);var p={offset:((r-f)/2|0)-u,size:0};o=a.reduce((t,e)=>{var r,a=[...t,{stackId:e.stackId,dataKeys:e.dataKeys,position:{offset:p.offset+p.size+u,size:s?c:null!=(r=e.barSize)?r:0}}];return p=a[a.length-1].position,a},d)}else{var y=(0,n.F4)(e,r,0,!0);r-2*y-(l-1)*u<=0&&(u=0);var m=(r-2*y-(l-1)*u)/l;m>1&&(m>>=0);var h=(0,v.H)(i)?Math.min(m,i):m;o=a.reduce((t,e,r)=>[...t,{stackId:e.stackId,dataKeys:e.dataKeys,position:{offset:y+(m+u)*r+(m-h)/2,size:h}}],d)}return o}}(r,a,i!==o?i:o,t,(0,n.uy)(l)?e:l);return i!==o&&null!=u&&(u=u.map(t=>p(p({},t),{},{position:p(p({},t.position),{},{offset:t.position.offset-i/2})}))),u}),M=(0,a.Mz)([z,y],(t,e)=>{if(null!=t){var r=t.find(t=>t.stackId===e.stackId&&t.dataKeys.includes(e.dataKey));if(null!=r)return r.position}}),w=(0,a.Mz)([i.ld,y],(t,e)=>{if(t.some(t=>"bar"===t.type&&e.dataKey===t.dataKey&&e.stackId===t.stackId&&e.stackId===t.stackId))return e}),A=(0,a.Mz)([(t,e,r,a)=>"horizontal"===(0,u.fz)(t)?(0,i.TC)(t,"yAxis",r,a):(0,i.TC)(t,"xAxis",e,a),y],(t,e)=>{if(!t||(null==e?void 0:e.dataKey)==null)return;var{stackId:r}=e;if(null!=r){var a=t[r];if(a){var{stackedData:i}=a;if(i)return i.find(t=>t.key===e.dataKey)}}}),O=(0,a.Mz)([s.HZ,(t,e,r,a)=>(0,i.Gx)(t,"xAxis",e,a),(t,e,r,a)=>(0,i.Gx)(t,"yAxis",r,a),(t,e,r,a)=>(0,i.CR)(t,"xAxis",e,a),(t,e,r,a)=>(0,i.CR)(t,"yAxis",r,a),M,u.fz,d.HS,b,A,w,(t,e,r,a,i,n)=>n],(t,e,r,a,i,n,o,u,d,s,c,v)=>{var f,{chartData:p,dataStartIndex:y,dataEndIndex:m}=u;if(null!=c&&null!=n&&("horizontal"===o||"vertical"===o)&&null!=e&&null!=r&&null!=a&&null!=i&&null!=d){var{data:h}=c;if(null!=(f=null!=h&&h.length>0?h:null==p?void 0:p.slice(y,m+1)))return(0,l.L)({layout:o,barSettings:c,pos:n,bandSize:d,xAxis:e,yAxis:r,xAxisTicks:a,yAxisTicks:i,stackedData:s,displayedData:f,offset:t,cells:v})}})},95903:(t,e,r)=>{r.d(e,{N:()=>l});var a=r(77331),i=r(29655);function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,a)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach(function(e){var a,i,n;a=t,i=e,n=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var l=(t,e,r,n,l,u,d)=>{if(null!=e&&null!=u){var{chartData:s,computedData:c,dataStartIndex:v,dataEndIndex:f}=r;return t.reduce((t,r)=>{var p,y,m,h,g,{dataDefinedOnItem:x,settings:b}=r,z=function(t,e,r){return Array.isArray(t)&&t&&e+r!==0?t.slice(e,r+1):t}((p=x,y=s,null!=p?p:y),v,f),M=null!=(m=null==b?void 0:b.dataKey)?m:null==n?void 0:n.dataKey,w=null==b?void 0:b.nameKey;return Array.isArray(h=null!=n&&n.dataKey&&Array.isArray(z)&&!Array.isArray(z[0])&&"axis"===d?(0,a.eP)(z,n.dataKey,l):u(z,e,c,w))?h.forEach(e=>{var r=o(o({},b),{},{name:e.name,unit:e.unit,color:void 0,fill:void 0});t.push((0,i.GF)({tooltipEntrySettings:r,dataKey:e.dataKey,payload:e.payload,value:(0,i.kr)(e.payload,e.dataKey),name:e.name}))}):t.push((0,i.GF)({tooltipEntrySettings:b,dataKey:M,payload:h,value:(0,i.kr)(h,M),name:null!=(g=(0,i.kr)(h,w))?g:null==b?void 0:b.name})),t},[])}}},96517:(t,e,r)=>{r.d(e,{J:()=>n,U:()=>i});var a=(0,r(78456).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(t,e)=>e.payload}}),{updatePolarOptions:i}=a.actions,n=a.reducer},97118:(t,e,r)=>{r.d(e,{N:()=>a});var a=(t,e)=>e},97900:(t,e,r)=>{r.d(e,{BZ:()=>K,aX:()=>H,dS:()=>C,dp:()=>D,fW:()=>M,pg:()=>S,r1:()=>k,u9:()=>E,yn:()=>N});var a=r(87242),i=r(87206),n=r.n(i),o=r(34797),l=r(29655),u=r(66580),d=r(77741),s=r(87654),c=r(28158),v=r(78813),f=r(99391),p=r(44626),y=r(98736),m=r(82115),h=r(99520),g=r(8522),x=r(49126),b=r(3106),z=r(95903),M=()=>(0,o.G)(s.iO),w=(t,e)=>e,A=(t,e,r)=>r,O=(t,e,r,a)=>a,k=(0,a.Mz)(d.R4,t=>n()(t,t=>t.coordinate)),P=(0,a.Mz)([b.J,w,A,O],y.i),j=(0,a.Mz)([P,d.n4],m.P),D=(t,e,r)=>{if(null!=e){var a=(0,b.J)(t);return"axis"===e?"hover"===r?a.axisInteraction.hover.dataKey:a.axisInteraction.click.dataKey:"hover"===r?a.itemInteraction.hover.dataKey:a.itemInteraction.click.dataKey}},I=(0,a.Mz)([b.J,w,A,O],g.q),S=(0,a.Mz)([f.Lp,f.A$,c.fz,v.HZ,d.R4,O,I,x.x],h.o),C=(0,a.Mz)([P,S],(t,e)=>{var r;return null!=(r=t.coordinate)?r:e}),K=(0,a.Mz)(d.R4,j,p.E),E=(0,a.Mz)([I,j,u.LF,d.Dn,K,x.x,w],z.N),N=(0,a.Mz)([P],t=>({isActive:t.active,activeIndex:t.index})),H=(t,e,r,a,i,n,o,u)=>{if(t&&e&&a&&i&&n){var d=(0,l.r4)(t.chartX,t.chartY,e,r,u);if(d){var s=(0,l.SW)(d,e),c=(0,l.gH)(s,o,n,a,i),v=(0,l.bk)(e,n,c,d);return{activeIndex:String(c),activeCoordinate:v}}}}},98246:(t,e,r)=>{r.d(e,{I:()=>a});var a=(t,e)=>{if(t&&e)return null!=t&&t.reversed?[e[1],e[0]]:e}},98736:(t,e,r)=>{r.d(e,{i:()=>o});var a=r(95322);function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,a)}return r}function n(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var a,i,n;a=t,i=e,n=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,e||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in a?Object.defineProperty(a,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var o=(t,e,r,i)=>{if(null==e)return a.k_;var o=function(t,e,r){return"axis"===e?"click"===r?t.axisInteraction.click:t.axisInteraction.hover:"click"===r?t.itemInteraction.click:t.itemInteraction.hover}(t,e,r);if(null==o)return a.k_;if(o.active)return o;if(t.keyboardInteraction.active)return t.keyboardInteraction;if(t.syncInteraction.active&&null!=t.syncInteraction.index)return t.syncInteraction;var l=!0===t.settings.active;if(null!=o.index){if(l)return n(n({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return n(n({},a.k_),{},{coordinate:o.coordinate})}},99391:(t,e,r)=>{r.d(e,{A$:()=>i,HK:()=>o,Lp:()=>a,et:()=>n});var a=t=>t.layout.width,i=t=>t.layout.height,n=t=>t.layout.scale,o=t=>t.layout.margin},99520:(t,e,r)=>{r.d(e,{o:()=>a});var a=(t,e,r,a,i,n,o,l)=>{if(null!=n&&null!=l){var u=o[0],d=null==u?void 0:l(u.positions,n);if(null!=d)return d;var s=null==i?void 0:i[Number(n)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(a.top+e)/2};else return{x:(a.left+t)/2,y:s.coordinate}}}},99660:(t,e,r)=>{r.d(e,{C:()=>l,U:()=>u});var a=r(87242),i=r(78813),n=r(99391),o=r(77331),l=t=>t.brush,u=(0,a.Mz)([l,i.HZ,n.HK],(t,e,r)=>({height:t.height,x:(0,o.Et)(t.x)?t.x:e.left,y:(0,o.Et)(t.y)?t.y:e.top+e.height+e.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.Et)(t.width)?t.width:e.width}))}};