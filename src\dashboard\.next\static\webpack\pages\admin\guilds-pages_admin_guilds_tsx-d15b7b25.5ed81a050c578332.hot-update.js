"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/guilds-pages_admin_guilds_tsx-d15b7b25",{

/***/ "(pages-dir-browser)/./pages/admin/guilds.tsx":
/*!********************************!*\
  !*** ./pages/admin/guilds.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: () => (/* binding */ __N_SSP),\n/* harmony export */   \"default\": () => (/* binding */ ServerManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Card,CardBody,CardHeader,Checkbox,Container,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,SimpleGrid,Skeleton,Spinner,Tab,TabList,TabPanel,TabPanels,Table,Tabs,Tbody,Td,Text,Th,Thead,Tooltip,Tr,VStack,useDisclosure,useToast!=!@chakra-ui/react */ \"(pages-dir-browser)/__barrel_optimize__?names=Badge,Box,Button,Card,CardBody,CardHeader,Checkbox,Container,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,SimpleGrid,Skeleton,Spinner,Tab,TabList,TabPanel,TabPanels,Table,Tabs,Tbody,Td,Text,Th,Thead,Tooltip,Tr,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/Layout */ \"(pages-dir-browser)/./components/Layout.tsx\");\n/* harmony import */ var _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useGuildInfo */ \"(pages-dir-browser)/./hooks/useGuildInfo.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit2,FiFolderPlus,FiHash,FiLock,FiMessageCircle,FiMessageSquare,FiPlus,FiRadio,FiSave,FiServer,FiSettings,FiTool,FiTrash2,FiUsers,FiVolume2,FiZap!=!react-icons/fi */ \"(pages-dir-browser)/__barrel_optimize__?names=FiEdit2,FiFolderPlus,FiHash,FiLock,FiMessageCircle,FiMessageSquare,FiPlus,FiRadio,FiSave,FiServer,FiSettings,FiTool,FiTrash2,FiUsers,FiVolume2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaPalette!=!react-icons/fa */ \"(pages-dir-browser)/__barrel_optimize__?names=FaPalette!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_4__);\n// @ts-nocheck\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Dynamic imports for heavy components\nconst CreateChannelDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_a\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-4215ff4a\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"_pages-dir-browser_components_CreateChannelDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/CreateChannelDialog */ \"(pages-dir-browser)/./components/CreateChannelDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/CreateChannelDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 63,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c = CreateChannelDialog;\nconst EditChannelDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_a\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-4215ff4a\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"_pages-dir-browser_components_EditChannelDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/EditChannelDialog */ \"(pages-dir-browser)/./components/EditChannelDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/EditChannelDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 68,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c1 = EditChannelDialog;\nconst EditRoleDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"commons\"), __webpack_require__.e(\"_pages-dir-browser_components_EditRoleDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/EditRoleDialog */ \"(pages-dir-browser)/./components/EditRoleDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/EditRoleDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 73,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c2 = EditRoleDialog;\nconst ColorBuilder = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-browser_components_ColorBuilder_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/ColorBuilder */ \"(pages-dir-browser)/./components/ColorBuilder.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/ColorBuilder\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 78,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c3 = ColorBuilder;\nconst CreateRoleDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"commons\"), __webpack_require__.e(\"_pages-dir-browser_components_CreateRoleDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/CreateRoleDialog */ \"(pages-dir-browser)/./components/CreateRoleDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/CreateRoleDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 83,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c4 = CreateRoleDialog;\n// Rate limiting constants\nconst RATE_LIMIT_MS = 2000; // 2 seconds between operations\nconst BULK_RATE_LIMIT_MS = 5000; // 5 seconds between bulk operations\n// Custom hook for rate limiting\nfunction useRateLimit() {\n    let delay = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : RATE_LIMIT_MS;\n    _s();\n    const [isRateLimited, setIsRateLimited] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const resetRateLimit = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"useRateLimit.useCallback[resetRateLimit]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            setIsRateLimited(true);\n            timeoutRef.current = setTimeout({\n                \"useRateLimit.useCallback[resetRateLimit]\": ()=>{\n                    setIsRateLimited(false);\n                }\n            }[\"useRateLimit.useCallback[resetRateLimit]\"], delay);\n        }\n    }[\"useRateLimit.useCallback[resetRateLimit]\"], [\n        delay\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"useRateLimit.useEffect\": ()=>{\n            return ({\n                \"useRateLimit.useEffect\": ()=>{\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"useRateLimit.useEffect\"];\n        }\n    }[\"useRateLimit.useEffect\"], []);\n    return [\n        isRateLimited,\n        resetRateLimit\n    ];\n}\n_s(useRateLimit, \"C8Tx8E3LpqDtNI/63DsUKsVihwU=\");\nconst CHANNEL_TYPE_CONFIG = {\n    0: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare,\n        color: 'blue',\n        label: 'Text'\n    },\n    2: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiVolume2,\n        color: 'green',\n        label: 'Voice'\n    },\n    4: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiFolderPlus,\n        color: 'purple',\n        label: 'Category'\n    },\n    5: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiRadio,\n        color: 'orange',\n        label: 'Announcement'\n    },\n    11: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageCircle,\n        color: 'cyan',\n        label: 'Public Thread'\n    },\n    12: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiLock,\n        color: 'pink',\n        label: 'Private Thread'\n    },\n    13: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHash,\n        color: 'teal',\n        label: 'Stage Voice'\n    },\n    15: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHash,\n        color: 'gray',\n        label: 'Forum'\n    }\n};\nconst PERMISSION_BADGES = {\n    ADMINISTRATOR: {\n        color: 'red',\n        label: 'Admin'\n    },\n    MANAGE_GUILD: {\n        color: 'orange',\n        label: 'Manage Server'\n    },\n    MANAGE_ROLES: {\n        color: 'yellow',\n        label: 'Manage Roles'\n    },\n    MANAGE_CHANNELS: {\n        color: 'green',\n        label: 'Manage Channels'\n    },\n    KICK_MEMBERS: {\n        color: 'purple',\n        label: 'Kick'\n    },\n    BAN_MEMBERS: {\n        color: 'pink',\n        label: 'Ban'\n    },\n    MANAGE_MESSAGES: {\n        color: 'blue',\n        label: 'Manage Messages'\n    },\n    MENTION_EVERYONE: {\n        color: 'cyan',\n        label: 'Mention @everyone'\n    }\n};\n// Add this helper map and function after PERMISSION_BADGES constant\nconst PERMISSION_FLAG_BITS = {\n    ADMINISTRATOR: 1n << 3n,\n    MANAGE_GUILD: 1n << 5n,\n    MANAGE_ROLES: 1n << 28n,\n    MANAGE_CHANNELS: 1n << 4n,\n    KICK_MEMBERS: 1n << 1n,\n    BAN_MEMBERS: 1n << 2n,\n    MANAGE_MESSAGES: 1n << 13n,\n    MENTION_EVERYONE: 1n << 17n\n};\nfunction decodePermissions(bitfield) {\n    if (!bitfield) return [];\n    if (Array.isArray(bitfield)) return bitfield;\n    try {\n        const permissions = [];\n        const bits = BigInt(bitfield);\n        for (const [permission, bit] of Object.entries(PERMISSION_FLAG_BITS)){\n            if ((bits & bit) === bit) {\n                permissions.push(permission);\n            }\n        }\n        return permissions;\n    } catch (error) {\n        console.error('Error decoding permissions:', error);\n        return [];\n    }\n}\nvar __N_SSP = true;\nfunction ServerManagement() {\n    _s1();\n    const toast = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const { displayName } = (0,_hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [isRateLimited, resetRateLimit] = useRateLimit();\n    const [isBulkRateLimited, resetBulkRateLimit] = useRateLimit(BULK_RATE_LIMIT_MS);\n    // State for guild settings\n    const [guildData, setGuildData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        prefix: '!',\n        botName: 'Bot',\n        guildName: '',\n        guildId: '',\n        guildIcon: null,\n        activities: [\n            {\n                type: 'PLAYING',\n                name: 'with Discord.js'\n            }\n        ],\n        activityRotationInterval: 60\n    });\n    // State for roles and channels\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [channels, setChannels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [channelsLoading, setChannelsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Modal states\n    const { isOpen: isCreateChannelOpen, onOpen: onCreateChannelOpen, onClose: onCreateChannelClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isEditChannelOpen, onOpen: onEditChannelOpen, onClose: onEditChannelClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isCreateRoleOpen, onOpen: onCreateRoleOpen, onClose: onCreateRoleClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isEditRoleOpen, onOpen: onEditRoleOpen, onClose: onEditRoleClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isColorBuilderOpen, onOpen: onColorBuilderOpen, onClose: onColorBuilderClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    // Selected items for editing\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedChannel, setSelectedChannel] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // Bulk selection state\n    const [selectedRoles, setSelectedRoles] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedChannels, setSelectedChannels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bulkDeleting, setBulkDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // File upload state\n    const [iconFile, setIconFile] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [iconPreview, setIconPreview] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const handleIconFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            setIconFile(file);\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                return setIconPreview((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result);\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const uploadIcon = async ()=>{\n        if (!iconFile) return;\n        const formData = new FormData();\n        formData.append('icon', iconFile);\n        try {\n            const response = await fetch('/api/discord/settings', {\n                method: 'POST',\n                body: formData\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setGuildData((prev)=>({\n                        ...prev,\n                        guildIcon: data.iconUrl\n                    }));\n                setIconFile(null);\n                setIconPreview(null);\n                toast({\n                    title: 'Success',\n                    description: 'Guild icon updated successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to upload icon',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const fetchGuildData = async ()=>{\n        try {\n            const [guildResponse, rolesResponse] = await Promise.all([\n                fetch('/api/discord/guild'),\n                fetch('/api/discord/roles')\n            ]);\n            if (guildResponse.ok) {\n                const guild = await guildResponse.json();\n                setGuildData((prev)=>({\n                        ...prev,\n                        guildName: guild.name,\n                        guildId: guild.id,\n                        guildIcon: guild.icon,\n                        botName: guild.botName || prev.botName // Use API botName or fallback to current\n                    }));\n            }\n            if (rolesResponse.ok) {\n                const rolesData = await rolesResponse.json();\n                const arr = Array.isArray(rolesData) ? rolesData : rolesData.roles || [];\n                setRoles(arr.sort((a, b)=>b.position - a.position));\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch guild data',\n                status: 'error',\n                duration: 3000\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchChannels = async ()=>{\n        try {\n            const response = await fetch('/api/discord/channels');\n            if (!response.ok) {\n                throw new Error('Failed to fetch channels');\n            }\n            const data = await response.json();\n            const sortedChannels = (data || []).sort((a, b)=>{\n                if (a.type === 4 && b.type !== 4) return -1;\n                if (a.type !== 4 && b.type === 4) return 1;\n                return a.position - b.position;\n            });\n            setChannels(sortedChannels);\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch channels',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setChannelsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"ServerManagement.useEffect\": ()=>{\n            fetchGuildData();\n            fetchChannels();\n        }\n    }[\"ServerManagement.useEffect\"], []);\n    const handleSettingChange = (setting, value)=>{\n        setGuildData((prev)=>({\n                ...prev,\n                [setting]: value\n            }));\n    };\n    const saveSettings = async ()=>{\n        if (saving || isRateLimited) return;\n        setSaving(true);\n        resetRateLimit();\n        try {\n            const response = await fetch('/api/discord/settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(guildData)\n            });\n            if (response.ok) {\n                toast({\n                    title: 'Success',\n                    description: 'Settings saved successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            } else {\n                throw new Error('Failed to save settings');\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to save settings',\n                status: 'error',\n                duration: 3000\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleRoleEdit = (role)=>{\n        setSelectedRole(role);\n        onEditRoleOpen();\n    };\n    const handleChannelEdit = (channel)=>{\n        setSelectedChannel(channel);\n        onEditChannelOpen();\n    };\n    const handleChannelCreate = ()=>{\n        onCreateChannelOpen();\n    };\n    const handleRoleCreate = ()=>{\n        onCreateRoleOpen();\n    };\n    const handleChannelDelete = async (channelId)=>{\n        if (isRateLimited) return;\n        try {\n            resetRateLimit();\n            const response = await fetch(\"/api/discord/channels/\".concat(channelId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchChannels();\n                toast({\n                    title: 'Success',\n                    description: 'Channel deleted successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to delete channel',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const getParentName = (parentId)=>{\n        if (!parentId || !channels) return '-';\n        const parent = channels.find((c)=>c.id === parentId);\n        return parent ? parent.name : '-';\n    };\n    // Bulk delete functions\n    const handleBulkDeleteRoles = async ()=>{\n        if (selectedRoles.length === 0 || bulkDeleting || isBulkRateLimited) return;\n        setBulkDeleting(true);\n        resetBulkRateLimit();\n        try {\n            const response = await fetch('/api/discord/roles/bulk-delete', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    roleIds: selectedRoles\n                })\n            });\n            const result = await response.json();\n            if (response.ok) {\n                toast({\n                    title: 'Success',\n                    description: result.message,\n                    status: 'success',\n                    duration: 5000\n                });\n                setSelectedRoles([]);\n                fetchGuildData(); // Refresh roles\n            } else {\n                throw new Error(result.error || 'Failed to delete roles');\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to delete roles',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setBulkDeleting(false);\n        }\n    };\n    const handleBulkDeleteChannels = async ()=>{\n        if (selectedChannels.length === 0 || bulkDeleting || isBulkRateLimited) return;\n        setBulkDeleting(true);\n        resetBulkRateLimit();\n        try {\n            const response = await fetch('/api/discord/channels/bulk-delete', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    channelIds: selectedChannels\n                })\n            });\n            const result = await response.json();\n            if (response.ok) {\n                toast({\n                    title: 'Success',\n                    description: result.message,\n                    status: 'success',\n                    duration: 5000\n                });\n                setSelectedChannels([]);\n                fetchChannels(); // Refresh channels\n            } else {\n                throw new Error(result.error || 'Failed to delete channels');\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to delete channels',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setBulkDeleting(false);\n        }\n    };\n    // Selection helper functions\n    const toggleRoleSelection = (roleId)=>{\n        setSelectedRoles((prev)=>prev.includes(roleId) ? prev.filter((id)=>id !== roleId) : [\n                ...prev,\n                roleId\n            ]);\n    };\n    const toggleChannelSelection = (channelId)=>{\n        setSelectedChannels((prev)=>prev.includes(channelId) ? prev.filter((id)=>id !== channelId) : [\n                ...prev,\n                channelId\n            ]);\n    };\n    const selectAllRoles = ()=>{\n        const selectableRoles = roles.filter((role)=>role.name !== '@everyone').map((role)=>role.id);\n        setSelectedRoles(selectedRoles.length === selectableRoles.length ? [] : selectableRoles);\n    };\n    const selectAllChannels = ()=>{\n        const channelIds = channels.map((channel)=>channel.id);\n        setSelectedChannels(selectedChannels.length === channelIds.length ? [] : channelIds);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                    spacing: 6,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                            height: \"60px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                            height: \"400px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 563,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                lineNumber: 560,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 559,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                    spacing: 8,\n                    align: \"stretch\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"rgba(255,255,255,0.08)\",\n                            p: 8,\n                            rounded: \"2xl\",\n                            backdropFilter: \"blur(10px)\",\n                            border: \"2px solid\",\n                            borderColor: \"blue.400\",\n                            boxShadow: \"0 0 15px rgba(66, 153, 225, 0.4)\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                justify: \"space-between\",\n                                align: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                size: \"xl\",\n                                                bgGradient: \"linear(to-r, blue.300, purple.400)\",\n                                                bgClip: \"text\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                        as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiServer,\n                                                        mr: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Server Management\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                color: \"gray.300\",\n                                                mt: 2,\n                                                children: [\n                                                    \"Comprehensive management for \",\n                                                    displayName || guildData.guildName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSave, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 27\n                                        }, void 0),\n                                        colorScheme: \"blue\",\n                                        onClick: saveSettings,\n                                        isLoading: saving,\n                                        isDisabled: isRateLimited,\n                                        size: \"lg\",\n                                        children: \"Save Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                            colorScheme: \"blue\",\n                            isLazy: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"General Settings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPalette,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Theme Builder\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTool,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Builders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Automation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanels, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 8,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                    size: \"md\",\n                                                                    children: \"Basic Settings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                                    columns: {\n                                                                        base: 1,\n                                                                        lg: 2\n                                                                    },\n                                                                    spacing: 6,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Bot Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 645,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.botName,\n                                                                                            onChange: (e)=>handleSettingChange('botName', e.target.value),\n                                                                                            placeholder: \"Enter bot name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 646,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 644,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Command Prefix\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 653,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.prefix,\n                                                                                            onChange: (e)=>handleSettingChange('prefix', e.target.value),\n                                                                                            placeholder: \"Enter command prefix\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 654,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 652,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 643,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Server Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 663,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.guildName || '',\n                                                                                            isReadOnly: true,\n                                                                                            bg: \"gray.50\",\n                                                                                            _dark: {\n                                                                                                bg: 'gray.700'\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 664,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 662,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Server ID\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 672,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.guildId || '',\n                                                                                            isReadOnly: true,\n                                                                                            bg: \"gray.50\",\n                                                                                            _dark: {\n                                                                                                bg: 'gray.700'\n                                                                                            },\n                                                                                            fontFamily: \"mono\",\n                                                                                            fontSize: \"sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 673,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 671,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 642,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                                    size: \"md\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                                            as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiUsers,\n                                                                                            mr: 2\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 693,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        \"Roles (\",\n                                                                                        roles.length,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 692,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPlus, {}, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 698,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        colorScheme: \"green\",\n                                                                                        onClick: handleRoleCreate,\n                                                                                        isDisabled: isRateLimited,\n                                                                                        size: \"sm\",\n                                                                                        children: \"Create Role\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 697,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 696,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            p: 3,\n                                                                            bg: \"blue.50\",\n                                                                            _dark: {\n                                                                                bg: 'blue.900'\n                                                                            },\n                                                                            borderRadius: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    fontWeight: \"medium\",\n                                                                                    children: [\n                                                                                        selectedRoles.length,\n                                                                                        \" role(s) selected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 711,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            variant: \"outline\",\n                                                                                            onClick: ()=>setSelectedRoles([]),\n                                                                                            children: \"Clear Selection\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 715,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            colorScheme: \"red\",\n                                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 725,\n                                                                                                columnNumber: 43\n                                                                                            }, void 0),\n                                                                                            onClick: handleBulkDeleteRoles,\n                                                                                            isLoading: bulkDeleting,\n                                                                                            isDisabled: isBulkRateLimited,\n                                                                                            children: \"Delete Selected\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 722,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 714,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 710,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 3,\n                                                                    children: [\n                                                                        ...Array(3)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                                            height: \"60px\"\n                                                                        }, i, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 741,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                                    overflowX: \"auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                                                        variant: \"simple\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Thead, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                isChecked: selectedRoles.length === roles.filter((r)=>r.name !== '@everyone').length && roles.length > 1,\n                                                                                                isIndeterminate: selectedRoles.length > 0 && selectedRoles.length < roles.filter((r)=>r.name !== '@everyone').length,\n                                                                                                onChange: selectAllRoles\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 750,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 749,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Role\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 756,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Members\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 757,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Permissions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 758,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Actions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 759,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 748,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 747,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tbody, {\n                                                                                children: (roles || []).map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                    isChecked: selectedRoles.includes(role.id),\n                                                                                                    onChange: ()=>toggleRoleSelection(role.id),\n                                                                                                    isDisabled: role.name === '@everyone'\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 766,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 765,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                                                                            w: 4,\n                                                                                                            h: 4,\n                                                                                                            rounded: \"full\",\n                                                                                                            bg: role.color ? \"#\".concat(role.color.toString(16).padStart(6, '0')) : 'gray.500'\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 774,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                            children: role.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 780,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 773,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 772,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                    colorScheme: \"blue\",\n                                                                                                    children: \"0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 784,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 783,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    wrap: \"wrap\",\n                                                                                                    spacing: 1,\n                                                                                                    children: [\n                                                                                                        (decodePermissions(role.permissions) || []).slice(0, 3).map((perm)=>{\n                                                                                                            var _PERMISSION_BADGES_perm, _PERMISSION_BADGES_perm1;\n                                                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                                colorScheme: ((_PERMISSION_BADGES_perm = PERMISSION_BADGES[perm]) === null || _PERMISSION_BADGES_perm === void 0 ? void 0 : _PERMISSION_BADGES_perm.color) || 'gray',\n                                                                                                                size: \"sm\",\n                                                                                                                children: ((_PERMISSION_BADGES_perm1 = PERMISSION_BADGES[perm]) === null || _PERMISSION_BADGES_perm1 === void 0 ? void 0 : _PERMISSION_BADGES_perm1.label) || perm\n                                                                                                            }, perm, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 789,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this);\n                                                                                                        }),\n                                                                                                        decodePermissions(role.permissions).length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                            colorScheme: \"gray\",\n                                                                                                            size: \"sm\",\n                                                                                                            children: [\n                                                                                                                \"+\",\n                                                                                                                decodePermissions(role.permissions).length - 3\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 798,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 787,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 786,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Edit Role\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Edit role\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiEdit2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 809,\n                                                                                                                    columnNumber: 49\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"blue\",\n                                                                                                                onClick: ()=>handleRoleEdit(role),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 807,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 806,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Delete Role\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Delete role\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 820,\n                                                                                                                    columnNumber: 49\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"red\",\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 818,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 817,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 805,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 804,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, role.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 764,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 762,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 746,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                                    size: \"md\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                                            as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHash,\n                                                                                            mr: 2\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 844,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        \"Channels (\",\n                                                                                        channels.length,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 843,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPlus, {}, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 849,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        colorScheme: \"blue\",\n                                                                                        onClick: onCreateChannelOpen,\n                                                                                        size: \"sm\",\n                                                                                        children: \"Create Channel\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 848,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 847,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 842,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedChannels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            p: 3,\n                                                                            bg: \"blue.50\",\n                                                                            _dark: {\n                                                                                bg: 'blue.900'\n                                                                            },\n                                                                            borderRadius: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    fontWeight: \"medium\",\n                                                                                    children: [\n                                                                                        selectedChannels.length,\n                                                                                        \" channel(s) selected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 861,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            variant: \"outline\",\n                                                                                            onClick: ()=>setSelectedChannels([]),\n                                                                                            children: \"Clear Selection\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 865,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            colorScheme: \"red\",\n                                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 875,\n                                                                                                columnNumber: 43\n                                                                                            }, void 0),\n                                                                                            onClick: handleBulkDeleteChannels,\n                                                                                            isLoading: bulkDeleting,\n                                                                                            isDisabled: isBulkRateLimited,\n                                                                                            children: \"Delete Selected\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 872,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 864,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 860,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 840,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                children: channelsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 3,\n                                                                    children: [\n                                                                        ...Array(5)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                                            height: \"50px\"\n                                                                        }, i, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 891,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 889,\n                                                                    columnNumber: 25\n                                                                }, this) : channels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                    color: \"gray.500\",\n                                                                    textAlign: \"center\",\n                                                                    py: 8,\n                                                                    children: \"No channels found\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                                    overflowX: \"auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                                                        variant: \"simple\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Thead, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                isChecked: selectedChannels.length === channels.length && channels.length > 0,\n                                                                                                isIndeterminate: selectedChannels.length > 0 && selectedChannels.length < channels.length,\n                                                                                                onChange: selectAllChannels\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 904,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 903,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 910,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Type\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 911,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Category\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 912,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Position\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 913,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Actions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 914,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 902,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 901,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tbody, {\n                                                                                children: (channels || []).map((channel)=>{\n                                                                                    const typeConfig = CHANNEL_TYPE_CONFIG[channel.type] || {\n                                                                                        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare,\n                                                                                        color: 'gray',\n                                                                                        label: 'Other'\n                                                                                    };\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                    isChecked: selectedChannels.includes(channel.id),\n                                                                                                    onChange: ()=>toggleChannelSelection(channel.id)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 923,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 922,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                                                            as: typeConfig.icon,\n                                                                                                            color: \"\".concat(typeConfig.color, \".400\")\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 930,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                            children: channel.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 934,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 929,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 928,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                    colorScheme: typeConfig.color,\n                                                                                                    children: typeConfig.label\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 938,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 937,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                    color: \"gray.500\",\n                                                                                                    children: getParentName(channel.parent_id)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 941,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 940,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                    color: \"gray.500\",\n                                                                                                    children: channel.position\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 944,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 943,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Edit Channel\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Edit channel\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiEdit2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 951,\n                                                                                                                    columnNumber: 51\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"blue\",\n                                                                                                                onClick: ()=>handleChannelEdit(channel),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 949,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 948,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Delete Channel\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Delete channel\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 962,\n                                                                                                                    columnNumber: 51\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"red\",\n                                                                                                                onClick: ()=>handleChannelDelete(channel.id),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 960,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 959,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 947,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 946,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, channel.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 921,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 917,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 900,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 887,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"\\uD83C\\uDFA8 Theme Builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 988,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Create and customize your own themes with the advanced color builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 987,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Custom Theme Builder\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 997,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 996,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Create your own custom themes with full color control\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1001,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPalette, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1005,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"purple\",\n                                                                                    onClick: onColorBuilderOpen,\n                                                                                    size: \"lg\",\n                                                                                    children: \"Open Color Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1004,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1000,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 999,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Theme Presets\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1018,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1017,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 3,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    mb: 2,\n                                                                                    children: \"Quick theme options available in the navigation bar\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1022,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    wrap: \"wrap\",\n                                                                                    spacing: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"blue\",\n                                                                                            children: \"Dark\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1026,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"purple\",\n                                                                                            children: \"Midnight\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1027,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"green\",\n                                                                                            children: \"Forest\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1028,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"orange\",\n                                                                                            children: \"Sunset\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1029,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"pink\",\n                                                                                            children: \"Rose\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1030,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1025,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1021,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1020,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1016,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 986,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"\\uD83D\\uDEE0️ Builders & Tools\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1043,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Create custom content and manage server features with powerful builders\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1044,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Content Builders\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1052,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1051,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1057,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"green\",\n                                                                                    onClick: ()=>window.open('/admin/experimental/addon-builder', '_blank'),\n                                                                                    children: \"Addon Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1056,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1064,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"blue\",\n                                                                                    onClick: ()=>window.open('/admin/applications-builder', '_blank'),\n                                                                                    children: \"Applications Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1063,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1071,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"purple\",\n                                                                                    onClick: ()=>window.open('/admin/embed-builder', '_blank'),\n                                                                                    isDisabled: true,\n                                                                                    children: \"Message Builder (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1070,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1055,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1054,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1050,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Management Tools\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1084,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1083,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1089,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"orange\",\n                                                                                    onClick: ()=>window.open('/admin/addons', '_blank'),\n                                                                                    children: \"Manage Addons\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1088,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1096,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"teal\",\n                                                                                    onClick: ()=>window.open('/admin/commands', '_blank'),\n                                                                                    children: \"Command Manager\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1095,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1103,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"cyan\",\n                                                                                    onClick: ()=>window.open('/admin/applications', '_blank'),\n                                                                                    children: \"Application Manager\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1102,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1087,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1086,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1082,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1049,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 1041,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 1040,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"⚡ Automation & Activities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1120,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Set up automated features and server activities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1121,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1119,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Activity Templates\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1129,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1128,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                color: \"gray.500\",\n                                                                                fontSize: \"sm\",\n                                                                                mb: 4,\n                                                                                children: \"Pre-built activity templates to get you started quickly:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 1132,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                                spacing: 2,\n                                                                                align: \"stretch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Event Management System\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1136,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Welcome & Onboarding Flow\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1137,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Moderation Automation\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1138,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Custom Commands\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1139,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Auto-Role Assignment\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1140,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Scheduled Messages\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1141,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 1135,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1131,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1127,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Automation Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1148,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1147,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Configure automated server features\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1152,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1156,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"yellow\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Auto-Moderation (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1155,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1164,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"green\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Welcome System (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1163,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1172,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"blue\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Event Scheduler (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1171,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1151,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1150,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1146,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1126,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 1118,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 1117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 573,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1190,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateChannelDialog, {\n                        isOpen: isCreateChannelOpen,\n                        onClose: onCreateChannelClose,\n                        onSuccess: fetchChannels\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1191,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1190,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1198,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditChannelDialog, {\n                        isOpen: isEditChannelOpen,\n                        onClose: onEditChannelClose,\n                        channel: selectedChannel,\n                        categories: channels.filter((c)=>c.type === 4),\n                        onSuccess: fetchChannels\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1199,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1198,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1208,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateRoleDialog, {\n                        isOpen: isCreateRoleOpen,\n                        onClose: onCreateRoleClose,\n                        onSuccess: fetchGuildData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1209,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1208,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1216,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditRoleDialog, {\n                        isOpen: isEditRoleOpen,\n                        onClose: onEditRoleClose,\n                        role: selectedRole,\n                        onSuccess: fetchGuildData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1217,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1216,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1225,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColorBuilder, {\n                        isOpen: isColorBuilderOpen,\n                        onClose: onColorBuilderClose\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1226,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1225,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 572,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n        lineNumber: 571,\n        columnNumber: 5\n    }, this);\n}\n_s1(ServerManagement, \"u60QMy3QgXZh7dp9dQ5e+1DUtHw=\", false, function() {\n    return [\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        useRateLimit,\n        useRateLimit,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure\n    ];\n});\n_c5 = ServerManagement;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"CreateChannelDialog\");\n$RefreshReg$(_c1, \"EditChannelDialog\");\n$RefreshReg$(_c2, \"EditRoleDialog\");\n$RefreshReg$(_c3, \"ColorBuilder\");\n$RefreshReg$(_c4, \"CreateRoleDialog\");\n$RefreshReg$(_c5, \"ServerManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/admin/guilds.tsx\n"));

/***/ })

});