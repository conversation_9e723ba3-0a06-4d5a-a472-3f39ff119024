(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9640],{33233:(e,s,l)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/guilds",function(){return l(64374)}])},64374:(e,s,l)=>{"use strict";l.r(s),l.d(s,{__N_SSP:()=>Q,default:()=>ee});var i=l(94513),n=l(43700),r=l(79028),a=l(64349),t=l(5142),c=l(28365),o=l(1870),d=l(7476),h=l(31862),x=l(15975),j=l(1871),u=l(78813),p=l(29484),g=l(22184),b=l(59220),m=l(35339),E=l(30301),S=l(24941),y=l(84748),f=l(93493),v=l(91140),T=l(57688),N=l(21181),C=l(82824),A=l(56858),M=l(46312),w=l(84482),I=l(5130),_=l(10246),k=l(32338),F=l(35440),O=l(80456),z=l(3037),G=l(7836),R=l(84622);l(75632),l(82273);var P=l(97119),D=l(52826),L=l(94285),B=l(35044),K=l(77072),H=l.n(K);let W=H()(()=>Promise.all([l.e(2457),l.e(9784),l.e(6021),l.e(3786),l.e(1430),l.e(9498),l.e(2142),l.e(1283),l.e(4301),l.e(9114),l.e(7170),l.e(2432),l.e(1281),l.e(3920),l.e(3119),l.e(9176),l.e(1307),l.e(727),l.e(3976),l.e(2774),l.e(879),l.e(9984),l.e(2048),l.e(8883),l.e(5652),l.e(4754),l.e(523),l.e(7889),l.e(8360),l.e(8063),l.e(9284),l.e(5300),l.e(1349),l.e(7102),l.e(6835),l.e(246),l.e(8255),l.e(393),l.e(9450),l.e(3704),l.e(7897),l.e(4599),l.e(3640),l.e(4914),l.e(8637),l.e(4020),l.e(9),l.e(5388),l.e(8174)]).then(l.bind(l,98174)),{loadableGenerated:{webpack:()=>[98174]},loading:()=>(0,i.jsx)(y.y,{size:"md"}),ssr:!1}),J=H()(()=>Promise.all([l.e(2457),l.e(9784),l.e(6021),l.e(3786),l.e(1430),l.e(9498),l.e(2142),l.e(1283),l.e(4301),l.e(9114),l.e(7170),l.e(2432),l.e(1281),l.e(3920),l.e(3119),l.e(9176),l.e(1307),l.e(727),l.e(3976),l.e(2774),l.e(879),l.e(9984),l.e(2048),l.e(8883),l.e(5652),l.e(4754),l.e(523),l.e(7889),l.e(8360),l.e(8063),l.e(9284),l.e(5300),l.e(1349),l.e(7102),l.e(6835),l.e(246),l.e(8255),l.e(393),l.e(9450),l.e(3704),l.e(7897),l.e(4599),l.e(3640),l.e(4914),l.e(8637),l.e(4020),l.e(9),l.e(5388),l.e(9196)]).then(l.bind(l,39196)),{loadableGenerated:{webpack:()=>[39196]},loading:()=>(0,i.jsx)(y.y,{size:"md"}),ssr:!1}),V=H()(()=>Promise.all([l.e(2457),l.e(9784),l.e(6021),l.e(3786),l.e(1430),l.e(9498),l.e(2142),l.e(1283),l.e(4301),l.e(9114),l.e(7170),l.e(2432),l.e(1281),l.e(3920),l.e(3119),l.e(9176),l.e(1307),l.e(727),l.e(3976),l.e(2774),l.e(879),l.e(9984),l.e(2048),l.e(8883),l.e(5652),l.e(4754),l.e(523),l.e(7889),l.e(8360),l.e(8063),l.e(9284),l.e(5300),l.e(1349),l.e(7102),l.e(6835),l.e(246),l.e(8255),l.e(393),l.e(9450),l.e(3704),l.e(7897),l.e(4599),l.e(3640),l.e(4914),l.e(8637),l.e(4020),l.e(9),l.e(5388),l.e(4223),l.e(5872)]).then(l.bind(l,35872)),{loadableGenerated:{webpack:()=>[35872]},loading:()=>(0,i.jsx)(y.y,{size:"md"}),ssr:!1}),Y=H()(()=>Promise.all([l.e(2457),l.e(9784),l.e(6021),l.e(3786),l.e(1430),l.e(9498),l.e(2142),l.e(1283),l.e(4301),l.e(9114),l.e(7170),l.e(2432),l.e(1281),l.e(3920),l.e(3119),l.e(9176),l.e(1307),l.e(727),l.e(3976),l.e(2774),l.e(879),l.e(9984),l.e(2048),l.e(8883),l.e(5652),l.e(4754),l.e(523),l.e(7889),l.e(8360),l.e(8063),l.e(9284),l.e(5300),l.e(1349),l.e(7102),l.e(6835),l.e(246),l.e(8255),l.e(393),l.e(9450),l.e(3704),l.e(7897),l.e(4599),l.e(3640),l.e(4914),l.e(8637),l.e(4020),l.e(9),l.e(5388),l.e(4223),l.e(152)]).then(l.bind(l,10152)),{loadableGenerated:{webpack:()=>[10152]},loading:()=>(0,i.jsx)(y.y,{size:"md"}),ssr:!1});function U(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2e3,[s,l]=(0,L.useState)(!1),i=(0,L.useRef)(null),n=(0,L.useCallback)(()=>{i.current&&clearTimeout(i.current),l(!0),i.current=setTimeout(()=>{l(!1)},e)},[e]);return(0,L.useEffect)(()=>()=>{i.current&&clearTimeout(i.current)},[]),[s,n]}let X={0:{icon:B.FiMessageSquare,color:"blue",label:"Text"},2:{icon:B.FiVolume2,color:"green",label:"Voice"},4:{icon:B.FiFolderPlus,color:"purple",label:"Category"},5:{icon:B.FiRadio,color:"orange",label:"Announcement"},11:{icon:B.FiMessageCircle,color:"cyan",label:"Public Thread"},12:{icon:B.FiLock,color:"pink",label:"Private Thread"},13:{icon:B.FiHash,color:"teal",label:"Stage Voice"},15:{icon:B.FiHash,color:"gray",label:"Forum"}},$={ADMINISTRATOR:{color:"red",label:"Admin"},MANAGE_GUILD:{color:"orange",label:"Manage Server"},MANAGE_ROLES:{color:"yellow",label:"Manage Roles"},MANAGE_CHANNELS:{color:"green",label:"Manage Channels"},KICK_MEMBERS:{color:"purple",label:"Kick"},BAN_MEMBERS:{color:"pink",label:"Ban"},MANAGE_MESSAGES:{color:"blue",label:"Manage Messages"},MENTION_EVERYONE:{color:"cyan",label:"Mention @everyone"}},q={ADMINISTRATOR:1n<<3n,MANAGE_GUILD:1n<<5n,MANAGE_ROLES:1n<<28n,MANAGE_CHANNELS:1n<<4n,KICK_MEMBERS:1n<<1n,BAN_MEMBERS:1n<<2n,MANAGE_MESSAGES:1n<<13n,MENTION_EVERYONE:1n<<17n};function Z(e){if(!e)return[];if(Array.isArray(e))return e;try{let s=[],l=BigInt(e);for(let[e,i]of Object.entries(q))(l&i)===i&&s.push(e);return s}catch(e){return[]}}var Q=!0;function ee(){var e,s;let l=(0,G.d)(),{displayName:K}=(0,D.A)(),[H,q]=U(),[Q,ee]=U(5e3),[es,el]=(0,L.useState)({prefix:"!",botName:"Bot",guildName:"",guildIcon:null,activities:[{type:"PLAYING",name:"with Discord.js"}],activityRotationInterval:60}),[ei,en]=(0,L.useState)([]),[er,ea]=(0,L.useState)([]),[et,ec]=(0,L.useState)(!0),[eo,ed]=(0,L.useState)(!0),[eh,ex]=(0,L.useState)(!1),{isOpen:ej,onOpen:eu,onClose:ep}=(0,R.j)(),{isOpen:eg,onOpen:eb,onClose:em}=(0,R.j)(),{isOpen:eE,onOpen:eS,onClose:ey}=(0,R.j)(),{isOpen:ef,onOpen:ev,onClose:eT}=(0,R.j)(),[eN,eC]=(0,L.useState)(null),[eA,eM]=(0,L.useState)(null),[ew,eI]=(0,L.useState)(null),[e_,ek]=(0,L.useState)(null);(0,L.useRef)(null);let eF=async()=>{try{let[e,s]=await Promise.all([fetch("/api/discord/guild"),fetch("/api/discord/roles")]);if(e.ok){let s=await e.json();el(e=>({...e,guildName:s.name,guildIcon:s.icon}))}if(s.ok){let e=await s.json(),l=Array.isArray(e)?e:e.roles||[];en(l.sort((e,s)=>s.position-e.position))}}catch(e){l({title:"Error",description:"Failed to fetch guild data",status:"error",duration:3e3})}finally{ec(!1)}},eO=async()=>{try{let e=await fetch("/api/discord/channels");if(!e.ok)throw Error("Failed to fetch channels");let s=(await e.json()||[]).sort((e,s)=>4===e.type&&4!==s.type?-1:4!==e.type&&4===s.type?1:e.position-s.position);ea(s)}catch(e){l({title:"Error",description:"Failed to fetch channels",status:"error",duration:5e3})}finally{ed(!1)}};(0,L.useEffect)(()=>{eF(),eO()},[]);let ez=(e,s)=>{el(l=>({...l,[e]:s}))},eG=async()=>{if(!eh&&!H){ex(!0),q();try{if((await fetch("/api/discord/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(es)})).ok)l({title:"Success",description:"Settings saved successfully",status:"success",duration:3e3});else throw Error("Failed to save settings")}catch(e){l({title:"Error",description:"Failed to save settings",status:"error",duration:3e3})}finally{ex(!1)}}},eR=e=>{eC(e),ev()},eP=e=>{eM(e),eb()},eD=async e=>{if(!H)try{q(),(await fetch("/api/discord/channels/".concat(e),{method:"DELETE"})).ok&&(await eO(),l({title:"Success",description:"Channel deleted successfully",status:"success",duration:3e3}))}catch(e){l({title:"Error",description:"Failed to delete channel",status:"error",duration:3e3})}},eL=e=>{if(!e||!er)return"-";let s=er.find(s=>s.id===e);return s?s.name:"-"};return et?(0,i.jsx)(P.A,{children:(0,i.jsx)(d.m,{maxW:"container.xl",py:8,children:(0,i.jsxs)(z.T,{spacing:6,children:[(0,i.jsx)(S.E,{height:"60px"}),(0,i.jsx)(S.E,{height:"400px"})]})})}):(0,i.jsx)(P.A,{children:(0,i.jsxs)(d.m,{maxW:"container.xl",py:8,children:[(0,i.jsxs)(z.T,{spacing:8,align:"stretch",children:[(0,i.jsx)(r.a,{bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"blue.400",boxShadow:"0 0 15px rgba(66, 153, 225, 0.4)",children:(0,i.jsxs)(j.z,{justify:"space-between",align:"center",children:[(0,i.jsxs)(r.a,{children:[(0,i.jsxs)(u.D,{size:"xl",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",children:[(0,i.jsx)(p.I,{as:B.FiServer,mr:3}),"Server Management"]}),(0,i.jsxs)(I.E,{color:"gray.300",mt:2,children:["Comprehensive management for ",K||es.guildName]})]}),(0,i.jsx)(a.$,{leftIcon:(0,i.jsx)(B.FiSave,{}),colorScheme:"blue",onClick:eG,isLoading:eh,isDisabled:H,size:"lg",children:"Save Settings"})]})}),(0,i.jsxs)(A.t,{colorScheme:"blue",isLazy:!0,children:[(0,i.jsxs)(v.w,{children:[(0,i.jsxs)(f.o,{children:[(0,i.jsx)(p.I,{as:B.FiSettings,mr:2}),"Guild Settings"]}),(0,i.jsxs)(f.o,{children:[(0,i.jsx)(p.I,{as:B.FiUsers,mr:2}),"Roles (",ei.length,")"]}),(0,i.jsxs)(f.o,{children:[(0,i.jsx)(p.I,{as:B.FiHash,mr:2}),"Channels (",er.length,")"]})]}),(0,i.jsxs)(N.T,{children:[(0,i.jsx)(T.K,{children:(0,i.jsxs)(E.r,{columns:{base:1,lg:2},spacing:6,children:[(0,i.jsxs)(t.Z,{children:[(0,i.jsx)(o.a,{children:(0,i.jsx)(u.D,{size:"md",children:"Basic Settings"})}),(0,i.jsx)(c.b,{children:(0,i.jsxs)(z.T,{spacing:4,children:[(0,i.jsxs)(h.MJ,{children:[(0,i.jsx)(x.l,{children:"Bot Name"}),(0,i.jsx)(b.p,{value:es.botName,onChange:e=>ez("botName",e.target.value),placeholder:"Enter bot name"})]}),(0,i.jsxs)(h.MJ,{children:[(0,i.jsx)(x.l,{children:"Command Prefix"}),(0,i.jsx)(b.p,{value:es.prefix,onChange:e=>ez("prefix",e.target.value),placeholder:"Enter command prefix"})]})]})})]}),(0,i.jsxs)(t.Z,{children:[(0,i.jsx)(o.a,{children:(0,i.jsx)(u.D,{size:"md",children:"Bot Activity"})}),(0,i.jsx)(c.b,{children:(0,i.jsxs)(z.T,{spacing:4,children:[(0,i.jsxs)(h.MJ,{children:[(0,i.jsx)(x.l,{children:"Activity Type"}),(0,i.jsxs)(m.l,{value:(null==(e=es.activities[0])?void 0:e.type)||"PLAYING",onChange:e=>{let s=[...es.activities];s[0]={...s[0],type:e.target.value},ez("activities",s)},children:[(0,i.jsx)("option",{value:"PLAYING",children:"Playing"}),(0,i.jsx)("option",{value:"STREAMING",children:"Streaming"}),(0,i.jsx)("option",{value:"LISTENING",children:"Listening to"}),(0,i.jsx)("option",{value:"WATCHING",children:"Watching"}),(0,i.jsx)("option",{value:"COMPETING",children:"Competing in"})]})]}),(0,i.jsxs)(h.MJ,{children:[(0,i.jsx)(x.l,{children:"Activity Name"}),(0,i.jsx)(b.p,{value:(null==(s=es.activities[0])?void 0:s.name)||"",onChange:e=>{let s=[...es.activities];s[0]={...s[0],name:e.target.value},ez("activities",s)},placeholder:"Enter activity name"})]})]})})]})]})}),(0,i.jsx)(T.K,{children:(0,i.jsxs)(z.T,{spacing:6,align:"stretch",children:[(0,i.jsxs)(j.z,{justify:"space-between",children:[(0,i.jsx)(I.E,{fontSize:"lg",fontWeight:"bold",children:"Server Roles"}),(0,i.jsx)(a.$,{leftIcon:(0,i.jsx)(B.FiPlus,{}),colorScheme:"green",onClick:()=>{eS()},isDisabled:H,children:"Create Role"})]}),(0,i.jsx)(r.a,{bg:"gray.800",rounded:"xl",p:6,border:"1px",borderColor:"gray.700",children:(0,i.jsxs)(C.X,{variant:"simple",children:[(0,i.jsx)(k.d,{children:(0,i.jsxs)(O.Tr,{children:[(0,i.jsx)(_.Th,{children:"Role"}),(0,i.jsx)(_.Th,{children:"Members"}),(0,i.jsx)(_.Th,{children:"Permissions"}),(0,i.jsx)(_.Th,{children:"Actions"})]})}),(0,i.jsx)(M.N,{children:(ei||[]).map(e=>(0,i.jsxs)(O.Tr,{children:[(0,i.jsx)(w.Td,{children:(0,i.jsxs)(j.z,{children:[(0,i.jsx)(r.a,{w:4,h:4,rounded:"full",bg:e.color?"#".concat(e.color.toString(16).padStart(6,"0")):"gray.500"}),(0,i.jsx)(I.E,{color:"white",children:e.name})]})}),(0,i.jsx)(w.Td,{children:(0,i.jsx)(n.E,{colorScheme:"blue",children:"0"})}),(0,i.jsx)(w.Td,{children:(0,i.jsxs)(j.z,{wrap:"wrap",spacing:1,children:[(Z(e.permissions)||[]).slice(0,3).map(e=>{var s,l;return(0,i.jsx)(n.E,{colorScheme:(null==(s=$[e])?void 0:s.color)||"gray",size:"sm",children:(null==(l=$[e])?void 0:l.label)||e},e)}),Z(e.permissions).length>3&&(0,i.jsxs)(n.E,{colorScheme:"gray",size:"sm",children:["+",Z(e.permissions).length-3]})]})}),(0,i.jsx)(w.Td,{children:(0,i.jsxs)(j.z,{spacing:2,children:[(0,i.jsx)(F.m,{label:"Edit Role",children:(0,i.jsx)(g.K,{"aria-label":"Edit role",icon:(0,i.jsx)(B.FiEdit2,{}),size:"sm",variant:"ghost",colorScheme:"blue",onClick:()=>eR(e),isDisabled:H})}),(0,i.jsx)(F.m,{label:"Delete Role",children:(0,i.jsx)(g.K,{"aria-label":"Delete role",icon:(0,i.jsx)(B.FiTrash2,{}),size:"sm",variant:"ghost",colorScheme:"red",isDisabled:H})})]})})]},e.id))})]})})]})}),(0,i.jsx)(T.K,{children:(0,i.jsxs)(z.T,{spacing:6,align:"stretch",children:[(0,i.jsxs)(j.z,{justify:"space-between",children:[(0,i.jsx)(I.E,{fontSize:"lg",fontWeight:"bold",children:"Server Channels"}),(0,i.jsx)(a.$,{leftIcon:(0,i.jsx)(B.FiPlus,{}),colorScheme:"green",onClick:()=>{eu()},isDisabled:H,children:"Create Channel"})]}),(0,i.jsx)(r.a,{bg:"gray.800",rounded:"xl",p:6,border:"1px",borderColor:"gray.700",children:eo?(0,i.jsxs)(z.T,{spacing:4,children:[(0,i.jsx)(S.E,{height:"40px"}),(0,i.jsx)(S.E,{height:"40px"}),(0,i.jsx)(S.E,{height:"40px"})]}):(0,i.jsxs)(C.X,{variant:"simple",children:[(0,i.jsx)(k.d,{children:(0,i.jsxs)(O.Tr,{children:[(0,i.jsx)(_.Th,{children:"Name"}),(0,i.jsx)(_.Th,{children:"Type"}),(0,i.jsx)(_.Th,{children:"Category"}),(0,i.jsx)(_.Th,{children:"Position"}),(0,i.jsx)(_.Th,{children:"Actions"})]})}),(0,i.jsx)(M.N,{children:(er||[]).map(e=>{let s=X[e.type]||{icon:B.FiMessageSquare,color:"gray",label:"Other"};return(0,i.jsxs)(O.Tr,{children:[(0,i.jsx)(w.Td,{children:(0,i.jsxs)(j.z,{children:[(0,i.jsx)(p.I,{as:s.icon,color:"".concat(s.color,".400")}),(0,i.jsx)(I.E,{color:"white",children:e.name})]})}),(0,i.jsx)(w.Td,{children:(0,i.jsx)(n.E,{colorScheme:s.color,children:s.label})}),(0,i.jsx)(w.Td,{children:(0,i.jsx)(I.E,{color:"gray.300",children:eL(e.parent_id)})}),(0,i.jsx)(w.Td,{children:(0,i.jsx)(I.E,{color:"gray.300",children:e.position})}),(0,i.jsx)(w.Td,{children:(0,i.jsxs)(j.z,{spacing:2,children:[(0,i.jsx)(F.m,{label:"Edit Channel",children:(0,i.jsx)(g.K,{"aria-label":"Edit channel",icon:(0,i.jsx)(B.FiEdit2,{}),size:"sm",variant:"ghost",colorScheme:"blue",onClick:()=>eP(e),isDisabled:H})}),(0,i.jsx)(F.m,{label:"Delete Channel",children:(0,i.jsx)(g.K,{"aria-label":"Delete channel",icon:(0,i.jsx)(B.FiTrash2,{}),size:"sm",variant:"ghost",colorScheme:"red",onClick:()=>eD(e.id),isDisabled:H})})]})})]},e.id)})})]})})]})})]})]})]}),(0,i.jsx)(L.Suspense,{fallback:(0,i.jsx)(y.y,{}),children:(0,i.jsx)(W,{isOpen:ej,onClose:ep,onSuccess:eO})}),(0,i.jsx)(L.Suspense,{fallback:(0,i.jsx)(y.y,{}),children:(0,i.jsx)(J,{isOpen:eg,onClose:em,channel:eA,onSuccess:eO})}),(0,i.jsx)(L.Suspense,{fallback:(0,i.jsx)(y.y,{}),children:(0,i.jsx)(Y,{isOpen:eE,onClose:ey,onSuccess:eF})}),(0,i.jsx)(L.Suspense,{fallback:(0,i.jsx)(y.y,{}),children:(0,i.jsx)(V,{isOpen:ef,onClose:eT,role:eN,onSuccess:eF})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,636,7398,1203,8792],()=>s(33233)),_N_E=e.O()}]);