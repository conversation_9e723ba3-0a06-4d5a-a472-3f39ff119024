"use strict";exports.id=9820,exports.ids=[9820],exports.modules={12838:(e,s,n)=>{n.a(e,async(e,t)=>{try{n.d(s,{$m:()=>o.$,$n:()=>r.$,MJ:()=>i.MJ,OO:()=>m.OO,Q0:()=>m.Q0,Q7:()=>m.Q7,Sh:()=>m.Sh,Tk:()=>g.T,aF:()=>c.aF,cw:()=>d.c,dO:()=>C.d,dj:()=>y.d,jl:()=>x.j,l6:()=>u.l,lR:()=>a.l,lw:()=>m.lw,mH:()=>p.m,pd:()=>l.p,rQ:()=>j.r,s_:()=>h.s});var r=n(77502),i=n(23678),a=n(63957),l=n(15376),c=n(75460),d=n(42929),h=n(7394),o=n(89164),x=n(87346),j=n(95148),p=n(12725),m=n(71342),u=n(29742),C=n(24046),g=n(17335),y=n(5978),w=e([r,i,a,l,c,d,h,o,x,j,p,m,u,C,g,y]);[r,i,a,l,c,d,h,o,x,j,p,m,u,C,g,y]=w.then?(await w)():w,t()}catch(e){t(e)}})},29820:(e,s,n)=>{n.a(e,async(e,t)=>{try{n.r(s),n.d(s,{default:()=>c});var r=n(8732),i=n(12838),a=n(82015),l=e([i]);i=(l.then?(await l)():l)[0];let d={GUILD_TEXT:0,GUILD_VOICE:2,GUILD_CATEGORY:4};function c({isOpen:e,onClose:s,onSuccess:n,channel:t,categories:l}){let c=(0,i.dj)(),[h,o]=(0,a.useState)(!1),[x,j]=(0,a.useState)({name:"",type:0,topic:"",nsfw:!1,bitrate:64e3,userLimit:0,parent:"",rateLimitPerUser:0}),p=async()=>{o(!0);try{let e=await fetch(`/api/discord/channels/${t.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:x.name,topic:x.topic,nsfw:x.nsfw,bitrate:x.type===d.GUILD_VOICE?x.bitrate:void 0,user_limit:x.type===d.GUILD_VOICE?x.userLimit:void 0,parent_id:x.parent||null,rate_limit_per_user:x.type===d.GUILD_TEXT?x.rateLimitPerUser:void 0})});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to update channel")}c({title:"Success",description:"Channel updated successfully",status:"success",duration:3e3}),n(),s()}catch(e){c({title:"Error",description:e.message||"Failed to update channel",status:"error",duration:5e3})}finally{o(!1)}},m=(e,s)=>{j(n=>({...n,[e]:s}))};return(0,r.jsxs)(i.aF,{isOpen:e,onClose:s,size:"xl",children:[(0,r.jsx)(i.mH,{backdropFilter:"blur(10px)"}),(0,r.jsxs)(i.$m,{bg:"gray.800",children:[(0,r.jsx)(i.rQ,{children:"Edit Channel"}),(0,r.jsx)(i.s_,{}),(0,r.jsx)(i.cw,{children:(0,r.jsxs)(i.Tk,{spacing:4,children:[(0,r.jsxs)(i.MJ,{children:[(0,r.jsx)(i.lR,{children:"Channel Name"}),(0,r.jsx)(i.pd,{placeholder:"Enter channel name",value:x.name,onChange:e=>m("name",e.target.value)})]}),x.type===d.GUILD_TEXT&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i.MJ,{children:[(0,r.jsx)(i.lR,{children:"Channel Topic"}),(0,r.jsx)(i.pd,{placeholder:"Enter channel topic",value:x.topic,onChange:e=>m("topic",e.target.value)})]}),(0,r.jsxs)(i.MJ,{children:[(0,r.jsx)(i.lR,{children:"Slowmode (seconds)"}),(0,r.jsxs)(i.Q7,{min:0,max:21600,value:x.rateLimitPerUser,onChange:e=>m("rateLimitPerUser",parseInt(e)),children:[(0,r.jsx)(i.OO,{}),(0,r.jsxs)(i.lw,{children:[(0,r.jsx)(i.Q0,{}),(0,r.jsx)(i.Sh,{})]})]})]}),(0,r.jsxs)(i.MJ,{display:"flex",alignItems:"center",children:[(0,r.jsx)(i.lR,{htmlFor:"nsfw",mb:"0",children:"Age-Restricted (NSFW)"}),(0,r.jsx)(i.dO,{id:"nsfw",isChecked:x.nsfw,onChange:e=>m("nsfw",e.target.checked)})]})]}),x.type===d.GUILD_VOICE&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i.MJ,{children:[(0,r.jsx)(i.lR,{children:"Bitrate (kbps)"}),(0,r.jsxs)(i.Q7,{min:8,max:96,value:x.bitrate/1e3,onChange:e=>m("bitrate",1e3*parseInt(e)),children:[(0,r.jsx)(i.OO,{}),(0,r.jsxs)(i.lw,{children:[(0,r.jsx)(i.Q0,{}),(0,r.jsx)(i.Sh,{})]})]})]}),(0,r.jsxs)(i.MJ,{children:[(0,r.jsx)(i.lR,{children:"User Limit"}),(0,r.jsxs)(i.Q7,{min:0,max:99,value:x.userLimit,onChange:e=>m("userLimit",parseInt(e)),children:[(0,r.jsx)(i.OO,{}),(0,r.jsxs)(i.lw,{children:[(0,r.jsx)(i.Q0,{}),(0,r.jsx)(i.Sh,{})]})]})]})]}),x.type!==d.GUILD_CATEGORY&&(0,r.jsxs)(i.MJ,{children:[(0,r.jsx)(i.lR,{children:"Parent Category"}),(0,r.jsxs)(i.l6,{value:x.parent,onChange:e=>m("parent",e.target.value),children:[(0,r.jsx)("option",{value:"",children:"None"}),l.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]})}),(0,r.jsxs)(i.jl,{children:[(0,r.jsx)(i.$n,{variant:"ghost",mr:3,onClick:s,children:"Cancel"}),(0,r.jsx)(i.$n,{colorScheme:"blue",onClick:p,isLoading:h,children:"Save Changes"})]})]})]})}t()}catch(e){t(e)}})}};