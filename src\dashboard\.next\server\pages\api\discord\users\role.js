"use strict";(()=>{var e={};e.id=5115,e.ids=[5115],e.modules={224:e=>{e.exports=import("@discordjs/rest")},15806:e=>{e.exports=require("next-auth/next")},19638:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>l});var a=t(15806),i=t(94506),o=t(224),u=t(33915),n=t(20381),d=e([o,u]);async function l(e,r){if("POST"!==e.method)return r.status(405).json({error:"Method not allowed"});try{if(!await (0,a.getServerSession)(e,r,i.authOptions)){let t=e.headers.authorization;if(!t||!t.startsWith("Bearer "))return r.status(401).json({error:"Unauthorized"})}let{userId:t,roleId:s,action:d}=e.body;if(!t||!s||!d)return r.status(400).json({error:"Missing required fields"});let l=new o.REST({version:"10"}).setToken(n._.DISCORD_BOT_TOKEN),p=n._.DISCORD_GUILD_ID;if("add"===d)await l.put(u.Routes.guildMemberRole(p,t,s));else{if("remove"!==d)return r.status(400).json({error:"Invalid action"});await l.delete(u.Routes.guildMemberRole(p,t,s))}r.status(200).json({success:!0})}catch(e){r.status(500).json({error:"Failed to manage user role"})}}[o,u]=d.then?(await d)():d,s()}catch(e){s(e)}})},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},33915:e=>{e.exports=import("discord-api-types/v10")},39227:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>l,default:()=>d,routeModule:()=>p});var a=t(93433),i=t(20264),o=t(20584),u=t(19638),n=e([u]);u=(n.then?(await n)():n)[0];let d=(0,o.M)(u,"default"),l=(0,o.M)(u,"config"),p=new a.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/discord/users/role",pathname:"/api/discord/users/role",bundlePath:"",filename:""},userland:u});s()}catch(e){s(e)}})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(39227));module.exports=s})();