#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const UNUSED_ADMIN_PAGES = [
  'src/dashboard/pages/admin/activity-logs.tsx',
  'src/dashboard/pages/admin/database.tsx',
  'src/dashboard/pages/admin/embed-builder.tsx',
  'src/dashboard/pages/admin/errors.tsx',
  'src/dashboard/pages/admin/theme-settings.tsx',
  'src/dashboard/pages/admin/users.tsx',
];

const UNUSED_API_ENDPOINTS = [
  // All previously unused API endpoints have been permanently removed or consolidated
];

const UNUSED_COMPONENTS = [
  // All previously unused components have been permanently removed
];

console.log('🚀 Starting optimized build process...');

// Step 1: Clean previous builds
console.log('🧹 Cleaning previous builds...');
try {
  ['dist', 'src/dashboard/.next', 'src/dashboard/out'].forEach((p) => {
    if (fs.existsSync(p)) {
      fs.rmSync(p, { recursive: true, force: true });
    }
  });
  console.log('✅ Cleaned previous builds');
} catch (error) {
  console.log('⚠️  Warning: Could not clean all previous builds');
}

// Step 2: Temporarily move unused files to reduce bundle size
console.log('📦 Temporarily removing unused files...');
const backupDir = '.build-backup';
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir, { recursive: true });
}

const movedFiles = [];
[...UNUSED_ADMIN_PAGES, ...UNUSED_COMPONENTS, ...UNUSED_API_ENDPOINTS].forEach(filePath => {
  if (fs.existsSync(filePath)) {
    const backupPath = path.join(backupDir, path.basename(filePath));
    try {
      fs.renameSync(filePath, backupPath);
      movedFiles.push({ original: filePath, backup: backupPath });
      console.log(`  Moved: ${filePath}`);
    } catch (error) {
      console.log(`  Warning: Could not move ${filePath}`);
    }
  }
});

// Step 3: Build TypeScript
console.log('🔨 Building TypeScript...');
try {
  execSync('npx tsc --project tsconfig.json', { stdio: 'inherit' });
  console.log('✅ TypeScript build completed');
} catch (error) {
  console.error('❌ TypeScript build failed');
  // Restore files before exiting
  movedFiles.forEach(({ original, backup }) => {
    try {
      fs.renameSync(backup, original);
    } catch (error) {
      console.log(`  Warning: Could not restore ${original}`);
    }
  });
  process.exit(1);
}

// Step 4: Copy necessary files (improved logic)
console.log('📋 Copying necessary files...');
try {
  // Create dist directory if it doesn't exist
  if (!fs.existsSync('dist')) {
    fs.mkdirSync('dist', { recursive: true });
  }
  
  // Copy config files and assets with better filtering
  const copyCommand = `Get-ChildItem -Path src -Recurse -Include config.yml,*.html,*.css,*.png,*.jpg,*.svg | Where-Object { $_.FullName -notlike '*node_modules*' -and $_.FullName -notlike '*.next*' -and $_.FullName -notlike '*out*' } | ForEach-Object { $relativePath = $_.FullName.Substring((Get-Location).Path.Length + 5); $destPath = Join-Path 'dist' $relativePath; $destDir = Split-Path $destPath -Parent; if (-not (Test-Path $destDir)) { New-Item -ItemType Directory -Path $destDir -Force | Out-Null }; Copy-Item $_.FullName -Destination $destPath -Force }`;
  
  execSync(`powershell -Command "${copyCommand}"`, { stdio: 'inherit' });
  console.log('✅ Files copied');
} catch (error) {
  console.log('⚠️  Warning: Some files may not have been copied');
}

// Step 5: Build dashboard with optimizations
console.log('🎨 Building dashboard...');
try {
  process.chdir('src/dashboard');
  
  // Set production environment variables for maximum optimization
  process.env.NODE_ENV = 'production';
  process.env.NEXT_TELEMETRY_DISABLED = '1';
  process.env.ANALYZE = 'false';
  
  // Use npx to run next build
  execSync('npx next build', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_OPTIONS: '--max-old-space-size=4096'
    }
  });
  
  console.log('✅ Dashboard build completed');
} catch (error) {
  console.error('❌ Dashboard build failed');
  process.chdir('../../');
  // Restore files before exiting
  movedFiles.forEach(({ original, backup }) => {
    try {
      fs.renameSync(backup, original);
    } catch (error) {
      console.log(`  Warning: Could not restore ${original}`);
    }
  });
  process.exit(1);
} finally {
  process.chdir('../../');
}

// Step 6: Restore moved files
console.log('🔄 Restoring moved files...');
movedFiles.forEach(({ original, backup }) => {
  try {
    fs.renameSync(backup, original);
    console.log(`  Restored: ${original}`);
  } catch (error) {
    console.log(`  Warning: Could not restore ${original}`);
  }
});

// Clean up backup directory
try {
  fs.rmSync(backupDir, { recursive: true, force: true });
} catch (error) {
  console.log('⚠️  Warning: Could not clean backup directory');
}

// Step 7: Analyze bundle size
console.log('📊 Analyzing bundle size...');
try {
  const dashboardBuildPath = 'src/dashboard/.next';
  if (fs.existsSync(dashboardBuildPath)) {
    try {
      const buildInfo = execSync(`powershell -Command "(Get-ChildItem -Path '${dashboardBuildPath}' -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB"`, { encoding: 'utf8' }).trim();
      console.log(`Dashboard build size: ${Math.round(parseFloat(buildInfo) * 100) / 100} MB`);
    } catch (error) {
      console.log('Dashboard build exists but could not measure size');
    }
  }
  
  const distPath = 'dist';
  if (fs.existsSync(distPath)) {
    try {
      const distInfo = execSync(`powershell -Command "(Get-ChildItem -Path '${distPath}' -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB"`, { encoding: 'utf8' }).trim();
      console.log(`Bot dist size: ${Math.round(parseFloat(distInfo) * 100) / 100} MB`);
    } catch (error) {
      console.log('Dist folder exists but could not measure size');
    }
  }
} catch (error) {
  console.log('⚠️  Could not analyze bundle size');
}

console.log('🎉 Optimized build completed successfully!');
console.log('');
console.log('📈 Optimizations applied:');
console.log('  • Dynamic imports for heavy components');
console.log('  • Temporarily removed unused admin pages');
console.log('  • Aggressive webpack splitting');
console.log('  • Production optimizations enabled');
console.log('  • Tree shaking optimized');
console.log('  • Bundle size analysis');
console.log('');
console.log('💡 To further reduce size:');
console.log('  • Consider removing more experimental features');
console.log('  • Implement lazy loading for admin pages');
console.log('  • Use lighter alternatives for heavy libraries');
console.log('  • Remove unused dependencies from package.json');
