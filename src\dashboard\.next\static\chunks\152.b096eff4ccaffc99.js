"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[152],{10152:(e,s,i)=>{i.r(s),i.d(s,{default:()=>c});var n=i(94513),r=i(13279),o=i(94285),l=i(43557);let t={General:{icon:l.pcC,permissions:["ADMINISTRATOR","VIEW_AUDIT_LOG","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","MANAGE_EMOJIS_AND_STICKERS","MANAGE_WEBHOOKS","VIEW_CHANNEL"]},Text:{icon:l.mEP,permissions:["SEND_MESSAGES","EMBED_LINKS","ATTACH_FILES","ADD_REACTIONS","USE_EXTERNAL_EMOJIS","MENTION_EVERYONE","<PERSON><PERSON><PERSON>_MESSAGES","READ_MESSAGE_HISTORY"]},Voice:{icon:l.o77,permissions:["CONNECT","SPEAK","STREAM","USE_VAD","PRIORITY_SPEAKER","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS"]},Members:{icon:l.cfS,permissions:["KICK_MEMBERS","BAN_MEMBERS","CHANGE_NICKNAME","MANAGE_NICKNAMES","CREATE_INSTANT_INVITE"]}};function c(e){let{isOpen:s,onClose:i,onSuccess:l}=e,c=(0,r.dj)(),[a,E]=(0,o.useState)(!1),[d,h]=(0,o.useState)({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1}),A=async()=>{if(!d.name.trim())return void c({title:"Error",description:"Role name is required",status:"error",duration:3e3});E(!0);try{let e=await fetch("/api/discord/roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to create role")}c({title:"Success",description:"Role created successfully",status:"success",duration:3e3}),h({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1}),l(),i()}catch(e){c({title:"Error",description:e.message||"Failed to create role",status:"error",duration:5e3})}finally{E(!1)}},S=e=>{h(s=>({...s,permissions:s.permissions.includes(e)?s.permissions.filter(s=>s!==e):[...s.permissions,e]}))},m=(e,s)=>{h(i=>({...i,[e]:s}))};return(0,n.jsxs)(r.aF,{isOpen:s,onClose:i,size:"xl",scrollBehavior:"inside",children:[(0,n.jsx)(r.mH,{backdropFilter:"blur(10px)"}),(0,n.jsxs)(r.$m,{bg:"gray.800",children:[(0,n.jsx)(r.rQ,{children:"Create New Role"}),(0,n.jsx)(r.s_,{}),(0,n.jsx)(r.cw,{children:(0,n.jsxs)(r.Tk,{spacing:6,children:[(0,n.jsxs)(r.MJ,{isRequired:!0,children:[(0,n.jsx)(r.lR,{children:"Role Name"}),(0,n.jsx)(r.pd,{placeholder:"Enter role name",value:d.name,onChange:e=>m("name",e.target.value)})]}),(0,n.jsxs)(r.MJ,{children:[(0,n.jsx)(r.lR,{children:"Role Color"}),(0,n.jsx)(r.pd,{type:"color",value:d.color,onChange:e=>m("color",e.target.value)})]}),(0,n.jsx)(r.MJ,{children:(0,n.jsxs)(r.zt,{spacing:4,children:[(0,n.jsx)(r.Sc,{isChecked:d.hoist,onChange:e=>m("hoist",e.target.checked),children:"Display role separately"}),(0,n.jsx)(r.Sc,{isChecked:d.mentionable,onChange:e=>m("mentionable",e.target.checked),children:"Allow anyone to @mention"})]})}),(0,n.jsx)(r.cG,{}),(0,n.jsx)(r.EY,{fontSize:"lg",fontWeight:"bold",alignSelf:"flex-start",children:"Permissions"}),Object.entries(t).map(e=>{let[s,i]=e;return(0,n.jsxs)(r.az,{w:"full",children:[(0,n.jsxs)(r.zt,{mb:2,children:[(0,n.jsx)(r.In,{as:i.icon}),(0,n.jsx)(r.EY,{fontWeight:"semibold",children:s})]}),(0,n.jsx)(r.rS,{columns:2,spacing:2,children:i.permissions.map(e=>(0,n.jsx)(r.Sc,{isChecked:d.permissions.includes(e),onChange:()=>S(e),children:e.split("_").map(e=>e.charAt(0)+e.slice(1).toLowerCase()).join(" ")},e))})]},s)})]})}),(0,n.jsxs)(r.jl,{children:[(0,n.jsx)(r.$n,{variant:"ghost",mr:3,onClick:i,children:"Cancel"}),(0,n.jsx)(r.$n,{colorScheme:"blue",onClick:A,isLoading:a,children:"Create Role"})]})]})]})}}}]);