"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6733],{36733:(e,t,o)=>{o.r(t),o.d(t,{default:()=>l});var a=o(94513);o(94285);var r=o(79028),n=o(75533),s=o(17600);let i="file:///config.yaml";n.wG.config({paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}});let l=e=>{let{value:t,onChange:o,height:l="60vh"}=e;return(0,a.jsx)(r.a,{borderWidth:"1px",borderColor:"purple.600",borderRadius:"md",overflow:"hidden",height:l,children:(0,a.jsx)(n.Ay,{height:"100%",language:"yaml",theme:"vs-dark",path:i,value:t,onChange:o,onMount:(e,t)=>{self.MonacoEnvironment={getWorker:function(e,t){return new Worker("https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/base/worker/workerMain.js",{type:"module"})}},t.languages.typescript.javascriptDefaults.setEagerModelSync(!0),(0,s.f)(t,{enableSchemaRequest:!0,hover:!0,completion:!0,validate:!0,format:!0,schemas:[{uri:"http://json.schemastore.org/github-workflow",fileMatch:[i]}]})},options:{minimap:{enabled:!1},tabSize:2,insertSpaces:!0,wordWrap:"on",automaticLayout:!0,padding:{top:10,bottom:10}}})})}}}]);