"use strict";exports.id=9498,exports.ids=[9498],exports.modules={30278:(r,e,t)=>{t.d(e,{_u:()=>rS,YU:()=>rs,AH:()=>rn,Vg:()=>ry,lL:()=>rR,H2:()=>rd,Dt:()=>rl,f4:()=>rc,HU:()=>rt,GF:()=>K,MN:()=>rg,q8:()=>rr,uB:()=>rp,gd:()=>rx});var a=t(13910);let o={open:(r,e)=>`${r}[data-open], ${r}[open], ${r}[data-state=open] ${e}`,closed:(r,e)=>`${r}[data-closed], ${r}[data-state=closed] ${e}`,hover:(r,e)=>`${r}:hover ${e}, ${r}[data-hover] ${e}`,focus:(r,e)=>`${r}:focus ${e}, ${r}[data-focus] ${e}`,focusVisible:(r,e)=>`${r}:focus-visible ${e}`,focusWithin:(r,e)=>`${r}:focus-within ${e}`,active:(r,e)=>`${r}:active ${e}, ${r}[data-active] ${e}`,disabled:(r,e)=>`${r}:disabled ${e}, ${r}[data-disabled] ${e}`,invalid:(r,e)=>`${r}:invalid ${e}, ${r}[data-invalid] ${e}`,checked:(r,e)=>`${r}:checked ${e}, ${r}[data-checked] ${e}`,placeholderShown:(r,e)=>`${r}:placeholder-shown ${e}`},i=r=>d(e=>r(e,"&"),"[role=group]","[data-group]",".group"),n=r=>d(e=>r(e,"~ &"),"[data-peer]",".peer"),d=(r,...e)=>e.map(r).join(", "),l={_hover:"&:hover, &[data-hover]",_active:"&:active, &[data-active]",_focus:"&:focus, &[data-focus]",_highlighted:"&[data-highlighted]",_focusWithin:"&:focus-within, &[data-focus-within]",_focusVisible:"&:focus-visible, &[data-focus-visible]",_disabled:"&:disabled, &[disabled], &[aria-disabled=true], &[data-disabled]",_readOnly:"&[aria-readonly=true], &[readonly], &[data-readonly]",_before:"&::before",_after:"&::after",_empty:"&:empty, &[data-empty]",_expanded:"&[aria-expanded=true], &[data-expanded], &[data-state=expanded]",_checked:"&[aria-checked=true], &[data-checked], &[data-state=checked]",_grabbed:"&[aria-grabbed=true], &[data-grabbed]",_pressed:"&[aria-pressed=true], &[data-pressed]",_invalid:"&[aria-invalid=true], &[data-invalid]",_valid:"&[data-valid], &[data-state=valid]",_loading:"&[data-loading], &[aria-busy=true]",_selected:"&[aria-selected=true], &[data-selected]",_hidden:"&[hidden], &[data-hidden]",_autofill:"&:-webkit-autofill",_even:"&:nth-of-type(even)",_odd:"&:nth-of-type(odd)",_first:"&:first-of-type",_firstLetter:"&::first-letter",_last:"&:last-of-type",_notFirst:"&:not(:first-of-type)",_notLast:"&:not(:last-of-type)",_visited:"&:visited",_activeLink:"&[aria-current=page]",_activeStep:"&[aria-current=step]",_indeterminate:"&:indeterminate, &[aria-checked=mixed], &[data-indeterminate], &[data-state=indeterminate]",_groupOpen:i(o.open),_groupClosed:i(o.closed),_groupHover:i(o.hover),_peerHover:n(o.hover),_groupFocus:i(o.focus),_peerFocus:n(o.focus),_groupFocusVisible:i(o.focusVisible),_peerFocusVisible:n(o.focusVisible),_groupActive:i(o.active),_peerActive:n(o.active),_groupDisabled:i(o.disabled),_peerDisabled:n(o.disabled),_groupInvalid:i(o.invalid),_peerInvalid:n(o.invalid),_groupChecked:i(o.checked),_peerChecked:n(o.checked),_groupFocusWithin:i(o.focusWithin),_peerFocusWithin:n(o.focusWithin),_peerPlaceholderShown:n(o.placeholderShown),_placeholder:"&::placeholder, &[data-placeholder]",_placeholderShown:"&:placeholder-shown, &[data-placeholder-shown]",_fullScreen:"&:fullscreen, &[data-fullscreen]",_selection:"&::selection",_rtl:"[dir=rtl] &, &[dir=rtl]",_ltr:"[dir=ltr] &, &[dir=ltr]",_mediaDark:"@media (prefers-color-scheme: dark)",_mediaReduceMotion:"@media (prefers-reduced-motion: reduce)",_dark:".chakra-ui-dark &:not([data-theme]),[data-theme=dark] &:not([data-theme]),&[data-theme=dark]",_light:".chakra-ui-light &:not([data-theme]),[data-theme=light] &:not([data-theme]),&[data-theme=light]",_horizontal:"&[data-orientation=horizontal]",_vertical:"&[data-orientation=vertical]",_open:"&[data-open], &[open], &[data-state=open]",_closed:"&[data-closed], &[data-state=closed]",_complete:"&[data-complete]",_incomplete:"&[data-incomplete]",_current:"&[data-current]"},s=Object.keys(l),c=r=>/!(important)?$/.test(r),p=r=>"string"==typeof r?r.replace(/!(important)?$/,"").trim():r,g=(r,e)=>t=>{let o=String(e),i=c(o),n=p(o),d=r?`${r}.${n}`:n,l=(0,a.Gv)(t.__cssMap)&&d in t.__cssMap?t.__cssMap[d].varRef:e;return l=p(l),i?`${l} !important`:l};function h(r){let{scale:e,transform:t,compose:a}=r;return(r,o)=>{let i=g(e,r)(o),n=t?.(i,o)??i;return a&&(n=a(n,o)),n}}let u=(...r)=>e=>r.reduce((r,e)=>e(r),e);function b(r,e){return t=>{let a={property:t,scale:r};return a.transform=h({scale:r,transform:e}),a}}let f=({rtl:r,ltr:e})=>t=>"rtl"===t.direction?r:e,m=["rotate(var(--chakra-rotate, 0))","scaleX(var(--chakra-scale-x, 1))","scaleY(var(--chakra-scale-y, 1))","skewX(var(--chakra-skew-x, 0))","skewY(var(--chakra-skew-y, 0))"],k={"--chakra-blur":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-brightness":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-contrast":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-grayscale":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-hue-rotate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-invert":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-saturate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-sepia":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-drop-shadow":"var(--chakra-empty,/*!*/ /*!*/)",filter:"var(--chakra-blur) var(--chakra-brightness) var(--chakra-contrast) var(--chakra-grayscale) var(--chakra-hue-rotate) var(--chakra-invert) var(--chakra-saturate) var(--chakra-sepia) var(--chakra-drop-shadow)"},v={backdropFilter:"var(--chakra-backdrop-blur) var(--chakra-backdrop-brightness) var(--chakra-backdrop-contrast) var(--chakra-backdrop-grayscale) var(--chakra-backdrop-hue-rotate) var(--chakra-backdrop-invert) var(--chakra-backdrop-opacity) var(--chakra-backdrop-saturate) var(--chakra-backdrop-sepia)","--chakra-backdrop-blur":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-brightness":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-contrast":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-grayscale":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-hue-rotate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-invert":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-opacity":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-saturate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-sepia":"var(--chakra-empty,/*!*/ /*!*/)"},S={"row-reverse":{space:"--chakra-space-x-reverse",divide:"--chakra-divide-x-reverse"},"column-reverse":{space:"--chakra-space-y-reverse",divide:"--chakra-divide-y-reverse"}},y={"to-t":"to top","to-tr":"to top right","to-r":"to right","to-br":"to bottom right","to-b":"to bottom","to-bl":"to bottom left","to-l":"to left","to-tl":"to top left"},R=new Set(Object.values(y)),T=new Set(["none","-moz-initial","inherit","initial","revert","unset"]),B=r=>r.trim(),x=r=>"string"==typeof r&&r.includes("(")&&r.includes(")"),_=r=>{let e=parseFloat(r.toString()),t=r.toString().replace(String(e),"");return{unitless:!t,value:e,unit:t}},w=r=>e=>`${r}(${e})`,I={filter:r=>"auto"!==r?r:k,backdropFilter:r=>"auto"!==r?r:v,ring:r=>({"--chakra-ring-offset-shadow":"var(--chakra-ring-inset) 0 0 0 var(--chakra-ring-offset-width) var(--chakra-ring-offset-color)","--chakra-ring-shadow":"var(--chakra-ring-inset) 0 0 0 calc(var(--chakra-ring-width) + var(--chakra-ring-offset-width)) var(--chakra-ring-color)","--chakra-ring-width":I.px(r),boxShadow:"var(--chakra-ring-offset-shadow), var(--chakra-ring-shadow), var(--chakra-shadow, 0 0 #0000)"}),bgClip:r=>"text"===r?{color:"transparent",backgroundClip:"text"}:{backgroundClip:r},transform:r=>"auto"===r?["translateX(var(--chakra-translate-x, 0))","translateY(var(--chakra-translate-y, 0))",...m].join(" "):"auto-gpu"===r?["translate3d(var(--chakra-translate-x, 0), var(--chakra-translate-y, 0), 0)",...m].join(" "):r,vh:r=>"$100vh"===r?"var(--chakra-vh)":r,px(r){if(null==r)return r;let{unitless:e}=_(r);return e||"number"==typeof r?`${r}px`:r},fraction:r=>"number"!=typeof r||r>1?r:`${100*r}%`,float:(r,e)=>"rtl"===e.direction?({left:"right",right:"left"})[r]:r,degree(r){if(/^var\(--.+\)$/.test(r)||null==r)return r;let e="string"==typeof r&&!r.endsWith("deg");return"number"==typeof r||e?`${r}deg`:r},gradient:(r,e)=>(function(r,e){if(null==r||T.has(r))return r;if(!(x(r)||T.has(r)))return`url('${r}')`;let t=/(^[a-z-A-Z]+)\((.*)\)/g.exec(r),a=t?.[1],o=t?.[2];if(!a||!o)return r;let i=a.includes("-gradient")?a:`${a}-gradient`,[n,...d]=o.split(",").map(B).filter(Boolean);if(d?.length===0)return r;let l=n in y?y[n]:n;d.unshift(l);let s=d.map(r=>{if(R.has(r))return r;let t=r.indexOf(" "),[a,o]=-1!==t?[r.substr(0,t),r.substr(t+1)]:[r],i=x(o)?o:o&&o.split(" "),n=`colors.${a}`,d=n in e.__cssMap?e.__cssMap[n].varRef:a;return i?[d,...Array.isArray(i)?i:[i]].join(" "):d});return`${i}(${s.join(", ")})`})(r,e??{}),blur:w("blur"),opacity:w("opacity"),brightness:w("brightness"),contrast:w("contrast"),dropShadow:w("drop-shadow"),grayscale:w("grayscale"),hueRotate:r=>w("hue-rotate")(I.degree(r)),invert:w("invert"),saturate:w("saturate"),sepia:w("sepia"),bgImage:r=>null==r||x(r)||T.has(r)?r:`url(${r})`,outline(r){let e="0"===String(r)||"none"===String(r);return null!==r&&e?{outline:"2px solid transparent",outlineOffset:"2px"}:{outline:r}},flexDirection(r){let{space:e,divide:t}=S[r]??{},a={flexDirection:r};return e&&(a[e]=1),t&&(a[t]=1),a}},$={borderWidths:b("borderWidths"),borderStyles:b("borderStyles"),colors:b("colors"),borders:b("borders"),gradients:b("gradients",I.gradient),radii:b("radii",I.px),space:b("space",u(I.vh,I.px)),spaceT:b("space",u(I.vh,I.px)),degreeT:r=>({property:r,transform:I.degree}),prop:(r,e,t)=>({property:r,scale:e,...e&&{transform:h({scale:e,transform:t})}}),propT:(r,e)=>({property:r,transform:e}),sizes:b("sizes",u(I.vh,I.px)),sizesT:b("sizes",u(I.vh,I.fraction)),shadows:b("shadows"),logical:function(r){let{property:e,scale:t,transform:a}=r;return{scale:t,property:f(e),transform:t?h({scale:t,compose:a}):a}},blur:b("blur",I.blur)},E={background:$.colors("background"),backgroundColor:$.colors("backgroundColor"),backgroundImage:$.gradients("backgroundImage"),backgroundSize:!0,backgroundPosition:!0,backgroundRepeat:!0,backgroundAttachment:!0,backgroundClip:{transform:I.bgClip},bgSize:$.prop("backgroundSize"),bgPosition:$.prop("backgroundPosition"),bg:$.colors("background"),bgColor:$.colors("backgroundColor"),bgPos:$.prop("backgroundPosition"),bgRepeat:$.prop("backgroundRepeat"),bgAttachment:$.prop("backgroundAttachment"),bgGradient:$.gradients("backgroundImage"),bgClip:{transform:I.bgClip}};Object.assign(E,{bgImage:E.backgroundImage,bgImg:E.backgroundImage});let W={border:$.borders("border"),borderWidth:$.borderWidths("borderWidth"),borderStyle:$.borderStyles("borderStyle"),borderColor:$.colors("borderColor"),borderRadius:$.radii("borderRadius"),borderTop:$.borders("borderTop"),borderBlockStart:$.borders("borderBlockStart"),borderTopLeftRadius:$.radii("borderTopLeftRadius"),borderStartStartRadius:$.logical({scale:"radii",property:{ltr:"borderTopLeftRadius",rtl:"borderTopRightRadius"}}),borderEndStartRadius:$.logical({scale:"radii",property:{ltr:"borderBottomLeftRadius",rtl:"borderBottomRightRadius"}}),borderTopRightRadius:$.radii("borderTopRightRadius"),borderStartEndRadius:$.logical({scale:"radii",property:{ltr:"borderTopRightRadius",rtl:"borderTopLeftRadius"}}),borderEndEndRadius:$.logical({scale:"radii",property:{ltr:"borderBottomRightRadius",rtl:"borderBottomLeftRadius"}}),borderRight:$.borders("borderRight"),borderInlineEnd:$.borders("borderInlineEnd"),borderBottom:$.borders("borderBottom"),borderBlockEnd:$.borders("borderBlockEnd"),borderBottomLeftRadius:$.radii("borderBottomLeftRadius"),borderBottomRightRadius:$.radii("borderBottomRightRadius"),borderLeft:$.borders("borderLeft"),borderInlineStart:{property:"borderInlineStart",scale:"borders"},borderInlineStartRadius:$.logical({scale:"radii",property:{ltr:["borderTopLeftRadius","borderBottomLeftRadius"],rtl:["borderTopRightRadius","borderBottomRightRadius"]}}),borderInlineEndRadius:$.logical({scale:"radii",property:{ltr:["borderTopRightRadius","borderBottomRightRadius"],rtl:["borderTopLeftRadius","borderBottomLeftRadius"]}}),borderX:$.borders(["borderLeft","borderRight"]),borderInline:$.borders("borderInline"),borderY:$.borders(["borderTop","borderBottom"]),borderBlock:$.borders("borderBlock"),borderTopWidth:$.borderWidths("borderTopWidth"),borderBlockStartWidth:$.borderWidths("borderBlockStartWidth"),borderTopColor:$.colors("borderTopColor"),borderBlockStartColor:$.colors("borderBlockStartColor"),borderTopStyle:$.borderStyles("borderTopStyle"),borderBlockStartStyle:$.borderStyles("borderBlockStartStyle"),borderBottomWidth:$.borderWidths("borderBottomWidth"),borderBlockEndWidth:$.borderWidths("borderBlockEndWidth"),borderBottomColor:$.colors("borderBottomColor"),borderBlockEndColor:$.colors("borderBlockEndColor"),borderBottomStyle:$.borderStyles("borderBottomStyle"),borderBlockEndStyle:$.borderStyles("borderBlockEndStyle"),borderLeftWidth:$.borderWidths("borderLeftWidth"),borderInlineStartWidth:$.borderWidths("borderInlineStartWidth"),borderLeftColor:$.colors("borderLeftColor"),borderInlineStartColor:$.colors("borderInlineStartColor"),borderLeftStyle:$.borderStyles("borderLeftStyle"),borderInlineStartStyle:$.borderStyles("borderInlineStartStyle"),borderRightWidth:$.borderWidths("borderRightWidth"),borderInlineEndWidth:$.borderWidths("borderInlineEndWidth"),borderRightColor:$.colors("borderRightColor"),borderInlineEndColor:$.colors("borderInlineEndColor"),borderRightStyle:$.borderStyles("borderRightStyle"),borderInlineEndStyle:$.borderStyles("borderInlineEndStyle"),borderTopRadius:$.radii(["borderTopLeftRadius","borderTopRightRadius"]),borderBottomRadius:$.radii(["borderBottomLeftRadius","borderBottomRightRadius"]),borderLeftRadius:$.radii(["borderTopLeftRadius","borderBottomLeftRadius"]),borderRightRadius:$.radii(["borderTopRightRadius","borderBottomRightRadius"])};Object.assign(W,{rounded:W.borderRadius,roundedTop:W.borderTopRadius,roundedTopLeft:W.borderTopLeftRadius,roundedTopRight:W.borderTopRightRadius,roundedTopStart:W.borderStartStartRadius,roundedTopEnd:W.borderStartEndRadius,roundedBottom:W.borderBottomRadius,roundedBottomLeft:W.borderBottomLeftRadius,roundedBottomRight:W.borderBottomRightRadius,roundedBottomStart:W.borderEndStartRadius,roundedBottomEnd:W.borderEndEndRadius,roundedLeft:W.borderLeftRadius,roundedRight:W.borderRightRadius,roundedStart:W.borderInlineStartRadius,roundedEnd:W.borderInlineEndRadius,borderStart:W.borderInlineStart,borderEnd:W.borderInlineEnd,borderTopStartRadius:W.borderStartStartRadius,borderTopEndRadius:W.borderStartEndRadius,borderBottomStartRadius:W.borderEndStartRadius,borderBottomEndRadius:W.borderEndEndRadius,borderStartRadius:W.borderInlineStartRadius,borderEndRadius:W.borderInlineEndRadius,borderStartWidth:W.borderInlineStartWidth,borderEndWidth:W.borderInlineEndWidth,borderStartColor:W.borderInlineStartColor,borderEndColor:W.borderInlineEndColor,borderStartStyle:W.borderInlineStartStyle,borderEndStyle:W.borderInlineEndStyle});let C={color:$.colors("color"),textColor:$.colors("color"),fill:$.colors("fill"),stroke:$.colors("stroke"),accentColor:$.colors("accentColor"),textFillColor:$.colors("textFillColor")},z={alignItems:!0,alignContent:!0,justifyItems:!0,justifyContent:!0,flexWrap:!0,flexDirection:{transform:I.flexDirection},flex:!0,flexFlow:!0,flexGrow:!0,flexShrink:!0,flexBasis:$.sizes("flexBasis"),justifySelf:!0,alignSelf:!0,order:!0,placeItems:!0,placeContent:!0,placeSelf:!0,gap:$.space("gap"),rowGap:$.space("rowGap"),columnGap:$.space("columnGap")};Object.assign(z,{flexDir:z.flexDirection});let L={width:$.sizesT("width"),inlineSize:$.sizesT("inlineSize"),height:$.sizes("height"),blockSize:$.sizes("blockSize"),boxSize:$.sizes(["width","height"]),minWidth:$.sizes("minWidth"),minInlineSize:$.sizes("minInlineSize"),minHeight:$.sizes("minHeight"),minBlockSize:$.sizes("minBlockSize"),maxWidth:$.sizes("maxWidth"),maxInlineSize:$.sizes("maxInlineSize"),maxHeight:$.sizes("maxHeight"),maxBlockSize:$.sizes("maxBlockSize"),overflow:!0,overflowX:!0,overflowY:!0,overscrollBehavior:!0,overscrollBehaviorX:!0,overscrollBehaviorY:!0,display:!0,aspectRatio:!0,hideFrom:{scale:"breakpoints",transform:(r,e)=>{let t=e.__breakpoints?.get(r)?.minW??r;return{[`@media screen and (min-width: ${t})`]:{display:"none"}}}},hideBelow:{scale:"breakpoints",transform:(r,e)=>{let t=e.__breakpoints?.get(r)?._minW??r;return{[`@media screen and (max-width: ${t})`]:{display:"none"}}}},verticalAlign:!0,boxSizing:!0,boxDecorationBreak:!0,float:$.propT("float",I.float),objectFit:!0,objectPosition:!0,visibility:!0,isolation:!0};Object.assign(L,{w:L.width,h:L.height,minW:L.minWidth,maxW:L.maxWidth,minH:L.minHeight,maxH:L.maxHeight,overscroll:L.overscrollBehavior,overscrollX:L.overscrollBehaviorX,overscrollY:L.overscrollBehaviorY});let M={filter:{transform:I.filter},blur:$.blur("--chakra-blur"),brightness:$.propT("--chakra-brightness",I.brightness),contrast:$.propT("--chakra-contrast",I.contrast),hueRotate:$.propT("--chakra-hue-rotate",I.hueRotate),invert:$.propT("--chakra-invert",I.invert),saturate:$.propT("--chakra-saturate",I.saturate),dropShadow:$.propT("--chakra-drop-shadow",I.dropShadow),backdropFilter:{transform:I.backdropFilter},backdropBlur:$.blur("--chakra-backdrop-blur"),backdropBrightness:$.propT("--chakra-backdrop-brightness",I.brightness),backdropContrast:$.propT("--chakra-backdrop-contrast",I.contrast),backdropHueRotate:$.propT("--chakra-backdrop-hue-rotate",I.hueRotate),backdropInvert:$.propT("--chakra-backdrop-invert",I.invert),backdropSaturate:$.propT("--chakra-backdrop-saturate",I.saturate)},j={ring:{transform:I.ring},ringColor:$.colors("--chakra-ring-color"),ringOffset:$.prop("--chakra-ring-offset-width"),ringOffsetColor:$.colors("--chakra-ring-offset-color"),ringInset:$.prop("--chakra-ring-inset")},O={appearance:!0,cursor:!0,resize:!0,userSelect:!0,pointerEvents:!0,outline:{transform:I.outline},outlineOffset:!0,outlineColor:$.colors("outlineColor")},P={gridGap:$.space("gridGap"),gridColumnGap:$.space("gridColumnGap"),gridRowGap:$.space("gridRowGap"),gridColumn:!0,gridRow:!0,gridAutoFlow:!0,gridAutoColumns:!0,gridColumnStart:!0,gridColumnEnd:!0,gridRowStart:!0,gridRowEnd:!0,gridAutoRows:!0,gridTemplate:!0,gridTemplateColumns:!0,gridTemplateRows:!0,gridTemplateAreas:!0,gridArea:!0},X=(r=>{let e=new WeakMap;return(t,a,o,i)=>{if(void 0===t)return r(t,a,o);e.has(t)||e.set(t,new Map);let n=e.get(t);if(n.has(a))return n.get(a);let d=r(t,a,o,i);return n.set(a,d),d}})(function(r,e,t,a){let o="string"==typeof e?e.split("."):[e];for(a=0;a<o.length&&r;a+=1)r=r[o[a]];return void 0===r?t:r}),A={border:"0px",clip:"rect(0, 0, 0, 0)",width:"1px",height:"1px",margin:"-1px",padding:"0px",overflow:"hidden",whiteSpace:"nowrap",position:"absolute"},F={position:"static",width:"auto",height:"auto",clip:"auto",padding:"0",margin:"0",overflow:"visible",whiteSpace:"normal"},D=(r,e,t)=>{let a={},o=X(r,e,{});for(let r in o)r in t&&null!=t[r]||(a[r]=o[r]);return a},G={position:!0,pos:$.prop("position"),zIndex:$.prop("zIndex","zIndices"),inset:$.spaceT("inset"),insetX:$.spaceT(["left","right"]),insetInline:$.spaceT("insetInline"),insetY:$.spaceT(["top","bottom"]),insetBlock:$.spaceT("insetBlock"),top:$.spaceT("top"),insetBlockStart:$.spaceT("insetBlockStart"),bottom:$.spaceT("bottom"),insetBlockEnd:$.spaceT("insetBlockEnd"),left:$.spaceT("left"),insetInlineStart:$.logical({scale:"space",property:{ltr:"left",rtl:"right"}}),right:$.spaceT("right"),insetInlineEnd:$.logical({scale:"space",property:{ltr:"right",rtl:"left"}})};Object.assign(G,{insetStart:G.insetInlineStart,insetEnd:G.insetInlineEnd});let Y={boxShadow:$.shadows("boxShadow"),mixBlendMode:!0,blendMode:$.prop("mixBlendMode"),backgroundBlendMode:!0,bgBlendMode:$.prop("backgroundBlendMode"),opacity:!0};Object.assign(Y,{shadow:Y.boxShadow});let H={margin:$.spaceT("margin"),marginTop:$.spaceT("marginTop"),marginBlockStart:$.spaceT("marginBlockStart"),marginRight:$.spaceT("marginRight"),marginInlineEnd:$.spaceT("marginInlineEnd"),marginBottom:$.spaceT("marginBottom"),marginBlockEnd:$.spaceT("marginBlockEnd"),marginLeft:$.spaceT("marginLeft"),marginInlineStart:$.spaceT("marginInlineStart"),marginX:$.spaceT(["marginInlineStart","marginInlineEnd"]),marginInline:$.spaceT("marginInline"),marginY:$.spaceT(["marginTop","marginBottom"]),marginBlock:$.spaceT("marginBlock"),padding:$.space("padding"),paddingTop:$.space("paddingTop"),paddingBlockStart:$.space("paddingBlockStart"),paddingRight:$.space("paddingRight"),paddingBottom:$.space("paddingBottom"),paddingBlockEnd:$.space("paddingBlockEnd"),paddingLeft:$.space("paddingLeft"),paddingInlineStart:$.space("paddingInlineStart"),paddingInlineEnd:$.space("paddingInlineEnd"),paddingX:$.space(["paddingInlineStart","paddingInlineEnd"]),paddingInline:$.space("paddingInline"),paddingY:$.space(["paddingTop","paddingBottom"]),paddingBlock:$.space("paddingBlock")};Object.assign(H,{m:H.margin,mt:H.marginTop,mr:H.marginRight,me:H.marginInlineEnd,marginEnd:H.marginInlineEnd,mb:H.marginBottom,ml:H.marginLeft,ms:H.marginInlineStart,marginStart:H.marginInlineStart,mx:H.marginX,my:H.marginY,p:H.padding,pt:H.paddingTop,py:H.paddingY,px:H.paddingX,pb:H.paddingBottom,pl:H.paddingLeft,ps:H.paddingInlineStart,paddingStart:H.paddingInlineStart,pr:H.paddingRight,pe:H.paddingInlineEnd,paddingEnd:H.paddingInlineEnd});let V={scrollBehavior:!0,scrollSnapAlign:!0,scrollSnapStop:!0,scrollSnapType:!0,scrollMargin:$.spaceT("scrollMargin"),scrollMarginTop:$.spaceT("scrollMarginTop"),scrollMarginBottom:$.spaceT("scrollMarginBottom"),scrollMarginLeft:$.spaceT("scrollMarginLeft"),scrollMarginRight:$.spaceT("scrollMarginRight"),scrollMarginX:$.spaceT(["scrollMarginLeft","scrollMarginRight"]),scrollMarginY:$.spaceT(["scrollMarginTop","scrollMarginBottom"]),scrollPadding:$.spaceT("scrollPadding"),scrollPaddingTop:$.spaceT("scrollPaddingTop"),scrollPaddingBottom:$.spaceT("scrollPaddingBottom"),scrollPaddingLeft:$.spaceT("scrollPaddingLeft"),scrollPaddingRight:$.spaceT("scrollPaddingRight"),scrollPaddingX:$.spaceT(["scrollPaddingLeft","scrollPaddingRight"]),scrollPaddingY:$.spaceT(["scrollPaddingTop","scrollPaddingBottom"])},Q={fontFamily:$.prop("fontFamily","fonts"),fontSize:$.prop("fontSize","fontSizes",I.px),fontWeight:$.prop("fontWeight","fontWeights"),lineHeight:$.prop("lineHeight","lineHeights"),letterSpacing:$.prop("letterSpacing","letterSpacings"),textAlign:!0,fontStyle:!0,textIndent:!0,wordBreak:!0,overflowWrap:!0,textOverflow:!0,textTransform:!0,whiteSpace:!0,isTruncated:{transform(r){if(!0===r)return{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}}},noOfLines:{static:{overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box",WebkitBoxOrient:"vertical",WebkitLineClamp:"var(--chakra-line-clamp)"},property:"--chakra-line-clamp"}},J={textDecorationColor:$.colors("textDecorationColor"),textDecoration:!0,textDecor:{property:"textDecoration"},textDecorationLine:!0,textDecorationStyle:!0,textDecorationThickness:!0,textUnderlineOffset:!0,textShadow:$.shadows("textShadow")},U={clipPath:!0,transform:$.propT("transform",I.transform),transformOrigin:!0,translateX:$.spaceT("--chakra-translate-x"),translateY:$.spaceT("--chakra-translate-y"),skewX:$.degreeT("--chakra-skew-x"),skewY:$.degreeT("--chakra-skew-y"),scaleX:$.prop("--chakra-scale-x"),scaleY:$.prop("--chakra-scale-y"),scale:$.prop(["--chakra-scale-x","--chakra-scale-y"]),rotate:$.degreeT("--chakra-rotate")},N={listStyleType:!0,listStylePosition:!0,listStylePos:$.prop("listStylePosition"),listStyleImage:!0,listStyleImg:$.prop("listStyleImage")},q={transition:!0,transitionDelay:!0,animation:!0,willChange:!0,transitionDuration:$.prop("transitionDuration","transition.duration"),transitionProperty:$.prop("transitionProperty","transition.property"),transitionTimingFunction:$.prop("transitionTimingFunction","transition.easing")},Z=(0,a.XQ)({},E,W,C,z,L,M,j,O,P,{srOnly:{transform:r=>!0===r?A:"focusable"===r?F:{}},layerStyle:{processResult:!0,transform:(r,e,t)=>D(e,`layerStyles.${r}`,t)},textStyle:{processResult:!0,transform:(r,e,t)=>D(e,`textStyles.${r}`,t)},apply:{processResult:!0,transform:(r,e,t)=>D(e,r,t)}},G,Y,H,V,Q,J,U,N,q),K=Object.keys(Object.assign({},H,L,z,P,G)),rr=[...Object.keys(Z),...s],re={...Z,...l},rt=r=>r in re,ra=r=>e=>{if(!e.__breakpoints)return r;let{isResponsive:t,toArrayValue:o,media:i}=e.__breakpoints,n={};for(let d in r){let l=(0,a.Jg)(r[d],e);if(null==l)continue;if(!Array.isArray(l=(0,a.Gv)(l)&&t(l)?o(l):l)){n[d]=l;continue}let s=l.slice(0,i.length).length;for(let r=0;r<s;r+=1){let e=i?.[r];if(!e){n[d]=l[r];continue}n[e]=n[e]||{},null!=l[r]&&(n[e][d]=l[r])}}return n},ro=(r,e)=>r.startsWith("--")&&"string"==typeof e&&!/^var\(--.+\)$/.test(e),ri=(r,e)=>{if(null==e)return e;let t=e=>r.__cssMap?.[e]?.varRef,a=r=>t(r)??r,[o,i]=function(r){let e=[],t="",a=!1;for(let o=0;o<r.length;o++){let i=r[o];"("===i?(a=!0,t+=i):")"===i?(a=!1,t+=i):","!==i||a?t+=i:(e.push(t),t="")}return(t=t.trim())&&e.push(t),e}(e);return e=t(o)??a(i)??a(e)},rn=r=>e=>(function(r){let{configs:e={},pseudos:t={},theme:o}=r,i=(r,n=!1)=>{let d=(0,a.Jg)(r,o),l=ra(d)(o),s={};for(let r in l){let c=l[r],p=(0,a.Jg)(c,o);r in t&&(r=t[r]),ro(r,p)&&(p=ri(o,p));let g=e[r];if(!0===g&&(g={property:r}),(0,a.Gv)(p)){s[r]=s[r]??{},s[r]=(0,a.XQ)({},s[r],i(p,!0));continue}let h=g?.transform?.(p,o,d)??p;h=g?.processResult?i(h,!0):h;let u=(0,a.Jg)(g?.property,o);if(!n&&g?.static){let r=(0,a.Jg)(g.static,o);s=(0,a.XQ)({},s,r)}if(u&&Array.isArray(u)){for(let r of u)s[r]=h;continue}if(u){"&"===u&&(0,a.Gv)(h)?s=(0,a.XQ)({},s,h):s[u]=h;continue}if((0,a.Gv)(h)){s=(0,a.XQ)({},s,h);continue}s[r]=h}return s};return i})({theme:e,pseudos:l,configs:Z})(r);function rd(r){return r}function rl(r){return r}function rs(r){return{definePartsStyle:r=>r,defineMultiStyleConfig:e=>({parts:r,...e})}}function rc(r,e,t){return r.__cssMap?.[`${e}.${t}`]?.varRef??t}function rp(r){return e=>{let{variant:t,size:o,theme:i}=e,n=function(r){let e=r.__breakpoints;return function(r,t,o,i){var n;if(!e)return;let d={},l=(n=e.toArrayValue,Array.isArray(o)?o:(0,a.Gv)(o)?n(o):null!=o?[o]:void 0);if(!l)return d;let s=l.length,c=1===s,p=!!r.parts;for(let o=0;o<s;o++){let n=e.details[o],s=e.details[function(r,e){for(let t=e+1;t<r.length;t++)if(null!=r[t])return t;return -1}(l,o)],g=(0,a.Rk)(n.minW,s?._minW),h=(0,a.Jg)(r[t]?.[l[o]],i);if(h){if(p){r.parts?.forEach(r=>{(0,a.XQ)(d,{[r]:c?h[r]:{[g]:h[r]}})});continue}if(!p){c?(0,a.XQ)(d,h):d[g]=h;continue}d[g]=h}}return d}}(i);return(0,a.XQ)({},(0,a.Jg)(r.baseStyle??{},e),n(r,"sizes",o,e),n(r,"variants",t,e))}}function rg(r){return(0,a.cJ)(r,["styleConfig","size","variant","colorScheme"])}function rh(r){return(0,a.Gv)(r)&&r.reference?r.reference:String(r)}let ru=(r,...e)=>e.map(rh).join(` ${r} `).replace(/calc/g,""),rb=(...r)=>`calc(${ru("+",...r)})`,rf=(...r)=>`calc(${ru("-",...r)})`,rm=(...r)=>`calc(${ru("*",...r)})`,rk=(...r)=>`calc(${ru("/",...r)})`,rv=r=>{let e=rh(r);return null==e||Number.isNaN(parseFloat(e))?rm(e,-1):String(e).startsWith("-")?String(e).slice(1):`-${e}`},rS=Object.assign(r=>({add:(...e)=>rS(rb(r,...e)),subtract:(...e)=>rS(rf(r,...e)),multiply:(...e)=>rS(rm(r,...e)),divide:(...e)=>rS(rk(r,...e)),negate:()=>rS(rv(r)),toString:()=>r.toString()}),{add:rb,subtract:rf,multiply:rm,divide:rk,negate:rv});function ry(r,e,t){let a=function(r,e=""){var t;return((t=function(r,e="-"){return r.replace(/\s+/g,e)}(`--${(function(r,e=""){return[e,r].filter(Boolean).join("-")})(r,e)}`.toString())).includes("\\.")||Number.isInteger(parseFloat(t.toString()))?t:t.replace(".","\\.")).replace(/[!-,/:-@[-^`{-~]/g,"\\$&")}(r,t);return{variable:a,reference:`var(${a}${e?`, ${e}`:""})`}}function rR(r,e){let t={};for(let a of e){if(Array.isArray(a)){let[e,o]=a;t[e]=ry(`${r}-${e}`,o);continue}t[a]=ry(`${r}-${a}`)}return t}let rT=["colors","borders","borderWidths","borderStyles","fonts","fontSizes","fontWeights","gradients","letterSpacings","lineHeights","radii","space","shadows","sizes","zIndices","transition","blur","breakpoints"];function rB(r,e){return ry(String(r).replace(/\./g,"-"),void 0,e)}function rx(r){let e=function(r){let{__cssMap:e,__cssVars:t,__breakpoints:a,...o}=r;return o}(r),{cssMap:t,cssVars:o}=function(r){let e=function(r){let e=(0,a.Up)(r,rT),t=r.semanticTokens,o=r=>s.includes(r)||"default"===r,i={};return(0,a.UU)(e,(r,e)=>{null!=r&&(i[e.join(".")]={isSemantic:!1,value:r})}),(0,a.UU)(t,(r,e)=>{null!=r&&(i[e.join(".")]={isSemantic:!0,value:r})},{stop:r=>Object.keys(r).every(o)}),i}(r),t=r.config?.cssVarPrefix,o={},i={};for(let[r,n]of Object.entries(e)){let{isSemantic:d,value:s}=n,{variable:c,reference:p}=rB(r,t);if(!d){if(r.startsWith("space")){let[e,...t]=r.split("."),a=`${e}.-${t.join(".")}`,o=rS.negate(s),n=rS.negate(p);i[a]={value:o,var:c,varRef:n}}o[c]=s,i[r]={value:s,var:c,varRef:p};continue}let g=(0,a.Gv)(s)?s:{default:s};o=(0,a.XQ)(o,Object.entries(g).reduce((a,[o,i])=>{if(!i)return a;let n=function(r,a){let o=[String(r).split(".")[0],a].join(".");if(!e[o])return a;let{reference:i}=rB(o,t);return i}(r,`${i}`);return"default"===o?a[c]=n:a[l?.[o]??o]={[c]:n},a},{})),i[r]={value:p,var:c,varRef:p}}return{cssVars:o,cssMap:i}}(e);return Object.assign(e,{__cssVars:{"--chakra-ring-inset":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-ring-offset-width":"0px","--chakra-ring-offset-color":"#fff","--chakra-ring-color":"rgba(66, 153, 225, 0.6)","--chakra-ring-offset-shadow":"0 0 #0000","--chakra-ring-shadow":"0 0 #0000","--chakra-space-x-reverse":"0","--chakra-space-y-reverse":"0",...o},__cssMap:t,__breakpoints:(0,a.d4)(e.breakpoints)}),e}}};