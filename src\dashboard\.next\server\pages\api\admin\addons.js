"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/addons";
exports.ids = ["pages/api/admin/addons"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Faddons&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Caddons%5Cindex.ts&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Faddons&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Caddons%5Cindex.ts&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_addons_index_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\addons\\index.ts */ \"(api-node)/./pages/api/admin/addons/index.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_addons_index_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_addons_index_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/addons\",\n        pathname: \"/api/admin/addons\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_admin_addons_index_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Faddons&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Caddons%5Cindex.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/admin/addons/index.ts":
/*!*****************************************!*\
  !*** ./pages/api/admin/addons/index.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// Helper function to locate config.yml\nfunction locateConfig() {\n    // Get the correct project root (dashboard runs from src/dashboard)\n    const projectRoot = process.cwd().includes('dashboard') ? path__WEBPACK_IMPORTED_MODULE_3___default().resolve(process.cwd(), '..', '..') : process.cwd();\n    const attempts = [\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(projectRoot, 'config.yml'),\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(projectRoot, '404-bot', 'config.yml')\n    ];\n    let found = attempts.find((p)=>fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(p));\n    if (!found) throw new Error('config.yml not found');\n    return found;\n}\n// Helper function to locate addons directories\nfunction locateAddonsDirs() {\n    // Get the correct project root (dashboard runs from src/dashboard)\n    const projectRoot = process.cwd().includes('dashboard') ? path__WEBPACK_IMPORTED_MODULE_3___default().resolve(process.cwd(), '..', '..') : process.cwd();\n    console.log('Process cwd:', process.cwd());\n    console.log('Project root:', projectRoot);\n    const attempts = [\n        // Source directories (TypeScript) - check these first\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(projectRoot, 'src', 'addons'),\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(projectRoot, '404-bot', 'src', 'addons'),\n        // Compiled directories (JavaScript)\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(projectRoot, 'dist', 'addons'),\n        path__WEBPACK_IMPORTED_MODULE_3___default().join(projectRoot, '404-bot', 'dist', 'addons')\n    ];\n    console.log('=== LOCATE ADDONS DIRS DEBUG ===');\n    console.log('Checking directories in order:');\n    const foundDirs = [];\n    attempts.forEach((attempt, i)=>{\n        const exists = fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(attempt);\n        console.log(`${i + 1}. ${attempt} - ${exists ? 'EXISTS' : 'NOT FOUND'}`);\n        if (exists) {\n            foundDirs.push(attempt);\n        }\n    });\n    // Also check fallback paths using project root\n    const fallbackSrc = path__WEBPACK_IMPORTED_MODULE_3___default().join(projectRoot, 'src', 'addons');\n    const fallbackDist = path__WEBPACK_IMPORTED_MODULE_3___default().join(projectRoot, 'dist', 'addons');\n    console.log('Trying fallback paths:');\n    console.log(`- ${fallbackSrc} - ${fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(fallbackSrc) ? 'EXISTS' : 'NOT FOUND'}`);\n    console.log(`- ${fallbackDist} - ${fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(fallbackDist) ? 'EXISTS' : 'NOT FOUND'}`);\n    if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(fallbackSrc) && !foundDirs.includes(fallbackSrc)) foundDirs.push(fallbackSrc);\n    if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(fallbackDist) && !foundDirs.includes(fallbackDist)) foundDirs.push(fallbackDist);\n    if (foundDirs.length === 0) throw new Error('No addons directories found');\n    // Remove duplicates\n    const uniqueDirs = Array.from(new Set(foundDirs));\n    console.log('Found addons directories:', uniqueDirs);\n    return uniqueDirs;\n}\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    // Check authentication\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    if (!session) {\n        return res.status(401).json({\n            error: 'Unauthorized'\n        });\n    }\n    // Check admin permission\n    const isAdmin = session.user.isAdmin;\n    if (!isAdmin) {\n        return res.status(403).json({\n            error: 'Forbidden - Admin access required'\n        });\n    }\n    try {\n        // Read config file\n        const configPath = locateConfig();\n        console.log('Found config at:', configPath);\n        const configFile = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(configPath, 'utf8');\n        const config = yaml__WEBPACK_IMPORTED_MODULE_4___default().parse(configFile);\n        // Get addons directories\n        const addonsDirs = locateAddonsDirs();\n        console.log('=== ADDON SCANNING DEBUG ===');\n        console.log('Found addons directories at:', addonsDirs);\n        const allAddons = new Map();\n        // Collect all addons from all directories\n        addonsDirs.forEach((dir)=>{\n            console.log(`Processing addon directory: ${dir}`);\n            const dirContents = fs__WEBPACK_IMPORTED_MODULE_2___default().readdirSync(dir, {\n                withFileTypes: true\n            });\n            const dirAddons = dirContents.filter((dirent)=>dirent.isDirectory()).map((dirent)=>dirent.name);\n            console.log(`Addons found in ${dir}:`, dirAddons);\n            dirAddons.forEach((name)=>{\n                const fullPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(dir, name);\n                // If we already have this addon, skip it (first found wins)\n                if (!allAddons.has(name)) {\n                    allAddons.set(name, {\n                        name,\n                        fullPath\n                    });\n                }\n            });\n        });\n        const addonEntries = Array.from(allAddons.values());\n        console.log('All unique addon entries:', addonEntries.map((a)=>`${a.name} (${a.fullPath})`));\n        // Also list what's actually in each directory\n        addonEntries.forEach(({ name, fullPath })=>{\n            const files = fs__WEBPACK_IMPORTED_MODULE_2___default().readdirSync(fullPath);\n            console.log(`Contents of ${name}:`, files);\n        });\n        // Separate built-in and custom addons\n        const builtInAddons = [];\n        const customAddons = [];\n        // Build info array for each addon directory\n        addonEntries.forEach(({ name, fullPath })=>{\n            console.log(`Processing addon: ${name}`);\n            // Default metadata\n            let info = {\n                name,\n                version: '1.0.0',\n                description: 'No description available',\n                author: 'Unknown'\n            };\n            // Check if this is a custom addon (created by addon builder)\n            let isCustomAddon = false;\n            // 1) Check config.yml metadata if present\n            try {\n                const cfgPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(fullPath, 'config.yml');\n                if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(cfgPath)) {\n                    const cfgRaw = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(cfgPath, 'utf8');\n                    const cfg = yaml__WEBPACK_IMPORTED_MODULE_4___default().parse(cfgRaw);\n                    console.log(`Config for ${name}:`, cfg);\n                    // Handle both nested (old) and flat (new) config structures\n                    const configData = cfg?.addon || cfg; // Use nested if available, otherwise flat\n                    if (configData) {\n                        info = {\n                            ...info,\n                            name: configData.name ?? info.name,\n                            version: configData.version ?? info.version,\n                            description: configData.description ?? info.description,\n                            author: configData.author ?? info.author\n                        };\n                        // Check if it's a custom addon (check both structures)\n                        isCustomAddon = configData.author === 'Addon Builder' || configData.description === 'Generated addon from visual builder' || configData.description?.includes('Generated addon from visual builder');\n                        console.log(`${name} - Config-based detection: Author: \"${configData.author}\", Description: \"${configData.description}\", isCustomAddon: ${isCustomAddon}`);\n                    }\n                } else {\n                    console.log(`No config.yml found for ${name}`);\n                }\n            } catch (err) {\n                console.warn(`Error reading addon ${name} config.yml:`, err);\n            }\n            // Additional check: Look for flow.json file (indicates visual builder creation)\n            if (!isCustomAddon) {\n                const flowJsonPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(fullPath, 'flow.json');\n                if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(flowJsonPath)) {\n                    isCustomAddon = true;\n                    console.log(`${name} - Found flow.json file, marking as custom addon`);\n                }\n            }\n            // 2) Fallback to parsing index.ts if still missing description or author\n            try {\n                const indexPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(fullPath, 'index.ts');\n                if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(indexPath)) {\n                    const indexContent = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(indexPath, 'utf8');\n                    // Check if it's a custom addon by looking at the file content\n                    if (!isCustomAddon) {\n                        const hasGeneratedMarker = indexContent.includes('Generated addon from visual builder');\n                        const hasBuilderAuthor = indexContent.includes('author: \\'Addon Builder\\'');\n                        isCustomAddon = hasGeneratedMarker && hasBuilderAuthor;\n                        console.log(`${name} - Checking index.ts: hasGeneratedMarker=${hasGeneratedMarker}, hasBuilderAuthor=${hasBuilderAuthor}, isCustomAddon=${isCustomAddon}`);\n                    }\n                    // Try to extract info from the addon structure\n                    const infoMatch = indexContent.match(/info:\\s*{([\\s\\S]*?)}/m);\n                    if (infoMatch) {\n                        const objBlock = infoMatch[1];\n                        // simple regex extractions\n                        const versionMatch = objBlock.match(/version:\\s*['\"]([^'\\\"]*)['\"]/);\n                        const descMatch = objBlock.match(/description:\\s*['\"]([^'\\\"]*)['\"]/);\n                        const authorMatch = objBlock.match(/author:\\s*['\"]([^'\\\"]*)['\"]/);\n                        if (versionMatch) info.version = versionMatch[1];\n                        if (descMatch && (!info.description || info.description === 'No description available')) info.description = descMatch[1];\n                        if (authorMatch && (!info.author || info.author === 'Unknown')) info.author = authorMatch[1];\n                    }\n                }\n            } catch (error) {\n                console.warn(`Error reading addon ${name} index.ts info:`, error);\n            }\n            // Check if addon is enabled\n            const isEnabled = config.addons?.enabled && (!config.addons.disabled || !config.addons.disabled.includes(name));\n            // Check if addon has config\n            const hasConfig = fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(path__WEBPACK_IMPORTED_MODULE_3___default().join(fullPath, 'config.yml'));\n            const addonData = {\n                ...info,\n                enabled: isEnabled,\n                hasConfig,\n                isCustomAddon,\n                config: config.addons?.configs?.[name] || {},\n                status: 'active'\n            };\n            console.log(`Final categorization for ${name}: isCustomAddon=${isCustomAddon}`);\n            // Categorize the addon\n            if (isCustomAddon) {\n                customAddons.push(addonData);\n                console.log(`Added ${name} to customAddons`);\n            } else {\n                builtInAddons.push(addonData);\n                console.log(`Added ${name} to builtInAddons`);\n            }\n        });\n        console.log(`Final results: builtInAddons=${builtInAddons.length}, customAddons=${customAddons.length}`);\n        console.log('Built-in addons:', builtInAddons.map((a)=>a.name));\n        console.log('Custom addons:', customAddons.map((a)=>a.name));\n        return res.status(200).json({\n            builtInAddons,\n            customAddons,\n            // Legacy support - return all addons for backward compatibility\n            addons: [\n                ...builtInAddons,\n                ...customAddons\n            ]\n        });\n    } catch (error) {\n        console.error('Error listing addons:', error);\n        return res.status(500).json({\n            error: 'Internal server error',\n            details: error.message\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/admin/addons/index.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Faddons&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Caddons%5Cindex.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();