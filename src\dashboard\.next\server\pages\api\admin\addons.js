"use strict";(()=>{var e={};e.id=7194,e.ids=[7194],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},50799:(e,r,s)=>{s.r(r),s.d(r,{config:()=>v,default:()=>y,routeModule:()=>x});var n={};s.r(n),s.d(n,{default:()=>m});var i=s(93433),o=s(20264),t=s(20584),d=s(15806),a=s(94506),c=s(29021),u=s.n(c),l=s(33873),p=s.n(l),f=s(72115),h=s.n(f);async function m(e,r){if("GET"!==e.method)return r.status(405).json({error:"Method not allowed"});let s=await (0,d.getServerSession)(e,r,a.authOptions);if(!s)return r.status(401).json({error:"Unauthorized"});if(!s.user.isAdmin)return r.status(403).json({error:"Forbidden - Admin access required"});try{let e=function(){let e=process.cwd().includes("dashboard")?p().resolve(process.cwd(),"..",".."):process.cwd(),r=[p().join(e,"config.yml"),p().join(e,"404-bot","config.yml")].find(e=>u().existsSync(e));if(!r)throw Error("config.yml not found");return r}(),s=u().readFileSync(e,"utf8"),n=h().parse(s),i=function(){let e=process.cwd().includes("dashboard")?p().resolve(process.cwd(),"..",".."):process.cwd(),r=[p().join(e,"src","addons"),p().join(e,"404-bot","src","addons"),p().join(e,"dist","addons"),p().join(e,"404-bot","dist","addons")],s=[];r.forEach((e,r)=>{u().existsSync(e)&&s.push(e)});let n=p().join(e,"src","addons"),i=p().join(e,"dist","addons");if(u().existsSync(n)&&!s.includes(n)&&s.push(n),u().existsSync(i)&&!s.includes(i)&&s.push(i),0===s.length)throw Error("No addons directories found");return Array.from(new Set(s))}(),o=new Map;i.forEach(e=>{u().readdirSync(e,{withFileTypes:!0}).filter(e=>e.isDirectory()).map(e=>e.name).forEach(r=>{let s=p().join(e,r);o.has(r)||o.set(r,{name:r,fullPath:s})})});let t=Array.from(o.values());t.forEach(({name:e,fullPath:r})=>{u().readdirSync(r)});let d=[],a=[];return t.forEach(({name:e,fullPath:r})=>{let s={name:e,version:"1.0.0",description:"No description available",author:"Unknown"},i=!1;try{let e=p().join(r,"config.yml");if(u().existsSync(e)){let r=u().readFileSync(e,"utf8"),n=h().parse(r),o=n?.addon||n;o&&(s={...s,name:o.name??s.name,version:o.version??s.version,description:o.description??s.description,author:o.author??s.author},i="Addon Builder"===o.author||"Generated addon from visual builder"===o.description||o.description?.includes("Generated addon from visual builder"))}}catch(e){}if(!i){let e=p().join(r,"flow.json");u().existsSync(e)&&(i=!0)}try{let e=p().join(r,"index.ts");if(u().existsSync(e)){let r=u().readFileSync(e,"utf8");if(!i){let e=r.includes("Generated addon from visual builder"),s=r.includes("author: 'Addon Builder'");i=e&&s}let n=r.match(/info:\s*{([\s\S]*?)}/m);if(n){let e=n[1],r=e.match(/version:\s*['"]([^'\"]*)['"]/),i=e.match(/description:\s*['"]([^'\"]*)['"]/),o=e.match(/author:\s*['"]([^'\"]*)['"]/);r&&(s.version=r[1]),i&&(!s.description||"No description available"===s.description)&&(s.description=i[1]),o&&(!s.author||"Unknown"===s.author)&&(s.author=o[1])}}}catch(e){}let o=n.addons?.enabled&&(!n.addons.disabled||!n.addons.disabled.includes(e)),t=u().existsSync(p().join(r,"config.yml")),c={...s,enabled:o,hasConfig:t,isCustomAddon:i,config:n.addons?.configs?.[e]||{},status:"active"};i?a.push(c):d.push(c)}),r.status(200).json({builtInAddons:d,customAddons:a,addons:[...d,...a]})}catch(e){return r.status(500).json({error:"Internal server error",details:e.message})}}let y=(0,t.M)(n,"default"),v=(0,t.M)(n,"config"),x=new i.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/admin/addons",pathname:"/api/admin/addons",bundlePath:"",filename:""},userland:n})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../webpack-api-runtime.js");r.C(e);var s=e=>r(r.s=e),n=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>s(50799));module.exports=n})();