"use strict";exports.id=4227,exports.ids=[4227],exports.modules={31465:(t,n,i)=>{i.d(n,{WH:()=>u,Mb:()=>function t(){var n=w(J()(f));return n.copy=function(){return L(n,t())},r.apply(n,arguments)},Cr:()=>function t(){var n=P(J()).domain([.1,1,10]);return n.copy=function(){return L(n,t()).base(n.base())},r.apply(n,arguments)},yj:()=>Q,q9:()=>tt,xh:()=>function t(){var n=z(J());return n.copy=function(){return L(n,t()).constant(n.constant())},r.apply(n,arguments)},jo:()=>function t(n){var i;function e(t){return null==t||isNaN(t*=1)?i:t}return e.invert=e,e.domain=e.range=function(t){return arguments.length?(n=Array.from(t,c),e):n.slice()},e.unknown=function(t){return arguments.length?(i=t,e):i},e.copy=function(){return t(n).unknown(i)},n=arguments.length?Array.from(n,c):[0,1],w(e)},U4:()=>h,m4:()=>function t(){var n=v();return n.copy=function(){return g(n,t())},o.apply(n,arguments),w(n)},ZE:()=>function t(){let n=P(d()).domain([1,10]);return n.copy=()=>g(n,t()).base(n.base()),o.apply(n,arguments),n},UM:()=>s,hq:()=>a,RW:()=>O,QL:()=>function t(){var n,i=[],r=[],h=[];function s(){var t=0,n=Math.max(1,r.length);for(h=Array(n-1);++t<n;)h[t-1]=(0,e.Z4)(i,t/n);return u}function u(t){return null==t||isNaN(t*=1)?n:r[(0,e.h1)(h,t)]}return u.invertExtent=function(t){var n=r.indexOf(t);return n<0?[NaN,NaN]:[n>0?h[n-1]:i[0],n<h.length?h[n]:i[i.length-1]]},u.domain=function(t){if(!arguments.length)return i.slice();for(let n of(i=[],t))null==n||isNaN(n*=1)||i.push(n);return i.sort(e.V_),s()},u.range=function(t){return arguments.length?(r=Array.from(t),s()):r.slice()},u.unknown=function(t){return arguments.length?(n=t,u):n},u.quantiles=function(){return h.slice()},u.copy=function(){return t().domain(i).range(r).unknown(n)},o.apply(u,arguments)},WT:()=>function t(){var n,i=0,r=1,h=1,s=[.5],u=[0,1];function a(t){return null!=t&&t<=t?u[(0,e.h1)(s,t,0,h)]:n}function _(){var t=-1;for(s=Array(h);++t<h;)s[t]=((t+1)*r-(t-h)*i)/(h+1);return a}return a.domain=function(t){return arguments.length?([i,r]=t,i*=1,r*=1,_()):[i,r]},a.range=function(t){return arguments.length?(h=(u=Array.from(t)).length-1,_()):u.slice()},a.invertExtent=function(t){var n=u.indexOf(t);return n<0?[NaN,NaN]:n<1?[i,s[0]]:n>=h?[s[h-1],r]:[s[n-1],s[n]]},a.unknown=function(t){return arguments.length&&(n=t),a},a.thresholds=function(){return s.slice()},a.copy=function(){return t().domain([i,r]).range(u).unknown(n)},o.apply(w(a),arguments)},af:()=>function t(){var n,i=v(),e=[0,1],r=!1;function h(t){var e,o=Math.sign(e=i(t))*Math.sqrt(Math.abs(e));return isNaN(o)?n:r?Math.round(o):o}return h.invert=function(t){return i.invert(U(t))},h.domain=function(t){return arguments.length?(i.domain(t),h):i.domain()},h.range=function(t){return arguments.length?(i.range((e=Array.from(t,c)).map(U)),h):e.slice()},h.rangeRound=function(t){return h.range(t).round(!0)},h.round=function(t){return arguments.length?(r=!!t,h):r},h.clamp=function(t){return arguments.length?(i.clamp(t),h):i.clamp()},h.unknown=function(t){return arguments.length?(n=t,h):n},h.copy=function(){return t(i.domain(),e).round(r).clamp(i.clamp()).unknown(n)},o.apply(h,arguments),w(h)},ex:()=>function t(){var n=w(F()(f));return n.copy=function(){return L(n,t())},r.apply(n,arguments)},M3:()=>function t(){var n=P(F()).domain([1,10]);return n.copy=function(){return L(n,t()).base(n.base())},r.apply(n,arguments)},ui:()=>$,T:()=>function t(){var n=[],i=f;function o(t){if(null!=t&&!isNaN(t*=1))return i(((0,e.h1)(n,t,1)-1)/(n.length-1))}return o.domain=function(t){if(!arguments.length)return n.slice();for(let i of(n=[],t))null==i||isNaN(i*=1)||n.push(i);return n.sort(e.V_),o},o.interpolator=function(t){return arguments.length?(i=t,o):i},o.range=function(){return n.map((t,e)=>i(e/(n.length-1)))},o.quantiles=function(t){return Array.from({length:t+1},(i,o)=>(0,e.YV)(n,o/t))},o.copy=function(){return t(i).domain(n)},r.apply(o,arguments)},ye:()=>K,nV:()=>function t(){var n=z(F());return n.copy=function(){return L(n,t()).constant(n.constant())},r.apply(n,arguments)},Bv:()=>W,aX:()=>function t(){var n=z(d());return n.copy=function(){return g(n,t()).constant(n.constant())},o.apply(n,arguments)},c3:()=>function t(){var n,i=[.5],r=[0,1],h=1;function s(t){return null!=t&&t<=t?r[(0,e.h1)(i,t,0,h)]:n}return s.domain=function(t){return arguments.length?(h=Math.min((i=Array.from(t)).length,r.length-1),s):i.slice()},s.range=function(t){return arguments.length?(r=Array.from(t),h=Math.min(i.length,r.length-1),s):r.slice()},s.invertExtent=function(t){var n=r.indexOf(t);return[i[n-1],i[n]]},s.unknown=function(t){return arguments.length?(n=t,s):n},s.copy=function(){return t().domain(i).range(r).unknown(n)},o.apply(s,arguments)},w7:()=>X,Pp:()=>Y,Vr:()=>T});var e=i(15081);function o(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function r(t,n){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof n?this.interpolator(n):this.range(n)}return this}let h=Symbol("implicit");function s(){var t=new e.Bu,n=[],i=[],r=h;function u(e){let o=t.get(e);if(void 0===o){if(r!==h)return r;t.set(e,o=n.push(e)-1)}return i[o%i.length]}return u.domain=function(i){if(!arguments.length)return n.slice();for(let o of(n=[],t=new e.Bu,i))t.has(o)||t.set(o,n.push(o)-1);return u},u.range=function(t){return arguments.length?(i=Array.from(t),u):i.slice()},u.unknown=function(t){return arguments.length?(r=t,u):r},u.copy=function(){return s(n,i).unknown(r)},o.apply(u,arguments),u}function u(){var t,n,i=s().unknown(void 0),r=i.domain,h=i.range,a=0,_=1,c=!1,l=0,f=0,p=.5;function y(){var i=r().length,o=_<a,s=o?_:a,u=o?a:_;t=(u-s)/Math.max(1,i-l+2*f),c&&(t=Math.floor(t)),s+=(u-s-t*(i-l))*p,n=t*(1-l),c&&(s=Math.round(s),n=Math.round(n));var y=(0,e.y1)(i).map(function(n){return s+t*n});return h(o?y.reverse():y)}return delete i.unknown,i.domain=function(t){return arguments.length?(r(t),y()):r()},i.range=function(t){return arguments.length?([a,_]=t,a*=1,_*=1,y()):[a,_]},i.rangeRound=function(t){return[a,_]=t,a*=1,_*=1,c=!0,y()},i.bandwidth=function(){return n},i.step=function(){return t},i.round=function(t){return arguments.length?(c=!!t,y()):c},i.padding=function(t){return arguments.length?(l=Math.min(1,f=+t),y()):l},i.paddingInner=function(t){return arguments.length?(l=Math.min(1,t),y()):l},i.paddingOuter=function(t){return arguments.length?(f=+t,y()):f},i.align=function(t){return arguments.length?(p=Math.max(0,Math.min(1,t)),y()):p},i.copy=function(){return u(r(),[a,_]).round(c).paddingInner(l).paddingOuter(f).align(p)},o.apply(y(),arguments)}function a(){return function t(n){var i=n.copy;return n.padding=n.paddingOuter,delete n.paddingInner,delete n.paddingOuter,n.copy=function(){return t(i())},n}(u.apply(null,arguments).paddingInner(1))}var _=i(39845);function c(t){return+t}var l=[0,1];function f(t){return t}function p(t,n){var i;return(n-=t*=1)?function(i){return(i-t)/n}:(i=isNaN(n)?NaN:.5,function(){return i})}function y(t,n,i){var e=t[0],o=t[1],r=n[0],h=n[1];return o<e?(e=p(o,e),r=i(h,r)):(e=p(e,o),r=i(r,h)),function(t){return r(e(t))}}function x(t,n,i){var o=Math.min(t.length,n.length)-1,r=Array(o),h=Array(o),s=-1;for(t[o]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++s<o;)r[s]=p(t[s],t[s+1]),h[s]=i(n[s],n[s+1]);return function(n){var i=(0,e.h1)(t,n,1,o)-1;return h[i](r[i](n))}}function g(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function d(){var t,n,i,e,o,r,h=l,s=l,u=_.GW,a=f;function p(){var t,n,i,u=Math.min(h.length,s.length);return a!==f&&(t=h[0],n=h[u-1],t>n&&(i=t,t=n,n=i),a=function(i){return Math.max(t,Math.min(n,i))}),e=u>2?x:y,o=r=null,g}function g(n){return null==n||isNaN(n*=1)?i:(o||(o=e(h.map(t),s,u)))(t(a(n)))}return g.invert=function(i){return a(n((r||(r=e(s,h.map(t),_.Dj)))(i)))},g.domain=function(t){return arguments.length?(h=Array.from(t,c),p()):h.slice()},g.range=function(t){return arguments.length?(s=Array.from(t),p()):s.slice()},g.rangeRound=function(t){return s=Array.from(t),u=_.sH,p()},g.clamp=function(t){return arguments.length?(a=!!t||f,p()):a!==f},g.interpolate=function(t){return arguments.length?(u=t,p()):u},g.unknown=function(t){return arguments.length?(i=t,g):i},function(i,e){return t=i,n=e,p()}}function v(){return d()(f,f)}var m=i(41355);function T(t,n,i,o){var r,h=(0,e.sG)(t,n,i);switch((o=(0,m.Gp)(null==o?",f":o)).type){case"s":var s=Math.max(Math.abs(t),Math.abs(n));return null!=o.precision||isNaN(r=(0,m.dT)(h,s))||(o.precision=r),(0,m.s)(o,s);case"":case"e":case"g":case"p":case"r":null!=o.precision||isNaN(r=(0,m.Pj)(h,Math.max(Math.abs(t),Math.abs(n))))||(o.precision=r-("e"===o.type));break;case"f":case"%":null!=o.precision||isNaN(r=(0,m.RT)(h))||(o.precision=r-("%"===o.type)*2)}return(0,m.GP)(o)}function w(t){var n=t.domain;return t.ticks=function(t){var i=n();return(0,e.Zc)(i[0],i[i.length-1],null==t?10:t)},t.tickFormat=function(t,i){var e=n();return T(e[0],e[e.length-1],null==t?10:t,i)},t.nice=function(i){null==i&&(i=10);var o,r,h=n(),s=0,u=h.length-1,a=h[s],_=h[u],c=10;for(_<a&&(r=a,a=_,_=r,r=s,s=u,u=r);c-- >0;){if((r=(0,e.lq)(a,_,i))===o)return h[s]=a,h[u]=_,n(h);if(r>0)a=Math.floor(a/r)*r,_=Math.ceil(_/r)*r;else if(r<0)a=Math.ceil(a*r)/r,_=Math.floor(_*r)/r;else break;o=r}return t},t}function b(t,n){t=t.slice();var i,e=0,o=t.length-1,r=t[e],h=t[o];return h<r&&(i=e,e=o,o=i,i=r,r=h,h=i),t[e]=n.floor(r),t[o]=n.ceil(h),t}function M(t){return Math.log(t)}function k(t){return Math.exp(t)}function N(t){return-Math.log(-t)}function E(t){return-Math.exp(-t)}function S(t){return isFinite(t)?+("1e"+t):t<0?0:t}function A(t){return(n,i)=>-t(-n,i)}function P(t){let n,i,o=t(M,k),r=o.domain,h=10;function s(){var e,s;return n=(e=h)===Math.E?Math.log:10===e&&Math.log10||2===e&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e),i=10===(s=h)?S:s===Math.E?Math.exp:t=>Math.pow(s,t),r()[0]<0?(n=A(n),i=A(i),t(N,E)):t(M,k),o}return o.base=function(t){return arguments.length?(h=+t,s()):h},o.domain=function(t){return arguments.length?(r(t),s()):r()},o.ticks=t=>{let o,s,u=r(),a=u[0],_=u[u.length-1],c=_<a;c&&([a,_]=[_,a]);let l=n(a),f=n(_),p=null==t?10:+t,y=[];if(!(h%1)&&f-l<p){if(l=Math.floor(l),f=Math.ceil(f),a>0){for(;l<=f;++l)for(o=1;o<h;++o)if(!((s=l<0?o/i(-l):o*i(l))<a)){if(s>_)break;y.push(s)}}else for(;l<=f;++l)for(o=h-1;o>=1;--o)if(!((s=l>0?o/i(-l):o*i(l))<a)){if(s>_)break;y.push(s)}2*y.length<p&&(y=(0,e.Zc)(a,_,p))}else y=(0,e.Zc)(l,f,Math.min(f-l,p)).map(i);return c?y.reverse():y},o.tickFormat=(t,e)=>{if(null==t&&(t=10),null==e&&(e=10===h?"s":","),"function"!=typeof e&&(h%1||null!=(e=(0,m.Gp)(e)).precision||(e.trim=!0),e=(0,m.GP)(e)),t===1/0)return e;let r=Math.max(1,h*t/o.ticks().length);return t=>{let o=t/i(Math.round(n(t)));return o*h<h-.5&&(o*=h),o<=r?e(t):""}},o.nice=()=>r(b(r(),{floor:t=>i(Math.floor(n(t))),ceil:t=>i(Math.ceil(n(t)))})),o}function q(t){return function(n){return Math.sign(n)*Math.log1p(Math.abs(n/t))}}function C(t){return function(n){return Math.sign(n)*Math.expm1(Math.abs(n))*t}}function z(t){var n=1,i=t(q(1),C(n));return i.constant=function(i){return arguments.length?t(q(n=+i),C(n)):n},w(i)}function D(t){return function(n){return n<0?-Math.pow(-n,t):Math.pow(n,t)}}function R(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function G(t){return t<0?-t*t:t*t}function I(t){var n=t(f,f),i=1;return n.exponent=function(n){return arguments.length?1==(i=+n)?t(f,f):.5===i?t(R,G):t(D(i),D(1/i)):i},w(n)}function O(){var t=I(d());return t.copy=function(){return g(t,O()).exponent(t.exponent())},o.apply(t,arguments),t}function W(){return O.apply(null,arguments).exponent(.5)}function U(t){return Math.sign(t)*t*t}var Z=i(79989),j=i(41193);function V(t){return new Date(t)}function B(t){return t instanceof Date?+t:+new Date(+t)}function H(t,n,i,e,o,r,h,s,u,a){var _=v(),c=_.invert,l=_.domain,f=a(".%L"),p=a(":%S"),y=a("%I:%M"),x=a("%I %p"),d=a("%a %d"),m=a("%b %d"),T=a("%B"),w=a("%Y");function M(t){return(u(t)<t?f:s(t)<t?p:h(t)<t?y:r(t)<t?x:e(t)<t?o(t)<t?d:m:i(t)<t?T:w)(t)}return _.invert=function(t){return new Date(c(t))},_.domain=function(t){return arguments.length?l(Array.from(t,B)):l().map(V)},_.ticks=function(n){var i=l();return t(i[0],i[i.length-1],null==n?10:n)},_.tickFormat=function(t,n){return null==n?M:a(n)},_.nice=function(t){var i=l();return t&&"function"==typeof t.range||(t=n(i[0],i[i.length-1],null==t?10:t)),t?l(b(i,t)):_},_.copy=function(){return g(_,H(t,n,i,e,o,r,h,s,u,a))},_}function X(){return o.apply(H(Z.Cf,Z.yE,Z.he,Z.Ui,Z.Kg,Z.UA,Z.Ag,Z.wX,Z.uc,j.DC).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function Y(){return o.apply(H(Z.$Z,Z.lk,Z.Mb,Z.R6,Z.zX,Z.dA,Z.pz,Z.O0,Z.Hn,j.aL).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function F(){var t,n,i,e,o,r=0,h=1,s=f,u=!1;function a(n){return null==n||isNaN(n*=1)?o:s(0===i?.5:(n=(e(n)-t)*i,u?Math.max(0,Math.min(1,n)):n))}function c(t){return function(n){var i,e;return arguments.length?([i,e]=n,s=t(i,e),a):[s(0),s(1)]}}return a.domain=function(o){return arguments.length?([r,h]=o,t=e(r*=1),n=e(h*=1),i=t===n?0:1/(n-t),a):[r,h]},a.clamp=function(t){return arguments.length?(u=!!t,a):u},a.interpolator=function(t){return arguments.length?(s=t,a):s},a.range=c(_.GW),a.rangeRound=c(_.sH),a.unknown=function(t){return arguments.length?(o=t,a):o},function(o){return e=o,t=o(r),n=o(h),i=t===n?0:1/(n-t),a}}function L(t,n){return n.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function $(){var t=I(F());return t.copy=function(){return L(t,$()).exponent(t.exponent())},r.apply(t,arguments)}function K(){return $.apply(null,arguments).exponent(.5)}function J(){var t,n,i,e,o,r,h,s=0,u=.5,a=1,c=1,l=f,p=!1;function y(t){return isNaN(t*=1)?h:(t=.5+((t=+r(t))-n)*(c*t<c*n?e:o),l(p?Math.max(0,Math.min(1,t)):t))}function x(t){return function(n){var i,e,o;return arguments.length?([i,e,o]=n,l=(0,_.$B)(t,[i,e,o]),y):[l(0),l(.5),l(1)]}}return y.domain=function(h){return arguments.length?([s,u,a]=h,t=r(s*=1),n=r(u*=1),i=r(a*=1),e=t===n?0:.5/(n-t),o=n===i?0:.5/(i-n),c=n<t?-1:1,y):[s,u,a]},y.clamp=function(t){return arguments.length?(p=!!t,y):p},y.interpolator=function(t){return arguments.length?(l=t,y):l},y.range=x(_.GW),y.rangeRound=x(_.sH),y.unknown=function(t){return arguments.length?(h=t,y):h},function(h){return r=h,t=h(s),n=h(u),i=h(a),e=t===n?0:.5/(n-t),o=n===i?0:.5/(i-n),c=n<t?-1:1,y}}function Q(){var t=I(J());return t.copy=function(){return L(t,Q()).exponent(t.exponent())},r.apply(t,arguments)}function tt(){return Q.apply(null,arguments).exponent(.5)}},66550:(t,n,i)=>{function e(t){return function(){return t}}i.d(n,{Wc:()=>g,qr:()=>B,Yu:()=>X,IA:()=>F,Wi:()=>m,PG:()=>T,lU:()=>f,Lx:()=>tr,nV:()=>tl,ux:()=>tf,Xf:()=>tx,GZ:()=>td,UP:()=>tm,dy:()=>tv,n8:()=>x,t$:()=>tk,qI:()=>tN,YW:()=>tT,e9:()=>tE,Re:()=>tS,rM:()=>tw,HR:()=>U,hK:()=>M,BV:()=>k,j:()=>S,yD:()=>A,N8:()=>z,ZK:()=>R,IJ:()=>W});let o=Math.cos,r=Math.sin,h=Math.sqrt,s=Math.PI,u=2*s;var a=i(16596);function _(t){let n=3;return t.digits=function(i){if(!arguments.length)return n;if(null==i)n=null;else{let t=Math.floor(i);if(!(t>=0))throw RangeError(`invalid digits: ${i}`);n=t}return t},()=>new a.wA(n)}function c(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function l(t){this._context=t}function f(t){return new l(t)}function p(t){return t[0]}function y(t){return t[1]}function x(t,n){var i=e(!0),o=null,r=f,h=null,s=_(u);function u(e){var u,a,_,l=(e=c(e)).length,f=!1;for(null==o&&(h=r(_=s())),u=0;u<=l;++u)!(u<l&&i(a=e[u],u,e))===f&&((f=!f)?h.lineStart():h.lineEnd()),f&&h.point(+t(a,u,e),+n(a,u,e));if(_)return h=null,_+""||null}return t="function"==typeof t?t:void 0===t?p:e(t),n="function"==typeof n?n:void 0===n?y:e(n),u.x=function(n){return arguments.length?(t="function"==typeof n?n:e(+n),u):t},u.y=function(t){return arguments.length?(n="function"==typeof t?t:e(+t),u):n},u.defined=function(t){return arguments.length?(i="function"==typeof t?t:e(!!t),u):i},u.curve=function(t){return arguments.length?(r=t,null!=o&&(h=r(o)),u):r},u.context=function(t){return arguments.length?(null==t?o=h=null:h=r(o=t),u):o},u}function g(t,n,i){var o=null,r=e(!0),h=null,s=f,u=null,a=_(l);function l(e){var _,l,f,p,y,x=(e=c(e)).length,g=!1,d=Array(x),v=Array(x);for(null==h&&(u=s(y=a())),_=0;_<=x;++_){if(!(_<x&&r(p=e[_],_,e))===g)if(g=!g)l=_,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),f=_-1;f>=l;--f)u.point(d[f],v[f]);u.lineEnd(),u.areaEnd()}g&&(d[_]=+t(p,_,e),v[_]=+n(p,_,e),u.point(o?+o(p,_,e):d[_],i?+i(p,_,e):v[_]))}if(y)return u=null,y+""||null}function g(){return x().defined(r).curve(s).context(h)}return t="function"==typeof t?t:void 0===t?p:e(+t),n="function"==typeof n?n:void 0===n?e(0):e(+n),i="function"==typeof i?i:void 0===i?y:e(+i),l.x=function(n){return arguments.length?(t="function"==typeof n?n:e(+n),o=null,l):t},l.x0=function(n){return arguments.length?(t="function"==typeof n?n:e(+n),l):t},l.x1=function(t){return arguments.length?(o=null==t?null:"function"==typeof t?t:e(+t),l):o},l.y=function(t){return arguments.length?(n="function"==typeof t?t:e(+t),i=null,l):n},l.y0=function(t){return arguments.length?(n="function"==typeof t?t:e(+t),l):n},l.y1=function(t){return arguments.length?(i=null==t?null:"function"==typeof t?t:e(+t),l):i},l.lineX0=l.lineY0=function(){return g().x(t).y(n)},l.lineY1=function(){return g().x(t).y(i)},l.lineX1=function(){return g().x(o).y(n)},l.defined=function(t){return arguments.length?(r="function"==typeof t?t:e(!!t),l):r},l.curve=function(t){return arguments.length?(s=t,null!=h&&(u=s(h)),l):s},l.context=function(t){return arguments.length?(null==t?h=u=null:u=s(h=t),l):h},l}function d(t){this._curve=t}Array.prototype.slice,l.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n)}}},d.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,n){this._curve.point(n*Math.sin(t),-(n*Math.cos(t)))}};class v{constructor(t,n){this._context=t,this._x=n}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,n,t,n):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+n)/2,t,this._y0,t,n)}this._x0=t,this._y0=n}}function m(t){return new v(t,!0)}function T(t){return new v(t,!1)}function w(t){return t.source}function b(t){return t.target}h(3);let M={draw(t,n){let i=h(n/s);t.moveTo(i,0),t.arc(0,0,i,0,u)}},k={draw(t,n){let i=h(n/5)/2;t.moveTo(-3*i,-i),t.lineTo(-i,-i),t.lineTo(-i,-3*i),t.lineTo(i,-3*i),t.lineTo(i,-i),t.lineTo(3*i,-i),t.lineTo(3*i,i),t.lineTo(i,i),t.lineTo(i,3*i),t.lineTo(-i,3*i),t.lineTo(-i,i),t.lineTo(-3*i,i),t.closePath()}},N=h(1/3),E=2*N,S={draw(t,n){let i=h(n/E),e=i*N;t.moveTo(0,-i),t.lineTo(e,0),t.lineTo(0,i),t.lineTo(-e,0),t.closePath()}},A={draw(t,n){let i=h(n),e=-i/2;t.rect(e,e,i,i)}},P=r(s/10)/r(7*s/10),q=r(u/10)*P,C=-o(u/10)*P,z={draw(t,n){let i=h(.8908130915292852*n),e=q*i,s=C*i;t.moveTo(0,-i),t.lineTo(e,s);for(let n=1;n<5;++n){let h=u*n/5,a=o(h),_=r(h);t.lineTo(_*i,-a*i),t.lineTo(a*e-_*s,_*e+a*s)}t.closePath()}},D=h(3),R={draw(t,n){let i=-h(n/(3*D));t.moveTo(0,2*i),t.lineTo(-D*i,-i),t.lineTo(D*i,-i),t.closePath()}};h(3);let G=h(3)/2,I=1/h(12),O=(I/2+1)*3,W={draw(t,n){let i=h(n/O),e=i/2,o=i*I,r=i*I+i,s=-e;t.moveTo(e,o),t.lineTo(e,r),t.lineTo(s,r),t.lineTo(-.5*e-G*o,G*e+-.5*o),t.lineTo(-.5*e-G*r,G*e+-.5*r),t.lineTo(-.5*s-G*r,G*s+-.5*r),t.lineTo(-.5*e+G*o,-.5*o-G*e),t.lineTo(-.5*e+G*r,-.5*r-G*e),t.lineTo(-.5*s+G*r,-.5*r-G*s),t.closePath()}};function U(t,n){let i=null,o=_(r);function r(){let e;if(i||(i=e=o()),t.apply(this,arguments).draw(i,+n.apply(this,arguments)),e)return i=null,e+""||null}return t="function"==typeof t?t:e(t||M),n="function"==typeof n?n:e(void 0===n?64:+n),r.type=function(n){return arguments.length?(t="function"==typeof n?n:e(n),r):t},r.size=function(t){return arguments.length?(n="function"==typeof t?t:e(+t),r):n},r.context=function(t){return arguments.length?(i=null==t?null:t,r):i},r}function Z(){}function j(t,n,i){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+i)/6)}function V(t){this._context=t}function B(t){return new V(t)}function H(t){this._context=t}function X(t){return new H(t)}function Y(t){this._context=t}function F(t){return new Y(t)}function L(t,n){this._basis=new V(t),this._beta=n}function $(t,n,i){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-i),t._x2,t._y2)}function K(t,n){this._context=t,this._k=(1-n)/6}function J(t,n){this._context=t,this._k=(1-n)/6}function Q(t,n){this._context=t,this._k=(1-n)/6}function tt(t,n,i){var e=t._x1,o=t._y1,r=t._x2,h=t._y2;if(t._l01_a>1e-12){var s=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,u=3*t._l01_a*(t._l01_a+t._l12_a);e=(e*s-t._x0*t._l12_2a+t._x2*t._l01_2a)/u,o=(o*s-t._y0*t._l12_2a+t._y2*t._l01_2a)/u}if(t._l23_a>1e-12){var a=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,_=3*t._l23_a*(t._l23_a+t._l12_a);r=(r*a+t._x1*t._l23_2a-n*t._l12_2a)/_,h=(h*a+t._y1*t._l23_2a-i*t._l12_2a)/_}t._context.bezierCurveTo(e,o,r,h,t._x2,t._y2)}function tn(t,n){this._context=t,this._alpha=n}function ti(t,n){this._context=t,this._alpha=n}function te(t,n){this._context=t,this._alpha=n}function to(t){this._context=t}function tr(t){return new to(t)}function th(t,n,i){var e=t._x1-t._x0,o=n-t._x1,r=(t._y1-t._y0)/(e||o<0&&-0),h=(i-t._y1)/(o||e<0&&-0);return((r<0?-1:1)+(h<0?-1:1))*Math.min(Math.abs(r),Math.abs(h),.5*Math.abs((r*o+h*e)/(e+o)))||0}function ts(t,n){var i=t._x1-t._x0;return i?(3*(t._y1-t._y0)/i-n)/2:n}function tu(t,n,i){var e=t._x0,o=t._y0,r=t._x1,h=t._y1,s=(r-e)/3;t._context.bezierCurveTo(e+s,o+s*n,r-s,h-s*i,r,h)}function ta(t){this._context=t}function t_(t){this._context=new tc(t)}function tc(t){this._context=t}function tl(t){return new ta(t)}function tf(t){return new t_(t)}function tp(t){this._context=t}function ty(t){var n,i,e=t.length-1,o=Array(e),r=Array(e),h=Array(e);for(o[0]=0,r[0]=2,h[0]=t[0]+2*t[1],n=1;n<e-1;++n)o[n]=1,r[n]=4,h[n]=4*t[n]+2*t[n+1];for(o[e-1]=2,r[e-1]=7,h[e-1]=8*t[e-1]+t[e],n=1;n<e;++n)i=o[n]/r[n-1],r[n]-=i,h[n]-=i*h[n-1];for(o[e-1]=h[e-1]/r[e-1],n=e-2;n>=0;--n)o[n]=(h[n]-o[n+1])/r[n];for(n=0,r[e-1]=(t[e]+o[e-1])/2;n<e-1;++n)r[n]=2*t[n+1]-o[n+1];return[o,r]}function tx(t){return new tp(t)}function tg(t,n){this._context=t,this._t=n}function td(t){return new tg(t,.5)}function tv(t){return new tg(t,0)}function tm(t){return new tg(t,1)}function tT(t,n){if((o=t.length)>1)for(var i,e,o,r=1,h=t[n[0]],s=h.length;r<o;++r)for(e=h,h=t[n[r]],i=0;i<s;++i)h[i][1]+=h[i][0]=isNaN(e[i][1])?e[i][0]:e[i][1]}function tw(t){for(var n=t.length,i=Array(n);--n>=0;)i[n]=n;return i}function tb(t,n){return t[n]}function tM(t){let n=[];return n.key=t,n}function tk(){var t=e([]),n=tw,i=tT,o=tb;function r(e){var r,h,s=Array.from(t.apply(this,arguments),tM),u=s.length,a=-1;for(let t of e)for(r=0,++a;r<u;++r)(s[r][a]=[0,+o(t,s[r].key,a,e)]).data=t;for(r=0,h=c(n(s));r<u;++r)s[h[r]].index=r;return i(s,h),s}return r.keys=function(n){return arguments.length?(t="function"==typeof n?n:e(Array.from(n)),r):t},r.value=function(t){return arguments.length?(o="function"==typeof t?t:e(+t),r):o},r.order=function(t){return arguments.length?(n=null==t?tw:"function"==typeof t?t:e(Array.from(t)),r):n},r.offset=function(t){return arguments.length?(i=null==t?tT:t,r):i},r}function tN(t,n){if((e=t.length)>0){for(var i,e,o,r=0,h=t[0].length;r<h;++r){for(o=i=0;i<e;++i)o+=t[i][r][1]||0;if(o)for(i=0;i<e;++i)t[i][r][1]/=o}tT(t,n)}}function tE(t,n){if((i=t.length)>0){for(var i,e=0,o=t[n[0]],r=o.length;e<r;++e){for(var h=0,s=0;h<i;++h)s+=t[h][e][1]||0;o[e][1]+=o[e][0]=-s/2}tT(t,n)}}function tS(t,n){if((o=t.length)>0&&(e=(i=t[n[0]]).length)>0){for(var i,e,o,r=0,h=1;h<e;++h){for(var s=0,u=0,a=0;s<o;++s){for(var _=t[n[s]],c=_[h][1]||0,l=(c-(_[h-1][1]||0))/2,f=0;f<s;++f){var p=t[n[f]];l+=(p[h][1]||0)-(p[h-1][1]||0)}u+=c,a+=l*c}i[h-1][1]+=i[h-1][0]=r,u&&(r-=a/u)}i[h-1][1]+=i[h-1][0]=r,tT(t,n)}}V.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:j(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:j(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},H.prototype={areaStart:Z,areaEnd:Z,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=n;break;case 1:this._point=2,this._x3=t,this._y3=n;break;case 2:this._point=3,this._x4=t,this._y4=n,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:j(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},Y.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var i=(this._x0+4*this._x1+t)/6,e=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(i,e):this._context.moveTo(i,e);break;case 3:this._point=4;default:j(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},L.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,i=t.length-1;if(i>0)for(var e,o=t[0],r=n[0],h=t[i]-o,s=n[i]-r,u=-1;++u<=i;)e=u/i,this._basis.point(this._beta*t[u]+(1-this._beta)*(o+e*h),this._beta*n[u]+(1-this._beta)*(r+e*s));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}},function t(n){function i(t){return 1===n?new V(t):new L(t,n)}return i.beta=function(n){return t(+n)},i}(.85),K.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:$(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:$(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function i(t){return new K(t,n)}return i.tension=function(n){return t(+n)},i}(0),J.prototype={areaStart:Z,areaEnd:Z,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:$(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function i(t){return new J(t,n)}return i.tension=function(n){return t(+n)},i}(0),Q.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:$(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function i(t){return new Q(t,n)}return i.tension=function(n){return t(+n)},i}(0),tn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t*=1,n*=1,this._point){var i=this._x2-t,e=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(i*i+e*e,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:tt(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function i(t){return n?new tn(t,n):new K(t,0)}return i.alpha=function(n){return t(+n)},i}(.5),ti.prototype={areaStart:Z,areaEnd:Z,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){if(t*=1,n*=1,this._point){var i=this._x2-t,e=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(i*i+e*e,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:tt(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function i(t){return n?new ti(t,n):new J(t,0)}return i.alpha=function(n){return t(+n)},i}(.5),te.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t*=1,n*=1,this._point){var i=this._x2-t,e=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(i*i+e*e,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:tt(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}},function t(n){function i(t){return n?new te(t,n):new Q(t,0)}return i.alpha=function(n){return t(+n)},i}(.5),to.prototype={areaStart:Z,areaEnd:Z,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,n){t*=1,n*=1,this._point?this._context.lineTo(t,n):(this._point=1,this._context.moveTo(t,n))}},ta.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:tu(this,this._t0,ts(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var i=NaN;if(n*=1,(t*=1)!==this._x1||n!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,tu(this,ts(this,i=th(this,t,n)),i);break;default:tu(this,this._t0,i=th(this,t,n))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n,this._t0=i}}},(t_.prototype=Object.create(ta.prototype)).point=function(t,n){ta.prototype.point.call(this,n,t)},tc.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,i,e,o,r){this._context.bezierCurveTo(n,t,e,i,r,o)}},tp.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,n=this._y,i=t.length;if(i)if(this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]),2===i)this._context.lineTo(t[1],n[1]);else for(var e=ty(t),o=ty(n),r=0,h=1;h<i;++r,++h)this._context.bezierCurveTo(e[0][r],o[0][r],e[1][r],o[1][r],t[h],n[h]);(this._line||0!==this._line&&1===i)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,n){this._x.push(+t),this._y.push(+n)}},tg.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,n){switch(t*=1,n*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,n),this._context.lineTo(t,n);else{var i=this._x*(1-this._t)+t*this._t;this._context.lineTo(i,this._y),this._context.lineTo(i,n)}}this._x=t,this._y=n}}}};