{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/admin/addons/[name]", "regex": "^/api/admin/addons/([^/]+?)(?:/)?$", "routeKeys": {"nxtPname": "nxtPname"}, "namedRegex": "^/api/admin/addons/(?<nxtPname>[^/]+?)(?:/)?$"}, {"page": "/api/admin/addons/[name]/config", "regex": "^/api/admin/addons/([^/]+?)/config(?:/)?$", "routeKeys": {"nxtPname": "nxtPname"}, "namedRegex": "^/api/admin/addons/(?<nxtPname>[^/]+?)/config(?:/)?$"}, {"page": "/api/admin/addons/[name]/flow", "regex": "^/api/admin/addons/([^/]+?)/flow(?:/)?$", "routeKeys": {"nxtPname": "nxtPname"}, "namedRegex": "^/api/admin/addons/(?<nxtPname>[^/]+?)/flow(?:/)?$"}, {"page": "/api/admin/applications-builder/[id]", "regex": "^/api/admin/applications\\-builder/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/applications\\-builder/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/errors/[id]", "regex": "^/api/admin/errors/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/errors/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/discord/channels/[channelId]", "regex": "^/api/discord/channels/([^/]+?)(?:/)?$", "routeKeys": {"nxtPchannelId": "nxtPchannelId"}, "namedRegex": "^/api/discord/channels/(?<nxtPchannelId>[^/]+?)(?:/)?$"}, {"page": "/api/discord/channels/[channelId]/messages", "regex": "^/api/discord/channels/([^/]+?)/messages(?:/)?$", "routeKeys": {"nxtPchannelId": "nxtPchannelId"}, "namedRegex": "^/api/discord/channels/(?<nxtPchannelId>[^/]+?)/messages(?:/)?$"}, {"page": "/api/discord/roles/[roleId]", "regex": "^/api/discord/roles/([^/]+?)(?:/)?$", "routeKeys": {"nxtProleId": "nxtProleId"}, "namedRegex": "^/api/discord/roles/(?<nxtProleId>[^/]+?)(?:/)?$"}, {"page": "/api/discord/tickets/[id]", "regex": "^/api/discord/tickets/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/discord/tickets/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/discord/tickets/[id]/transcript", "regex": "^/api/discord/tickets/([^/]+?)/transcript(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/discord/tickets/(?<nxtPid>[^/]+?)/transcript(?:/)?$"}, {"page": "/api/experimental/flags/[feature]", "regex": "^/api/experimental/flags/([^/]+?)(?:/)?$", "routeKeys": {"nxtPfeature": "nxtPfeature"}, "namedRegex": "^/api/experimental/flags/(?<nxtPfeature>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/admin/addons", "regex": "^/admin/addons(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/addons(?:/)?$"}, {"page": "/admin/applications", "regex": "^/admin/applications(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/applications(?:/)?$"}, {"page": "/admin/applications-builder", "regex": "^/admin/applications\\-builder(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/applications\\-builder(?:/)?$"}, {"page": "/admin/commands", "regex": "^/admin/commands(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/commands(?:/)?$"}, {"page": "/admin/experimental/addon-builder", "regex": "^/admin/experimental/addon\\-builder(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/experimental/addon\\-builder(?:/)?$"}, {"page": "/admin/guilds", "regex": "^/admin/guilds(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/guilds(?:/)?$"}, {"page": "/applications", "regex": "^/applications(?:/)?$", "routeKeys": {}, "namedRegex": "^/applications(?:/)?$"}, {"page": "/experimental", "regex": "^/experimental(?:/)?$", "routeKeys": {}, "namedRegex": "^/experimental(?:/)?$"}, {"page": "/experimental/addon-builder", "regex": "^/experimental/addon\\-builder(?:/)?$", "routeKeys": {}, "namedRegex": "^/experimental/addon\\-builder(?:/)?$"}, {"page": "/gameservers", "regex": "^/gameservers(?:/)?$", "routeKeys": {}, "namedRegex": "^/gameservers(?:/)?$"}, {"page": "/overview", "regex": "^/overview(?:/)?$", "routeKeys": {}, "namedRegex": "^/overview(?:/)?$"}, {"page": "/signin", "regex": "^/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/signin(?:/)?$"}, {"page": "/tickets", "regex": "^/tickets(?:/)?$", "routeKeys": {}, "namedRegex": "^/tickets(?:/)?$"}, {"page": "/unauthorized", "regex": "^/unauthorized(?:/)?$", "routeKeys": {}, "namedRegex": "^/unauthorized(?:/)?$"}], "dataRoutes": [{"page": "/", "dataRouteRegex": "^/_next/data/yWtL1hdX\\-KL6x0BA4YGw_/index\\.json$"}, {"page": "/admin/applications", "dataRouteRegex": "^/_next/data/yWtL1hdX\\-KL6x0BA4YGw_/admin/applications\\.json$"}, {"page": "/admin/guilds", "dataRouteRegex": "^/_next/data/yWtL1hdX\\-KL6x0BA4YGw_/admin/guilds\\.json$"}, {"page": "/applications", "dataRouteRegex": "^/_next/data/yWtL1hdX\\-KL6x0BA4YGw_/applications\\.json$"}, {"page": "/experimental", "dataRouteRegex": "^/_next/data/yWtL1hdX\\-KL6x0BA4YGw_/experimental\\.json$"}, {"page": "/experimental/addon-builder", "dataRouteRegex": "^/_next/data/yWtL1hdX\\-KL6x0BA4YGw_/experimental/addon-builder\\.json$"}, {"page": "/gameservers", "dataRouteRegex": "^/_next/data/yWtL1hdX\\-KL6x0BA4YGw_/gameservers\\.json$"}, {"page": "/overview", "dataRouteRegex": "^/_next/data/yWtL1hdX\\-KL6x0BA4YGw_/overview\\.json$"}, {"page": "/tickets", "dataRouteRegex": "^/_next/data/yWtL1hdX\\-KL6x0BA4YGw_/tickets\\.json$"}], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}