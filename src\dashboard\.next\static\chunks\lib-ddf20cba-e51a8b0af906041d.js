"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7897],{6944:(e,t,n)=>{function r(e){return Number.isFinite(e)}function o(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}n.d(t,{F:()=>o,H:()=>r})},7501:(e,t,n)=>{n.d(t,{d:()=>k,M:()=>x});var r=n(50985),o=n.n(r),a=e=>e,i={},u=e=>e===i,l=e=>function t(){return 0==arguments.length||1==arguments.length&&u(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},s=(e,t)=>1===e?t:l(function(){for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];var a=r.filter(e=>e!==i).length;return a>=e?t(...r):s(e-a,l(function(){for(var e=arguments.length,n=Array(e),o=0;o<e;o++)n[o]=arguments[o];return t(...r.map(e=>u(e)?n.shift():e),...n)}))}),p=e=>s(e.length,e),c=(e,t)=>{for(var n=[],r=e;r<t;++r)n[r-e]=r;return n},d=p((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),f=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(!t.length)return a;var r=t.reverse(),o=r[0],i=r.slice(1);return function(){return i.reduce((e,t)=>t(e),o(...arguments))}},h=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),v=e=>{var t=null,n=null;return function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return t&&o.every((e,n)=>{var r;return e===(null==(r=t)?void 0:r[n])})?n:(t=o,n=e(...o))}};function g(e){var t;return 0===e?1:Math.floor(new(o())(e).abs().log(10).toNumber())+1}function m(e,t,n){for(var r=new(o())(e),a=0,i=[];r.lt(t)&&a<1e5;)i.push(r.toNumber()),r=r.add(n),a++;return i}p((e,t,n)=>{var r=+e;return r+n*(t-r)}),p((e,t,n)=>{var r=t-e;return(n-e)/(r=r||1/0)}),p((e,t,n)=>{var r=t-e;return Math.max(0,Math.min(1,(n-e)/(r=r||1/0)))});var C=e=>{var[t,n]=e,[r,o]=[t,n];return t>n&&([r,o]=[n,t]),[r,o]},y=(e,t,n)=>{if(e.lte(0))return new(o())(0);var r=g(e.toNumber()),a=new(o())(10).pow(r),i=e.div(a),u=1!==r?.05:.1,l=new(o())(Math.ceil(i.div(u).toNumber())).add(n).mul(u).mul(a);return new(o())(t?l.toNumber():Math.ceil(l.toNumber()))},b=(e,t,n)=>{var r=new(o())(1),a=new(o())(e);if(!a.isint()&&n){var i=Math.abs(e);i<1?(r=new(o())(10).pow(g(e)-1),a=new(o())(Math.floor(a.div(r).toNumber())).mul(r)):i>1&&(a=new(o())(Math.floor(e)))}else 0===e?a=new(o())(Math.floor((t-1)/2)):n||(a=new(o())(Math.floor(e)));var u=Math.floor((t-1)/2);return f(d(e=>a.add(new(o())(e-u).mul(r)).toNumber()),c)(0,t)},w=function(e,t,n,r){var a,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(n-1)))return{step:new(o())(0),tickMin:new(o())(0),tickMax:new(o())(0)};var u=y(new(o())(t).sub(e).div(n-1),r,i),l=Math.ceil((a=e<=0&&t>=0?new(o())(0):(a=new(o())(e).add(t).div(2)).sub(new(o())(a).mod(u))).sub(e).div(u).toNumber()),s=Math.ceil(new(o())(t).sub(a).div(u).toNumber()),p=l+s+1;return p>n?w(e,t,n,r,i+1):(p<n&&(s=t>0?s+(n-p):s,l=t>0?l:l+(n-p)),{step:u,tickMin:a.sub(new(o())(l).mul(u)),tickMax:a.add(new(o())(s).mul(u))})},k=v(function(e){var[t,n]=e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,a=!(arguments.length>2)||void 0===arguments[2]||arguments[2],i=Math.max(r,2),[u,l]=C([t,n]);if(u===-1/0||l===1/0){var s=l===1/0?[u,...c(0,r-1).map(()=>1/0)]:[...c(0,r-1).map(()=>-1/0),l];return t>n?h(s):s}if(u===l)return b(u,r,a);var{step:p,tickMin:d,tickMax:f}=w(u,l,i,a,0),v=m(d,f.add(new(o())(.1).mul(p)),p);return t>n?h(v):v}),x=v(function(e,t){var[n,r]=e,a=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[i,u]=C([n,r]);if(i===-1/0||u===1/0)return[n,r];if(i===u)return[i];var l=Math.max(t,2),s=y(new(o())(u).sub(i).div(l-1),a,0),p=[...m(new(o())(i),new(o())(u),s),u];return!1===a&&(p=p.map(e=>Math.round(e))),n>r?h(p):p})},14901:(e,t,n)=>{function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function o(e,t){var n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach(function(t){var r,o,a;r=e,o=t,a=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in r?Object.defineProperty(r,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[o]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({},e);return Object.keys(t).reduce((e,n)=>(void 0===e[n]&&void 0!==t[n]&&(e[n]=t[n]),e),n)}n.d(t,{e:()=>o})},32037:(e,t,n)=>{n.d(t,{w:()=>r});var r=e=>{var t=e.currentTarget.getBoundingClientRect(),n=t.width/e.currentTarget.offsetWidth,r=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/n),chartY:Math.round((e.clientY-t.top)/r)}}},33048:(e,t,n)=>{n.d(t,{JH:()=>i,f5:()=>l,v1:()=>s});var r=n(33053),o=n(93833),a=n(6944);function i(e){if(Array.isArray(e)&&2===e.length){var[t,n]=e;if((0,a.H)(t)&&(0,a.H)(n))return!0}return!1}function u(e,t,n){return n?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}function l(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var n,r,[o,u]=e;if((0,a.H)(o))n=o;else if("function"==typeof o)return;if((0,a.H)(u))r=u;else if("function"==typeof u)return;var l=[n,r];if(i(l))return l}}function s(e,t,n){if(n||null!=t){if("function"==typeof e&&null!=t)try{var a=e(t,n);if(i(a))return u(a,t,n)}catch(e){}if(Array.isArray(e)&&2===e.length){var l,s,[p,c]=e;if("auto"===p)null!=t&&(l=Math.min(...t));else if((0,o.Et)(p))l=p;else if("function"==typeof p)try{null!=t&&(l=p(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof p&&r.IH.test(p)){var d=r.IH.exec(p);if(null==d||null==t)l=void 0;else{var f=+d[1];l=t[0]-f}}else l=null==t?void 0:t[0];if("auto"===c)null!=t&&(s=Math.max(...t));else if((0,o.Et)(c))s=c;else if("function"==typeof c)try{null!=t&&(s=c(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof c&&r.qx.test(c)){var h=r.qx.exec(c);if(null==h||null==t)s=void 0;else{var v=+h[1];s=t[1]+v}}else s=null==t?void 0:t[1];var g=[l,s];if(i(g))return null==t?g:u(g,t,n)}}}},38837:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(94285),o=n(92735),a=n(68101),i=n(34975),u=n(6944);function l(){var e=(0,o.j)(),[t,n]=(0,r.useState)(null),l=(0,o.G)(a.et);return(0,r.useEffect)(()=>{if(null!=t){var n=t.getBoundingClientRect().width/t.offsetWidth;(0,u.H)(n)&&n!==l&&e((0,i.hF)(n))}},[t,e,l]),n}},69634:(e,t,n)=>{n.d(t,{eK:()=>l});var r=n(3638),o=n(93833),a="recharts-tooltip-wrapper",i={visibility:"hidden"};function u(e){var{allowEscapeViewBox:t,coordinate:n,key:r,offsetTopLeft:a,position:i,reverseDirection:u,tooltipDimension:l,viewBox:s,viewBoxDimension:p}=e;if(i&&(0,o.Et)(i[r]))return i[r];var c=n[r]-l-(a>0?a:0),d=n[r]+a;if(t[r])return u[r]?c:d;var f=s[r];return null==f?0:u[r]?c<f?Math.max(d,f):Math.max(c,f):null==p?0:d+l>f+p?Math.max(c,f):Math.max(d,f)}function l(e){var t,n,l,{allowEscapeViewBox:s,coordinate:p,offsetTopLeft:c,position:d,reverseDirection:f,tooltipBox:h,useTranslate3d:v,viewBox:g}=e;return{cssProperties:t=h.height>0&&h.width>0&&p?function(e){var{translateX:t,translateY:n,useTranslate3d:r}=e;return{transform:r?"translate3d(".concat(t,"px, ").concat(n,"px, 0)"):"translate(".concat(t,"px, ").concat(n,"px)")}}({translateX:n=u({allowEscapeViewBox:s,coordinate:p,key:"x",offsetTopLeft:c,position:d,reverseDirection:f,tooltipDimension:h.width,viewBox:g,viewBoxDimension:g.width}),translateY:l=u({allowEscapeViewBox:s,coordinate:p,key:"y",offsetTopLeft:c,position:d,reverseDirection:f,tooltipDimension:h.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:v}):i,cssClasses:function(e){var{coordinate:t,translateX:n,translateY:i}=e;return(0,r.$)(a,{["".concat(a,"-right")]:(0,o.Et)(n)&&t&&(0,o.Et)(t.x)&&n>=t.x,["".concat(a,"-left")]:(0,o.Et)(n)&&t&&(0,o.Et)(t.x)&&n<t.x,["".concat(a,"-bottom")]:(0,o.Et)(i)&&t&&(0,o.Et)(t.y)&&i>=t.y,["".concat(a,"-top")]:(0,o.Et)(i)&&t&&(0,o.Et)(t.y)&&i<t.y})}({translateX:n,translateY:l,coordinate:p})}}},76378:(e,t,n)=>{n.d(t,{n:()=>a});var r=n(94285),o=n(93833);function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",n=(0,r.useRef)((0,o.NF)(t)),a=(0,r.useRef)(e);return a.current!==e&&(n.current=(0,o.NF)(t),a.current=e),n.current}},82766:(e,t,n)=>{n.d(t,{s:()=>a});var r=n(93819),o=n.n(r);function a(e,t,n){return!0===t?o()(e,n):"function"==typeof t?o()(e,t):e}},83733:(e,t,n)=>{n.d(t,{QQ:()=>o,VU:()=>i,XC:()=>p,_U:()=>l,j2:()=>u});var r=n(94285),o=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],a=["points","pathLength"],i={svg:["viewBox","children"],polygon:a,polyline:a},u=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],l=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,r.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var o={};return Object.keys(n).forEach(e=>{u.includes(e)&&(o[e]=t||(t=>n[e](n,t)))}),o},s=(e,t,n)=>r=>(e(t,n,r),null),p=(e,t,n)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var r=null;return Object.keys(e).forEach(o=>{var a=e[o];u.includes(o)&&"function"==typeof a&&(r||(r={}),r[o]=s(a,t,n))}),r}},87684:(e,t,n)=>{function r(e){return({dispatch:t,getState:n})=>r=>o=>"function"==typeof o?o(t,n,e):r(o)}n.d(t,{P:()=>o,Y:()=>a});var o=r(),a=r},88622:(e,t,n)=>{n.d(t,{V:()=>o});var r=n(94285);function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,n]=(0,r.useState)({height:0,left:0,top:0,width:0}),o=(0,r.useCallback)(e=>{if(null!=e){var r=e.getBoundingClientRect(),o={height:r.height,left:r.left,top:r.top,width:r.width};(Math.abs(o.height-t.height)>1||Math.abs(o.left-t.left)>1||Math.abs(o.top-t.top)>1||Math.abs(o.width-t.width)>1)&&n({height:o.height,left:o.left,top:o.top,width:o.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,o]}},95324:(e,t,n)=>{n.d(t,{B:()=>r});function r(e,t,n){if(t<1)return[];if(1===t&&void 0===n)return e;for(var r=[],o=0;o<e.length;o+=t)if(void 0!==n&&!0!==n(e[o]))return;else r.push(e[o]);return r}}}]);