"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4754],{12333:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return d},APP_DIR_ALIAS:function(){return y},CACHE_ONE_YEAR:function(){return h},DOT_NEXT_ALIAS:function(){return b},ESLINT_DEFAULT_DIRS:function(){return Q},GSP_NO_RETURNED_VALUE:function(){return V},GSSP_COMPONENT_MEMBER_ERROR:function(){return K},GSSP_NO_RETURNED_VALUE:function(){return k},INFINITE_CACHE:function(){return m},INSTRUMENTATION_HOOK_FILENAME:function(){return C},MATCHED_PATH_HEADER:function(){return o},MIDDLEWARE_FILENAME:function(){return N},MIDDLEWARE_LOCATION_REGEXP:function(){return O},NEXT_BODY_SUFFIX:function(){return E},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return I},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return R},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return g},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return P},NEXT_CACHE_TAGS_HEADER:function(){return f},NEXT_CACHE_TAG_MAX_ITEMS:function(){return S},NEXT_CACHE_TAG_MAX_LENGTH:function(){return T},NEXT_DATA_SUFFIX:function(){return l},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return p},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return A},NON_STANDARD_NODE_ENV:function(){return $},PAGES_DIR_ALIAS:function(){return x},PRERENDER_REVALIDATE_HEADER:function(){return i},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return a},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return F},ROOT_DIR_ALIAS:function(){return v},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return M},RSC_ACTION_ENCRYPTION_ALIAS:function(){return w},RSC_ACTION_PROXY_ALIAS:function(){return L},RSC_ACTION_VALIDATE_ALIAS:function(){return j},RSC_CACHE_WRAPPER_ALIAS:function(){return X},RSC_MOD_REF_PROXY_ALIAS:function(){return D},RSC_PREFETCH_SUFFIX:function(){return s},RSC_SEGMENTS_DIR_SUFFIX:function(){return u},RSC_SEGMENT_SUFFIX:function(){return _},RSC_SUFFIX:function(){return c},SERVER_PROPS_EXPORT_ERROR:function(){return Y},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return H},SERVER_PROPS_SSG_CONFLICT:function(){return U},SERVER_RUNTIME:function(){return q},SSG_FALLBACK_EXPORT_ERROR:function(){return z},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return G},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return B},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return W},WEBPACK_LAYERS:function(){return Z},WEBPACK_RESOURCE_QUERIES:function(){return ee}});let r="nxtP",n="nxtI",o="x-matched-path",i="x-prerender-revalidate",a="x-prerender-revalidate-if-generated",s=".prefetch.rsc",u=".segments",_=".segment.rsc",c=".rsc",d=".action",l=".json",p=".meta",E=".body",f="x-next-cache-tags",R="x-next-revalidated-tags",g="x-next-revalidate-tag-token",A="next-resume",S=128,T=256,P=1024,I="_N_T_",h=31536e3,m=0xfffffffe,N="middleware",O=`(?:src/)?${N}`,C="instrumentation",x="private-next-pages",b="private-dot-next",v="private-next-root-dir",y="private-next-app-dir",D="private-next-rsc-mod-ref-proxy",j="private-next-rsc-action-validate",L="private-next-rsc-server-reference",X="private-next-rsc-cache-wrapper",w="private-next-rsc-action-encryption",M="private-next-rsc-action-client-wrapper",F="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",G="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",H="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",U="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",B="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",Y="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",V="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",k="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",W="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",K="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",$='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',z="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",Q=["app","pages","components","lib","src"],q={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},J={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},Z={...J,GROUP:{builtinReact:[J.reactServerComponents,J.actionBrowser],serverOnly:[J.reactServerComponents,J.actionBrowser,J.instrument,J.middleware],neutralTarget:[J.apiNode,J.apiEdge],clientOnly:[J.serverSideRendering,J.appPagesBrowser],bundled:[J.reactServerComponents,J.actionBrowser,J.serverSideRendering,J.appPagesBrowser,J.shared,J.instrument,J.middleware],appPages:[J.reactServerComponents,J.serverSideRendering,J.appPagesBrowser,J.actionBrowser]}},ee={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},26727:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(34007),o=r(94513),i=n._(r(94285)),a=r(10989);async function s(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,a.loadGetInitialProps)(t,r)}}class u extends i.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,o.jsx)(e,{...t})}}u.origGetInitialProps=s,u.getInitialProps=s,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32484:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{copyNextErrorCode:function(){return n},createDigestWithErrorCode:function(){return r},extractNextErrorCode:function(){return o}});let r=(e,t)=>"object"==typeof e&&null!==e&&"__NEXT_ERROR_CODE"in e?`${t}@${e.__NEXT_ERROR_CODE}`:t,n=(e,t)=>{let r=o(e);r&&"object"==typeof t&&null!==t&&Object.defineProperty(t,"__NEXT_ERROR_CODE",{value:r,enumerable:!1,configurable:!0})},o=e=>"object"==typeof e&&null!==e&&"__NEXT_ERROR_CODE"in e&&"string"==typeof e.__NEXT_ERROR_CODE?e.__NEXT_ERROR_CODE:"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest?e.digest.split("@").find(e=>e.startsWith("E")):void 0},39136:(e,t,r)=>{e.exports=r(13876)},39474:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let n=r(34007),o=r(94513),i=n._(r(94285)),a=n._(r(51570)),s={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function u(e){let{req:t,res:r,err:n}=e;return{statusCode:r&&r.statusCode?r.statusCode:n?n.statusCode:404,hostname:window.location.hostname}}let _={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class c extends i.default.Component{render(){let{statusCode:e,withDarkMode:t=!0}=this.props,r=this.props.title||s[e]||"An unexpected error has occurred";return(0,o.jsxs)("div",{style:_.error,children:[(0,o.jsx)(a.default,{children:(0,o.jsx)("title",{children:e?e+": "+r:"Application error: a client-side exception has occurred"})}),(0,o.jsxs)("div",{style:_.desc,children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(t?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?(0,o.jsx)("h1",{className:"next-error-h1",style:_.h1,children:e}):null,(0,o.jsx)("div",{style:_.wrap,children:(0,o.jsxs)("h2",{style:_.h2,children:[this.props.title||e?r:(0,o.jsxs)(o.Fragment,{children:["Application error: a client-side exception has occurred"," ",!!this.props.hostname&&(0,o.jsxs)(o.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}c.displayName="ErrorPage",c.getInitialProps=u,c.origGetInitialProps=u,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67207:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},getProperError:function(){return i}});let n=r(72215);function o(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}function i(e){return o(e)?e:Object.defineProperty(Error((0,n.isPlainObject)(e)?function(e){let t=new WeakSet;return JSON.stringify(e,(e,r)=>{if("object"==typeof r&&null!==r){if(t.has(r))return"[Circular]";t.add(r)}return r})}(e):e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},82797:(e,t)=>{function r(e){return"/api"===e||!!(null==e?void 0:e.startsWith("/api/"))}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAPIRoute",{enumerable:!0,get:function(){return r}})}}]);