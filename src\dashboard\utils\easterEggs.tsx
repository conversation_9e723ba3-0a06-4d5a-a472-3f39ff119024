import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  VStack,
  Icon,
  HStack,
  useDisclosure,
  Box,
} from '@chakra-ui/react';
import { FaTrophy, FaStar, FaCrown } from 'react-icons/fa';
import React from 'react';

// Easter egg IDs and their requirements
export const EASTER_EGGS = {
  ROBOT_MASTER: 'robot_master',           // Click robot 30 times on sign in
  COLOR_WIZARD: 'color_wizard',           // Click all gradient texts in sequence
  TICKET_WHISPERER: 'ticket_whisperer',   // Find hidden ticket message
  SERVER_SAGE: 'server_sage',             // Click server status indicators in specific order
  ADMIN_ARCHAEOLOGIST: 'admin_archaeologist', // Find hidden admin panel pattern
  OVERVIEW_ORACLE: 'overview_oracle',      // Decode hidden message in overview
  VOID_WALKER: '404_walker',              // Find all 404 references
} as const;

// Easter egg messages
export const EASTER_EGG_MESSAGES = {
  [EASTER_EGGS.ROBOT_MASTER]: "🤖 Beep boop! You've mastered the art of robot communication!",
  [EASTER_EGGS.COLOR_WIZARD]: "🌈 The colors speak to you now, young wizard!",
  [EASTER_EGGS.TICKET_WHISPERER]: "🎫 You've uncovered the secret support channel!",
  [EASTER_EGGS.SERVER_SAGE]: "🖥️ The servers bow to your wisdom!",
  [EASTER_EGGS.ADMIN_ARCHAEOLOGIST]: "🏺 Ancient admin secrets revealed!",
  [EASTER_EGGS.OVERVIEW_ORACLE]: "🔮 You see beyond the ordinary dashboard!",
  [EASTER_EGGS.VOID_WALKER]: "🌌 You've mastered the art of finding what's not found!",
} as const;

// Local storage key for easter eggs
const STORAGE_KEY = 'dashboard_easter_eggs';

interface CompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Completion Modal Component
const CompletionModal: React.FC<CompletionModalProps> = ({ isOpen, onClose }) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered size="xl">
      <ModalOverlay backdropFilter="blur(10px)" />
      <ModalContent
        bg="gray.800"
        borderWidth={2}
        borderColor="yellow.400"
        boxShadow="0 0 30px rgba(236, 201, 75, 0.4)"
        p={4}
      >
        <ModalHeader textAlign="center" fontSize="2xl">
          <HStack justify="center" spacing={4}>
            <Icon as={FaCrown} color="yellow.400" boxSize={8} />
            <Text bgGradient="linear(to-r, yellow.400, orange.400)" bgClip="text">
              Easter Egg Master!
            </Text>
            <Icon as={FaCrown} color="yellow.400" boxSize={8} />
          </HStack>
        </ModalHeader>

        <ModalBody>
          <VStack spacing={6} align="stretch">
            <Box
              p={6}
              bg="whiteAlpha.100"
              borderRadius="xl"
              textAlign="center"
              position="relative"
              overflow="hidden"
              _before={{
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'radial-gradient(circle at center, rgba(236, 201, 75, 0.2), transparent)',
                pointerEvents: 'none',
              }}
            >
              <Icon as={FaTrophy} color="yellow.400" boxSize={12} mb={4} />
              <Text fontSize="lg" fontWeight="bold" mb={4}>
                Congratulations! You've discovered all the hidden easter eggs!
              </Text>
              <Text color="gray.300">
                Your dedication and curiosity have earned you the legendary Easter Egg Hunter role.
                This special role has been added to your Discord profile!
              </Text>
            </Box>

            <VStack spacing={3}>
              {Object.entries(EASTER_EGGS).map(([key, value]) => (
                <HStack key={key} justify="space-between" w="100%" p={2} bg="whiteAlpha.50" borderRadius="md">
                  <HStack>
                    <Icon as={FaStar} color="yellow.400" />
                    <Text>{EASTER_EGG_MESSAGES[value as keyof typeof EASTER_EGG_MESSAGES].split('!')[0]}</Text>
                  </HStack>
                  <Text color="green.400">✓</Text>
                </HStack>
              ))}
            </VStack>
          </VStack>
        </ModalBody>

        <ModalFooter justifyContent="center">
          <Button
            colorScheme="yellow"
            size="lg"
            onClick={onClose}
            _hover={{
              transform: 'translateY(-2px)',
              boxShadow: '0 0 20px rgba(236, 201, 75, 0.4)',
            }}
          >
            Continue Your Journey
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

// Hook to manage easter egg state
export const useEasterEggs = () => {
  const [foundEasterEggs, setFoundEasterEggs] = useState<string[]>([]);
  const [isAllFound, setIsAllFound] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Load found easter eggs from local storage
  useEffect(() => {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      setFoundEasterEggs(parsed);
      setIsAllFound(parsed.length === Object.keys(EASTER_EGGS).length);
    }
  }, []);

  // Save easter eggs to local storage and check for completion
  const markEasterEggFound = async (eggId: string) => {
    if (foundEasterEggs.includes(eggId)) return;

    const newFound = [...foundEasterEggs, eggId];
    setFoundEasterEggs(newFound);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(newFound));

    // Check if all easter eggs are found
    if (newFound.length === Object.keys(EASTER_EGGS).length) {
      setIsAllFound(true);
      // Show completion modal
      onOpen();
      // Award the special role
      try {
        const response = await fetch('/api/discord/roles/easter-egg', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (!response.ok) {
          console.error('Failed to award easter egg role');
        }
      } catch (error) {
        console.error('Error awarding easter egg role:', error);
      }
    }
  };

  return {
    foundEasterEggs,
    isAllFound,
    markEasterEggFound,
    CompletionModal: () => <CompletionModal isOpen={isOpen} onClose={onClose} />,
  };
};

// Sequence checker for pattern-based easter eggs
export class SequenceChecker {
  private sequence: string[];
  private currentIndex: number;
  private onComplete: () => void;

  constructor(sequence: string[], onComplete: () => void) {
    this.sequence = sequence;
    this.currentIndex = 0;
    this.onComplete = onComplete;
  }

  check(value: string) {
    if (value === this.sequence[this.currentIndex]) {
      this.currentIndex++;
      if (this.currentIndex === this.sequence.length) {
        this.currentIndex = 0;
        this.onComplete();
        return true;
      }
    } else {
      this.currentIndex = value === this.sequence[0] ? 1 : 0;
    }
    return false;
  }

  reset() {
    this.currentIndex = 0;
  }
}

// Konami code sequence
export const KONAMI_CODE = [
  'ArrowUp',
  'ArrowUp',
  'ArrowDown',
  'ArrowDown',
  'ArrowLeft',
  'ArrowRight',
  'ArrowLeft',
  'ArrowRight',
  'b',
  'a'
] as const; 