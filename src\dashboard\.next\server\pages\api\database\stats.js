"use strict";(()=>{var e={};e.id=4012,e.ids=[4012],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},66282:(e,t,r)=>{r.r(t),r.d(t,{config:()=>m,default:()=>g,routeModule:()=>h});var a={};r.r(a),r.d(a,{default:()=>f});var n=r(93433),o=r(20264),s=r(20584),i=r(15806),u=r(94506),d=r(12518);let{url:l,name:c}=r(98580).dashboardConfig.database,p=null;async function x(){if(p)return p;let e=await d.MongoClient.connect(l);return p=e,e}async function f(e,t){if(!await (0,i.getServerSession)(e,t,u.authOptions))return t.status(401).json({error:"Unauthorized"});if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let e=(await x()).db(c),r=await e.listCollections().toArray(),a=0;for(let t of r){let r=await e.collection(t.name).countDocuments();a+=r}let n=await e.stats();return t.status(200).json({collectionsCount:r.length,documentsCount:a,databaseSize:n.dataSize,storageSize:n.storageSize,avgObjSize:n.avgObjSize,indexes:n.indexes,indexSize:n.indexSize})}catch(e){return t.status(500).json({error:"Internal server error"})}}let g=(0,s.M)(a,"default"),m=(0,s.M)(a,"config"),h=new n.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/database/stats",pathname:"/api/database/stats",bundlePath:"",filename:""},userland:a})},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(66282));module.exports=a})();