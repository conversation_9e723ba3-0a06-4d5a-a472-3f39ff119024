"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/admin/guilds-c";
exports.ids = ["pages/admin/guilds-c"];
exports.modules = {

/***/ "(pages-dir-node)/./core/config.ts":
/*!************************!*\
  !*** ./core/config.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dashboardConfig: () => (/* binding */ dashboardConfig),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n// @ts-nocheck\n\n\n\nlet config = {};\ntry {\n    // Locate config.yml by walking up directories (dashboard may run from nested cwd)\n    const possible = [\n        'config.yml',\n        '../config.yml',\n        '../../config.yml',\n        '../../../config.yml',\n        '../../../../config.yml'\n    ].map((rel)=>path__WEBPACK_IMPORTED_MODULE_2__.resolve(process.cwd(), rel));\n    let configPath = possible.find((p)=>fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(p));\n    if (!configPath) {\n        // fallback relative to file location\n        const dirBased = path__WEBPACK_IMPORTED_MODULE_2__.resolve(__dirname, '../../../config.yml');\n        if (fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(dirBased)) configPath = dirBased;\n    }\n    if (!configPath) {\n        throw new Error('config.yml not found');\n    }\n    const fileContents = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(configPath, 'utf8');\n    config = yaml__WEBPACK_IMPORTED_MODULE_1___default().parse(fileContents);\n} catch (error) {\n    console.error('Error: Failed to load config.yml:', error);\n    process.exit(1); // Exit if we can't load the config\n}\n// Export the dashboard-specific config\nconst dashboardConfig = {\n    bot: {\n        token: config.bot.token,\n        clientId: config.bot.clientId,\n        clientSecret: config.bot.clientSecret,\n        guildId: config.bot.guildId,\n        ticketCategoryId: config.bot.ticketCategoryId || null,\n        ticketLogChannelId: config.bot.ticketLogChannelId || null,\n        prefix: config.bot.prefix\n    },\n    dashboard: {\n        admins: config.dashboard?.admins || [],\n        adminRoleIds: config.dashboard?.adminRoleIds || [],\n        session: {\n            secret: config.dashboard?.session?.secret || config.bot.clientSecret\n        }\n    },\n    database: {\n        url: config.database.url,\n        name: config.database.name,\n        options: {\n            maxPoolSize: config.database.options?.maxPoolSize || 10,\n            minPoolSize: config.database.options?.minPoolSize || 1,\n            maxIdleTimeMS: config.database.options?.maxIdleTimeMS || 30000,\n            serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,\n            socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,\n            connectTimeoutMS: config.database.options?.connectTimeoutMS || 10000,\n            retryWrites: config.database.options?.retryWrites !== false,\n            retryReads: config.database.options?.retryReads !== false\n        }\n    }\n};\n// Validate required configuration\nif (!dashboardConfig.bot.token) {\n    console.error('Error: Discord bot token is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.clientId || !dashboardConfig.bot.clientSecret) {\n    console.error('Error: Discord OAuth2 credentials (clientId and clientSecret) are required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.guildId) {\n    console.error('Error: Guild ID is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.database.url || !dashboardConfig.database.name) {\n    console.error('Error: Database configuration (url and name) is required in config.yml');\n    process.exit(1);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dashboardConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./core/config.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useExperimentalFeatures.ts":
/*!******************************************!*\
  !*** ./hooks/useExperimentalFeatures.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useExperimentalFeatures)\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction useExperimentalFeatures() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.useSession)();\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reason, setReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useExperimentalFeatures.useEffect\": ()=>{\n            async function checkAccess() {\n                if (!session?.user) {\n                    setHasAccess(false);\n                    setReason('unauthenticated');\n                    setIsLoading(false);\n                    return;\n                }\n                try {\n                    const response = await fetch('/api/discord/user/experimental');\n                    const data = await response.json();\n                    setHasAccess(data.hasAccess);\n                    setReason(data.reason);\n                } catch (error) {\n                    console.error('Error checking experimental features access:', error);\n                    setHasAccess(false);\n                    setReason('error');\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n            checkAccess();\n        }\n    }[\"useExperimentalFeatures.useEffect\"], [\n        session\n    ]);\n    return {\n        hasAccess,\n        reason,\n        isLoading,\n        isDeveloper: reason === 'developer',\n        isTester: reason === 'tester'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useExperimentalFeatures.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useGuildInfo.ts":
/*!*******************************!*\
  !*** ./hooks/useGuildInfo.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useGuildInfo)\n/* harmony export */ });\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swr */ \"swr\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swr__WEBPACK_IMPORTED_MODULE_0__]);\nswr__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst fetcher = async (url)=>{\n    const res = await fetch(url);\n    if (!res.ok) {\n        if (res.status === 401) {\n            // Return default data for unauthorized state\n            return {\n                name: '404 Bot',\n                botName: '404 Bot'\n            };\n        }\n        throw new Error('Failed to fetch guild info');\n    }\n    return res.json();\n};\nfunction useGuildInfo() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    // Only fetch if we're authenticated\n    const shouldFetch = status === 'authenticated';\n    const { data, error } = (0,swr__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(shouldFetch ? '/api/discord/guild' : null, fetcher, {\n        revalidateOnFocus: false,\n        revalidateOnReconnect: false\n    });\n    // Local preference state (guild vs bot)\n    const [pref, setPref] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"useGuildInfo.useState\": ()=>{\n            if (true) return 'guild';\n            return localStorage.getItem('dashboardDisplayNamePref') || 'guild';\n        }\n    }[\"useGuildInfo.useState\"]);\n    // Function to update preference and broadcast change\n    const updatePreference = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useGuildInfo.useCallback[updatePreference]\": (newPref)=>{\n            setPref(newPref);\n            if (false) {}\n        }\n    }[\"useGuildInfo.useCallback[updatePreference]\"], []);\n    // Listen for preference changes in this tab (custom event) or other tabs (storage event)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useGuildInfo.useEffect\": ()=>{\n            if (true) return;\n            const handleCustom = {\n                \"useGuildInfo.useEffect.handleCustom\": (e)=>{\n                    if (e?.detail) setPref(e.detail);\n                }\n            }[\"useGuildInfo.useEffect.handleCustom\"];\n            const handleStorage = {\n                \"useGuildInfo.useEffect.handleStorage\": (e)=>{\n                    if (e.key === 'dashboardDisplayNamePref') {\n                        setPref(e.newValue || 'guild');\n                    }\n                }\n            }[\"useGuildInfo.useEffect.handleStorage\"];\n            window.addEventListener('displayNamePrefChanged', handleCustom);\n            window.addEventListener('storage', handleStorage);\n            return ({\n                \"useGuildInfo.useEffect\": ()=>{\n                    window.removeEventListener('displayNamePrefChanged', handleCustom);\n                    window.removeEventListener('storage', handleStorage);\n                }\n            })[\"useGuildInfo.useEffect\"];\n        }\n    }[\"useGuildInfo.useEffect\"], []);\n    // Default display name when not authenticated\n    const defaultName = '404 Bot Dashboard';\n    // Determine displayName\n    let displayName = defaultName;\n    if (data) {\n        if (pref === 'bot' && data.botName) {\n            displayName = data.botName;\n        } else {\n            displayName = data.name || defaultName;\n        }\n    }\n    return {\n        guild: data,\n        displayName,\n        pref,\n        updatePreference,\n        isLoading: shouldFetch && !error && !data,\n        isError: !!error\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useGuildInfo.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/admin/guilds.tsx":
/*!********************************!*\
  !*** ./pages/admin/guilds.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServerManagement),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Card,CardBody,CardHeader,Container,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,SimpleGrid,Skeleton,Spinner,Tab,TabList,TabPanel,TabPanels,Table,Tabs,Tbody,Td,Text,Th,Thead,Tooltip,Tr,VStack,useDisclosure,useToast!=!@chakra-ui/react */ \"(pages-dir-node)/__barrel_optimize__?names=Badge,Box,Button,Card,CardBody,CardHeader,Container,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,SimpleGrid,Skeleton,Spinner,Tab,TabList,TabPanel,TabPanels,Table,Tabs,Tbody,Td,Text,Th,Thead,Tooltip,Tr,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/Layout */ \"(pages-dir-node)/./components/Layout.tsx\");\n/* harmony import */ var _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useGuildInfo */ \"(pages-dir-node)/./hooks/useGuildInfo.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _api_auth_nextauth___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../api/auth/[...nextauth] */ \"(pages-dir-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit2,FiFolderPlus,FiHash,FiLock,FiMessageCircle,FiMessageSquare,FiPlus,FiRadio,FiSave,FiServer,FiSettings,FiTool,FiTrash2,FiUsers,FiVolume2,FiZap!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiEdit2,FiFolderPlus,FiHash,FiLock,FiMessageCircle,FiMessageSquare,FiPlus,FiRadio,FiSave,FiServer,FiSettings,FiTool,FiTrash2,FiUsers,FiVolume2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaPalette!=!react-icons/fa */ \"(pages-dir-node)/__barrel_optimize__?names=FaPalette!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Layout__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__, _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__]);\n([_components_Layout__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__, _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// @ts-nocheck\n\n\n\n\n\n\n\n\n\n\n// Dynamic imports for heavy components\nconst CreateChannelDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-cedb36cc\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-0e951d65\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171\"), __webpack_require__.e(\"lib-node_modules_pnpm_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_o\"), __webpack_require__.e(\"lib-node_modules_pnpm_r\"), __webpack_require__.e(\"lib-node_modules_pnpm_t\"), __webpack_require__.e(\"_pages-dir-node_components_CreateChannelDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/CreateChannelDialog */ \"(pages-dir-node)/./components/CreateChannelDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/CreateChannelDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 63,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\nconst EditChannelDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-cedb36cc\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-0e951d65\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171\"), __webpack_require__.e(\"lib-node_modules_pnpm_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_o\"), __webpack_require__.e(\"lib-node_modules_pnpm_r\"), __webpack_require__.e(\"lib-node_modules_pnpm_t\"), __webpack_require__.e(\"_pages-dir-node_components_EditChannelDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/EditChannelDialog */ \"(pages-dir-node)/./components/EditChannelDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/EditChannelDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 68,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\nconst EditRoleDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-cedb36cc\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-0e951d65\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171\"), __webpack_require__.e(\"lib-node_modules_pnpm_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_o\"), __webpack_require__.e(\"lib-node_modules_pnpm_r\"), __webpack_require__.e(\"lib-node_modules_pnpm_t\"), __webpack_require__.e(\"commons\"), __webpack_require__.e(\"_pages-dir-node_components_EditRoleDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/EditRoleDialog */ \"(pages-dir-node)/./components/EditRoleDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/EditRoleDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 73,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\nconst ColorBuilder = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-node_components_ColorBuilder_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/ColorBuilder */ \"(pages-dir-node)/./components/ColorBuilder.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/ColorBuilder\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 78,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\nconst CreateRoleDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-cedb36cc\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-0e951d65\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171\"), __webpack_require__.e(\"lib-node_modules_pnpm_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_o\"), __webpack_require__.e(\"lib-node_modules_pnpm_r\"), __webpack_require__.e(\"lib-node_modules_pnpm_t\"), __webpack_require__.e(\"commons\"), __webpack_require__.e(\"_pages-dir-node_components_CreateRoleDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/CreateRoleDialog */ \"(pages-dir-node)/./components/CreateRoleDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/CreateRoleDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 83,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n// Rate limiting constants\nconst RATE_LIMIT_MS = 2000; // 2 seconds between operations\nconst BULK_RATE_LIMIT_MS = 5000; // 5 seconds between bulk operations\n// Custom hook for rate limiting\nfunction useRateLimit(delay = RATE_LIMIT_MS) {\n    const [isRateLimited, setIsRateLimited] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const resetRateLimit = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"useRateLimit.useCallback[resetRateLimit]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            setIsRateLimited(true);\n            timeoutRef.current = setTimeout({\n                \"useRateLimit.useCallback[resetRateLimit]\": ()=>{\n                    setIsRateLimited(false);\n                }\n            }[\"useRateLimit.useCallback[resetRateLimit]\"], delay);\n        }\n    }[\"useRateLimit.useCallback[resetRateLimit]\"], [\n        delay\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"useRateLimit.useEffect\": ()=>{\n            return ({\n                \"useRateLimit.useEffect\": ()=>{\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"useRateLimit.useEffect\"];\n        }\n    }[\"useRateLimit.useEffect\"], []);\n    return [\n        isRateLimited,\n        resetRateLimit\n    ];\n}\nconst CHANNEL_TYPE_CONFIG = {\n    0: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMessageSquare,\n        color: 'blue',\n        label: 'Text'\n    },\n    2: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiVolume2,\n        color: 'green',\n        label: 'Voice'\n    },\n    4: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiFolderPlus,\n        color: 'purple',\n        label: 'Category'\n    },\n    5: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiRadio,\n        color: 'orange',\n        label: 'Announcement'\n    },\n    11: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMessageCircle,\n        color: 'cyan',\n        label: 'Public Thread'\n    },\n    12: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiLock,\n        color: 'pink',\n        label: 'Private Thread'\n    },\n    13: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiHash,\n        color: 'teal',\n        label: 'Stage Voice'\n    },\n    15: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiHash,\n        color: 'gray',\n        label: 'Forum'\n    }\n};\nconst PERMISSION_BADGES = {\n    ADMINISTRATOR: {\n        color: 'red',\n        label: 'Admin'\n    },\n    MANAGE_GUILD: {\n        color: 'orange',\n        label: 'Manage Server'\n    },\n    MANAGE_ROLES: {\n        color: 'yellow',\n        label: 'Manage Roles'\n    },\n    MANAGE_CHANNELS: {\n        color: 'green',\n        label: 'Manage Channels'\n    },\n    KICK_MEMBERS: {\n        color: 'purple',\n        label: 'Kick'\n    },\n    BAN_MEMBERS: {\n        color: 'pink',\n        label: 'Ban'\n    },\n    MANAGE_MESSAGES: {\n        color: 'blue',\n        label: 'Manage Messages'\n    },\n    MENTION_EVERYONE: {\n        color: 'cyan',\n        label: 'Mention @everyone'\n    }\n};\n// Add this helper map and function after PERMISSION_BADGES constant\nconst PERMISSION_FLAG_BITS = {\n    ADMINISTRATOR: 1n << 3n,\n    MANAGE_GUILD: 1n << 5n,\n    MANAGE_ROLES: 1n << 28n,\n    MANAGE_CHANNELS: 1n << 4n,\n    KICK_MEMBERS: 1n << 1n,\n    BAN_MEMBERS: 1n << 2n,\n    MANAGE_MESSAGES: 1n << 13n,\n    MENTION_EVERYONE: 1n << 17n\n};\nfunction decodePermissions(bitfield) {\n    if (!bitfield) return [];\n    if (Array.isArray(bitfield)) return bitfield;\n    try {\n        const permissions = [];\n        const bits = BigInt(bitfield);\n        for (const [permission, bit] of Object.entries(PERMISSION_FLAG_BITS)){\n            if ((bits & bit) === bit) {\n                permissions.push(permission);\n            }\n        }\n        return permissions;\n    } catch (error) {\n        console.error('Error decoding permissions:', error);\n        return [];\n    }\n}\nfunction ServerManagement() {\n    const toast = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const { displayName } = (0,_hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [isRateLimited, resetRateLimit] = useRateLimit();\n    const [isBulkRateLimited, resetBulkRateLimit] = useRateLimit(BULK_RATE_LIMIT_MS);\n    // State for guild settings\n    const [guildData, setGuildData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        prefix: '!',\n        botName: 'Bot',\n        guildName: '',\n        guildIcon: null,\n        activities: [\n            {\n                type: 'PLAYING',\n                name: 'with Discord.js'\n            }\n        ],\n        activityRotationInterval: 60\n    });\n    // State for roles and channels\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [channels, setChannels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [channelsLoading, setChannelsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Modal states\n    const { isOpen: isCreateChannelOpen, onOpen: onCreateChannelOpen, onClose: onCreateChannelClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useDisclosure)();\n    const { isOpen: isEditChannelOpen, onOpen: onEditChannelOpen, onClose: onEditChannelClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useDisclosure)();\n    const { isOpen: isCreateRoleOpen, onOpen: onCreateRoleOpen, onClose: onCreateRoleClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useDisclosure)();\n    const { isOpen: isEditRoleOpen, onOpen: onEditRoleOpen, onClose: onEditRoleClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useDisclosure)();\n    const { isOpen: isColorBuilderOpen, onOpen: onColorBuilderOpen, onClose: onColorBuilderClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useDisclosure)();\n    // Selected items for editing\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedChannel, setSelectedChannel] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // File upload state\n    const [iconFile, setIconFile] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [iconPreview, setIconPreview] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const handleIconFileChange = (e)=>{\n        const file = e.target.files?.[0];\n        if (file) {\n            setIconFile(file);\n            const reader = new FileReader();\n            reader.onload = (e)=>setIconPreview(e.target?.result);\n            reader.readAsDataURL(file);\n        }\n    };\n    const uploadIcon = async ()=>{\n        if (!iconFile) return;\n        const formData = new FormData();\n        formData.append('icon', iconFile);\n        try {\n            const response = await fetch('/api/discord/settings', {\n                method: 'POST',\n                body: formData\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setGuildData((prev)=>({\n                        ...prev,\n                        guildIcon: data.iconUrl\n                    }));\n                setIconFile(null);\n                setIconPreview(null);\n                toast({\n                    title: 'Success',\n                    description: 'Guild icon updated successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to upload icon',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const fetchGuildData = async ()=>{\n        try {\n            const [guildResponse, rolesResponse] = await Promise.all([\n                fetch('/api/discord/guild'),\n                fetch('/api/discord/roles')\n            ]);\n            if (guildResponse.ok) {\n                const guild = await guildResponse.json();\n                setGuildData((prev)=>({\n                        ...prev,\n                        guildName: guild.name,\n                        guildIcon: guild.icon\n                    }));\n            }\n            if (rolesResponse.ok) {\n                const rolesData = await rolesResponse.json();\n                const arr = Array.isArray(rolesData) ? rolesData : rolesData.roles || [];\n                setRoles(arr.sort((a, b)=>b.position - a.position));\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch guild data',\n                status: 'error',\n                duration: 3000\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchChannels = async ()=>{\n        try {\n            const response = await fetch('/api/discord/channels');\n            if (!response.ok) {\n                throw new Error('Failed to fetch channels');\n            }\n            const data = await response.json();\n            const sortedChannels = (data || []).sort((a, b)=>{\n                if (a.type === 4 && b.type !== 4) return -1;\n                if (a.type !== 4 && b.type === 4) return 1;\n                return a.position - b.position;\n            });\n            setChannels(sortedChannels);\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch channels',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setChannelsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"ServerManagement.useEffect\": ()=>{\n            fetchGuildData();\n            fetchChannels();\n        }\n    }[\"ServerManagement.useEffect\"], []);\n    const handleSettingChange = (setting, value)=>{\n        setGuildData((prev)=>({\n                ...prev,\n                [setting]: value\n            }));\n    };\n    const saveSettings = async ()=>{\n        if (saving || isRateLimited) return;\n        setSaving(true);\n        resetRateLimit();\n        try {\n            const response = await fetch('/api/discord/settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(guildData)\n            });\n            if (response.ok) {\n                toast({\n                    title: 'Success',\n                    description: 'Settings saved successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            } else {\n                throw new Error('Failed to save settings');\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to save settings',\n                status: 'error',\n                duration: 3000\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleRoleEdit = (role)=>{\n        setSelectedRole(role);\n        onEditRoleOpen();\n    };\n    const handleChannelEdit = (channel)=>{\n        setSelectedChannel(channel);\n        onEditChannelOpen();\n    };\n    const handleChannelCreate = ()=>{\n        onCreateChannelOpen();\n    };\n    const handleRoleCreate = ()=>{\n        onCreateRoleOpen();\n    };\n    const handleChannelDelete = async (channelId)=>{\n        if (isRateLimited) return;\n        try {\n            resetRateLimit();\n            const response = await fetch(`/api/discord/channels/${channelId}`, {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchChannels();\n                toast({\n                    title: 'Success',\n                    description: 'Channel deleted successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to delete channel',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const getParentName = (parentId)=>{\n        if (!parentId || !channels) return '-';\n        const parent = channels.find((c)=>c.id === parentId);\n        return parent ? parent.name : '-';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                    spacing: 6,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            height: \"60px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            height: \"400px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                lineNumber: 441,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 440,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                    spacing: 8,\n                    align: \"stretch\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                            bg: \"rgba(255,255,255,0.08)\",\n                            p: 8,\n                            rounded: \"2xl\",\n                            backdropFilter: \"blur(10px)\",\n                            border: \"2px solid\",\n                            borderColor: \"blue.400\",\n                            boxShadow: \"0 0 15px rgba(66, 153, 225, 0.4)\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                justify: \"space-between\",\n                                align: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                size: \"xl\",\n                                                bgGradient: \"linear(to-r, blue.300, purple.400)\",\n                                                bgClip: \"text\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                        as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiServer,\n                                                        mr: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Server Management\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                color: \"gray.300\",\n                                                mt: 2,\n                                                children: [\n                                                    \"Comprehensive management for \",\n                                                    displayName || guildData.guildName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSave, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 27\n                                        }, void 0),\n                                        colorScheme: \"blue\",\n                                        onClick: saveSettings,\n                                        isLoading: saving,\n                                        isDisabled: isRateLimited,\n                                        size: \"lg\",\n                                        children: \"Save Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                            colorScheme: \"blue\",\n                            isLazy: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSettings,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"General Settings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                    as: _barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaPalette,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Theme Builder\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiTool,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Builders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiZap,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Automation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabPanels, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                spacing: 8,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                    size: \"md\",\n                                                                    children: \"Basic Settings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.SimpleGrid, {\n                                                                    columns: {\n                                                                        base: 1,\n                                                                        lg: 2\n                                                                    },\n                                                                    spacing: 6,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                            children: \"Bot Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 526,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                            value: guildData.botName,\n                                                                                            onChange: (e)=>handleSettingChange('botName', e.target.value),\n                                                                                            placeholder: \"Enter bot name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 527,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 525,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                            children: \"Command Prefix\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 534,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                            value: guildData.prefix,\n                                                                                            onChange: (e)=>handleSettingChange('prefix', e.target.value),\n                                                                                            placeholder: \"Enter command prefix\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 535,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                            children: \"Server Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 544,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                            value: guildData?.name || '',\n                                                                                            isReadOnly: true,\n                                                                                            bg: \"gray.50\",\n                                                                                            _dark: {\n                                                                                                bg: 'gray.700'\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 545,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 543,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                            children: \"Member Count\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 553,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                            value: guildData?.memberCount || '0',\n                                                                                            isReadOnly: true,\n                                                                                            bg: \"gray.50\",\n                                                                                            _dark: {\n                                                                                                bg: 'gray.700'\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 554,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 552,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 542,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                    justify: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiUsers,\n                                                                                    mr: 2\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 571,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Roles (\",\n                                                                                roles.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 570,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiPlus, {}, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 575,\n                                                                                columnNumber: 37\n                                                                            }, void 0),\n                                                                            colorScheme: \"green\",\n                                                                            onClick: handleRoleCreate,\n                                                                            isDisabled: isRateLimited,\n                                                                            children: \"Create Role\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                    spacing: 3,\n                                                                    children: [\n                                                                        ...Array(3)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                                            height: \"60px\"\n                                                                        }, i, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 588,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                    overflowX: \"auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                                        variant: \"simple\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Thead, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tr, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Role\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 596,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Members\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 597,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Permissions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 598,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Actions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 599,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 595,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 594,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tbody, {\n                                                                                children: (roles || []).map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tr, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                                                            w: 4,\n                                                                                                            h: 4,\n                                                                                                            rounded: \"full\",\n                                                                                                            bg: role.color ? `#${role.color.toString(16).padStart(6, '0')}` : 'gray.500'\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 607,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                                            children: role.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 613,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 606,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 605,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                                    colorScheme: \"blue\",\n                                                                                                    children: \"0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 617,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 616,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                                    wrap: \"wrap\",\n                                                                                                    spacing: 1,\n                                                                                                    children: [\n                                                                                                        (decodePermissions(role.permissions) || []).slice(0, 3).map((perm)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                                                colorScheme: PERMISSION_BADGES[perm]?.color || 'gray',\n                                                                                                                size: \"sm\",\n                                                                                                                children: PERMISSION_BADGES[perm]?.label || perm\n                                                                                                            }, perm, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 622,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)),\n                                                                                                        decodePermissions(role.permissions).length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                                            colorScheme: \"gray\",\n                                                                                                            size: \"sm\",\n                                                                                                            children: [\n                                                                                                                \"+\",\n                                                                                                                decodePermissions(role.permissions).length - 3\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 631,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 620,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 619,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                                                                            label: \"Edit Role\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.IconButton, {\n                                                                                                                \"aria-label\": \"Edit role\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiEdit2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 642,\n                                                                                                                    columnNumber: 49\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"blue\",\n                                                                                                                onClick: ()=>handleRoleEdit(role),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 640,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 639,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                                                                            label: \"Delete Role\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.IconButton, {\n                                                                                                                \"aria-label\": \"Delete role\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiTrash2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 653,\n                                                                                                                    columnNumber: 49\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"red\",\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 651,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 650,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 638,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 637,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, role.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 604,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 602,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                    justify: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiHash,\n                                                                                    mr: 2\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 676,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Channels (\",\n                                                                                channels.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 675,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiPlus, {}, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 680,\n                                                                                columnNumber: 37\n                                                                            }, void 0),\n                                                                            colorScheme: \"blue\",\n                                                                            onClick: onCreateChannelOpen,\n                                                                            children: \"Create Channel\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                children: channelsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                    spacing: 3,\n                                                                    children: [\n                                                                        ...Array(5)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                                            height: \"50px\"\n                                                                        }, i, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 25\n                                                                }, this) : channels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                    color: \"gray.500\",\n                                                                    textAlign: \"center\",\n                                                                    py: 8,\n                                                                    children: \"No channels found\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 696,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                    overflowX: \"auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                                        variant: \"simple\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Thead, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tr, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 704,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Type\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 705,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Category\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 706,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Position\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 707,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Actions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 708,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 703,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 702,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tbody, {\n                                                                                children: (channels || []).map((channel)=>{\n                                                                                    const typeConfig = CHANNEL_TYPE_CONFIG[channel.type] || {\n                                                                                        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMessageSquare,\n                                                                                        color: 'gray',\n                                                                                        label: 'Other'\n                                                                                    };\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tr, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                                                            as: typeConfig.icon,\n                                                                                                            color: `${typeConfig.color}.400`\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 718,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                                            children: channel.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 722,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 717,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 716,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                                    colorScheme: typeConfig.color,\n                                                                                                    children: typeConfig.label\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 726,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 725,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                                    color: \"gray.500\",\n                                                                                                    children: getParentName(channel.parent_id)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 729,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 728,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                                    color: \"gray.500\",\n                                                                                                    children: channel.position\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 732,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 731,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                                                                            label: \"Edit Channel\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.IconButton, {\n                                                                                                                \"aria-label\": \"Edit channel\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiEdit2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 739,\n                                                                                                                    columnNumber: 51\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"blue\",\n                                                                                                                onClick: ()=>handleChannelEdit(channel),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 737,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 736,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                                                                            label: \"Delete Channel\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.IconButton, {\n                                                                                                                \"aria-label\": \"Delete channel\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiTrash2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 750,\n                                                                                                                    columnNumber: 51\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"red\",\n                                                                                                                onClick: ()=>handleChannelDelete(channel.id),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 748,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 747,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 735,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 734,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, channel.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 715,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 711,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"\\uD83C\\uDFA8 Theme Builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Create and customize your own themes with the advanced color builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 777,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Custom Theme Builder\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 785,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 784,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Create your own custom themes with full color control\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 789,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaPalette, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 793,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"purple\",\n                                                                                    onClick: onColorBuilderOpen,\n                                                                                    size: \"lg\",\n                                                                                    children: \"Open Color Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 792,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 788,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 787,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Theme Presets\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 806,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 805,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 3,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    mb: 2,\n                                                                                    children: \"Quick theme options available in the navigation bar\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 810,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                    wrap: \"wrap\",\n                                                                                    spacing: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                            colorScheme: \"blue\",\n                                                                                            children: \"Dark\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 814,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                            colorScheme: \"purple\",\n                                                                                            children: \"Midnight\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 815,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                            colorScheme: \"green\",\n                                                                                            children: \"Forest\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 816,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                            colorScheme: \"orange\",\n                                                                                            children: \"Sunset\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 817,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                            colorScheme: \"pink\",\n                                                                                            children: \"Rose\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 818,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 813,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 809,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 808,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"\\uD83D\\uDEE0️ Builders & Tools\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 831,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Create custom content and manage server features with powerful builders\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 830,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Content Builders\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 840,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 839,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 845,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"green\",\n                                                                                    onClick: ()=>window.open('/admin/experimental/addon-builder', '_blank'),\n                                                                                    children: \"Addon Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 844,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMessageSquare, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 852,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"blue\",\n                                                                                    onClick: ()=>window.open('/admin/applications-builder', '_blank'),\n                                                                                    children: \"Applications Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 851,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMessageSquare, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 859,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"purple\",\n                                                                                    onClick: ()=>window.open('/admin/embed-builder', '_blank'),\n                                                                                    isDisabled: true,\n                                                                                    children: \"Message Builder (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 858,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 843,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 842,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Management Tools\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 872,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 871,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 877,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"orange\",\n                                                                                    onClick: ()=>window.open('/admin/addons', '_blank'),\n                                                                                    children: \"Manage Addons\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 876,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 884,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"teal\",\n                                                                                    onClick: ()=>window.open('/admin/commands', '_blank'),\n                                                                                    children: \"Command Manager\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 883,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 891,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"cyan\",\n                                                                                    onClick: ()=>window.open('/admin/applications', '_blank'),\n                                                                                    children: \"Application Manager\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 890,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 875,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 874,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 870,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 828,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"⚡ Automation & Activities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 908,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Set up automated features and server activities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 909,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Activity Templates\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 917,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 916,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                color: \"gray.500\",\n                                                                                fontSize: \"sm\",\n                                                                                mb: 4,\n                                                                                children: \"Pre-built activity templates to get you started quickly:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 920,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                                spacing: 2,\n                                                                                align: \"stretch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Event Management System\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 924,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Welcome & Onboarding Flow\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 925,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Moderation Automation\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 926,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Custom Commands\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 927,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Auto-Role Assignment\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 928,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Scheduled Messages\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 929,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 923,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 919,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 915,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Automation Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 936,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 935,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Configure automated server features\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 940,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 944,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"yellow\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Auto-Moderation (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 943,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 952,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"green\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Welcome System (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 951,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 960,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"blue\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Event Scheduler (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 959,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 939,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 938,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 934,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 906,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 454,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 978,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateChannelDialog, {\n                        isOpen: isCreateChannelOpen,\n                        onClose: onCreateChannelClose,\n                        onSuccess: fetchChannels\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 979,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 978,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 986,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditChannelDialog, {\n                        isOpen: isEditChannelOpen,\n                        onClose: onEditChannelClose,\n                        channel: selectedChannel,\n                        categories: channels.filter((c)=>c.type === 4),\n                        onSuccess: fetchChannels\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 987,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 986,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 996,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateRoleDialog, {\n                        isOpen: isCreateRoleOpen,\n                        onClose: onCreateRoleClose,\n                        onSuccess: fetchGuildData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 997,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 996,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1004,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditRoleDialog, {\n                        isOpen: isEditRoleOpen,\n                        onClose: onEditRoleClose,\n                        role: selectedRole,\n                        onSuccess: fetchGuildData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1005,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1004,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1013,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColorBuilder, {\n                        isOpen: isColorBuilderOpen,\n                        onClose: onColorBuilderClose\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1014,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1013,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 453,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n        lineNumber: 452,\n        columnNumber: 5\n    }, this);\n}\nconst getServerSideProps = async (ctx)=>{\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_4__.getServerSession)(ctx.req, ctx.res, _api_auth_nextauth___WEBPACK_IMPORTED_MODULE_5__.authOptions);\n    if (!session) {\n        return {\n            redirect: {\n                destination: '/api/auth/signin?callbackUrl=%2Fadmin%2Fguilds',\n                permanent: false\n            }\n        };\n    }\n    return {\n        props: {\n            session\n        }\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL2FkbWluL2d1aWxkcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGNBQWM7O0FBaURZO0FBQ21CO0FBQ087QUFDdUI7QUFFekI7QUFDTTtBQUNzSjtBQUNuSztBQUNSO0FBRW5DLHVDQUF1QztBQUN2QyxNQUFNNEQsc0JBQXNCRCxtREFBT0EsQ0FBQyxJQUFNLDJsSUFBOEM7Ozs7OztJQUN0RkUsU0FBUyxrQkFBTSw4REFBQzdCLGdVQUFPQTtZQUFDOEIsTUFBSzs7Ozs7O0lBQzdCQyxLQUFLOztBQUdQLE1BQU1DLG9CQUFvQkwsbURBQU9BLENBQUMsSUFBTSxxbElBQTRDOzs7Ozs7SUFDbEZFLFNBQVMsa0JBQU0sOERBQUM3QixnVUFBT0E7WUFBQzhCLE1BQUs7Ozs7OztJQUM3QkMsS0FBSzs7QUFHUCxNQUFNRSxpQkFBaUJOLG1EQUFPQSxDQUFDLElBQU0sOG1JQUF5Qzs7Ozs7O0lBQzVFRSxTQUFTLGtCQUFNLDhEQUFDN0IsZ1VBQU9BO1lBQUM4QixNQUFLOzs7Ozs7SUFDN0JDLEtBQUs7O0FBR1AsTUFBTUcsZUFBZVAsbURBQU9BLENBQUMsSUFBTSwrTkFBdUM7Ozs7OztJQUN4RUUsU0FBUyxrQkFBTSw4REFBQzdCLGdVQUFPQTtZQUFDOEIsTUFBSzs7Ozs7O0lBQzdCQyxLQUFLOztBQUdQLE1BQU1JLG1CQUFtQlIsbURBQU9BLENBQUMsSUFBTSxvbklBQTJDOzs7Ozs7SUFDaEZFLFNBQVMsa0JBQU0sOERBQUM3QixnVUFBT0E7WUFBQzhCLE1BQUs7Ozs7OztJQUM3QkMsS0FBSzs7QUFHUCwwQkFBMEI7QUFDMUIsTUFBTUssZ0JBQWdCLE1BQU0sK0JBQStCO0FBQzNELE1BQU1DLHFCQUFxQixNQUFNLG9DQUFvQztBQUVyRSxnQ0FBZ0M7QUFDaEMsU0FBU0MsYUFBYUMsUUFBUUgsYUFBYTtJQUN6QyxNQUFNLENBQUNJLGVBQWVDLGlCQUFpQixHQUFHdEMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTXVDLGFBQWFwQyw2Q0FBTUEsQ0FBQztJQUUxQixNQUFNcUMsaUJBQWlCdEMsa0RBQVdBO29EQUFDO1lBQ2pDLElBQUlxQyxXQUFXRSxPQUFPLEVBQUU7Z0JBQ3RCQyxhQUFhSCxXQUFXRSxPQUFPO1lBQ2pDO1lBQ0FILGlCQUFpQjtZQUNqQkMsV0FBV0UsT0FBTyxHQUFHRTs0REFBVztvQkFDOUJMLGlCQUFpQjtnQkFDbkI7MkRBQUdGO1FBQ0w7bURBQUc7UUFBQ0E7S0FBTTtJQUVWbkMsZ0RBQVNBO2tDQUFDO1lBQ1I7MENBQU87b0JBQ0wsSUFBSXNDLFdBQVdFLE9BQU8sRUFBRTt3QkFDdEJDLGFBQWFILFdBQVdFLE9BQU87b0JBQ2pDO2dCQUNGOztRQUNGO2lDQUFHLEVBQUU7SUFFTCxPQUFPO1FBQUNKO1FBQWVHO0tBQWU7QUFDeEM7QUFnQ0EsTUFBTUksc0JBQXNCO0lBQzFCLEdBQUk7UUFBRUMsTUFBTS9CLHVPQUFlQTtRQUFFZ0MsT0FBTztRQUFVQyxPQUFPO0lBQU87SUFDNUQsR0FBSTtRQUFFRixNQUFNOUIsaU9BQVNBO1FBQU8rQixPQUFPO1FBQVVDLE9BQU87SUFBUTtJQUM1RCxHQUFJO1FBQUVGLE1BQU03QixvT0FBWUE7UUFBSThCLE9BQU87UUFBVUMsT0FBTztJQUFXO0lBQy9ELEdBQUk7UUFBRUYsTUFBTTVCLCtOQUFPQTtRQUFTNkIsT0FBTztRQUFVQyxPQUFPO0lBQWU7SUFDbkUsSUFBSTtRQUFFRixNQUFNMUIsdU9BQWVBO1FBQUMyQixPQUFPO1FBQVVDLE9BQU87SUFBZ0I7SUFDcEUsSUFBSTtRQUFFRixNQUFNM0IsOE5BQU1BO1FBQVU0QixPQUFPO1FBQVVDLE9BQU87SUFBaUI7SUFDckUsSUFBSTtRQUFFRixNQUFNcEMsOE5BQU1BO1FBQVVxQyxPQUFPO1FBQVVDLE9BQU87SUFBYTtJQUNqRSxJQUFJO1FBQUVGLE1BQU1wQyw4TkFBTUE7UUFBVXFDLE9BQU87UUFBVUMsT0FBTztJQUFPO0FBQzdEO0FBRUEsTUFBTUMsb0JBQW9CO0lBQ3hCQyxlQUFlO1FBQUVILE9BQU87UUFBT0MsT0FBTztJQUFRO0lBQzlDRyxjQUFjO1FBQUVKLE9BQU87UUFBVUMsT0FBTztJQUFnQjtJQUN4REksY0FBYztRQUFFTCxPQUFPO1FBQVVDLE9BQU87SUFBZTtJQUN2REssaUJBQWlCO1FBQUVOLE9BQU87UUFBU0MsT0FBTztJQUFrQjtJQUM1RE0sY0FBYztRQUFFUCxPQUFPO1FBQVVDLE9BQU87SUFBTztJQUMvQ08sYUFBYTtRQUFFUixPQUFPO1FBQVFDLE9BQU87SUFBTTtJQUMzQ1EsaUJBQWlCO1FBQUVULE9BQU87UUFBUUMsT0FBTztJQUFrQjtJQUMzRFMsa0JBQWtCO1FBQUVWLE9BQU87UUFBUUMsT0FBTztJQUFvQjtBQUNoRTtBQUVBLG9FQUFvRTtBQUNwRSxNQUFNVSx1QkFBdUU7SUFDM0VSLGVBQWUsRUFBRSxJQUFJLEVBQUU7SUFDdkJDLGNBQWMsRUFBRSxJQUFJLEVBQUU7SUFDdEJDLGNBQWMsRUFBRSxJQUFJLEdBQUc7SUFDdkJDLGlCQUFpQixFQUFFLElBQUksRUFBRTtJQUN6QkMsY0FBYyxFQUFFLElBQUksRUFBRTtJQUN0QkMsYUFBYSxFQUFFLElBQUksRUFBRTtJQUNyQkMsaUJBQWlCLEVBQUUsSUFBSSxHQUFHO0lBQzFCQyxrQkFBa0IsRUFBRSxJQUFJLEdBQUc7QUFDN0I7QUFFQSxTQUFTRSxrQkFBa0JDLFFBQXlCO0lBQ2xELElBQUksQ0FBQ0EsVUFBVSxPQUFPLEVBQUU7SUFDeEIsSUFBSUMsTUFBTUMsT0FBTyxDQUFDRixXQUFXLE9BQU9BO0lBQ3BDLElBQUk7UUFDRixNQUFNRyxjQUFrRCxFQUFFO1FBQzFELE1BQU1DLE9BQU9DLE9BQU9MO1FBRXBCLEtBQUssTUFBTSxDQUFDTSxZQUFZQyxJQUFJLElBQUlDLE9BQU9DLE9BQU8sQ0FBQ1gsc0JBQXVCO1lBQ3BFLElBQUksQ0FBQ00sT0FBT0csR0FBRSxNQUFPQSxLQUFLO2dCQUN4QkosWUFBWU8sSUFBSSxDQUFDSjtZQUNuQjtRQUNGO1FBRUEsT0FBT0g7SUFDVCxFQUFFLE9BQU9RLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7UUFDN0MsT0FBTyxFQUFFO0lBQ1g7QUFDRjtBQUVlLFNBQVNFO0lBQ3RCLE1BQU1DLFFBQVFwRyxxVUFBUUE7SUFDdEIsTUFBTSxFQUFFcUcsV0FBVyxFQUFFLEdBQUczRSwrREFBWUE7SUFDcEMsTUFBTSxDQUFDc0MsZUFBZUcsZUFBZSxHQUFHTDtJQUN4QyxNQUFNLENBQUN3QyxtQkFBbUJDLG1CQUFtQixHQUFHekMsYUFBYUQ7SUFFN0QsMkJBQTJCO0lBQzNCLE1BQU0sQ0FBQzJDLFdBQVdDLGFBQWEsR0FBRzlFLCtDQUFRQSxDQUFnQjtRQUN4RCtFLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsWUFBWTtZQUFDO2dCQUFFQyxNQUFNO2dCQUFXQyxNQUFNO1lBQWtCO1NBQUU7UUFDMURDLDBCQUEwQjtJQUM1QjtJQUVBLCtCQUErQjtJQUMvQixNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR3hGLCtDQUFRQSxDQUFTLEVBQUU7SUFDN0MsTUFBTSxDQUFDeUYsVUFBVUMsWUFBWSxHQUFHMUYsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUMwQixTQUFTaUUsV0FBVyxHQUFHM0YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDNEYsaUJBQWlCQyxtQkFBbUIsR0FBRzdGLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQzhGLFFBQVFDLFVBQVUsR0FBRy9GLCtDQUFRQSxDQUFDO0lBRXJDLGVBQWU7SUFDZixNQUFNLEVBQUVnRyxRQUFRQyxtQkFBbUIsRUFBRUMsUUFBUUMsbUJBQW1CLEVBQUVDLFNBQVNDLG9CQUFvQixFQUFFLEdBQUd0SCwwVUFBYUE7SUFDakgsTUFBTSxFQUFFaUgsUUFBUU0saUJBQWlCLEVBQUVKLFFBQVFLLGlCQUFpQixFQUFFSCxTQUFTSSxrQkFBa0IsRUFBRSxHQUFHekgsMFVBQWFBO0lBQzNHLE1BQU0sRUFBRWlILFFBQVFTLGdCQUFnQixFQUFFUCxRQUFRUSxnQkFBZ0IsRUFBRU4sU0FBU08saUJBQWlCLEVBQUUsR0FBRzVILDBVQUFhQTtJQUN4RyxNQUFNLEVBQUVpSCxRQUFRWSxjQUFjLEVBQUVWLFFBQVFXLGNBQWMsRUFBRVQsU0FBU1UsZUFBZSxFQUFFLEdBQUcvSCwwVUFBYUE7SUFDbEcsTUFBTSxFQUFFaUgsUUFBUWUsa0JBQWtCLEVBQUViLFFBQVFjLGtCQUFrQixFQUFFWixTQUFTYSxtQkFBbUIsRUFBRSxHQUFHbEksMFVBQWFBO0lBRTlHLDZCQUE2QjtJQUM3QixNQUFNLENBQUNtSSxjQUFjQyxnQkFBZ0IsR0FBR25ILCtDQUFRQSxDQUFjO0lBQzlELE1BQU0sQ0FBQ29ILGlCQUFpQkMsbUJBQW1CLEdBQUdySCwrQ0FBUUEsQ0FBaUI7SUFFdkUsb0JBQW9CO0lBQ3BCLE1BQU0sQ0FBQ3NILFVBQVVDLFlBQVksR0FBR3ZILCtDQUFRQSxDQUFjO0lBQ3RELE1BQU0sQ0FBQ3dILGFBQWFDLGVBQWUsR0FBR3pILCtDQUFRQSxDQUFnQjtJQUM5RCxNQUFNMEgsZUFBZXZILDZDQUFNQSxDQUFtQjtJQUU5QyxNQUFNd0gsdUJBQXVCLENBQUNDO1FBQzVCLE1BQU1DLE9BQU9ELEVBQUVFLE1BQU0sQ0FBQ0MsS0FBSyxFQUFFLENBQUMsRUFBRTtRQUNoQyxJQUFJRixNQUFNO1lBQ1JOLFlBQVlNO1lBQ1osTUFBTUcsU0FBUyxJQUFJQztZQUNuQkQsT0FBT0UsTUFBTSxHQUFHLENBQUNOLElBQU1ILGVBQWVHLEVBQUVFLE1BQU0sRUFBRUs7WUFDaERILE9BQU9JLGFBQWEsQ0FBQ1A7UUFDdkI7SUFDRjtJQUVBLE1BQU1RLGFBQWE7UUFDakIsSUFBSSxDQUFDZixVQUFVO1FBRWYsTUFBTWdCLFdBQVcsSUFBSUM7UUFDckJELFNBQVNFLE1BQU0sQ0FBQyxRQUFRbEI7UUFFeEIsSUFBSTtZQUNGLE1BQU1tQixXQUFXLE1BQU1DLE1BQU0seUJBQXlCO2dCQUNwREMsUUFBUTtnQkFDUkMsTUFBTU47WUFDUjtZQUVBLElBQUlHLFNBQVNJLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1MLFNBQVNNLElBQUk7Z0JBQ2hDakUsYUFBYWtFLENBQUFBLE9BQVM7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRTlELFdBQVc0RCxLQUFLRyxPQUFPO29CQUFDO2dCQUN6RDFCLFlBQVk7Z0JBQ1pFLGVBQWU7Z0JBQ2ZoRCxNQUFNO29CQUNKeUUsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsUUFBUTtvQkFDUkMsVUFBVTtnQkFDWjtZQUNGO1FBQ0YsRUFBRSxPQUFPL0UsT0FBTztZQUNkRyxNQUFNO2dCQUNKeUUsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsUUFBUTtnQkFDUkMsVUFBVTtZQUNaO1FBQ0Y7SUFDRjtJQUVBLE1BQU1DLGlCQUFpQjtRQUNyQixJQUFJO1lBQ0YsTUFBTSxDQUFDQyxlQUFlQyxjQUFjLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO2dCQUN2RGhCLE1BQU07Z0JBQ05BLE1BQU07YUFDUDtZQUVELElBQUlhLGNBQWNWLEVBQUUsRUFBRTtnQkFDcEIsTUFBTWMsUUFBUSxNQUFNSixjQUFjUixJQUFJO2dCQUN0Q2pFLGFBQWFrRSxDQUFBQSxPQUFTO3dCQUNwQixHQUFHQSxJQUFJO3dCQUNQL0QsV0FBVzBFLE1BQU10RSxJQUFJO3dCQUNyQkgsV0FBV3lFLE1BQU05RyxJQUFJO29CQUN2QjtZQUNGO1lBRUEsSUFBSTJHLGNBQWNYLEVBQUUsRUFBRTtnQkFDcEIsTUFBTWUsWUFBWSxNQUFNSixjQUFjVCxJQUFJO2dCQUMxQyxNQUFNYyxNQUFNakcsTUFBTUMsT0FBTyxDQUFDK0YsYUFBYUEsWUFBWUEsVUFBVXJFLEtBQUssSUFBSSxFQUFFO2dCQUN4RUMsU0FBU3FFLElBQUlDLElBQUksQ0FBQyxDQUFDQyxHQUFTQyxJQUFZQSxFQUFFQyxRQUFRLEdBQUdGLEVBQUVFLFFBQVE7WUFDakU7UUFDRixFQUFFLE9BQU8zRixPQUFPO1lBQ2RHLE1BQU07Z0JBQ0p5RSxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxRQUFRO2dCQUNSQyxVQUFVO1lBQ1o7UUFDRixTQUFVO1lBQ1IxRCxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU11RSxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGLE1BQU16QixXQUFXLE1BQU1DLE1BQU07WUFDN0IsSUFBSSxDQUFDRCxTQUFTSSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSXNCLE1BQU07WUFDbEI7WUFDQSxNQUFNckIsT0FBTyxNQUFNTCxTQUFTTSxJQUFJO1lBQ2hDLE1BQU1xQixpQkFBaUIsQ0FBQ3RCLFFBQVEsRUFBRSxFQUFFZ0IsSUFBSSxDQUFDLENBQUNDLEdBQVlDO2dCQUNwRCxJQUFJRCxFQUFFM0UsSUFBSSxLQUFLLEtBQUs0RSxFQUFFNUUsSUFBSSxLQUFLLEdBQUcsT0FBTyxDQUFDO2dCQUMxQyxJQUFJMkUsRUFBRTNFLElBQUksS0FBSyxLQUFLNEUsRUFBRTVFLElBQUksS0FBSyxHQUFHLE9BQU87Z0JBQ3pDLE9BQU8yRSxFQUFFRSxRQUFRLEdBQUdELEVBQUVDLFFBQVE7WUFDaEM7WUFDQXZFLFlBQVkwRTtRQUNkLEVBQUUsT0FBTzlGLE9BQU87WUFDZEcsTUFBTTtnQkFDSnlFLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFFBQVE7Z0JBQ1JDLFVBQVU7WUFDWjtRQUNGLFNBQVU7WUFDUnhELG1CQUFtQjtRQUNyQjtJQUNGO0lBRUE1RixnREFBU0E7c0NBQUM7WUFDUnFKO1lBQ0FZO1FBQ0Y7cUNBQUcsRUFBRTtJQUVMLE1BQU1HLHNCQUFzQixDQUFDQyxTQUE4QkM7UUFDekR6RixhQUFha0UsQ0FBQUEsT0FBUztnQkFBRSxHQUFHQSxJQUFJO2dCQUFFLENBQUNzQixRQUFRLEVBQUVDO1lBQU07SUFDcEQ7SUFFQSxNQUFNQyxlQUFlO1FBQ25CLElBQUkxRSxVQUFVekQsZUFBZTtRQUU3QjBELFVBQVU7UUFDVnZEO1FBRUEsSUFBSTtZQUNGLE1BQU1pRyxXQUFXLE1BQU1DLE1BQU0seUJBQXlCO2dCQUNwREMsUUFBUTtnQkFDUjhCLFNBQVM7b0JBQUUsZ0JBQWdCO2dCQUFtQjtnQkFDOUM3QixNQUFNOEIsS0FBS0MsU0FBUyxDQUFDOUY7WUFDdkI7WUFFQSxJQUFJNEQsU0FBU0ksRUFBRSxFQUFFO2dCQUNmcEUsTUFBTTtvQkFDSnlFLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLFFBQVE7b0JBQ1JDLFVBQVU7Z0JBQ1o7WUFDRixPQUFPO2dCQUNMLE1BQU0sSUFBSWMsTUFBTTtZQUNsQjtRQUNGLEVBQUUsT0FBTzdGLE9BQU87WUFDZEcsTUFBTTtnQkFDSnlFLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFFBQVE7Z0JBQ1JDLFVBQVU7WUFDWjtRQUNGLFNBQVU7WUFDUnRELFVBQVU7UUFDWjtJQUNGO0lBRUEsTUFBTTZFLGlCQUFpQixDQUFDQztRQUN0QjFELGdCQUFnQjBEO1FBQ2hCaEU7SUFDRjtJQUVBLE1BQU1pRSxvQkFBb0IsQ0FBQ0M7UUFDekIxRCxtQkFBbUIwRDtRQUNuQnhFO0lBQ0Y7SUFFQSxNQUFNeUUsc0JBQXNCO1FBQzFCN0U7SUFDRjtJQUVBLE1BQU04RSxtQkFBbUI7UUFDdkJ2RTtJQUNGO0lBRUEsTUFBTXdFLHNCQUFzQixPQUFPQztRQUNqQyxJQUFJOUksZUFBZTtRQUVuQixJQUFJO1lBQ0ZHO1lBQ0EsTUFBTWlHLFdBQVcsTUFBTUMsTUFBTSxDQUFDLHNCQUFzQixFQUFFeUMsV0FBVyxFQUFFO2dCQUNqRXhDLFFBQVE7WUFDVjtZQUVBLElBQUlGLFNBQVNJLEVBQUUsRUFBRTtnQkFDZixNQUFNcUI7Z0JBQ056RixNQUFNO29CQUNKeUUsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsUUFBUTtvQkFDUkMsVUFBVTtnQkFDWjtZQUNGO1FBQ0YsRUFBRSxPQUFPL0UsT0FBTztZQUNkRyxNQUFNO2dCQUNKeUUsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsUUFBUTtnQkFDUkMsVUFBVTtZQUNaO1FBQ0Y7SUFDRjtJQUVBLE1BQU0rQixnQkFBZ0IsQ0FBQ0M7UUFDckIsSUFBSSxDQUFDQSxZQUFZLENBQUM1RixVQUFVLE9BQU87UUFDbkMsTUFBTTZGLFNBQVM3RixTQUFTOEYsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtKO1FBQzNDLE9BQU9DLFNBQVNBLE9BQU9qRyxJQUFJLEdBQUc7SUFDaEM7SUFFQSxJQUFJM0QsU0FBUztRQUNYLHFCQUNFLDhEQUFDNUIsMERBQU1BO3NCQUNMLDRFQUFDaEMsa1VBQVNBO2dCQUFDNE4sTUFBSztnQkFBZUMsSUFBSTswQkFDakMsNEVBQUN6TiwrVEFBTUE7b0JBQUMwTixTQUFTOztzQ0FDZiw4REFBQzlNLGlVQUFRQTs0QkFBQytNLFFBQU87Ozs7OztzQ0FDakIsOERBQUMvTSxpVUFBUUE7NEJBQUMrTSxRQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSzNCO0lBRUEscUJBQ0UsOERBQUMvTCwwREFBTUE7a0JBQ0wsNEVBQUNoQyxrVUFBU0E7WUFBQzROLE1BQUs7WUFBZUMsSUFBSTs7OEJBQ2pDLDhEQUFDek4sK1RBQU1BO29CQUFDME4sU0FBUztvQkFBR0UsT0FBTTs7c0NBRXhCLDhEQUFDak8sNFRBQUdBOzRCQUNGa08sSUFBRzs0QkFDSEMsR0FBRzs0QkFDSEMsU0FBUTs0QkFDUkMsZ0JBQWU7NEJBQ2ZDLFFBQU87NEJBQ1BDLGFBQVk7NEJBQ1pDLFdBQVU7c0NBRVYsNEVBQUNsTywrVEFBTUE7Z0NBQUNtTyxTQUFRO2dDQUFnQlIsT0FBTTs7a0RBQ3BDLDhEQUFDak8sNFRBQUdBOzswREFDRiw4REFBQ0UsZ1VBQU9BO2dEQUNONEQsTUFBSztnREFDTDRLLFlBQVc7Z0RBQ1hDLFFBQU87O2tFQUVQLDhEQUFDcE8sNlRBQUlBO3dEQUFDcU8sSUFBSXJMLGdPQUFRQTt3REFBRXNMLElBQUk7Ozs7OztvREFBSzs7Ozs7OzswREFHL0IsOERBQUMxTyw2VEFBSUE7Z0RBQUM4RSxPQUFNO2dEQUFXNkosSUFBSTs7b0RBQUc7b0RBQ0VqSSxlQUFlRyxVQUFVSSxTQUFTOzs7Ozs7Ozs7Ozs7O2tEQUdwRSw4REFBQ3hHLCtUQUFNQTt3Q0FDTG1PLHdCQUFVLDhEQUFDak0sOE5BQU1BOzs7Ozt3Q0FDakJrTSxhQUFZO3dDQUNaQyxTQUFTdEM7d0NBQ1R1QyxXQUFXakg7d0NBQ1hrSCxZQUFZM0s7d0NBQ1pWLE1BQUs7a0RBQ047Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9MLDhEQUFDM0MsNlRBQUlBOzRCQUFDNk4sYUFBWTs0QkFBT0ksTUFBTTs7OENBQzdCLDhEQUFDaE8sZ1VBQU9BOztzREFDTiw4REFBQ0UsNFRBQUdBOzs4REFDRiw4REFBQ2YsNlRBQUlBO29EQUFDcU8sSUFBSWxNLGtPQUFVQTtvREFBRW1NLElBQUk7Ozs7OztnREFBSzs7Ozs7OztzREFHakMsOERBQUN2Tiw0VEFBR0E7OzhEQUNGLDhEQUFDZiw2VEFBSUE7b0RBQUNxTyxJQUFJbEwsc0ZBQVNBO29EQUFFbUwsSUFBSTs7Ozs7O2dEQUFLOzs7Ozs7O3NEQUdoQyw4REFBQ3ZOLDRUQUFHQTs7OERBQ0YsOERBQUNmLDZUQUFJQTtvREFBQ3FPLElBQUluTCw4TkFBTUE7b0RBQUVvTCxJQUFJOzs7Ozs7Z0RBQUs7Ozs7Ozs7c0RBRzdCLDhEQUFDdk4sNFRBQUdBOzs4REFDRiw4REFBQ2YsNlRBQUlBO29EQUFDcU8sSUFBSXBMLDZOQUFLQTtvREFBRXFMLElBQUk7Ozs7OztnREFBSzs7Ozs7Ozs7Ozs7Ozs4Q0FLOUIsOERBQUN4TixrVUFBU0E7O3NEQUVSLDhEQUFDRSxpVUFBUUE7c0RBQ1AsNEVBQUNsQiwrVEFBTUE7Z0RBQUMwTixTQUFTO2dEQUFHRSxPQUFNOztrRUFFeEIsOERBQUN4Tiw2VEFBSUE7OzBFQUNILDhEQUFDQyxtVUFBVUE7MEVBQ1QsNEVBQUNSLGdVQUFPQTtvRUFBQzRELE1BQUs7OEVBQUs7Ozs7Ozs7Ozs7OzBFQUVyQiw4REFBQ25ELGlVQUFRQTswRUFDUCw0RUFBQ1AsbVVBQVVBO29FQUFDaVAsU0FBUzt3RUFBRUMsTUFBTTt3RUFBR0MsSUFBSTtvRUFBRTtvRUFBR3hCLFNBQVM7O3NGQUNoRCw4REFBQzFOLCtUQUFNQTs0RUFBQzBOLFNBQVM7NEVBQUdFLE9BQU07OzhGQUN4Qiw4REFBQ25OLG9VQUFXQTs7c0dBQ1YsOERBQUNDLGtVQUFTQTtzR0FBQzs7Ozs7O3NHQUNYLDhEQUFDRiw4VEFBS0E7NEZBQ0o2TCxPQUFPMUYsVUFBVUcsT0FBTzs0RkFDeEJxSSxVQUFVLENBQUN6RixJQUFNeUMsb0JBQW9CLFdBQVd6QyxFQUFFRSxNQUFNLENBQUN5QyxLQUFLOzRGQUM5RCtDLGFBQVk7Ozs7Ozs7Ozs7Ozs4RkFHaEIsOERBQUMzTyxvVUFBV0E7O3NHQUNWLDhEQUFDQyxrVUFBU0E7c0dBQUM7Ozs7OztzR0FDWCw4REFBQ0YsOFRBQUtBOzRGQUNKNkwsT0FBTzFGLFVBQVVFLE1BQU07NEZBQ3ZCc0ksVUFBVSxDQUFDekYsSUFBTXlDLG9CQUFvQixVQUFVekMsRUFBRUUsTUFBTSxDQUFDeUMsS0FBSzs0RkFDN0QrQyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0ZBSWxCLDhEQUFDcFAsK1RBQU1BOzRFQUFDME4sU0FBUzs0RUFBR0UsT0FBTTs7OEZBQ3hCLDhEQUFDbk4sb1VBQVdBOztzR0FDViw4REFBQ0Msa1VBQVNBO3NHQUFDOzs7Ozs7c0dBQ1gsOERBQUNGLDhUQUFLQTs0RkFDSjZMLE9BQU8xRixXQUFXUSxRQUFROzRGQUMxQmtJLFVBQVU7NEZBQ1Z4QixJQUFHOzRGQUNIeUIsT0FBTztnR0FBRXpCLElBQUk7NEZBQVc7Ozs7Ozs7Ozs7Ozs4RkFHNUIsOERBQUNwTixvVUFBV0E7O3NHQUNWLDhEQUFDQyxrVUFBU0E7c0dBQUM7Ozs7OztzR0FDWCw4REFBQ0YsOFRBQUtBOzRGQUNKNkwsT0FBTzFGLFdBQVc0SSxlQUFlOzRGQUNqQ0YsVUFBVTs0RkFDVnhCLElBQUc7NEZBQ0h5QixPQUFPO2dHQUFFekIsSUFBSTs0RkFBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBU3BDLDhEQUFDek4sNlRBQUlBOzswRUFDSCw4REFBQ0MsbVVBQVVBOzBFQUNULDRFQUFDSiwrVEFBTUE7b0VBQUNtTyxTQUFROztzRkFDZCw4REFBQ3ZPLGdVQUFPQTs0RUFBQzRELE1BQUs7OzhGQUNaLDhEQUFDdkQsNlRBQUlBO29GQUFDcU8sSUFBSWpNLCtOQUFPQTtvRkFBRWtNLElBQUk7Ozs7OztnRkFBSztnRkFDcEJuSCxNQUFNbUksTUFBTTtnRkFBQzs7Ozs7OztzRkFFdkIsOERBQUNqUCwrVEFBTUE7NEVBQ0xtTyx3QkFBVSw4REFBQy9MLDhOQUFNQTs7Ozs7NEVBQ2pCZ00sYUFBWTs0RUFDWkMsU0FBUzdCOzRFQUNUK0IsWUFBWTNLO3NGQUNiOzs7Ozs7Ozs7Ozs7Ozs7OzswRUFLTCw4REFBQzdELGlVQUFRQTswRUFDTmtELHdCQUNDLDhEQUFDeEQsK1RBQU1BO29FQUFDME4sU0FBUzs4RUFDZDsyRUFBSWhJLE1BQU07cUVBQUcsQ0FBQytKLEdBQUcsQ0FBQyxDQUFDQyxHQUFHQyxrQkFDckIsOERBQUMvTyxpVUFBUUE7NEVBQVMrTSxRQUFPOzJFQUFWZ0M7Ozs7Ozs7Ozt5RkFJbkIsOERBQUNoUSw0VEFBR0E7b0VBQUNpUSxXQUFVOzhFQUNiLDRFQUFDek8sOFRBQUtBO3dFQUFDME8sU0FBUTt3RUFBU3BNLE1BQUs7OzBGQUMzQiw4REFBQ3JDLDhUQUFLQTswRkFDSiw0RUFBQ0UsMlRBQUVBOztzR0FDRCw4REFBQ0MsMlRBQUVBO3NHQUFDOzs7Ozs7c0dBQ0osOERBQUNBLDJUQUFFQTtzR0FBQzs7Ozs7O3NHQUNKLDhEQUFDQSwyVEFBRUE7c0dBQUM7Ozs7OztzR0FDSiw4REFBQ0EsMlRBQUVBO3NHQUFDOzs7Ozs7Ozs7Ozs7Ozs7OzswRkFHUiw4REFBQ0YsOFRBQUtBOzBGQUNILENBQUNnRyxTQUFTLEVBQUUsRUFBRW9JLEdBQUcsQ0FBQyxDQUFDOUMscUJBQ2xCLDhEQUFDckwsMlRBQUVBOzswR0FDRCw4REFBQ0UsMlRBQUVBOzBHQUNELDRFQUFDdkIsK1RBQU1BOztzSEFDTCw4REFBQ04sNFRBQUdBOzRHQUNGbVEsR0FBRzs0R0FDSEMsR0FBRzs0R0FDSGhDLFNBQVE7NEdBQ1JGLElBQUlsQixLQUFLL0gsS0FBSyxHQUFHLENBQUMsQ0FBQyxFQUFFK0gsS0FBSy9ILEtBQUssQ0FBQ29MLFFBQVEsQ0FBQyxJQUFJQyxRQUFRLENBQUMsR0FBRyxNQUFNLEdBQUc7Ozs7OztzSEFFcEUsOERBQUNuUSw2VEFBSUE7c0hBQUU2TSxLQUFLeEYsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEdBR3BCLDhEQUFDM0YsMlRBQUVBOzBHQUNELDRFQUFDYiw4VEFBS0E7b0dBQUNnTyxhQUFZOzhHQUFPOzs7Ozs7Ozs7OzswR0FFNUIsOERBQUNuTiwyVEFBRUE7MEdBQ0QsNEVBQUN2QiwrVEFBTUE7b0dBQUNpUSxNQUFLO29HQUFPeEMsU0FBUzs7d0dBQ3pCbEksQ0FBQUEsa0JBQWtCbUgsS0FBSy9HLFdBQVcsS0FBSyxFQUFFLEVBQUV1SyxLQUFLLENBQUMsR0FBRyxHQUFHVixHQUFHLENBQUMsQ0FBQ1cscUJBQzVELDhEQUFDelAsOFRBQUtBO2dIQUVKZ08sYUFBYTdKLGlCQUFpQixDQUFDc0wsS0FBSyxFQUFFeEwsU0FBUztnSEFDL0NuQixNQUFLOzBIQUVKcUIsaUJBQWlCLENBQUNzTCxLQUFLLEVBQUV2TCxTQUFTdUw7K0dBSjlCQTs7Ozs7d0dBT1I1SyxrQkFBa0JtSCxLQUFLL0csV0FBVyxFQUFFNEosTUFBTSxHQUFHLG1CQUM1Qyw4REFBQzdPLDhUQUFLQTs0R0FBQ2dPLGFBQVk7NEdBQU9sTCxNQUFLOztnSEFBSztnSEFDaEMrQixrQkFBa0JtSCxLQUFLL0csV0FBVyxFQUFFNEosTUFBTSxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEdBS3ZELDhEQUFDaE8sMlRBQUVBOzBHQUNELDRFQUFDdkIsK1RBQU1BO29HQUFDeU4sU0FBUzs7c0hBQ2YsOERBQUNoTSxnVUFBT0E7NEdBQUNtRCxPQUFNO3NIQUNiLDRFQUFDcEQsbVVBQVVBO2dIQUNUNE8sY0FBVztnSEFDWDFMLG9CQUFNLDhEQUFDbkMsK05BQU9BOzs7OztnSEFDZGlCLE1BQUs7Z0hBQ0xvTSxTQUFRO2dIQUNSbEIsYUFBWTtnSEFDWkMsU0FBUyxJQUFNbEMsZUFBZUM7Z0hBQzlCbUMsWUFBWTNLOzs7Ozs7Ozs7OztzSEFHaEIsOERBQUN6QyxnVUFBT0E7NEdBQUNtRCxPQUFNO3NIQUNiLDRFQUFDcEQsbVVBQVVBO2dIQUNUNE8sY0FBVztnSEFDWDFMLG9CQUFNLDhEQUFDakMsZ09BQVFBOzs7OztnSEFDZmUsTUFBSztnSEFDTG9NLFNBQVE7Z0hBQ1JsQixhQUFZO2dIQUNaRyxZQUFZM0s7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VGQXJEYndJLEtBQUtZLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQW9FOUIsOERBQUNuTiw2VEFBSUE7OzBFQUNILDhEQUFDQyxtVUFBVUE7MEVBQ1QsNEVBQUNKLCtUQUFNQTtvRUFBQ21PLFNBQVE7O3NGQUNkLDhEQUFDdk8sZ1VBQU9BOzRFQUFDNEQsTUFBSzs7OEZBQ1osOERBQUN2RCw2VEFBSUE7b0ZBQUNxTyxJQUFJaE0sOE5BQU1BO29GQUFFaU0sSUFBSTs7Ozs7O2dGQUFLO2dGQUNoQmpILFNBQVNpSSxNQUFNO2dGQUFDOzs7Ozs7O3NGQUU3Qiw4REFBQ2pQLCtUQUFNQTs0RUFDTG1PLHdCQUFVLDhEQUFDL0wsOE5BQU1BOzs7Ozs0RUFDakJnTSxhQUFZOzRFQUNaQyxTQUFTM0c7c0ZBQ1Y7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUtMLDhEQUFDM0gsaVVBQVFBOzBFQUNOb0gsZ0NBQ0MsOERBQUMxSCwrVEFBTUE7b0VBQUMwTixTQUFTOzhFQUNkOzJFQUFJaEksTUFBTTtxRUFBRyxDQUFDK0osR0FBRyxDQUFDLENBQUNDLEdBQUdDLGtCQUNyQiw4REFBQy9PLGlVQUFRQTs0RUFBUytNLFFBQU87MkVBQVZnQzs7Ozs7Ozs7OzJFQUdqQnBJLFNBQVNpSSxNQUFNLEtBQUssa0JBQ3RCLDhEQUFDMVAsNlRBQUlBO29FQUFDOEUsT0FBTTtvRUFBVzBMLFdBQVU7b0VBQVM3QyxJQUFJOzhFQUFHOzs7Ozt5RkFJakQsOERBQUM5Tiw0VEFBR0E7b0VBQUNpUSxXQUFVOzhFQUNiLDRFQUFDek8sOFRBQUtBO3dFQUFDME8sU0FBUTt3RUFBU3BNLE1BQUs7OzBGQUMzQiw4REFBQ3JDLDhUQUFLQTswRkFDSiw0RUFBQ0UsMlRBQUVBOztzR0FDRCw4REFBQ0MsMlRBQUVBO3NHQUFDOzs7Ozs7c0dBQ0osOERBQUNBLDJUQUFFQTtzR0FBQzs7Ozs7O3NHQUNKLDhEQUFDQSwyVEFBRUE7c0dBQUM7Ozs7OztzR0FDSiw4REFBQ0EsMlRBQUVBO3NHQUFDOzs7Ozs7c0dBQ0osOERBQUNBLDJUQUFFQTtzR0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEZBR1IsOERBQUNGLDhUQUFLQTswRkFDSCxDQUFDa0csWUFBWSxFQUFFLEVBQUVrSSxHQUFHLENBQUMsQ0FBQzVDO29GQUNyQixNQUFNMEQsYUFBYTdMLG1CQUFtQixDQUFDbUksUUFBUTNGLElBQUksQ0FBQyxJQUFJO3dGQUFFdkMsTUFBTS9CLHVPQUFlQTt3RkFBRWdDLE9BQU87d0ZBQVFDLE9BQU87b0ZBQVE7b0ZBQy9HLHFCQUNFLDhEQUFDdkQsMlRBQUVBOzswR0FDRCw4REFBQ0UsMlRBQUVBOzBHQUNELDRFQUFDdkIsK1RBQU1BOztzSEFDTCw4REFBQ0MsNlRBQUlBOzRHQUNIcU8sSUFBSWdDLFdBQVc1TCxJQUFJOzRHQUNuQkMsT0FBTyxHQUFHMkwsV0FBVzNMLEtBQUssQ0FBQyxJQUFJLENBQUM7Ozs7OztzSEFFbEMsOERBQUM5RSw2VEFBSUE7c0hBQUUrTSxRQUFRMUYsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEdBR3ZCLDhEQUFDM0YsMlRBQUVBOzBHQUNELDRFQUFDYiw4VEFBS0E7b0dBQUNnTyxhQUFhNEIsV0FBVzNMLEtBQUs7OEdBQUcyTCxXQUFXMUwsS0FBSzs7Ozs7Ozs7Ozs7MEdBRXpELDhEQUFDckQsMlRBQUVBOzBHQUNELDRFQUFDMUIsNlRBQUlBO29HQUFDOEUsT0FBTTs4R0FBWXNJLGNBQWNMLFFBQVEyRCxTQUFTOzs7Ozs7Ozs7OzswR0FFekQsOERBQUNoUCwyVEFBRUE7MEdBQ0QsNEVBQUMxQiw2VEFBSUE7b0dBQUM4RSxPQUFNOzhHQUFZaUksUUFBUWQsUUFBUTs7Ozs7Ozs7Ozs7MEdBRTFDLDhEQUFDdkssMlRBQUVBOzBHQUNELDRFQUFDdkIsK1RBQU1BO29HQUFDeU4sU0FBUzs7c0hBQ2YsOERBQUNoTSxnVUFBT0E7NEdBQUNtRCxPQUFNO3NIQUNiLDRFQUFDcEQsbVVBQVVBO2dIQUNUNE8sY0FBVztnSEFDWDFMLG9CQUFNLDhEQUFDbkMsK05BQU9BOzs7OztnSEFDZGlCLE1BQUs7Z0hBQ0xvTSxTQUFRO2dIQUNSbEIsYUFBWTtnSEFDWkMsU0FBUyxJQUFNaEMsa0JBQWtCQztnSEFDakNpQyxZQUFZM0s7Ozs7Ozs7Ozs7O3NIQUdoQiw4REFBQ3pDLGdVQUFPQTs0R0FBQ21ELE9BQU07c0hBQ2IsNEVBQUNwRCxtVUFBVUE7Z0hBQ1Q0TyxjQUFXO2dIQUNYMUwsb0JBQU0sOERBQUNqQyxnT0FBUUE7Ozs7O2dIQUNmZSxNQUFLO2dIQUNMb00sU0FBUTtnSEFDUmxCLGFBQVk7Z0hBQ1pDLFNBQVMsSUFBTTVCLG9CQUFvQkgsUUFBUVUsRUFBRTtnSEFDN0N1QixZQUFZM0s7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VGQXhDYjBJLFFBQVFVLEVBQUU7Ozs7O2dGQStDdkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFXaEIsOERBQUNyTSxpVUFBUUE7c0RBQ1AsNEVBQUNsQiwrVEFBTUE7Z0RBQUMwTixTQUFTO2dEQUFHRSxPQUFNOztrRUFDeEIsOERBQUNqTyw0VEFBR0E7OzBFQUNGLDhEQUFDRSxnVUFBT0E7Z0VBQUM0RCxNQUFLO2dFQUFLZ04sSUFBSTswRUFBRzs7Ozs7OzBFQUMxQiw4REFBQzNRLDZUQUFJQTtnRUFBQzhFLE9BQU07Z0VBQVcwSyxPQUFPO29FQUFFMUssT0FBTztnRUFBVztnRUFBRzZMLElBQUk7MEVBQUc7Ozs7Ozs7Ozs7OztrRUFLOUQsOERBQUMxUSxtVUFBVUE7d0RBQUNpUCxTQUFTOzREQUFFQyxNQUFNOzREQUFHQyxJQUFJO3dEQUFFO3dEQUFHeEIsU0FBUzs7MEVBQ2hELDhEQUFDdE4sNlRBQUlBOztrRkFDSCw4REFBQ0MsbVVBQVVBO2tGQUNULDRFQUFDUixnVUFBT0E7NEVBQUM0RCxNQUFLO3NGQUFLOzs7Ozs7Ozs7OztrRkFFckIsOERBQUNuRCxpVUFBUUE7a0ZBQ1AsNEVBQUNOLCtUQUFNQTs0RUFBQzBOLFNBQVM7NEVBQUdFLE9BQU07OzhGQUN4Qiw4REFBQzlOLDZUQUFJQTtvRkFBQzRRLFVBQVM7b0ZBQUs5TCxPQUFNOzhGQUFXOzs7Ozs7OEZBR3JDLDhEQUFDckUsK1RBQU1BO29GQUNMbU8sd0JBQVUsOERBQUNyTCxzRkFBU0E7Ozs7O29GQUNwQnNMLGFBQVk7b0ZBQ1pDLFNBQVM5RjtvRkFDVHJGLE1BQUs7OEZBQ047Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQU9QLDhEQUFDckQsNlRBQUlBOztrRkFDSCw4REFBQ0MsbVVBQVVBO2tGQUNULDRFQUFDUixnVUFBT0E7NEVBQUM0RCxNQUFLO3NGQUFLOzs7Ozs7Ozs7OztrRkFFckIsOERBQUNuRCxpVUFBUUE7a0ZBQ1AsNEVBQUNOLCtUQUFNQTs0RUFBQzBOLFNBQVM7NEVBQUdFLE9BQU07OzhGQUN4Qiw4REFBQzlOLDZUQUFJQTtvRkFBQzRRLFVBQVM7b0ZBQUs5TCxPQUFNO29GQUFXNkwsSUFBSTs4RkFBRzs7Ozs7OzhGQUc1Qyw4REFBQ3hRLCtUQUFNQTtvRkFBQ2lRLE1BQUs7b0ZBQU94QyxTQUFTOztzR0FDM0IsOERBQUMvTSw4VEFBS0E7NEZBQUNnTyxhQUFZO3NHQUFPOzs7Ozs7c0dBQzFCLDhEQUFDaE8sOFRBQUtBOzRGQUFDZ08sYUFBWTtzR0FBUzs7Ozs7O3NHQUM1Qiw4REFBQ2hPLDhUQUFLQTs0RkFBQ2dPLGFBQVk7c0dBQVE7Ozs7OztzR0FDM0IsOERBQUNoTyw4VEFBS0E7NEZBQUNnTyxhQUFZO3NHQUFTOzs7Ozs7c0dBQzVCLDhEQUFDaE8sOFRBQUtBOzRGQUFDZ08sYUFBWTtzR0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFVeEMsOERBQUN6TixpVUFBUUE7c0RBQ1AsNEVBQUNsQiwrVEFBTUE7Z0RBQUMwTixTQUFTO2dEQUFHRSxPQUFNOztrRUFDeEIsOERBQUNqTyw0VEFBR0E7OzBFQUNGLDhEQUFDRSxnVUFBT0E7Z0VBQUM0RCxNQUFLO2dFQUFLZ04sSUFBSTswRUFBRzs7Ozs7OzBFQUMxQiw4REFBQzNRLDZUQUFJQTtnRUFBQzhFLE9BQU07Z0VBQVcwSyxPQUFPO29FQUFFMUssT0FBTztnRUFBVztnRUFBRzZMLElBQUk7MEVBQUc7Ozs7Ozs7Ozs7OztrRUFLOUQsOERBQUMxUSxtVUFBVUE7d0RBQUNpUCxTQUFTOzREQUFFQyxNQUFNOzREQUFHQyxJQUFJO3dEQUFFO3dEQUFHeEIsU0FBUzs7MEVBQ2hELDhEQUFDdE4sNlRBQUlBOztrRkFDSCw4REFBQ0MsbVVBQVVBO2tGQUNULDRFQUFDUixnVUFBT0E7NEVBQUM0RCxNQUFLO3NGQUFLOzs7Ozs7Ozs7OztrRkFFckIsOERBQUNuRCxpVUFBUUE7a0ZBQ1AsNEVBQUNOLCtUQUFNQTs0RUFBQzBOLFNBQVM7NEVBQUdFLE9BQU07OzhGQUN4Qiw4REFBQ3JOLCtUQUFNQTtvRkFDTG1PLHdCQUFVLDhEQUFDdkwsNk5BQUtBOzs7OztvRkFDaEJ3TCxhQUFZO29GQUNaQyxTQUFTLElBQU0rQixPQUFPQyxJQUFJLENBQUMscUNBQXFDOzhGQUNqRTs7Ozs7OzhGQUdELDhEQUFDclEsK1RBQU1BO29GQUNMbU8sd0JBQVUsOERBQUM5TCx1T0FBZUE7Ozs7O29GQUMxQitMLGFBQVk7b0ZBQ1pDLFNBQVMsSUFBTStCLE9BQU9DLElBQUksQ0FBQywrQkFBK0I7OEZBQzNEOzs7Ozs7OEZBR0QsOERBQUNyUSwrVEFBTUE7b0ZBQ0xtTyx3QkFBVSw4REFBQzlMLHVPQUFlQTs7Ozs7b0ZBQzFCK0wsYUFBWTtvRkFDWkMsU0FBUyxJQUFNK0IsT0FBT0MsSUFBSSxDQUFDLHdCQUF3QjtvRkFDbkQ5QixVQUFVOzhGQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFPUCw4REFBQzFPLDZUQUFJQTs7a0ZBQ0gsOERBQUNDLG1VQUFVQTtrRkFDVCw0RUFBQ1IsZ1VBQU9BOzRFQUFDNEQsTUFBSztzRkFBSzs7Ozs7Ozs7Ozs7a0ZBRXJCLDhEQUFDbkQsaVVBQVFBO2tGQUNQLDRFQUFDTiwrVEFBTUE7NEVBQUMwTixTQUFTOzRFQUFHRSxPQUFNOzs4RkFDeEIsOERBQUNyTiwrVEFBTUE7b0ZBQ0xtTyx3QkFBVSw4REFBQ3JNLGtPQUFVQTs7Ozs7b0ZBQ3JCc00sYUFBWTtvRkFDWkMsU0FBUyxJQUFNK0IsT0FBT0MsSUFBSSxDQUFDLGlCQUFpQjs4RkFDN0M7Ozs7Ozs4RkFHRCw4REFBQ3JRLCtUQUFNQTtvRkFDTG1PLHdCQUFVLDhEQUFDck0sa09BQVVBOzs7OztvRkFDckJzTSxhQUFZO29GQUNaQyxTQUFTLElBQU0rQixPQUFPQyxJQUFJLENBQUMsbUJBQW1COzhGQUMvQzs7Ozs7OzhGQUdELDhEQUFDclEsK1RBQU1BO29GQUNMbU8sd0JBQVUsOERBQUNyTSxrT0FBVUE7Ozs7O29GQUNyQnNNLGFBQVk7b0ZBQ1pDLFNBQVMsSUFBTStCLE9BQU9DLElBQUksQ0FBQyx1QkFBdUI7OEZBQ25EOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQVdiLDhEQUFDMVAsaVVBQVFBO3NEQUNQLDRFQUFDbEIsK1RBQU1BO2dEQUFDME4sU0FBUztnREFBR0UsT0FBTTs7a0VBQ3hCLDhEQUFDak8sNFRBQUdBOzswRUFDRiw4REFBQ0UsZ1VBQU9BO2dFQUFDNEQsTUFBSztnRUFBS2dOLElBQUk7MEVBQUc7Ozs7OzswRUFDMUIsOERBQUMzUSw2VEFBSUE7Z0VBQUM4RSxPQUFNO2dFQUFXMEssT0FBTztvRUFBRTFLLE9BQU87Z0VBQVc7Z0VBQUc2TCxJQUFJOzBFQUFHOzs7Ozs7Ozs7Ozs7a0VBSzlELDhEQUFDMVEsbVVBQVVBO3dEQUFDaVAsU0FBUzs0REFBRUMsTUFBTTs0REFBR0MsSUFBSTt3REFBRTt3REFBR3hCLFNBQVM7OzBFQUNoRCw4REFBQ3ROLDZUQUFJQTs7a0ZBQ0gsOERBQUNDLG1VQUFVQTtrRkFDVCw0RUFBQ1IsZ1VBQU9BOzRFQUFDNEQsTUFBSztzRkFBSzs7Ozs7Ozs7Ozs7a0ZBRXJCLDhEQUFDbkQsaVVBQVFBOzswRkFDUCw4REFBQ1IsNlRBQUlBO2dGQUFDOEUsT0FBTTtnRkFBVzhMLFVBQVM7Z0ZBQUtELElBQUk7MEZBQUc7Ozs7OzswRkFHNUMsOERBQUN6USwrVEFBTUE7Z0ZBQUMwTixTQUFTO2dGQUFHRSxPQUFNOztrR0FDeEIsOERBQUM5Tiw2VEFBSUE7d0ZBQUM0USxVQUFTO2tHQUFLOzs7Ozs7a0dBQ3BCLDhEQUFDNVEsNlRBQUlBO3dGQUFDNFEsVUFBUztrR0FBSzs7Ozs7O2tHQUNwQiw4REFBQzVRLDZUQUFJQTt3RkFBQzRRLFVBQVM7a0dBQUs7Ozs7OztrR0FDcEIsOERBQUM1USw2VEFBSUE7d0ZBQUM0USxVQUFTO2tHQUFLOzs7Ozs7a0dBQ3BCLDhEQUFDNVEsNlRBQUlBO3dGQUFDNFEsVUFBUztrR0FBSzs7Ozs7O2tHQUNwQiw4REFBQzVRLDZUQUFJQTt3RkFBQzRRLFVBQVM7a0dBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFLMUIsOERBQUN0USw2VEFBSUE7O2tGQUNILDhEQUFDQyxtVUFBVUE7a0ZBQ1QsNEVBQUNSLGdVQUFPQTs0RUFBQzRELE1BQUs7c0ZBQUs7Ozs7Ozs7Ozs7O2tGQUVyQiw4REFBQ25ELGlVQUFRQTtrRkFDUCw0RUFBQ04sK1RBQU1BOzRFQUFDME4sU0FBUzs0RUFBR0UsT0FBTTs7OEZBQ3hCLDhEQUFDOU4sNlRBQUlBO29GQUFDNFEsVUFBUztvRkFBSzlMLE9BQU07OEZBQVc7Ozs7Ozs4RkFHckMsOERBQUNyRSwrVEFBTUE7b0ZBQ0xtTyx3QkFBVSw4REFBQ3ZMLDZOQUFLQTs7Ozs7b0ZBQ2hCd0wsYUFBWTtvRkFDWmtCLFNBQVE7b0ZBQ1JmLFVBQVU7OEZBQ1g7Ozs7Ozs4RkFHRCw4REFBQ3ZPLCtUQUFNQTtvRkFDTG1PLHdCQUFVLDhEQUFDdkwsNk5BQUtBOzs7OztvRkFDaEJ3TCxhQUFZO29GQUNaa0IsU0FBUTtvRkFDUmYsVUFBVTs4RkFDWDs7Ozs7OzhGQUdELDhEQUFDdk8sK1RBQU1BO29GQUNMbU8sd0JBQVUsOERBQUN2TCw2TkFBS0E7Ozs7O29GQUNoQndMLGFBQVk7b0ZBQ1prQixTQUFRO29GQUNSZixVQUFVOzhGQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQWNuQiw4REFBQzVNLDJDQUFRQTtvQkFBQzJPLHdCQUFVLDhEQUFDbFAsZ1VBQU9BOzs7Ozs4QkFDMUIsNEVBQUM0Qjt3QkFDQ3VFLFFBQVFDO3dCQUNSRyxTQUFTQzt3QkFDVDJJLFdBQVc5RTs7Ozs7Ozs7Ozs7OEJBSWYsOERBQUM5SiwyQ0FBUUE7b0JBQUMyTyx3QkFBVSw4REFBQ2xQLGdVQUFPQTs7Ozs7OEJBQzFCLDRFQUFDZ0M7d0JBQ0NtRSxRQUFRTTt3QkFDUkYsU0FBU0k7d0JBQ1R1RSxTQUFTM0Q7d0JBQ1Q2SCxZQUFZeEosU0FBU3lKLE1BQU0sQ0FBQzFELENBQUFBLElBQUtBLEVBQUVwRyxJQUFJLEtBQUs7d0JBQzVDNEosV0FBVzlFOzs7Ozs7Ozs7Ozs4QkFJZiw4REFBQzlKLDJDQUFRQTtvQkFBQzJPLHdCQUFVLDhEQUFDbFAsZ1VBQU9BOzs7Ozs4QkFDMUIsNEVBQUNtQzt3QkFDQ2dFLFFBQVFTO3dCQUNSTCxTQUFTTzt3QkFDVHFJLFdBQVcxRjs7Ozs7Ozs7Ozs7OEJBSWYsOERBQUNsSiwyQ0FBUUE7b0JBQUMyTyx3QkFBVSw4REFBQ2xQLGdVQUFPQTs7Ozs7OEJBQzFCLDRFQUFDaUM7d0JBQ0NrRSxRQUFRWTt3QkFDUlIsU0FBU1U7d0JBQ1QrRCxNQUFNM0Q7d0JBQ044SCxXQUFXMUY7Ozs7Ozs7Ozs7OzhCQUlmLDhEQUFDbEosMkNBQVFBO29CQUFDMk8sd0JBQVUsOERBQUNsUCxnVUFBT0E7Ozs7OzhCQUMxQiw0RUFBQ2tDO3dCQUNDaUUsUUFBUWU7d0JBQ1JYLFNBQVNhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXJCO0FBRU8sTUFBTWtJLHFCQUF5QyxPQUFPQztJQUMzRCxNQUFNQyxVQUFVLE1BQU1oUCxnRUFBZ0JBLENBQUMrTyxJQUFJRSxHQUFHLEVBQUVGLElBQUlHLEdBQUcsRUFBRWpQLDREQUFXQTtJQUVwRSxJQUFJLENBQUMrTyxTQUFTO1FBQ1osT0FBTztZQUNMRyxVQUFVO2dCQUNSQyxhQUFhO2dCQUNiQyxXQUFXO1lBQ2I7UUFDRjtJQUNGO0lBRUEsT0FBTztRQUFFQyxPQUFPO1lBQUVOO1FBQVE7SUFBRTtBQUM5QixFQUFFIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxzcmNcXGRhc2hib2FyZFxccGFnZXNcXGFkbWluXFxndWlsZHMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEB0cy1ub2NoZWNrXHJcbmltcG9ydCB7XHJcbiAgQm94LFxyXG4gIENvbnRhaW5lcixcclxuICBIZWFkaW5nLFxyXG4gIFRleHQsXHJcbiAgU2ltcGxlR3JpZCxcclxuICBWU3RhY2ssXHJcbiAgSFN0YWNrLFxyXG4gIEljb24sXHJcbiAgdXNlVG9hc3QsXHJcbiAgQ2FyZCxcclxuICBDYXJkSGVhZGVyLFxyXG4gIENhcmRCb2R5LFxyXG4gIEJ1dHRvbixcclxuICBJbnB1dCxcclxuICBTZWxlY3QsXHJcbiAgU3dpdGNoLFxyXG4gIEZvcm1Db250cm9sLFxyXG4gIEZvcm1MYWJlbCxcclxuICBCYWRnZSxcclxuICBEaXZpZGVyLFxyXG4gIFNrZWxldG9uLFxyXG4gIHVzZURpc2Nsb3N1cmUsXHJcbiAgTW9kYWwsXHJcbiAgTW9kYWxPdmVybGF5LFxyXG4gIE1vZGFsQ29udGVudCxcclxuICBNb2RhbEhlYWRlcixcclxuICBNb2RhbEJvZHksXHJcbiAgTW9kYWxGb290ZXIsXHJcbiAgTW9kYWxDbG9zZUJ1dHRvbixcclxuICBUYWJzLFxyXG4gIFRhYkxpc3QsXHJcbiAgVGFiUGFuZWxzLFxyXG4gIFRhYixcclxuICBUYWJQYW5lbCxcclxuICBUYWJsZSxcclxuICBUaGVhZCxcclxuICBUYm9keSxcclxuICBUcixcclxuICBUaCxcclxuICBUZCxcclxuICBJY29uQnV0dG9uLFxyXG4gIFRvb2x0aXAsXHJcbiAgQ2hlY2tib3gsXHJcbiAgRmxleCxcclxuICBSYWRpb0dyb3VwLFxyXG4gIFJhZGlvLFxyXG4gIFNwaW5uZXIsXHJcbn0gZnJvbSAnQGNoYWtyYS11aS9yZWFjdCc7XHJcbmltcG9ydCBMYXlvdXQgZnJvbSAnLi4vLi4vY29tcG9uZW50cy9MYXlvdXQnO1xyXG5pbXBvcnQgdXNlR3VpbGRJbmZvIGZyb20gJy4uLy4uL2hvb2tzL3VzZUd1aWxkSW5mbyc7XHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrLCB1c2VSZWYsIFN1c3BlbnNlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBHZXRTZXJ2ZXJTaWRlUHJvcHMgfSBmcm9tICduZXh0JztcclxuaW1wb3J0IHsgZ2V0U2VydmVyU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aC9uZXh0JztcclxuaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tICcuLi9hcGkvYXV0aC9bLi4ubmV4dGF1dGhdJztcclxuaW1wb3J0IHsgRmlTZXR0aW5ncywgRmlVc2VycywgRmlIYXNoLCBGaUVkaXQyLCBGaVNhdmUsIEZpVHJhc2gyLCBGaVBsdXMsIEZpTWVzc2FnZVNxdWFyZSwgRmlWb2x1bWUyLCBGaUZvbGRlclBsdXMsIEZpUmFkaW8sIEZpTG9jaywgRmlNZXNzYWdlQ2lyY2xlLCBGaVRyYXNoLCBGaVNlcnZlciwgRmlaYXAsIEZpVG9vbCB9IGZyb20gJ3JlYWN0LWljb25zL2ZpJztcclxuaW1wb3J0IHsgRmFQYWxldHRlIH0gZnJvbSAncmVhY3QtaWNvbnMvZmEnO1xyXG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnO1xyXG5cclxuLy8gRHluYW1pYyBpbXBvcnRzIGZvciBoZWF2eSBjb21wb25lbnRzXHJcbmNvbnN0IENyZWF0ZUNoYW5uZWxEaWFsb2cgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnLi4vLi4vY29tcG9uZW50cy9DcmVhdGVDaGFubmVsRGlhbG9nJyksIHtcclxuICBsb2FkaW5nOiAoKSA9PiA8U3Bpbm5lciBzaXplPVwibWRcIiAvPixcclxuICBzc3I6IGZhbHNlXHJcbn0pO1xyXG5cclxuY29uc3QgRWRpdENoYW5uZWxEaWFsb2cgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnLi4vLi4vY29tcG9uZW50cy9FZGl0Q2hhbm5lbERpYWxvZycpLCB7XHJcbiAgbG9hZGluZzogKCkgPT4gPFNwaW5uZXIgc2l6ZT1cIm1kXCIgLz4sXHJcbiAgc3NyOiBmYWxzZVxyXG59KTtcclxuXHJcbmNvbnN0IEVkaXRSb2xlRGlhbG9nID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJy4uLy4uL2NvbXBvbmVudHMvRWRpdFJvbGVEaWFsb2cnKSwge1xyXG4gIGxvYWRpbmc6ICgpID0+IDxTcGlubmVyIHNpemU9XCJtZFwiIC8+LFxyXG4gIHNzcjogZmFsc2VcclxufSk7XHJcblxyXG5jb25zdCBDb2xvckJ1aWxkZXIgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnLi4vLi4vY29tcG9uZW50cy9Db2xvckJ1aWxkZXInKSwge1xyXG4gIGxvYWRpbmc6ICgpID0+IDxTcGlubmVyIHNpemU9XCJtZFwiIC8+LFxyXG4gIHNzcjogZmFsc2VcclxufSk7XHJcblxyXG5jb25zdCBDcmVhdGVSb2xlRGlhbG9nID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJy4uLy4uL2NvbXBvbmVudHMvQ3JlYXRlUm9sZURpYWxvZycpLCB7XHJcbiAgbG9hZGluZzogKCkgPT4gPFNwaW5uZXIgc2l6ZT1cIm1kXCIgLz4sXHJcbiAgc3NyOiBmYWxzZVxyXG59KTtcclxuXHJcbi8vIFJhdGUgbGltaXRpbmcgY29uc3RhbnRzXHJcbmNvbnN0IFJBVEVfTElNSVRfTVMgPSAyMDAwOyAvLyAyIHNlY29uZHMgYmV0d2VlbiBvcGVyYXRpb25zXHJcbmNvbnN0IEJVTEtfUkFURV9MSU1JVF9NUyA9IDUwMDA7IC8vIDUgc2Vjb25kcyBiZXR3ZWVuIGJ1bGsgb3BlcmF0aW9uc1xyXG5cclxuLy8gQ3VzdG9tIGhvb2sgZm9yIHJhdGUgbGltaXRpbmdcclxuZnVuY3Rpb24gdXNlUmF0ZUxpbWl0KGRlbGF5ID0gUkFURV9MSU1JVF9NUykge1xyXG4gIGNvbnN0IFtpc1JhdGVMaW1pdGVkLCBzZXRJc1JhdGVMaW1pdGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCB0aW1lb3V0UmVmID0gdXNlUmVmKG51bGwpO1xyXG5cclxuICBjb25zdCByZXNldFJhdGVMaW1pdCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIGlmICh0aW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICB9XHJcbiAgICBzZXRJc1JhdGVMaW1pdGVkKHRydWUpO1xyXG4gICAgdGltZW91dFJlZi5jdXJyZW50ID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIHNldElzUmF0ZUxpbWl0ZWQoZmFsc2UpO1xyXG4gICAgfSwgZGVsYXkpO1xyXG4gIH0sIFtkZWxheV0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgaWYgKHRpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgcmV0dXJuIFtpc1JhdGVMaW1pdGVkLCByZXNldFJhdGVMaW1pdF07XHJcbn1cclxuXHJcbmludGVyZmFjZSBSb2xlIHtcclxuICBpZDogc3RyaW5nO1xyXG4gIG5hbWU6IHN0cmluZztcclxuICBjb2xvcjogbnVtYmVyO1xyXG4gIHBvc2l0aW9uOiBudW1iZXI7XHJcbiAgcGVybWlzc2lvbnM6IHN0cmluZztcclxufVxyXG5cclxuaW50ZXJmYWNlIENoYW5uZWwge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIHR5cGU6IG51bWJlcjtcclxuICBwYXJlbnRfaWQ/OiBzdHJpbmc7XHJcbiAgcG9zaXRpb246IG51bWJlcjtcclxuICB0b3BpYz86IHN0cmluZztcclxuICBuc2Z3PzogYm9vbGVhbjtcclxuICByYXRlX2xpbWl0X3Blcl91c2VyPzogbnVtYmVyO1xyXG4gIGJpdHJhdGU/OiBudW1iZXI7XHJcbiAgdXNlcl9saW1pdD86IG51bWJlcjtcclxufVxyXG5cclxuaW50ZXJmYWNlIEd1aWxkU2V0dGluZ3Mge1xyXG4gIHByZWZpeDogc3RyaW5nO1xyXG4gIGJvdE5hbWU6IHN0cmluZztcclxuICBndWlsZE5hbWU6IHN0cmluZztcclxuICBndWlsZEljb246IHN0cmluZyB8IG51bGw7XHJcbiAgYWN0aXZpdGllczogeyB0eXBlOiAnUExBWUlORycgfCAnU1RSRUFNSU5HJyB8ICdMSVNURU5JTkcnIHwgJ1dBVENISU5HJyB8ICdDT01QRVRJTkcnOyBuYW1lOiBzdHJpbmc7IH1bXTtcclxuICBhY3Rpdml0eVJvdGF0aW9uSW50ZXJ2YWw6IG51bWJlcjsgLy8gaW4gc2Vjb25kc1xyXG59XHJcblxyXG5jb25zdCBDSEFOTkVMX1RZUEVfQ09ORklHID0ge1xyXG4gIDA6ICB7IGljb246IEZpTWVzc2FnZVNxdWFyZSwgY29sb3I6ICdibHVlJywgICBsYWJlbDogJ1RleHQnIH0sXHJcbiAgMjogIHsgaWNvbjogRmlWb2x1bWUyLCAgICAgIGNvbG9yOiAnZ3JlZW4nLCAgbGFiZWw6ICdWb2ljZScgfSxcclxuICA0OiAgeyBpY29uOiBGaUZvbGRlclBsdXMsICAgY29sb3I6ICdwdXJwbGUnLCBsYWJlbDogJ0NhdGVnb3J5JyB9LFxyXG4gIDU6ICB7IGljb246IEZpUmFkaW8sICAgICAgICBjb2xvcjogJ29yYW5nZScsIGxhYmVsOiAnQW5ub3VuY2VtZW50JyB9LFxyXG4gIDExOiB7IGljb246IEZpTWVzc2FnZUNpcmNsZSxjb2xvcjogJ2N5YW4nLCAgIGxhYmVsOiAnUHVibGljIFRocmVhZCcgfSxcclxuICAxMjogeyBpY29uOiBGaUxvY2ssICAgICAgICAgY29sb3I6ICdwaW5rJywgICBsYWJlbDogJ1ByaXZhdGUgVGhyZWFkJyB9LFxyXG4gIDEzOiB7IGljb246IEZpSGFzaCwgICAgICAgICBjb2xvcjogJ3RlYWwnLCAgIGxhYmVsOiAnU3RhZ2UgVm9pY2UnfSxcclxuICAxNTogeyBpY29uOiBGaUhhc2gsICAgICAgICAgY29sb3I6ICdncmF5JywgICBsYWJlbDogJ0ZvcnVtJ31cclxufTtcclxuXHJcbmNvbnN0IFBFUk1JU1NJT05fQkFER0VTID0ge1xyXG4gIEFETUlOSVNUUkFUT1I6IHsgY29sb3I6ICdyZWQnLCBsYWJlbDogJ0FkbWluJyB9LFxyXG4gIE1BTkFHRV9HVUlMRDogeyBjb2xvcjogJ29yYW5nZScsIGxhYmVsOiAnTWFuYWdlIFNlcnZlcicgfSxcclxuICBNQU5BR0VfUk9MRVM6IHsgY29sb3I6ICd5ZWxsb3cnLCBsYWJlbDogJ01hbmFnZSBSb2xlcycgfSxcclxuICBNQU5BR0VfQ0hBTk5FTFM6IHsgY29sb3I6ICdncmVlbicsIGxhYmVsOiAnTWFuYWdlIENoYW5uZWxzJyB9LFxyXG4gIEtJQ0tfTUVNQkVSUzogeyBjb2xvcjogJ3B1cnBsZScsIGxhYmVsOiAnS2ljaycgfSxcclxuICBCQU5fTUVNQkVSUzogeyBjb2xvcjogJ3BpbmsnLCBsYWJlbDogJ0JhbicgfSxcclxuICBNQU5BR0VfTUVTU0FHRVM6IHsgY29sb3I6ICdibHVlJywgbGFiZWw6ICdNYW5hZ2UgTWVzc2FnZXMnIH0sXHJcbiAgTUVOVElPTl9FVkVSWU9ORTogeyBjb2xvcjogJ2N5YW4nLCBsYWJlbDogJ01lbnRpb24gQGV2ZXJ5b25lJyB9LFxyXG59O1xyXG5cclxuLy8gQWRkIHRoaXMgaGVscGVyIG1hcCBhbmQgZnVuY3Rpb24gYWZ0ZXIgUEVSTUlTU0lPTl9CQURHRVMgY29uc3RhbnRcclxuY29uc3QgUEVSTUlTU0lPTl9GTEFHX0JJVFM6IFJlY29yZDxrZXlvZiB0eXBlb2YgUEVSTUlTU0lPTl9CQURHRVMsIGJpZ2ludD4gPSB7XHJcbiAgQURNSU5JU1RSQVRPUjogMW4gPDwgM24sXHJcbiAgTUFOQUdFX0dVSUxEOiAxbiA8PCA1bixcclxuICBNQU5BR0VfUk9MRVM6IDFuIDw8IDI4bixcclxuICBNQU5BR0VfQ0hBTk5FTFM6IDFuIDw8IDRuLFxyXG4gIEtJQ0tfTUVNQkVSUzogMW4gPDwgMW4sXHJcbiAgQkFOX01FTUJFUlM6IDFuIDw8IDJuLFxyXG4gIE1BTkFHRV9NRVNTQUdFUzogMW4gPDwgMTNuLFxyXG4gIE1FTlRJT05fRVZFUllPTkU6IDFuIDw8IDE3bixcclxufTtcclxuXHJcbmZ1bmN0aW9uIGRlY29kZVBlcm1pc3Npb25zKGJpdGZpZWxkOiBzdHJpbmcgfCBudW1iZXIpOiAoa2V5b2YgdHlwZW9mIFBFUk1JU1NJT05fQkFER0VTKVtdIHtcclxuICBpZiAoIWJpdGZpZWxkKSByZXR1cm4gW107XHJcbiAgaWYgKEFycmF5LmlzQXJyYXkoYml0ZmllbGQpKSByZXR1cm4gYml0ZmllbGQgYXMgYW55O1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBwZXJtaXNzaW9uczogKGtleW9mIHR5cGVvZiBQRVJNSVNTSU9OX0JBREdFUylbXSA9IFtdO1xyXG4gICAgY29uc3QgYml0cyA9IEJpZ0ludChiaXRmaWVsZCk7XHJcbiAgICBcclxuICAgIGZvciAoY29uc3QgW3Blcm1pc3Npb24sIGJpdF0gb2YgT2JqZWN0LmVudHJpZXMoUEVSTUlTU0lPTl9GTEFHX0JJVFMpKSB7XHJcbiAgICAgIGlmICgoYml0cyAmIGJpdCkgPT09IGJpdCkge1xyXG4gICAgICAgIHBlcm1pc3Npb25zLnB1c2gocGVybWlzc2lvbiBhcyBrZXlvZiB0eXBlb2YgUEVSTUlTU0lPTl9CQURHRVMpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBwZXJtaXNzaW9ucztcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVjb2RpbmcgcGVybWlzc2lvbnM6JywgZXJyb3IpO1xyXG4gICAgcmV0dXJuIFtdO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2VydmVyTWFuYWdlbWVudCgpIHtcclxuICBjb25zdCB0b2FzdCA9IHVzZVRvYXN0KCk7XHJcbiAgY29uc3QgeyBkaXNwbGF5TmFtZSB9ID0gdXNlR3VpbGRJbmZvKCk7XHJcbiAgY29uc3QgW2lzUmF0ZUxpbWl0ZWQsIHJlc2V0UmF0ZUxpbWl0XSA9IHVzZVJhdGVMaW1pdCgpO1xyXG4gIGNvbnN0IFtpc0J1bGtSYXRlTGltaXRlZCwgcmVzZXRCdWxrUmF0ZUxpbWl0XSA9IHVzZVJhdGVMaW1pdChCVUxLX1JBVEVfTElNSVRfTVMpO1xyXG4gIFxyXG4gIC8vIFN0YXRlIGZvciBndWlsZCBzZXR0aW5nc1xyXG4gIGNvbnN0IFtndWlsZERhdGEsIHNldEd1aWxkRGF0YV0gPSB1c2VTdGF0ZTxHdWlsZFNldHRpbmdzPih7XHJcbiAgICBwcmVmaXg6ICchJyxcclxuICAgIGJvdE5hbWU6ICdCb3QnLFxyXG4gICAgZ3VpbGROYW1lOiAnJyxcclxuICAgIGd1aWxkSWNvbjogbnVsbCxcclxuICAgIGFjdGl2aXRpZXM6IFt7IHR5cGU6ICdQTEFZSU5HJywgbmFtZTogJ3dpdGggRGlzY29yZC5qcycgfV0sXHJcbiAgICBhY3Rpdml0eVJvdGF0aW9uSW50ZXJ2YWw6IDYwXHJcbiAgfSk7XHJcbiAgXHJcbiAgLy8gU3RhdGUgZm9yIHJvbGVzIGFuZCBjaGFubmVsc1xyXG4gIGNvbnN0IFtyb2xlcywgc2V0Um9sZXNdID0gdXNlU3RhdGU8Um9sZVtdPihbXSk7XHJcbiAgY29uc3QgW2NoYW5uZWxzLCBzZXRDaGFubmVsc10gPSB1c2VTdGF0ZTxDaGFubmVsW10+KFtdKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbY2hhbm5lbHNMb2FkaW5nLCBzZXRDaGFubmVsc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW3NhdmluZywgc2V0U2F2aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBcclxuICAvLyBNb2RhbCBzdGF0ZXNcclxuICBjb25zdCB7IGlzT3BlbjogaXNDcmVhdGVDaGFubmVsT3Blbiwgb25PcGVuOiBvbkNyZWF0ZUNoYW5uZWxPcGVuLCBvbkNsb3NlOiBvbkNyZWF0ZUNoYW5uZWxDbG9zZSB9ID0gdXNlRGlzY2xvc3VyZSgpO1xyXG4gIGNvbnN0IHsgaXNPcGVuOiBpc0VkaXRDaGFubmVsT3Blbiwgb25PcGVuOiBvbkVkaXRDaGFubmVsT3Blbiwgb25DbG9zZTogb25FZGl0Q2hhbm5lbENsb3NlIH0gPSB1c2VEaXNjbG9zdXJlKCk7XHJcbiAgY29uc3QgeyBpc09wZW46IGlzQ3JlYXRlUm9sZU9wZW4sIG9uT3Blbjogb25DcmVhdGVSb2xlT3Blbiwgb25DbG9zZTogb25DcmVhdGVSb2xlQ2xvc2UgfSA9IHVzZURpc2Nsb3N1cmUoKTtcclxuICBjb25zdCB7IGlzT3BlbjogaXNFZGl0Um9sZU9wZW4sIG9uT3Blbjogb25FZGl0Um9sZU9wZW4sIG9uQ2xvc2U6IG9uRWRpdFJvbGVDbG9zZSB9ID0gdXNlRGlzY2xvc3VyZSgpO1xyXG4gIGNvbnN0IHsgaXNPcGVuOiBpc0NvbG9yQnVpbGRlck9wZW4sIG9uT3Blbjogb25Db2xvckJ1aWxkZXJPcGVuLCBvbkNsb3NlOiBvbkNvbG9yQnVpbGRlckNsb3NlIH0gPSB1c2VEaXNjbG9zdXJlKCk7XHJcbiAgXHJcbiAgLy8gU2VsZWN0ZWQgaXRlbXMgZm9yIGVkaXRpbmdcclxuICBjb25zdCBbc2VsZWN0ZWRSb2xlLCBzZXRTZWxlY3RlZFJvbGVdID0gdXNlU3RhdGU8Um9sZSB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZENoYW5uZWwsIHNldFNlbGVjdGVkQ2hhbm5lbF0gPSB1c2VTdGF0ZTxDaGFubmVsIHwgbnVsbD4obnVsbCk7XHJcbiAgXHJcbiAgLy8gRmlsZSB1cGxvYWQgc3RhdGVcclxuICBjb25zdCBbaWNvbkZpbGUsIHNldEljb25GaWxlXSA9IHVzZVN0YXRlPEZpbGUgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbaWNvblByZXZpZXcsIHNldEljb25QcmV2aWV3XSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IGZpbGVJbnB1dFJlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50PihudWxsKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlSWNvbkZpbGVDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcclxuICAgIGNvbnN0IGZpbGUgPSBlLnRhcmdldC5maWxlcz8uWzBdO1xyXG4gICAgaWYgKGZpbGUpIHtcclxuICAgICAgc2V0SWNvbkZpbGUoZmlsZSk7XHJcbiAgICAgIGNvbnN0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7XHJcbiAgICAgIHJlYWRlci5vbmxvYWQgPSAoZSkgPT4gc2V0SWNvblByZXZpZXcoZS50YXJnZXQ/LnJlc3VsdCBhcyBzdHJpbmcpO1xyXG4gICAgICByZWFkZXIucmVhZEFzRGF0YVVSTChmaWxlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCB1cGxvYWRJY29uID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKCFpY29uRmlsZSkgcmV0dXJuO1xyXG4gICAgXHJcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xyXG4gICAgZm9ybURhdGEuYXBwZW5kKCdpY29uJywgaWNvbkZpbGUpO1xyXG4gICAgXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2Rpc2NvcmQvc2V0dGluZ3MnLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgYm9keTogZm9ybURhdGFcclxuICAgICAgfSk7XHJcbiAgICAgIFxyXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIHNldEd1aWxkRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGd1aWxkSWNvbjogZGF0YS5pY29uVXJsIH0pKTtcclxuICAgICAgICBzZXRJY29uRmlsZShudWxsKTtcclxuICAgICAgICBzZXRJY29uUHJldmlldyhudWxsKTtcclxuICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICB0aXRsZTogJ1N1Y2Nlc3MnLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdHdWlsZCBpY29uIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAgICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnLFxyXG4gICAgICAgICAgZHVyYXRpb246IDMwMDBcclxuICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiAnRXJyb3InLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnRmFpbGVkIHRvIHVwbG9hZCBpY29uJyxcclxuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXHJcbiAgICAgICAgZHVyYXRpb246IDMwMDBcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZmV0Y2hHdWlsZERhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBbZ3VpbGRSZXNwb25zZSwgcm9sZXNSZXNwb25zZV0gPSBhd2FpdCBQcm9taXNlLmFsbChbXHJcbiAgICAgICAgZmV0Y2goJy9hcGkvZGlzY29yZC9ndWlsZCcpLFxyXG4gICAgICAgIGZldGNoKCcvYXBpL2Rpc2NvcmQvcm9sZXMnKVxyXG4gICAgICBdKTtcclxuICAgICAgXHJcbiAgICAgIGlmIChndWlsZFJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgY29uc3QgZ3VpbGQgPSBhd2FpdCBndWlsZFJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICBzZXRHdWlsZERhdGEocHJldiA9PiAoe1xyXG4gICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgIGd1aWxkTmFtZTogZ3VpbGQubmFtZSxcclxuICAgICAgICAgIGd1aWxkSWNvbjogZ3VpbGQuaWNvblxyXG4gICAgICAgIH0pKTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgaWYgKHJvbGVzUmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zdCByb2xlc0RhdGEgPSBhd2FpdCByb2xlc1Jlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICBjb25zdCBhcnIgPSBBcnJheS5pc0FycmF5KHJvbGVzRGF0YSkgPyByb2xlc0RhdGEgOiByb2xlc0RhdGEucm9sZXMgfHwgW107XHJcbiAgICAgICAgc2V0Um9sZXMoYXJyLnNvcnQoKGE6IFJvbGUsIGI6IFJvbGUpID0+IGIucG9zaXRpb24gLSBhLnBvc2l0aW9uKSk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICB0aXRsZTogJ0Vycm9yJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJ0ZhaWxlZCB0byBmZXRjaCBndWlsZCBkYXRhJyxcclxuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXHJcbiAgICAgICAgZHVyYXRpb246IDMwMDBcclxuICAgICAgfSk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBmZXRjaENoYW5uZWxzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9kaXNjb3JkL2NoYW5uZWxzJyk7XHJcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBjaGFubmVscycpO1xyXG4gICAgICB9XHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIGNvbnN0IHNvcnRlZENoYW5uZWxzID0gKGRhdGEgfHwgW10pLnNvcnQoKGE6IENoYW5uZWwsIGI6IENoYW5uZWwpID0+IHtcclxuICAgICAgICBpZiAoYS50eXBlID09PSA0ICYmIGIudHlwZSAhPT0gNCkgcmV0dXJuIC0xO1xyXG4gICAgICAgIGlmIChhLnR5cGUgIT09IDQgJiYgYi50eXBlID09PSA0KSByZXR1cm4gMTtcclxuICAgICAgICByZXR1cm4gYS5wb3NpdGlvbiAtIGIucG9zaXRpb247XHJcbiAgICAgIH0pO1xyXG4gICAgICBzZXRDaGFubmVscyhzb3J0ZWRDaGFubmVscyk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdGYWlsZWQgdG8gZmV0Y2ggY2hhbm5lbHMnLFxyXG4gICAgICAgIHN0YXR1czogJ2Vycm9yJyxcclxuICAgICAgICBkdXJhdGlvbjogNTAwMCxcclxuICAgICAgfSk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRDaGFubmVsc0xvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBmZXRjaEd1aWxkRGF0YSgpO1xyXG4gICAgZmV0Y2hDaGFubmVscygpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2V0dGluZ0NoYW5nZSA9IChzZXR0aW5nOiBrZXlvZiBHdWlsZFNldHRpbmdzLCB2YWx1ZTogYW55KSA9PiB7XHJcbiAgICBzZXRHdWlsZERhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBbc2V0dGluZ106IHZhbHVlIH0pKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBzYXZlU2V0dGluZ3MgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoc2F2aW5nIHx8IGlzUmF0ZUxpbWl0ZWQpIHJldHVybjtcclxuICAgIFxyXG4gICAgc2V0U2F2aW5nKHRydWUpO1xyXG4gICAgcmVzZXRSYXRlTGltaXQoKTtcclxuICAgIFxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9kaXNjb3JkL3NldHRpbmdzJywge1xyXG4gICAgICAgIG1ldGhvZDogJ1BVVCcsXHJcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZ3VpbGREYXRhKVxyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgIHRpdGxlOiAnU3VjY2VzcycsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ1NldHRpbmdzIHNhdmVkIHN1Y2Nlc3NmdWxseScsXHJcbiAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcclxuICAgICAgICAgIGR1cmF0aW9uOiAzMDAwXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gc2F2ZSBzZXR0aW5ncycpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdGYWlsZWQgdG8gc2F2ZSBzZXR0aW5ncycsXHJcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxyXG4gICAgICAgIGR1cmF0aW9uOiAzMDAwXHJcbiAgICAgIH0pO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0U2F2aW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVSb2xlRWRpdCA9IChyb2xlOiBSb2xlKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZFJvbGUocm9sZSk7XHJcbiAgICBvbkVkaXRSb2xlT3BlbigpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNoYW5uZWxFZGl0ID0gKGNoYW5uZWw6IENoYW5uZWwpID0+IHtcclxuICAgIHNldFNlbGVjdGVkQ2hhbm5lbChjaGFubmVsKTtcclxuICAgIG9uRWRpdENoYW5uZWxPcGVuKCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ2hhbm5lbENyZWF0ZSA9ICgpID0+IHtcclxuICAgIG9uQ3JlYXRlQ2hhbm5lbE9wZW4oKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVSb2xlQ3JlYXRlID0gKCkgPT4ge1xyXG4gICAgb25DcmVhdGVSb2xlT3BlbigpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNoYW5uZWxEZWxldGUgPSBhc3luYyAoY2hhbm5lbElkOiBzdHJpbmcpID0+IHtcclxuICAgIGlmIChpc1JhdGVMaW1pdGVkKSByZXR1cm47XHJcbiAgICBcclxuICAgIHRyeSB7XHJcbiAgICAgIHJlc2V0UmF0ZUxpbWl0KCk7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvZGlzY29yZC9jaGFubmVscy8ke2NoYW5uZWxJZH1gLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJ1xyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIGF3YWl0IGZldGNoQ2hhbm5lbHMoKTtcclxuICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICB0aXRsZTogJ1N1Y2Nlc3MnLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdDaGFubmVsIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAgICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnLFxyXG4gICAgICAgICAgZHVyYXRpb246IDMwMDBcclxuICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiAnRXJyb3InLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnRmFpbGVkIHRvIGRlbGV0ZSBjaGFubmVsJyxcclxuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXHJcbiAgICAgICAgZHVyYXRpb246IDMwMDBcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0UGFyZW50TmFtZSA9IChwYXJlbnRJZDogc3RyaW5nIHwgdW5kZWZpbmVkKSA9PiB7XHJcbiAgICBpZiAoIXBhcmVudElkIHx8ICFjaGFubmVscykgcmV0dXJuICctJztcclxuICAgIGNvbnN0IHBhcmVudCA9IGNoYW5uZWxzLmZpbmQoYyA9PiBjLmlkID09PSBwYXJlbnRJZCk7XHJcbiAgICByZXR1cm4gcGFyZW50ID8gcGFyZW50Lm5hbWUgOiAnLSc7XHJcbiAgfTtcclxuXHJcbiAgaWYgKGxvYWRpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxMYXlvdXQ+XHJcbiAgICAgICAgPENvbnRhaW5lciBtYXhXPVwiY29udGFpbmVyLnhsXCIgcHk9ezh9PlxyXG4gICAgICAgICAgPFZTdGFjayBzcGFjaW5nPXs2fT5cclxuICAgICAgICAgICAgPFNrZWxldG9uIGhlaWdodD1cIjYwcHhcIiAvPlxyXG4gICAgICAgICAgICA8U2tlbGV0b24gaGVpZ2h0PVwiNDAwcHhcIiAvPlxyXG4gICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgPC9Db250YWluZXI+XHJcbiAgICAgIDwvTGF5b3V0PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8TGF5b3V0PlxyXG4gICAgICA8Q29udGFpbmVyIG1heFc9XCJjb250YWluZXIueGxcIiBweT17OH0+XHJcbiAgICAgICAgPFZTdGFjayBzcGFjaW5nPXs4fSBhbGlnbj1cInN0cmV0Y2hcIj5cclxuICAgICAgICAgIHsvKiBIZWFkZXIgKi99XHJcbiAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgIGJnPVwicmdiYSgyNTUsMjU1LDI1NSwwLjA4KVwiXHJcbiAgICAgICAgICAgIHA9ezh9XHJcbiAgICAgICAgICAgIHJvdW5kZWQ9XCIyeGxcIlxyXG4gICAgICAgICAgICBiYWNrZHJvcEZpbHRlcj1cImJsdXIoMTBweClcIlxyXG4gICAgICAgICAgICBib3JkZXI9XCIycHggc29saWRcIlxyXG4gICAgICAgICAgICBib3JkZXJDb2xvcj1cImJsdWUuNDAwXCJcclxuICAgICAgICAgICAgYm94U2hhZG93PVwiMCAwIDE1cHggcmdiYSg2NiwgMTUzLCAyMjUsIDAuNClcIlxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8SFN0YWNrIGp1c3RpZnk9XCJzcGFjZS1iZXR3ZWVuXCIgYWxpZ249XCJjZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8Qm94PlxyXG4gICAgICAgICAgICAgICAgPEhlYWRpbmdcclxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInhsXCJcclxuICAgICAgICAgICAgICAgICAgYmdHcmFkaWVudD1cImxpbmVhcih0by1yLCBibHVlLjMwMCwgcHVycGxlLjQwMClcIlxyXG4gICAgICAgICAgICAgICAgICBiZ0NsaXA9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPEljb24gYXM9e0ZpU2VydmVyfSBtcj17M30gLz5cclxuICAgICAgICAgICAgICAgICAgU2VydmVyIE1hbmFnZW1lbnRcclxuICAgICAgICAgICAgICAgIDwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwiZ3JheS4zMDBcIiBtdD17Mn0+XHJcbiAgICAgICAgICAgICAgICAgIENvbXByZWhlbnNpdmUgbWFuYWdlbWVudCBmb3Ige2Rpc3BsYXlOYW1lIHx8IGd1aWxkRGF0YS5ndWlsZE5hbWV9XHJcbiAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgbGVmdEljb249ezxGaVNhdmUgLz59XHJcbiAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cImJsdWVcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17c2F2ZVNldHRpbmdzfVxyXG4gICAgICAgICAgICAgICAgaXNMb2FkaW5nPXtzYXZpbmd9XHJcbiAgICAgICAgICAgICAgICBpc0Rpc2FibGVkPXtpc1JhdGVMaW1pdGVkfVxyXG4gICAgICAgICAgICAgICAgc2l6ZT1cImxnXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBTYXZlIFNldHRpbmdzXHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvSFN0YWNrPlxyXG4gICAgICAgICAgPC9Cb3g+XHJcblxyXG4gICAgICAgICAgey8qIE1haW4gQ29udGVudCBUYWJzICovfVxyXG4gICAgICAgICAgPFRhYnMgY29sb3JTY2hlbWU9XCJibHVlXCIgaXNMYXp5PlxyXG4gICAgICAgICAgICA8VGFiTGlzdD5cclxuICAgICAgICAgICAgICA8VGFiPlxyXG4gICAgICAgICAgICAgICAgPEljb24gYXM9e0ZpU2V0dGluZ3N9IG1yPXsyfSAvPlxyXG4gICAgICAgICAgICAgICAgR2VuZXJhbCBTZXR0aW5nc1xyXG4gICAgICAgICAgICAgIDwvVGFiPlxyXG4gICAgICAgICAgICAgIDxUYWI+XHJcbiAgICAgICAgICAgICAgICA8SWNvbiBhcz17RmFQYWxldHRlfSBtcj17Mn0gLz5cclxuICAgICAgICAgICAgICAgIFRoZW1lIEJ1aWxkZXJcclxuICAgICAgICAgICAgICA8L1RhYj5cclxuICAgICAgICAgICAgICA8VGFiPlxyXG4gICAgICAgICAgICAgICAgPEljb24gYXM9e0ZpVG9vbH0gbXI9ezJ9IC8+XHJcbiAgICAgICAgICAgICAgICBCdWlsZGVyc1xyXG4gICAgICAgICAgICAgIDwvVGFiPlxyXG4gICAgICAgICAgICAgIDxUYWI+XHJcbiAgICAgICAgICAgICAgICA8SWNvbiBhcz17RmlaYXB9IG1yPXsyfSAvPlxyXG4gICAgICAgICAgICAgICAgQXV0b21hdGlvblxyXG4gICAgICAgICAgICAgIDwvVGFiPlxyXG4gICAgICAgICAgICA8L1RhYkxpc3Q+XHJcblxyXG4gICAgICAgICAgICA8VGFiUGFuZWxzPlxyXG4gICAgICAgICAgICAgIHsvKiBHZW5lcmFsIFNldHRpbmdzIFRhYiAqL31cclxuICAgICAgICAgICAgICA8VGFiUGFuZWw+XHJcbiAgICAgICAgICAgICAgICA8VlN0YWNrIHNwYWNpbmc9ezh9IGFsaWduPVwic3RyZXRjaFwiPlxyXG4gICAgICAgICAgICAgICAgICB7LyogQmFzaWMgU2V0dGluZ3MgKi99XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEhlYWRpbmcgc2l6ZT1cIm1kXCI+QmFzaWMgU2V0dGluZ3M8L0hlYWRpbmc+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxTaW1wbGVHcmlkIGNvbHVtbnM9e3sgYmFzZTogMSwgbGc6IDIgfX0gc3BhY2luZz17Nn0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17NH0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1Db250cm9sPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1MYWJlbD5Cb3QgTmFtZTwvRm9ybUxhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtndWlsZERhdGEuYm90TmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVTZXR0aW5nQ2hhbmdlKCdib3ROYW1lJywgZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGJvdCBuYW1lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Gb3JtQ29udHJvbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUNvbnRyb2w+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUxhYmVsPkNvbW1hbmQgUHJlZml4PC9Gb3JtTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2d1aWxkRGF0YS5wcmVmaXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2V0dGluZ0NoYW5nZSgncHJlZml4JywgZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGNvbW1hbmQgcHJlZml4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Gb3JtQ29udHJvbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17NH0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1Db250cm9sPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1MYWJlbD5TZXJ2ZXIgTmFtZTwvRm9ybUxhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtndWlsZERhdGE/Lm5hbWUgfHwgJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUmVhZE9ubHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmc9XCJncmF5LjUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2Rhcms9e3sgYmc6ICdncmF5LjcwMCcgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Gb3JtQ29udHJvbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUNvbnRyb2w+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUxhYmVsPk1lbWJlciBDb3VudDwvRm9ybUxhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtndWlsZERhdGE/Lm1lbWJlckNvdW50IHx8ICcwJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNSZWFkT25seVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiZz1cImdyYXkuNTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfZGFyaz17eyBiZzogJ2dyYXkuNzAwJyB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0Zvcm1Db250cm9sPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1ZTdGFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvU2ltcGxlR3JpZD5cclxuICAgICAgICAgICAgICAgICAgICA8L0NhcmRCb2R5PlxyXG4gICAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICAgICAgICB7LyogUm9sZXMgU2VjdGlvbiAqL31cclxuICAgICAgICAgICAgICAgICAgPENhcmQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8SFN0YWNrIGp1c3RpZnk9XCJzcGFjZS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxIZWFkaW5nIHNpemU9XCJtZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uIGFzPXtGaVVzZXJzfSBtcj17Mn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBSb2xlcyAoe3JvbGVzLmxlbmd0aH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmlQbHVzIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwiZ3JlZW5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVJvbGVDcmVhdGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaXNEaXNhYmxlZD17aXNSYXRlTGltaXRlZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIENyZWF0ZSBSb2xlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9IU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgIHtsb2FkaW5nID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VlN0YWNrIHNwYWNpbmc9ezN9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtbLi4uQXJyYXkoMyldLm1hcCgoXywgaSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNrZWxldG9uIGtleT17aX0gaGVpZ2h0PVwiNjBweFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVlN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJveCBvdmVyZmxvd1g9XCJhdXRvXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlIHZhcmlhbnQ9XCJzaW1wbGVcIiBzaXplPVwic21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUaGVhZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUaD5Sb2xlPC9UaD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGg+TWVtYmVyczwvVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRoPlBlcm1pc3Npb25zPC9UaD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGg+QWN0aW9uczwvVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RoZWFkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRib2R5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7KHJvbGVzIHx8IFtdKS5tYXAoKHJvbGUpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHIga2V5PXtyb2xlLmlkfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEhTdGFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3PXs0fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaD17NH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvdW5kZWQ9XCJmdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJnPXtyb2xlLmNvbG9yID8gYCMke3JvbGUuY29sb3IudG9TdHJpbmcoMTYpLnBhZFN0YXJ0KDYsICcwJyl9YCA6ICdncmF5LjUwMCd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dD57cm9sZS5uYW1lfTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9IU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY29sb3JTY2hlbWU9XCJibHVlXCI+MDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SFN0YWNrIHdyYXA9XCJ3cmFwXCIgc3BhY2luZz17MX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyhkZWNvZGVQZXJtaXNzaW9ucyhyb2xlLnBlcm1pc3Npb25zKSB8fCBbXSkuc2xpY2UoMCwgMykubWFwKChwZXJtKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtwZXJtfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT17UEVSTUlTU0lPTl9CQURHRVNbcGVybV0/LmNvbG9yIHx8ICdncmF5J31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1BFUk1JU1NJT05fQkFER0VTW3Blcm1dPy5sYWJlbCB8fCBwZXJtfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZGVjb2RlUGVybWlzc2lvbnMocm9sZS5wZXJtaXNzaW9ucykubGVuZ3RoID4gMyAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY29sb3JTY2hlbWU9XCJncmF5XCIgc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICt7ZGVjb2RlUGVybWlzc2lvbnMocm9sZS5wZXJtaXNzaW9ucykubGVuZ3RoIC0gM31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9IU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SFN0YWNrIHNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGxhYmVsPVwiRWRpdCBSb2xlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiRWRpdCByb2xlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbj17PEZpRWRpdDIgLz59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwiYmx1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVJvbGVFZGl0KHJvbGUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkPXtpc1JhdGVMaW1pdGVkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgbGFiZWw9XCJEZWxldGUgUm9sZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25CdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkRlbGV0ZSByb2xlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbj17PEZpVHJhc2gyIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cInJlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzRGlzYWJsZWQ9e2lzUmF0ZUxpbWl0ZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9IU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIENoYW5uZWxzIFNlY3Rpb24gKi99XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEhTdGFjayBqdXN0aWZ5PVwic3BhY2UtYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8SGVhZGluZyBzaXplPVwibWRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbiBhcz17RmlIYXNofSBtcj17Mn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBDaGFubmVscyAoe2NoYW5uZWxzLmxlbmd0aH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmlQbHVzIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwiYmx1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17b25DcmVhdGVDaGFubmVsT3Blbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIENyZWF0ZSBDaGFubmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9IU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgIHtjaGFubmVsc0xvYWRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17M30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge1suLi5BcnJheSg1KV0ubWFwKChfLCBpKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2tlbGV0b24ga2V5PXtpfSBoZWlnaHQ9XCI1MHB4XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICApIDogY2hhbm5lbHMubGVuZ3RoID09PSAwID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cImdyYXkuNTAwXCIgdGV4dEFsaWduPVwiY2VudGVyXCIgcHk9ezh9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIE5vIGNoYW5uZWxzIGZvdW5kXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxCb3ggb3ZlcmZsb3dYPVwiYXV0b1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZSB2YXJpYW50PVwic2ltcGxlXCIgc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGhlYWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGg+TmFtZTwvVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRoPlR5cGU8L1RoPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUaD5DYXRlZ29yeTwvVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRoPlBvc2l0aW9uPC9UaD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGg+QWN0aW9uczwvVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RoZWFkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRib2R5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7KGNoYW5uZWxzIHx8IFtdKS5tYXAoKGNoYW5uZWwpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0eXBlQ29uZmlnID0gQ0hBTk5FTF9UWVBFX0NPTkZJR1tjaGFubmVsLnR5cGVdIHx8IHsgaWNvbjogRmlNZXNzYWdlU3F1YXJlLCBjb2xvcjogJ2dyYXknLCBsYWJlbDogJ090aGVyJyB9O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHIga2V5PXtjaGFubmVsLmlkfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxIU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcz17dHlwZUNvbmZpZy5pY29ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj17YCR7dHlwZUNvbmZpZy5jb2xvcn0uNDAwYH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dD57Y2hhbm5lbC5uYW1lfTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0hTdGFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSBjb2xvclNjaGVtZT17dHlwZUNvbmZpZy5jb2xvcn0+e3R5cGVDb25maWcubGFiZWx9PC9CYWRnZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwiZ3JheS41MDBcIj57Z2V0UGFyZW50TmFtZShjaGFubmVsLnBhcmVudF9pZCl9PC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCJncmF5LjUwMFwiPntjaGFubmVsLnBvc2l0aW9ufTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxIU3RhY2sgc3BhY2luZz17Mn0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBsYWJlbD1cIkVkaXQgQ2hhbm5lbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJFZGl0IGNoYW5uZWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb249ezxGaUVkaXQyIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cImJsdWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUNoYW5uZWxFZGl0KGNoYW5uZWwpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzRGlzYWJsZWQ9e2lzUmF0ZUxpbWl0ZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBsYWJlbD1cIkRlbGV0ZSBDaGFubmVsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkRlbGV0ZSBjaGFubmVsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uPXs8RmlUcmFzaDIgLz59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwicmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVDaGFubmVsRGVsZXRlKGNoYW5uZWwuaWQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzRGlzYWJsZWQ9e2lzUmF0ZUxpbWl0ZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9IU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgICAgICAgPC9UYWJQYW5lbD5cclxuXHJcbiAgICAgICAgICAgICAgey8qIFRoZW1lIEJ1aWxkZXIgVGFiICovfVxyXG4gICAgICAgICAgICAgIDxUYWJQYW5lbD5cclxuICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17Nn0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxCb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgPEhlYWRpbmcgc2l6ZT1cIm1kXCIgbWI9ezR9PvCfjqggVGhlbWUgQnVpbGRlcjwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cImdyYXkuNjAwXCIgX2Rhcms9e3sgY29sb3I6ICdncmF5LjMwMCcgfX0gbWI9ezZ9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgQ3JlYXRlIGFuZCBjdXN0b21pemUgeW91ciBvd24gdGhlbWVzIHdpdGggdGhlIGFkdmFuY2VkIGNvbG9yIGJ1aWxkZXJcclxuICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG5cclxuICAgICAgICAgICAgICAgICAgPFNpbXBsZUdyaWQgY29sdW1ucz17eyBiYXNlOiAxLCBsZzogMiB9fSBzcGFjaW5nPXs2fT5cclxuICAgICAgICAgICAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8SGVhZGluZyBzaXplPVwic21cIj5DdXN0b20gVGhlbWUgQnVpbGRlcjwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFZTdGFjayBzcGFjaW5nPXs0fSBhbGlnbj1cInN0cmV0Y2hcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT1cInNtXCIgY29sb3I9XCJncmF5LjUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQ3JlYXRlIHlvdXIgb3duIGN1c3RvbSB0aGVtZXMgd2l0aCBmdWxsIGNvbG9yIGNvbnRyb2xcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVmdEljb249ezxGYVBhbGV0dGUgLz59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cInB1cnBsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtvbkNvbG9yQnVpbGRlck9wZW59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwibGdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIE9wZW4gQ29sb3IgQnVpbGRlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1ZTdGFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8SGVhZGluZyBzaXplPVwic21cIj5UaGVtZSBQcmVzZXRzPC9IZWFkaW5nPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRCb2R5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VlN0YWNrIHNwYWNpbmc9ezN9IGFsaWduPVwic3RyZXRjaFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwic21cIiBjb2xvcj1cImdyYXkuNTAwXCIgbWI9ezJ9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgUXVpY2sgdGhlbWUgb3B0aW9ucyBhdmFpbGFibGUgaW4gdGhlIG5hdmlnYXRpb24gYmFyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxIU3RhY2sgd3JhcD1cIndyYXBcIiBzcGFjaW5nPXsyfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSBjb2xvclNjaGVtZT1cImJsdWVcIj5EYXJrPC9CYWRnZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSBjb2xvclNjaGVtZT1cInB1cnBsZVwiPk1pZG5pZ2h0PC9CYWRnZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSBjb2xvclNjaGVtZT1cImdyZWVuXCI+Rm9yZXN0PC9CYWRnZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSBjb2xvclNjaGVtZT1cIm9yYW5nZVwiPlN1bnNldDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY29sb3JTY2hlbWU9XCJwaW5rXCI+Um9zZTwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9IU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVlN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgICAgICAgIDwvU2ltcGxlR3JpZD5cclxuICAgICAgICAgICAgICAgIDwvVlN0YWNrPlxyXG4gICAgICAgICAgICAgIDwvVGFiUGFuZWw+XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBCdWlsZGVycyBUYWIgKi99XHJcbiAgICAgICAgICAgICAgPFRhYlBhbmVsPlxyXG4gICAgICAgICAgICAgICAgPFZTdGFjayBzcGFjaW5nPXs2fSBhbGlnbj1cInN0cmV0Y2hcIj5cclxuICAgICAgICAgICAgICAgICAgPEJveD5cclxuICAgICAgICAgICAgICAgICAgICA8SGVhZGluZyBzaXplPVwibWRcIiBtYj17NH0+8J+boO+4jyBCdWlsZGVycyAmIFRvb2xzPC9IZWFkaW5nPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwiZ3JheS42MDBcIiBfZGFyaz17eyBjb2xvcjogJ2dyYXkuMzAwJyB9fSBtYj17Nn0+XHJcbiAgICAgICAgICAgICAgICAgICAgICBDcmVhdGUgY3VzdG9tIGNvbnRlbnQgYW5kIG1hbmFnZSBzZXJ2ZXIgZmVhdHVyZXMgd2l0aCBwb3dlcmZ1bCBidWlsZGVyc1xyXG4gICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8U2ltcGxlR3JpZCBjb2x1bW5zPXt7IGJhc2U6IDEsIGxnOiAyIH19IHNwYWNpbmc9ezZ9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxIZWFkaW5nIHNpemU9XCJzbVwiPkNvbnRlbnQgQnVpbGRlcnM8L0hlYWRpbmc+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17NH0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVmdEljb249ezxGaVphcCAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwiZ3JlZW5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4oJy9hZG1pbi9leHBlcmltZW50YWwvYWRkb24tYnVpbGRlcicsICdfYmxhbmsnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBBZGRvbiBCdWlsZGVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVmdEljb249ezxGaU1lc3NhZ2VTcXVhcmUgLz59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cImJsdWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4oJy9hZG1pbi9hcHBsaWNhdGlvbnMtYnVpbGRlcicsICdfYmxhbmsnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBBcHBsaWNhdGlvbnMgQnVpbGRlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmlNZXNzYWdlU3F1YXJlIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3JTY2hlbWU9XCJwdXJwbGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4oJy9hZG1pbi9lbWJlZC1idWlsZGVyJywgJ19ibGFuaycpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNEaXNhYmxlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1lc3NhZ2UgQnVpbGRlciAoQ29taW5nIFNvb24pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVlN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxIZWFkaW5nIHNpemU9XCJzbVwiPk1hbmFnZW1lbnQgVG9vbHM8L0hlYWRpbmc+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17NH0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVmdEljb249ezxGaVNldHRpbmdzIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3JTY2hlbWU9XCJvcmFuZ2VcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4oJy9hZG1pbi9hZGRvbnMnLCAnX2JsYW5rJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgTWFuYWdlIEFkZG9uc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmlTZXR0aW5ncyAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwidGVhbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cub3BlbignL2FkbWluL2NvbW1hbmRzJywgJ19ibGFuaycpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENvbW1hbmQgTWFuYWdlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmlTZXR0aW5ncyAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwiY3lhblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB3aW5kb3cub3BlbignL2FkbWluL2FwcGxpY2F0aW9ucycsICdfYmxhbmsnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBBcHBsaWNhdGlvbiBNYW5hZ2VyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVlN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgICAgICAgIDwvU2ltcGxlR3JpZD5cclxuICAgICAgICAgICAgICAgIDwvVlN0YWNrPlxyXG4gICAgICAgICAgICAgIDwvVGFiUGFuZWw+XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBBdXRvbWF0aW9uIFRhYiAqL31cclxuICAgICAgICAgICAgICA8VGFiUGFuZWw+XHJcbiAgICAgICAgICAgICAgICA8VlN0YWNrIHNwYWNpbmc9ezZ9IGFsaWduPVwic3RyZXRjaFwiPlxyXG4gICAgICAgICAgICAgICAgICA8Qm94PlxyXG4gICAgICAgICAgICAgICAgICAgIDxIZWFkaW5nIHNpemU9XCJtZFwiIG1iPXs0fT7imqEgQXV0b21hdGlvbiAmIEFjdGl2aXRpZXM8L0hlYWRpbmc+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCJncmF5LjYwMFwiIF9kYXJrPXt7IGNvbG9yOiAnZ3JheS4zMDAnIH19IG1iPXs2fT5cclxuICAgICAgICAgICAgICAgICAgICAgIFNldCB1cCBhdXRvbWF0ZWQgZmVhdHVyZXMgYW5kIHNlcnZlciBhY3Rpdml0aWVzXHJcbiAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICA8L0JveD5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxTaW1wbGVHcmlkIGNvbHVtbnM9e3sgYmFzZTogMSwgbGc6IDIgfX0gc3BhY2luZz17Nn0+XHJcbiAgICAgICAgICAgICAgICAgICAgPENhcmQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEhlYWRpbmcgc2l6ZT1cInNtXCI+QWN0aXZpdHkgVGVtcGxhdGVzPC9IZWFkaW5nPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRCb2R5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cImdyYXkuNTAwXCIgZm9udFNpemU9XCJzbVwiIG1iPXs0fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBQcmUtYnVpbHQgYWN0aXZpdHkgdGVtcGxhdGVzIHRvIGdldCB5b3Ugc3RhcnRlZCBxdWlja2x5OlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17Mn0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiPuKAoiBFdmVudCBNYW5hZ2VtZW50IFN5c3RlbTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT1cInNtXCI+4oCiIFdlbGNvbWUgJiBPbmJvYXJkaW5nIEZsb3c8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiPuKAoiBNb2RlcmF0aW9uIEF1dG9tYXRpb248L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiPuKAoiBDdXN0b20gQ29tbWFuZHM8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiPuKAoiBBdXRvLVJvbGUgQXNzaWdubWVudDwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT1cInNtXCI+4oCiIFNjaGVkdWxlZCBNZXNzYWdlczwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRCb2R5PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPENhcmQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEhlYWRpbmcgc2l6ZT1cInNtXCI+QXV0b21hdGlvbiBTZXR0aW5nczwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFZTdGFjayBzcGFjaW5nPXs0fSBhbGlnbj1cInN0cmV0Y2hcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT1cInNtXCIgY29sb3I9XCJncmF5LjUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQ29uZmlndXJlIGF1dG9tYXRlZCBzZXJ2ZXIgZmVhdHVyZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVmdEljb249ezxGaVphcCAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwieWVsbG93XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzRGlzYWJsZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBBdXRvLU1vZGVyYXRpb24gKENvbWluZyBTb29uKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmlaYXAgLz59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cImdyZWVuXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzRGlzYWJsZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBXZWxjb21lIFN5c3RlbSAoQ29taW5nIFNvb24pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVmdEljb249ezxGaVphcCAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwiYmx1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgRXZlbnQgU2NoZWR1bGVyIChDb21pbmcgU29vbilcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRCb2R5PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cclxuICAgICAgICAgICAgICAgICAgPC9TaW1wbGVHcmlkPlxyXG4gICAgICAgICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgICAgICAgPC9UYWJQYW5lbD5cclxuICAgICAgICAgICAgPC9UYWJQYW5lbHM+XHJcbiAgICAgICAgICA8L1RhYnM+XHJcbiAgICAgICAgPC9WU3RhY2s+XHJcblxyXG4gICAgICAgIHsvKiBNb2RhbHMgKi99XHJcbiAgICAgICAgPFN1c3BlbnNlIGZhbGxiYWNrPXs8U3Bpbm5lciAvPn0+XHJcbiAgICAgICAgICA8Q3JlYXRlQ2hhbm5lbERpYWxvZ1xyXG4gICAgICAgICAgICBpc09wZW49e2lzQ3JlYXRlQ2hhbm5lbE9wZW59XHJcbiAgICAgICAgICAgIG9uQ2xvc2U9e29uQ3JlYXRlQ2hhbm5lbENsb3NlfVxyXG4gICAgICAgICAgICBvblN1Y2Nlc3M9e2ZldGNoQ2hhbm5lbHN9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvU3VzcGVuc2U+XHJcblxyXG4gICAgICAgIDxTdXNwZW5zZSBmYWxsYmFjaz17PFNwaW5uZXIgLz59PlxyXG4gICAgICAgICAgPEVkaXRDaGFubmVsRGlhbG9nXHJcbiAgICAgICAgICAgIGlzT3Blbj17aXNFZGl0Q2hhbm5lbE9wZW59XHJcbiAgICAgICAgICAgIG9uQ2xvc2U9e29uRWRpdENoYW5uZWxDbG9zZX1cclxuICAgICAgICAgICAgY2hhbm5lbD17c2VsZWN0ZWRDaGFubmVsfVxyXG4gICAgICAgICAgICBjYXRlZ29yaWVzPXtjaGFubmVscy5maWx0ZXIoYyA9PiBjLnR5cGUgPT09IDQpfVxyXG4gICAgICAgICAgICBvblN1Y2Nlc3M9e2ZldGNoQ2hhbm5lbHN9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvU3VzcGVuc2U+XHJcblxyXG4gICAgICAgIDxTdXNwZW5zZSBmYWxsYmFjaz17PFNwaW5uZXIgLz59PlxyXG4gICAgICAgICAgPENyZWF0ZVJvbGVEaWFsb2dcclxuICAgICAgICAgICAgaXNPcGVuPXtpc0NyZWF0ZVJvbGVPcGVufVxyXG4gICAgICAgICAgICBvbkNsb3NlPXtvbkNyZWF0ZVJvbGVDbG9zZX1cclxuICAgICAgICAgICAgb25TdWNjZXNzPXtmZXRjaEd1aWxkRGF0YX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9TdXNwZW5zZT5cclxuXHJcbiAgICAgICAgPFN1c3BlbnNlIGZhbGxiYWNrPXs8U3Bpbm5lciAvPn0+XHJcbiAgICAgICAgICA8RWRpdFJvbGVEaWFsb2dcclxuICAgICAgICAgICAgaXNPcGVuPXtpc0VkaXRSb2xlT3Blbn1cclxuICAgICAgICAgICAgb25DbG9zZT17b25FZGl0Um9sZUNsb3NlfVxyXG4gICAgICAgICAgICByb2xlPXtzZWxlY3RlZFJvbGV9XHJcbiAgICAgICAgICAgIG9uU3VjY2Vzcz17ZmV0Y2hHdWlsZERhdGF9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvU3VzcGVuc2U+XHJcblxyXG4gICAgICAgIDxTdXNwZW5zZSBmYWxsYmFjaz17PFNwaW5uZXIgLz59PlxyXG4gICAgICAgICAgPENvbG9yQnVpbGRlclxyXG4gICAgICAgICAgICBpc09wZW49e2lzQ29sb3JCdWlsZGVyT3Blbn1cclxuICAgICAgICAgICAgb25DbG9zZT17b25Db2xvckJ1aWxkZXJDbG9zZX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9TdXNwZW5zZT5cclxuICAgICAgPC9Db250YWluZXI+XHJcbiAgICA8L0xheW91dD5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgZ2V0U2VydmVyU2lkZVByb3BzOiBHZXRTZXJ2ZXJTaWRlUHJvcHMgPSBhc3luYyAoY3R4KSA9PiB7XHJcbiAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oY3R4LnJlcSwgY3R4LnJlcywgYXV0aE9wdGlvbnMpO1xyXG5cclxuICBpZiAoIXNlc3Npb24pIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIHJlZGlyZWN0OiB7XHJcbiAgICAgICAgZGVzdGluYXRpb246ICcvYXBpL2F1dGgvc2lnbmluP2NhbGxiYWNrVXJsPSUyRmFkbWluJTJGZ3VpbGRzJyxcclxuICAgICAgICBwZXJtYW5lbnQ6IGZhbHNlLFxyXG4gICAgICB9LFxyXG4gICAgfTtcclxuICB9XHJcblxyXG4gIHJldHVybiB7IHByb3BzOiB7IHNlc3Npb24gfSB9O1xyXG59O1xyXG4iXSwibmFtZXMiOlsiQm94IiwiQ29udGFpbmVyIiwiSGVhZGluZyIsIlRleHQiLCJTaW1wbGVHcmlkIiwiVlN0YWNrIiwiSFN0YWNrIiwiSWNvbiIsInVzZVRvYXN0IiwiQ2FyZCIsIkNhcmRIZWFkZXIiLCJDYXJkQm9keSIsIkJ1dHRvbiIsIklucHV0IiwiRm9ybUNvbnRyb2wiLCJGb3JtTGFiZWwiLCJCYWRnZSIsIlNrZWxldG9uIiwidXNlRGlzY2xvc3VyZSIsIlRhYnMiLCJUYWJMaXN0IiwiVGFiUGFuZWxzIiwiVGFiIiwiVGFiUGFuZWwiLCJUYWJsZSIsIlRoZWFkIiwiVGJvZHkiLCJUciIsIlRoIiwiVGQiLCJJY29uQnV0dG9uIiwiVG9vbHRpcCIsIlNwaW5uZXIiLCJMYXlvdXQiLCJ1c2VHdWlsZEluZm8iLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlUmVmIiwiU3VzcGVuc2UiLCJnZXRTZXJ2ZXJTZXNzaW9uIiwiYXV0aE9wdGlvbnMiLCJGaVNldHRpbmdzIiwiRmlVc2VycyIsIkZpSGFzaCIsIkZpRWRpdDIiLCJGaVNhdmUiLCJGaVRyYXNoMiIsIkZpUGx1cyIsIkZpTWVzc2FnZVNxdWFyZSIsIkZpVm9sdW1lMiIsIkZpRm9sZGVyUGx1cyIsIkZpUmFkaW8iLCJGaUxvY2siLCJGaU1lc3NhZ2VDaXJjbGUiLCJGaVNlcnZlciIsIkZpWmFwIiwiRmlUb29sIiwiRmFQYWxldHRlIiwiZHluYW1pYyIsIkNyZWF0ZUNoYW5uZWxEaWFsb2ciLCJsb2FkaW5nIiwic2l6ZSIsInNzciIsIkVkaXRDaGFubmVsRGlhbG9nIiwiRWRpdFJvbGVEaWFsb2ciLCJDb2xvckJ1aWxkZXIiLCJDcmVhdGVSb2xlRGlhbG9nIiwiUkFURV9MSU1JVF9NUyIsIkJVTEtfUkFURV9MSU1JVF9NUyIsInVzZVJhdGVMaW1pdCIsImRlbGF5IiwiaXNSYXRlTGltaXRlZCIsInNldElzUmF0ZUxpbWl0ZWQiLCJ0aW1lb3V0UmVmIiwicmVzZXRSYXRlTGltaXQiLCJjdXJyZW50IiwiY2xlYXJUaW1lb3V0Iiwic2V0VGltZW91dCIsIkNIQU5ORUxfVFlQRV9DT05GSUciLCJpY29uIiwiY29sb3IiLCJsYWJlbCIsIlBFUk1JU1NJT05fQkFER0VTIiwiQURNSU5JU1RSQVRPUiIsIk1BTkFHRV9HVUlMRCIsIk1BTkFHRV9ST0xFUyIsIk1BTkFHRV9DSEFOTkVMUyIsIktJQ0tfTUVNQkVSUyIsIkJBTl9NRU1CRVJTIiwiTUFOQUdFX01FU1NBR0VTIiwiTUVOVElPTl9FVkVSWU9ORSIsIlBFUk1JU1NJT05fRkxBR19CSVRTIiwiZGVjb2RlUGVybWlzc2lvbnMiLCJiaXRmaWVsZCIsIkFycmF5IiwiaXNBcnJheSIsInBlcm1pc3Npb25zIiwiYml0cyIsIkJpZ0ludCIsInBlcm1pc3Npb24iLCJiaXQiLCJPYmplY3QiLCJlbnRyaWVzIiwicHVzaCIsImVycm9yIiwiY29uc29sZSIsIlNlcnZlck1hbmFnZW1lbnQiLCJ0b2FzdCIsImRpc3BsYXlOYW1lIiwiaXNCdWxrUmF0ZUxpbWl0ZWQiLCJyZXNldEJ1bGtSYXRlTGltaXQiLCJndWlsZERhdGEiLCJzZXRHdWlsZERhdGEiLCJwcmVmaXgiLCJib3ROYW1lIiwiZ3VpbGROYW1lIiwiZ3VpbGRJY29uIiwiYWN0aXZpdGllcyIsInR5cGUiLCJuYW1lIiwiYWN0aXZpdHlSb3RhdGlvbkludGVydmFsIiwicm9sZXMiLCJzZXRSb2xlcyIsImNoYW5uZWxzIiwic2V0Q2hhbm5lbHMiLCJzZXRMb2FkaW5nIiwiY2hhbm5lbHNMb2FkaW5nIiwic2V0Q2hhbm5lbHNMb2FkaW5nIiwic2F2aW5nIiwic2V0U2F2aW5nIiwiaXNPcGVuIiwiaXNDcmVhdGVDaGFubmVsT3BlbiIsIm9uT3BlbiIsIm9uQ3JlYXRlQ2hhbm5lbE9wZW4iLCJvbkNsb3NlIiwib25DcmVhdGVDaGFubmVsQ2xvc2UiLCJpc0VkaXRDaGFubmVsT3BlbiIsIm9uRWRpdENoYW5uZWxPcGVuIiwib25FZGl0Q2hhbm5lbENsb3NlIiwiaXNDcmVhdGVSb2xlT3BlbiIsIm9uQ3JlYXRlUm9sZU9wZW4iLCJvbkNyZWF0ZVJvbGVDbG9zZSIsImlzRWRpdFJvbGVPcGVuIiwib25FZGl0Um9sZU9wZW4iLCJvbkVkaXRSb2xlQ2xvc2UiLCJpc0NvbG9yQnVpbGRlck9wZW4iLCJvbkNvbG9yQnVpbGRlck9wZW4iLCJvbkNvbG9yQnVpbGRlckNsb3NlIiwic2VsZWN0ZWRSb2xlIiwic2V0U2VsZWN0ZWRSb2xlIiwic2VsZWN0ZWRDaGFubmVsIiwic2V0U2VsZWN0ZWRDaGFubmVsIiwiaWNvbkZpbGUiLCJzZXRJY29uRmlsZSIsImljb25QcmV2aWV3Iiwic2V0SWNvblByZXZpZXciLCJmaWxlSW5wdXRSZWYiLCJoYW5kbGVJY29uRmlsZUNoYW5nZSIsImUiLCJmaWxlIiwidGFyZ2V0IiwiZmlsZXMiLCJyZWFkZXIiLCJGaWxlUmVhZGVyIiwib25sb2FkIiwicmVzdWx0IiwicmVhZEFzRGF0YVVSTCIsInVwbG9hZEljb24iLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiYXBwZW5kIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImJvZHkiLCJvayIsImRhdGEiLCJqc29uIiwicHJldiIsImljb25VcmwiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwic3RhdHVzIiwiZHVyYXRpb24iLCJmZXRjaEd1aWxkRGF0YSIsImd1aWxkUmVzcG9uc2UiLCJyb2xlc1Jlc3BvbnNlIiwiUHJvbWlzZSIsImFsbCIsImd1aWxkIiwicm9sZXNEYXRhIiwiYXJyIiwic29ydCIsImEiLCJiIiwicG9zaXRpb24iLCJmZXRjaENoYW5uZWxzIiwiRXJyb3IiLCJzb3J0ZWRDaGFubmVscyIsImhhbmRsZVNldHRpbmdDaGFuZ2UiLCJzZXR0aW5nIiwidmFsdWUiLCJzYXZlU2V0dGluZ3MiLCJoZWFkZXJzIiwiSlNPTiIsInN0cmluZ2lmeSIsImhhbmRsZVJvbGVFZGl0Iiwicm9sZSIsImhhbmRsZUNoYW5uZWxFZGl0IiwiY2hhbm5lbCIsImhhbmRsZUNoYW5uZWxDcmVhdGUiLCJoYW5kbGVSb2xlQ3JlYXRlIiwiaGFuZGxlQ2hhbm5lbERlbGV0ZSIsImNoYW5uZWxJZCIsImdldFBhcmVudE5hbWUiLCJwYXJlbnRJZCIsInBhcmVudCIsImZpbmQiLCJjIiwiaWQiLCJtYXhXIiwicHkiLCJzcGFjaW5nIiwiaGVpZ2h0IiwiYWxpZ24iLCJiZyIsInAiLCJyb3VuZGVkIiwiYmFja2Ryb3BGaWx0ZXIiLCJib3JkZXIiLCJib3JkZXJDb2xvciIsImJveFNoYWRvdyIsImp1c3RpZnkiLCJiZ0dyYWRpZW50IiwiYmdDbGlwIiwiYXMiLCJtciIsIm10IiwibGVmdEljb24iLCJjb2xvclNjaGVtZSIsIm9uQ2xpY2siLCJpc0xvYWRpbmciLCJpc0Rpc2FibGVkIiwiaXNMYXp5IiwiY29sdW1ucyIsImJhc2UiLCJsZyIsIm9uQ2hhbmdlIiwicGxhY2Vob2xkZXIiLCJpc1JlYWRPbmx5IiwiX2RhcmsiLCJtZW1iZXJDb3VudCIsImxlbmd0aCIsIm1hcCIsIl8iLCJpIiwib3ZlcmZsb3dYIiwidmFyaWFudCIsInciLCJoIiwidG9TdHJpbmciLCJwYWRTdGFydCIsIndyYXAiLCJzbGljZSIsInBlcm0iLCJhcmlhLWxhYmVsIiwidGV4dEFsaWduIiwidHlwZUNvbmZpZyIsInBhcmVudF9pZCIsIm1iIiwiZm9udFNpemUiLCJ3aW5kb3ciLCJvcGVuIiwiZmFsbGJhY2siLCJvblN1Y2Nlc3MiLCJjYXRlZ29yaWVzIiwiZmlsdGVyIiwiZ2V0U2VydmVyU2lkZVByb3BzIiwiY3R4Iiwic2Vzc2lvbiIsInJlcSIsInJlcyIsInJlZGlyZWN0IiwiZGVzdGluYXRpb24iLCJwZXJtYW5lbnQiLCJwcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/admin/guilds.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/discord */ \"next-auth/providers/discord\");\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(pages-dir-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nconst authOptions = {\n    providers: [\n        next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId,\n            clientSecret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n            authorization: {\n                params: {\n                    scope: 'identify email guilds'\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, account, profile }) {\n            if (account && profile) {\n                token.accessToken = account.access_token || null;\n                token.id = profile.id || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                // Ensure we have valid values for serialization\n                const userId = token.id || null;\n                const accessToken = token.accessToken || null;\n                // Attach Discord user ID to session\n                session.user.id = userId;\n                session.user.accessToken = accessToken;\n                // Default to false for admin status\n                let isAdmin = false;\n                if (userId) {\n                    console.log('Checking admin status for user:', userId);\n                    // Check explicit admin IDs\n                    const adminIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.admins || [];\n                    console.log('Configured admin IDs:', adminIds);\n                    if (adminIds.includes(userId)) {\n                        console.log('User is in admin list');\n                        isAdmin = true;\n                    } else {\n                        console.log('User not in admin list, checking roles...');\n                        // Check roles if configured\n                        const roleIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.adminRoleIds || [];\n                        console.log('Configured admin role IDs:', roleIds);\n                        if (roleIds.length && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId) {\n                            try {\n                                console.log('Fetching member roles from Discord API...');\n                                console.log('Bot token (first 20 chars):', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token.substring(0, 20) + '...');\n                                console.log('Guild ID:', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId);\n                                console.log('User ID:', userId);\n                                console.log('Admin role IDs to check:', roleIds);\n                                const res = await fetch(`https://discord.com/api/v10/guilds/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId}/members/${userId}`, {\n                                    headers: {\n                                        Authorization: `Bot ${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token}`\n                                    }\n                                });\n                                console.log('Discord API response status:', res.status);\n                                if (res.ok) {\n                                    const member = await res.json();\n                                    console.log('Member data:', JSON.stringify(member, null, 2));\n                                    console.log('Member roles:', member.roles);\n                                    isAdmin = roleIds.some((rid)=>member.roles?.includes(rid)) || false;\n                                    console.log('Has admin role:', isAdmin);\n                                } else {\n                                    const errorText = await res.text();\n                                    console.error('Failed to fetch member - Status:', res.status);\n                                    console.error('Error response:', errorText);\n                                }\n                            } catch (error) {\n                                console.error('Failed to fetch guild member:', error);\n                            }\n                        } else {\n                            console.log('No role IDs configured or missing bot token/guild ID');\n                        }\n                    }\n                } else {\n                    console.log('No user ID available');\n                }\n                // Set admin status\n                session.user.isAdmin = isAdmin;\n                console.log('Final admin status:', isAdmin);\n                // Ensure all session values are serializable\n                session.user = {\n                    ...session.user,\n                    id: session.user.id || null,\n                    accessToken: session.user.accessToken || null,\n                    isAdmin: session.user.isAdmin || false,\n                    name: session.user.name || null,\n                    email: session.user.email || null,\n                    image: session.user.image || null\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Dynamically derive equivalent localhost URL (same protocol & port)\n            const parsed = new URL(baseUrl);\n            const localhostBase = `${parsed.protocol}//localhost${parsed.port ? `:${parsed.port}` : ''}`;\n            if (url.startsWith(baseUrl) || url.startsWith(localhostBase)) {\n                return url;\n            }\n            return baseUrl;\n        }\n    },\n    secret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.session.secret || _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n    pages: {\n        signIn: '/signin'\n    },\n    // Always show auth errors, but only debug logs in development\n    debug: \"development\" === 'development',\n    logger: {\n        error: (code, metadata)=>{\n            console.error('[NextAuth Error]', code, metadata);\n        },\n        warn: (code)=>{\n            if (true) {\n                console.warn('[NextAuth Warn]', code);\n            }\n        },\n        debug: (code, metadata)=>{\n            if (true) {\n                console.debug('[NextAuth Debug]', code, metadata);\n            }\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/react");;

/***/ }),

/***/ "@emotion/styled":
/*!**********************************!*\
  !*** external "@emotion/styled" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("@emotion/styled");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "swr":
/*!**********************!*\
  !*** external "swr" ***!
  \**********************/
/***/ ((module) => {

module.exports = import("swr");;

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c","chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-cedb36cc","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-0e951d65","chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9","chakra-node_modules_pnpm_chakra-ui_theme-","chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_fa_index_mjs-b1cc012-b4687165","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_f","lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons","pages/admin/guilds-_"], () => (__webpack_exec__("(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Fguilds&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Cguilds.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();