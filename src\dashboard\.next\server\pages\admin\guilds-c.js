"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/admin/guilds-c";
exports.ids = ["pages/admin/guilds-c"];
exports.modules = {

/***/ "(pages-dir-node)/./core/config.ts":
/*!************************!*\
  !*** ./core/config.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dashboardConfig: () => (/* binding */ dashboardConfig),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n// @ts-nocheck\n\n\n\nlet config = {};\ntry {\n    // Locate config.yml by walking up directories (dashboard may run from nested cwd)\n    const possible = [\n        'config.yml',\n        '../config.yml',\n        '../../config.yml',\n        '../../../config.yml',\n        '../../../../config.yml'\n    ].map((rel)=>path__WEBPACK_IMPORTED_MODULE_2__.resolve(process.cwd(), rel));\n    let configPath = possible.find((p)=>fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(p));\n    if (!configPath) {\n        // fallback relative to file location\n        const dirBased = path__WEBPACK_IMPORTED_MODULE_2__.resolve(__dirname, '../../../config.yml');\n        if (fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(dirBased)) configPath = dirBased;\n    }\n    if (!configPath) {\n        throw new Error('config.yml not found');\n    }\n    const fileContents = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(configPath, 'utf8');\n    config = yaml__WEBPACK_IMPORTED_MODULE_1___default().parse(fileContents);\n} catch (error) {\n    console.error('Error: Failed to load config.yml:', error);\n    process.exit(1); // Exit if we can't load the config\n}\n// Export the dashboard-specific config\nconst dashboardConfig = {\n    bot: {\n        token: config.bot.token,\n        clientId: config.bot.clientId,\n        clientSecret: config.bot.clientSecret,\n        guildId: config.bot.guildId,\n        ticketCategoryId: config.bot.ticketCategoryId || null,\n        ticketLogChannelId: config.bot.ticketLogChannelId || null,\n        prefix: config.bot.prefix\n    },\n    dashboard: {\n        admins: config.dashboard?.admins || [],\n        adminRoleIds: config.dashboard?.adminRoleIds || [],\n        session: {\n            secret: config.dashboard?.session?.secret || config.bot.clientSecret\n        }\n    },\n    database: {\n        url: config.database.url,\n        name: config.database.name,\n        options: {\n            maxPoolSize: config.database.options?.maxPoolSize || 10,\n            minPoolSize: config.database.options?.minPoolSize || 1,\n            maxIdleTimeMS: config.database.options?.maxIdleTimeMS || 30000,\n            serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,\n            socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,\n            connectTimeoutMS: config.database.options?.connectTimeoutMS || 10000,\n            retryWrites: config.database.options?.retryWrites !== false,\n            retryReads: config.database.options?.retryReads !== false\n        }\n    }\n};\n// Validate required configuration\nif (!dashboardConfig.bot.token) {\n    console.error('Error: Discord bot token is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.clientId || !dashboardConfig.bot.clientSecret) {\n    console.error('Error: Discord OAuth2 credentials (clientId and clientSecret) are required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.guildId) {\n    console.error('Error: Guild ID is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.database.url || !dashboardConfig.database.name) {\n    console.error('Error: Database configuration (url and name) is required in config.yml');\n    process.exit(1);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dashboardConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./core/config.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useExperimentalFeatures.ts":
/*!******************************************!*\
  !*** ./hooks/useExperimentalFeatures.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useExperimentalFeatures)\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction useExperimentalFeatures() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.useSession)();\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reason, setReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useExperimentalFeatures.useEffect\": ()=>{\n            async function checkAccess() {\n                if (!session?.user) {\n                    setHasAccess(false);\n                    setReason('unauthenticated');\n                    setIsLoading(false);\n                    return;\n                }\n                try {\n                    const response = await fetch('/api/discord/user/experimental');\n                    const data = await response.json();\n                    setHasAccess(data.hasAccess);\n                    setReason(data.reason);\n                } catch (error) {\n                    console.error('Error checking experimental features access:', error);\n                    setHasAccess(false);\n                    setReason('error');\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n            checkAccess();\n        }\n    }[\"useExperimentalFeatures.useEffect\"], [\n        session\n    ]);\n    return {\n        hasAccess,\n        reason,\n        isLoading,\n        isDeveloper: reason === 'developer',\n        isTester: reason === 'tester'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useExperimentalFeatures.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useGuildInfo.ts":
/*!*******************************!*\
  !*** ./hooks/useGuildInfo.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useGuildInfo)\n/* harmony export */ });\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swr */ \"swr\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swr__WEBPACK_IMPORTED_MODULE_0__]);\nswr__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst fetcher = async (url)=>{\n    const res = await fetch(url);\n    if (!res.ok) {\n        if (res.status === 401) {\n            // Return default data for unauthorized state\n            return {\n                name: '404 Bot',\n                botName: '404 Bot'\n            };\n        }\n        throw new Error('Failed to fetch guild info');\n    }\n    return res.json();\n};\nfunction useGuildInfo() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    // Only fetch if we're authenticated\n    const shouldFetch = status === 'authenticated';\n    const { data, error } = (0,swr__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(shouldFetch ? '/api/discord/guild' : null, fetcher, {\n        revalidateOnFocus: false,\n        revalidateOnReconnect: false\n    });\n    // Local preference state (guild vs bot)\n    const [pref, setPref] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"useGuildInfo.useState\": ()=>{\n            if (true) return 'guild';\n            return localStorage.getItem('dashboardDisplayNamePref') || 'guild';\n        }\n    }[\"useGuildInfo.useState\"]);\n    // Function to update preference and broadcast change\n    const updatePreference = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useGuildInfo.useCallback[updatePreference]\": (newPref)=>{\n            setPref(newPref);\n            if (false) {}\n        }\n    }[\"useGuildInfo.useCallback[updatePreference]\"], []);\n    // Listen for preference changes in this tab (custom event) or other tabs (storage event)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useGuildInfo.useEffect\": ()=>{\n            if (true) return;\n            const handleCustom = {\n                \"useGuildInfo.useEffect.handleCustom\": (e)=>{\n                    if (e?.detail) setPref(e.detail);\n                }\n            }[\"useGuildInfo.useEffect.handleCustom\"];\n            const handleStorage = {\n                \"useGuildInfo.useEffect.handleStorage\": (e)=>{\n                    if (e.key === 'dashboardDisplayNamePref') {\n                        setPref(e.newValue || 'guild');\n                    }\n                }\n            }[\"useGuildInfo.useEffect.handleStorage\"];\n            window.addEventListener('displayNamePrefChanged', handleCustom);\n            window.addEventListener('storage', handleStorage);\n            return ({\n                \"useGuildInfo.useEffect\": ()=>{\n                    window.removeEventListener('displayNamePrefChanged', handleCustom);\n                    window.removeEventListener('storage', handleStorage);\n                }\n            })[\"useGuildInfo.useEffect\"];\n        }\n    }[\"useGuildInfo.useEffect\"], []);\n    // Default display name when not authenticated\n    const defaultName = '404 Bot Dashboard';\n    // Determine displayName\n    let displayName = defaultName;\n    if (data) {\n        if (pref === 'bot' && data.botName) {\n            displayName = data.botName;\n        } else {\n            displayName = data.name || defaultName;\n        }\n    }\n    return {\n        guild: data,\n        displayName,\n        pref,\n        updatePreference,\n        isLoading: shouldFetch && !error && !data,\n        isError: !!error\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useGuildInfo.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/admin/guilds.tsx":
/*!********************************!*\
  !*** ./pages/admin/guilds.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServerManagement),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Card,CardBody,CardHeader,Container,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,SimpleGrid,Skeleton,Spinner,Tab,TabList,TabPanel,TabPanels,Table,Tabs,Tbody,Td,Text,Th,Thead,Tooltip,Tr,VStack,useDisclosure,useToast!=!@chakra-ui/react */ \"(pages-dir-node)/__barrel_optimize__?names=Badge,Box,Button,Card,CardBody,CardHeader,Container,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,SimpleGrid,Skeleton,Spinner,Tab,TabList,TabPanel,TabPanels,Table,Tabs,Tbody,Td,Text,Th,Thead,Tooltip,Tr,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/Layout */ \"(pages-dir-node)/./components/Layout.tsx\");\n/* harmony import */ var _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useGuildInfo */ \"(pages-dir-node)/./hooks/useGuildInfo.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _api_auth_nextauth___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../api/auth/[...nextauth] */ \"(pages-dir-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit2,FiFolderPlus,FiHash,FiLock,FiMessageCircle,FiMessageSquare,FiPlus,FiRadio,FiSave,FiServer,FiSettings,FiTool,FiTrash2,FiUsers,FiVolume2,FiZap!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiEdit2,FiFolderPlus,FiHash,FiLock,FiMessageCircle,FiMessageSquare,FiPlus,FiRadio,FiSave,FiServer,FiSettings,FiTool,FiTrash2,FiUsers,FiVolume2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaPalette!=!react-icons/fa */ \"(pages-dir-node)/__barrel_optimize__?names=FaPalette!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Layout__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__, _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__]);\n([_components_Layout__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__, _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// @ts-nocheck\n\n\n\n\n\n\n\n\n\n\n// Dynamic imports for heavy components\nconst CreateChannelDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-cedb36cc\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-0e951d65\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171\"), __webpack_require__.e(\"lib-node_modules_pnpm_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_o\"), __webpack_require__.e(\"lib-node_modules_pnpm_r\"), __webpack_require__.e(\"lib-node_modules_pnpm_t\"), __webpack_require__.e(\"_pages-dir-node_components_CreateChannelDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/CreateChannelDialog */ \"(pages-dir-node)/./components/CreateChannelDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/CreateChannelDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 63,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\nconst EditChannelDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-cedb36cc\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-0e951d65\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171\"), __webpack_require__.e(\"lib-node_modules_pnpm_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_o\"), __webpack_require__.e(\"lib-node_modules_pnpm_r\"), __webpack_require__.e(\"lib-node_modules_pnpm_t\"), __webpack_require__.e(\"_pages-dir-node_components_EditChannelDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/EditChannelDialog */ \"(pages-dir-node)/./components/EditChannelDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/EditChannelDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 68,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\nconst EditRoleDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-cedb36cc\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-0e951d65\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171\"), __webpack_require__.e(\"lib-node_modules_pnpm_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_o\"), __webpack_require__.e(\"lib-node_modules_pnpm_r\"), __webpack_require__.e(\"lib-node_modules_pnpm_t\"), __webpack_require__.e(\"commons\"), __webpack_require__.e(\"_pages-dir-node_components_EditRoleDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/EditRoleDialog */ \"(pages-dir-node)/./components/EditRoleDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/EditRoleDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 73,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\nconst ColorBuilder = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-node_components_ColorBuilder_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/ColorBuilder */ \"(pages-dir-node)/./components/ColorBuilder.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/ColorBuilder\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 78,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\nconst CreateRoleDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_6___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-cedb36cc\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-0e951d65\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811\"), __webpack_require__.e(\"lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171\"), __webpack_require__.e(\"lib-node_modules_pnpm_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i\"), __webpack_require__.e(\"lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338\"), __webpack_require__.e(\"lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d\"), __webpack_require__.e(\"lib-node_modules_pnpm_o\"), __webpack_require__.e(\"lib-node_modules_pnpm_r\"), __webpack_require__.e(\"lib-node_modules_pnpm_t\"), __webpack_require__.e(\"commons\"), __webpack_require__.e(\"_pages-dir-node_components_CreateRoleDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/CreateRoleDialog */ \"(pages-dir-node)/./components/CreateRoleDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/CreateRoleDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 83,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n// Rate limiting constants\nconst RATE_LIMIT_MS = 2000; // 2 seconds between operations\nconst BULK_RATE_LIMIT_MS = 5000; // 5 seconds between bulk operations\n// Custom hook for rate limiting\nfunction useRateLimit(delay = RATE_LIMIT_MS) {\n    const [isRateLimited, setIsRateLimited] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const resetRateLimit = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"useRateLimit.useCallback[resetRateLimit]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            setIsRateLimited(true);\n            timeoutRef.current = setTimeout({\n                \"useRateLimit.useCallback[resetRateLimit]\": ()=>{\n                    setIsRateLimited(false);\n                }\n            }[\"useRateLimit.useCallback[resetRateLimit]\"], delay);\n        }\n    }[\"useRateLimit.useCallback[resetRateLimit]\"], [\n        delay\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"useRateLimit.useEffect\": ()=>{\n            return ({\n                \"useRateLimit.useEffect\": ()=>{\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"useRateLimit.useEffect\"];\n        }\n    }[\"useRateLimit.useEffect\"], []);\n    return [\n        isRateLimited,\n        resetRateLimit\n    ];\n}\nconst CHANNEL_TYPE_CONFIG = {\n    0: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMessageSquare,\n        color: 'blue',\n        label: 'Text'\n    },\n    2: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiVolume2,\n        color: 'green',\n        label: 'Voice'\n    },\n    4: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiFolderPlus,\n        color: 'purple',\n        label: 'Category'\n    },\n    5: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiRadio,\n        color: 'orange',\n        label: 'Announcement'\n    },\n    11: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMessageCircle,\n        color: 'cyan',\n        label: 'Public Thread'\n    },\n    12: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiLock,\n        color: 'pink',\n        label: 'Private Thread'\n    },\n    13: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiHash,\n        color: 'teal',\n        label: 'Stage Voice'\n    },\n    15: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiHash,\n        color: 'gray',\n        label: 'Forum'\n    }\n};\nconst PERMISSION_BADGES = {\n    ADMINISTRATOR: {\n        color: 'red',\n        label: 'Admin'\n    },\n    MANAGE_GUILD: {\n        color: 'orange',\n        label: 'Manage Server'\n    },\n    MANAGE_ROLES: {\n        color: 'yellow',\n        label: 'Manage Roles'\n    },\n    MANAGE_CHANNELS: {\n        color: 'green',\n        label: 'Manage Channels'\n    },\n    KICK_MEMBERS: {\n        color: 'purple',\n        label: 'Kick'\n    },\n    BAN_MEMBERS: {\n        color: 'pink',\n        label: 'Ban'\n    },\n    MANAGE_MESSAGES: {\n        color: 'blue',\n        label: 'Manage Messages'\n    },\n    MENTION_EVERYONE: {\n        color: 'cyan',\n        label: 'Mention @everyone'\n    }\n};\n// Add this helper map and function after PERMISSION_BADGES constant\nconst PERMISSION_FLAG_BITS = {\n    ADMINISTRATOR: 1n << 3n,\n    MANAGE_GUILD: 1n << 5n,\n    MANAGE_ROLES: 1n << 28n,\n    MANAGE_CHANNELS: 1n << 4n,\n    KICK_MEMBERS: 1n << 1n,\n    BAN_MEMBERS: 1n << 2n,\n    MANAGE_MESSAGES: 1n << 13n,\n    MENTION_EVERYONE: 1n << 17n\n};\nfunction decodePermissions(bitfield) {\n    if (!bitfield) return [];\n    if (Array.isArray(bitfield)) return bitfield;\n    try {\n        const permissions = [];\n        const bits = BigInt(bitfield);\n        for (const [permission, bit] of Object.entries(PERMISSION_FLAG_BITS)){\n            if ((bits & bit) === bit) {\n                permissions.push(permission);\n            }\n        }\n        return permissions;\n    } catch (error) {\n        console.error('Error decoding permissions:', error);\n        return [];\n    }\n}\nfunction ServerManagement() {\n    const toast = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const { displayName } = (0,_hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [isRateLimited, resetRateLimit] = useRateLimit();\n    const [isBulkRateLimited, resetBulkRateLimit] = useRateLimit(BULK_RATE_LIMIT_MS);\n    // State for guild settings\n    const [guildData, setGuildData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        prefix: '!',\n        botName: 'Bot',\n        guildName: '',\n        guildIcon: null,\n        activities: [\n            {\n                type: 'PLAYING',\n                name: 'with Discord.js'\n            }\n        ],\n        activityRotationInterval: 60\n    });\n    // State for roles and channels\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [channels, setChannels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [channelsLoading, setChannelsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Modal states\n    const { isOpen: isCreateChannelOpen, onOpen: onCreateChannelOpen, onClose: onCreateChannelClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useDisclosure)();\n    const { isOpen: isEditChannelOpen, onOpen: onEditChannelOpen, onClose: onEditChannelClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useDisclosure)();\n    const { isOpen: isCreateRoleOpen, onOpen: onCreateRoleOpen, onClose: onCreateRoleClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useDisclosure)();\n    const { isOpen: isEditRoleOpen, onOpen: onEditRoleOpen, onClose: onEditRoleClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useDisclosure)();\n    const { isOpen: isColorBuilderOpen, onOpen: onColorBuilderOpen, onClose: onColorBuilderClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.useDisclosure)();\n    // Selected items for editing\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedChannel, setSelectedChannel] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // Bulk selection state\n    const [selectedRoles, setSelectedRoles] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedChannels, setSelectedChannels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bulkDeleting, setBulkDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // File upload state\n    const [iconFile, setIconFile] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [iconPreview, setIconPreview] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const handleIconFileChange = (e)=>{\n        const file = e.target.files?.[0];\n        if (file) {\n            setIconFile(file);\n            const reader = new FileReader();\n            reader.onload = (e)=>setIconPreview(e.target?.result);\n            reader.readAsDataURL(file);\n        }\n    };\n    const uploadIcon = async ()=>{\n        if (!iconFile) return;\n        const formData = new FormData();\n        formData.append('icon', iconFile);\n        try {\n            const response = await fetch('/api/discord/settings', {\n                method: 'POST',\n                body: formData\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setGuildData((prev)=>({\n                        ...prev,\n                        guildIcon: data.iconUrl\n                    }));\n                setIconFile(null);\n                setIconPreview(null);\n                toast({\n                    title: 'Success',\n                    description: 'Guild icon updated successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to upload icon',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const fetchGuildData = async ()=>{\n        try {\n            const [guildResponse, rolesResponse] = await Promise.all([\n                fetch('/api/discord/guild'),\n                fetch('/api/discord/roles')\n            ]);\n            if (guildResponse.ok) {\n                const guild = await guildResponse.json();\n                setGuildData((prev)=>({\n                        ...prev,\n                        guildName: guild.name,\n                        guildIcon: guild.icon,\n                        botName: guild.botName || prev.botName // Use API botName or fallback to current\n                    }));\n            }\n            if (rolesResponse.ok) {\n                const rolesData = await rolesResponse.json();\n                const arr = Array.isArray(rolesData) ? rolesData : rolesData.roles || [];\n                setRoles(arr.sort((a, b)=>b.position - a.position));\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch guild data',\n                status: 'error',\n                duration: 3000\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchChannels = async ()=>{\n        try {\n            const response = await fetch('/api/discord/channels');\n            if (!response.ok) {\n                throw new Error('Failed to fetch channels');\n            }\n            const data = await response.json();\n            const sortedChannels = (data || []).sort((a, b)=>{\n                if (a.type === 4 && b.type !== 4) return -1;\n                if (a.type !== 4 && b.type === 4) return 1;\n                return a.position - b.position;\n            });\n            setChannels(sortedChannels);\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch channels',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setChannelsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"ServerManagement.useEffect\": ()=>{\n            fetchGuildData();\n            fetchChannels();\n        }\n    }[\"ServerManagement.useEffect\"], []);\n    const handleSettingChange = (setting, value)=>{\n        setGuildData((prev)=>({\n                ...prev,\n                [setting]: value\n            }));\n    };\n    const saveSettings = async ()=>{\n        if (saving || isRateLimited) return;\n        setSaving(true);\n        resetRateLimit();\n        try {\n            const response = await fetch('/api/discord/settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(guildData)\n            });\n            if (response.ok) {\n                toast({\n                    title: 'Success',\n                    description: 'Settings saved successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            } else {\n                throw new Error('Failed to save settings');\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to save settings',\n                status: 'error',\n                duration: 3000\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleRoleEdit = (role)=>{\n        setSelectedRole(role);\n        onEditRoleOpen();\n    };\n    const handleChannelEdit = (channel)=>{\n        setSelectedChannel(channel);\n        onEditChannelOpen();\n    };\n    const handleChannelCreate = ()=>{\n        onCreateChannelOpen();\n    };\n    const handleRoleCreate = ()=>{\n        onCreateRoleOpen();\n    };\n    const handleChannelDelete = async (channelId)=>{\n        if (isRateLimited) return;\n        try {\n            resetRateLimit();\n            const response = await fetch(`/api/discord/channels/${channelId}`, {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchChannels();\n                toast({\n                    title: 'Success',\n                    description: 'Channel deleted successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to delete channel',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const getParentName = (parentId)=>{\n        if (!parentId || !channels) return '-';\n        const parent = channels.find((c)=>c.id === parentId);\n        return parent ? parent.name : '-';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                    spacing: 6,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            height: \"60px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            height: \"400px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                lineNumber: 447,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 446,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                    spacing: 8,\n                    align: \"stretch\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                            bg: \"rgba(255,255,255,0.08)\",\n                            p: 8,\n                            rounded: \"2xl\",\n                            backdropFilter: \"blur(10px)\",\n                            border: \"2px solid\",\n                            borderColor: \"blue.400\",\n                            boxShadow: \"0 0 15px rgba(66, 153, 225, 0.4)\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                justify: \"space-between\",\n                                align: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                size: \"xl\",\n                                                bgGradient: \"linear(to-r, blue.300, purple.400)\",\n                                                bgClip: \"text\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                        as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiServer,\n                                                        mr: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Server Management\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                color: \"gray.300\",\n                                                mt: 2,\n                                                children: [\n                                                    \"Comprehensive management for \",\n                                                    displayName || guildData.guildName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSave, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 27\n                                        }, void 0),\n                                        colorScheme: \"blue\",\n                                        onClick: saveSettings,\n                                        isLoading: saving,\n                                        isDisabled: isRateLimited,\n                                        size: \"lg\",\n                                        children: \"Save Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                            colorScheme: \"blue\",\n                            isLazy: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSettings,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"General Settings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                    as: _barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaPalette,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Theme Builder\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiTool,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Builders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiZap,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Automation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabPanels, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                spacing: 8,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                    size: \"md\",\n                                                                    children: \"Basic Settings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.SimpleGrid, {\n                                                                    columns: {\n                                                                        base: 1,\n                                                                        lg: 2\n                                                                    },\n                                                                    spacing: 6,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                            children: \"Bot Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 532,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                            value: guildData.botName,\n                                                                                            onChange: (e)=>handleSettingChange('botName', e.target.value),\n                                                                                            placeholder: \"Enter bot name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 533,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 531,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                            children: \"Command Prefix\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 540,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                            value: guildData.prefix,\n                                                                                            onChange: (e)=>handleSettingChange('prefix', e.target.value),\n                                                                                            placeholder: \"Enter command prefix\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 541,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 539,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                            children: \"Server Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 550,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                            value: guildData?.name || '',\n                                                                                            isReadOnly: true,\n                                                                                            bg: \"gray.50\",\n                                                                                            _dark: {\n                                                                                                bg: 'gray.700'\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 551,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 549,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                                            children: \"Member Count\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 559,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                                            value: guildData?.memberCount || '0',\n                                                                                            isReadOnly: true,\n                                                                                            bg: \"gray.50\",\n                                                                                            _dark: {\n                                                                                                bg: 'gray.700'\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 560,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 558,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                    justify: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiUsers,\n                                                                                    mr: 2\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 577,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Roles (\",\n                                                                                roles.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiPlus, {}, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 581,\n                                                                                columnNumber: 37\n                                                                            }, void 0),\n                                                                            colorScheme: \"green\",\n                                                                            onClick: handleRoleCreate,\n                                                                            isDisabled: isRateLimited,\n                                                                            children: \"Create Role\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 580,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                    spacing: 3,\n                                                                    children: [\n                                                                        ...Array(3)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                                            height: \"60px\"\n                                                                        }, i, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                    overflowX: \"auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                                        variant: \"simple\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Thead, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tr, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Role\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 602,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Members\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 603,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Permissions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 604,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Actions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 605,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 601,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 600,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tbody, {\n                                                                                children: (roles || []).map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tr, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                                                            w: 4,\n                                                                                                            h: 4,\n                                                                                                            rounded: \"full\",\n                                                                                                            bg: role.color ? `#${role.color.toString(16).padStart(6, '0')}` : 'gray.500'\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 613,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                                            children: role.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 619,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 612,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 611,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                                    colorScheme: \"blue\",\n                                                                                                    children: \"0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 623,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 622,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                                    wrap: \"wrap\",\n                                                                                                    spacing: 1,\n                                                                                                    children: [\n                                                                                                        (decodePermissions(role.permissions) || []).slice(0, 3).map((perm)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                                                colorScheme: PERMISSION_BADGES[perm]?.color || 'gray',\n                                                                                                                size: \"sm\",\n                                                                                                                children: PERMISSION_BADGES[perm]?.label || perm\n                                                                                                            }, perm, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 628,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)),\n                                                                                                        decodePermissions(role.permissions).length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                                            colorScheme: \"gray\",\n                                                                                                            size: \"sm\",\n                                                                                                            children: [\n                                                                                                                \"+\",\n                                                                                                                decodePermissions(role.permissions).length - 3\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 637,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 626,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 625,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                                                                            label: \"Edit Role\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.IconButton, {\n                                                                                                                \"aria-label\": \"Edit role\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiEdit2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 648,\n                                                                                                                    columnNumber: 49\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"blue\",\n                                                                                                                onClick: ()=>handleRoleEdit(role),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 646,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 645,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                                                                            label: \"Delete Role\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.IconButton, {\n                                                                                                                \"aria-label\": \"Delete role\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiTrash2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 659,\n                                                                                                                    columnNumber: 49\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"red\",\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 657,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 656,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 644,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 643,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, role.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 610,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 608,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 599,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                    justify: \"space-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiHash,\n                                                                                    mr: 2\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 682,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Channels (\",\n                                                                                channels.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 681,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiPlus, {}, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 686,\n                                                                                columnNumber: 37\n                                                                            }, void 0),\n                                                                            colorScheme: \"blue\",\n                                                                            onClick: onCreateChannelOpen,\n                                                                            children: \"Create Channel\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                children: channelsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                    spacing: 3,\n                                                                    children: [\n                                                                        ...Array(5)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                                            height: \"50px\"\n                                                                        }, i, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 696,\n                                                                    columnNumber: 25\n                                                                }, this) : channels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                    color: \"gray.500\",\n                                                                    textAlign: \"center\",\n                                                                    py: 8,\n                                                                    children: \"No channels found\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                    overflowX: \"auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                                                        variant: \"simple\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Thead, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tr, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 710,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Type\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 711,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Category\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 712,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Position\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 713,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Th, {\n                                                                                            children: \"Actions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 714,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 709,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tbody, {\n                                                                                children: (channels || []).map((channel)=>{\n                                                                                    const typeConfig = CHANNEL_TYPE_CONFIG[channel.type] || {\n                                                                                        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMessageSquare,\n                                                                                        color: 'gray',\n                                                                                        label: 'Other'\n                                                                                    };\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tr, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Icon, {\n                                                                                                            as: typeConfig.icon,\n                                                                                                            color: `${typeConfig.color}.400`\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 724,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                                            children: channel.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 728,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 723,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 722,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                                    colorScheme: typeConfig.color,\n                                                                                                    children: typeConfig.label\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 732,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 731,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                                    color: \"gray.500\",\n                                                                                                    children: getParentName(channel.parent_id)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 735,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 734,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                                    color: \"gray.500\",\n                                                                                                    children: channel.position\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 738,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 737,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                                                                            label: \"Edit Channel\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.IconButton, {\n                                                                                                                \"aria-label\": \"Edit channel\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiEdit2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 745,\n                                                                                                                    columnNumber: 51\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"blue\",\n                                                                                                                onClick: ()=>handleChannelEdit(channel),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 743,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 742,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                                                                            label: \"Delete Channel\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.IconButton, {\n                                                                                                                \"aria-label\": \"Delete channel\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiTrash2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 756,\n                                                                                                                    columnNumber: 51\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"red\",\n                                                                                                                onClick: ()=>handleChannelDelete(channel.id),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 754,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 753,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 741,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 740,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, channel.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 721,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 717,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"\\uD83C\\uDFA8 Theme Builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Create and customize your own themes with the advanced color builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Custom Theme Builder\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 791,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 790,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Create your own custom themes with full color control\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 795,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaPalette, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 799,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"purple\",\n                                                                                    onClick: onColorBuilderOpen,\n                                                                                    size: \"lg\",\n                                                                                    children: \"Open Color Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 798,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 794,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 793,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Theme Presets\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 812,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 811,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 3,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    mb: 2,\n                                                                                    children: \"Quick theme options available in the navigation bar\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 816,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.HStack, {\n                                                                                    wrap: \"wrap\",\n                                                                                    spacing: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                            colorScheme: \"blue\",\n                                                                                            children: \"Dark\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 820,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                            colorScheme: \"purple\",\n                                                                                            children: \"Midnight\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 821,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                            colorScheme: \"green\",\n                                                                                            children: \"Forest\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 822,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                            colorScheme: \"orange\",\n                                                                                            children: \"Sunset\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 823,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                            colorScheme: \"pink\",\n                                                                                            children: \"Rose\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 824,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 819,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 815,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 814,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 810,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"\\uD83D\\uDEE0️ Builders & Tools\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 837,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Create custom content and manage server features with powerful builders\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 836,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Content Builders\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 846,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 845,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 851,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"green\",\n                                                                                    onClick: ()=>window.open('/admin/experimental/addon-builder', '_blank'),\n                                                                                    children: \"Addon Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 850,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMessageSquare, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 858,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"blue\",\n                                                                                    onClick: ()=>window.open('/admin/applications-builder', '_blank'),\n                                                                                    children: \"Applications Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 857,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMessageSquare, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 865,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"purple\",\n                                                                                    onClick: ()=>window.open('/admin/embed-builder', '_blank'),\n                                                                                    isDisabled: true,\n                                                                                    children: \"Message Builder (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 864,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 849,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 848,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 844,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Management Tools\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 878,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 877,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 883,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"orange\",\n                                                                                    onClick: ()=>window.open('/admin/addons', '_blank'),\n                                                                                    children: \"Manage Addons\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 882,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 890,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"teal\",\n                                                                                    onClick: ()=>window.open('/admin/commands', '_blank'),\n                                                                                    children: \"Command Manager\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 889,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 897,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"cyan\",\n                                                                                    onClick: ()=>window.open('/admin/applications', '_blank'),\n                                                                                    children: \"Application Manager\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 896,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 881,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 880,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"⚡ Automation & Activities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 914,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Set up automated features and server activities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 915,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Activity Templates\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 923,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 922,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                color: \"gray.500\",\n                                                                                fontSize: \"sm\",\n                                                                                mb: 4,\n                                                                                children: \"Pre-built activity templates to get you started quickly:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 926,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                                spacing: 2,\n                                                                                align: \"stretch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Event Management System\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 930,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Welcome & Onboarding Flow\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 931,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Moderation Automation\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 932,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Custom Commands\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 933,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Auto-Role Assignment\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 934,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Scheduled Messages\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 935,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 929,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 925,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 921,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Automation Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 942,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 941,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Configure automated server features\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 946,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 950,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"yellow\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Auto-Moderation (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 949,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 958,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"green\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Welcome System (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 957,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 966,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"blue\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Event Scheduler (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 965,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 945,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 944,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 940,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 984,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateChannelDialog, {\n                        isOpen: isCreateChannelOpen,\n                        onClose: onCreateChannelClose,\n                        onSuccess: fetchChannels\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 985,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 984,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 992,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditChannelDialog, {\n                        isOpen: isEditChannelOpen,\n                        onClose: onEditChannelClose,\n                        channel: selectedChannel,\n                        categories: channels.filter((c)=>c.type === 4),\n                        onSuccess: fetchChannels\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 993,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 992,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1002,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateRoleDialog, {\n                        isOpen: isCreateRoleOpen,\n                        onClose: onCreateRoleClose,\n                        onSuccess: fetchGuildData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1003,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1002,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1010,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditRoleDialog, {\n                        isOpen: isEditRoleOpen,\n                        onClose: onEditRoleClose,\n                        role: selectedRole,\n                        onSuccess: fetchGuildData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1011,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1010,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1019,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColorBuilder, {\n                        isOpen: isColorBuilderOpen,\n                        onClose: onColorBuilderClose\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1020,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1019,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 459,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n        lineNumber: 458,\n        columnNumber: 5\n    }, this);\n}\nconst getServerSideProps = async (ctx)=>{\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_4__.getServerSession)(ctx.req, ctx.res, _api_auth_nextauth___WEBPACK_IMPORTED_MODULE_5__.authOptions);\n    if (!session) {\n        return {\n            redirect: {\n                destination: '/api/auth/signin?callbackUrl=%2Fadmin%2Fguilds',\n                permanent: false\n            }\n        };\n    }\n    return {\n        props: {\n            session\n        }\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/admin/guilds.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/discord */ \"next-auth/providers/discord\");\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(pages-dir-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nconst authOptions = {\n    providers: [\n        next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId,\n            clientSecret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n            authorization: {\n                params: {\n                    scope: 'identify email guilds'\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, account, profile }) {\n            if (account && profile) {\n                token.accessToken = account.access_token || null;\n                token.id = profile.id || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                // Ensure we have valid values for serialization\n                const userId = token.id || null;\n                const accessToken = token.accessToken || null;\n                // Attach Discord user ID to session\n                session.user.id = userId;\n                session.user.accessToken = accessToken;\n                // Default to false for admin status\n                let isAdmin = false;\n                if (userId) {\n                    console.log('Checking admin status for user:', userId);\n                    // Check explicit admin IDs\n                    const adminIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.admins || [];\n                    console.log('Configured admin IDs:', adminIds);\n                    if (adminIds.includes(userId)) {\n                        console.log('User is in admin list');\n                        isAdmin = true;\n                    } else {\n                        console.log('User not in admin list, checking roles...');\n                        // Check roles if configured\n                        const roleIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.adminRoleIds || [];\n                        console.log('Configured admin role IDs:', roleIds);\n                        if (roleIds.length && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId) {\n                            try {\n                                console.log('Fetching member roles from Discord API...');\n                                console.log('Bot token (first 20 chars):', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token.substring(0, 20) + '...');\n                                console.log('Guild ID:', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId);\n                                console.log('User ID:', userId);\n                                console.log('Admin role IDs to check:', roleIds);\n                                const res = await fetch(`https://discord.com/api/v10/guilds/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId}/members/${userId}`, {\n                                    headers: {\n                                        Authorization: `Bot ${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token}`\n                                    }\n                                });\n                                console.log('Discord API response status:', res.status);\n                                if (res.ok) {\n                                    const member = await res.json();\n                                    console.log('Member data:', JSON.stringify(member, null, 2));\n                                    console.log('Member roles:', member.roles);\n                                    isAdmin = roleIds.some((rid)=>member.roles?.includes(rid)) || false;\n                                    console.log('Has admin role:', isAdmin);\n                                } else {\n                                    const errorText = await res.text();\n                                    console.error('Failed to fetch member - Status:', res.status);\n                                    console.error('Error response:', errorText);\n                                }\n                            } catch (error) {\n                                console.error('Failed to fetch guild member:', error);\n                            }\n                        } else {\n                            console.log('No role IDs configured or missing bot token/guild ID');\n                        }\n                    }\n                } else {\n                    console.log('No user ID available');\n                }\n                // Set admin status\n                session.user.isAdmin = isAdmin;\n                console.log('Final admin status:', isAdmin);\n                // Ensure all session values are serializable\n                session.user = {\n                    ...session.user,\n                    id: session.user.id || null,\n                    accessToken: session.user.accessToken || null,\n                    isAdmin: session.user.isAdmin || false,\n                    name: session.user.name || null,\n                    email: session.user.email || null,\n                    image: session.user.image || null\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Dynamically derive equivalent localhost URL (same protocol & port)\n            const parsed = new URL(baseUrl);\n            const localhostBase = `${parsed.protocol}//localhost${parsed.port ? `:${parsed.port}` : ''}`;\n            if (url.startsWith(baseUrl) || url.startsWith(localhostBase)) {\n                return url;\n            }\n            return baseUrl;\n        }\n    },\n    secret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.session.secret || _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n    pages: {\n        signIn: '/signin'\n    },\n    // Always show auth errors, but only debug logs in development\n    debug: \"development\" === 'development',\n    logger: {\n        error: (code, metadata)=>{\n            console.error('[NextAuth Error]', code, metadata);\n        },\n        warn: (code)=>{\n            if (true) {\n                console.warn('[NextAuth Warn]', code);\n            }\n        },\n        debug: (code, metadata)=>{\n            if (true) {\n                console.debug('[NextAuth Debug]', code, metadata);\n            }\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/react");;

/***/ }),

/***/ "@emotion/styled":
/*!**********************************!*\
  !*** external "@emotion/styled" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("@emotion/styled");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "swr":
/*!**********************!*\
  !*** external "swr" ***!
  \**********************/
/***/ ((module) => {

module.exports = import("swr");;

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["chakra-node_modules_pnpm_chakra-ui_anatomy_2_3_6_node_modules_chakra-ui_anatomy_dist_esm_c","chakra-node_modules_pnpm_chakra-ui_hooks_2_4_5_react_19_1_0_node_modules_chakra-ui_hooks_dist_esm_i","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-cedb36cc","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27","chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-0e951d65","chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9","chakra-node_modules_pnpm_chakra-ui_theme-","chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_fa_index_mjs-b1cc012-b4687165","icons-node_modules_pnpm_react-icons_5_5_0_react_19_1_0_node_modules_react-icons_f","lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons","pages/admin/guilds-_"], () => (__webpack_exec__("(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Fguilds&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Cguilds.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();