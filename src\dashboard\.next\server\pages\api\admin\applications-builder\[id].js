"use strict";(()=>{var e={};e.id=3262,e.ids=[3262],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},26048:(e,t,r)=>{r.r(t),r.d(t,{config:()=>c,default:()=>l,routeModule:()=>f});var a={};r.r(a),r.d(a,{default:()=>p});var i=r(93433),s=r(20264),o=r(20584),n=r(15806),u=r(94506),d=r(72290);async function p(e,t){let r=await (0,n.getServerSession)(e,t,u.authOptions);if(!r?.user?.id)return t.status(401).json({error:"Unauthorized"});if(!r.user?.isAdmin)return t.status(403).json({error:"Admin access required"});let{id:a}=e.query;if("string"!=typeof a)return t.status(400).json({error:"Invalid application ID"});let i=(await (0,d.L)()).collection("custom_applications");if("GET"===e.method)try{let e=await i.findOne({id:a});if(!e)return t.status(404).json({error:"Application not found"});return t.status(200).json({application:e})}catch(e){return t.status(500).json({error:"Failed to fetch application"})}if("DELETE"===e.method)try{let e=await i.deleteOne({id:a});if(0===e.deletedCount)return t.status(404).json({error:"Application not found"});return t.status(200).json({success:!0,message:"Application deleted successfully"})}catch(e){return t.status(500).json({error:"Failed to delete application"})}if("PUT"===e.method)try{let r={...e.body,id:a,updatedAt:new Date},s=await i.updateOne({id:a},{$set:r});if(0===s.matchedCount)return t.status(404).json({error:"Application not found"});return t.status(200).json({application:r})}catch(e){return t.status(500).json({error:"Failed to update application"})}return t.status(405).json({error:"Method not allowed"})}let l=(0,o.M)(a,"default"),c=(0,o.M)(a,"config"),f=new i.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/admin/applications-builder/[id]",pathname:"/api/admin/applications-builder/[id]",bundlePath:"",filename:""},userland:a})},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(26048));module.exports=a})();