"use strict";(()=>{var e={};e.id=2671,e.ids=[2671],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},50086:(e,t,r)=>{r.r(t),r.d(t,{config:()=>p,default:()=>c,routeModule:()=>h});var s={};r.r(s),r.d(s,{default:()=>l});var o=r(93433),a=r(20264),i=r(20584),n=r(15806),u=r(94506),d=r(98580);async function l(e,t){try{let r=await (0,n.getServerSession)(e,t,u.authOptions);if(!r?.user)return t.status(401).json({error:"Unauthorized"});if(!r.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});let{guildId:s,token:o}=d.dashboardConfig.bot;if(!o||!s)return t.status(500).json({error:"Bot configuration missing"});let{roleId:a}=e.query;if(!a||"string"!=typeof a)return t.status(400).json({error:"Role ID is required"});if("PATCH"===e.method)try{let{color:r,...i}=e.body;r&&(i.color=r=parseInt(r.replace("#",""),16));let n=await fetch(`https://discord.com/api/v10/guilds/${s}/roles/${a}`,{method:"PATCH",headers:{Authorization:`Bot ${o}`,"Content-Type":"application/json"},body:JSON.stringify(i)});if(!n.ok){let e;try{e=await n.json()}catch{e=await n.text()}return t.status(n.status).json(e)}let u=await n.json();return t.status(200).json(u)}catch(e){return t.status(500).json({error:"Failed to update role"})}if("DELETE"===e.method)try{let e=await fetch(`https://discord.com/api/v10/guilds/${s}/roles/${a}`,{method:"DELETE",headers:{Authorization:`Bot ${o}`}});if(!e.ok){let r;try{r=await e.json()}catch{r=await e.text()}return t.status(e.status).json(r)}return t.status(200).json({message:"Role deleted successfully"})}catch(e){return t.status(500).json({error:"Failed to delete role"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let c=(0,i.M)(s,"default"),p=(0,i.M)(s,"config"),h=new o.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/discord/roles/[roleId]",pathname:"/api/discord/roles/[roleId]",bundlePath:"",filename:""},userland:s})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(50086));module.exports=s})();