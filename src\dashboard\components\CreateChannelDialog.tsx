import {
  <PERSON>dal,
  ModalOverlay,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Stack,
  Switch,
  FormHelperText,
  useToast,
  Text,
  Divider,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';

interface Category {
  id: string;
  name: string;
}

interface CreateChannelDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

// Update the channel type mapping
const CHANNEL_TYPES = {
  GUILD_TEXT: 0,
  GUILD_VOICE: 2,
  GUILD_CATEGORY: 4,
} as const;

export default function CreateChannelDialog({ isOpen, onClose, onSuccess }: CreateChannelDialogProps) {
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [channelData, setChannelData] = useState({
    name: '',
    type: 'GUILD_TEXT',
    parent: '',
    topic: '',
    nsfw: false,
    rateLimitPerUser: 0,
    position: 0,
    bitrate: 64000, // For voice channels
    userLimit: 0, // For voice channels
  });

  useEffect(() => {
    // Fetch categories when the modal opens
    if (isOpen) {
      fetchCategories();
    }
  }, [isOpen]);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/discord/channels');
      if (!response.ok) {
        throw new Error('Failed to fetch channels');
      }
      const channels = await response.json();
      // Filter out categories (type 4 or 'category')
      const categoryChannels = channels.filter((channel: any) => {
        // Detect categories regardless of format returned by API
        if (typeof channel.raw_type === 'number') {
          return channel.raw_type === CHANNEL_TYPES.GUILD_CATEGORY;
        }
        // Fallback to string comparison
        return channel.type === 'GUILD_CATEGORY' || channel.type === 'category';
      });
      setCategories(categoryChannels);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch categories',
        status: 'error',
        duration: 3000,
      });
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setChannelData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async () => {
    try {
      setIsLoading(true);

      // Validate channel name
      if (!channelData.name.trim()) {
        toast({
          title: 'Error',
          description: 'Channel name is required',
          status: 'error',
          duration: 3000,
        });
        return;
      }

      // Format channel name (lowercase, no spaces)
      const formattedName = channelData.name.toLowerCase().replace(/\s+/g, '-');

      // Convert channel type to numeric value
      const numericType = CHANNEL_TYPES[channelData.type as keyof typeof CHANNEL_TYPES];

      console.log('Sending channel data:', {
        ...channelData,
        name: formattedName,
        type: numericType,
      });

      const response = await fetch('/api/discord/channels', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...channelData,
          name: formattedName,
          type: numericType,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Failed to create channel. Response:', errorText);
        let errorMessage = 'Failed to create channel';
        try {
          const errorJson = JSON.parse(errorText);
          errorMessage = errorJson.message || errorJson.error || errorMessage;
        } catch (e) {
          // If response isn't JSON, use the raw text
          errorMessage = errorText;
        }
        throw new Error(errorMessage);
      }

      toast({
        title: 'Success',
        description: 'Channel created successfully',
        status: 'success',
        duration: 3000,
      });

      onSuccess?.();
      onClose();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create channel',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setChannelData({
        name: '',
        type: 'GUILD_TEXT',
        parent: '',
        topic: '',
        nsfw: false,
        rateLimitPerUser: 0,
        position: 0,
        bitrate: 64000,
        userLimit: 0,
      });
    }
  }, [isOpen]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay backdropFilter="blur(10px)" />
      <ModalContent bg="gray.800" border="1px" borderColor="blue.500">
        <ModalHeader>Create Channel</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Stack spacing={4}>
            <FormControl isRequired>
              <FormLabel>Channel Name</FormLabel>
              <Input
                placeholder="Enter channel name"
                value={channelData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
              />
              <FormHelperText>
                Channel name will be automatically formatted (lowercase, hyphens instead of spaces)
              </FormHelperText>
            </FormControl>

            <FormControl>
              <FormLabel>Channel Type</FormLabel>
              <Select
                value={channelData.type}
                onChange={(e) => handleInputChange('type', e.target.value)}
              >
                <option value="GUILD_TEXT">Text Channel</option>
                <option value="GUILD_VOICE">Voice Channel</option>
                <option value="GUILD_CATEGORY">Category</option>
              </Select>
            </FormControl>

            {channelData.type !== 'GUILD_CATEGORY' && (
              <FormControl>
                <FormLabel>Parent Category</FormLabel>
                <Select
                  placeholder="Select category"
                  value={channelData.parent}
                  onChange={(e) => handleInputChange('parent', e.target.value)}
                >
                  <option value="">None</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </Select>
              </FormControl>
            )}

            {channelData.type === 'GUILD_TEXT' && (
              <>
                <FormControl>
                  <FormLabel>Channel Topic</FormLabel>
                  <Input
                    placeholder="Enter channel topic"
                    value={channelData.topic}
                    onChange={(e) => handleInputChange('topic', e.target.value)}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Slowmode (seconds)</FormLabel>
                  <NumberInput
                    min={0}
                    max={21600}
                    value={channelData.rateLimitPerUser}
                    onChange={(value) => handleInputChange('rateLimitPerUser', parseInt(value))}
                  >
                    <NumberInputField />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                  <FormHelperText>
                    Set how long users must wait between sending messages (0 to disable)
                  </FormHelperText>
                </FormControl>

                <FormControl display="flex" alignItems="center">
                  <FormLabel mb="0">Age-Restricted (NSFW)</FormLabel>
                  <Switch
                    isChecked={channelData.nsfw}
                    onChange={(e) => handleInputChange('nsfw', e.target.checked)}
                  />
                </FormControl>
              </>
            )}

            {channelData.type === 'GUILD_VOICE' && (
              <>
                <FormControl>
                  <FormLabel>Bitrate (kbps)</FormLabel>
                  <NumberInput
                    min={8}
                    max={96}
                    value={channelData.bitrate / 1000}
                    onChange={(value) => handleInputChange('bitrate', parseInt(value) * 1000)}
                  >
                    <NumberInputField />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                </FormControl>

                <FormControl>
                  <FormLabel>User Limit</FormLabel>
                  <NumberInput
                    min={0}
                    max={99}
                    value={channelData.userLimit}
                    onChange={(value) => handleInputChange('userLimit', parseInt(value))}
                  >
                    <NumberInputField />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                  <FormHelperText>Set to 0 for unlimited users</FormHelperText>
                </FormControl>
              </>
            )}

            <FormControl>
              <FormLabel>Position</FormLabel>
              <NumberInput
                min={0}
                value={channelData.position}
                onChange={(value) => handleInputChange('position', parseInt(value))}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
              <FormHelperText>
                Channel position in the list (0 = top)
              </FormHelperText>
            </FormControl>
          </Stack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            Cancel
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isLoading={isLoading}
            loadingText="Creating..."
          >
            Create Channel
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
} 