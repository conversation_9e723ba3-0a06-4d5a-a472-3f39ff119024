"use strict";(()=>{var e={};e.id=1474,e.ids=[1474],e.modules={224:e=>{e.exports=import("@discordjs/rest")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},33915:e=>{e.exports=import("discord-api-types/v10")},41561:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>p});var a=t(15806),o=t(94506),i=t(224),n=t(33915),u=t(20381),d=e([i,n]);async function p(e,r){if("POST"!==e.method)return r.status(405).json({error:"Method not allowed"});try{if(!await (0,a.getServerSession)(e,r,o.authOptions)){let t=e.headers.authorization;if(!t||!t.startsWith("Bearer "))return r.status(401).json({error:"Unauthorized"})}let{userId:t,reason:s}=e.body;if(!t)return r.status(400).json({error:"Missing userId"});let d=new i.REST({version:"10"}).setToken(u._.DISCORD_BOT_TOKEN),p=u._.DISCORD_GUILD_ID;await d.put(n.Routes.guildBan(p,t),{body:{delete_message_days:1,reason:s||"No reason provided"}}),r.status(200).json({success:!0})}catch(e){r.status(500).json({error:"Failed to ban user"})}}[i,n]=d.then?(await d)():d,s()}catch(e){s(e)}})},54985:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>p,default:()=>d,routeModule:()=>c});var a=t(93433),o=t(20264),i=t(20584),n=t(41561),u=e([n]);n=(u.then?(await u)():u)[0];let d=(0,i.M)(n,"default"),p=(0,i.M)(n,"config"),c=new a.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/discord/users/ban",pathname:"/api/discord/users/ban",bundlePath:"",filename:""},userland:n});s()}catch(e){s(e)}})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>t(54985));module.exports=s})();