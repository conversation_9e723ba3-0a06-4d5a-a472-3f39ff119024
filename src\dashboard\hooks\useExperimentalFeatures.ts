import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

export interface ExperimentalFeaturesStatus {
  hasAccess: boolean;
  reason: string;
  isLoading: boolean;
  isDeveloper: boolean;
  isTester: boolean;
}

export default function useExperimentalFeatures(): ExperimentalFeaturesStatus {
  const { data: session } = useSession();
  const [hasAccess, setHasAccess] = useState(false);
  const [reason, setReason] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function checkAccess() {
      if (!session?.user) {
        setHasAccess(false);
        setReason('unauthenticated');
        setIsLoading(false);
        return;
      }

      try {
        const response = await fetch('/api/discord/user/experimental');
        const data = await response.json();
        
        setHasAccess(data.hasAccess);
        setReason(data.reason);
      } catch (error) {
        console.error('Error checking experimental features access:', error);
        setHasAccess(false);
        setReason('error');
      } finally {
        setIsLoading(false);
      }
    }

    checkAccess();
  }, [session]);

  return {
    hasAccess,
    reason,
    isLoading,
    isDeveloper: reason === 'developer',
    isTester: reason === 'tester'
  };
} 