(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6934],{469:(e,r,o)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/gameservers",function(){return o(86447)}])},28193:()=>{},86447:(e,r,o)=>{"use strict";o.r(r),o.d(r,{__N_SSP:()=>B,default:()=>X});var s=o(94513),a=o(94285),t=o(72468),l=o(81139),n=o(24490),i=o(31840),c=o(29607),d=o(79028),h=o(64349),p=o(1871),g=o(78813),x=o(29484),u=o(22184),b=o(30301),j=o(84748),v=o(5130),m=o(3037),f=o(7836),w=o(84622);o(75632),o(82273);var y=o(99500),C=o(97119),S=o(43700),_=o(52080),E=o(23640),k=o(36468),z=o(42398),A=o(11067),P=o(30994),O=o(35440),F=o(88142),T=o(76338);o(28193),o(29326);let I={minecraft:{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accent:"#68D391"},minecraftbe:{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accent:"#68D391"},csgo:{color:"orange",gradient:{from:"rgba(237, 137, 54, 0.4)",to:"rgba(237, 137, 54, 0.1)"},accent:"#ED8936"},valheim:{color:"red",gradient:{from:"rgba(245, 101, 101, 0.4)",to:"rgba(245, 101, 101, 0.1)"},accent:"#F56565"},rust:{color:"brown",gradient:{from:"rgba(193, 105, 79, 0.4)",to:"rgba(193, 105, 79, 0.1)"},accent:"#C1694F"},arkse:{color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"},accent:"#9F7AEA"},sdtd:{color:"yellow",gradient:{from:"rgba(236, 201, 75, 0.4)",to:"rgba(236, 201, 75, 0.1)"},accent:"#ECC94B"},default:{color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"},accent:"#4299E1"}},R=e=>{var r;let{server:o}=e,[t,l]=(0,a.useState)(!1),{hasCopied:n,onCopy:i}=(0,T.i)(o.hasPassword?"Server: ".concat(o.host,":").concat(o.port,"\nPassword: ").concat(o.password):"".concat(o.host,":").concat(o.port)),c=I[o.type.toLowerCase()]||I.default;(0,F.dU)("gray.800","gray.800");let g=o.online?"".concat(c.color,".400"):"red.400",u=o.online?c.color:"red",b=(null==(r=o.players)?void 0:r.length)||0;return(0,s.jsx)(d.a,{p:5,bg:"linear-gradient(135deg, ".concat(c.gradient.from,", ").concat(c.gradient.to,")"),borderRadius:"xl",border:"2px",borderColor:g,width:"100%",position:"relative",overflow:"hidden",zIndex:1,transition:"all 0.2s",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(45deg, rgba(0,0,0,0.1) 0%, rgba(255,255,255,0.05) 100%)",zIndex:-1},_hover:{transform:"translateY(-2px)",boxShadow:"0 8px 20px ".concat(c.gradient.from),borderColor:o.online?"".concat(c.color,".300"):"red.300"},children:(0,s.jsxs)(m.T,{align:"stretch",spacing:4,children:[(0,s.jsx)(p.z,{justify:"space-between",children:(0,s.jsxs)(m.T,{align:"start",spacing:1,children:[(0,s.jsxs)(p.z,{children:[(0,s.jsx)(v.E,{fontSize:"xl",fontWeight:"bold",color:"white",children:o.name||"".concat(o.host,":").concat(o.port)}),o.hasPassword&&(0,s.jsx)(O.m,{label:"Password Protected",children:(0,s.jsx)("span",{children:(0,s.jsx)(x.I,{as:y.JhU,color:"".concat(c.color,".200")})})})]}),(0,s.jsxs)(p.z,{spacing:2,children:[(0,s.jsx)(S.E,{colorScheme:u,fontSize:"sm",children:o.online?"Online":"Offline"}),(0,s.jsx)(S.E,{colorScheme:c.color,fontSize:"sm",children:o.type.toUpperCase()})]})]})}),o.description&&(0,s.jsx)(v.E,{color:"gray.300",fontSize:"sm",children:o.description}),o.online?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(p.z,{spacing:8,justify:"space-around",children:[(0,s.jsxs)(z.r,{children:[(0,s.jsx)(A.v,{color:"gray.400",children:(0,s.jsxs)(p.z,{children:[(0,s.jsx)(y.x$1,{}),(0,s.jsx)(v.E,{children:"Players"})]})}),(0,s.jsxs)(P.k,{color:"white",children:[b,"/",o.maxPlayers||"?"]})]}),o.map&&(0,s.jsxs)(z.r,{children:[(0,s.jsx)(A.v,{color:"gray.400",children:(0,s.jsxs)(p.z,{children:[(0,s.jsx)(y.pBr,{}),(0,s.jsx)(v.E,{children:"Map"})]})}),(0,s.jsx)(P.k,{color:"white",fontSize:"lg",children:o.map})]}),o.ping&&(0,s.jsxs)(z.r,{children:[(0,s.jsx)(A.v,{color:"gray.400",children:"Ping"}),(0,s.jsxs)(P.k,{color:o.ping<100?"".concat(c.color,".400"):o.ping<200?"yellow.400":"red.400",children:[o.ping,"ms"]})]})]}),(0,s.jsx)(h.$,{size:"sm",variant:"ghost",colorScheme:c.color,onClick:()=>l(!t),leftIcon:(0,s.jsx)(y.__w,{}),children:t?"Hide Details":"Show Details"}),(0,s.jsx)(E.S,{in:t,children:(0,s.jsxs)(m.T,{align:"stretch",spacing:3,pt:2,children:[(0,s.jsx)(k.c,{borderColor:"whiteAlpha.200"}),(0,s.jsxs)(d.a,{children:[(0,s.jsx)(v.E,{color:"gray.400",mb:2,fontWeight:"bold",children:"Connection Information"}),(0,s.jsx)(m.T,{align:"stretch",spacing:2,children:(0,s.jsxs)(p.z,{children:[(0,s.jsxs)(_.C,{p:2,borderRadius:"md",bg:"gray.700",color:"".concat(c.color,".300"),children:[o.host,":",o.port]}),o.hasPassword&&(0,s.jsxs)(_.C,{p:2,borderRadius:"md",bg:"gray.700",color:"".concat(c.color,".300"),children:["Password: ",o.password]}),(0,s.jsx)(O.m,{label:n?"Copied!":"Copy All",children:(0,s.jsx)(h.$,{size:"sm",variant:"ghost",colorScheme:n?"green":c.color,onClick:i,children:(0,s.jsx)(x.I,{as:n?y.CMH:y.paH})})})]})})]}),(0,s.jsxs)(d.a,{children:[(0,s.jsx)(v.E,{color:"gray.400",mb:2,fontWeight:"bold",children:"How to Connect"}),(0,s.jsx)(_.C,{display:"block",whiteSpace:"pre",p:3,borderRadius:"md",bg:"gray.700",color:"".concat(c.color,".300"),children:(()=>{switch(o.type.toLowerCase()){case"minecraft":case"minecraftbe":return'1. Open Minecraft\n2. Click "Multiplayer"\n3. Click "Add Server"\n4. Enter server address: '.concat(o.host,":").concat(o.port).concat(o.hasPassword?"\n5. Enter Password: ".concat(o.password):"");case"sdtd":return'1. Open 7 Days to Die\n2. Click "Join Game"\n3. Click "Server Browser"\n4. Search for "'.concat(o.name,'"\n').concat(o.hasPassword?"5. Enter Password: ".concat(o.password):"");default:return"Connect using: ".concat(o.host,":").concat(o.port).concat(o.hasPassword?"\nPassword: ".concat(o.password):"")}})()})]})]})})]}):(0,s.jsx)(v.E,{color:"red.400",children:o.error||"Server is offline"}),(0,s.jsxs)(p.z,{fontSize:"sm",color:"gray.500",spacing:2,children:[(0,s.jsx)(y.w_X,{}),(0,s.jsxs)(v.E,{children:["Last updated: ",new Date(o.lastUpdated).toLocaleTimeString()]})]})]})})};var D=o(49892),M=o(31862),N=o(15975),J=o(59220),$=o(90066),q=o(46949),U=o(95066),Y=o(53083),L=o(35624),G=o(55206);function H(e){var r;let{isOpen:o,onClose:t,onSave:g,server:x}=e,[u,b]=(0,a.useState)((null==x?void 0:x.name)||""),[w,y]=(0,a.useState)((null==x?void 0:x.description)||""),[C,S]=(0,a.useState)((null==x?void 0:x.host)||""),[_,E]=(0,a.useState)((null==x?void 0:x.port)||25565),[k,z]=(0,a.useState)((null==x?void 0:x.type)||""),[A,P]=(0,a.useState)((null==x?void 0:x.hasPassword)||!1),[O,F]=(0,a.useState)((null==x?void 0:x.password)||""),[T,I]=(0,a.useState)(""),[R,H]=(0,a.useState)([]),[W,B]=(0,a.useState)(!1),[X,K]=(0,a.useState)(!1),Q=(0,f.d)();(0,a.useEffect)(()=>{x&&(b(x.name||""),y(x.description||""),S(x.host),E(x.port),z(x.type),P(x.hasPassword||!1),F(x.password||""),I(""))},[x]),(0,a.useEffect)(()=>{T?V():(H([]),K(!1))},[T]);let V=async()=>{try{B(!0);let e=await fetch("/api/gameservers/games?search=".concat(encodeURIComponent(T)));if(!e.ok)throw Error("Failed to search games");let r=await e.json();H(r),K(!0)}catch(e){Q({title:"Error",description:"Failed to search games",status:"error",duration:5e3,isClosable:!0})}finally{B(!1)}},Z=async()=>{if(!u||!C||!_||!k)return void Q({title:"Error",description:"Please fill in all required fields",status:"error",duration:5e3,isClosable:!0});if(A&&!O)return void Q({title:"Error",description:"Password is required when password protection is enabled",status:"error",duration:5e3,isClosable:!0});try{let e={_id:null==x?void 0:x._id,name:u,description:w,host:C,port:Number(_),type:k,hasPassword:A,password:A?O:void 0};await g(e),t()}catch(e){Q({title:"Error",description:e instanceof Error?e.message:"Failed to save server",status:"error",duration:5e3,isClosable:!0})}},ee=e=>{z(e.id),I(e.name),K(!1)};return(0,s.jsxs)(q.aF,{isOpen:o,onClose:t,size:"lg",children:[(0,s.jsx)(c.m,{backdropFilter:"blur(10px)"}),(0,s.jsxs)(Y.$,{bg:"gray.800",border:"1px",borderColor:"whiteAlpha.200",children:[(0,s.jsx)(i.r,{color:"white",children:x?"Edit Server":"Add Server"}),(0,s.jsx)(U.s,{}),(0,s.jsx)(l.c,{children:(0,s.jsxs)(m.T,{spacing:6,children:[(0,s.jsxs)(M.MJ,{isRequired:!0,children:[(0,s.jsx)(N.l,{color:"gray.200",children:"Server Name"}),(0,s.jsx)(J.p,{value:u,onChange:e=>b(e.target.value),placeholder:"My Game Server",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,s.jsxs)(M.MJ,{children:[(0,s.jsx)(N.l,{color:"gray.200",children:"Description"}),(0,s.jsx)(J.p,{value:w,onChange:e=>y(e.target.value),placeholder:"Optional description",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,s.jsxs)(M.MJ,{isRequired:!0,children:[(0,s.jsx)(N.l,{color:"gray.200",children:"Game Type"}),(0,s.jsx)(J.p,{value:T,onChange:e=>{I(e.target.value),z("")},placeholder:"Search for a game...",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"},onFocus:()=>K(!0)}),W&&(0,s.jsx)(D.s,{justify:"center",mt:2,children:(0,s.jsx)(j.y,{size:"sm",color:"blue.300"})}),X&&R.length>0&&(0,s.jsx)(d.a,{mt:2,border:"1px",borderColor:"whiteAlpha.200",borderRadius:"md",maxH:"200px",overflowY:"auto",bg:"gray.700",children:(0,s.jsx)($.B8,{spacing:0,children:R.map(e=>(0,s.jsx)($.ck,{p:2,cursor:"pointer",_hover:{bg:"whiteAlpha.100"},onClick:()=>ee(e),color:"gray.200",children:(0,s.jsx)(v.E,{children:e.name})},e.id))})}),k&&(0,s.jsxs)(v.E,{mt:1,fontSize:"sm",color:"blue.300",children:["Selected: ",(null==(r=R.find(e=>e.id===k))?void 0:r.name)||k]})]}),(0,s.jsxs)(M.MJ,{isRequired:!0,children:[(0,s.jsx)(N.l,{color:"gray.200",children:"Host"}),(0,s.jsx)(J.p,{value:C,onChange:e=>S(e.target.value),placeholder:"localhost or IP address",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,s.jsxs)(M.MJ,{isRequired:!0,children:[(0,s.jsx)(N.l,{color:"gray.200",children:"Port"}),(0,s.jsx)(L.Q7,{value:_,min:1,max:65535,children:(0,s.jsx)(L.OO,{onChange:e=>E(parseInt(e.target.value)||25565),bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})})]}),(0,s.jsx)(M.MJ,{children:(0,s.jsxs)(p.z,{children:[(0,s.jsx)(N.l,{color:"gray.200",mb:"0",children:"Password Protected"}),(0,s.jsx)(G.d,{colorScheme:"blue",isChecked:A,onChange:e=>P(e.target.checked)})]})}),A&&(0,s.jsxs)(M.MJ,{isRequired:!0,children:[(0,s.jsx)(N.l,{color:"gray.200",children:"Server Password"}),(0,s.jsx)(J.p,{value:O,onChange:e=>F(e.target.value),placeholder:"Enter server password",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]})]})}),(0,s.jsxs)(n.j,{gap:3,children:[(0,s.jsx)(h.$,{variant:"ghost",onClick:t,color:"gray.300",_hover:{bg:"whiteAlpha.100"},children:"Cancel"}),(0,s.jsx)(h.$,{colorScheme:"blue",onClick:Z,_hover:{bg:"blue.500"},_active:{bg:"blue.600"},children:x?"Save Changes":"Add Server"})]})]})]})}var W=o(53424),B=!0;function X(){var e;let{data:r}=(0,W.useSession)(),o=(null==r||null==(e=r.user)?void 0:e.isAdmin)===!0,[S,_]=(0,a.useState)([]),[E,k]=(0,a.useState)(!0),[z,A]=(0,a.useState)(!1),[P,O]=(0,a.useState)(),[F,T]=(0,a.useState)(),{isOpen:I,onOpen:D,onClose:M}=(0,w.j)(),{isOpen:N,onOpen:J,onClose:$}=(0,w.j)(),q=a.useRef(null),U=(0,f.d)(),Y=async()=>{try{A(!0);let e=await fetch("/api/gameservers/query");if(!e.ok)throw Error("Failed to fetch server status");let r=await e.json();_(r)}catch(e){_([]),U({title:"Error",description:e instanceof Error?e.message:"Failed to fetch server status",status:"error",duration:5e3,isClosable:!0})}finally{k(!1),A(!1)}};(0,a.useEffect)(()=>{Y();let e=setInterval(Y,3e4);return()=>clearInterval(e)},[]);let L=async e=>{try{let r,o=await fetch("/api/gameservers/games?type=".concat(encodeURIComponent(e.type)));if(!o.ok)throw Error("Invalid game type");let{type:s}=await o.json();e.type=s;let a=e._id?"PUT":"POST";if("PUT"===a){let{_id:o,...s}=e;r={id:o,...s}}else{let{_id:o,...s}=e;r=s}let t=await fetch("/api/gameservers/manage",{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),l=await t.json();if(!t.ok)throw Error(l.error||"Failed to save server");Y(),U({title:e._id?"Server Updated":"Server Added",description:e._id?"The server has been updated successfully":"The server has been added successfully",status:"success",duration:3e3,isClosable:!0})}catch(e){throw U({title:"Error",description:e instanceof Error?e.message:"Failed to save server",status:"error",duration:5e3,isClosable:!0}),e}},G=e=>{O({_id:e._id,name:e.name,type:e.type,host:e.host,port:e.port,description:e.description,hasPassword:e.hasPassword,password:e.password}),D()},B=e=>{O(e),J()},X=async()=>{if(P)try{let e={id:P._id},r=await fetch("/api/gameservers/manage",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),o=await r.json();if(!r.ok)throw Error(o.error||"Failed to delete server");_(e=>e.filter(e=>e._id!==P._id)),O(void 0),$(),U({title:"Success",description:"Server deleted successfully",status:"success",duration:5e3,isClosable:!0})}catch(e){U({title:"Error",description:e.message||"Failed to delete server",status:"error",duration:5e3,isClosable:!0})}};return(0,s.jsx)(C.A,{children:(0,s.jsxs)(d.a,{w:"full",p:4,children:[(0,s.jsxs)(d.a,{maxW:"4xl",mx:"auto",mb:8,mt:8,bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"green.400",boxShadow:"0 0 15px rgba(72, 187, 120, 0.4)",textAlign:"center",children:[(0,s.jsx)(g.D,{size:"2xl",bgGradient:"linear(to-r, green.300, teal.400)",bgClip:"text",mb:4,children:"Game Servers"}),(0,s.jsx)(v.E,{color:"gray.300",fontSize:"lg",mb:6,children:"Monitor and manage your game servers in real-time"}),(0,s.jsxs)(p.z,{spacing:4,justify:"center",children:[o&&(0,s.jsx)(h.$,{leftIcon:(0,s.jsx)(y.OiG,{}),colorScheme:"green",onClick:()=>{O(void 0),D()},size:"md",variant:"solid",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Add Server"}),(0,s.jsx)(h.$,{leftIcon:(0,s.jsx)(y.DIg,{}),onClick:Y,isLoading:z,loadingText:"Refreshing",size:"md",variant:"outline",colorScheme:"green",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Refresh Status"})]})]}),(0,s.jsx)(d.a,{maxW:"7xl",mx:"auto",children:E?(0,s.jsxs)(m.T,{py:8,bg:"rgba(255,255,255,0.08)",rounded:"2xl",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",children:[(0,s.jsx)(j.y,{size:"xl",color:"green.400"}),(0,s.jsx)(v.E,{color:"gray.400",children:"Loading servers..."})]}):S&&0!==S.length?(0,s.jsx)(b.r,{columns:{base:1,md:2,lg:3},spacing:6,children:(S||[]).map((e,r)=>(0,s.jsxs)(d.a,{position:"relative",transition:"all 0.2s",_hover:o?{transform:"translateY(-4px)","& > .server-actions":{opacity:1,transform:"translateY(0)"}}:void 0,children:[(0,s.jsx)(R,{server:e}),o&&(0,s.jsxs)(p.z,{className:"server-actions",position:"absolute",top:2,right:2,spacing:1,bg:"blackAlpha.800",p:1,borderRadius:"md",opacity:0,transform:"translateY(-4px)",transition:"all 0.2s",zIndex:2,backdropFilter:"blur(8px)",children:[(0,s.jsx)(u.K,{"aria-label":"Edit server",icon:(0,s.jsx)(y.uO9,{}),size:"sm",variant:"ghost",colorScheme:"green",onClick:()=>G(e),_hover:{bg:"green.700"}}),(0,s.jsx)(u.K,{"aria-label":"Delete server",icon:(0,s.jsx)(y.qbC,{}),size:"sm",variant:"ghost",colorScheme:"red",onClick:()=>B(e),_hover:{bg:"red.700"}})]})]},"".concat(e._id||"".concat(e.host,":").concat(e.port,"-").concat(r))))}):(0,s.jsxs)(m.T,{spacing:4,p:8,bg:"rgba(255,255,255,0.08)",rounded:"2xl",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",textAlign:"center",children:[(0,s.jsx)(x.I,{as:y.pBr,boxSize:12,color:"green.400"}),(0,s.jsx)(v.E,{color:"gray.300",fontSize:"lg",children:"No game servers found"}),(0,s.jsx)(v.E,{fontSize:"md",color:"gray.500",children:"Add your first game server to start monitoring"}),o&&(0,s.jsx)(h.$,{leftIcon:(0,s.jsx)(y.OiG,{}),colorScheme:"green",onClick:()=>{O(void 0),D()},size:"md",variant:"outline",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Add Your First Server"})]})}),o&&(0,s.jsx)(H,{isOpen:I,onClose:M,server:P,onSave:L}),(0,s.jsx)(t.Lt,{isOpen:N,leastDestructiveRef:q,onClose:$,isCentered:!0,children:(0,s.jsx)(c.m,{backdropFilter:"blur(10px)",children:(0,s.jsxs)(t.EO,{bg:"gray.800",border:"1px",borderColor:"whiteAlpha.200",children:[(0,s.jsx)(i.r,{fontSize:"lg",fontWeight:"bold",color:"white",children:"Delete Server"}),(0,s.jsxs)(l.c,{color:"gray.300",children:["Are you sure you want to delete ",(null==P?void 0:P.name)||"".concat(null==P?void 0:P.host,":").concat(null==P?void 0:P.port),"? This action cannot be undone."]}),(0,s.jsxs)(n.j,{gap:3,children:[(0,s.jsx)(h.$,{ref:q,onClick:$,variant:"ghost",color:"gray.300",children:"Cancel"}),(0,s.jsx)(h.$,{colorScheme:"red",onClick:X,_hover:{bg:"red.600"},_active:{bg:"red.700"},children:"Delete"})]})]})})})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[2457,9784,6021,3786,1430,9498,2142,1283,5713,6185,4301,9114,7170,2432,1281,3920,3119,9176,1307,727,3976,2774,879,9984,2048,8883,5652,4754,523,7889,8360,8063,9284,5300,1349,7102,6835,246,8255,393,9450,3704,7897,4599,3640,4914,8637,4020,9,5388,4223,636,7398,1203,8792],()=>r(469)),_N_E=e.O()}]);