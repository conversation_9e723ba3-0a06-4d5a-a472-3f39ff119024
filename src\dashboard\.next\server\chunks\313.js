"use strict";exports.id=313,exports.ids=[313],exports.modules={754:(e,i,n)=>{n.d(i,{V:()=>s});let s=[{id:1,scenario:"A user is repeatedly posting invite links to other Discord servers in general chat, despite being warned by other members.",context:"Server Rules: No advertising or self-promotion without permission."},{id:2,scenario:"Two users are having a heated argument about politics in the gaming channel, using increasingly hostile language.",context:"Server Rules: Keep discussions on-topic, no political discussions, maintain respectful communication."},{id:3,scenario:"A member reports that another user is sending them unwanted DMs with inappropriate content.",context:"Server Rules: No harassment, respect privacy, no NSFW content."},{id:4,scenario:"A user is spamming emojis and text across multiple channels simultaneously.",context:"Server Rules: No spamming, maintain channel cleanliness."},{id:5,scenario:"A well-known member is caught using racial slurs in voice chat.",context:"Server Rules: Zero tolerance for hate speech and discrimination."},{id:6,scenario:"A user is sharing what appears to be leaked personal information about another member.",context:"Server Rules: No doxxing, respect privacy, protect personal information."},{id:7,scenario:"Multiple users are organizing a raid on another Discord server.",context:"Server Rules: No organizing or participating in raids, maintain good relations with other communities."},{id:8,scenario:"A user is repeatedly asking for free items/currency in the trading channel.",context:"Server Rules: No begging, follow trading channel guidelines."},{id:9,scenario:"A member is posting links to suspicious websites claiming to offer free Discord Nitro.",context:"Server Rules: No scam links, protect community safety."},{id:10,scenario:"A user is using alternate accounts to bypass a temporary mute.",context:"Server Rules: No ban/mute evasion, respect moderator actions."},{id:11,scenario:"Several users are sharing memes with subtle but inappropriate sexual references in the general chat.",context:"Server Rules: Keep content family-friendly, no NSFW content or innuendos."},{id:12,scenario:"A user is repeatedly mentioning everyone in non-emergency situations.",context:"Server Rules: Don't abuse mentions, respect notification settings."},{id:13,scenario:"A member is threatening self-harm in a public channel.",context:"Server Rules: Take mental health concerns seriously, have protocol for crisis situations."},{id:14,scenario:"Users are sharing copyrighted content (movies/games) in the media channel.",context:"Server Rules: No piracy, respect intellectual property rights."},{id:15,scenario:"A user is roleplaying inappropriately in serious discussion channels.",context:"Server Rules: Keep roleplay in designated channels, respect channel purposes."}]},50313:(e,i,n)=>{n.a(e,async(e,s)=>{try{n.r(i),n.d(i,{default:()=>h});var r=n(8732),t=n(82015),o=n(93580),a=n(754),c=e([o]);o=(c.then?(await c)():c)[0];let l=e=>[...a.V].sort(()=>Math.random()-.5).map((e,i)=>({...e,displayId:i+1})).slice(0,e),d=(0,t.memo)(({scenario:e,response:i,onResponseChange:n})=>(0,r.jsx)(o.Zp,{bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",mb:4,children:(0,r.jsx)(o.bw,{children:(0,r.jsxs)(o.Tk,{align:"stretch",spacing:4,children:[(0,r.jsxs)(o.az,{children:[(0,r.jsxs)(o.EY,{fontWeight:"bold",mb:2,children:["Scenario ",e.displayId,":"]}),(0,r.jsx)(o.EY,{children:e.scenario})]}),(0,r.jsxs)(o.az,{children:[(0,r.jsx)(o.EY,{fontWeight:"bold",color:"blue.300",mb:2,children:"Context:"}),(0,r.jsx)(o.EY,{children:e.context})]}),(0,r.jsxs)(o.az,{children:[(0,r.jsx)(o.EY,{fontWeight:"bold",color:"green.300",mb:2,children:"How would you handle this situation?"}),(0,r.jsx)(o.TM,{value:i,onChange:e=>n(e.target.value),placeholder:"Explain your approach to handling this situation...",minH:"150px",bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"},resize:"vertical"})]})]})})}));d.displayName="ScenarioCard";let h=(0,t.memo)(({onFormChange:e,initialData:i})=>{let[n,s]=(0,t.useState)([]),[a,c]=(0,t.useState)(i||{});(0,t.useEffect)(()=>{s(l(15))},[]),(0,t.useEffect)(()=>{let i=setTimeout(()=>{e(a)},100);return()=>clearTimeout(i)},[a,e]);let h=(0,t.useCallback)((e,i)=>{c(n=>({...n,[e]:i}))},[]),u=Object.keys(a).length/n.length*100;return(0,r.jsxs)(o.Tk,{spacing:6,align:"stretch",position:"relative",children:[(0,r.jsxs)(o.az,{position:"sticky",top:0,bg:"gray.800",p:4,zIndex:1,children:[(0,r.jsx)(o.DZ,{size:"md",mb:2,children:"Moderation Scenarios"}),(0,r.jsx)(o.ke,{value:u,size:"sm",colorScheme:"blue",borderRadius:"full"}),(0,r.jsxs)(o.EY,{mt:2,fontSize:"sm",color:"gray.400",children:[Math.round(u),"% Complete (",Object.keys(a).length," of ",n.length," scenarios)"]})]}),n.map(e=>(0,r.jsx)(d,{scenario:e,response:a[e.id]||"",onResponseChange:i=>h(e.id,i)},e.id))]})});s()}catch(e){s(e)}})},93580:(e,i,n)=>{n.a(e,async(e,s)=>{try{n.d(i,{DZ:()=>a.D,EY:()=>l.E,TM:()=>d.T,Tk:()=>h.T,Zp:()=>t.Z,az:()=>r.a,bw:()=>o.b,ke:()=>c.k});var r=n(45200),t=n(90846),o=n(60615),a=n(30519),c=n(97040),l=n(87378),d=n(37506),h=n(17335),u=e([r,t,o,a,c,l,d,h]);[r,t,o,a,c,l,d,h]=u.then?(await u)():u,s()}catch(e){s(e)}})}};