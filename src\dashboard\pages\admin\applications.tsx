import {
  Box,
  Heading,
  Text,
  HStack,
  VStack,
  Badge,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Stack,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useToast,
  Switch,
  FormControl,
  FormLabel,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Avatar,
  Button,
  Divider,
  List,
  ListItem,
  Icon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Spinner,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import Layout from '../../components/Layout';
import { useState, useEffect } from 'react';
import { FiMoreVertical, FiCheckCircle, FiXCircle, FiClock, FiUser, FiCalendar, FiGlobe, FiClock as FiTime, FiTrash2, FiSettings } from 'react-icons/fi';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../api/auth/[...nextauth]';
import { FaFileAlt, FaFlask } from 'react-icons/fa';

interface Application {
  _id: string;
  userId: string;
  username?: string;
  date: string;
  age: number;
  timezone: string;
  hoursPerWeek: number;
  answers: {
    statement: string;
  };
  quizAnswers: Array<{
    question: string;
    answer: string;
  }>;
  extraInfo?: string;
  status: 'pending' | 'approved' | 'rejected';
}

interface ExperimentalApplication {
  _id: string;
  userId: string;
  username?: string;
  feature: string;
  reason: string;
  timestamp: string;
  status: 'pending' | 'approved' | 'rejected';
}

interface ApplicationStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  recentIncrease: number;
}

export default function Applications() {
  const [applications, setApplications] = useState<Application[]>([]);
  const [experimentalApps, setExperimentalApps] = useState<ExperimentalApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<ApplicationStats>({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    recentIncrease: 0
  });
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedApp, setSelectedApp] = useState<Application | ExperimentalApplication | null>(null);

  useEffect(() => {
    fetchApplications();
    fetchExperimentalApplications();
  }, []);

  const fetchApplications = async () => {
    try {
      const response = await fetch('/api/admin/applications');
      if (response.ok) {
        const data = await response.json();
        setApplications(data.applications || []);
        setStats(data.stats || stats);
      }
    } catch (error) {
      console.error('Failed to fetch applications:', error);
      toast({
        title: 'Error',
        description: 'Failed to load applications',
        status: 'error',
        duration: 3000,
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchExperimentalApplications = async () => {
    try {
      const response = await fetch('/api/admin/applications?type=experimental');
      if (response.ok) {
        const data = await response.json();
        setExperimentalApps((data.applications || []).filter((app: any) => app.type === 'experimental'));
      }
    } catch (error) {
      console.error('Failed to fetch experimental applications:', error);
    }
  };

  const handleApplicationAction = async (appId: string, action: 'approve' | 'reject', type: 'regular' | 'experimental') => {
    try {
      const endpoint = '/api/admin/applications';
      const response = await fetch(`${endpoint}/${appId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action }),
      });

      if (response.ok) {
        if (type === 'regular') {
          fetchApplications();
        } else {
          fetchExperimentalApplications();
        }
        toast({
          title: 'Success',
          description: `Application ${action}d successfully`,
          status: 'success',
          duration: 3000,
        });
        onClose();
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${action} application`,
        status: 'error',
        duration: 3000,
      });
    }
  };

  const viewApplication = (app: Application | ExperimentalApplication) => {
    setSelectedApp(app);
    onOpen();
  };

  if (loading) {
    return (
      <Layout>
        <Box p={8} display="flex" justifyContent="center" alignItems="center" minH="400px">
          <Spinner size="xl" />
        </Box>
      </Layout>
    );
  }

  return (
    <Layout>
      <Box p={8}>
        <VStack align="stretch" spacing={6}>
          <HStack>
            <Icon as={FaFileAlt} boxSize={6} color="blue.500" />
            <Heading size="lg">Applications Management</Heading>
          </HStack>

          <Text color="gray.600" _dark={{ color: 'gray.300' }}>
            Manage and review all user applications including role applications and experimental feature requests.
          </Text>

          {/* Stats Cards */}
          <SimpleGrid columns={{ base: 1, md: 4 }} spacing={6}>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>Total Applications</StatLabel>
                  <StatNumber>{stats.total}</StatNumber>
                  <StatHelpText>
                    <StatArrow type={stats.recentIncrease >= 0 ? 'increase' : 'decrease'} />
                    {Math.abs(stats.recentIncrease)}% this month
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>Pending Review</StatLabel>
                  <StatNumber color="yellow.500">{stats.pending}</StatNumber>
                  <StatHelpText>Requires attention</StatHelpText>
                </Stat>
              </CardBody>
            </Card>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>Approved</StatLabel>
                  <StatNumber color="green.500">{stats.approved}</StatNumber>
                  <StatHelpText>Accepted applications</StatHelpText>
                </Stat>
              </CardBody>
            </Card>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>Rejected</StatLabel>
                  <StatNumber color="red.500">{stats.rejected}</StatNumber>
                  <StatHelpText>Declined applications</StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </SimpleGrid>

          <Tabs colorScheme="blue" isLazy>
            <TabList>
              <Tab>
                <HStack>
                  <Icon as={FaFileAlt} />
                  <Text>Role Applications</Text>
                  {applications.filter(a => a.status === 'pending').length > 0 && (
                    <Badge colorScheme="red">{applications.filter(a => a.status === 'pending').length}</Badge>
                  )}
                </HStack>
              </Tab>
              <Tab>
                <HStack>
                  <Icon as={FaFlask} />
                  <Text>Experimental Requests</Text>
                  {experimentalApps.filter(a => a.status === 'pending').length > 0 && (
                    <Badge colorScheme="yellow">{experimentalApps.filter(a => a.status === 'pending').length}</Badge>
                  )}
                </HStack>
              </Tab>
            </TabList>

            <TabPanels>
              {/* Role Applications Tab */}
              <TabPanel>
                {applications.length === 0 ? (
                  <Alert status="info">
                    <AlertIcon />
                    <AlertDescription>
                      No role applications found. Applications will appear here when users apply for moderator or other roles.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Table variant="simple">
                    <Thead>
                      <Tr>
                        <Th>User</Th>
                        <Th>Application Type</Th>
                        <Th>Submitted</Th>
                        <Th>Status</Th>
                        <Th>Actions</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {applications.map((app) => (
                        <Tr key={app._id}>
                          <Td>
                            <HStack>
                              <Avatar size="sm" name={app.username || app.userId} />
                              <Text>{app.username || app.userId}</Text>
                            </HStack>
                          </Td>
                          <Td>Moderator</Td>
                          <Td>{new Date(app.date).toLocaleDateString()}</Td>
                          <Td>
                            <Badge
                              colorScheme={
                                app.status === 'approved' ? 'green' :
                                app.status === 'rejected' ? 'red' : 'yellow'
                              }
                            >
                              {app.status}
                            </Badge>
                          </Td>
                          <Td>
                            <HStack spacing={2}>
                              <Button size="sm" colorScheme="blue" onClick={() => viewApplication(app)}>
                                View
                              </Button>
                              {app.status === 'pending' && (
                                <>
                                  <Button 
                                    size="sm" 
                                    colorScheme="green" 
                                    onClick={() => handleApplicationAction(app._id, 'approve', 'regular')}
                                  >
                                    Accept
                                  </Button>
                                  <Button 
                                    size="sm" 
                                    colorScheme="red" 
                                    onClick={() => handleApplicationAction(app._id, 'reject', 'regular')}
                                  >
                                    Reject
                                  </Button>
                                </>
                              )}
                            </HStack>
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                )}
              </TabPanel>

              {/* Experimental Requests Tab */}
              <TabPanel>
                {experimentalApps.length === 0 ? (
                  <Alert status="info">
                    <AlertIcon />
                    <AlertDescription>
                      No experimental feature requests found. Requests will appear here when users apply for experimental features access.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Table variant="simple">
                    <Thead>
                      <Tr>
                        <Th>User</Th>
                        <Th>Feature</Th>
                        <Th>Submitted</Th>
                        <Th>Status</Th>
                        <Th>Actions</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {experimentalApps.map((app) => (
                        <Tr key={app._id}>
                          <Td>
                            <HStack>
                              <Avatar size="sm" name={app.username || app.userId} />
                              <Text>{app.username || app.userId}</Text>
                            </HStack>
                          </Td>
                          <Td>
                            <Badge colorScheme="purple">{app.feature}</Badge>
                          </Td>
                          <Td>{new Date(app.timestamp).toLocaleDateString()}</Td>
                          <Td>
                            <Badge
                              colorScheme={
                                app.status === 'approved' ? 'green' :
                                app.status === 'rejected' ? 'red' : 'yellow'
                              }
                            >
                              {app.status}
                            </Badge>
                          </Td>
                          <Td>
                            <HStack spacing={2}>
                              <Button size="sm" colorScheme="blue" onClick={() => viewApplication(app)}>
                                View
                              </Button>
                              {app.status === 'pending' && (
                                <>
                                  <Button 
                                    size="sm" 
                                    colorScheme="green" 
                                    onClick={() => handleApplicationAction(app._id, 'approve', 'experimental')}
                                  >
                                    Accept
                                  </Button>
                                  <Button 
                                    size="sm" 
                                    colorScheme="red" 
                                    onClick={() => handleApplicationAction(app._id, 'reject', 'experimental')}
                                  >
                                    Reject
                                  </Button>
                                </>
                              )}
                            </HStack>
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                )}
              </TabPanel>
            </TabPanels>
          </Tabs>
        </VStack>
      </Box>

      {/* Application Detail Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <HStack>
              <Icon as={selectedApp && 'feature' in selectedApp ? FaFlask : FaFileAlt} />
              <Text>Application Details</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            {selectedApp && (
              <VStack align="stretch" spacing={4}>
                <HStack>
                  <Avatar name={selectedApp.username || selectedApp.userId} />
                  <VStack align="start" spacing={0}>
                    <Text fontWeight="bold">{selectedApp.username || selectedApp.userId}</Text>
                    <Text fontSize="sm" color="gray.500">
                      {'feature' in selectedApp ? 'Experimental Request' : 'Role Application'}
                    </Text>
                  </VStack>
                </HStack>
                
                <Divider />
                
                {'feature' in selectedApp ? (
                  // Experimental Application Details
                  <VStack align="stretch" spacing={3}>
                    <Box>
                      <Text fontWeight="bold">Feature Requested:</Text>
                      <Badge colorScheme="purple">{selectedApp.feature}</Badge>
                    </Box>
                    <Box>
                      <Text fontWeight="bold">Reason:</Text>
                      <Text>{selectedApp.reason}</Text>
                    </Box>
                    <Box>
                      <Text fontWeight="bold">Submitted:</Text>
                      <Text>{new Date(selectedApp.timestamp).toLocaleString()}</Text>
                    </Box>
                  </VStack>
                ) : (
                  // Regular Application Details
                  <VStack align="stretch" spacing={3}>
                    <Box>
                      <Text fontWeight="bold">Age:</Text>
                      <Text>{selectedApp.age} years old</Text>
                    </Box>
                    <Box>
                      <Text fontWeight="bold">Timezone:</Text>
                      <Text>{selectedApp.timezone}</Text>
                    </Box>
                    <Box>
                      <Text fontWeight="bold">Hours per week:</Text>
                      <Text>{selectedApp.hoursPerWeek} hours</Text>
                    </Box>
                    <Box>
                      <Text fontWeight="bold">Motivation:</Text>
                      <Text>{selectedApp.answers?.statement}</Text>
                    </Box>
                    {selectedApp.extraInfo && (
                      <Box>
                        <Text fontWeight="bold">Additional Information:</Text>
                        <Text>{selectedApp.extraInfo}</Text>
                      </Box>
                    )}
                  </VStack>
                )}
              </VStack>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </Layout>
  );
}

// Server-side guard
export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);

  if (!session) {
    return {
      redirect: {
        destination: '/api/auth/signin?callbackUrl=%2Fadmin%2Fapplications',
        permanent: false,
      },
    };
  }

  return { props: { session } };
}; 