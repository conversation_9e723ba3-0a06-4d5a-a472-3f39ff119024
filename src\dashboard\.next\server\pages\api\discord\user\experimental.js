"use strict";(()=>{var e={};e.id=7166,e.ids=[7166],e.modules={12518:e=>{e.exports=require("mongodb")},15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},40378:(e,t,r)=>{r.r(t),r.d(t,{config:()=>b,default:()=>m,routeModule:()=>g});var s={};r.r(s),r.d(s,{default:()=>h});var a=r(93433),n=r(20264),o=r(20584),i=r(15806),d=r(94506),u=r(12518),l=r(98580);let c=null,p=l.dashboardConfig.database?.url||"mongodb://localhost:27017",x=l.dashboardConfig.database?.name||"discord_bot";async function f(){return c||(c=await u.MongoClient.connect(p,{...l.dashboardConfig.database?.options||{}})),c.db(x)}async function h(e,t){let r=await (0,i.getServerSession)(e,t,d.authOptions);if(!r?.user?.id)return t.status(401).json({hasAccess:!1,reason:"unauthenticated"});let s=r.user.id;if("933023999770918932"===s)return t.status(200).json({hasAccess:!0,reason:"developer"});try{let e=await f();if(await e.collection("experimental_testers").findOne({userId:s}))return t.status(200).json({hasAccess:!0,reason:"tester"});let r=await e.collection("experimental_settings").findOne({key:"applications_enabled"}),a=r?.enabled===!0;return t.status(200).json({hasAccess:a,reason:a?"open":"closed"})}catch(e){return t.status(500).json({hasAccess:!1,reason:"error"})}}let m=(0,o.M)(s,"default"),b=(0,o.M)(s,"config"),g=new a.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/discord/user/experimental",pathname:"/api/discord/user/experimental",bundlePath:"",filename:""},userland:s})},65542:e=>{e.exports=require("next-auth")},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>r(40378));module.exports=s})();