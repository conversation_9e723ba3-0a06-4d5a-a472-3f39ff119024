"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/discord/user/experimental";
exports.ids = ["pages/api/discord/user/experimental"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fuser%2Fexperimental&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cuser%5Cexperimental.ts&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fuser%2Fexperimental&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cuser%5Cexperimental.ts&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_discord_user_experimental_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\discord\\user\\experimental.ts */ \"(api-node)/./pages/api/discord/user/experimental.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_user_experimental_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_user_experimental_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/discord/user/experimental\",\n        pathname: \"/api/discord/user/experimental\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_discord_user_experimental_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fuser%2Fexperimental&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cuser%5Cexperimental.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/discord/user/experimental.ts":
/*!************************************************!*\
  !*** ./pages/api/discord/user/experimental.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../core/config */ \"(api-node)/./core/config.ts\");\n\n\n\n\n// Hardcoded developer ID\nconst DEVELOPER_ID = '933023999770918932';\nlet cachedClient = null;\nconst mongoUrl = _core_config__WEBPACK_IMPORTED_MODULE_3__.dashboardConfig.database?.url || 'mongodb://localhost:27017';\nconst dbName = _core_config__WEBPACK_IMPORTED_MODULE_3__.dashboardConfig.database?.name || 'discord_bot';\nasync function getDb() {\n    if (!cachedClient) {\n        cachedClient = await mongodb__WEBPACK_IMPORTED_MODULE_2__.MongoClient.connect(mongoUrl, {\n            ..._core_config__WEBPACK_IMPORTED_MODULE_3__.dashboardConfig.database?.options || {}\n        });\n    }\n    return cachedClient.db(dbName);\n}\nasync function handler(req, res) {\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    if (!session?.user?.id) {\n        return res.status(401).json({\n            hasAccess: false,\n            reason: 'unauthenticated'\n        });\n    }\n    const userId = session.user.id;\n    if (userId === DEVELOPER_ID) {\n        return res.status(200).json({\n            hasAccess: true,\n            reason: 'developer'\n        });\n    }\n    try {\n        const db = await getDb();\n        // Check if user already approved as tester\n        const tester = await db.collection('experimental_testers').findOne({\n            userId\n        });\n        if (tester) {\n            return res.status(200).json({\n                hasAccess: true,\n                reason: 'tester'\n            });\n        }\n        // Check if applications are currently open\n        const setting = await db.collection('experimental_settings').findOne({\n            key: 'applications_enabled'\n        });\n        const applicationsEnabled = setting?.enabled === true;\n        return res.status(200).json({\n            hasAccess: applicationsEnabled,\n            reason: applicationsEnabled ? 'open' : 'closed'\n        });\n    } catch (error) {\n        console.error('Error checking experimental access:', error);\n        return res.status(500).json({\n            hasAccess: false,\n            reason: 'error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/discord/user/experimental.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["lib-node_modules_pnpm_a","lib-node_modules_pnpm_c","lib-node_modules_pnpm_focus-lock_1_3_6_node_modules_focus-lock_dist_es5_c","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-85c92453","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-03c2a040","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-8ab76cb1","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-ac5c196e","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-d32b2f31","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-f169a811","lib-node_modules_pnpm_framer-motion_12_23_1__emot_727bdef267d31ec3b66c9339e42b434d_node_modules_fram-4892e171","lib-node_modules_pnpm_f","lib-node_modules_pnpm_lodash_mergewith_4_6_2_node_modules_lodash_mergewith_index_js-7","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_a","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_f","lib-node_modules_pnpm_motion-dom_12_23_1_node_modules_motion-dom_dist_es_i","lib-node_modules_pnpm_motion-utils_12_23_1_node_modules_motion-utils_dist_es_a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-85a13ecd","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-ebbcd7f4","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-91ea90d8","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-0dd4236a","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-11aef79d","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_dist-494d0338","lib-node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_node_modules_next_d","lib-node_modules_pnpm_o","lib-node_modules_pnpm_r","lib-node_modules_pnpm_t","commons"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fuser%2Fexperimental&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cuser%5Cexperimental.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();