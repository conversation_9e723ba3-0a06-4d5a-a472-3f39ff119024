"use strict";exports.id=3119,exports.ids=[3119],exports.modules={62:(t,e,n)=>{n.d(e,{F:()=>o,e:()=>s});var i=n(87558);function s(t){return{point:{x:t.pageX,y:t.pageY}}}let o=t=>e=>(0,i.Mc)(e)&&t(e,s(e))},1362:(t,e,n)=>{n.d(e,{p:()=>i});let i=t=>Array.isArray(t)},7421:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(82015).createContext)({})},8232:(t,e,n)=>{n(42643),n(65057),n(46315)},9690:(t,e,n)=>{n.d(e,{e:()=>a});var i=n(87558),s=n(62),o=n(21966);function r(t,e,n){let{props:o}=t;t.animationState&&o.whileHover&&t.animationState.setActive("whileHover","Start"===n);let r=o["onHover"+n];r&&i.Gt.postRender(()=>r(e,(0,s.e)(e)))}class a extends(7311==n.j?o.X:null){mount(){let{current:t}=this.node;t&&(this.unmount=(0,i.PT)(t,(t,e)=>(r(this.node,e,"Start"),t=>r(this.node,t,"End"))))}unmount(){}}},11169:(t,e,n)=>{n(8732),n(82015),n(99914),n(19948)},13451:(t,e,n)=>{n.d(e,{N:()=>i});let i=(0,n(82015).createContext)({})},21994:(t,e,n)=>{n.d(e,{f:()=>c});var i=n(87558),s=n(78487);let o=t=>null!==t,r={type:"spring",stiffness:500,damping:25,restSpeed:10},a=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),l={type:"keyframes",duration:.8},u={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},h=(t,{keyframes:e})=>e.length>2?l:i.fu.has(t)?t.startsWith("scale")?a(e[1]):r:u,c=(t,e,n,r={},a,l)=>u=>{let c=(0,i.rU)(r,t)||{},d=c.delay||r.delay||0,{elapsed:p=0}=r;p-=(0,s.fD)(d);let m={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...c,delay:-p,onUpdate:t=>{e.set(t),c.onUpdate&&c.onUpdate(t)},onComplete:()=>{u(),c.onComplete&&c.onComplete()},name:t,motionValue:e,element:l?void 0:a};!function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(c)&&Object.assign(m,h(t,m)),m.duration&&(m.duration=(0,s.fD)(m.duration)),m.repeatDelay&&(m.repeatDelay=(0,s.fD)(m.repeatDelay)),void 0!==m.from&&(m.keyframes[0]=m.from);let f=!1;if(!1!==m.type&&(0!==m.duration||m.repeatDelay)||(m.duration=0,0===m.delay&&(f=!0)),(s.W9.instantAnimations||s.W9.skipAnimations)&&(f=!0,m.duration=0,m.delay=0),m.allowFlatten=!c.type&&!c.ease,f&&!l&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:n="loop"},i){let s=t.filter(o),r=e&&"loop"!==n&&e%2==1?0:s.length-1;return s[r]}(m.keyframes,c);if(void 0!==t)return void i.Gt.update(()=>{m.onUpdate(t),m.onComplete()})}return c.isSync?new i.sb(m):new i.AT(m)}},22695:(t,e,n)=>{n.d(e,{h:()=>o});var i=n(45150),s=n(62);function o(t,e,n,o){return(0,i.k)(t,e,(0,s.F)(n),o)}},22908:(t,e,n)=>{n.d(e,{f:()=>h});var i=n(87558),s=n(78487),o=n(22695),r=n(21966),a=n(15832),l=n(25327);let u=t=>(e,n)=>{t&&i.Gt.postRender(()=>t(e,n))};class h extends r.X{constructor(){super(...arguments),this.removePointerDownListener=s.lQ}onPointerDown(t){this.session=new l.Q(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:(0,a.s)(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:s}=this.node.getProps();return{onSessionStart:u(t),onStart:u(e),onMove:n,onEnd:(t,e)=>{delete this.session,s&&i.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=(0,o.h)(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},22928:(t,e,n)=>{n(42643),n(65057),n(28594)},25327:(t,e,n)=>{n.d(e,{Q:()=>l});var i=n(87558),s=n(78487),o=n(22695),r=n(62),a=n(23622);class l{constructor(t,e,{transformPagePoint:n,contextWindow:l=window,dragSnapToOrigin:h=!1,distanceThreshold:d=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=c(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=(0,a.w)(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!n)return;let{point:s}=t,{timestamp:o}=i.uv;this.history.push({...s,timestamp:o});let{onStart:r,onMove:l}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=u(e,this.transformPagePoint),i.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=c("pointercancel"===t.type?this.lastMoveEventInfo:u(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!(0,i.Mc)(t))return;this.dragSnapToOrigin=h,this.handlers=e,this.transformPagePoint=n,this.distanceThreshold=d,this.contextWindow=l||window;let p=u((0,r.e)(t),this.transformPagePoint),{point:m}=p,{timestamp:f}=i.uv;this.history=[{...m,timestamp:f}];let{onSessionStart:v}=e;v&&v(t,c(p,this.history)),this.removeListeners=(0,s.Fs)((0,o.h)(this.contextWindow,"pointermove",this.handlePointerMove),(0,o.h)(this.contextWindow,"pointerup",this.handlePointerUp),(0,o.h)(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,i.WG)(this.updatePoint)}}function u(t,e){return e?{point:e(t.point)}:t}function h(t,e){return{x:t.x-e.x,y:t.y-e.y}}function c({point:t},e){return{point:t,delta:h(t,d(e)),offset:h(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null,o=d(t);for(;n>=0&&(i=t[n],!(o.timestamp-i.timestamp>(0,s.fD)(.1)));)n--;if(!i)return{x:0,y:0};let r=(0,s.Xu)(o.timestamp-i.timestamp);if(0===r)return{x:0,y:0};let a={x:(o.x-i.x)/r,y:(o.y-i.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function d(t){return t[t.length-1]}},28594:(t,e,n)=>{n(87558),n(78487)},29771:(t,e,n)=>{n(82015),n(73819)},35508:(t,e,n)=>{n.d(e,{c:()=>r});var i=n(78487),s=n(45150),o=n(21966);class r extends o.X{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,i.Fs)((0,s.k)(this.node.current,"focus",()=>this.onFocus()),(0,s.k)(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},36118:(t,e,n)=>{n(42643);class i{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach(n=>{n.start(t.nativeEvent||t,e)})}cancel(){this.componentControls.forEach(t=>{t.cancel()})}stop(){this.componentControls.forEach(t=>{t.stop()})}}},37654:(t,e,n)=>{n.d(e,{$:()=>l});var i=n(87558),s=n(44518),o=n(29239),r=n(92378),a=n(21994);function l(t,e,{delay:n=0,transitionOverride:u,type:h}={}){let{transition:c=t.getDefaultTransition(),transitionEnd:d,...p}=e;u&&(c=u);let m=[],f=h&&t.animationState&&t.animationState.getState()[h];for(let e in p){let s=t.getValue(e,t.latestValues[e]??null),l=p[e];if(void 0===l||f&&function({protectedKeys:t,needsAnimating:e},n){let i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}(f,e))continue;let u={delay:n,...(0,i.rU)(c||{},e)},h=s.get();if(void 0!==h&&!s.isAnimating&&!Array.isArray(l)&&l===h&&!u.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let n=(0,r.P)(t);if(n){let t=window.MotionHandoffAnimation(n,e,i.Gt);null!==t&&(u.startTime=t,d=!0)}}(0,o.g)(t,e),s.start((0,a.f)(e,s,l,t.shouldReduceMotion&&i.$y.has(e)?{type:!1}:u,t,d));let v=s.animation;v&&m.push(v)}return d&&Promise.all(m).then(()=>{i.Gt.update(()=>{d&&(0,s.U)(t,d)})}),m}},42420:(t,e,n)=>{n(8732),n(78487),n(82015),n(42643),n(72190)},44042:(t,e,n)=>{n(78487),n(44518),n(72222)},45150:(t,e,n)=>{n.d(e,{k:()=>i});function i(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}},46315:(t,e,n)=>{var i=n(87558),s=n(78487);function o(t){return"object"==typeof t&&!Array.isArray(t)}let r=t=>"number"==typeof t;var a=n(73093),l=n(37654),u=n(78515),h=n(65111),c=n(29858);function d(t){let e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=(0,i.xZ)(t)&&!(0,i.h1)(t)?new c.l(e):new u.M(e);n.mount(t),a.C.set(t,n)}function p(t){let e=new h.K({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),a.C.set(t,e)}var m=n(78488)},56761:(t,e,n)=>{n(8732),n(82015),n(94520),n(76868),n(42643)},58546:(t,e,n)=>{n.d(e,{w:()=>S});var i=n(21966),s=n(78487),o=n(87558),r=n(21994),a=n(45150),l=n(22695),u=n(62),h=n(320),c=n(2196),d=n(38054),p=n(23194),m=n(61739),f=n(15832),v=n(68965),g=n(29239),y=n(25327);function x(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function P(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}function E(t,e,n){return{min:A(t,e),max:A(t,n)}}function A(t,e){return"number"==typeof t?t:t[e]||0}let C=new WeakMap;class w{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,d.ge)(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:n}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new y.Q(t,{onSessionStart:t=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor((0,u.e)(t).point)},onStart:(t,e)=>{let{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=(0,o.Wp)(n),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),(0,p.X)(t=>{let e=this.getAxisMotionValue(t).get()||0;if(o.rq.test(e)){let{projection:n}=this.visualElement;if(n&&n.layout){let i=n.layout.layoutBox[t];i&&(e=(0,c.CQ)(i)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&o.Gt.postRender(()=>s(t,e)),(0,g.g)(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:r}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}(r),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>(0,p.X)(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,distanceThreshold:n,contextWindow:(0,f.s)(this.visualElement)})}stop(t,e){let n=t||this.latestPointerEvent,i=e||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!i||!n)return;let{velocity:r}=i;this.startAnimation(r);let{onDragEnd:a}=this.getProps();a&&o.Gt.postRender(()=>a(n,i))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){let{drag:i}=this.getProps();if(!n||!M(t,i,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?(0,o.k$)(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?(0,o.k$)(n,t,i.max):Math.min(t,n)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&(0,v.X)(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(t,{top:e,left:n,bottom:i,right:s}){return{x:x(t.x,n,s),y:x(t.y,e,i)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:E(t,"left","right"),y:E(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&(0,p.X)(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!(0,v.X)(e))return!1;let i=e.current;(0,s.V1)(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:o}=this.visualElement;if(!o||!o.layout)return!1;let r=(0,m.L)(i,o.root,this.visualElement.getTransformPagePoint()),a=(t=o.layout.layoutBox,{x:P(t.x,r.x),y:P(t.y,r.y)});if(n){let t=n((0,h.pA)(a));this.hasMutatedConstraints=!!t,t&&(a=(0,h.FY)(t))}return a}startAnimation(t){let{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{};return Promise.all((0,p.X)(r=>{if(!M(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?t[r]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,u)})).then(r)}startAxisValueAnimation(t,e){let n=this.getAxisMotionValue(t);return(0,g.g)(this.visualElement,t),n.start((0,r.f)(t,n,0,e,this.visualElement,!1))}stopAnimation(){(0,p.X)(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){(0,p.X)(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps();return n[e]||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){(0,p.X)(e=>{let{drag:n}=this.getProps();if(!M(e,n,this.currentDirection))return;let{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){let{min:n,max:r}=i.layout.layoutBox[e];s.set(t[e]-(0,o.k$)(n,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!(0,v.X)(e)||!n||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};(0,p.X)(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let n=e.get();i[t]=function(t,e){let n=.5,i=(0,c.CQ)(t),o=(0,c.CQ)(e);return o>i?n=(0,s.qB)(e.min,e.max-i,t.min):i>o&&(n=(0,s.qB)(t.min,t.max-o,e.min)),(0,s.qE)(0,1,n)}({min:n,max:n},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),(0,p.X)(e=>{if(!M(e,t,null))return;let n=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];n.set((0,o.k$)(s,r,i[e]))})}addListeners(){if(!this.visualElement.current)return;C.set(this.visualElement,this);let t=this.visualElement.current,e=(0,l.h)(t,"pointerdown",t=>{let{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),n=()=>{let{dragConstraints:t}=this.getProps();(0,v.X)(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",n);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),o.Gt.read(n);let r=(0,a.k)(window,"resize",()=>this.scalePositionWithinConstraints()),u=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&((0,p.X)(e=>{let n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),e(),s(),u&&u()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=.35,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function M(t,e,n){return(!0===e||e===t)&&(null===n||n===t)}class S extends i.X{constructor(t){super(t),this.removeGroupControls=s.lQ,this.removeListeners=s.lQ,this.controls=new w(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||s.lQ}unmount(){this.removeGroupControls(),this.removeListeners()}}},68015:(t,e,n)=>{n.d(e,{H:()=>a});var i=n(87558),s=n(62),o=n(21966);function r(t,e,n){let{props:o}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&o.whileTap&&t.animationState.setActive("whileTap","Start"===n);let r=o["onTap"+("End"===n?"":n)];r&&i.Gt.postRender(()=>r(e,(0,s.e)(e)))}class a extends(7311==n.j?o.X:null){mount(){let{current:t}=this.node;t&&(this.unmount=(0,i.c$)(t,(t,e)=>(r(this.node,e,"Start"),(t,{success:e})=>r(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},70858:(t,e,n)=>{(0,n(82015).createContext)(null)},72190:(t,e,n)=>{n(8732),n(82015),n(97667),n(70858),n(77731),n(68129)},72222:(t,e,n)=>{n.d(e,{_:()=>a});var i=n(18641),s=n(37654);function o(t,e,n={}){let a=(0,i.K)(t,e,"exit"===n.type?t.presenceContext?.custom:void 0),{transition:l=t.getDefaultTransition()||{}}=a||{};n.transitionOverride&&(l=n.transitionOverride);let u=a?()=>Promise.all((0,s.$)(t,a,n)):()=>Promise.resolve(),h=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:u}=l;return function(t,e,n=0,i=0,s=0,a=1,l){let u=[],h=t.variantChildren.size,c=(h-1)*s,d="function"==typeof i,p=d?t=>i(t,h):1===a?(t=0)=>t*s:(t=0)=>c-t*s;return Array.from(t.variantChildren).sort(r).forEach((t,s)=>{t.notify("AnimationStart",e),u.push(o(t,e,{...l,delay:n+(d?0:i)+p(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(u)}(t,e,i,s,a,u,n)}:()=>Promise.resolve(),{when:c}=l;if(!c)return Promise.all([u(),h(n.delay)]);{let[t,e]="beforeChildren"===c?[u,h]:[h,u];return t().then(()=>e())}}function r(t,e){return t.sortNodePosition(e)}function a(t,e,n={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>o(t,e,n)));else if("string"==typeof e)r=o(t,e,n);else{let o="function"==typeof e?(0,i.K)(t,e,n.custom):e;r=Promise.all((0,s.$)(t,o,n))}return r.then(()=>{t.notify("AnimationComplete",e)})}},73400:(t,e,n)=>{n.d(e,{N:()=>i});function i(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}},73819:(t,e,n)=>{n.d(e,{t:()=>s});var i=n(82015);let s=7311==n.j?(0,i.createContext)(null):null},73912:(t,e,n)=>{n.d(e,{n:()=>i});let i="data-"+(0,n(47302).I)("framerAppearId")},74161:(t,e,n)=>{n(87558),n(78487),n(73912),n(92378);let i=new Set},78488:(t,e,n)=>{n.d(e,{z:()=>o});var i=n(87558),s=n(21994);function o(t,e,n){let o=(0,i.SS)(t)?t:(0,i.OQ)(t);return o.start((0,s.f)("",o,e,n)),o.animation}},80762:(t,e,n)=>{n(82015);var i=n(22580),s=n(38054),o=n(82465);n(42643),n(72222);let r=()=>({});o.B,(0,i.T)({scrapeMotionValuesFromProps:r,createRenderState:r})},83950:(t,e,n)=>{n(82015),n(45150)},88231:(t,e,n)=>{n(8732),n(78487),(0,n(82015).createContext)(null),n(30743),n(42643),n(87558);n(51921),n(10945)},89429:(t,e,n)=>{n(42643),n(69045),n(44042)},91426:(t,e,n)=>{n.d(e,{N:()=>y});var i=n(8732),s=n(82015),o=n(97667),r=n(42643),a=n(69045),l=n(73819),u=n(87558),h=n(94520);class c extends s.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,n=(0,u.$P)(t)&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=n-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d({children:t,isPresent:e,anchorX:n,root:o}){let r=(0,s.useId)(),a=(0,s.useRef)(null),l=(0,s.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,s.useContext)(h.Q);return(0,s.useInsertionEffect)(()=>{let{width:t,height:i,top:s,left:h,right:c}=l.current;if(e||!a.current||!t||!i)return;let d="left"===n?`left: ${h}`:`right: ${c}`;a.current.dataset.motionPopId=r;let p=document.createElement("style");u&&(p.nonce=u);let m=o??document.head;return m.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${t}px !important;
            height: ${i}px !important;
            ${d}px !important;
            top: ${s}px !important;
          }
        `),()=>{m.removeChild(p),m.contains(p)&&m.removeChild(p)}},[e]),(0,i.jsx)(c,{isPresent:e,childRef:a,sizeRef:l,children:s.cloneElement(t,{ref:a})})}let p=({children:t,initial:e,isPresent:n,onExitComplete:o,custom:a,presenceAffectsLayout:u,mode:h,anchorX:c,root:p})=>{let f=(0,r.M)(m),v=(0,s.useId)(),g=!0,y=(0,s.useMemo)(()=>(g=!1,{id:v,initial:e,isPresent:n,custom:a,onExitComplete:t=>{for(let e of(f.set(t,!0),f.values()))if(!e)return;o&&o()},register:t=>(f.set(t,!1),()=>f.delete(t))}),[n,f,o]);return u&&g&&(y={...y}),(0,s.useMemo)(()=>{f.forEach((t,e)=>f.set(e,!1))},[n]),s.useEffect(()=>{n||f.size||!o||o()},[n]),"popLayout"===h&&(t=(0,i.jsx)(d,{isPresent:n,anchorX:c,root:p,children:t})),(0,i.jsx)(l.t.Provider,{value:y,children:t})};function m(){return new Map}var f=n(93030);let v=t=>t.key||"";function g(t){let e=[];return s.Children.forEach(t,t=>{(0,s.isValidElement)(t)&&e.push(t)}),e}let y=({children:t,custom:e,initial:n=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:h="sync",propagate:c=!1,anchorX:d="left",root:m})=>{let[y,x]=(0,f.xQ)(c),P=(0,s.useMemo)(()=>g(t),[t]),E=c&&!y?[]:P.map(v),A=(0,s.useRef)(!0),C=(0,s.useRef)(P),w=(0,r.M)(()=>new Map),[M,S]=(0,s.useState)(P),[D,L]=(0,s.useState)(P);(0,a.E)(()=>{A.current=!1,C.current=P;for(let t=0;t<D.length;t++){let e=v(D[t]);E.includes(e)?w.delete(e):!0!==w.get(e)&&w.set(e,!1)}},[D,E.length,E.join("-")]);let k=[];if(P!==M){let t=[...P];for(let e=0;e<D.length;e++){let n=D[e],i=v(n);E.includes(i)||(t.splice(e,0,n),k.push(n))}return"wait"===h&&k.length&&(t=k),L(g(t)),S(P),null}let{forceRender:b}=(0,s.useContext)(o.L);return(0,i.jsx)(i.Fragment,{children:D.map(t=>{let s=v(t),o=(!c||!!y)&&(P===D||E.includes(s));return(0,i.jsx)(p,{isPresent:o,initial:(!A.current||!!n)&&void 0,custom:e,presenceAffectsLayout:u,mode:h,root:m,onExitComplete:o?void 0:()=>{if(!w.has(s))return;w.set(s,!0);let t=!0;w.forEach(e=>{e||(t=!1)}),t&&(b?.(),L(C.current),c&&x?.(),l&&l())},anchorX:d,children:t},s)})})}},92378:(t,e,n)=>{n.d(e,{P:()=>s});var i=n(73912);function s(t){return t.props[i.n]}},93030:(t,e,n)=>{n.d(e,{tF:()=>r,xQ:()=>o});var i=n(82015),s=n(73819);function o(t=!0){let e=(0,i.useContext)(s.t);if(null===e)return[!0,null];let{isPresent:n,onExitComplete:r,register:a}=e,l=(0,i.useId)();(0,i.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,i.useCallback)(()=>t&&r&&r(l),[l,r,t]);return!n&&r?[!1,u]:[!0]}function r(){var t;return null===(t=(0,i.useContext)(s.t))||t.isPresent}},94520:(t,e,n)=>{n.d(e,{Q:()=>i});let i=(0,n(82015).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},96345:(t,e,n)=>{n.d(e,{z:()=>a});var i=n(82015),s=n(7421),o=n(11395),r=n(85647);function a(t){let{initial:e,animate:n}=function(t,e){if((0,o.e)(t)){let{initial:e,animate:n}=t;return{initial:!1===e||(0,r.w)(e)?e:void 0,animate:(0,r.w)(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,(0,i.useContext)(s.A));return(0,i.useMemo)(()=>({initial:e,animate:n}),[l(e),l(n)])}function l(t){return Array.isArray(t)?t.join(" "):t}},97667:(t,e,n)=>{n.d(e,{L:()=>i});let i=(0,n(82015).createContext)({})},99914:(t,e,n)=>{n.d(e,{Y:()=>i});let i=(0,n(82015).createContext)({strict:!1})}};