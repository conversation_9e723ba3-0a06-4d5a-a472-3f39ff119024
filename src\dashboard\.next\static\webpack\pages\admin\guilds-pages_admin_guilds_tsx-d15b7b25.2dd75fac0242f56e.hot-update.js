"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/guilds-pages_admin_guilds_tsx-d15b7b25",{

/***/ "(pages-dir-browser)/./pages/admin/guilds.tsx":
/*!********************************!*\
  !*** ./pages/admin/guilds.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSP: () => (/* binding */ __N_SSP),\n/* harmony export */   \"default\": () => (/* binding */ ServerManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Box,Button,Card,CardBody,CardHeader,Checkbox,Container,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,SimpleGrid,Skeleton,Spinner,Tab,TabList,TabPanel,TabPanels,Table,Tabs,Tbody,Td,Text,Th,Thead,Tooltip,Tr,VStack,useDisclosure,useToast!=!@chakra-ui/react */ \"(pages-dir-browser)/__barrel_optimize__?names=Badge,Box,Button,Card,CardBody,CardHeader,Checkbox,Container,FormControl,FormLabel,HStack,Heading,Icon,IconButton,Input,SimpleGrid,Skeleton,Spinner,Tab,TabList,TabPanel,TabPanels,Table,Tabs,Tbody,Td,Text,Th,Thead,Tooltip,Tr,VStack,useDisclosure,useToast!=!../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_52dd68cd2d0baeef7a118a3d91f31127/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/Layout */ \"(pages-dir-browser)/./components/Layout.tsx\");\n/* harmony import */ var _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useGuildInfo */ \"(pages-dir-browser)/./hooks/useGuildInfo.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit2,FiFolderPlus,FiHash,FiLock,FiMessageCircle,FiMessageSquare,FiPlus,FiRadio,FiSave,FiServer,FiSettings,FiTool,FiTrash2,FiUsers,FiVolume2,FiZap!=!react-icons/fi */ \"(pages-dir-browser)/__barrel_optimize__?names=FiEdit2,FiFolderPlus,FiHash,FiLock,FiMessageCircle,FiMessageSquare,FiPlus,FiRadio,FiSave,FiServer,FiSettings,FiTool,FiTrash2,FiUsers,FiVolume2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaPalette!=!react-icons/fa */ \"(pages-dir-browser)/__barrel_optimize__?names=FaPalette!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_4__);\n// @ts-nocheck\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Dynamic imports for heavy components\nconst CreateChannelDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_a\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-4215ff4a\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"_pages-dir-browser_components_CreateChannelDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/CreateChannelDialog */ \"(pages-dir-browser)/./components/CreateChannelDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/CreateChannelDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 64,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c = CreateChannelDialog;\nconst EditChannelDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_a\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-25e88711\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-4215ff4a\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_react_2_10_9__em_52dd68cd2d0baeef7a118a3d91f31127_node_modules_ch-e7983c27\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_styled-system_2_12_4_react_19_1_0_node_modules_chakra-ui_styled-s-7f910ba9\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_theme-\"), __webpack_require__.e(\"chakra-node_modules_pnpm_chakra-ui_utils_2_2_5_react_19_1_0_node_modules_chakra-ui_utils_dist_esm_a\"), __webpack_require__.e(\"_pages-dir-browser_components_EditChannelDialog_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../../components/EditChannelDialog */ \"(pages-dir-browser)/./components/EditChannelDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/EditChannelDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 69,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c1 = EditChannelDialog;\nconst EditRoleDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-browser_components_EditRoleDialog_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/EditRoleDialog */ \"(pages-dir-browser)/./components/EditRoleDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/EditRoleDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 74,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c2 = EditRoleDialog;\nconst ColorBuilder = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-browser_components_ColorBuilder_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/ColorBuilder */ \"(pages-dir-browser)/./components/ColorBuilder.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/ColorBuilder\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 79,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c3 = ColorBuilder;\nconst CreateRoleDialog = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-browser_components_CreateRoleDialog_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/CreateRoleDialog */ \"(pages-dir-browser)/./components/CreateRoleDialog.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\guilds.tsx -> \" + \"../../components/CreateRoleDialog\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n            size: \"md\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 84,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c4 = CreateRoleDialog;\n// Rate limiting constants\nconst RATE_LIMIT_MS = 2000; // 2 seconds between operations\nconst BULK_RATE_LIMIT_MS = 5000; // 5 seconds between bulk operations\n// Custom hook for rate limiting\nfunction useRateLimit() {\n    let delay = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : RATE_LIMIT_MS;\n    _s();\n    const [isRateLimited, setIsRateLimited] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const resetRateLimit = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"useRateLimit.useCallback[resetRateLimit]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            setIsRateLimited(true);\n            timeoutRef.current = setTimeout({\n                \"useRateLimit.useCallback[resetRateLimit]\": ()=>{\n                    setIsRateLimited(false);\n                }\n            }[\"useRateLimit.useCallback[resetRateLimit]\"], delay);\n        }\n    }[\"useRateLimit.useCallback[resetRateLimit]\"], [\n        delay\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"useRateLimit.useEffect\": ()=>{\n            return ({\n                \"useRateLimit.useEffect\": ()=>{\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"useRateLimit.useEffect\"];\n        }\n    }[\"useRateLimit.useEffect\"], []);\n    return [\n        isRateLimited,\n        resetRateLimit\n    ];\n}\n_s(useRateLimit, \"C8Tx8E3LpqDtNI/63DsUKsVihwU=\");\nconst CHANNEL_TYPE_CONFIG = {\n    0: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare,\n        color: 'blue',\n        label: 'Text'\n    },\n    2: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiVolume2,\n        color: 'green',\n        label: 'Voice'\n    },\n    4: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiFolderPlus,\n        color: 'purple',\n        label: 'Category'\n    },\n    5: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiRadio,\n        color: 'orange',\n        label: 'Announcement'\n    },\n    11: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageCircle,\n        color: 'cyan',\n        label: 'Public Thread'\n    },\n    12: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiLock,\n        color: 'pink',\n        label: 'Private Thread'\n    },\n    13: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHash,\n        color: 'teal',\n        label: 'Stage Voice'\n    },\n    15: {\n        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHash,\n        color: 'gray',\n        label: 'Forum'\n    }\n};\nconst PERMISSION_BADGES = {\n    ADMINISTRATOR: {\n        color: 'red',\n        label: 'Admin'\n    },\n    MANAGE_GUILD: {\n        color: 'orange',\n        label: 'Manage Server'\n    },\n    MANAGE_ROLES: {\n        color: 'yellow',\n        label: 'Manage Roles'\n    },\n    MANAGE_CHANNELS: {\n        color: 'green',\n        label: 'Manage Channels'\n    },\n    KICK_MEMBERS: {\n        color: 'purple',\n        label: 'Kick'\n    },\n    BAN_MEMBERS: {\n        color: 'pink',\n        label: 'Ban'\n    },\n    MANAGE_MESSAGES: {\n        color: 'blue',\n        label: 'Manage Messages'\n    },\n    MENTION_EVERYONE: {\n        color: 'cyan',\n        label: 'Mention @everyone'\n    }\n};\n// Add this helper map and function after PERMISSION_BADGES constant\nconst PERMISSION_FLAG_BITS = {\n    ADMINISTRATOR: 1n << 3n,\n    MANAGE_GUILD: 1n << 5n,\n    MANAGE_ROLES: 1n << 28n,\n    MANAGE_CHANNELS: 1n << 4n,\n    KICK_MEMBERS: 1n << 1n,\n    BAN_MEMBERS: 1n << 2n,\n    MANAGE_MESSAGES: 1n << 13n,\n    MENTION_EVERYONE: 1n << 17n\n};\nfunction decodePermissions(bitfield) {\n    if (!bitfield) return [];\n    if (Array.isArray(bitfield)) return bitfield;\n    try {\n        const permissions = [];\n        const bits = BigInt(bitfield);\n        for (const [permission, bit] of Object.entries(PERMISSION_FLAG_BITS)){\n            if ((bits & bit) === bit) {\n                permissions.push(permission);\n            }\n        }\n        return permissions;\n    } catch (error) {\n        console.error('Error decoding permissions:', error);\n        return [];\n    }\n}\nvar __N_SSP = true;\nfunction ServerManagement() {\n    _s1();\n    const toast = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const { displayName } = (0,_hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const [isRateLimited, resetRateLimit] = useRateLimit();\n    const [isBulkRateLimited, resetBulkRateLimit] = useRateLimit(BULK_RATE_LIMIT_MS);\n    // State for guild settings\n    const [guildData, setGuildData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        prefix: '!',\n        botName: 'Bot',\n        guildName: '',\n        guildId: '',\n        guildIcon: null,\n        activities: [\n            {\n                type: 'PLAYING',\n                name: 'with Discord.js'\n            }\n        ],\n        activityRotationInterval: 60\n    });\n    // State for roles and channels\n    const [roles, setRoles] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [channels, setChannels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [channelsLoading, setChannelsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Modal states\n    const { isOpen: isCreateChannelOpen, onOpen: onCreateChannelOpen, onClose: onCreateChannelClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isEditChannelOpen, onOpen: onEditChannelOpen, onClose: onEditChannelClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isCreateRoleOpen, onOpen: onCreateRoleOpen, onClose: onCreateRoleClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isEditRoleOpen, onOpen: onEditRoleOpen, onClose: onEditRoleClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    const { isOpen: isColorBuilderOpen, onOpen: onColorBuilderOpen, onClose: onColorBuilderClose } = (0,_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure)();\n    // Selected items for editing\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedChannel, setSelectedChannel] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // Bulk selection state\n    const [selectedRoles, setSelectedRoles] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [selectedChannels, setSelectedChannels] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bulkDeleting, setBulkDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // File upload state\n    const [iconFile, setIconFile] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [iconPreview, setIconPreview] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const handleIconFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            setIconFile(file);\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                return setIconPreview((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result);\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const uploadIcon = async ()=>{\n        if (!iconFile) return;\n        const formData = new FormData();\n        formData.append('icon', iconFile);\n        try {\n            const response = await fetch('/api/discord/settings', {\n                method: 'POST',\n                body: formData\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setGuildData((prev)=>({\n                        ...prev,\n                        guildIcon: data.iconUrl\n                    }));\n                setIconFile(null);\n                setIconPreview(null);\n                toast({\n                    title: 'Success',\n                    description: 'Guild icon updated successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to upload icon',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const fetchGuildData = async ()=>{\n        try {\n            const [guildResponse, rolesResponse] = await Promise.all([\n                fetch('/api/discord/guild'),\n                fetch('/api/discord/roles')\n            ]);\n            if (guildResponse.ok) {\n                const guild = await guildResponse.json();\n                setGuildData((prev)=>({\n                        ...prev,\n                        guildName: guild.name,\n                        guildId: guild.id,\n                        guildIcon: guild.icon,\n                        botName: guild.botName || prev.botName // Use API botName or fallback to current\n                    }));\n            }\n            if (rolesResponse.ok) {\n                const rolesData = await rolesResponse.json();\n                const arr = Array.isArray(rolesData) ? rolesData : rolesData.roles || [];\n                setRoles(arr.sort((a, b)=>b.position - a.position));\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch guild data',\n                status: 'error',\n                duration: 3000\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchChannels = async ()=>{\n        try {\n            const response = await fetch('/api/discord/channels');\n            if (!response.ok) {\n                throw new Error('Failed to fetch channels');\n            }\n            const data = await response.json();\n            const sortedChannels = (data || []).sort((a, b)=>{\n                if (a.type === 4 && b.type !== 4) return -1;\n                if (a.type !== 4 && b.type === 4) return 1;\n                return a.position - b.position;\n            });\n            setChannels(sortedChannels);\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch channels',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setChannelsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"ServerManagement.useEffect\": ()=>{\n            fetchGuildData();\n            fetchChannels();\n        }\n    }[\"ServerManagement.useEffect\"], []);\n    const handleSettingChange = (setting, value)=>{\n        setGuildData((prev)=>({\n                ...prev,\n                [setting]: value\n            }));\n    };\n    const saveSettings = async ()=>{\n        if (saving || isRateLimited) return;\n        setSaving(true);\n        resetRateLimit();\n        try {\n            const response = await fetch('/api/discord/settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(guildData)\n            });\n            if (response.ok) {\n                toast({\n                    title: 'Success',\n                    description: 'Settings saved successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            } else {\n                throw new Error('Failed to save settings');\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to save settings',\n                status: 'error',\n                duration: 3000\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleRoleEdit = (role)=>{\n        setSelectedRole(role);\n        onEditRoleOpen();\n    };\n    const handleChannelEdit = (channel)=>{\n        setSelectedChannel(channel);\n        onEditChannelOpen();\n    };\n    const handleChannelCreate = ()=>{\n        onCreateChannelOpen();\n    };\n    const handleRoleCreate = ()=>{\n        onCreateRoleOpen();\n    };\n    const handleChannelDelete = async (channelId)=>{\n        if (isRateLimited) return;\n        try {\n            resetRateLimit();\n            const response = await fetch(\"/api/discord/channels/\".concat(channelId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchChannels();\n                toast({\n                    title: 'Success',\n                    description: 'Channel deleted successfully',\n                    status: 'success',\n                    duration: 3000\n                });\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to delete channel',\n                status: 'error',\n                duration: 3000\n            });\n        }\n    };\n    const getParentName = (parentId)=>{\n        if (!parentId || !channels) return '-';\n        const parent = channels.find((c)=>c.id === parentId);\n        return parent ? parent.name : '-';\n    };\n    // Bulk delete functions\n    const handleBulkDeleteRoles = async ()=>{\n        if (selectedRoles.length === 0 || bulkDeleting || isBulkRateLimited) return;\n        setBulkDeleting(true);\n        resetBulkRateLimit();\n        try {\n            const response = await fetch('/api/discord/roles/bulk-delete', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    roleIds: selectedRoles\n                })\n            });\n            const result = await response.json();\n            if (response.ok) {\n                toast({\n                    title: 'Success',\n                    description: result.message,\n                    status: 'success',\n                    duration: 5000\n                });\n                setSelectedRoles([]);\n                fetchGuildData(); // Refresh roles\n            } else {\n                throw new Error(result.error || 'Failed to delete roles');\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to delete roles',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setBulkDeleting(false);\n        }\n    };\n    const handleBulkDeleteChannels = async ()=>{\n        if (selectedChannels.length === 0 || bulkDeleting || isBulkRateLimited) return;\n        setBulkDeleting(true);\n        resetBulkRateLimit();\n        try {\n            const response = await fetch('/api/discord/channels/bulk-delete', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    channelIds: selectedChannels\n                })\n            });\n            const result = await response.json();\n            if (response.ok) {\n                toast({\n                    title: 'Success',\n                    description: result.message,\n                    status: 'success',\n                    duration: 5000\n                });\n                setSelectedChannels([]);\n                fetchChannels(); // Refresh channels\n            } else {\n                throw new Error(result.error || 'Failed to delete channels');\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to delete channels',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setBulkDeleting(false);\n        }\n    };\n    // Selection helper functions\n    const toggleRoleSelection = (roleId)=>{\n        setSelectedRoles((prev)=>prev.includes(roleId) ? prev.filter((id)=>id !== roleId) : [\n                ...prev,\n                roleId\n            ]);\n    };\n    const toggleChannelSelection = (channelId)=>{\n        setSelectedChannels((prev)=>prev.includes(channelId) ? prev.filter((id)=>id !== channelId) : [\n                ...prev,\n                channelId\n            ]);\n    };\n    const selectAllRoles = ()=>{\n        const selectableRoles = roles.filter((role)=>role.name !== '@everyone').map((role)=>role.id);\n        setSelectedRoles(selectedRoles.length === selectableRoles.length ? [] : selectableRoles);\n    };\n    const selectAllChannels = ()=>{\n        const channelIds = channels.map((channel)=>channel.id);\n        setSelectedChannels(selectedChannels.length === channelIds.length ? [] : channelIds);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                    spacing: 6,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                            height: \"60px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 563,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                            height: \"400px\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                lineNumber: 561,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 560,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Container, {\n            maxW: \"container.xl\",\n            py: 8,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                    spacing: 8,\n                    align: \"stretch\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            bg: \"rgba(255,255,255,0.08)\",\n                            p: 8,\n                            rounded: \"2xl\",\n                            backdropFilter: \"blur(10px)\",\n                            border: \"2px solid\",\n                            borderColor: \"blue.400\",\n                            boxShadow: \"0 0 15px rgba(66, 153, 225, 0.4)\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                justify: \"space-between\",\n                                align: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                size: \"xl\",\n                                                bgGradient: \"linear(to-r, blue.300, purple.400)\",\n                                                bgClip: \"text\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                        as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiServer,\n                                                        mr: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Server Management\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                color: \"gray.300\",\n                                                mt: 2,\n                                                children: [\n                                                    \"Comprehensive management for \",\n                                                    displayName || guildData.guildName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSave, {}, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 27\n                                        }, void 0),\n                                        colorScheme: \"blue\",\n                                        onClick: saveSettings,\n                                        isLoading: saving,\n                                        isDisabled: isRateLimited,\n                                        size: \"lg\",\n                                        children: \"Save Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                            colorScheme: \"blue\",\n                            isLazy: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"General Settings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPalette,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Theme Builder\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTool,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Builders\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tab, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                    as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap,\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Automation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanels, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 8,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                    size: \"md\",\n                                                                    children: \"Basic Settings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 640,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                                    columns: {\n                                                                        base: 1,\n                                                                        lg: 2\n                                                                    },\n                                                                    spacing: 6,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Bot Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 646,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.botName,\n                                                                                            onChange: (e)=>handleSettingChange('botName', e.target.value),\n                                                                                            placeholder: \"Enter bot name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 647,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 645,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Command Prefix\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 654,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.prefix,\n                                                                                            onChange: (e)=>handleSettingChange('prefix', e.target.value),\n                                                                                            placeholder: \"Enter command prefix\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 655,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 653,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Server Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 664,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.guildName || '',\n                                                                                            isReadOnly: true,\n                                                                                            bg: \"gray.50\",\n                                                                                            _dark: {\n                                                                                                bg: 'gray.700'\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 665,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 663,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormControl, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.FormLabel, {\n                                                                                            children: \"Server ID\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 673,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            value: guildData.guildId || '',\n                                                                                            isReadOnly: true,\n                                                                                            bg: \"gray.50\",\n                                                                                            _dark: {\n                                                                                                bg: 'gray.700'\n                                                                                            },\n                                                                                            fontFamily: \"mono\",\n                                                                                            fontSize: \"sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 674,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 672,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 662,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 643,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                                    size: \"md\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                                            as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiUsers,\n                                                                                            mr: 2\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 694,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        \"Roles (\",\n                                                                                        roles.length,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 693,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPlus, {}, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 699,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        colorScheme: \"green\",\n                                                                                        onClick: handleRoleCreate,\n                                                                                        isDisabled: isRateLimited,\n                                                                                        size: \"sm\",\n                                                                                        children: \"Create Role\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 698,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 697,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedRoles.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            p: 3,\n                                                                            bg: \"blue.50\",\n                                                                            _dark: {\n                                                                                bg: 'blue.900'\n                                                                            },\n                                                                            borderRadius: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    fontWeight: \"medium\",\n                                                                                    children: [\n                                                                                        selectedRoles.length,\n                                                                                        \" role(s) selected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 712,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            variant: \"outline\",\n                                                                                            onClick: ()=>setSelectedRoles([]),\n                                                                                            children: \"Clear Selection\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 716,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            colorScheme: \"red\",\n                                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 726,\n                                                                                                columnNumber: 43\n                                                                                            }, void 0),\n                                                                                            onClick: handleBulkDeleteRoles,\n                                                                                            isLoading: bulkDeleting,\n                                                                                            isDisabled: isBulkRateLimited,\n                                                                                            children: \"Delete Selected\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 723,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 715,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 711,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 3,\n                                                                    children: [\n                                                                        ...Array(3)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                                            height: \"60px\"\n                                                                        }, i, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 740,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                                    overflowX: \"auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                                                        variant: \"simple\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Thead, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                isChecked: selectedRoles.length === roles.filter((r)=>r.name !== '@everyone').length && roles.length > 1,\n                                                                                                isIndeterminate: selectedRoles.length > 0 && selectedRoles.length < roles.filter((r)=>r.name !== '@everyone').length,\n                                                                                                onChange: selectAllRoles\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 751,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 750,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Role\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 757,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Members\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 758,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Permissions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 759,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Actions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 760,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 749,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 748,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tbody, {\n                                                                                children: (roles || []).map((role)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                    isChecked: selectedRoles.includes(role.id),\n                                                                                                    onChange: ()=>toggleRoleSelection(role.id),\n                                                                                                    isDisabled: role.name === '@everyone'\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 767,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 766,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                                                                            w: 4,\n                                                                                                            h: 4,\n                                                                                                            rounded: \"full\",\n                                                                                                            bg: role.color ? \"#\".concat(role.color.toString(16).padStart(6, '0')) : 'gray.500'\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 775,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                            children: role.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 781,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 774,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 773,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                    colorScheme: \"blue\",\n                                                                                                    children: \"0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 785,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 784,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    wrap: \"wrap\",\n                                                                                                    spacing: 1,\n                                                                                                    children: [\n                                                                                                        (decodePermissions(role.permissions) || []).slice(0, 3).map((perm)=>{\n                                                                                                            var _PERMISSION_BADGES_perm, _PERMISSION_BADGES_perm1;\n                                                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                                colorScheme: ((_PERMISSION_BADGES_perm = PERMISSION_BADGES[perm]) === null || _PERMISSION_BADGES_perm === void 0 ? void 0 : _PERMISSION_BADGES_perm.color) || 'gray',\n                                                                                                                size: \"sm\",\n                                                                                                                children: ((_PERMISSION_BADGES_perm1 = PERMISSION_BADGES[perm]) === null || _PERMISSION_BADGES_perm1 === void 0 ? void 0 : _PERMISSION_BADGES_perm1.label) || perm\n                                                                                                            }, perm, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 790,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this);\n                                                                                                        }),\n                                                                                                        decodePermissions(role.permissions).length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                            colorScheme: \"gray\",\n                                                                                                            size: \"sm\",\n                                                                                                            children: [\n                                                                                                                \"+\",\n                                                                                                                decodePermissions(role.permissions).length - 3\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 799,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 788,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 787,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Edit Role\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Edit role\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiEdit2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 810,\n                                                                                                                    columnNumber: 49\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"blue\",\n                                                                                                                onClick: ()=>handleRoleEdit(role),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 808,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 807,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Delete Role\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Delete role\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 821,\n                                                                                                                    columnNumber: 49\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"red\",\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 819,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 818,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 806,\n                                                                                                    columnNumber: 37\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 805,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, role.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 765,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 763,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 747,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 4,\n                                                                    align: \"stretch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                                    size: \"md\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                                            as: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiHash,\n                                                                                            mr: 2\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 845,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        \"Channels (\",\n                                                                                        channels.length,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 844,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPlus, {}, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 850,\n                                                                                            columnNumber: 41\n                                                                                        }, void 0),\n                                                                                        colorScheme: \"blue\",\n                                                                                        onClick: onCreateChannelOpen,\n                                                                                        size: \"sm\",\n                                                                                        children: \"Create Channel\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 849,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 848,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 843,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        selectedChannels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                            justify: \"space-between\",\n                                                                            p: 3,\n                                                                            bg: \"blue.50\",\n                                                                            _dark: {\n                                                                                bg: 'blue.900'\n                                                                            },\n                                                                            borderRadius: \"md\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    fontWeight: \"medium\",\n                                                                                    children: [\n                                                                                        selectedChannels.length,\n                                                                                        \" channel(s) selected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 862,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    spacing: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            variant: \"outline\",\n                                                                                            onClick: ()=>setSelectedChannels([]),\n                                                                                            children: \"Clear Selection\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 866,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                            size: \"sm\",\n                                                                                            colorScheme: \"red\",\n                                                                                            leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 876,\n                                                                                                columnNumber: 43\n                                                                                            }, void 0),\n                                                                                            onClick: handleBulkDeleteChannels,\n                                                                                            isLoading: bulkDeleting,\n                                                                                            isDisabled: isBulkRateLimited,\n                                                                                            children: \"Delete Selected\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 873,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 865,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 861,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                children: channelsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                    spacing: 3,\n                                                                    children: [\n                                                                        ...Array(5)\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                                            height: \"50px\"\n                                                                        }, i, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 892,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 25\n                                                                }, this) : channels.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                    color: \"gray.500\",\n                                                                    textAlign: \"center\",\n                                                                    py: 8,\n                                                                    children: \"No channels found\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 896,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                                    overflowX: \"auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                                                        variant: \"simple\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Thead, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                isChecked: selectedChannels.length === channels.length && channels.length > 0,\n                                                                                                isIndeterminate: selectedChannels.length > 0 && selectedChannels.length < channels.length,\n                                                                                                onChange: selectAllChannels\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 905,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 904,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Name\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 911,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Type\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 912,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Category\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 913,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Position\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 914,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Th, {\n                                                                                            children: \"Actions\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 915,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 903,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 902,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tbody, {\n                                                                                children: (channels || []).map((channel)=>{\n                                                                                    const typeConfig = CHANNEL_TYPE_CONFIG[channel.type] || {\n                                                                                        icon: _barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare,\n                                                                                        color: 'gray',\n                                                                                        label: 'Other'\n                                                                                    };\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tr, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                                                    isChecked: selectedChannels.includes(channel.id),\n                                                                                                    onChange: ()=>toggleChannelSelection(channel.id)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 924,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 923,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Icon, {\n                                                                                                            as: typeConfig.icon,\n                                                                                                            color: \"\".concat(typeConfig.color, \".400\")\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 931,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                            children: channel.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 935,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 930,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 929,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                                    colorScheme: typeConfig.color,\n                                                                                                    children: typeConfig.label\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 939,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 938,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                    color: \"gray.500\",\n                                                                                                    children: getParentName(channel.parent_id)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 942,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 941,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                                    color: \"gray.500\",\n                                                                                                    children: channel.position\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 945,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 944,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Td, {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                                    spacing: 2,\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Edit Channel\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Edit channel\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiEdit2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 952,\n                                                                                                                    columnNumber: 51\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"blue\",\n                                                                                                                onClick: ()=>handleChannelEdit(channel),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 950,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 949,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                                                                                            label: \"Delete Channel\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.IconButton, {\n                                                                                                                \"aria-label\": \"Delete channel\",\n                                                                                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2, {}, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                    lineNumber: 963,\n                                                                                                                    columnNumber: 51\n                                                                                                                }, void 0),\n                                                                                                                size: \"sm\",\n                                                                                                                variant: \"ghost\",\n                                                                                                                colorScheme: \"red\",\n                                                                                                                onClick: ()=>handleChannelDelete(channel.id),\n                                                                                                                isDisabled: isRateLimited\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                                lineNumber: 961,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                            lineNumber: 960,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                    lineNumber: 948,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                                lineNumber: 947,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, channel.id, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 922,\n                                                                                        columnNumber: 35\n                                                                                    }, this);\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 918,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 901,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                    lineNumber: 900,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 888,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"\\uD83C\\uDFA8 Theme Builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Create and customize your own themes with the advanced color builder\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 990,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Custom Theme Builder\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 998,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 997,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Create your own custom themes with full color control\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1002,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaPalette_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPalette, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1006,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"purple\",\n                                                                                    onClick: onColorBuilderOpen,\n                                                                                    size: \"lg\",\n                                                                                    children: \"Open Color Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1005,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1001,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1000,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 996,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Theme Presets\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1019,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1018,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 3,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    mb: 2,\n                                                                                    children: \"Quick theme options available in the navigation bar\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1023,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                                                                                    wrap: \"wrap\",\n                                                                                    spacing: 2,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"blue\",\n                                                                                            children: \"Dark\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1027,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"purple\",\n                                                                                            children: \"Midnight\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1028,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"green\",\n                                                                                            children: \"Forest\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1029,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"orange\",\n                                                                                            children: \"Sunset\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1030,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                            colorScheme: \"pink\",\n                                                                                            children: \"Rose\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                            lineNumber: 1031,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1026,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1022,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1021,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1017,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 995,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 987,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"\\uD83D\\uDEE0️ Builders & Tools\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1044,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Create custom content and manage server features with powerful builders\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1045,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Content Builders\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1053,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1052,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1058,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"green\",\n                                                                                    onClick: ()=>window.open('/admin/experimental/addon-builder', '_blank'),\n                                                                                    children: \"Addon Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1057,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1065,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"blue\",\n                                                                                    onClick: ()=>window.open('/admin/applications-builder', '_blank'),\n                                                                                    children: \"Applications Builder\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1064,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageSquare, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1072,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"purple\",\n                                                                                    onClick: ()=>window.open('/admin/embed-builder', '_blank'),\n                                                                                    isDisabled: true,\n                                                                                    children: \"Message Builder (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1071,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1056,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1055,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1051,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Management Tools\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1085,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1084,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1090,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"orange\",\n                                                                                    onClick: ()=>window.open('/admin/addons', '_blank'),\n                                                                                    children: \"Manage Addons\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1089,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1097,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"teal\",\n                                                                                    onClick: ()=>window.open('/admin/commands', '_blank'),\n                                                                                    children: \"Command Manager\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1096,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1104,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"cyan\",\n                                                                                    onClick: ()=>window.open('/admin/applications', '_blank'),\n                                                                                    children: \"Application Manager\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1103,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1088,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1087,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1083,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1050,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 1041,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.TabPanel, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                spacing: 6,\n                                                align: \"stretch\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                size: \"md\",\n                                                                mb: 4,\n                                                                children: \"⚡ Automation & Activities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1121,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                color: \"gray.600\",\n                                                                _dark: {\n                                                                    color: 'gray.300'\n                                                                },\n                                                                mb: 6,\n                                                                children: \"Set up automated features and server activities\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1122,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.SimpleGrid, {\n                                                        columns: {\n                                                            base: 1,\n                                                            lg: 2\n                                                        },\n                                                        spacing: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Activity Templates\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1130,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1129,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                color: \"gray.500\",\n                                                                                fontSize: \"sm\",\n                                                                                mb: 4,\n                                                                                children: \"Pre-built activity templates to get you started quickly:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 1133,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                                spacing: 2,\n                                                                                align: \"stretch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Event Management System\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1137,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Welcome & Onboarding Flow\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1138,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Moderation Automation\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1139,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Custom Commands\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1140,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Auto-Role Assignment\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1141,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                        fontSize: \"sm\",\n                                                                                        children: \"• Scheduled Messages\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1142,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                lineNumber: 1136,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1132,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1128,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Heading, {\n                                                                            size: \"sm\",\n                                                                            children: \"Automation Settings\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1149,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1148,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.CardBody, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.VStack, {\n                                                                            spacing: 4,\n                                                                            align: \"stretch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                                                    fontSize: \"sm\",\n                                                                                    color: \"gray.500\",\n                                                                                    children: \"Configure automated server features\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1153,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1157,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"yellow\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Auto-Moderation (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1156,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1165,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"green\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Welcome System (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1164,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit2_FiFolderPlus_FiHash_FiLock_FiMessageCircle_FiMessageSquare_FiPlus_FiRadio_FiSave_FiServer_FiSettings_FiTool_FiTrash2_FiUsers_FiVolume2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap, {}, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                        lineNumber: 1173,\n                                                                                        columnNumber: 39\n                                                                                    }, void 0),\n                                                                                    colorScheme: \"blue\",\n                                                                                    variant: \"outline\",\n                                                                                    isDisabled: true,\n                                                                                    children: \"Event Scheduler (Coming Soon)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                                    lineNumber: 1172,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                            lineNumber: 1152,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                        lineNumber: 1151,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                                lineNumber: 1147,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                        lineNumber: 1127,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                                lineNumber: 1119,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                            lineNumber: 1118,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1191,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateChannelDialog, {\n                        isOpen: isCreateChannelOpen,\n                        onClose: onCreateChannelClose,\n                        onSuccess: fetchChannels\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1192,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1191,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1199,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditChannelDialog, {\n                        isOpen: isEditChannelOpen,\n                        onClose: onEditChannelClose,\n                        channel: selectedChannel,\n                        categories: channels.filter((c)=>c.type === 4),\n                        onSuccess: fetchChannels\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1200,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1209,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CreateRoleDialog, {\n                        isOpen: isCreateRoleOpen,\n                        onClose: onCreateRoleClose,\n                        onSuccess: fetchGuildData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1210,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1209,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1217,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditRoleDialog, {\n                        isOpen: isEditRoleOpen,\n                        onClose: onEditRoleClose,\n                        role: selectedRole,\n                        onSuccess: fetchGuildData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1218,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1217,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1226,\n                        columnNumber: 29\n                    }, void 0),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColorBuilder, {\n                        isOpen: isColorBuilderOpen,\n                        onClose: onColorBuilderClose\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                        lineNumber: 1227,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n                    lineNumber: 1226,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n            lineNumber: 573,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\guilds.tsx\",\n        lineNumber: 572,\n        columnNumber: 5\n    }, this);\n}\n_s1(ServerManagement, \"u60QMy3QgXZh7dp9dQ5e+1DUtHw=\", false, function() {\n    return [\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        useRateLimit,\n        useRateLimit,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure,\n        _barrel_optimize_names_Badge_Box_Button_Card_CardBody_CardHeader_Checkbox_Container_FormControl_FormLabel_HStack_Heading_Icon_IconButton_Input_SimpleGrid_Skeleton_Spinner_Tab_TabList_TabPanel_TabPanels_Table_Tabs_Tbody_Td_Text_Th_Thead_Tooltip_Tr_VStack_useDisclosure_useToast_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.useDisclosure\n    ];\n});\n_c5 = ServerManagement;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"CreateChannelDialog\");\n$RefreshReg$(_c1, \"EditChannelDialog\");\n$RefreshReg$(_c2, \"EditRoleDialog\");\n$RefreshReg$(_c3, \"ColorBuilder\");\n$RefreshReg$(_c4, \"CreateRoleDialog\");\n$RefreshReg$(_c5, \"ServerManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL2FkbWluL2d1aWxkcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGNBQWM7OztBQWlEWTtBQUNtQjtBQUNPO0FBQ3VCO0FBSW1JO0FBQ25LO0FBQ1I7QUFHbkMsdUNBQXVDO0FBQ3ZDLE1BQU0yRCxzQkFBc0JELG1EQUFPQSxDQUFDLElBQU0seWlDQUE4Qzs7Ozs7O0lBQ3RGRSxTQUFTLGtCQUFNLDhEQUFDM0IseVVBQU9BO1lBQUM0QixNQUFLOzs7Ozs7SUFDN0JDLEtBQUs7O0tBRkRIO0FBS04sTUFBTUksb0JBQW9CTCxtREFBT0EsQ0FBQyxJQUFNLG1pQ0FBNEM7Ozs7OztJQUNsRkUsU0FBUyxrQkFBTSw4REFBQzNCLHlVQUFPQTtZQUFDNEIsTUFBSzs7Ozs7O0lBQzdCQyxLQUFLOztNQUZEQztBQUtOLE1BQU1DLGlCQUFpQk4sbURBQU9BLENBQUMsSUFBTSwyT0FBeUM7Ozs7OztJQUM1RUUsU0FBUyxrQkFBTSw4REFBQzNCLHlVQUFPQTtZQUFDNEIsTUFBSzs7Ozs7O0lBQzdCQyxLQUFLOztNQUZERTtBQUtOLE1BQU1DLGVBQWVQLG1EQUFPQSxDQUFDLElBQU0scU9BQXVDOzs7Ozs7SUFDeEVFLFNBQVMsa0JBQU0sOERBQUMzQix5VUFBT0E7WUFBQzRCLE1BQUs7Ozs7OztJQUM3QkMsS0FBSzs7TUFGREc7QUFLTixNQUFNQyxtQkFBbUJSLG1EQUFPQSxDQUFDLElBQU0saVBBQTJDOzs7Ozs7SUFDaEZFLFNBQVMsa0JBQU0sOERBQUMzQix5VUFBT0E7WUFBQzRCLE1BQUs7Ozs7OztJQUM3QkMsS0FBSzs7TUFGREk7QUFLTiwwQkFBMEI7QUFDMUIsTUFBTUMsZ0JBQWdCLE1BQU0sK0JBQStCO0FBQzNELE1BQU1DLHFCQUFxQixNQUFNLG9DQUFvQztBQUVyRSxnQ0FBZ0M7QUFDaEMsU0FBU0M7UUFBYUMsUUFBQUEsaUVBQVFIOztJQUM1QixNQUFNLENBQUNJLGVBQWVDLGlCQUFpQixHQUFHcEMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTXFDLGFBQWFsQyw2Q0FBTUEsQ0FBQztJQUUxQixNQUFNbUMsaUJBQWlCcEMsa0RBQVdBO29EQUFDO1lBQ2pDLElBQUltQyxXQUFXRSxPQUFPLEVBQUU7Z0JBQ3RCQyxhQUFhSCxXQUFXRSxPQUFPO1lBQ2pDO1lBQ0FILGlCQUFpQjtZQUNqQkMsV0FBV0UsT0FBTyxHQUFHRTs0REFBVztvQkFDOUJMLGlCQUFpQjtnQkFDbkI7MkRBQUdGO1FBQ0w7bURBQUc7UUFBQ0E7S0FBTTtJQUVWakMsZ0RBQVNBO2tDQUFDO1lBQ1I7MENBQU87b0JBQ0wsSUFBSW9DLFdBQVdFLE9BQU8sRUFBRTt3QkFDdEJDLGFBQWFILFdBQVdFLE9BQU87b0JBQ2pDO2dCQUNGOztRQUNGO2lDQUFHLEVBQUU7SUFFTCxPQUFPO1FBQUNKO1FBQWVHO0tBQWU7QUFDeEM7R0F2QlNMO0FBd0RULE1BQU1TLHNCQUFzQjtJQUMxQixHQUFJO1FBQUVDLE1BQU0vQix1T0FBZUE7UUFBRWdDLE9BQU87UUFBVUMsT0FBTztJQUFPO0lBQzVELEdBQUk7UUFBRUYsTUFBTTlCLGlPQUFTQTtRQUFPK0IsT0FBTztRQUFVQyxPQUFPO0lBQVE7SUFDNUQsR0FBSTtRQUFFRixNQUFNN0Isb09BQVlBO1FBQUk4QixPQUFPO1FBQVVDLE9BQU87SUFBVztJQUMvRCxHQUFJO1FBQUVGLE1BQU01QiwrTkFBT0E7UUFBUzZCLE9BQU87UUFBVUMsT0FBTztJQUFlO0lBQ25FLElBQUk7UUFBRUYsTUFBTTFCLHVPQUFlQTtRQUFDMkIsT0FBTztRQUFVQyxPQUFPO0lBQWdCO0lBQ3BFLElBQUk7UUFBRUYsTUFBTTNCLDhOQUFNQTtRQUFVNEIsT0FBTztRQUFVQyxPQUFPO0lBQWlCO0lBQ3JFLElBQUk7UUFBRUYsTUFBTXBDLDhOQUFNQTtRQUFVcUMsT0FBTztRQUFVQyxPQUFPO0lBQWE7SUFDakUsSUFBSTtRQUFFRixNQUFNcEMsOE5BQU1BO1FBQVVxQyxPQUFPO1FBQVVDLE9BQU87SUFBTztBQUM3RDtBQUVBLE1BQU1DLG9CQUFvQjtJQUN4QkMsZUFBZTtRQUFFSCxPQUFPO1FBQU9DLE9BQU87SUFBUTtJQUM5Q0csY0FBYztRQUFFSixPQUFPO1FBQVVDLE9BQU87SUFBZ0I7SUFDeERJLGNBQWM7UUFBRUwsT0FBTztRQUFVQyxPQUFPO0lBQWU7SUFDdkRLLGlCQUFpQjtRQUFFTixPQUFPO1FBQVNDLE9BQU87SUFBa0I7SUFDNURNLGNBQWM7UUFBRVAsT0FBTztRQUFVQyxPQUFPO0lBQU87SUFDL0NPLGFBQWE7UUFBRVIsT0FBTztRQUFRQyxPQUFPO0lBQU07SUFDM0NRLGlCQUFpQjtRQUFFVCxPQUFPO1FBQVFDLE9BQU87SUFBa0I7SUFDM0RTLGtCQUFrQjtRQUFFVixPQUFPO1FBQVFDLE9BQU87SUFBb0I7QUFDaEU7QUFFQSxvRUFBb0U7QUFDcEUsTUFBTVUsdUJBQXVFO0lBQzNFUixlQUFlLEVBQUUsSUFBSSxFQUFFO0lBQ3ZCQyxjQUFjLEVBQUUsSUFBSSxFQUFFO0lBQ3RCQyxjQUFjLEVBQUUsSUFBSSxHQUFHO0lBQ3ZCQyxpQkFBaUIsRUFBRSxJQUFJLEVBQUU7SUFDekJDLGNBQWMsRUFBRSxJQUFJLEVBQUU7SUFDdEJDLGFBQWEsRUFBRSxJQUFJLEVBQUU7SUFDckJDLGlCQUFpQixFQUFFLElBQUksR0FBRztJQUMxQkMsa0JBQWtCLEVBQUUsSUFBSSxHQUFHO0FBQzdCO0FBRUEsU0FBU0Usa0JBQWtCQyxRQUF5QjtJQUNsRCxJQUFJLENBQUNBLFVBQVUsT0FBTyxFQUFFO0lBQ3hCLElBQUlDLE1BQU1DLE9BQU8sQ0FBQ0YsV0FBVyxPQUFPQTtJQUNwQyxJQUFJO1FBQ0YsTUFBTUcsY0FBa0QsRUFBRTtRQUMxRCxNQUFNQyxPQUFPQyxPQUFPTDtRQUVwQixLQUFLLE1BQU0sQ0FBQ00sWUFBWUMsSUFBSSxJQUFJQyxPQUFPQyxPQUFPLENBQUNYLHNCQUF1QjtZQUNwRSxJQUFJLENBQUNNLE9BQU9HLEdBQUUsTUFBT0EsS0FBSztnQkFDeEJKLFlBQVlPLElBQUksQ0FBQ0o7WUFDbkI7UUFDRjtRQUVBLE9BQU9IO0lBQ1QsRUFBRSxPQUFPUSxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO1FBQzdDLE9BQU8sRUFBRTtJQUNYO0FBQ0Y7O0FBRWUsU0FBU0U7O0lBQ3RCLE1BQU1DLFFBQVFuRyw4VUFBUUE7SUFDdEIsTUFBTSxFQUFFb0csV0FBVyxFQUFFLEdBQUd6RSwrREFBWUE7SUFDcEMsTUFBTSxDQUFDb0MsZUFBZUcsZUFBZSxHQUFHTDtJQUN4QyxNQUFNLENBQUN3QyxtQkFBbUJDLG1CQUFtQixHQUFHekMsYUFBYUQ7SUFFN0QsMkJBQTJCO0lBQzNCLE1BQU0sQ0FBQzJDLFdBQVdDLGFBQWEsR0FBRzVFLCtDQUFRQSxDQUFnQjtRQUN4RDZFLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxZQUFZO1lBQUM7Z0JBQUVDLE1BQU07Z0JBQVdDLE1BQU07WUFBa0I7U0FBRTtRQUMxREMsMEJBQTBCO0lBQzVCO0lBRUEsK0JBQStCO0lBQy9CLE1BQU0sQ0FBQ0MsT0FBT0MsU0FBUyxHQUFHdkYsK0NBQVFBLENBQVMsRUFBRTtJQUM3QyxNQUFNLENBQUN3RixVQUFVQyxZQUFZLEdBQUd6RiwrQ0FBUUEsQ0FBWSxFQUFFO0lBQ3RELE1BQU0sQ0FBQ3dCLFNBQVNrRSxXQUFXLEdBQUcxRiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUMyRixpQkFBaUJDLG1CQUFtQixHQUFHNUYsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDNkYsUUFBUUMsVUFBVSxHQUFHOUYsK0NBQVFBLENBQUM7SUFFckMsZUFBZTtJQUNmLE1BQU0sRUFBRStGLFFBQVFDLG1CQUFtQixFQUFFQyxRQUFRQyxtQkFBbUIsRUFBRUMsU0FBU0Msb0JBQW9CLEVBQUUsR0FBR3RILG1WQUFhQTtJQUNqSCxNQUFNLEVBQUVpSCxRQUFRTSxpQkFBaUIsRUFBRUosUUFBUUssaUJBQWlCLEVBQUVILFNBQVNJLGtCQUFrQixFQUFFLEdBQUd6SCxtVkFBYUE7SUFDM0csTUFBTSxFQUFFaUgsUUFBUVMsZ0JBQWdCLEVBQUVQLFFBQVFRLGdCQUFnQixFQUFFTixTQUFTTyxpQkFBaUIsRUFBRSxHQUFHNUgsbVZBQWFBO0lBQ3hHLE1BQU0sRUFBRWlILFFBQVFZLGNBQWMsRUFBRVYsUUFBUVcsY0FBYyxFQUFFVCxTQUFTVSxlQUFlLEVBQUUsR0FBRy9ILG1WQUFhQTtJQUNsRyxNQUFNLEVBQUVpSCxRQUFRZSxrQkFBa0IsRUFBRWIsUUFBUWMsa0JBQWtCLEVBQUVaLFNBQVNhLG1CQUFtQixFQUFFLEdBQUdsSSxtVkFBYUE7SUFFOUcsNkJBQTZCO0lBQzdCLE1BQU0sQ0FBQ21JLGNBQWNDLGdCQUFnQixHQUFHbEgsK0NBQVFBLENBQWM7SUFDOUQsTUFBTSxDQUFDbUgsaUJBQWlCQyxtQkFBbUIsR0FBR3BILCtDQUFRQSxDQUFpQjtJQUV2RSx1QkFBdUI7SUFDdkIsTUFBTSxDQUFDcUgsZUFBZUMsaUJBQWlCLEdBQUd0SCwrQ0FBUUEsQ0FBVyxFQUFFO0lBQy9ELE1BQU0sQ0FBQ3VILGtCQUFrQkMsb0JBQW9CLEdBQUd4SCwrQ0FBUUEsQ0FBVyxFQUFFO0lBQ3JFLE1BQU0sQ0FBQ3lILGNBQWNDLGdCQUFnQixHQUFHMUgsK0NBQVFBLENBQUM7SUFFakQsb0JBQW9CO0lBQ3BCLE1BQU0sQ0FBQzJILFVBQVVDLFlBQVksR0FBRzVILCtDQUFRQSxDQUFjO0lBQ3RELE1BQU0sQ0FBQzZILGFBQWFDLGVBQWUsR0FBRzlILCtDQUFRQSxDQUFnQjtJQUM5RCxNQUFNK0gsZUFBZTVILDZDQUFNQSxDQUFtQjtJQUU5QyxNQUFNNkgsdUJBQXVCLENBQUNDO1lBQ2ZBO1FBQWIsTUFBTUMsUUFBT0Qsa0JBQUFBLEVBQUVFLE1BQU0sQ0FBQ0MsS0FBSyxjQUFkSCxzQ0FBQUEsZUFBZ0IsQ0FBQyxFQUFFO1FBQ2hDLElBQUlDLE1BQU07WUFDUk4sWUFBWU07WUFDWixNQUFNRyxTQUFTLElBQUlDO1lBQ25CRCxPQUFPRSxNQUFNLEdBQUcsQ0FBQ047b0JBQXFCQTt1QkFBZkgsZ0JBQWVHLFlBQUFBLEVBQUVFLE1BQU0sY0FBUkYsZ0NBQUFBLFVBQVVPLE1BQU07O1lBQ3RESCxPQUFPSSxhQUFhLENBQUNQO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNUSxhQUFhO1FBQ2pCLElBQUksQ0FBQ2YsVUFBVTtRQUVmLE1BQU1nQixXQUFXLElBQUlDO1FBQ3JCRCxTQUFTRSxNQUFNLENBQUMsUUFBUWxCO1FBRXhCLElBQUk7WUFDRixNQUFNbUIsV0FBVyxNQUFNQyxNQUFNLHlCQUF5QjtnQkFDcERDLFFBQVE7Z0JBQ1JDLE1BQU1OO1lBQ1I7WUFFQSxJQUFJRyxTQUFTSSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNTCxTQUFTTSxJQUFJO2dCQUNoQ3hFLGFBQWF5RSxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUVwRSxXQUFXa0UsS0FBS0csT0FBTztvQkFBQztnQkFDekQxQixZQUFZO2dCQUNaRSxlQUFlO2dCQUNmdkQsTUFBTTtvQkFDSmdGLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLFFBQVE7b0JBQ1JDLFVBQVU7Z0JBQ1o7WUFDRjtRQUNGLEVBQUUsT0FBT3RGLE9BQU87WUFDZEcsTUFBTTtnQkFDSmdGLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFFBQVE7Z0JBQ1JDLFVBQVU7WUFDWjtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxpQkFBaUI7UUFDckIsSUFBSTtZQUNGLE1BQU0sQ0FBQ0MsZUFBZUMsY0FBYyxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztnQkFDdkRoQixNQUFNO2dCQUNOQSxNQUFNO2FBQ1A7WUFFRCxJQUFJYSxjQUFjVixFQUFFLEVBQUU7Z0JBQ3BCLE1BQU1jLFFBQVEsTUFBTUosY0FBY1IsSUFBSTtnQkFDdEN4RSxhQUFheUUsQ0FBQUEsT0FBUzt3QkFDcEIsR0FBR0EsSUFBSTt3QkFDUHRFLFdBQVdpRixNQUFNNUUsSUFBSTt3QkFDckJKLFNBQVNnRixNQUFNQyxFQUFFO3dCQUNqQmhGLFdBQVcrRSxNQUFNckgsSUFBSTt3QkFDckJtQyxTQUFTa0YsTUFBTWxGLE9BQU8sSUFBSXVFLEtBQUt2RSxPQUFPLENBQUMseUNBQXlDO29CQUNsRjtZQUNGO1lBRUEsSUFBSStFLGNBQWNYLEVBQUUsRUFBRTtnQkFDcEIsTUFBTWdCLFlBQVksTUFBTUwsY0FBY1QsSUFBSTtnQkFDMUMsTUFBTWUsTUFBTXpHLE1BQU1DLE9BQU8sQ0FBQ3VHLGFBQWFBLFlBQVlBLFVBQVU1RSxLQUFLLElBQUksRUFBRTtnQkFDeEVDLFNBQVM0RSxJQUFJQyxJQUFJLENBQUMsQ0FBQ0MsR0FBU0MsSUFBWUEsRUFBRUMsUUFBUSxHQUFHRixFQUFFRSxRQUFRO1lBQ2pFO1FBQ0YsRUFBRSxPQUFPbkcsT0FBTztZQUNkRyxNQUFNO2dCQUNKZ0YsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsUUFBUTtnQkFDUkMsVUFBVTtZQUNaO1FBQ0YsU0FBVTtZQUNSaEUsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNOEUsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRixNQUFNMUIsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLElBQUksQ0FBQ0QsU0FBU0ksRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUl1QixNQUFNO1lBQ2xCO1lBQ0EsTUFBTXRCLE9BQU8sTUFBTUwsU0FBU00sSUFBSTtZQUNoQyxNQUFNc0IsaUJBQWlCLENBQUN2QixRQUFRLEVBQUUsRUFBRWlCLElBQUksQ0FBQyxDQUFDQyxHQUFZQztnQkFDcEQsSUFBSUQsRUFBRWxGLElBQUksS0FBSyxLQUFLbUYsRUFBRW5GLElBQUksS0FBSyxHQUFHLE9BQU8sQ0FBQztnQkFDMUMsSUFBSWtGLEVBQUVsRixJQUFJLEtBQUssS0FBS21GLEVBQUVuRixJQUFJLEtBQUssR0FBRyxPQUFPO2dCQUN6QyxPQUFPa0YsRUFBRUUsUUFBUSxHQUFHRCxFQUFFQyxRQUFRO1lBQ2hDO1lBQ0E5RSxZQUFZaUY7UUFDZCxFQUFFLE9BQU90RyxPQUFPO1lBQ2RHLE1BQU07Z0JBQ0pnRixPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxRQUFRO2dCQUNSQyxVQUFVO1lBQ1o7UUFDRixTQUFVO1lBQ1I5RCxtQkFBbUI7UUFDckI7SUFDRjtJQUVBM0YsZ0RBQVNBO3NDQUFDO1lBQ1IwSjtZQUNBYTtRQUNGO3FDQUFHLEVBQUU7SUFFTCxNQUFNRyxzQkFBc0IsQ0FBQ0MsU0FBOEJDO1FBQ3pEakcsYUFBYXlFLENBQUFBLE9BQVM7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRSxDQUFDdUIsUUFBUSxFQUFFQztZQUFNO0lBQ3BEO0lBRUEsTUFBTUMsZUFBZTtRQUNuQixJQUFJakYsVUFBVTFELGVBQWU7UUFFN0IyRCxVQUFVO1FBQ1Z4RDtRQUVBLElBQUk7WUFDRixNQUFNd0csV0FBVyxNQUFNQyxNQUFNLHlCQUF5QjtnQkFDcERDLFFBQVE7Z0JBQ1IrQixTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDOUIsTUFBTStCLEtBQUtDLFNBQVMsQ0FBQ3RHO1lBQ3ZCO1lBRUEsSUFBSW1FLFNBQVNJLEVBQUUsRUFBRTtnQkFDZjNFLE1BQU07b0JBQ0pnRixPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxRQUFRO29CQUNSQyxVQUFVO2dCQUNaO1lBQ0YsT0FBTztnQkFDTCxNQUFNLElBQUllLE1BQU07WUFDbEI7UUFDRixFQUFFLE9BQU9yRyxPQUFPO1lBQ2RHLE1BQU07Z0JBQ0pnRixPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxRQUFRO2dCQUNSQyxVQUFVO1lBQ1o7UUFDRixTQUFVO1lBQ1I1RCxVQUFVO1FBQ1o7SUFDRjtJQUVBLE1BQU1vRixpQkFBaUIsQ0FBQ0M7UUFDdEJqRSxnQkFBZ0JpRTtRQUNoQnZFO0lBQ0Y7SUFFQSxNQUFNd0Usb0JBQW9CLENBQUNDO1FBQ3pCakUsbUJBQW1CaUU7UUFDbkIvRTtJQUNGO0lBRUEsTUFBTWdGLHNCQUFzQjtRQUMxQnBGO0lBQ0Y7SUFFQSxNQUFNcUYsbUJBQW1CO1FBQ3ZCOUU7SUFDRjtJQUVBLE1BQU0rRSxzQkFBc0IsT0FBT0M7UUFDakMsSUFBSXRKLGVBQWU7UUFFbkIsSUFBSTtZQUNGRztZQUNBLE1BQU13RyxXQUFXLE1BQU1DLE1BQU0seUJBQW1DLE9BQVYwQyxZQUFhO2dCQUNqRXpDLFFBQVE7WUFDVjtZQUVBLElBQUlGLFNBQVNJLEVBQUUsRUFBRTtnQkFDZixNQUFNc0I7Z0JBQ05qRyxNQUFNO29CQUNKZ0YsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsUUFBUTtvQkFDUkMsVUFBVTtnQkFDWjtZQUNGO1FBQ0YsRUFBRSxPQUFPdEYsT0FBTztZQUNkRyxNQUFNO2dCQUNKZ0YsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsUUFBUTtnQkFDUkMsVUFBVTtZQUNaO1FBQ0Y7SUFDRjtJQUVBLE1BQU1nQyxnQkFBZ0IsQ0FBQ0M7UUFDckIsSUFBSSxDQUFDQSxZQUFZLENBQUNuRyxVQUFVLE9BQU87UUFDbkMsTUFBTW9HLFNBQVNwRyxTQUFTcUcsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFN0IsRUFBRSxLQUFLMEI7UUFDM0MsT0FBT0MsU0FBU0EsT0FBT3hHLElBQUksR0FBRztJQUNoQztJQUVBLHdCQUF3QjtJQUN4QixNQUFNMkcsd0JBQXdCO1FBQzVCLElBQUkxRSxjQUFjMkUsTUFBTSxLQUFLLEtBQUt2RSxnQkFBZ0JoRCxtQkFBbUI7UUFFckVpRCxnQkFBZ0I7UUFDaEJoRDtRQUVBLElBQUk7WUFDRixNQUFNb0UsV0FBVyxNQUFNQyxNQUFNLGtDQUFrQztnQkFDN0RDLFFBQVE7Z0JBQ1IrQixTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0E5QixNQUFNK0IsS0FBS0MsU0FBUyxDQUFDO29CQUFFZ0IsU0FBUzVFO2dCQUFjO1lBQ2hEO1lBRUEsTUFBTW1CLFNBQVMsTUFBTU0sU0FBU00sSUFBSTtZQUVsQyxJQUFJTixTQUFTSSxFQUFFLEVBQUU7Z0JBQ2YzRSxNQUFNO29CQUNKZ0YsT0FBTztvQkFDUEMsYUFBYWhCLE9BQU8wRCxPQUFPO29CQUMzQnpDLFFBQVE7b0JBQ1JDLFVBQVU7Z0JBQ1o7Z0JBQ0FwQyxpQkFBaUIsRUFBRTtnQkFDbkJxQyxrQkFBa0IsZ0JBQWdCO1lBQ3BDLE9BQU87Z0JBQ0wsTUFBTSxJQUFJYyxNQUFNakMsT0FBT3BFLEtBQUssSUFBSTtZQUNsQztRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkRyxNQUFNO2dCQUNKZ0YsT0FBTztnQkFDUEMsYUFBYXBGLE1BQU04SCxPQUFPLElBQUk7Z0JBQzlCekMsUUFBUTtnQkFDUkMsVUFBVTtZQUNaO1FBQ0YsU0FBVTtZQUNSaEMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNeUUsMkJBQTJCO1FBQy9CLElBQUk1RSxpQkFBaUJ5RSxNQUFNLEtBQUssS0FBS3ZFLGdCQUFnQmhELG1CQUFtQjtRQUV4RWlELGdCQUFnQjtRQUNoQmhEO1FBRUEsSUFBSTtZQUNGLE1BQU1vRSxXQUFXLE1BQU1DLE1BQU0scUNBQXFDO2dCQUNoRUMsUUFBUTtnQkFDUitCLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQTlCLE1BQU0rQixLQUFLQyxTQUFTLENBQUM7b0JBQUVtQixZQUFZN0U7Z0JBQWlCO1lBQ3REO1lBRUEsTUFBTWlCLFNBQVMsTUFBTU0sU0FBU00sSUFBSTtZQUVsQyxJQUFJTixTQUFTSSxFQUFFLEVBQUU7Z0JBQ2YzRSxNQUFNO29CQUNKZ0YsT0FBTztvQkFDUEMsYUFBYWhCLE9BQU8wRCxPQUFPO29CQUMzQnpDLFFBQVE7b0JBQ1JDLFVBQVU7Z0JBQ1o7Z0JBQ0FsQyxvQkFBb0IsRUFBRTtnQkFDdEJnRCxpQkFBaUIsbUJBQW1CO1lBQ3RDLE9BQU87Z0JBQ0wsTUFBTSxJQUFJQyxNQUFNakMsT0FBT3BFLEtBQUssSUFBSTtZQUNsQztRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkRyxNQUFNO2dCQUNKZ0YsT0FBTztnQkFDUEMsYUFBYXBGLE1BQU04SCxPQUFPLElBQUk7Z0JBQzlCekMsUUFBUTtnQkFDUkMsVUFBVTtZQUNaO1FBQ0YsU0FBVTtZQUNSaEMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTTJFLHNCQUFzQixDQUFDQztRQUMzQmhGLGlCQUFpQitCLENBQUFBLE9BQ2ZBLEtBQUtrRCxRQUFRLENBQUNELFVBQ1ZqRCxLQUFLbUQsTUFBTSxDQUFDdkMsQ0FBQUEsS0FBTUEsT0FBT3FDLFVBQ3pCO21CQUFJakQ7Z0JBQU1pRDthQUFPO0lBRXpCO0lBRUEsTUFBTUcseUJBQXlCLENBQUNoQjtRQUM5QmpFLG9CQUFvQjZCLENBQUFBLE9BQ2xCQSxLQUFLa0QsUUFBUSxDQUFDZCxhQUNWcEMsS0FBS21ELE1BQU0sQ0FBQ3ZDLENBQUFBLEtBQU1BLE9BQU93QixhQUN6QjttQkFBSXBDO2dCQUFNb0M7YUFBVTtJQUU1QjtJQUVBLE1BQU1pQixpQkFBaUI7UUFDckIsTUFBTUMsa0JBQWtCckgsTUFBTWtILE1BQU0sQ0FBQ3JCLENBQUFBLE9BQVFBLEtBQUsvRixJQUFJLEtBQUssYUFBYXdILEdBQUcsQ0FBQ3pCLENBQUFBLE9BQVFBLEtBQUtsQixFQUFFO1FBQzNGM0MsaUJBQWlCRCxjQUFjMkUsTUFBTSxLQUFLVyxnQkFBZ0JYLE1BQU0sR0FBRyxFQUFFLEdBQUdXO0lBQzFFO0lBRUEsTUFBTUUsb0JBQW9CO1FBQ3hCLE1BQU1ULGFBQWE1RyxTQUFTb0gsR0FBRyxDQUFDdkIsQ0FBQUEsVUFBV0EsUUFBUXBCLEVBQUU7UUFDckR6QyxvQkFBb0JELGlCQUFpQnlFLE1BQU0sS0FBS0ksV0FBV0osTUFBTSxHQUFHLEVBQUUsR0FBR0k7SUFDM0U7SUFFQSxJQUFJNUssU0FBUztRQUNYLHFCQUNFLDhEQUFDMUIsMERBQU1BO3NCQUNMLDRFQUFDakMsMlVBQVNBO2dCQUFDaVAsTUFBSztnQkFBZUMsSUFBSTswQkFDakMsNEVBQUM5Tyx3VUFBTUE7b0JBQUMrTyxTQUFTOztzQ0FDZiw4REFBQ25PLDBVQUFRQTs0QkFBQ29PLFFBQU87Ozs7OztzQ0FDakIsOERBQUNwTywwVUFBUUE7NEJBQUNvTyxRQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSzNCO0lBRUEscUJBQ0UsOERBQUNuTiwwREFBTUE7a0JBQ0wsNEVBQUNqQywyVUFBU0E7WUFBQ2lQLE1BQUs7WUFBZUMsSUFBSTs7OEJBQ2pDLDhEQUFDOU8sd1VBQU1BO29CQUFDK08sU0FBUztvQkFBR0UsT0FBTTs7c0NBRXhCLDhEQUFDdFAscVVBQUdBOzRCQUNGdVAsSUFBRzs0QkFDSEMsR0FBRzs0QkFDSEMsU0FBUTs0QkFDUkMsZ0JBQWU7NEJBQ2ZDLFFBQU87NEJBQ1BDLGFBQVk7NEJBQ1pDLFdBQVU7c0NBRVYsNEVBQUN2UCx3VUFBTUE7Z0NBQUN3UCxTQUFRO2dDQUFnQlIsT0FBTTs7a0RBQ3BDLDhEQUFDdFAscVVBQUdBOzswREFDRiw4REFBQ0UseVVBQU9BO2dEQUNOMkQsTUFBSztnREFDTGtNLFlBQVc7Z0RBQ1hDLFFBQU87O2tFQUVQLDhEQUFDelAsc1VBQUlBO3dEQUFDMFAsSUFBSTNNLGdPQUFRQTt3REFBRTRNLElBQUk7Ozs7OztvREFBSzs7Ozs7OzswREFHL0IsOERBQUMvUCxzVUFBSUE7Z0RBQUM2RSxPQUFNO2dEQUFXbUwsSUFBSTs7b0RBQUc7b0RBQ0V2SixlQUFlRyxVQUFVSSxTQUFTOzs7Ozs7Ozs7Ozs7O2tEQUdwRSw4REFBQ3ZHLHdVQUFNQTt3Q0FDTHdQLHdCQUFVLDhEQUFDdk4sOE5BQU1BOzs7Ozt3Q0FDakJ3TixhQUFZO3dDQUNaQyxTQUFTcEQ7d0NBQ1RxRCxXQUFXdEk7d0NBQ1h1SSxZQUFZak07d0NBQ1pWLE1BQUs7a0RBQ047Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9MLDhEQUFDMUMsc1VBQUlBOzRCQUFDa1AsYUFBWTs0QkFBT0ksTUFBTTs7OENBQzdCLDhEQUFDclAseVVBQU9BOztzREFDTiw4REFBQ0UscVVBQUdBOzs4REFDRiw4REFBQ2Ysc1VBQUlBO29EQUFDMFAsSUFBSXhOLGtPQUFVQTtvREFBRXlOLElBQUk7Ozs7OztnREFBSzs7Ozs7OztzREFHakMsOERBQUM1TyxxVUFBR0E7OzhEQUNGLDhEQUFDZixzVUFBSUE7b0RBQUMwUCxJQUFJeE0sc0ZBQVNBO29EQUFFeU0sSUFBSTs7Ozs7O2dEQUFLOzs7Ozs7O3NEQUdoQyw4REFBQzVPLHFVQUFHQTs7OERBQ0YsOERBQUNmLHNVQUFJQTtvREFBQzBQLElBQUl6TSw4TkFBTUE7b0RBQUUwTSxJQUFJOzs7Ozs7Z0RBQUs7Ozs7Ozs7c0RBRzdCLDhEQUFDNU8scVVBQUdBOzs4REFDRiw4REFBQ2Ysc1VBQUlBO29EQUFDMFAsSUFBSTFNLDZOQUFLQTtvREFBRTJNLElBQUk7Ozs7OztnREFBSzs7Ozs7Ozs7Ozs7Ozs4Q0FLOUIsOERBQUM3TywyVUFBU0E7O3NEQUVSLDhEQUFDRSwwVUFBUUE7c0RBQ1AsNEVBQUNsQix3VUFBTUE7Z0RBQUMrTyxTQUFTO2dEQUFHRSxPQUFNOztrRUFFeEIsOERBQUM3TyxzVUFBSUE7OzBFQUNILDhEQUFDQyw0VUFBVUE7MEVBQ1QsNEVBQUNSLHlVQUFPQTtvRUFBQzJELE1BQUs7OEVBQUs7Ozs7Ozs7Ozs7OzBFQUVyQiw4REFBQ2xELDBVQUFRQTswRUFDUCw0RUFBQ1AsNFVBQVVBO29FQUFDc1EsU0FBUzt3RUFBRUMsTUFBTTt3RUFBR0MsSUFBSTtvRUFBRTtvRUFBR3hCLFNBQVM7O3NGQUNoRCw4REFBQy9PLHdVQUFNQTs0RUFBQytPLFNBQVM7NEVBQUdFLE9BQU07OzhGQUN4Qiw4REFBQ3hPLDZVQUFXQTs7c0dBQ1YsOERBQUNDLDJVQUFTQTtzR0FBQzs7Ozs7O3NHQUNYLDhEQUFDRix1VUFBS0E7NEZBQ0pvTSxPQUFPbEcsVUFBVUcsT0FBTzs0RkFDeEIySixVQUFVLENBQUN4RyxJQUFNMEMsb0JBQW9CLFdBQVcxQyxFQUFFRSxNQUFNLENBQUMwQyxLQUFLOzRGQUM5RDZELGFBQVk7Ozs7Ozs7Ozs7Ozs4RkFHaEIsOERBQUNoUSw2VUFBV0E7O3NHQUNWLDhEQUFDQywyVUFBU0E7c0dBQUM7Ozs7OztzR0FDWCw4REFBQ0YsdVVBQUtBOzRGQUNKb00sT0FBT2xHLFVBQVVFLE1BQU07NEZBQ3ZCNEosVUFBVSxDQUFDeEcsSUFBTTBDLG9CQUFvQixVQUFVMUMsRUFBRUUsTUFBTSxDQUFDMEMsS0FBSzs0RkFDN0Q2RCxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0ZBSWxCLDhEQUFDelEsd1VBQU1BOzRFQUFDK08sU0FBUzs0RUFBR0UsT0FBTTs7OEZBQ3hCLDhEQUFDeE8sNlVBQVdBOztzR0FDViw4REFBQ0MsMlVBQVNBO3NHQUFDOzs7Ozs7c0dBQ1gsOERBQUNGLHVVQUFLQTs0RkFDSm9NLE9BQU9sRyxVQUFVSSxTQUFTLElBQUk7NEZBQzlCNEosVUFBVTs0RkFDVnhCLElBQUc7NEZBQ0h5QixPQUFPO2dHQUFFekIsSUFBSTs0RkFBVzs7Ozs7Ozs7Ozs7OzhGQUc1Qiw4REFBQ3pPLDZVQUFXQTs7c0dBQ1YsOERBQUNDLDJVQUFTQTtzR0FBQzs7Ozs7O3NHQUNYLDhEQUFDRix1VUFBS0E7NEZBQ0pvTSxPQUFPbEcsVUFBVUssT0FBTyxJQUFJOzRGQUM1QjJKLFVBQVU7NEZBQ1Z4QixJQUFHOzRGQUNIeUIsT0FBTztnR0FBRXpCLElBQUk7NEZBQVc7NEZBQ3hCMEIsWUFBVzs0RkFDWEMsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBU3JCLDhEQUFDelEsc1VBQUlBOzswRUFDSCw4REFBQ0MsNFVBQVVBOzBFQUNULDRFQUFDTCx3VUFBTUE7b0VBQUMrTyxTQUFTO29FQUFHRSxPQUFNOztzRkFDeEIsOERBQUNoUCx3VUFBTUE7NEVBQUN3UCxTQUFROzs4RkFDZCw4REFBQzVQLHlVQUFPQTtvRkFBQzJELE1BQUs7O3NHQUNaLDhEQUFDdEQsc1VBQUlBOzRGQUFDMFAsSUFBSXZOLCtOQUFPQTs0RkFBRXdOLElBQUk7Ozs7Ozt3RkFBSzt3RkFDcEJ4SSxNQUFNMEcsTUFBTTt3RkFBQzs7Ozs7Ozs4RkFFdkIsOERBQUM5Tix3VUFBTUE7b0ZBQUM4TyxTQUFTOzhGQUNmLDRFQUFDeE8sd1VBQU1BO3dGQUNMd1Asd0JBQVUsOERBQUNyTiw4TkFBTUE7Ozs7O3dGQUNqQnNOLGFBQVk7d0ZBQ1pDLFNBQVMzQzt3RkFDVDZDLFlBQVlqTTt3RkFDWlYsTUFBSztrR0FDTjs7Ozs7Ozs7Ozs7Ozs7Ozs7d0VBTUo0RixjQUFjMkUsTUFBTSxHQUFHLG1CQUN0Qiw4REFBQzlOLHdVQUFNQTs0RUFBQ3dQLFNBQVE7NEVBQWdCTixHQUFHOzRFQUFHRCxJQUFHOzRFQUFVeUIsT0FBTztnRkFBRXpCLElBQUk7NEVBQVc7NEVBQUc0QixjQUFhOzs4RkFDekYsOERBQUNoUixzVUFBSUE7b0ZBQUMrUSxVQUFTO29GQUFLRSxZQUFXOzt3RkFDNUIzSCxjQUFjMkUsTUFBTTt3RkFBQzs7Ozs7Ozs4RkFFeEIsOERBQUM5Tix3VUFBTUE7b0ZBQUM4TyxTQUFTOztzR0FDZiw4REFBQ3hPLHdVQUFNQTs0RkFDTGlELE1BQUs7NEZBQ0x3TixTQUFROzRGQUNSZixTQUFTLElBQU01RyxpQkFBaUIsRUFBRTtzR0FDbkM7Ozs7OztzR0FHRCw4REFBQzlJLHdVQUFNQTs0RkFDTGlELE1BQUs7NEZBQ0x3TSxhQUFZOzRGQUNaRCx3QkFBVSw4REFBQ3ROLGdPQUFRQTs7Ozs7NEZBQ25Cd04sU0FBU25DOzRGQUNUb0MsV0FBVzFHOzRGQUNYMkcsWUFBWTNKO3NHQUNiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFRWCw4REFBQ2xHLDBVQUFRQTswRUFDTmlELHdCQUNDLDhEQUFDdkQsd1VBQU1BO29FQUFDK08sU0FBUzs4RUFDZDsyRUFBSXRKLE1BQU07cUVBQUcsQ0FBQ2tKLEdBQUcsQ0FBQyxDQUFDc0MsR0FBR0Msa0JBQ3JCLDhEQUFDdFEsMFVBQVFBOzRFQUFTb08sUUFBTzsyRUFBVmtDOzs7Ozs7Ozs7eUZBSW5CLDhEQUFDdlIscVVBQUdBO29FQUFDd1IsV0FBVTs4RUFDYiw0RUFBQ2hRLHVVQUFLQTt3RUFBQzZQLFNBQVE7d0VBQVN4TixNQUFLOzswRkFDM0IsOERBQUNwQyx1VUFBS0E7MEZBQ0osNEVBQUNFLG9VQUFFQTs7c0dBQ0QsOERBQUNDLG9VQUFFQTtzR0FDRCw0RUFBQ0ksMFVBQVFBO2dHQUNQeVAsV0FBV2hJLGNBQWMyRSxNQUFNLEtBQUsxRyxNQUFNa0gsTUFBTSxDQUFDOEMsQ0FBQUEsSUFBS0EsRUFBRWxLLElBQUksS0FBSyxhQUFhNEcsTUFBTSxJQUFJMUcsTUFBTTBHLE1BQU0sR0FBRztnR0FDdkd1RCxpQkFBaUJsSSxjQUFjMkUsTUFBTSxHQUFHLEtBQUszRSxjQUFjMkUsTUFBTSxHQUFHMUcsTUFBTWtILE1BQU0sQ0FBQzhDLENBQUFBLElBQUtBLEVBQUVsSyxJQUFJLEtBQUssYUFBYTRHLE1BQU07Z0dBQ3BIeUMsVUFBVS9COzs7Ozs7Ozs7OztzR0FHZCw4REFBQ2xOLG9VQUFFQTtzR0FBQzs7Ozs7O3NHQUNKLDhEQUFDQSxvVUFBRUE7c0dBQUM7Ozs7OztzR0FDSiw4REFBQ0Esb1VBQUVBO3NHQUFDOzs7Ozs7c0dBQ0osOERBQUNBLG9VQUFFQTtzR0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEZBR1IsOERBQUNGLHVVQUFLQTswRkFDSCxDQUFDZ0csU0FBUyxFQUFFLEVBQUVzSCxHQUFHLENBQUMsQ0FBQ3pCLHFCQUNsQiw4REFBQzVMLG9VQUFFQTs7MEdBQ0QsOERBQUNFLG9VQUFFQTswR0FDRCw0RUFBQ0csMFVBQVFBO29HQUNQeVAsV0FBV2hJLGNBQWNrRixRQUFRLENBQUNwQixLQUFLbEIsRUFBRTtvR0FDekN3RSxVQUFVLElBQU1wQyxvQkFBb0JsQixLQUFLbEIsRUFBRTtvR0FDM0NtRSxZQUFZakQsS0FBSy9GLElBQUksS0FBSzs7Ozs7Ozs7Ozs7MEdBRzlCLDhEQUFDM0Ysb1VBQUVBOzBHQUNELDRFQUFDdkIsd1VBQU1BOztzSEFDTCw4REFBQ04scVVBQUdBOzRHQUNGNFIsR0FBRzs0R0FDSEMsR0FBRzs0R0FDSHBDLFNBQVE7NEdBQ1JGLElBQUloQyxLQUFLdkksS0FBSyxHQUFHLElBQTZDLE9BQXpDdUksS0FBS3ZJLEtBQUssQ0FBQzhNLFFBQVEsQ0FBQyxJQUFJQyxRQUFRLENBQUMsR0FBRyxRQUFTOzs7Ozs7c0hBRXBFLDhEQUFDNVIsc1VBQUlBO3NIQUFFb04sS0FBSy9GLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzBHQUdwQiw4REFBQzNGLG9VQUFFQTswR0FDRCw0RUFBQ2IsdVVBQUtBO29HQUFDcVAsYUFBWTs4R0FBTzs7Ozs7Ozs7Ozs7MEdBRTVCLDhEQUFDeE8sb1VBQUVBOzBHQUNELDRFQUFDdkIsd1VBQU1BO29HQUFDMFIsTUFBSztvR0FBTzVDLFNBQVM7O3dHQUN6QnhKLENBQUFBLGtCQUFrQjJILEtBQUt2SCxXQUFXLEtBQUssRUFBRSxFQUFFaU0sS0FBSyxDQUFDLEdBQUcsR0FBR2pELEdBQUcsQ0FBQyxDQUFDa0Q7Z0hBRzdDaE4seUJBR1pBO2lJQUxILDhEQUFDbEUsdVVBQUtBO2dIQUVKcVAsYUFBYW5MLEVBQUFBLDBCQUFBQSxpQkFBaUIsQ0FBQ2dOLEtBQUssY0FBdkJoTiw4Q0FBQUEsd0JBQXlCRixLQUFLLEtBQUk7Z0hBQy9DbkIsTUFBSzswSEFFSnFCLEVBQUFBLDJCQUFBQSxpQkFBaUIsQ0FBQ2dOLEtBQUssY0FBdkJoTiwrQ0FBQUEseUJBQXlCRCxLQUFLLEtBQUlpTjsrR0FKOUJBOzs7Ozs7d0dBT1J0TSxrQkFBa0IySCxLQUFLdkgsV0FBVyxFQUFFb0ksTUFBTSxHQUFHLG1CQUM1Qyw4REFBQ3BOLHVVQUFLQTs0R0FBQ3FQLGFBQVk7NEdBQU94TSxNQUFLOztnSEFBSztnSEFDaEMrQixrQkFBa0IySCxLQUFLdkgsV0FBVyxFQUFFb0ksTUFBTSxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEdBS3ZELDhEQUFDdk0sb1VBQUVBOzBHQUNELDRFQUFDdkIsd1VBQU1BO29HQUFDOE8sU0FBUzs7c0hBQ2YsOERBQUNyTix5VUFBT0E7NEdBQUNrRCxPQUFNO3NIQUNiLDRFQUFDbkQsNFVBQVVBO2dIQUNUcVEsY0FBVztnSEFDWHBOLG9CQUFNLDhEQUFDbkMsK05BQU9BOzs7OztnSEFDZGlCLE1BQUs7Z0hBQ0x3TixTQUFRO2dIQUNSaEIsYUFBWTtnSEFDWkMsU0FBUyxJQUFNaEQsZUFBZUM7Z0hBQzlCaUQsWUFBWWpNOzs7Ozs7Ozs7OztzSEFHaEIsOERBQUN4Qyx5VUFBT0E7NEdBQUNrRCxPQUFNO3NIQUNiLDRFQUFDbkQsNFVBQVVBO2dIQUNUcVEsY0FBVztnSEFDWHBOLG9CQUFNLDhEQUFDakMsZ09BQVFBOzs7OztnSEFDZmUsTUFBSztnSEFDTHdOLFNBQVE7Z0hBQ1JoQixhQUFZO2dIQUNaRyxZQUFZak07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VGQTVEYmdKLEtBQUtsQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUEyRTlCLDhEQUFDNUwsc1VBQUlBOzswRUFDSCw4REFBQ0MsNFVBQVVBOzBFQUNULDRFQUFDTCx3VUFBTUE7b0VBQUMrTyxTQUFTO29FQUFHRSxPQUFNOztzRkFDeEIsOERBQUNoUCx3VUFBTUE7NEVBQUN3UCxTQUFROzs4RkFDZCw4REFBQzVQLHlVQUFPQTtvRkFBQzJELE1BQUs7O3NHQUNaLDhEQUFDdEQsc1VBQUlBOzRGQUFDMFAsSUFBSXROLDhOQUFNQTs0RkFBRXVOLElBQUk7Ozs7Ozt3RkFBSzt3RkFDaEJ0SSxTQUFTd0csTUFBTTt3RkFBQzs7Ozs7Ozs4RkFFN0IsOERBQUM5Tix3VUFBTUE7b0ZBQUM4TyxTQUFTOzhGQUNmLDRFQUFDeE8sd1VBQU1BO3dGQUNMd1Asd0JBQVUsOERBQUNyTiw4TkFBTUE7Ozs7O3dGQUNqQnNOLGFBQVk7d0ZBQ1pDLFNBQVNoSTt3RkFDVHpFLE1BQUs7a0dBQ047Ozs7Ozs7Ozs7Ozs7Ozs7O3dFQU1KOEYsaUJBQWlCeUUsTUFBTSxHQUFHLG1CQUN6Qiw4REFBQzlOLHdVQUFNQTs0RUFBQ3dQLFNBQVE7NEVBQWdCTixHQUFHOzRFQUFHRCxJQUFHOzRFQUFVeUIsT0FBTztnRkFBRXpCLElBQUk7NEVBQVc7NEVBQUc0QixjQUFhOzs4RkFDekYsOERBQUNoUixzVUFBSUE7b0ZBQUMrUSxVQUFTO29GQUFLRSxZQUFXOzt3RkFDNUJ6SCxpQkFBaUJ5RSxNQUFNO3dGQUFDOzs7Ozs7OzhGQUUzQiw4REFBQzlOLHdVQUFNQTtvRkFBQzhPLFNBQVM7O3NHQUNmLDhEQUFDeE8sd1VBQU1BOzRGQUNMaUQsTUFBSzs0RkFDTHdOLFNBQVE7NEZBQ1JmLFNBQVMsSUFBTTFHLG9CQUFvQixFQUFFO3NHQUN0Qzs7Ozs7O3NHQUdELDhEQUFDaEosd1VBQU1BOzRGQUNMaUQsTUFBSzs0RkFDTHdNLGFBQVk7NEZBQ1pELHdCQUFVLDhEQUFDdE4sZ09BQVFBOzs7Ozs0RkFDbkJ3TixTQUFTL0I7NEZBQ1RnQyxXQUFXMUc7NEZBQ1gyRyxZQUFZM0o7c0dBQ2I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQVFYLDhEQUFDbEcsMFVBQVFBOzBFQUNOb0gsZ0NBQ0MsOERBQUMxSCx3VUFBTUE7b0VBQUMrTyxTQUFTOzhFQUNkOzJFQUFJdEosTUFBTTtxRUFBRyxDQUFDa0osR0FBRyxDQUFDLENBQUNzQyxHQUFHQyxrQkFDckIsOERBQUN0USwwVUFBUUE7NEVBQVNvTyxRQUFPOzJFQUFWa0M7Ozs7Ozs7OzsyRUFHakIzSixTQUFTd0csTUFBTSxLQUFLLGtCQUN0Qiw4REFBQ2pPLHNVQUFJQTtvRUFBQzZFLE9BQU07b0VBQVdvTixXQUFVO29FQUFTakQsSUFBSTs4RUFBRzs7Ozs7eUZBSWpELDhEQUFDblAscVVBQUdBO29FQUFDd1IsV0FBVTs4RUFDYiw0RUFBQ2hRLHVVQUFLQTt3RUFBQzZQLFNBQVE7d0VBQVN4TixNQUFLOzswRkFDM0IsOERBQUNwQyx1VUFBS0E7MEZBQ0osNEVBQUNFLG9VQUFFQTs7c0dBQ0QsOERBQUNDLG9VQUFFQTtzR0FDRCw0RUFBQ0ksMFVBQVFBO2dHQUNQeVAsV0FBVzlILGlCQUFpQnlFLE1BQU0sS0FBS3hHLFNBQVN3RyxNQUFNLElBQUl4RyxTQUFTd0csTUFBTSxHQUFHO2dHQUM1RXVELGlCQUFpQmhJLGlCQUFpQnlFLE1BQU0sR0FBRyxLQUFLekUsaUJBQWlCeUUsTUFBTSxHQUFHeEcsU0FBU3dHLE1BQU07Z0dBQ3pGeUMsVUFBVTVCOzs7Ozs7Ozs7OztzR0FHZCw4REFBQ3JOLG9VQUFFQTtzR0FBQzs7Ozs7O3NHQUNKLDhEQUFDQSxvVUFBRUE7c0dBQUM7Ozs7OztzR0FDSiw4REFBQ0Esb1VBQUVBO3NHQUFDOzs7Ozs7c0dBQ0osOERBQUNBLG9VQUFFQTtzR0FBQzs7Ozs7O3NHQUNKLDhEQUFDQSxvVUFBRUE7c0dBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7OzBGQUdSLDhEQUFDRix1VUFBS0E7MEZBQ0gsQ0FBQ2tHLFlBQVksRUFBRSxFQUFFb0gsR0FBRyxDQUFDLENBQUN2QjtvRkFDckIsTUFBTTRFLGFBQWF2TixtQkFBbUIsQ0FBQzJJLFFBQVFsRyxJQUFJLENBQUMsSUFBSTt3RkFBRXhDLE1BQU0vQix1T0FBZUE7d0ZBQUVnQyxPQUFPO3dGQUFRQyxPQUFPO29GQUFRO29GQUMvRyxxQkFDRSw4REFBQ3RELG9VQUFFQTs7MEdBQ0QsOERBQUNFLG9VQUFFQTswR0FDRCw0RUFBQ0csMFVBQVFBO29HQUNQeVAsV0FBVzlILGlCQUFpQmdGLFFBQVEsQ0FBQ2xCLFFBQVFwQixFQUFFO29HQUMvQ3dFLFVBQVUsSUFBTWhDLHVCQUF1QnBCLFFBQVFwQixFQUFFOzs7Ozs7Ozs7OzswR0FHckQsOERBQUN4SyxvVUFBRUE7MEdBQ0QsNEVBQUN2Qix3VUFBTUE7O3NIQUNMLDhEQUFDQyxzVUFBSUE7NEdBQ0gwUCxJQUFJb0MsV0FBV3ROLElBQUk7NEdBQ25CQyxPQUFPLEdBQW9CLE9BQWpCcU4sV0FBV3JOLEtBQUssRUFBQzs7Ozs7O3NIQUU3Qiw4REFBQzdFLHNVQUFJQTtzSEFBRXNOLFFBQVFqRyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzswR0FHdkIsOERBQUMzRixvVUFBRUE7MEdBQ0QsNEVBQUNiLHVVQUFLQTtvR0FBQ3FQLGFBQWFnQyxXQUFXck4sS0FBSzs4R0FBR3FOLFdBQVdwTixLQUFLOzs7Ozs7Ozs7OzswR0FFekQsOERBQUNwRCxvVUFBRUE7MEdBQ0QsNEVBQUMxQixzVUFBSUE7b0dBQUM2RSxPQUFNOzhHQUFZOEksY0FBY0wsUUFBUTZFLFNBQVM7Ozs7Ozs7Ozs7OzBHQUV6RCw4REFBQ3pRLG9VQUFFQTswR0FDRCw0RUFBQzFCLHNVQUFJQTtvR0FBQzZFLE9BQU07OEdBQVl5SSxRQUFRZCxRQUFROzs7Ozs7Ozs7OzswR0FFMUMsOERBQUM5SyxvVUFBRUE7MEdBQ0QsNEVBQUN2Qix3VUFBTUE7b0dBQUM4TyxTQUFTOztzSEFDZiw4REFBQ3JOLHlVQUFPQTs0R0FBQ2tELE9BQU07c0hBQ2IsNEVBQUNuRCw0VUFBVUE7Z0hBQ1RxUSxjQUFXO2dIQUNYcE4sb0JBQU0sOERBQUNuQywrTkFBT0E7Ozs7O2dIQUNkaUIsTUFBSztnSEFDTHdOLFNBQVE7Z0hBQ1JoQixhQUFZO2dIQUNaQyxTQUFTLElBQU05QyxrQkFBa0JDO2dIQUNqQytDLFlBQVlqTTs7Ozs7Ozs7Ozs7c0hBR2hCLDhEQUFDeEMseVVBQU9BOzRHQUFDa0QsT0FBTTtzSEFDYiw0RUFBQ25ELDRVQUFVQTtnSEFDVHFRLGNBQVc7Z0hBQ1hwTixvQkFBTSw4REFBQ2pDLGdPQUFRQTs7Ozs7Z0hBQ2ZlLE1BQUs7Z0hBQ0x3TixTQUFRO2dIQUNSaEIsYUFBWTtnSEFDWkMsU0FBUyxJQUFNMUMsb0JBQW9CSCxRQUFRcEIsRUFBRTtnSEFDN0NtRSxZQUFZak07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VGQTlDYmtKLFFBQVFwQixFQUFFOzs7OztnRkFxRHZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBV2hCLDhEQUFDOUssMFVBQVFBO3NEQUNQLDRFQUFDbEIsd1VBQU1BO2dEQUFDK08sU0FBUztnREFBR0UsT0FBTTs7a0VBQ3hCLDhEQUFDdFAscVVBQUdBOzswRUFDRiw4REFBQ0UseVVBQU9BO2dFQUFDMkQsTUFBSztnRUFBSzBPLElBQUk7MEVBQUc7Ozs7OzswRUFDMUIsOERBQUNwUyxzVUFBSUE7Z0VBQUM2RSxPQUFNO2dFQUFXZ00sT0FBTztvRUFBRWhNLE9BQU87Z0VBQVc7Z0VBQUd1TixJQUFJOzBFQUFHOzs7Ozs7Ozs7Ozs7a0VBSzlELDhEQUFDblMsNFVBQVVBO3dEQUFDc1EsU0FBUzs0REFBRUMsTUFBTTs0REFBR0MsSUFBSTt3REFBRTt3REFBR3hCLFNBQVM7OzBFQUNoRCw4REFBQzNPLHNVQUFJQTs7a0ZBQ0gsOERBQUNDLDRVQUFVQTtrRkFDVCw0RUFBQ1IseVVBQU9BOzRFQUFDMkQsTUFBSztzRkFBSzs7Ozs7Ozs7Ozs7a0ZBRXJCLDhEQUFDbEQsMFVBQVFBO2tGQUNQLDRFQUFDTix3VUFBTUE7NEVBQUMrTyxTQUFTOzRFQUFHRSxPQUFNOzs4RkFDeEIsOERBQUNuUCxzVUFBSUE7b0ZBQUMrUSxVQUFTO29GQUFLbE0sT0FBTTs4RkFBVzs7Ozs7OzhGQUdyQyw4REFBQ3BFLHdVQUFNQTtvRkFDTHdQLHdCQUFVLDhEQUFDM00sc0ZBQVNBOzs7OztvRkFDcEI0TSxhQUFZO29GQUNaQyxTQUFTbkg7b0ZBQ1R0RixNQUFLOzhGQUNOOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFPUCw4REFBQ3BELHNVQUFJQTs7a0ZBQ0gsOERBQUNDLDRVQUFVQTtrRkFDVCw0RUFBQ1IseVVBQU9BOzRFQUFDMkQsTUFBSztzRkFBSzs7Ozs7Ozs7Ozs7a0ZBRXJCLDhEQUFDbEQsMFVBQVFBO2tGQUNQLDRFQUFDTix3VUFBTUE7NEVBQUMrTyxTQUFTOzRFQUFHRSxPQUFNOzs4RkFDeEIsOERBQUNuUCxzVUFBSUE7b0ZBQUMrUSxVQUFTO29GQUFLbE0sT0FBTTtvRkFBV3VOLElBQUk7OEZBQUc7Ozs7Ozs4RkFHNUMsOERBQUNqUyx3VUFBTUE7b0ZBQUMwUixNQUFLO29GQUFPNUMsU0FBUzs7c0dBQzNCLDhEQUFDcE8sdVVBQUtBOzRGQUFDcVAsYUFBWTtzR0FBTzs7Ozs7O3NHQUMxQiw4REFBQ3JQLHVVQUFLQTs0RkFBQ3FQLGFBQVk7c0dBQVM7Ozs7OztzR0FDNUIsOERBQUNyUCx1VUFBS0E7NEZBQUNxUCxhQUFZO3NHQUFROzs7Ozs7c0dBQzNCLDhEQUFDclAsdVVBQUtBOzRGQUFDcVAsYUFBWTtzR0FBUzs7Ozs7O3NHQUM1Qiw4REFBQ3JQLHVVQUFLQTs0RkFBQ3FQLGFBQVk7c0dBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBVXhDLDhEQUFDOU8sMFVBQVFBO3NEQUNQLDRFQUFDbEIsd1VBQU1BO2dEQUFDK08sU0FBUztnREFBR0UsT0FBTTs7a0VBQ3hCLDhEQUFDdFAscVVBQUdBOzswRUFDRiw4REFBQ0UseVVBQU9BO2dFQUFDMkQsTUFBSztnRUFBSzBPLElBQUk7MEVBQUc7Ozs7OzswRUFDMUIsOERBQUNwUyxzVUFBSUE7Z0VBQUM2RSxPQUFNO2dFQUFXZ00sT0FBTztvRUFBRWhNLE9BQU87Z0VBQVc7Z0VBQUd1TixJQUFJOzBFQUFHOzs7Ozs7Ozs7Ozs7a0VBSzlELDhEQUFDblMsNFVBQVVBO3dEQUFDc1EsU0FBUzs0REFBRUMsTUFBTTs0REFBR0MsSUFBSTt3REFBRTt3REFBR3hCLFNBQVM7OzBFQUNoRCw4REFBQzNPLHNVQUFJQTs7a0ZBQ0gsOERBQUNDLDRVQUFVQTtrRkFDVCw0RUFBQ1IseVVBQU9BOzRFQUFDMkQsTUFBSztzRkFBSzs7Ozs7Ozs7Ozs7a0ZBRXJCLDhEQUFDbEQsMFVBQVFBO2tGQUNQLDRFQUFDTix3VUFBTUE7NEVBQUMrTyxTQUFTOzRFQUFHRSxPQUFNOzs4RkFDeEIsOERBQUMxTyx3VUFBTUE7b0ZBQ0x3UCx3QkFBVSw4REFBQzdNLDZOQUFLQTs7Ozs7b0ZBQ2hCOE0sYUFBWTtvRkFDWkMsU0FBUyxJQUFNa0MsT0FBT0MsSUFBSSxDQUFDLHFDQUFxQzs4RkFDakU7Ozs7Ozs4RkFHRCw4REFBQzdSLHdVQUFNQTtvRkFDTHdQLHdCQUFVLDhEQUFDcE4sdU9BQWVBOzs7OztvRkFDMUJxTixhQUFZO29GQUNaQyxTQUFTLElBQU1rQyxPQUFPQyxJQUFJLENBQUMsK0JBQStCOzhGQUMzRDs7Ozs7OzhGQUdELDhEQUFDN1Isd1VBQU1BO29GQUNMd1Asd0JBQVUsOERBQUNwTix1T0FBZUE7Ozs7O29GQUMxQnFOLGFBQVk7b0ZBQ1pDLFNBQVMsSUFBTWtDLE9BQU9DLElBQUksQ0FBQyx3QkFBd0I7b0ZBQ25EakMsVUFBVTs4RkFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBT1AsOERBQUMvUCxzVUFBSUE7O2tGQUNILDhEQUFDQyw0VUFBVUE7a0ZBQ1QsNEVBQUNSLHlVQUFPQTs0RUFBQzJELE1BQUs7c0ZBQUs7Ozs7Ozs7Ozs7O2tGQUVyQiw4REFBQ2xELDBVQUFRQTtrRkFDUCw0RUFBQ04sd1VBQU1BOzRFQUFDK08sU0FBUzs0RUFBR0UsT0FBTTs7OEZBQ3hCLDhEQUFDMU8sd1VBQU1BO29GQUNMd1Asd0JBQVUsOERBQUMzTixrT0FBVUE7Ozs7O29GQUNyQjROLGFBQVk7b0ZBQ1pDLFNBQVMsSUFBTWtDLE9BQU9DLElBQUksQ0FBQyxpQkFBaUI7OEZBQzdDOzs7Ozs7OEZBR0QsOERBQUM3Uix3VUFBTUE7b0ZBQ0x3UCx3QkFBVSw4REFBQzNOLGtPQUFVQTs7Ozs7b0ZBQ3JCNE4sYUFBWTtvRkFDWkMsU0FBUyxJQUFNa0MsT0FBT0MsSUFBSSxDQUFDLG1CQUFtQjs4RkFDL0M7Ozs7Ozs4RkFHRCw4REFBQzdSLHdVQUFNQTtvRkFDTHdQLHdCQUFVLDhEQUFDM04sa09BQVVBOzs7OztvRkFDckI0TixhQUFZO29GQUNaQyxTQUFTLElBQU1rQyxPQUFPQyxJQUFJLENBQUMsdUJBQXVCOzhGQUNuRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFXYiw4REFBQ2xSLDBVQUFRQTtzREFDUCw0RUFBQ2xCLHdVQUFNQTtnREFBQytPLFNBQVM7Z0RBQUdFLE9BQU07O2tFQUN4Qiw4REFBQ3RQLHFVQUFHQTs7MEVBQ0YsOERBQUNFLHlVQUFPQTtnRUFBQzJELE1BQUs7Z0VBQUswTyxJQUFJOzBFQUFHOzs7Ozs7MEVBQzFCLDhEQUFDcFMsc1VBQUlBO2dFQUFDNkUsT0FBTTtnRUFBV2dNLE9BQU87b0VBQUVoTSxPQUFPO2dFQUFXO2dFQUFHdU4sSUFBSTswRUFBRzs7Ozs7Ozs7Ozs7O2tFQUs5RCw4REFBQ25TLDRVQUFVQTt3REFBQ3NRLFNBQVM7NERBQUVDLE1BQU07NERBQUdDLElBQUk7d0RBQUU7d0RBQUd4QixTQUFTOzswRUFDaEQsOERBQUMzTyxzVUFBSUE7O2tGQUNILDhEQUFDQyw0VUFBVUE7a0ZBQ1QsNEVBQUNSLHlVQUFPQTs0RUFBQzJELE1BQUs7c0ZBQUs7Ozs7Ozs7Ozs7O2tGQUVyQiw4REFBQ2xELDBVQUFRQTs7MEZBQ1AsOERBQUNSLHNVQUFJQTtnRkFBQzZFLE9BQU07Z0ZBQVdrTSxVQUFTO2dGQUFLcUIsSUFBSTswRkFBRzs7Ozs7OzBGQUc1Qyw4REFBQ2xTLHdVQUFNQTtnRkFBQytPLFNBQVM7Z0ZBQUdFLE9BQU07O2tHQUN4Qiw4REFBQ25QLHNVQUFJQTt3RkFBQytRLFVBQVM7a0dBQUs7Ozs7OztrR0FDcEIsOERBQUMvUSxzVUFBSUE7d0ZBQUMrUSxVQUFTO2tHQUFLOzs7Ozs7a0dBQ3BCLDhEQUFDL1Esc1VBQUlBO3dGQUFDK1EsVUFBUztrR0FBSzs7Ozs7O2tHQUNwQiw4REFBQy9RLHNVQUFJQTt3RkFBQytRLFVBQVM7a0dBQUs7Ozs7OztrR0FDcEIsOERBQUMvUSxzVUFBSUE7d0ZBQUMrUSxVQUFTO2tHQUFLOzs7Ozs7a0dBQ3BCLDhEQUFDL1Esc1VBQUlBO3dGQUFDK1EsVUFBUztrR0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUsxQiw4REFBQ3pRLHNVQUFJQTs7a0ZBQ0gsOERBQUNDLDRVQUFVQTtrRkFDVCw0RUFBQ1IseVVBQU9BOzRFQUFDMkQsTUFBSztzRkFBSzs7Ozs7Ozs7Ozs7a0ZBRXJCLDhEQUFDbEQsMFVBQVFBO2tGQUNQLDRFQUFDTix3VUFBTUE7NEVBQUMrTyxTQUFTOzRFQUFHRSxPQUFNOzs4RkFDeEIsOERBQUNuUCxzVUFBSUE7b0ZBQUMrUSxVQUFTO29GQUFLbE0sT0FBTTs4RkFBVzs7Ozs7OzhGQUdyQyw4REFBQ3BFLHdVQUFNQTtvRkFDTHdQLHdCQUFVLDhEQUFDN00sNk5BQUtBOzs7OztvRkFDaEI4TSxhQUFZO29GQUNaZ0IsU0FBUTtvRkFDUmIsVUFBVTs4RkFDWDs7Ozs7OzhGQUdELDhEQUFDNVAsd1VBQU1BO29GQUNMd1Asd0JBQVUsOERBQUM3TSw2TkFBS0E7Ozs7O29GQUNoQjhNLGFBQVk7b0ZBQ1pnQixTQUFRO29GQUNSYixVQUFVOzhGQUNYOzs7Ozs7OEZBR0QsOERBQUM1UCx3VUFBTUE7b0ZBQ0x3UCx3QkFBVSw4REFBQzdNLDZOQUFLQTs7Ozs7b0ZBQ2hCOE0sYUFBWTtvRkFDWmdCLFNBQVE7b0ZBQ1JiLFVBQVU7OEZBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBY25CLDhEQUFDaE8sMkNBQVFBO29CQUFDa1Esd0JBQVUsOERBQUN6USx5VUFBT0E7Ozs7OzhCQUMxQiw0RUFBQzBCO3dCQUNDd0UsUUFBUUM7d0JBQ1JHLFNBQVNDO3dCQUNUbUssV0FBVy9GOzs7Ozs7Ozs7Ozs4QkFJZiw4REFBQ3BLLDJDQUFRQTtvQkFBQ2tRLHdCQUFVLDhEQUFDelEseVVBQU9BOzs7Ozs4QkFDMUIsNEVBQUM4Qjt3QkFDQ29FLFFBQVFNO3dCQUNSRixTQUFTSTt3QkFDVDhFLFNBQVNsRTt3QkFDVHFKLFlBQVloTCxTQUFTZ0gsTUFBTSxDQUFDVixDQUFBQSxJQUFLQSxFQUFFM0csSUFBSSxLQUFLO3dCQUM1Q29MLFdBQVcvRjs7Ozs7Ozs7Ozs7OEJBSWYsOERBQUNwSywyQ0FBUUE7b0JBQUNrUSx3QkFBVSw4REFBQ3pRLHlVQUFPQTs7Ozs7OEJBQzFCLDRFQUFDaUM7d0JBQ0NpRSxRQUFRUzt3QkFDUkwsU0FBU087d0JBQ1Q2SixXQUFXNUc7Ozs7Ozs7Ozs7OzhCQUlmLDhEQUFDdkosMkNBQVFBO29CQUFDa1Esd0JBQVUsOERBQUN6USx5VUFBT0E7Ozs7OzhCQUMxQiw0RUFBQytCO3dCQUNDbUUsUUFBUVk7d0JBQ1JSLFNBQVNVO3dCQUNUc0UsTUFBTWxFO3dCQUNOc0osV0FBVzVHOzs7Ozs7Ozs7Ozs4QkFJZiw4REFBQ3ZKLDJDQUFRQTtvQkFBQ2tRLHdCQUFVLDhEQUFDelEseVVBQU9BOzs7Ozs4QkFDMUIsNEVBQUNnQzt3QkFDQ2tFLFFBQVFlO3dCQUNSWCxTQUFTYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1yQjtJQXhnQ3dCMUM7O1FBQ1JsRywwVUFBUUE7UUFDRTJCLDJEQUFZQTtRQUNJa0M7UUFDUUE7UUFxQm9EbkQsK1VBQWFBO1FBQ25CQSwrVUFBYUE7UUFDaEJBLCtVQUFhQTtRQUNuQkEsK1VBQWFBO1FBQ0RBLCtVQUFhQTs7O01BN0J4RndGIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxzcmNcXGRhc2hib2FyZFxccGFnZXNcXGFkbWluXFxndWlsZHMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEB0cy1ub2NoZWNrXHJcbmltcG9ydCB7XHJcbiAgQm94LFxyXG4gIENvbnRhaW5lcixcclxuICBIZWFkaW5nLFxyXG4gIFRleHQsXHJcbiAgU2ltcGxlR3JpZCxcclxuICBWU3RhY2ssXHJcbiAgSFN0YWNrLFxyXG4gIEljb24sXHJcbiAgdXNlVG9hc3QsXHJcbiAgQ2FyZCxcclxuICBDYXJkSGVhZGVyLFxyXG4gIENhcmRCb2R5LFxyXG4gIEJ1dHRvbixcclxuICBJbnB1dCxcclxuICBTZWxlY3QsXHJcbiAgU3dpdGNoLFxyXG4gIEZvcm1Db250cm9sLFxyXG4gIEZvcm1MYWJlbCxcclxuICBCYWRnZSxcclxuICBEaXZpZGVyLFxyXG4gIFNrZWxldG9uLFxyXG4gIHVzZURpc2Nsb3N1cmUsXHJcbiAgTW9kYWwsXHJcbiAgTW9kYWxPdmVybGF5LFxyXG4gIE1vZGFsQ29udGVudCxcclxuICBNb2RhbEhlYWRlcixcclxuICBNb2RhbEJvZHksXHJcbiAgTW9kYWxGb290ZXIsXHJcbiAgTW9kYWxDbG9zZUJ1dHRvbixcclxuICBUYWJzLFxyXG4gIFRhYkxpc3QsXHJcbiAgVGFiUGFuZWxzLFxyXG4gIFRhYixcclxuICBUYWJQYW5lbCxcclxuICBUYWJsZSxcclxuICBUaGVhZCxcclxuICBUYm9keSxcclxuICBUcixcclxuICBUaCxcclxuICBUZCxcclxuICBJY29uQnV0dG9uLFxyXG4gIFRvb2x0aXAsXHJcbiAgQ2hlY2tib3gsXHJcbiAgRmxleCxcclxuICBSYWRpb0dyb3VwLFxyXG4gIFJhZGlvLFxyXG4gIFNwaW5uZXIsXHJcbn0gZnJvbSAnQGNoYWtyYS11aS9yZWFjdCc7XHJcbmltcG9ydCBMYXlvdXQgZnJvbSAnLi4vLi4vY29tcG9uZW50cy9MYXlvdXQnO1xyXG5pbXBvcnQgdXNlR3VpbGRJbmZvIGZyb20gJy4uLy4uL2hvb2tzL3VzZUd1aWxkSW5mbyc7XHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrLCB1c2VSZWYsIFN1c3BlbnNlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBHZXRTZXJ2ZXJTaWRlUHJvcHMgfSBmcm9tICduZXh0JztcclxuaW1wb3J0IHsgZ2V0U2VydmVyU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aC9uZXh0JztcclxuaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tICcuLi9hcGkvYXV0aC9bLi4ubmV4dGF1dGhdJztcclxuaW1wb3J0IHsgRmlTZXR0aW5ncywgRmlVc2VycywgRmlIYXNoLCBGaUVkaXQyLCBGaVNhdmUsIEZpVHJhc2gyLCBGaVBsdXMsIEZpTWVzc2FnZVNxdWFyZSwgRmlWb2x1bWUyLCBGaUZvbGRlclBsdXMsIEZpUmFkaW8sIEZpTG9jaywgRmlNZXNzYWdlQ2lyY2xlLCBGaVRyYXNoLCBGaVNlcnZlciwgRmlaYXAsIEZpVG9vbCB9IGZyb20gJ3JlYWN0LWljb25zL2ZpJztcclxuaW1wb3J0IHsgRmFQYWxldHRlIH0gZnJvbSAncmVhY3QtaWNvbnMvZmEnO1xyXG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnO1xyXG5pbXBvcnQgeyB1c2VUaGVtZSwgQ09MT1JfU0NIRU1FUyB9IGZyb20gJy4uLy4uL2NvbnRleHRzL1RoZW1lQ29udGV4dCc7XHJcblxyXG4vLyBEeW5hbWljIGltcG9ydHMgZm9yIGhlYXZ5IGNvbXBvbmVudHNcclxuY29uc3QgQ3JlYXRlQ2hhbm5lbERpYWxvZyA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCcuLi8uLi9jb21wb25lbnRzL0NyZWF0ZUNoYW5uZWxEaWFsb2cnKSwge1xyXG4gIGxvYWRpbmc6ICgpID0+IDxTcGlubmVyIHNpemU9XCJtZFwiIC8+LFxyXG4gIHNzcjogZmFsc2VcclxufSk7XHJcblxyXG5jb25zdCBFZGl0Q2hhbm5lbERpYWxvZyA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCcuLi8uLi9jb21wb25lbnRzL0VkaXRDaGFubmVsRGlhbG9nJyksIHtcclxuICBsb2FkaW5nOiAoKSA9PiA8U3Bpbm5lciBzaXplPVwibWRcIiAvPixcclxuICBzc3I6IGZhbHNlXHJcbn0pO1xyXG5cclxuY29uc3QgRWRpdFJvbGVEaWFsb2cgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnLi4vLi4vY29tcG9uZW50cy9FZGl0Um9sZURpYWxvZycpLCB7XHJcbiAgbG9hZGluZzogKCkgPT4gPFNwaW5uZXIgc2l6ZT1cIm1kXCIgLz4sXHJcbiAgc3NyOiBmYWxzZVxyXG59KTtcclxuXHJcbmNvbnN0IENvbG9yQnVpbGRlciA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCcuLi8uLi9jb21wb25lbnRzL0NvbG9yQnVpbGRlcicpLCB7XHJcbiAgbG9hZGluZzogKCkgPT4gPFNwaW5uZXIgc2l6ZT1cIm1kXCIgLz4sXHJcbiAgc3NyOiBmYWxzZVxyXG59KTtcclxuXHJcbmNvbnN0IENyZWF0ZVJvbGVEaWFsb2cgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnLi4vLi4vY29tcG9uZW50cy9DcmVhdGVSb2xlRGlhbG9nJyksIHtcclxuICBsb2FkaW5nOiAoKSA9PiA8U3Bpbm5lciBzaXplPVwibWRcIiAvPixcclxuICBzc3I6IGZhbHNlXHJcbn0pO1xyXG5cclxuLy8gUmF0ZSBsaW1pdGluZyBjb25zdGFudHNcclxuY29uc3QgUkFURV9MSU1JVF9NUyA9IDIwMDA7IC8vIDIgc2Vjb25kcyBiZXR3ZWVuIG9wZXJhdGlvbnNcclxuY29uc3QgQlVMS19SQVRFX0xJTUlUX01TID0gNTAwMDsgLy8gNSBzZWNvbmRzIGJldHdlZW4gYnVsayBvcGVyYXRpb25zXHJcblxyXG4vLyBDdXN0b20gaG9vayBmb3IgcmF0ZSBsaW1pdGluZ1xyXG5mdW5jdGlvbiB1c2VSYXRlTGltaXQoZGVsYXkgPSBSQVRFX0xJTUlUX01TKSB7XHJcbiAgY29uc3QgW2lzUmF0ZUxpbWl0ZWQsIHNldElzUmF0ZUxpbWl0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IHRpbWVvdXRSZWYgPSB1c2VSZWYobnVsbCk7XHJcblxyXG4gIGNvbnN0IHJlc2V0UmF0ZUxpbWl0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgaWYgKHRpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgIH1cclxuICAgIHNldElzUmF0ZUxpbWl0ZWQodHJ1ZSk7XHJcbiAgICB0aW1lb3V0UmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgc2V0SXNSYXRlTGltaXRlZChmYWxzZSk7XHJcbiAgICB9LCBkZWxheSk7XHJcbiAgfSwgW2RlbGF5XSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBpZiAodGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gW2lzUmF0ZUxpbWl0ZWQsIHJlc2V0UmF0ZUxpbWl0XTtcclxufVxyXG5cclxuaW50ZXJmYWNlIFJvbGUge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIGNvbG9yOiBudW1iZXI7XHJcbiAgcG9zaXRpb246IG51bWJlcjtcclxuICBwZXJtaXNzaW9uczogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgQ2hhbm5lbCB7XHJcbiAgaWQ6IHN0cmluZztcclxuICBuYW1lOiBzdHJpbmc7XHJcbiAgdHlwZTogbnVtYmVyO1xyXG4gIHBhcmVudF9pZD86IHN0cmluZztcclxuICBwb3NpdGlvbjogbnVtYmVyO1xyXG4gIHRvcGljPzogc3RyaW5nO1xyXG4gIG5zZnc/OiBib29sZWFuO1xyXG4gIHJhdGVfbGltaXRfcGVyX3VzZXI/OiBudW1iZXI7XHJcbiAgYml0cmF0ZT86IG51bWJlcjtcclxuICB1c2VyX2xpbWl0PzogbnVtYmVyO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgR3VpbGRTZXR0aW5ncyB7XHJcbiAgcHJlZml4OiBzdHJpbmc7XHJcbiAgYm90TmFtZTogc3RyaW5nO1xyXG4gIGd1aWxkTmFtZTogc3RyaW5nO1xyXG4gIGd1aWxkSWQ6IHN0cmluZztcclxuICBndWlsZEljb246IHN0cmluZyB8IG51bGw7XHJcbiAgYWN0aXZpdGllczogeyB0eXBlOiAnUExBWUlORycgfCAnU1RSRUFNSU5HJyB8ICdMSVNURU5JTkcnIHwgJ1dBVENISU5HJyB8ICdDT01QRVRJTkcnOyBuYW1lOiBzdHJpbmc7IH1bXTtcclxuICBhY3Rpdml0eVJvdGF0aW9uSW50ZXJ2YWw6IG51bWJlcjsgLy8gaW4gc2Vjb25kc1xyXG59XHJcblxyXG5jb25zdCBDSEFOTkVMX1RZUEVfQ09ORklHID0ge1xyXG4gIDA6ICB7IGljb246IEZpTWVzc2FnZVNxdWFyZSwgY29sb3I6ICdibHVlJywgICBsYWJlbDogJ1RleHQnIH0sXHJcbiAgMjogIHsgaWNvbjogRmlWb2x1bWUyLCAgICAgIGNvbG9yOiAnZ3JlZW4nLCAgbGFiZWw6ICdWb2ljZScgfSxcclxuICA0OiAgeyBpY29uOiBGaUZvbGRlclBsdXMsICAgY29sb3I6ICdwdXJwbGUnLCBsYWJlbDogJ0NhdGVnb3J5JyB9LFxyXG4gIDU6ICB7IGljb246IEZpUmFkaW8sICAgICAgICBjb2xvcjogJ29yYW5nZScsIGxhYmVsOiAnQW5ub3VuY2VtZW50JyB9LFxyXG4gIDExOiB7IGljb246IEZpTWVzc2FnZUNpcmNsZSxjb2xvcjogJ2N5YW4nLCAgIGxhYmVsOiAnUHVibGljIFRocmVhZCcgfSxcclxuICAxMjogeyBpY29uOiBGaUxvY2ssICAgICAgICAgY29sb3I6ICdwaW5rJywgICBsYWJlbDogJ1ByaXZhdGUgVGhyZWFkJyB9LFxyXG4gIDEzOiB7IGljb246IEZpSGFzaCwgICAgICAgICBjb2xvcjogJ3RlYWwnLCAgIGxhYmVsOiAnU3RhZ2UgVm9pY2UnfSxcclxuICAxNTogeyBpY29uOiBGaUhhc2gsICAgICAgICAgY29sb3I6ICdncmF5JywgICBsYWJlbDogJ0ZvcnVtJ31cclxufTtcclxuXHJcbmNvbnN0IFBFUk1JU1NJT05fQkFER0VTID0ge1xyXG4gIEFETUlOSVNUUkFUT1I6IHsgY29sb3I6ICdyZWQnLCBsYWJlbDogJ0FkbWluJyB9LFxyXG4gIE1BTkFHRV9HVUlMRDogeyBjb2xvcjogJ29yYW5nZScsIGxhYmVsOiAnTWFuYWdlIFNlcnZlcicgfSxcclxuICBNQU5BR0VfUk9MRVM6IHsgY29sb3I6ICd5ZWxsb3cnLCBsYWJlbDogJ01hbmFnZSBSb2xlcycgfSxcclxuICBNQU5BR0VfQ0hBTk5FTFM6IHsgY29sb3I6ICdncmVlbicsIGxhYmVsOiAnTWFuYWdlIENoYW5uZWxzJyB9LFxyXG4gIEtJQ0tfTUVNQkVSUzogeyBjb2xvcjogJ3B1cnBsZScsIGxhYmVsOiAnS2ljaycgfSxcclxuICBCQU5fTUVNQkVSUzogeyBjb2xvcjogJ3BpbmsnLCBsYWJlbDogJ0JhbicgfSxcclxuICBNQU5BR0VfTUVTU0FHRVM6IHsgY29sb3I6ICdibHVlJywgbGFiZWw6ICdNYW5hZ2UgTWVzc2FnZXMnIH0sXHJcbiAgTUVOVElPTl9FVkVSWU9ORTogeyBjb2xvcjogJ2N5YW4nLCBsYWJlbDogJ01lbnRpb24gQGV2ZXJ5b25lJyB9LFxyXG59O1xyXG5cclxuLy8gQWRkIHRoaXMgaGVscGVyIG1hcCBhbmQgZnVuY3Rpb24gYWZ0ZXIgUEVSTUlTU0lPTl9CQURHRVMgY29uc3RhbnRcclxuY29uc3QgUEVSTUlTU0lPTl9GTEFHX0JJVFM6IFJlY29yZDxrZXlvZiB0eXBlb2YgUEVSTUlTU0lPTl9CQURHRVMsIGJpZ2ludD4gPSB7XHJcbiAgQURNSU5JU1RSQVRPUjogMW4gPDwgM24sXHJcbiAgTUFOQUdFX0dVSUxEOiAxbiA8PCA1bixcclxuICBNQU5BR0VfUk9MRVM6IDFuIDw8IDI4bixcclxuICBNQU5BR0VfQ0hBTk5FTFM6IDFuIDw8IDRuLFxyXG4gIEtJQ0tfTUVNQkVSUzogMW4gPDwgMW4sXHJcbiAgQkFOX01FTUJFUlM6IDFuIDw8IDJuLFxyXG4gIE1BTkFHRV9NRVNTQUdFUzogMW4gPDwgMTNuLFxyXG4gIE1FTlRJT05fRVZFUllPTkU6IDFuIDw8IDE3bixcclxufTtcclxuXHJcbmZ1bmN0aW9uIGRlY29kZVBlcm1pc3Npb25zKGJpdGZpZWxkOiBzdHJpbmcgfCBudW1iZXIpOiAoa2V5b2YgdHlwZW9mIFBFUk1JU1NJT05fQkFER0VTKVtdIHtcclxuICBpZiAoIWJpdGZpZWxkKSByZXR1cm4gW107XHJcbiAgaWYgKEFycmF5LmlzQXJyYXkoYml0ZmllbGQpKSByZXR1cm4gYml0ZmllbGQgYXMgYW55O1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBwZXJtaXNzaW9uczogKGtleW9mIHR5cGVvZiBQRVJNSVNTSU9OX0JBREdFUylbXSA9IFtdO1xyXG4gICAgY29uc3QgYml0cyA9IEJpZ0ludChiaXRmaWVsZCk7XHJcbiAgICBcclxuICAgIGZvciAoY29uc3QgW3Blcm1pc3Npb24sIGJpdF0gb2YgT2JqZWN0LmVudHJpZXMoUEVSTUlTU0lPTl9GTEFHX0JJVFMpKSB7XHJcbiAgICAgIGlmICgoYml0cyAmIGJpdCkgPT09IGJpdCkge1xyXG4gICAgICAgIHBlcm1pc3Npb25zLnB1c2gocGVybWlzc2lvbiBhcyBrZXlvZiB0eXBlb2YgUEVSTUlTU0lPTl9CQURHRVMpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBwZXJtaXNzaW9ucztcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVjb2RpbmcgcGVybWlzc2lvbnM6JywgZXJyb3IpO1xyXG4gICAgcmV0dXJuIFtdO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2VydmVyTWFuYWdlbWVudCgpIHtcclxuICBjb25zdCB0b2FzdCA9IHVzZVRvYXN0KCk7XHJcbiAgY29uc3QgeyBkaXNwbGF5TmFtZSB9ID0gdXNlR3VpbGRJbmZvKCk7XHJcbiAgY29uc3QgW2lzUmF0ZUxpbWl0ZWQsIHJlc2V0UmF0ZUxpbWl0XSA9IHVzZVJhdGVMaW1pdCgpO1xyXG4gIGNvbnN0IFtpc0J1bGtSYXRlTGltaXRlZCwgcmVzZXRCdWxrUmF0ZUxpbWl0XSA9IHVzZVJhdGVMaW1pdChCVUxLX1JBVEVfTElNSVRfTVMpO1xyXG4gIFxyXG4gIC8vIFN0YXRlIGZvciBndWlsZCBzZXR0aW5nc1xyXG4gIGNvbnN0IFtndWlsZERhdGEsIHNldEd1aWxkRGF0YV0gPSB1c2VTdGF0ZTxHdWlsZFNldHRpbmdzPih7XHJcbiAgICBwcmVmaXg6ICchJyxcclxuICAgIGJvdE5hbWU6ICdCb3QnLFxyXG4gICAgZ3VpbGROYW1lOiAnJyxcclxuICAgIGd1aWxkSWQ6ICcnLFxyXG4gICAgZ3VpbGRJY29uOiBudWxsLFxyXG4gICAgYWN0aXZpdGllczogW3sgdHlwZTogJ1BMQVlJTkcnLCBuYW1lOiAnd2l0aCBEaXNjb3JkLmpzJyB9XSxcclxuICAgIGFjdGl2aXR5Um90YXRpb25JbnRlcnZhbDogNjBcclxuICB9KTtcclxuICBcclxuICAvLyBTdGF0ZSBmb3Igcm9sZXMgYW5kIGNoYW5uZWxzXHJcbiAgY29uc3QgW3JvbGVzLCBzZXRSb2xlc10gPSB1c2VTdGF0ZTxSb2xlW10+KFtdKTtcclxuICBjb25zdCBbY2hhbm5lbHMsIHNldENoYW5uZWxzXSA9IHVzZVN0YXRlPENoYW5uZWxbXT4oW10pO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtjaGFubmVsc0xvYWRpbmcsIHNldENoYW5uZWxzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbc2F2aW5nLCBzZXRTYXZpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIFxyXG4gIC8vIE1vZGFsIHN0YXRlc1xyXG4gIGNvbnN0IHsgaXNPcGVuOiBpc0NyZWF0ZUNoYW5uZWxPcGVuLCBvbk9wZW46IG9uQ3JlYXRlQ2hhbm5lbE9wZW4sIG9uQ2xvc2U6IG9uQ3JlYXRlQ2hhbm5lbENsb3NlIH0gPSB1c2VEaXNjbG9zdXJlKCk7XHJcbiAgY29uc3QgeyBpc09wZW46IGlzRWRpdENoYW5uZWxPcGVuLCBvbk9wZW46IG9uRWRpdENoYW5uZWxPcGVuLCBvbkNsb3NlOiBvbkVkaXRDaGFubmVsQ2xvc2UgfSA9IHVzZURpc2Nsb3N1cmUoKTtcclxuICBjb25zdCB7IGlzT3BlbjogaXNDcmVhdGVSb2xlT3Blbiwgb25PcGVuOiBvbkNyZWF0ZVJvbGVPcGVuLCBvbkNsb3NlOiBvbkNyZWF0ZVJvbGVDbG9zZSB9ID0gdXNlRGlzY2xvc3VyZSgpO1xyXG4gIGNvbnN0IHsgaXNPcGVuOiBpc0VkaXRSb2xlT3Blbiwgb25PcGVuOiBvbkVkaXRSb2xlT3Blbiwgb25DbG9zZTogb25FZGl0Um9sZUNsb3NlIH0gPSB1c2VEaXNjbG9zdXJlKCk7XHJcbiAgY29uc3QgeyBpc09wZW46IGlzQ29sb3JCdWlsZGVyT3Blbiwgb25PcGVuOiBvbkNvbG9yQnVpbGRlck9wZW4sIG9uQ2xvc2U6IG9uQ29sb3JCdWlsZGVyQ2xvc2UgfSA9IHVzZURpc2Nsb3N1cmUoKTtcclxuICBcclxuICAvLyBTZWxlY3RlZCBpdGVtcyBmb3IgZWRpdGluZ1xyXG4gIGNvbnN0IFtzZWxlY3RlZFJvbGUsIHNldFNlbGVjdGVkUm9sZV0gPSB1c2VTdGF0ZTxSb2xlIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW3NlbGVjdGVkQ2hhbm5lbCwgc2V0U2VsZWN0ZWRDaGFubmVsXSA9IHVzZVN0YXRlPENoYW5uZWwgfCBudWxsPihudWxsKTtcclxuXHJcbiAgLy8gQnVsayBzZWxlY3Rpb24gc3RhdGVcclxuICBjb25zdCBbc2VsZWN0ZWRSb2xlcywgc2V0U2VsZWN0ZWRSb2xlc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pO1xyXG4gIGNvbnN0IFtzZWxlY3RlZENoYW5uZWxzLCBzZXRTZWxlY3RlZENoYW5uZWxzXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSk7XHJcbiAgY29uc3QgW2J1bGtEZWxldGluZywgc2V0QnVsa0RlbGV0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBcclxuICAvLyBGaWxlIHVwbG9hZCBzdGF0ZVxyXG4gIGNvbnN0IFtpY29uRmlsZSwgc2V0SWNvbkZpbGVdID0gdXNlU3RhdGU8RmlsZSB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtpY29uUHJldmlldywgc2V0SWNvblByZXZpZXddID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgZmlsZUlucHV0UmVmID0gdXNlUmVmPEhUTUxJbnB1dEVsZW1lbnQ+KG51bGwpO1xyXG5cclxuICBjb25zdCBoYW5kbGVJY29uRmlsZUNoYW5nZSA9IChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xyXG4gICAgY29uc3QgZmlsZSA9IGUudGFyZ2V0LmZpbGVzPy5bMF07XHJcbiAgICBpZiAoZmlsZSkge1xyXG4gICAgICBzZXRJY29uRmlsZShmaWxlKTtcclxuICAgICAgY29uc3QgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTtcclxuICAgICAgcmVhZGVyLm9ubG9hZCA9IChlKSA9PiBzZXRJY29uUHJldmlldyhlLnRhcmdldD8ucmVzdWx0IGFzIHN0cmluZyk7XHJcbiAgICAgIHJlYWRlci5yZWFkQXNEYXRhVVJMKGZpbGUpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IHVwbG9hZEljb24gPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIWljb25GaWxlKSByZXR1cm47XHJcbiAgICBcclxuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XHJcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ2ljb24nLCBpY29uRmlsZSk7XHJcbiAgICBcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvZGlzY29yZC9zZXR0aW5ncycsIHtcclxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICBib2R5OiBmb3JtRGF0YVxyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgICAgc2V0R3VpbGREYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgZ3VpbGRJY29uOiBkYXRhLmljb25VcmwgfSkpO1xyXG4gICAgICAgIHNldEljb25GaWxlKG51bGwpO1xyXG4gICAgICAgIHNldEljb25QcmV2aWV3KG51bGwpO1xyXG4gICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgIHRpdGxlOiAnU3VjY2VzcycsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ0d1aWxkIGljb24gdXBkYXRlZCBzdWNjZXNzZnVsbHknLFxyXG4gICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycsXHJcbiAgICAgICAgICBkdXJhdGlvbjogMzAwMFxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdGYWlsZWQgdG8gdXBsb2FkIGljb24nLFxyXG4gICAgICAgIHN0YXR1czogJ2Vycm9yJyxcclxuICAgICAgICBkdXJhdGlvbjogMzAwMFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBmZXRjaEd1aWxkRGF0YSA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IFtndWlsZFJlc3BvbnNlLCByb2xlc1Jlc3BvbnNlXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcclxuICAgICAgICBmZXRjaCgnL2FwaS9kaXNjb3JkL2d1aWxkJyksXHJcbiAgICAgICAgZmV0Y2goJy9hcGkvZGlzY29yZC9yb2xlcycpXHJcbiAgICAgIF0pO1xyXG5cclxuICAgICAgaWYgKGd1aWxkUmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zdCBndWlsZCA9IGF3YWl0IGd1aWxkUmVzcG9uc2UuanNvbigpO1xyXG4gICAgICAgIHNldEd1aWxkRGF0YShwcmV2ID0+ICh7XHJcbiAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgZ3VpbGROYW1lOiBndWlsZC5uYW1lLFxyXG4gICAgICAgICAgZ3VpbGRJZDogZ3VpbGQuaWQsXHJcbiAgICAgICAgICBndWlsZEljb246IGd1aWxkLmljb24sXHJcbiAgICAgICAgICBib3ROYW1lOiBndWlsZC5ib3ROYW1lIHx8IHByZXYuYm90TmFtZSAvLyBVc2UgQVBJIGJvdE5hbWUgb3IgZmFsbGJhY2sgdG8gY3VycmVudFxyXG4gICAgICAgIH0pKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKHJvbGVzUmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zdCByb2xlc0RhdGEgPSBhd2FpdCByb2xlc1Jlc3BvbnNlLmpzb24oKTtcclxuICAgICAgICBjb25zdCBhcnIgPSBBcnJheS5pc0FycmF5KHJvbGVzRGF0YSkgPyByb2xlc0RhdGEgOiByb2xlc0RhdGEucm9sZXMgfHwgW107XHJcbiAgICAgICAgc2V0Um9sZXMoYXJyLnNvcnQoKGE6IFJvbGUsIGI6IFJvbGUpID0+IGIucG9zaXRpb24gLSBhLnBvc2l0aW9uKSk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICB0aXRsZTogJ0Vycm9yJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJ0ZhaWxlZCB0byBmZXRjaCBndWlsZCBkYXRhJyxcclxuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXHJcbiAgICAgICAgZHVyYXRpb246IDMwMDBcclxuICAgICAgfSk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBmZXRjaENoYW5uZWxzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9kaXNjb3JkL2NoYW5uZWxzJyk7XHJcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBjaGFubmVscycpO1xyXG4gICAgICB9XHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIGNvbnN0IHNvcnRlZENoYW5uZWxzID0gKGRhdGEgfHwgW10pLnNvcnQoKGE6IENoYW5uZWwsIGI6IENoYW5uZWwpID0+IHtcclxuICAgICAgICBpZiAoYS50eXBlID09PSA0ICYmIGIudHlwZSAhPT0gNCkgcmV0dXJuIC0xO1xyXG4gICAgICAgIGlmIChhLnR5cGUgIT09IDQgJiYgYi50eXBlID09PSA0KSByZXR1cm4gMTtcclxuICAgICAgICByZXR1cm4gYS5wb3NpdGlvbiAtIGIucG9zaXRpb247XHJcbiAgICAgIH0pO1xyXG4gICAgICBzZXRDaGFubmVscyhzb3J0ZWRDaGFubmVscyk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdGYWlsZWQgdG8gZmV0Y2ggY2hhbm5lbHMnLFxyXG4gICAgICAgIHN0YXR1czogJ2Vycm9yJyxcclxuICAgICAgICBkdXJhdGlvbjogNTAwMCxcclxuICAgICAgfSk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRDaGFubmVsc0xvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBmZXRjaEd1aWxkRGF0YSgpO1xyXG4gICAgZmV0Y2hDaGFubmVscygpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2V0dGluZ0NoYW5nZSA9IChzZXR0aW5nOiBrZXlvZiBHdWlsZFNldHRpbmdzLCB2YWx1ZTogYW55KSA9PiB7XHJcbiAgICBzZXRHdWlsZERhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBbc2V0dGluZ106IHZhbHVlIH0pKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBzYXZlU2V0dGluZ3MgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoc2F2aW5nIHx8IGlzUmF0ZUxpbWl0ZWQpIHJldHVybjtcclxuICAgIFxyXG4gICAgc2V0U2F2aW5nKHRydWUpO1xyXG4gICAgcmVzZXRSYXRlTGltaXQoKTtcclxuICAgIFxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9kaXNjb3JkL3NldHRpbmdzJywge1xyXG4gICAgICAgIG1ldGhvZDogJ1BVVCcsXHJcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZ3VpbGREYXRhKVxyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgIHRpdGxlOiAnU3VjY2VzcycsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ1NldHRpbmdzIHNhdmVkIHN1Y2Nlc3NmdWxseScsXHJcbiAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcclxuICAgICAgICAgIGR1cmF0aW9uOiAzMDAwXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gc2F2ZSBzZXR0aW5ncycpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6ICdFcnJvcicsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdGYWlsZWQgdG8gc2F2ZSBzZXR0aW5ncycsXHJcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxyXG4gICAgICAgIGR1cmF0aW9uOiAzMDAwXHJcbiAgICAgIH0pO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0U2F2aW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVSb2xlRWRpdCA9IChyb2xlOiBSb2xlKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZFJvbGUocm9sZSk7XHJcbiAgICBvbkVkaXRSb2xlT3BlbigpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNoYW5uZWxFZGl0ID0gKGNoYW5uZWw6IENoYW5uZWwpID0+IHtcclxuICAgIHNldFNlbGVjdGVkQ2hhbm5lbChjaGFubmVsKTtcclxuICAgIG9uRWRpdENoYW5uZWxPcGVuKCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ2hhbm5lbENyZWF0ZSA9ICgpID0+IHtcclxuICAgIG9uQ3JlYXRlQ2hhbm5lbE9wZW4oKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVSb2xlQ3JlYXRlID0gKCkgPT4ge1xyXG4gICAgb25DcmVhdGVSb2xlT3BlbigpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNoYW5uZWxEZWxldGUgPSBhc3luYyAoY2hhbm5lbElkOiBzdHJpbmcpID0+IHtcclxuICAgIGlmIChpc1JhdGVMaW1pdGVkKSByZXR1cm47XHJcbiAgICBcclxuICAgIHRyeSB7XHJcbiAgICAgIHJlc2V0UmF0ZUxpbWl0KCk7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvZGlzY29yZC9jaGFubmVscy8ke2NoYW5uZWxJZH1gLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJ1xyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIGF3YWl0IGZldGNoQ2hhbm5lbHMoKTtcclxuICAgICAgICB0b2FzdCh7XHJcbiAgICAgICAgICB0aXRsZTogJ1N1Y2Nlc3MnLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdDaGFubmVsIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5JyxcclxuICAgICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnLFxyXG4gICAgICAgICAgZHVyYXRpb246IDMwMDBcclxuICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiAnRXJyb3InLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnRmFpbGVkIHRvIGRlbGV0ZSBjaGFubmVsJyxcclxuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXHJcbiAgICAgICAgZHVyYXRpb246IDMwMDBcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0UGFyZW50TmFtZSA9IChwYXJlbnRJZDogc3RyaW5nIHwgdW5kZWZpbmVkKSA9PiB7XHJcbiAgICBpZiAoIXBhcmVudElkIHx8ICFjaGFubmVscykgcmV0dXJuICctJztcclxuICAgIGNvbnN0IHBhcmVudCA9IGNoYW5uZWxzLmZpbmQoYyA9PiBjLmlkID09PSBwYXJlbnRJZCk7XHJcbiAgICByZXR1cm4gcGFyZW50ID8gcGFyZW50Lm5hbWUgOiAnLSc7XHJcbiAgfTtcclxuXHJcbiAgLy8gQnVsayBkZWxldGUgZnVuY3Rpb25zXHJcbiAgY29uc3QgaGFuZGxlQnVsa0RlbGV0ZVJvbGVzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKHNlbGVjdGVkUm9sZXMubGVuZ3RoID09PSAwIHx8IGJ1bGtEZWxldGluZyB8fCBpc0J1bGtSYXRlTGltaXRlZCkgcmV0dXJuO1xyXG5cclxuICAgIHNldEJ1bGtEZWxldGluZyh0cnVlKTtcclxuICAgIHJlc2V0QnVsa1JhdGVMaW1pdCgpO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvZGlzY29yZC9yb2xlcy9idWxrLWRlbGV0ZScsIHtcclxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyByb2xlSWRzOiBzZWxlY3RlZFJvbGVzIH0pLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgIHRpdGxlOiAnU3VjY2VzcycsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogcmVzdWx0Lm1lc3NhZ2UsXHJcbiAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcclxuICAgICAgICAgIGR1cmF0aW9uOiA1MDAwLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHNldFNlbGVjdGVkUm9sZXMoW10pO1xyXG4gICAgICAgIGZldGNoR3VpbGREYXRhKCk7IC8vIFJlZnJlc2ggcm9sZXNcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0LmVycm9yIHx8ICdGYWlsZWQgdG8gZGVsZXRlIHJvbGVzJyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICB0aXRsZTogJ0Vycm9yJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogZXJyb3IubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGRlbGV0ZSByb2xlcycsXHJcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxyXG4gICAgICAgIGR1cmF0aW9uOiA1MDAwLFxyXG4gICAgICB9KTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldEJ1bGtEZWxldGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQnVsa0RlbGV0ZUNoYW5uZWxzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKHNlbGVjdGVkQ2hhbm5lbHMubGVuZ3RoID09PSAwIHx8IGJ1bGtEZWxldGluZyB8fCBpc0J1bGtSYXRlTGltaXRlZCkgcmV0dXJuO1xyXG5cclxuICAgIHNldEJ1bGtEZWxldGluZyh0cnVlKTtcclxuICAgIHJlc2V0QnVsa1JhdGVMaW1pdCgpO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvZGlzY29yZC9jaGFubmVscy9idWxrLWRlbGV0ZScsIHtcclxuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBjaGFubmVsSWRzOiBzZWxlY3RlZENoYW5uZWxzIH0pLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xyXG4gICAgICAgIHRvYXN0KHtcclxuICAgICAgICAgIHRpdGxlOiAnU3VjY2VzcycsXHJcbiAgICAgICAgICBkZXNjcmlwdGlvbjogcmVzdWx0Lm1lc3NhZ2UsXHJcbiAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcclxuICAgICAgICAgIGR1cmF0aW9uOiA1MDAwLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHNldFNlbGVjdGVkQ2hhbm5lbHMoW10pO1xyXG4gICAgICAgIGZldGNoQ2hhbm5lbHMoKTsgLy8gUmVmcmVzaCBjaGFubmVsc1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihyZXN1bHQuZXJyb3IgfHwgJ0ZhaWxlZCB0byBkZWxldGUgY2hhbm5lbHMnKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiAnRXJyb3InLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gZGVsZXRlIGNoYW5uZWxzJyxcclxuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXHJcbiAgICAgICAgZHVyYXRpb246IDUwMDAsXHJcbiAgICAgIH0pO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0QnVsa0RlbGV0aW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBTZWxlY3Rpb24gaGVscGVyIGZ1bmN0aW9uc1xyXG4gIGNvbnN0IHRvZ2dsZVJvbGVTZWxlY3Rpb24gPSAocm9sZUlkOiBzdHJpbmcpID0+IHtcclxuICAgIHNldFNlbGVjdGVkUm9sZXMocHJldiA9PlxyXG4gICAgICBwcmV2LmluY2x1ZGVzKHJvbGVJZClcclxuICAgICAgICA/IHByZXYuZmlsdGVyKGlkID0+IGlkICE9PSByb2xlSWQpXHJcbiAgICAgICAgOiBbLi4ucHJldiwgcm9sZUlkXVxyXG4gICAgKTtcclxuICB9O1xyXG5cclxuICBjb25zdCB0b2dnbGVDaGFubmVsU2VsZWN0aW9uID0gKGNoYW5uZWxJZDogc3RyaW5nKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZENoYW5uZWxzKHByZXYgPT5cclxuICAgICAgcHJldi5pbmNsdWRlcyhjaGFubmVsSWQpXHJcbiAgICAgICAgPyBwcmV2LmZpbHRlcihpZCA9PiBpZCAhPT0gY2hhbm5lbElkKVxyXG4gICAgICAgIDogWy4uLnByZXYsIGNoYW5uZWxJZF1cclxuICAgICk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgc2VsZWN0QWxsUm9sZXMgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBzZWxlY3RhYmxlUm9sZXMgPSByb2xlcy5maWx0ZXIocm9sZSA9PiByb2xlLm5hbWUgIT09ICdAZXZlcnlvbmUnKS5tYXAocm9sZSA9PiByb2xlLmlkKTtcclxuICAgIHNldFNlbGVjdGVkUm9sZXMoc2VsZWN0ZWRSb2xlcy5sZW5ndGggPT09IHNlbGVjdGFibGVSb2xlcy5sZW5ndGggPyBbXSA6IHNlbGVjdGFibGVSb2xlcyk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgc2VsZWN0QWxsQ2hhbm5lbHMgPSAoKSA9PiB7XHJcbiAgICBjb25zdCBjaGFubmVsSWRzID0gY2hhbm5lbHMubWFwKGNoYW5uZWwgPT4gY2hhbm5lbC5pZCk7XHJcbiAgICBzZXRTZWxlY3RlZENoYW5uZWxzKHNlbGVjdGVkQ2hhbm5lbHMubGVuZ3RoID09PSBjaGFubmVsSWRzLmxlbmd0aCA/IFtdIDogY2hhbm5lbElkcyk7XHJcbiAgfTtcclxuXHJcbiAgaWYgKGxvYWRpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxMYXlvdXQ+XHJcbiAgICAgICAgPENvbnRhaW5lciBtYXhXPVwiY29udGFpbmVyLnhsXCIgcHk9ezh9PlxyXG4gICAgICAgICAgPFZTdGFjayBzcGFjaW5nPXs2fT5cclxuICAgICAgICAgICAgPFNrZWxldG9uIGhlaWdodD1cIjYwcHhcIiAvPlxyXG4gICAgICAgICAgICA8U2tlbGV0b24gaGVpZ2h0PVwiNDAwcHhcIiAvPlxyXG4gICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgPC9Db250YWluZXI+XHJcbiAgICAgIDwvTGF5b3V0PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8TGF5b3V0PlxyXG4gICAgICA8Q29udGFpbmVyIG1heFc9XCJjb250YWluZXIueGxcIiBweT17OH0+XHJcbiAgICAgICAgPFZTdGFjayBzcGFjaW5nPXs4fSBhbGlnbj1cInN0cmV0Y2hcIj5cclxuICAgICAgICAgIHsvKiBIZWFkZXIgKi99XHJcbiAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgIGJnPVwicmdiYSgyNTUsMjU1LDI1NSwwLjA4KVwiXHJcbiAgICAgICAgICAgIHA9ezh9XHJcbiAgICAgICAgICAgIHJvdW5kZWQ9XCIyeGxcIlxyXG4gICAgICAgICAgICBiYWNrZHJvcEZpbHRlcj1cImJsdXIoMTBweClcIlxyXG4gICAgICAgICAgICBib3JkZXI9XCIycHggc29saWRcIlxyXG4gICAgICAgICAgICBib3JkZXJDb2xvcj1cImJsdWUuNDAwXCJcclxuICAgICAgICAgICAgYm94U2hhZG93PVwiMCAwIDE1cHggcmdiYSg2NiwgMTUzLCAyMjUsIDAuNClcIlxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8SFN0YWNrIGp1c3RpZnk9XCJzcGFjZS1iZXR3ZWVuXCIgYWxpZ249XCJjZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8Qm94PlxyXG4gICAgICAgICAgICAgICAgPEhlYWRpbmdcclxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInhsXCJcclxuICAgICAgICAgICAgICAgICAgYmdHcmFkaWVudD1cImxpbmVhcih0by1yLCBibHVlLjMwMCwgcHVycGxlLjQwMClcIlxyXG4gICAgICAgICAgICAgICAgICBiZ0NsaXA9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPEljb24gYXM9e0ZpU2VydmVyfSBtcj17M30gLz5cclxuICAgICAgICAgICAgICAgICAgU2VydmVyIE1hbmFnZW1lbnRcclxuICAgICAgICAgICAgICAgIDwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwiZ3JheS4zMDBcIiBtdD17Mn0+XHJcbiAgICAgICAgICAgICAgICAgIENvbXByZWhlbnNpdmUgbWFuYWdlbWVudCBmb3Ige2Rpc3BsYXlOYW1lIHx8IGd1aWxkRGF0YS5ndWlsZE5hbWV9XHJcbiAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgbGVmdEljb249ezxGaVNhdmUgLz59XHJcbiAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cImJsdWVcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17c2F2ZVNldHRpbmdzfVxyXG4gICAgICAgICAgICAgICAgaXNMb2FkaW5nPXtzYXZpbmd9XHJcbiAgICAgICAgICAgICAgICBpc0Rpc2FibGVkPXtpc1JhdGVMaW1pdGVkfVxyXG4gICAgICAgICAgICAgICAgc2l6ZT1cImxnXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBTYXZlIFNldHRpbmdzXHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvSFN0YWNrPlxyXG4gICAgICAgICAgPC9Cb3g+XHJcblxyXG4gICAgICAgICAgey8qIE1haW4gQ29udGVudCBUYWJzICovfVxyXG4gICAgICAgICAgPFRhYnMgY29sb3JTY2hlbWU9XCJibHVlXCIgaXNMYXp5PlxyXG4gICAgICAgICAgICA8VGFiTGlzdD5cclxuICAgICAgICAgICAgICA8VGFiPlxyXG4gICAgICAgICAgICAgICAgPEljb24gYXM9e0ZpU2V0dGluZ3N9IG1yPXsyfSAvPlxyXG4gICAgICAgICAgICAgICAgR2VuZXJhbCBTZXR0aW5nc1xyXG4gICAgICAgICAgICAgIDwvVGFiPlxyXG4gICAgICAgICAgICAgIDxUYWI+XHJcbiAgICAgICAgICAgICAgICA8SWNvbiBhcz17RmFQYWxldHRlfSBtcj17Mn0gLz5cclxuICAgICAgICAgICAgICAgIFRoZW1lIEJ1aWxkZXJcclxuICAgICAgICAgICAgICA8L1RhYj5cclxuICAgICAgICAgICAgICA8VGFiPlxyXG4gICAgICAgICAgICAgICAgPEljb24gYXM9e0ZpVG9vbH0gbXI9ezJ9IC8+XHJcbiAgICAgICAgICAgICAgICBCdWlsZGVyc1xyXG4gICAgICAgICAgICAgIDwvVGFiPlxyXG4gICAgICAgICAgICAgIDxUYWI+XHJcbiAgICAgICAgICAgICAgICA8SWNvbiBhcz17RmlaYXB9IG1yPXsyfSAvPlxyXG4gICAgICAgICAgICAgICAgQXV0b21hdGlvblxyXG4gICAgICAgICAgICAgIDwvVGFiPlxyXG4gICAgICAgICAgICA8L1RhYkxpc3Q+XHJcblxyXG4gICAgICAgICAgICA8VGFiUGFuZWxzPlxyXG4gICAgICAgICAgICAgIHsvKiBHZW5lcmFsIFNldHRpbmdzIFRhYiAqL31cclxuICAgICAgICAgICAgICA8VGFiUGFuZWw+XHJcbiAgICAgICAgICAgICAgICA8VlN0YWNrIHNwYWNpbmc9ezh9IGFsaWduPVwic3RyZXRjaFwiPlxyXG4gICAgICAgICAgICAgICAgICB7LyogQmFzaWMgU2V0dGluZ3MgKi99XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEhlYWRpbmcgc2l6ZT1cIm1kXCI+QmFzaWMgU2V0dGluZ3M8L0hlYWRpbmc+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxTaW1wbGVHcmlkIGNvbHVtbnM9e3sgYmFzZTogMSwgbGc6IDIgfX0gc3BhY2luZz17Nn0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17NH0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1Db250cm9sPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1MYWJlbD5Cb3QgTmFtZTwvRm9ybUxhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtndWlsZERhdGEuYm90TmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVTZXR0aW5nQ2hhbmdlKCdib3ROYW1lJywgZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGJvdCBuYW1lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Gb3JtQ29udHJvbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUNvbnRyb2w+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUxhYmVsPkNvbW1hbmQgUHJlZml4PC9Gb3JtTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2d1aWxkRGF0YS5wcmVmaXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2V0dGluZ0NoYW5nZSgncHJlZml4JywgZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGNvbW1hbmQgcHJlZml4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Gb3JtQ29udHJvbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17NH0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1Db250cm9sPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1MYWJlbD5TZXJ2ZXIgTmFtZTwvRm9ybUxhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtndWlsZERhdGEuZ3VpbGROYW1lIHx8ICcnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1JlYWRPbmx5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJnPVwiZ3JheS41MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9kYXJrPXt7IGJnOiAnZ3JheS43MDAnIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvRm9ybUNvbnRyb2w+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1Db250cm9sPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1MYWJlbD5TZXJ2ZXIgSUQ8L0Zvcm1MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Z3VpbGREYXRhLmd1aWxkSWQgfHwgJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUmVhZE9ubHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmc9XCJncmF5LjUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2Rhcms9e3sgYmc6ICdncmF5LjcwMCcgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseT1cIm1vbm9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Gb3JtQ29udHJvbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1NpbXBsZUdyaWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgey8qIFJvbGVzIFNlY3Rpb24gKi99XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFZTdGFjayBzcGFjaW5nPXs0fSBhbGlnbj1cInN0cmV0Y2hcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEhTdGFjayBqdXN0aWZ5PVwic3BhY2UtYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxIZWFkaW5nIHNpemU9XCJtZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb24gYXM9e0ZpVXNlcnN9IG1yPXsyfSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgUm9sZXMgKHtyb2xlcy5sZW5ndGh9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SFN0YWNrIHNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWZ0SWNvbj17PEZpUGx1cyAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3JTY2hlbWU9XCJncmVlblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVJvbGVDcmVhdGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzRGlzYWJsZWQ9e2lzUmF0ZUxpbWl0ZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIENyZWF0ZSBSb2xlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0hTdGFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9IU3RhY2s+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRSb2xlcy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SFN0YWNrIGp1c3RpZnk9XCJzcGFjZS1iZXR3ZWVuXCIgcD17M30gYmc9XCJibHVlLjUwXCIgX2Rhcms9e3sgYmc6ICdibHVlLjkwMCcgfX0gYm9yZGVyUmFkaXVzPVwibWRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwic21cIiBmb250V2VpZ2h0PVwibWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFJvbGVzLmxlbmd0aH0gcm9sZShzKSBzZWxlY3RlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEhTdGFjayBzcGFjaW5nPXsyfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkUm9sZXMoW10pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2xlYXIgU2VsZWN0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cInJlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVmdEljb249ezxGaVRyYXNoMiAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVCdWxrRGVsZXRlUm9sZXN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNMb2FkaW5nPXtidWxrRGVsZXRpbmd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNEaXNhYmxlZD17aXNCdWxrUmF0ZUxpbWl0ZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBEZWxldGUgU2VsZWN0ZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0hTdGFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0hTdGFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVlN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICA8Q2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7bG9hZGluZyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPFZTdGFjayBzcGFjaW5nPXszfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Wy4uLkFycmF5KDMpXS5tYXAoKF8sIGkpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTa2VsZXRvbiBrZXk9e2l9IGhlaWdodD1cIjYwcHhcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1ZTdGFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxCb3ggb3ZlcmZsb3dYPVwiYXV0b1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZSB2YXJpYW50PVwic2ltcGxlXCIgc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGhlYWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNDaGVja2VkPXtzZWxlY3RlZFJvbGVzLmxlbmd0aCA9PT0gcm9sZXMuZmlsdGVyKHIgPT4gci5uYW1lICE9PSAnQGV2ZXJ5b25lJykubGVuZ3RoICYmIHJvbGVzLmxlbmd0aCA+IDF9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzSW5kZXRlcm1pbmF0ZT17c2VsZWN0ZWRSb2xlcy5sZW5ndGggPiAwICYmIHNlbGVjdGVkUm9sZXMubGVuZ3RoIDwgcm9sZXMuZmlsdGVyKHIgPT4gci5uYW1lICE9PSAnQGV2ZXJ5b25lJykubGVuZ3RofVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17c2VsZWN0QWxsUm9sZXN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRoPlJvbGU8L1RoPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUaD5NZW1iZXJzPC9UaD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGg+UGVybWlzc2lvbnM8L1RoPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUaD5BY3Rpb25zPC9UaD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ucj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGhlYWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsocm9sZXMgfHwgW10pLm1hcCgocm9sZSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUciBrZXk9e3JvbGUuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0NoZWNrZWQ9e3NlbGVjdGVkUm9sZXMuaW5jbHVkZXMocm9sZS5pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eygpID0+IHRvZ2dsZVJvbGVTZWxlY3Rpb24ocm9sZS5pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNEaXNhYmxlZD17cm9sZS5uYW1lID09PSAnQGV2ZXJ5b25lJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxIU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdz17NH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGg9ezR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3VuZGVkPVwiZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiZz17cm9sZS5jb2xvciA/IGAjJHtyb2xlLmNvbG9yLnRvU3RyaW5nKDE2KS5wYWRTdGFydCg2LCAnMCcpfWAgOiAnZ3JheS41MDAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQ+e3JvbGUubmFtZX08L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSFN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNvbG9yU2NoZW1lPVwiYmx1ZVwiPjA8L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEhTdGFjayB3cmFwPVwid3JhcFwiIHNwYWNpbmc9ezF9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsoZGVjb2RlUGVybWlzc2lvbnMocm9sZS5wZXJtaXNzaW9ucykgfHwgW10pLnNsaWNlKDAsIDMpLm1hcCgocGVybSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17cGVybX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3JTY2hlbWU9e1BFUk1JU1NJT05fQkFER0VTW3Blcm1dPy5jb2xvciB8fCAnZ3JheSd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtQRVJNSVNTSU9OX0JBREdFU1twZXJtXT8ubGFiZWwgfHwgcGVybX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2RlY29kZVBlcm1pc3Npb25zKHJvbGUucGVybWlzc2lvbnMpLmxlbmd0aCA+IDMgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNvbG9yU2NoZW1lPVwiZ3JheVwiIHNpemU9XCJzbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAre2RlY29kZVBlcm1pc3Npb25zKHJvbGUucGVybWlzc2lvbnMpLmxlbmd0aCAtIDN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSFN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEhTdGFjayBzcGFjaW5nPXsyfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBsYWJlbD1cIkVkaXQgUm9sZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25CdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkVkaXQgcm9sZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb249ezxGaUVkaXQyIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cImJsdWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVSb2xlRWRpdChyb2xlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNEaXNhYmxlZD17aXNSYXRlTGltaXRlZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGxhYmVsPVwiRGVsZXRlIFJvbGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJEZWxldGUgcm9sZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb249ezxGaVRyYXNoMiAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3JTY2hlbWU9XCJyZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkPXtpc1JhdGVMaW1pdGVkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSFN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgIHsvKiBDaGFubmVscyBTZWN0aW9uICovfVxyXG4gICAgICAgICAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17NH0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxIU3RhY2sganVzdGlmeT1cInNwYWNlLWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SGVhZGluZyBzaXplPVwibWRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uIGFzPXtGaUhhc2h9IG1yPXsyfSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2hhbm5lbHMgKHtjaGFubmVscy5sZW5ndGh9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SFN0YWNrIHNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWZ0SWNvbj17PEZpUGx1cyAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3JTY2hlbWU9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17b25DcmVhdGVDaGFubmVsT3Blbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ3JlYXRlIENoYW5uZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvSFN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0hTdGFjaz5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZENoYW5uZWxzLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxIU3RhY2sganVzdGlmeT1cInNwYWNlLWJldHdlZW5cIiBwPXszfSBiZz1cImJsdWUuNTBcIiBfZGFyaz17eyBiZzogJ2JsdWUuOTAwJyB9fSBib3JkZXJSYWRpdXM9XCJtZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiIGZvbnRXZWlnaHQ9XCJtZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkQ2hhbm5lbHMubGVuZ3RofSBjaGFubmVsKHMpIHNlbGVjdGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SFN0YWNrIHNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRDaGFubmVscyhbXSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDbGVhciBTZWxlY3Rpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwicmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWZ0SWNvbj17PEZpVHJhc2gyIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUJ1bGtEZWxldGVDaGFubmVsc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0xvYWRpbmc9e2J1bGtEZWxldGluZ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkPXtpc0J1bGtSYXRlTGltaXRlZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIERlbGV0ZSBTZWxlY3RlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSFN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvSFN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgIHtjaGFubmVsc0xvYWRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17M30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge1suLi5BcnJheSg1KV0ubWFwKChfLCBpKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2tlbGV0b24ga2V5PXtpfSBoZWlnaHQ9XCI1MHB4XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICApIDogY2hhbm5lbHMubGVuZ3RoID09PSAwID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cImdyYXkuNTAwXCIgdGV4dEFsaWduPVwiY2VudGVyXCIgcHk9ezh9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIE5vIGNoYW5uZWxzIGZvdW5kXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxCb3ggb3ZlcmZsb3dYPVwiYXV0b1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZSB2YXJpYW50PVwic2ltcGxlXCIgc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGhlYWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNDaGVja2VkPXtzZWxlY3RlZENoYW5uZWxzLmxlbmd0aCA9PT0gY2hhbm5lbHMubGVuZ3RoICYmIGNoYW5uZWxzLmxlbmd0aCA+IDB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzSW5kZXRlcm1pbmF0ZT17c2VsZWN0ZWRDaGFubmVscy5sZW5ndGggPiAwICYmIHNlbGVjdGVkQ2hhbm5lbHMubGVuZ3RoIDwgY2hhbm5lbHMubGVuZ3RofVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17c2VsZWN0QWxsQ2hhbm5lbHN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRoPk5hbWU8L1RoPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUaD5UeXBlPC9UaD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGg+Q2F0ZWdvcnk8L1RoPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUaD5Qb3NpdGlvbjwvVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRoPkFjdGlvbnM8L1RoPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UaGVhZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyhjaGFubmVscyB8fCBbXSkubWFwKChjaGFubmVsKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdHlwZUNvbmZpZyA9IENIQU5ORUxfVFlQRV9DT05GSUdbY2hhbm5lbC50eXBlXSB8fCB7IGljb246IEZpTWVzc2FnZVNxdWFyZSwgY29sb3I6ICdncmF5JywgbGFiZWw6ICdPdGhlcicgfTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyIGtleT17Y2hhbm5lbC5pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzQ2hlY2tlZD17c2VsZWN0ZWRDaGFubmVscy5pbmNsdWRlcyhjaGFubmVsLmlkKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoKSA9PiB0b2dnbGVDaGFubmVsU2VsZWN0aW9uKGNoYW5uZWwuaWQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SFN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXM9e3R5cGVDb25maWcuaWNvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9e2Ake3R5cGVDb25maWcuY29sb3J9LjQwMGB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQ+e2NoYW5uZWwubmFtZX08L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9IU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY29sb3JTY2hlbWU9e3R5cGVDb25maWcuY29sb3J9Pnt0eXBlQ29uZmlnLmxhYmVsfTwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cImdyYXkuNTAwXCI+e2dldFBhcmVudE5hbWUoY2hhbm5lbC5wYXJlbnRfaWQpfTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwiZ3JheS41MDBcIj57Y2hhbm5lbC5wb3NpdGlvbn08L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SFN0YWNrIHNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgbGFiZWw9XCJFZGl0IENoYW5uZWxcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25CdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiRWRpdCBjaGFubmVsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uPXs8RmlFZGl0MiAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3JTY2hlbWU9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVDaGFubmVsRWRpdChjaGFubmVsKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkPXtpc1JhdGVMaW1pdGVkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgbGFiZWw9XCJEZWxldGUgQ2hhbm5lbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJEZWxldGUgY2hhbm5lbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbj17PEZpVHJhc2gyIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cInJlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQ2hhbm5lbERlbGV0ZShjaGFubmVsLmlkKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkPXtpc1JhdGVMaW1pdGVkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSFN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ucj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cclxuICAgICAgICAgICAgICAgIDwvVlN0YWNrPlxyXG4gICAgICAgICAgICAgIDwvVGFiUGFuZWw+XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBUaGVtZSBCdWlsZGVyIFRhYiAqL31cclxuICAgICAgICAgICAgICA8VGFiUGFuZWw+XHJcbiAgICAgICAgICAgICAgICA8VlN0YWNrIHNwYWNpbmc9ezZ9IGFsaWduPVwic3RyZXRjaFwiPlxyXG4gICAgICAgICAgICAgICAgICA8Qm94PlxyXG4gICAgICAgICAgICAgICAgICAgIDxIZWFkaW5nIHNpemU9XCJtZFwiIG1iPXs0fT7wn46oIFRoZW1lIEJ1aWxkZXI8L0hlYWRpbmc+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCJncmF5LjYwMFwiIF9kYXJrPXt7IGNvbG9yOiAnZ3JheS4zMDAnIH19IG1iPXs2fT5cclxuICAgICAgICAgICAgICAgICAgICAgIENyZWF0ZSBhbmQgY3VzdG9taXplIHlvdXIgb3duIHRoZW1lcyB3aXRoIHRoZSBhZHZhbmNlZCBjb2xvciBidWlsZGVyXHJcbiAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICA8L0JveD5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxTaW1wbGVHcmlkIGNvbHVtbnM9e3sgYmFzZTogMSwgbGc6IDIgfX0gc3BhY2luZz17Nn0+XHJcbiAgICAgICAgICAgICAgICAgICAgPENhcmQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEhlYWRpbmcgc2l6ZT1cInNtXCI+Q3VzdG9tIFRoZW1lIEJ1aWxkZXI8L0hlYWRpbmc+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17NH0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiIGNvbG9yPVwiZ3JheS41MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENyZWF0ZSB5b3VyIG93biBjdXN0b20gdGhlbWVzIHdpdGggZnVsbCBjb2xvciBjb250cm9sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmFQYWxldHRlIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3JTY2hlbWU9XCJwdXJwbGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17b25Db2xvckJ1aWxkZXJPcGVufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cImxnXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBPcGVuIENvbG9yIEJ1aWxkZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9WU3RhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRCb2R5PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPENhcmQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEhlYWRpbmcgc2l6ZT1cInNtXCI+VGhlbWUgUHJlc2V0czwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFZTdGFjayBzcGFjaW5nPXszfSBhbGlnbj1cInN0cmV0Y2hcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT1cInNtXCIgY29sb3I9XCJncmF5LjUwMFwiIG1iPXsyfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFF1aWNrIHRoZW1lIG9wdGlvbnMgYXZhaWxhYmxlIGluIHRoZSBuYXZpZ2F0aW9uIGJhclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SFN0YWNrIHdyYXA9XCJ3cmFwXCIgc3BhY2luZz17Mn0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY29sb3JTY2hlbWU9XCJibHVlXCI+RGFyazwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY29sb3JTY2hlbWU9XCJwdXJwbGVcIj5NaWRuaWdodDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY29sb3JTY2hlbWU9XCJncmVlblwiPkZvcmVzdDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY29sb3JTY2hlbWU9XCJvcmFuZ2VcIj5TdW5zZXQ8L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNvbG9yU2NoZW1lPVwicGlua1wiPlJvc2U8L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvSFN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1ZTdGFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICAgICAgICA8L1NpbXBsZUdyaWQ+XHJcbiAgICAgICAgICAgICAgICA8L1ZTdGFjaz5cclxuICAgICAgICAgICAgICA8L1RhYlBhbmVsPlxyXG5cclxuICAgICAgICAgICAgICB7LyogQnVpbGRlcnMgVGFiICovfVxyXG4gICAgICAgICAgICAgIDxUYWJQYW5lbD5cclxuICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17Nn0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxCb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgPEhlYWRpbmcgc2l6ZT1cIm1kXCIgbWI9ezR9PvCfm6DvuI8gQnVpbGRlcnMgJiBUb29sczwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cImdyYXkuNjAwXCIgX2Rhcms9e3sgY29sb3I6ICdncmF5LjMwMCcgfX0gbWI9ezZ9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgQ3JlYXRlIGN1c3RvbSBjb250ZW50IGFuZCBtYW5hZ2Ugc2VydmVyIGZlYXR1cmVzIHdpdGggcG93ZXJmdWwgYnVpbGRlcnNcclxuICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG5cclxuICAgICAgICAgICAgICAgICAgPFNpbXBsZUdyaWQgY29sdW1ucz17eyBiYXNlOiAxLCBsZzogMiB9fSBzcGFjaW5nPXs2fT5cclxuICAgICAgICAgICAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8SGVhZGluZyBzaXplPVwic21cIj5Db250ZW50IEJ1aWxkZXJzPC9IZWFkaW5nPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRCb2R5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VlN0YWNrIHNwYWNpbmc9ezR9IGFsaWduPVwic3RyZXRjaFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmlaYXAgLz59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cImdyZWVuXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5vcGVuKCcvYWRtaW4vZXhwZXJpbWVudGFsL2FkZG9uLWJ1aWxkZXInLCAnX2JsYW5rJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQWRkb24gQnVpbGRlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmlNZXNzYWdlU3F1YXJlIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3JTY2hlbWU9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5vcGVuKCcvYWRtaW4vYXBwbGljYXRpb25zLWJ1aWxkZXInLCAnX2JsYW5rJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQXBwbGljYXRpb25zIEJ1aWxkZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWZ0SWNvbj17PEZpTWVzc2FnZVNxdWFyZSAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwicHVycGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5vcGVuKCcvYWRtaW4vZW1iZWQtYnVpbGRlcicsICdfYmxhbmsnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzRGlzYWJsZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBNZXNzYWdlIEJ1aWxkZXIgKENvbWluZyBTb29uKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1ZTdGFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8SGVhZGluZyBzaXplPVwic21cIj5NYW5hZ2VtZW50IFRvb2xzPC9IZWFkaW5nPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRCb2R5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VlN0YWNrIHNwYWNpbmc9ezR9IGFsaWduPVwic3RyZXRjaFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmlTZXR0aW5ncyAvPn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yU2NoZW1lPVwib3JhbmdlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5vcGVuKCcvYWRtaW4vYWRkb25zJywgJ19ibGFuaycpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1hbmFnZSBBZGRvbnNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWZ0SWNvbj17PEZpU2V0dGluZ3MgLz59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cInRlYWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4oJy9hZG1pbi9jb21tYW5kcycsICdfYmxhbmsnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDb21tYW5kIE1hbmFnZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWZ0SWNvbj17PEZpU2V0dGluZ3MgLz59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cImN5YW5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4oJy9hZG1pbi9hcHBsaWNhdGlvbnMnLCAnX2JsYW5rJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQXBwbGljYXRpb24gTWFuYWdlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1ZTdGFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICAgICAgICA8L1NpbXBsZUdyaWQ+XHJcbiAgICAgICAgICAgICAgICA8L1ZTdGFjaz5cclxuICAgICAgICAgICAgICA8L1RhYlBhbmVsPlxyXG5cclxuICAgICAgICAgICAgICB7LyogQXV0b21hdGlvbiBUYWIgKi99XHJcbiAgICAgICAgICAgICAgPFRhYlBhbmVsPlxyXG4gICAgICAgICAgICAgICAgPFZTdGFjayBzcGFjaW5nPXs2fSBhbGlnbj1cInN0cmV0Y2hcIj5cclxuICAgICAgICAgICAgICAgICAgPEJveD5cclxuICAgICAgICAgICAgICAgICAgICA8SGVhZGluZyBzaXplPVwibWRcIiBtYj17NH0+4pqhIEF1dG9tYXRpb24gJiBBY3Rpdml0aWVzPC9IZWFkaW5nPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0IGNvbG9yPVwiZ3JheS42MDBcIiBfZGFyaz17eyBjb2xvcjogJ2dyYXkuMzAwJyB9fSBtYj17Nn0+XHJcbiAgICAgICAgICAgICAgICAgICAgICBTZXQgdXAgYXV0b21hdGVkIGZlYXR1cmVzIGFuZCBzZXJ2ZXIgYWN0aXZpdGllc1xyXG4gICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8U2ltcGxlR3JpZCBjb2x1bW5zPXt7IGJhc2U6IDEsIGxnOiAyIH19IHNwYWNpbmc9ezZ9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxIZWFkaW5nIHNpemU9XCJzbVwiPkFjdGl2aXR5IFRlbXBsYXRlczwvSGVhZGluZz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCJncmF5LjUwMFwiIGZvbnRTaXplPVwic21cIiBtYj17NH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgUHJlLWJ1aWx0IGFjdGl2aXR5IHRlbXBsYXRlcyB0byBnZXQgeW91IHN0YXJ0ZWQgcXVpY2tseTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VlN0YWNrIHNwYWNpbmc9ezJ9IGFsaWduPVwic3RyZXRjaFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwic21cIj7igKIgRXZlbnQgTWFuYWdlbWVudCBTeXN0ZW08L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiPuKAoiBXZWxjb21lICYgT25ib2FyZGluZyBGbG93PC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwic21cIj7igKIgTW9kZXJhdGlvbiBBdXRvbWF0aW9uPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwic21cIj7igKIgQ3VzdG9tIENvbW1hbmRzPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPVwic21cIj7igKIgQXV0by1Sb2xlIEFzc2lnbm1lbnQ8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiPuKAoiBTY2hlZHVsZWQgTWVzc2FnZXM8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVlN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxIZWFkaW5nIHNpemU9XCJzbVwiPkF1dG9tYXRpb24gU2V0dGluZ3M8L0hlYWRpbmc+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEJvZHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxWU3RhY2sgc3BhY2luZz17NH0gYWxpZ249XCJzdHJldGNoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9XCJzbVwiIGNvbG9yPVwiZ3JheS41MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENvbmZpZ3VyZSBhdXRvbWF0ZWQgc2VydmVyIGZlYXR1cmVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmlaYXAgLz59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cInllbGxvd1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQXV0by1Nb2RlcmF0aW9uIChDb21pbmcgU29vbilcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWZ0SWNvbj17PEZpWmFwIC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3JTY2hlbWU9XCJncmVlblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0Rpc2FibGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgV2VsY29tZSBTeXN0ZW0gKENvbWluZyBTb29uKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnRJY29uPXs8RmlaYXAgLz59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvclNjaGVtZT1cImJsdWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNEaXNhYmxlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEV2ZW50IFNjaGVkdWxlciAoQ29taW5nIFNvb24pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVlN0YWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQm9keT5cclxuICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgICAgICAgIDwvU2ltcGxlR3JpZD5cclxuICAgICAgICAgICAgICAgIDwvVlN0YWNrPlxyXG4gICAgICAgICAgICAgIDwvVGFiUGFuZWw+XHJcbiAgICAgICAgICAgIDwvVGFiUGFuZWxzPlxyXG4gICAgICAgICAgPC9UYWJzPlxyXG4gICAgICAgIDwvVlN0YWNrPlxyXG5cclxuICAgICAgICB7LyogTW9kYWxzICovfVxyXG4gICAgICAgIDxTdXNwZW5zZSBmYWxsYmFjaz17PFNwaW5uZXIgLz59PlxyXG4gICAgICAgICAgPENyZWF0ZUNoYW5uZWxEaWFsb2dcclxuICAgICAgICAgICAgaXNPcGVuPXtpc0NyZWF0ZUNoYW5uZWxPcGVufVxyXG4gICAgICAgICAgICBvbkNsb3NlPXtvbkNyZWF0ZUNoYW5uZWxDbG9zZX1cclxuICAgICAgICAgICAgb25TdWNjZXNzPXtmZXRjaENoYW5uZWxzfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L1N1c3BlbnNlPlxyXG5cclxuICAgICAgICA8U3VzcGVuc2UgZmFsbGJhY2s9ezxTcGlubmVyIC8+fT5cclxuICAgICAgICAgIDxFZGl0Q2hhbm5lbERpYWxvZ1xyXG4gICAgICAgICAgICBpc09wZW49e2lzRWRpdENoYW5uZWxPcGVufVxyXG4gICAgICAgICAgICBvbkNsb3NlPXtvbkVkaXRDaGFubmVsQ2xvc2V9XHJcbiAgICAgICAgICAgIGNoYW5uZWw9e3NlbGVjdGVkQ2hhbm5lbH1cclxuICAgICAgICAgICAgY2F0ZWdvcmllcz17Y2hhbm5lbHMuZmlsdGVyKGMgPT4gYy50eXBlID09PSA0KX1cclxuICAgICAgICAgICAgb25TdWNjZXNzPXtmZXRjaENoYW5uZWxzfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L1N1c3BlbnNlPlxyXG5cclxuICAgICAgICA8U3VzcGVuc2UgZmFsbGJhY2s9ezxTcGlubmVyIC8+fT5cclxuICAgICAgICAgIDxDcmVhdGVSb2xlRGlhbG9nXHJcbiAgICAgICAgICAgIGlzT3Blbj17aXNDcmVhdGVSb2xlT3Blbn1cclxuICAgICAgICAgICAgb25DbG9zZT17b25DcmVhdGVSb2xlQ2xvc2V9XHJcbiAgICAgICAgICAgIG9uU3VjY2Vzcz17ZmV0Y2hHdWlsZERhdGF9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvU3VzcGVuc2U+XHJcblxyXG4gICAgICAgIDxTdXNwZW5zZSBmYWxsYmFjaz17PFNwaW5uZXIgLz59PlxyXG4gICAgICAgICAgPEVkaXRSb2xlRGlhbG9nXHJcbiAgICAgICAgICAgIGlzT3Blbj17aXNFZGl0Um9sZU9wZW59XHJcbiAgICAgICAgICAgIG9uQ2xvc2U9e29uRWRpdFJvbGVDbG9zZX1cclxuICAgICAgICAgICAgcm9sZT17c2VsZWN0ZWRSb2xlfVxyXG4gICAgICAgICAgICBvblN1Y2Nlc3M9e2ZldGNoR3VpbGREYXRhfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L1N1c3BlbnNlPlxyXG5cclxuICAgICAgICA8U3VzcGVuc2UgZmFsbGJhY2s9ezxTcGlubmVyIC8+fT5cclxuICAgICAgICAgIDxDb2xvckJ1aWxkZXJcclxuICAgICAgICAgICAgaXNPcGVuPXtpc0NvbG9yQnVpbGRlck9wZW59XHJcbiAgICAgICAgICAgIG9uQ2xvc2U9e29uQ29sb3JCdWlsZGVyQ2xvc2V9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvU3VzcGVuc2U+XHJcbiAgICAgIDwvQ29udGFpbmVyPlxyXG4gICAgPC9MYXlvdXQ+XHJcbiAgKTtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IGdldFNlcnZlclNpZGVQcm9wczogR2V0U2VydmVyU2lkZVByb3BzID0gYXN5bmMgKGN0eCkgPT4ge1xyXG4gIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKGN0eC5yZXEsIGN0eC5yZXMsIGF1dGhPcHRpb25zKTtcclxuXHJcbiAgaWYgKCFzZXNzaW9uKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICByZWRpcmVjdDoge1xyXG4gICAgICAgIGRlc3RpbmF0aW9uOiAnL2FwaS9hdXRoL3NpZ25pbj9jYWxsYmFja1VybD0lMkZhZG1pbiUyRmd1aWxkcycsXHJcbiAgICAgICAgcGVybWFuZW50OiBmYWxzZSxcclxuICAgICAgfSxcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICByZXR1cm4geyBwcm9wczogeyBzZXNzaW9uIH0gfTtcclxufTtcclxuIl0sIm5hbWVzIjpbIkJveCIsIkNvbnRhaW5lciIsIkhlYWRpbmciLCJUZXh0IiwiU2ltcGxlR3JpZCIsIlZTdGFjayIsIkhTdGFjayIsIkljb24iLCJ1c2VUb2FzdCIsIkNhcmQiLCJDYXJkSGVhZGVyIiwiQ2FyZEJvZHkiLCJCdXR0b24iLCJJbnB1dCIsIkZvcm1Db250cm9sIiwiRm9ybUxhYmVsIiwiQmFkZ2UiLCJTa2VsZXRvbiIsInVzZURpc2Nsb3N1cmUiLCJUYWJzIiwiVGFiTGlzdCIsIlRhYlBhbmVscyIsIlRhYiIsIlRhYlBhbmVsIiwiVGFibGUiLCJUaGVhZCIsIlRib2R5IiwiVHIiLCJUaCIsIlRkIiwiSWNvbkJ1dHRvbiIsIlRvb2x0aXAiLCJDaGVja2JveCIsIlNwaW5uZXIiLCJMYXlvdXQiLCJ1c2VHdWlsZEluZm8iLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlUmVmIiwiU3VzcGVuc2UiLCJGaVNldHRpbmdzIiwiRmlVc2VycyIsIkZpSGFzaCIsIkZpRWRpdDIiLCJGaVNhdmUiLCJGaVRyYXNoMiIsIkZpUGx1cyIsIkZpTWVzc2FnZVNxdWFyZSIsIkZpVm9sdW1lMiIsIkZpRm9sZGVyUGx1cyIsIkZpUmFkaW8iLCJGaUxvY2siLCJGaU1lc3NhZ2VDaXJjbGUiLCJGaVNlcnZlciIsIkZpWmFwIiwiRmlUb29sIiwiRmFQYWxldHRlIiwiZHluYW1pYyIsIkNyZWF0ZUNoYW5uZWxEaWFsb2ciLCJsb2FkaW5nIiwic2l6ZSIsInNzciIsIkVkaXRDaGFubmVsRGlhbG9nIiwiRWRpdFJvbGVEaWFsb2ciLCJDb2xvckJ1aWxkZXIiLCJDcmVhdGVSb2xlRGlhbG9nIiwiUkFURV9MSU1JVF9NUyIsIkJVTEtfUkFURV9MSU1JVF9NUyIsInVzZVJhdGVMaW1pdCIsImRlbGF5IiwiaXNSYXRlTGltaXRlZCIsInNldElzUmF0ZUxpbWl0ZWQiLCJ0aW1lb3V0UmVmIiwicmVzZXRSYXRlTGltaXQiLCJjdXJyZW50IiwiY2xlYXJUaW1lb3V0Iiwic2V0VGltZW91dCIsIkNIQU5ORUxfVFlQRV9DT05GSUciLCJpY29uIiwiY29sb3IiLCJsYWJlbCIsIlBFUk1JU1NJT05fQkFER0VTIiwiQURNSU5JU1RSQVRPUiIsIk1BTkFHRV9HVUlMRCIsIk1BTkFHRV9ST0xFUyIsIk1BTkFHRV9DSEFOTkVMUyIsIktJQ0tfTUVNQkVSUyIsIkJBTl9NRU1CRVJTIiwiTUFOQUdFX01FU1NBR0VTIiwiTUVOVElPTl9FVkVSWU9ORSIsIlBFUk1JU1NJT05fRkxBR19CSVRTIiwiZGVjb2RlUGVybWlzc2lvbnMiLCJiaXRmaWVsZCIsIkFycmF5IiwiaXNBcnJheSIsInBlcm1pc3Npb25zIiwiYml0cyIsIkJpZ0ludCIsInBlcm1pc3Npb24iLCJiaXQiLCJPYmplY3QiLCJlbnRyaWVzIiwicHVzaCIsImVycm9yIiwiY29uc29sZSIsIlNlcnZlck1hbmFnZW1lbnQiLCJ0b2FzdCIsImRpc3BsYXlOYW1lIiwiaXNCdWxrUmF0ZUxpbWl0ZWQiLCJyZXNldEJ1bGtSYXRlTGltaXQiLCJndWlsZERhdGEiLCJzZXRHdWlsZERhdGEiLCJwcmVmaXgiLCJib3ROYW1lIiwiZ3VpbGROYW1lIiwiZ3VpbGRJZCIsImd1aWxkSWNvbiIsImFjdGl2aXRpZXMiLCJ0eXBlIiwibmFtZSIsImFjdGl2aXR5Um90YXRpb25JbnRlcnZhbCIsInJvbGVzIiwic2V0Um9sZXMiLCJjaGFubmVscyIsInNldENoYW5uZWxzIiwic2V0TG9hZGluZyIsImNoYW5uZWxzTG9hZGluZyIsInNldENoYW5uZWxzTG9hZGluZyIsInNhdmluZyIsInNldFNhdmluZyIsImlzT3BlbiIsImlzQ3JlYXRlQ2hhbm5lbE9wZW4iLCJvbk9wZW4iLCJvbkNyZWF0ZUNoYW5uZWxPcGVuIiwib25DbG9zZSIsIm9uQ3JlYXRlQ2hhbm5lbENsb3NlIiwiaXNFZGl0Q2hhbm5lbE9wZW4iLCJvbkVkaXRDaGFubmVsT3BlbiIsIm9uRWRpdENoYW5uZWxDbG9zZSIsImlzQ3JlYXRlUm9sZU9wZW4iLCJvbkNyZWF0ZVJvbGVPcGVuIiwib25DcmVhdGVSb2xlQ2xvc2UiLCJpc0VkaXRSb2xlT3BlbiIsIm9uRWRpdFJvbGVPcGVuIiwib25FZGl0Um9sZUNsb3NlIiwiaXNDb2xvckJ1aWxkZXJPcGVuIiwib25Db2xvckJ1aWxkZXJPcGVuIiwib25Db2xvckJ1aWxkZXJDbG9zZSIsInNlbGVjdGVkUm9sZSIsInNldFNlbGVjdGVkUm9sZSIsInNlbGVjdGVkQ2hhbm5lbCIsInNldFNlbGVjdGVkQ2hhbm5lbCIsInNlbGVjdGVkUm9sZXMiLCJzZXRTZWxlY3RlZFJvbGVzIiwic2VsZWN0ZWRDaGFubmVscyIsInNldFNlbGVjdGVkQ2hhbm5lbHMiLCJidWxrRGVsZXRpbmciLCJzZXRCdWxrRGVsZXRpbmciLCJpY29uRmlsZSIsInNldEljb25GaWxlIiwiaWNvblByZXZpZXciLCJzZXRJY29uUHJldmlldyIsImZpbGVJbnB1dFJlZiIsImhhbmRsZUljb25GaWxlQ2hhbmdlIiwiZSIsImZpbGUiLCJ0YXJnZXQiLCJmaWxlcyIsInJlYWRlciIsIkZpbGVSZWFkZXIiLCJvbmxvYWQiLCJyZXN1bHQiLCJyZWFkQXNEYXRhVVJMIiwidXBsb2FkSWNvbiIsImZvcm1EYXRhIiwiRm9ybURhdGEiLCJhcHBlbmQiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiYm9keSIsIm9rIiwiZGF0YSIsImpzb24iLCJwcmV2IiwiaWNvblVybCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJzdGF0dXMiLCJkdXJhdGlvbiIsImZldGNoR3VpbGREYXRhIiwiZ3VpbGRSZXNwb25zZSIsInJvbGVzUmVzcG9uc2UiLCJQcm9taXNlIiwiYWxsIiwiZ3VpbGQiLCJpZCIsInJvbGVzRGF0YSIsImFyciIsInNvcnQiLCJhIiwiYiIsInBvc2l0aW9uIiwiZmV0Y2hDaGFubmVscyIsIkVycm9yIiwic29ydGVkQ2hhbm5lbHMiLCJoYW5kbGVTZXR0aW5nQ2hhbmdlIiwic2V0dGluZyIsInZhbHVlIiwic2F2ZVNldHRpbmdzIiwiaGVhZGVycyIsIkpTT04iLCJzdHJpbmdpZnkiLCJoYW5kbGVSb2xlRWRpdCIsInJvbGUiLCJoYW5kbGVDaGFubmVsRWRpdCIsImNoYW5uZWwiLCJoYW5kbGVDaGFubmVsQ3JlYXRlIiwiaGFuZGxlUm9sZUNyZWF0ZSIsImhhbmRsZUNoYW5uZWxEZWxldGUiLCJjaGFubmVsSWQiLCJnZXRQYXJlbnROYW1lIiwicGFyZW50SWQiLCJwYXJlbnQiLCJmaW5kIiwiYyIsImhhbmRsZUJ1bGtEZWxldGVSb2xlcyIsImxlbmd0aCIsInJvbGVJZHMiLCJtZXNzYWdlIiwiaGFuZGxlQnVsa0RlbGV0ZUNoYW5uZWxzIiwiY2hhbm5lbElkcyIsInRvZ2dsZVJvbGVTZWxlY3Rpb24iLCJyb2xlSWQiLCJpbmNsdWRlcyIsImZpbHRlciIsInRvZ2dsZUNoYW5uZWxTZWxlY3Rpb24iLCJzZWxlY3RBbGxSb2xlcyIsInNlbGVjdGFibGVSb2xlcyIsIm1hcCIsInNlbGVjdEFsbENoYW5uZWxzIiwibWF4VyIsInB5Iiwic3BhY2luZyIsImhlaWdodCIsImFsaWduIiwiYmciLCJwIiwicm91bmRlZCIsImJhY2tkcm9wRmlsdGVyIiwiYm9yZGVyIiwiYm9yZGVyQ29sb3IiLCJib3hTaGFkb3ciLCJqdXN0aWZ5IiwiYmdHcmFkaWVudCIsImJnQ2xpcCIsImFzIiwibXIiLCJtdCIsImxlZnRJY29uIiwiY29sb3JTY2hlbWUiLCJvbkNsaWNrIiwiaXNMb2FkaW5nIiwiaXNEaXNhYmxlZCIsImlzTGF6eSIsImNvbHVtbnMiLCJiYXNlIiwibGciLCJvbkNoYW5nZSIsInBsYWNlaG9sZGVyIiwiaXNSZWFkT25seSIsIl9kYXJrIiwiZm9udEZhbWlseSIsImZvbnRTaXplIiwiYm9yZGVyUmFkaXVzIiwiZm9udFdlaWdodCIsInZhcmlhbnQiLCJfIiwiaSIsIm92ZXJmbG93WCIsImlzQ2hlY2tlZCIsInIiLCJpc0luZGV0ZXJtaW5hdGUiLCJ3IiwiaCIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJ3cmFwIiwic2xpY2UiLCJwZXJtIiwiYXJpYS1sYWJlbCIsInRleHRBbGlnbiIsInR5cGVDb25maWciLCJwYXJlbnRfaWQiLCJtYiIsIndpbmRvdyIsIm9wZW4iLCJmYWxsYmFjayIsIm9uU3VjY2VzcyIsImNhdGVnb3JpZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/admin/guilds.tsx\n"));

/***/ })

});