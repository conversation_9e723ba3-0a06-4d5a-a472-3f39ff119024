"use strict";(()=>{var e={};e.id=3324,e.ids=[3324],e.modules={15806:e=>{e.exports=require("next-auth/next")},20396:e=>{e.exports=require("next-auth/providers/discord")},29021:e=>{e.exports=require("fs")},33873:e=>{e.exports=require("path")},65542:e=>{e.exports=require("next-auth")},65619:(e,r,s)=>{s.r(r),s.d(r,{config:()=>p,default:()=>l,routeModule:()=>m});var t={};s.r(t),s.d(t,{default:()=>d});var i=s(93433),a=s(20264),n=s(20584),u=s(15806),o=s(94506);async function d(e,r){if("GET"!==e.method)return r.status(405).json({error:"Method not allowed"});try{let s=await (0,u.getServerSession)(e,r,o.authOptions);if(!s)return r.status(401).json({error:"Unauthorized",hasSession:!1});let t=s.user;return r.status(200).json({hasSession:!0,userId:t.id,isAdmin:t.isAdmin,name:t.name,email:t.email,debug:{sessionKeys:Object.keys(s),userKeys:Object.keys(t),fullUser:t}})}catch(e){return r.status(500).json({error:"Internal server error",details:e.message})}}let l=(0,n.M)(t,"default"),p=(0,n.M)(t,"config"),m=new i.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/auth/permissions",pathname:"/api/auth/permissions",bundlePath:"",filename:""},userland:t})},72115:e=>{e.exports=require("yaml")},75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")}};var r=require("../../../webpack-api-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4301,9114,4227,9895,873,3920,3119,9176,966,727,7130,2774,8990,5652,7232,1581,523,7889,8360,8063,1516,4959,6835,246,393,713,9450,397,7897,4599,3640,4223],()=>s(65619));module.exports=t})();