"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["lib-node_modules_pnpm_redux_"],{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_13e03a16b3880bf31db5b9783f72eaa4/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_13e03a16b3880bf31db5b9783f72eaa4/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReducerType: () => (/* binding */ ReducerType),\n/* harmony export */   SHOULD_AUTOBATCH: () => (/* binding */ SHOULD_AUTOBATCH),\n/* harmony export */   TaskAbortError: () => (/* binding */ TaskAbortError),\n/* harmony export */   Tuple: () => (/* binding */ Tuple),\n/* harmony export */   addListener: () => (/* binding */ addListener),\n/* harmony export */   asyncThunkCreator: () => (/* binding */ asyncThunkCreator),\n/* harmony export */   autoBatchEnhancer: () => (/* binding */ autoBatchEnhancer),\n/* harmony export */   buildCreateSlice: () => (/* binding */ buildCreateSlice),\n/* harmony export */   clearAllListeners: () => (/* binding */ clearAllListeners),\n/* harmony export */   combineSlices: () => (/* binding */ combineSlices),\n/* harmony export */   configureStore: () => (/* binding */ configureStore),\n/* harmony export */   createAction: () => (/* binding */ createAction),\n/* harmony export */   createActionCreatorInvariantMiddleware: () => (/* binding */ createActionCreatorInvariantMiddleware),\n/* harmony export */   createAsyncThunk: () => (/* binding */ createAsyncThunk),\n/* harmony export */   createDraftSafeSelector: () => (/* binding */ createDraftSafeSelector),\n/* harmony export */   createDraftSafeSelectorCreator: () => (/* binding */ createDraftSafeSelectorCreator),\n/* harmony export */   createDynamicMiddleware: () => (/* binding */ createDynamicMiddleware),\n/* harmony export */   createEntityAdapter: () => (/* binding */ createEntityAdapter),\n/* harmony export */   createImmutableStateInvariantMiddleware: () => (/* binding */ createImmutableStateInvariantMiddleware),\n/* harmony export */   createListenerMiddleware: () => (/* binding */ createListenerMiddleware),\n/* harmony export */   createNextState: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.produce),\n/* harmony export */   createReducer: () => (/* binding */ createReducer),\n/* harmony export */   createSelector: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.createSelector),\n/* harmony export */   createSelectorCreator: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.createSelectorCreator),\n/* harmony export */   createSerializableStateInvariantMiddleware: () => (/* binding */ createSerializableStateInvariantMiddleware),\n/* harmony export */   createSlice: () => (/* binding */ createSlice),\n/* harmony export */   current: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.current),\n/* harmony export */   findNonSerializableValue: () => (/* binding */ findNonSerializableValue),\n/* harmony export */   formatProdErrorMessage: () => (/* binding */ formatProdErrorMessage),\n/* harmony export */   freeze: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.freeze),\n/* harmony export */   isActionCreator: () => (/* binding */ isActionCreator),\n/* harmony export */   isAllOf: () => (/* binding */ isAllOf),\n/* harmony export */   isAnyOf: () => (/* binding */ isAnyOf),\n/* harmony export */   isAsyncThunkAction: () => (/* binding */ isAsyncThunkAction),\n/* harmony export */   isDraft: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.isDraft),\n/* harmony export */   isFluxStandardAction: () => (/* binding */ isFSA),\n/* harmony export */   isFulfilled: () => (/* binding */ isFulfilled),\n/* harmony export */   isImmutableDefault: () => (/* binding */ isImmutableDefault),\n/* harmony export */   isPending: () => (/* binding */ isPending),\n/* harmony export */   isPlain: () => (/* binding */ isPlain),\n/* harmony export */   isRejected: () => (/* binding */ isRejected),\n/* harmony export */   isRejectedWithValue: () => (/* binding */ isRejectedWithValue),\n/* harmony export */   lruMemoize: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.lruMemoize),\n/* harmony export */   miniSerializeError: () => (/* binding */ miniSerializeError),\n/* harmony export */   nanoid: () => (/* binding */ nanoid),\n/* harmony export */   original: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.original),\n/* harmony export */   prepareAutoBatched: () => (/* binding */ prepareAutoBatched),\n/* harmony export */   removeListener: () => (/* binding */ removeListener),\n/* harmony export */   unwrapResult: () => (/* binding */ unwrapResult),\n/* harmony export */   weakMapMemoize: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.weakMapMemoize)\n/* harmony export */ });\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redux */ \"(pages-dir-browser)/../../node_modules/.pnpm/redux@5.0.1/node_modules/redux/dist/redux.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in redux__WEBPACK_IMPORTED_MODULE_0__) if([\"default\",\"ReducerType\",\"SHOULD_AUTOBATCH\",\"TaskAbortError\",\"Tuple\",\"addListener\",\"asyncThunkCreator\",\"autoBatchEnhancer\",\"buildCreateSlice\",\"clearAllListeners\",\"combineSlices\",\"configureStore\",\"createAction\",\"createActionCreatorInvariantMiddleware\",\"createAsyncThunk\",\"createDraftSafeSelector\",\"createDraftSafeSelectorCreator\",\"createDynamicMiddleware\",\"createEntityAdapter\",\"createImmutableStateInvariantMiddleware\",\"createListenerMiddleware\",\"createNextState\",\"createReducer\",\"createSelector\",\"createSelectorCreator\",\"createSerializableStateInvariantMiddleware\",\"createSlice\",\"current\",\"findNonSerializableValue\",\"formatProdErrorMessage\",\"freeze\",\"isActionCreator\",\"isAllOf\",\"isAnyOf\",\"isAsyncThunkAction\",\"isDraft\",\"isFluxStandardAction\",\"isFulfilled\",\"isImmutableDefault\",\"isPending\",\"isPlain\",\"isRejected\",\"isRejectedWithValue\",\"lruMemoize\",\"miniSerializeError\",\"nanoid\",\"original\",\"prepareAutoBatched\",\"removeListener\",\"unwrapResult\",\"weakMapMemoize\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => redux__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! immer */ \"(pages-dir-browser)/../../node_modules/.pnpm/immer@10.1.1/node_modules/immer/dist/immer.mjs\");\n/* harmony import */ var reselect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reselect */ \"(pages-dir-browser)/../../node_modules/.pnpm/reselect@5.1.1/node_modules/reselect/dist/reselect.mjs\");\n/* harmony import */ var redux_thunk__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux-thunk */ \"(pages-dir-browser)/../../node_modules/.pnpm/redux-thunk@3.1.0_redux@5.0.1/node_modules/redux-thunk/dist/redux-thunk.mjs\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/process.js\");\n// src/index.ts\n\n\n\n\n// src/createDraftSafeSelector.ts\n\n\nvar createDraftSafeSelectorCreator = (...args) => {\n  const createSelector2 = (0,reselect__WEBPACK_IMPORTED_MODULE_1__.createSelectorCreator)(...args);\n  const createDraftSafeSelector2 = Object.assign((...args2) => {\n    const selector = createSelector2(...args2);\n    const wrappedSelector = (value, ...rest) => selector((0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(value) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__.current)(value) : value, ...rest);\n    Object.assign(wrappedSelector, selector);\n    return wrappedSelector;\n  }, {\n    withTypes: () => createDraftSafeSelector2\n  });\n  return createDraftSafeSelector2;\n};\nvar createDraftSafeSelector = /* @__PURE__ */ createDraftSafeSelectorCreator(reselect__WEBPACK_IMPORTED_MODULE_1__.weakMapMemoize);\n\n// src/configureStore.ts\n\n\n// src/devtoolsExtension.ts\n\nvar composeWithDevTools = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function() {\n  if (arguments.length === 0) return void 0;\n  if (typeof arguments[0] === \"object\") return redux__WEBPACK_IMPORTED_MODULE_0__.compose;\n  return redux__WEBPACK_IMPORTED_MODULE_0__.compose.apply(null, arguments);\n};\nvar devToolsEnhancer = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function() {\n  return function(noop3) {\n    return noop3;\n  };\n};\n\n// src/getDefaultMiddleware.ts\n\n\n// src/createAction.ts\n\n\n// src/tsHelpers.ts\nvar hasMatchFunction = (v) => {\n  return v && typeof v.match === \"function\";\n};\n\n// src/createAction.ts\nfunction createAction(type, prepareAction) {\n  function actionCreator(...args) {\n    if (prepareAction) {\n      let prepared = prepareAction(...args);\n      if (!prepared) {\n        throw new Error( false ? 0 : \"prepareAction did not return an object\");\n      }\n      return {\n        type,\n        payload: prepared.payload,\n        ...\"meta\" in prepared && {\n          meta: prepared.meta\n        },\n        ...\"error\" in prepared && {\n          error: prepared.error\n        }\n      };\n    }\n    return {\n      type,\n      payload: args[0]\n    };\n  }\n  actionCreator.toString = () => `${type}`;\n  actionCreator.type = type;\n  actionCreator.match = (action) => (0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action) && action.type === type;\n  return actionCreator;\n}\nfunction isActionCreator(action) {\n  return typeof action === \"function\" && \"type\" in action && // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\n  hasMatchFunction(action);\n}\nfunction isFSA(action) {\n  return (0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action) && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key) {\n  return [\"type\", \"payload\", \"error\", \"meta\"].indexOf(key) > -1;\n}\n\n// src/actionCreatorInvariantMiddleware.ts\nfunction getMessage(type) {\n  const splitType = type ? `${type}`.split(\"/\") : [];\n  const actionName = splitType[splitType.length - 1] || \"actionCreator\";\n  return `Detected an action creator with type \"${type || \"unknown\"}\" being dispatched. \nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`;\n}\nfunction createActionCreatorInvariantMiddleware(options = {}) {\n  if (false) {}\n  const {\n    isActionCreator: isActionCreator2 = isActionCreator\n  } = options;\n  return () => (next) => (action) => {\n    if (isActionCreator2(action)) {\n      console.warn(getMessage(action.type));\n    }\n    return next(action);\n  };\n}\n\n// src/utils.ts\n\nfunction getTimeMeasureUtils(maxDelay, fnName) {\n  let elapsed = 0;\n  return {\n    measureTime(fn) {\n      const started = Date.now();\n      try {\n        return fn();\n      } finally {\n        const finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded() {\n      if (elapsed > maxDelay) {\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\nIt is disabled in production builds, so you don't need to worry about that.`);\n      }\n    }\n  };\n}\nvar Tuple = class _Tuple extends Array {\n  constructor(...items) {\n    super(...items);\n    Object.setPrototypeOf(this, _Tuple.prototype);\n  }\n  static get [Symbol.species]() {\n    return _Tuple;\n  }\n  concat(...arr) {\n    return super.concat.apply(this, arr);\n  }\n  prepend(...arr) {\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new _Tuple(...arr[0].concat(this));\n    }\n    return new _Tuple(...arr.concat(this));\n  }\n};\nfunction freezeDraftable(val) {\n  return (0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraftable)(val) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__.produce)(val, () => {\n  }) : val;\n}\nfunction getOrInsertComputed(map, key, compute) {\n  if (map.has(key)) return map.get(key);\n  return map.set(key, compute(key)).get(key);\n}\n\n// src/immutableStateInvariantMiddleware.ts\nfunction isImmutableDefault(value) {\n  return typeof value !== \"object\" || value == null || Object.isFrozen(value);\n}\nfunction trackForMutations(isImmutable, ignorePaths, obj) {\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations() {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\nfunction trackProperties(isImmutable, ignorePaths = [], obj, path = \"\", checkedObjects = /* @__PURE__ */ new Set()) {\n  const tracked = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (const key in obj) {\n      const childPath = path ? path + \".\" + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked;\n}\nfunction detectMutations(isImmutable, ignoredPaths = [], trackedProperty, obj, sameParentRef = false, path = \"\") {\n  const prevObj = trackedProperty ? trackedProperty.value : void 0;\n  const sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n  const keysToDetect = {};\n  for (let key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (let key in obj) {\n    keysToDetect[key] = true;\n  }\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (let key in keysToDetect) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some((ignored) => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return result;\n    }\n  }\n  return {\n    wasMutated: false\n  };\n}\nfunction createImmutableStateInvariantMiddleware(options = {}) {\n  if (false) {} else {\n    let stringify2 = function(obj, serializer, indent, decycler) {\n      return JSON.stringify(obj, getSerialize2(serializer, decycler), indent);\n    }, getSerialize2 = function(serializer, decycler) {\n      let stack = [], keys = [];\n      if (!decycler) decycler = function(_, value) {\n        if (stack[0] === value) return \"[Circular ~]\";\n        return \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\n      };\n      return function(key, value) {\n        if (stack.length > 0) {\n          var thisPos = stack.indexOf(this);\n          ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n          ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n          if (~stack.indexOf(value)) value = decycler.call(this, key, value);\n        } else stack.push(value);\n        return serializer == null ? value : serializer.call(this, key, value);\n      };\n    };\n    var stringify = stringify2, getSerialize = getSerialize2;\n    let {\n      isImmutable = isImmutableDefault,\n      ignoredPaths,\n      warnAfter = 32\n    } = options;\n    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n    return ({\n      getState\n    }) => {\n      let state = getState();\n      let tracker = track(state);\n      let result;\n      return (next) => (action) => {\n        const measureUtils = getTimeMeasureUtils(warnAfter, \"ImmutableStateInvariantMiddleware\");\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error( false ? 0 : `A state mutation was detected between dispatches, in the path '${result.path || \"\"}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        const dispatchedAction = next(action);\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error( false ? 0 : `A state mutation was detected inside a dispatch, in the path: ${result.path || \"\"}. Take a look at the reducer(s) handling the action ${stringify2(action)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  }\n}\n\n// src/serializableStateInvariantMiddleware.ts\n\nfunction isPlain(val) {\n  const type = typeof val;\n  return val == null || type === \"string\" || type === \"boolean\" || type === \"number\" || Array.isArray(val) || (0,redux__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(val);\n}\nfunction findNonSerializableValue(value, path = \"\", isSerializable = isPlain, getEntries, ignoredPaths = [], cache) {\n  let foundNestedSerializable;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || \"<root>\",\n      value\n    };\n  }\n  if (typeof value !== \"object\" || value === null) {\n    return false;\n  }\n  if (cache?.has(value)) return false;\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (const [key, nestedValue] of entries) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some((ignored) => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        keyPath: nestedPath,\n        value: nestedValue\n      };\n    }\n    if (typeof nestedValue === \"object\") {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return foundNestedSerializable;\n      }\n    }\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nfunction isNestedFrozen(value) {\n  if (!Object.isFrozen(value)) return false;\n  for (const nestedValue of Object.values(value)) {\n    if (typeof nestedValue !== \"object\" || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\nfunction createSerializableStateInvariantMiddleware(options = {}) {\n  if (false) {} else {\n    const {\n      isSerializable = isPlain,\n      getEntries,\n      ignoredActions = [],\n      ignoredActionPaths = [\"meta.arg\", \"meta.baseQueryMeta\"],\n      ignoredPaths = [],\n      warnAfter = 32,\n      ignoreState = false,\n      ignoreActions = false,\n      disableCache = false\n    } = options;\n    const cache = !disableCache && WeakSet ? /* @__PURE__ */ new WeakSet() : void 0;\n    return (storeAPI) => (next) => (action) => {\n      if (!(0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action)) {\n        return next(action);\n      }\n      const result = next(action);\n      const measureUtils = getTimeMeasureUtils(warnAfter, \"SerializableStateInvariantMiddleware\");\n      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {\n        measureUtils.measureTime(() => {\n          const foundActionNonSerializableValue = findNonSerializableValue(action, \"\", isSerializable, getEntries, ignoredActionPaths, cache);\n          if (foundActionNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundActionNonSerializableValue;\n            console.error(`A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`, value, \"\\nTake a look at the logic that dispatched this action: \", action, \"\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)\", \"\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)\");\n          }\n        });\n      }\n      if (!ignoreState) {\n        measureUtils.measureTime(() => {\n          const state = storeAPI.getState();\n          const foundStateNonSerializableValue = findNonSerializableValue(state, \"\", isSerializable, getEntries, ignoredPaths, cache);\n          if (foundStateNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundStateNonSerializableValue;\n            console.error(`A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`, value, `\nTake a look at the reducer(s) handling this action type: ${action.type}.\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n      }\n      return result;\n    };\n  }\n}\n\n// src/getDefaultMiddleware.ts\nfunction isBoolean(x) {\n  return typeof x === \"boolean\";\n}\nvar buildGetDefaultMiddleware = () => function getDefaultMiddleware(options) {\n  const {\n    thunk = true,\n    immutableCheck = true,\n    serializableCheck = true,\n    actionCreatorCheck = true\n  } = options ?? {};\n  let middlewareArray = new Tuple();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(redux_thunk__WEBPACK_IMPORTED_MODULE_3__.thunk);\n    } else {\n      middlewareArray.push((0,redux_thunk__WEBPACK_IMPORTED_MODULE_3__.withExtraArgument)(thunk.extraArgument));\n    }\n  }\n  if (true) {\n    if (immutableCheck) {\n      let immutableOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n    }\n    if (serializableCheck) {\n      let serializableOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      let actionCreatorOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray;\n};\n\n// src/autoBatchEnhancer.ts\nvar SHOULD_AUTOBATCH = \"RTK_autoBatch\";\nvar prepareAutoBatched = () => (payload) => ({\n  payload,\n  meta: {\n    [SHOULD_AUTOBATCH]: true\n  }\n});\nvar createQueueWithTimer = (timeout) => {\n  return (notify) => {\n    setTimeout(notify, timeout);\n  };\n};\nvar autoBatchEnhancer = (options = {\n  type: \"raf\"\n}) => (next) => (...args) => {\n  const store = next(...args);\n  let notifying = true;\n  let shouldNotifyAtEndOfTick = false;\n  let notificationQueued = false;\n  const listeners = /* @__PURE__ */ new Set();\n  const queueCallback = options.type === \"tick\" ? queueMicrotask : options.type === \"raf\" ? (\n    // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.\n    typeof window !== \"undefined\" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10)\n  ) : options.type === \"callback\" ? options.queueNotification : createQueueWithTimer(options.timeout);\n  const notifyListeners = () => {\n    notificationQueued = false;\n    if (shouldNotifyAtEndOfTick) {\n      shouldNotifyAtEndOfTick = false;\n      listeners.forEach((l) => l());\n    }\n  };\n  return Object.assign({}, store, {\n    // Override the base `store.subscribe` method to keep original listeners\n    // from running if we're delaying notifications\n    subscribe(listener2) {\n      const wrappedListener = () => notifying && listener2();\n      const unsubscribe = store.subscribe(wrappedListener);\n      listeners.add(listener2);\n      return () => {\n        unsubscribe();\n        listeners.delete(listener2);\n      };\n    },\n    // Override the base `store.dispatch` method so that we can check actions\n    // for the `shouldAutoBatch` flag and determine if batching is active\n    dispatch(action) {\n      try {\n        notifying = !action?.meta?.[SHOULD_AUTOBATCH];\n        shouldNotifyAtEndOfTick = !notifying;\n        if (shouldNotifyAtEndOfTick) {\n          if (!notificationQueued) {\n            notificationQueued = true;\n            queueCallback(notifyListeners);\n          }\n        }\n        return store.dispatch(action);\n      } finally {\n        notifying = true;\n      }\n    }\n  });\n};\n\n// src/getDefaultEnhancers.ts\nvar buildGetDefaultEnhancers = (middlewareEnhancer) => function getDefaultEnhancers(options) {\n  const {\n    autoBatch = true\n  } = options ?? {};\n  let enhancerArray = new Tuple(middlewareEnhancer);\n  if (autoBatch) {\n    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === \"object\" ? autoBatch : void 0));\n  }\n  return enhancerArray;\n};\n\n// src/configureStore.ts\nfunction configureStore(options) {\n  const getDefaultMiddleware = buildGetDefaultMiddleware();\n  const {\n    reducer = void 0,\n    middleware,\n    devTools = true,\n    duplicateMiddlewareCheck = true,\n    preloadedState = void 0,\n    enhancers = void 0\n  } = options || {};\n  let rootReducer;\n  if (typeof reducer === \"function\") {\n    rootReducer = reducer;\n  } else if ((0,redux__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(reducer)) {\n    rootReducer = (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)(reducer);\n  } else {\n    throw new Error( false ? 0 : \"`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers\");\n  }\n  if ( true && middleware && typeof middleware !== \"function\") {\n    throw new Error( false ? 0 : \"`middleware` field must be a callback\");\n  }\n  let finalMiddleware;\n  if (typeof middleware === \"function\") {\n    finalMiddleware = middleware(getDefaultMiddleware);\n    if ( true && !Array.isArray(finalMiddleware)) {\n      throw new Error( false ? 0 : \"when using a middleware builder function, an array of middleware must be returned\");\n    }\n  } else {\n    finalMiddleware = getDefaultMiddleware();\n  }\n  if ( true && finalMiddleware.some((item) => typeof item !== \"function\")) {\n    throw new Error( false ? 0 : \"each middleware provided to configureStore must be a function\");\n  }\n  if ( true && duplicateMiddlewareCheck) {\n    let middlewareReferences = /* @__PURE__ */ new Set();\n    finalMiddleware.forEach((middleware2) => {\n      if (middlewareReferences.has(middleware2)) {\n        throw new Error( false ? 0 : \"Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.\");\n      }\n      middlewareReferences.add(middleware2);\n    });\n  }\n  let finalCompose = redux__WEBPACK_IMPORTED_MODULE_0__.compose;\n  if (devTools) {\n    finalCompose = composeWithDevTools({\n      // Enable capture of stack traces for dispatched Redux actions\n      trace: \"development\" !== \"production\",\n      ...typeof devTools === \"object\" && devTools\n    });\n  }\n  const middlewareEnhancer = (0,redux__WEBPACK_IMPORTED_MODULE_0__.applyMiddleware)(...finalMiddleware);\n  const getDefaultEnhancers = buildGetDefaultEnhancers(middlewareEnhancer);\n  if ( true && enhancers && typeof enhancers !== \"function\") {\n    throw new Error( false ? 0 : \"`enhancers` field must be a callback\");\n  }\n  let storeEnhancers = typeof enhancers === \"function\" ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();\n  if ( true && !Array.isArray(storeEnhancers)) {\n    throw new Error( false ? 0 : \"`enhancers` callback must return an array\");\n  }\n  if ( true && storeEnhancers.some((item) => typeof item !== \"function\")) {\n    throw new Error( false ? 0 : \"each enhancer provided to configureStore must be a function\");\n  }\n  if ( true && finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {\n    console.error(\"middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`\");\n  }\n  const composedEnhancer = finalCompose(...storeEnhancers);\n  return (0,redux__WEBPACK_IMPORTED_MODULE_0__.createStore)(rootReducer, preloadedState, composedEnhancer);\n}\n\n// src/createReducer.ts\n\n\n// src/mapBuilders.ts\nfunction executeReducerBuilderCallback(builderCallback) {\n  const actionsMap = {};\n  const actionMatchers = [];\n  let defaultCaseReducer;\n  const builder = {\n    addCase(typeOrActionCreator, reducer) {\n      if (true) {\n        if (actionMatchers.length > 0) {\n          throw new Error( false ? 0 : \"`builder.addCase` should only be called before calling `builder.addMatcher`\");\n        }\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addCase` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error( false ? 0 : \"`builder.addCase` cannot be called with an empty action type\");\n      }\n      if (type in actionsMap) {\n        throw new Error( false ? 0 : `\\`builder.addCase\\` cannot be called with two reducers for the same action type '${type}'`);\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher(matcher, reducer) {\n      if (true) {\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addMatcher` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      actionMatchers.push({\n        matcher,\n        reducer\n      });\n      return builder;\n    },\n    addDefaultCase(reducer) {\n      if (true) {\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addDefaultCase` can only be called once\");\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}\n\n// src/createReducer.ts\nfunction isStateFunction(x) {\n  return typeof x === \"function\";\n}\nfunction createReducer(initialState, mapOrBuilderCallback) {\n  if (true) {\n    if (typeof mapOrBuilderCallback === \"object\") {\n      throw new Error( false ? 0 : \"The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n    }\n  }\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);\n  let getInitialState;\n  if (isStateFunction(initialState)) {\n    getInitialState = () => freezeDraftable(initialState());\n  } else {\n    const frozenInitialState = freezeDraftable(initialState);\n    getInitialState = () => frozenInitialState;\n  }\n  function reducer(state = getInitialState(), action) {\n    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(({\n      matcher\n    }) => matcher(action)).map(({\n      reducer: reducer2\n    }) => reducer2)];\n    if (caseReducers.filter((cr) => !!cr).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce((previousState, caseReducer) => {\n      if (caseReducer) {\n        if ((0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(previousState)) {\n          const draft = previousState;\n          const result = caseReducer(draft, action);\n          if (result === void 0) {\n            return previousState;\n          }\n          return result;\n        } else if (!(0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraftable)(previousState)) {\n          const result = caseReducer(previousState, action);\n          if (result === void 0) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error(\"A case reducer on a non-draftable value must not return undefined\");\n          }\n          return result;\n        } else {\n          return (0,immer__WEBPACK_IMPORTED_MODULE_2__.produce)(previousState, (draft) => {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer;\n}\n\n// src/matchers.ts\nvar matches = (matcher, action) => {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\nfunction isAnyOf(...matchers) {\n  return (action) => {\n    return matchers.some((matcher) => matches(matcher, action));\n  };\n}\nfunction isAllOf(...matchers) {\n  return (action) => {\n    return matchers.every((matcher) => matches(matcher, action));\n  };\n}\nfunction hasExpectedRequestMetadata(action, validStatus) {\n  if (!action || !action.meta) return false;\n  const hasValidRequestId = typeof action.meta.requestId === \"string\";\n  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a) {\n  return typeof a[0] === \"function\" && \"pending\" in a[0] && \"fulfilled\" in a[0] && \"rejected\" in a[0];\n}\nfunction isPending(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"pending\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.pending));\n}\nfunction isRejected(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.rejected));\n}\nfunction isRejectedWithValue(...asyncThunks) {\n  const hasFlag = (action) => {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  if (asyncThunks.length === 0) {\n    return isAllOf(isRejected(...asyncThunks), hasFlag);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return isAllOf(isRejected(...asyncThunks), hasFlag);\n}\nfunction isFulfilled(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"fulfilled\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.fulfilled));\n}\nfunction isAsyncThunkAction(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"pending\", \"fulfilled\", \"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.flatMap((asyncThunk) => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));\n}\n\n// src/nanoid.ts\nvar urlAlphabet = \"ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW\";\nvar nanoid = (size = 21) => {\n  let id = \"\";\n  let i = size;\n  while (i--) {\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};\n\n// src/createAsyncThunk.ts\nvar commonProperties = [\"name\", \"message\", \"stack\", \"code\"];\nvar RejectWithValue = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar FulfillWithMeta = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar miniSerializeError = (value) => {\n  if (typeof value === \"object\" && value !== null) {\n    const simpleError = {};\n    for (const property of commonProperties) {\n      if (typeof value[property] === \"string\") {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nvar externalAbortMessage = \"External signal was aborted\";\nvar createAsyncThunk = /* @__PURE__ */ (() => {\n  function createAsyncThunk2(typePrefix, payloadCreator, options) {\n    const fulfilled = createAction(typePrefix + \"/fulfilled\", (payload, requestId, arg, meta) => ({\n      payload,\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        requestStatus: \"fulfilled\"\n      }\n    }));\n    const pending = createAction(typePrefix + \"/pending\", (requestId, arg, meta) => ({\n      payload: void 0,\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        requestStatus: \"pending\"\n      }\n    }));\n    const rejected = createAction(typePrefix + \"/rejected\", (error, requestId, arg, payload, meta) => ({\n      payload,\n      error: (options && options.serializeError || miniSerializeError)(error || \"Rejected\"),\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        rejectedWithValue: !!payload,\n        requestStatus: \"rejected\",\n        aborted: error?.name === \"AbortError\",\n        condition: error?.name === \"ConditionError\"\n      }\n    }));\n    function actionCreator(arg, {\n      signal\n    } = {}) {\n      return (dispatch, getState, extra) => {\n        const requestId = options?.idGenerator ? options.idGenerator(arg) : nanoid();\n        const abortController = new AbortController();\n        let abortHandler;\n        let abortReason;\n        function abort(reason) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        if (signal) {\n          if (signal.aborted) {\n            abort(externalAbortMessage);\n          } else {\n            signal.addEventListener(\"abort\", () => abort(externalAbortMessage), {\n              once: true\n            });\n          }\n        }\n        const promise = async function() {\n          let finalAction;\n          try {\n            let conditionResult = options?.condition?.(arg, {\n              getState,\n              extra\n            });\n            if (isThenable(conditionResult)) {\n              conditionResult = await conditionResult;\n            }\n            if (conditionResult === false || abortController.signal.aborted) {\n              throw {\n                name: \"ConditionError\",\n                message: \"Aborted due to condition callback returning false.\"\n              };\n            }\n            const abortedPromise = new Promise((_, reject) => {\n              abortHandler = () => {\n                reject({\n                  name: \"AbortError\",\n                  message: abortReason || \"Aborted\"\n                });\n              };\n              abortController.signal.addEventListener(\"abort\", abortHandler);\n            });\n            dispatch(pending(requestId, arg, options?.getPendingMeta?.({\n              requestId,\n              arg\n            }, {\n              getState,\n              extra\n            })));\n            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n              dispatch,\n              getState,\n              extra,\n              requestId,\n              signal: abortController.signal,\n              abort,\n              rejectWithValue: (value, meta) => {\n                return new RejectWithValue(value, meta);\n              },\n              fulfillWithValue: (value, meta) => {\n                return new FulfillWithMeta(value, meta);\n              }\n            })).then((result) => {\n              if (result instanceof RejectWithValue) {\n                throw result;\n              }\n              if (result instanceof FulfillWithMeta) {\n                return fulfilled(result.payload, requestId, arg, result.meta);\n              }\n              return fulfilled(result, requestId, arg);\n            })]);\n          } catch (err) {\n            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err, requestId, arg);\n          } finally {\n            if (abortHandler) {\n              abortController.signal.removeEventListener(\"abort\", abortHandler);\n            }\n          }\n          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;\n          if (!skipDispatch) {\n            dispatch(finalAction);\n          }\n          return finalAction;\n        }();\n        return Object.assign(promise, {\n          abort,\n          requestId,\n          arg,\n          unwrap() {\n            return promise.then(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator, {\n      pending,\n      rejected,\n      fulfilled,\n      settled: isAnyOf(rejected, fulfilled),\n      typePrefix\n    });\n  }\n  createAsyncThunk2.withTypes = () => createAsyncThunk2;\n  return createAsyncThunk2;\n})();\nfunction unwrapResult(action) {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\nfunction isThenable(value) {\n  return value !== null && typeof value === \"object\" && typeof value.then === \"function\";\n}\n\n// src/createSlice.ts\nvar asyncThunkSymbol = /* @__PURE__ */ Symbol.for(\"rtk-slice-createasyncthunk\");\nvar asyncThunkCreator = {\n  [asyncThunkSymbol]: createAsyncThunk\n};\nvar ReducerType = /* @__PURE__ */ ((ReducerType2) => {\n  ReducerType2[\"reducer\"] = \"reducer\";\n  ReducerType2[\"reducerWithPrepare\"] = \"reducerWithPrepare\";\n  ReducerType2[\"asyncThunk\"] = \"asyncThunk\";\n  return ReducerType2;\n})(ReducerType || {});\nfunction getType(slice, actionKey) {\n  return `${slice}/${actionKey}`;\n}\nfunction buildCreateSlice({\n  creators\n} = {}) {\n  const cAT = creators?.asyncThunk?.[asyncThunkSymbol];\n  return function createSlice2(options) {\n    const {\n      name,\n      reducerPath = name\n    } = options;\n    if (!name) {\n      throw new Error( false ? 0 : \"`name` is a required option for createSlice\");\n    }\n    if (typeof process !== \"undefined\" && \"development\" === \"development\") {\n      if (options.initialState === void 0) {\n        console.error(\"You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`\");\n      }\n    }\n    const reducers = (typeof options.reducers === \"function\" ? options.reducers(buildReducerCreators()) : options.reducers) || {};\n    const reducerNames = Object.keys(reducers);\n    const context = {\n      sliceCaseReducersByName: {},\n      sliceCaseReducersByType: {},\n      actionCreators: {},\n      sliceMatchers: []\n    };\n    const contextMethods = {\n      addCase(typeOrActionCreator, reducer2) {\n        const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n        if (!type) {\n          throw new Error( false ? 0 : \"`context.addCase` cannot be called with an empty action type\");\n        }\n        if (type in context.sliceCaseReducersByType) {\n          throw new Error( false ? 0 : \"`context.addCase` cannot be called with two reducers for the same action type: \" + type);\n        }\n        context.sliceCaseReducersByType[type] = reducer2;\n        return contextMethods;\n      },\n      addMatcher(matcher, reducer2) {\n        context.sliceMatchers.push({\n          matcher,\n          reducer: reducer2\n        });\n        return contextMethods;\n      },\n      exposeAction(name2, actionCreator) {\n        context.actionCreators[name2] = actionCreator;\n        return contextMethods;\n      },\n      exposeCaseReducer(name2, reducer2) {\n        context.sliceCaseReducersByName[name2] = reducer2;\n        return contextMethods;\n      }\n    };\n    reducerNames.forEach((reducerName) => {\n      const reducerDefinition = reducers[reducerName];\n      const reducerDetails = {\n        reducerName,\n        type: getType(name, reducerName),\n        createNotation: typeof options.reducers === \"function\"\n      };\n      if (isAsyncThunkSliceReducerDefinition(reducerDefinition)) {\n        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);\n      } else {\n        handleNormalReducerDefinition(reducerDetails, reducerDefinition, contextMethods);\n      }\n    });\n    function buildReducer() {\n      if (true) {\n        if (typeof options.extraReducers === \"object\") {\n          throw new Error( false ? 0 : \"The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = void 0] = typeof options.extraReducers === \"function\" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];\n      const finalCaseReducers = {\n        ...extraReducers,\n        ...context.sliceCaseReducersByType\n      };\n      return createReducer(options.initialState, (builder) => {\n        for (let key in finalCaseReducers) {\n          builder.addCase(key, finalCaseReducers[key]);\n        }\n        for (let sM of context.sliceMatchers) {\n          builder.addMatcher(sM.matcher, sM.reducer);\n        }\n        for (let m of actionMatchers) {\n          builder.addMatcher(m.matcher, m.reducer);\n        }\n        if (defaultCaseReducer) {\n          builder.addDefaultCase(defaultCaseReducer);\n        }\n      });\n    }\n    const selectSelf = (state) => state;\n    const injectedSelectorCache = /* @__PURE__ */ new Map();\n    const injectedStateCache = /* @__PURE__ */ new WeakMap();\n    let _reducer;\n    function reducer(state, action) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    }\n    function getInitialState() {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n    function makeSelectorProps(reducerPath2, injected = false) {\n      function selectSlice(state) {\n        let sliceState = state[reducerPath2];\n        if (typeof sliceState === \"undefined\") {\n          if (injected) {\n            sliceState = getOrInsertComputed(injectedStateCache, selectSlice, getInitialState);\n          } else if (true) {\n            throw new Error( false ? 0 : \"selectSlice returned undefined for an uninjected slice reducer\");\n          }\n        }\n        return sliceState;\n      }\n      function getSelectors(selectState = selectSelf) {\n        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => /* @__PURE__ */ new WeakMap());\n        return getOrInsertComputed(selectorCache, selectState, () => {\n          const map = {};\n          for (const [name2, selector] of Object.entries(options.selectors ?? {})) {\n            map[name2] = wrapSelector(selector, selectState, () => getOrInsertComputed(injectedStateCache, selectState, getInitialState), injected);\n          }\n          return map;\n        });\n      }\n      return {\n        reducerPath: reducerPath2,\n        getSelectors,\n        get selectors() {\n          return getSelectors(selectSlice);\n        },\n        selectSlice\n      };\n    }\n    const slice = {\n      name,\n      reducer,\n      actions: context.actionCreators,\n      caseReducers: context.sliceCaseReducersByName,\n      getInitialState,\n      ...makeSelectorProps(reducerPath),\n      injectInto(injectable, {\n        reducerPath: pathOpt,\n        ...config\n      } = {}) {\n        const newReducerPath = pathOpt ?? reducerPath;\n        injectable.inject({\n          reducerPath: newReducerPath,\n          reducer\n        }, config);\n        return {\n          ...slice,\n          ...makeSelectorProps(newReducerPath, true)\n        };\n      }\n    };\n    return slice;\n  };\n}\nfunction wrapSelector(selector, selectState, getInitialState, injected) {\n  function wrapper(rootState, ...args) {\n    let sliceState = selectState(rootState);\n    if (typeof sliceState === \"undefined\") {\n      if (injected) {\n        sliceState = getInitialState();\n      } else if (true) {\n        throw new Error( false ? 0 : \"selectState returned undefined for an uninjected slice reducer\");\n      }\n    }\n    return selector(sliceState, ...args);\n  }\n  wrapper.unwrapped = selector;\n  return wrapper;\n}\nvar createSlice = /* @__PURE__ */ buildCreateSlice();\nfunction buildReducerCreators() {\n  function asyncThunk(payloadCreator, config) {\n    return {\n      _reducerDefinitionType: \"asyncThunk\" /* asyncThunk */,\n      payloadCreator,\n      ...config\n    };\n  }\n  asyncThunk.withTypes = () => asyncThunk;\n  return {\n    reducer(caseReducer) {\n      return Object.assign({\n        // hack so the wrapping function has the same name as the original\n        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original\n        [caseReducer.name](...args) {\n          return caseReducer(...args);\n        }\n      }[caseReducer.name], {\n        _reducerDefinitionType: \"reducer\" /* reducer */\n      });\n    },\n    preparedReducer(prepare, reducer) {\n      return {\n        _reducerDefinitionType: \"reducerWithPrepare\" /* reducerWithPrepare */,\n        prepare,\n        reducer\n      };\n    },\n    asyncThunk\n  };\n}\nfunction handleNormalReducerDefinition({\n  type,\n  reducerName,\n  createNotation\n}, maybeReducerWithPrepare, context) {\n  let caseReducer;\n  let prepareCallback;\n  if (\"reducer\" in maybeReducerWithPrepare) {\n    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {\n      throw new Error( false ? 0 : \"Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.\");\n    }\n    caseReducer = maybeReducerWithPrepare.reducer;\n    prepareCallback = maybeReducerWithPrepare.prepare;\n  } else {\n    caseReducer = maybeReducerWithPrepare;\n  }\n  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));\n}\nfunction isAsyncThunkSliceReducerDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"asyncThunk\" /* asyncThunk */;\n}\nfunction isCaseReducerWithPrepareDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"reducerWithPrepare\" /* reducerWithPrepare */;\n}\nfunction handleThunkCaseReducerDefinition({\n  type,\n  reducerName\n}, reducerDefinition, context, cAT) {\n  if (!cAT) {\n    throw new Error( false ? 0 : \"Cannot use `create.asyncThunk` in the built-in `createSlice`. Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.\");\n  }\n  const {\n    payloadCreator,\n    fulfilled,\n    pending,\n    rejected,\n    settled,\n    options\n  } = reducerDefinition;\n  const thunk = cAT(type, payloadCreator, options);\n  context.exposeAction(reducerName, thunk);\n  if (fulfilled) {\n    context.addCase(thunk.fulfilled, fulfilled);\n  }\n  if (pending) {\n    context.addCase(thunk.pending, pending);\n  }\n  if (rejected) {\n    context.addCase(thunk.rejected, rejected);\n  }\n  if (settled) {\n    context.addMatcher(thunk.settled, settled);\n  }\n  context.exposeCaseReducer(reducerName, {\n    fulfilled: fulfilled || noop,\n    pending: pending || noop,\n    rejected: rejected || noop,\n    settled: settled || noop\n  });\n}\nfunction noop() {\n}\n\n// src/entities/entity_state.ts\nfunction getInitialEntityState() {\n  return {\n    ids: [],\n    entities: {}\n  };\n}\nfunction createInitialStateFactory(stateAdapter) {\n  function getInitialState(additionalState = {}, entities) {\n    const state = Object.assign(getInitialEntityState(), additionalState);\n    return entities ? stateAdapter.setAll(state, entities) : state;\n  }\n  return {\n    getInitialState\n  };\n}\n\n// src/entities/state_selectors.ts\nfunction createSelectorsFactory() {\n  function getSelectors(selectState, options = {}) {\n    const {\n      createSelector: createSelector2 = createDraftSafeSelector\n    } = options;\n    const selectIds = (state) => state.ids;\n    const selectEntities = (state) => state.entities;\n    const selectAll = createSelector2(selectIds, selectEntities, (ids, entities) => ids.map((id) => entities[id]));\n    const selectId = (_, id) => id;\n    const selectById = (entities, id) => entities[id];\n    const selectTotal = createSelector2(selectIds, (ids) => ids.length);\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal,\n        selectById: createSelector2(selectEntities, selectId, selectById)\n      };\n    }\n    const selectGlobalizedEntities = createSelector2(selectState, selectEntities);\n    return {\n      selectIds: createSelector2(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createSelector2(selectState, selectAll),\n      selectTotal: createSelector2(selectState, selectTotal),\n      selectById: createSelector2(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors\n  };\n}\n\n// src/entities/state_adapter.ts\n\nvar isDraftTyped = immer__WEBPACK_IMPORTED_MODULE_2__.isDraft;\nfunction createSingleArgumentStateOperator(mutator) {\n  const operator = createStateOperator((_, state) => mutator(state));\n  return function operation(state) {\n    return operator(state, void 0);\n  };\n}\nfunction createStateOperator(mutator) {\n  return function operation(state, arg) {\n    function isPayloadActionArgument(arg2) {\n      return isFSA(arg2);\n    }\n    const runMutator = (draft) => {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraftTyped(state)) {\n      runMutator(state);\n      return state;\n    }\n    return (0,immer__WEBPACK_IMPORTED_MODULE_2__.produce)(state, runMutator);\n  };\n}\n\n// src/entities/utils.ts\n\nfunction selectIdValue(entity, selectId) {\n  const key = selectId(entity);\n  if ( true && key === void 0) {\n    console.warn(\"The entity passed to the `selectId` implementation returned undefined.\", \"You should probably provide your own `selectId` implementation.\", \"The entity that was passed:\", entity, \"The `selectId` implementation:\", selectId.toString());\n  }\n  return key;\n}\nfunction ensureEntitiesArray(entities) {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nfunction getCurrent(value) {\n  return (0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(value) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__.current)(value) : value;\n}\nfunction splitAddedUpdatedEntities(newEntities, selectId, state) {\n  newEntities = ensureEntitiesArray(newEntities);\n  const existingIdsArray = getCurrent(state.ids);\n  const existingIds = new Set(existingIdsArray);\n  const added = [];\n  const addedIds = /* @__PURE__ */ new Set([]);\n  const updated = [];\n  for (const entity of newEntities) {\n    const id = selectIdValue(entity, selectId);\n    if (existingIds.has(id) || addedIds.has(id)) {\n      updated.push({\n        id,\n        changes: entity\n      });\n    } else {\n      addedIds.add(id);\n      added.push(entity);\n    }\n  }\n  return [added, updated, existingIdsArray];\n}\n\n// src/entities/unsorted_state_adapter.ts\nfunction createUnsortedStateAdapter(selectId) {\n  function addOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key);\n    state.entities[key] = entity;\n  }\n  function addManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key);\n    }\n    ;\n    state.entities[key] = entity;\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {};\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key, state) {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys, state) {\n    let didMutate = false;\n    keys.forEach((key) => {\n      if (key in state.entities) {\n        delete state.entities[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = state.ids.filter((id) => id in state.entities);\n    }\n  }\n  function removeAllMutably(state) {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys, update, state) {\n    const original3 = state.entities[update.id];\n    if (original3 === void 0) {\n      return false;\n    }\n    const updated = Object.assign({}, original3, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete state.entities[update.id];\n    }\n    ;\n    state.entities[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    const newKeys = {};\n    const updatesPerEntity = {};\n    updates.forEach((update) => {\n      if (update.id in state.entities) {\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          // Spreads ignore falsy values, so this works even if there isn't\n          // an existing update already at this key\n          changes: {\n            ...updatesPerEntity[update.id]?.changes,\n            ...update.changes\n          }\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    const didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter((update) => takeNewKey(newKeys, update, state)).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.values(state.entities).map((e) => selectIdValue(e, selectId));\n      }\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    addManyMutably(added, state);\n    updateManyMutably(updated, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}\n\n// src/entities/sorted_state_adapter.ts\nfunction findInsertIndex(sortedItems, item, comparisonFunction) {\n  let lowIndex = 0;\n  let highIndex = sortedItems.length;\n  while (lowIndex < highIndex) {\n    let middleIndex = lowIndex + highIndex >>> 1;\n    const currentItem = sortedItems[middleIndex];\n    const res = comparisonFunction(item, currentItem);\n    if (res >= 0) {\n      lowIndex = middleIndex + 1;\n    } else {\n      highIndex = middleIndex;\n    }\n  }\n  return lowIndex;\n}\nfunction insert(sortedItems, item, comparisonFunction) {\n  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);\n  sortedItems.splice(insertAtIndex, 0, item);\n  return sortedItems;\n}\nfunction createSortedStateAdapter(selectId, comparer) {\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n  function addOneMutably(entity, state) {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities, state, existingIds) {\n    newEntities = ensureEntitiesArray(newEntities);\n    const existingKeys = new Set(existingIds ?? getCurrent(state.ids));\n    const models = newEntities.filter((model) => !existingKeys.has(selectIdValue(model, selectId)));\n    if (models.length !== 0) {\n      mergeFunction(state, models);\n    }\n  }\n  function setOneMutably(entity, state) {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      for (const item of newEntities) {\n        delete state.entities[selectId(item)];\n      }\n      mergeFunction(state, newEntities);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {};\n    state.ids = [];\n    addManyMutably(newEntities, state, []);\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    let appliedUpdates = false;\n    let replacedIds = false;\n    for (let update of updates) {\n      const entity = state.entities[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      const newId = selectId(entity);\n      if (update.id !== newId) {\n        replacedIds = true;\n        delete state.entities[update.id];\n        const oldIndex = state.ids.indexOf(update.id);\n        state.ids[oldIndex] = newId;\n        state.entities[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      mergeFunction(state, [], appliedUpdates, replacedIds);\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    if (added.length) {\n      addManyMutably(added, state, existingIdsArray);\n    }\n    if (updated.length) {\n      updateManyMutably(updated, state);\n    }\n  }\n  function areArraysEqual(a, b) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  const mergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {\n    const currentEntities = getCurrent(state.entities);\n    const currentIds = getCurrent(state.ids);\n    const stateEntities = state.entities;\n    let ids = currentIds;\n    if (replacedIds) {\n      ids = new Set(currentIds);\n    }\n    let sortedEntities = [];\n    for (const id of ids) {\n      const entity = currentEntities[id];\n      if (entity) {\n        sortedEntities.push(entity);\n      }\n    }\n    const wasPreviouslyEmpty = sortedEntities.length === 0;\n    for (const item of addedItems) {\n      stateEntities[selectId(item)] = item;\n      if (!wasPreviouslyEmpty) {\n        insert(sortedEntities, item, comparer);\n      }\n    }\n    if (wasPreviouslyEmpty) {\n      sortedEntities = addedItems.slice().sort(comparer);\n    } else if (appliedUpdates) {\n      sortedEntities.sort(comparer);\n    }\n    const newSortedIds = sortedEntities.map(selectId);\n    if (!areArraysEqual(currentIds, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  };\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}\n\n// src/entities/create_adapter.ts\nfunction createEntityAdapter(options = {}) {\n  const {\n    selectId,\n    sortComparer\n  } = {\n    sortComparer: false,\n    selectId: (instance) => instance.id,\n    ...options\n  };\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  const stateFactory = createInitialStateFactory(stateAdapter);\n  const selectorsFactory = createSelectorsFactory();\n  return {\n    selectId,\n    sortComparer,\n    ...stateFactory,\n    ...selectorsFactory,\n    ...stateAdapter\n  };\n}\n\n// src/listenerMiddleware/index.ts\n\n\n// src/listenerMiddleware/exceptions.ts\nvar task = \"task\";\nvar listener = \"listener\";\nvar completed = \"completed\";\nvar cancelled = \"cancelled\";\nvar taskCancelled = `task-${cancelled}`;\nvar taskCompleted = `task-${completed}`;\nvar listenerCancelled = `${listener}-${cancelled}`;\nvar listenerCompleted = `${listener}-${completed}`;\nvar TaskAbortError = class {\n  constructor(code) {\n    this.code = code;\n    this.message = `${task} ${cancelled} (reason: ${code})`;\n  }\n  name = \"TaskAbortError\";\n  message;\n};\n\n// src/listenerMiddleware/utils.ts\nvar assertFunction = (func, expected) => {\n  if (typeof func !== \"function\") {\n    throw new TypeError( false ? 0 : `${expected} is not a function`);\n  }\n};\nvar noop2 = () => {\n};\nvar catchRejection = (promise, onError = noop2) => {\n  promise.catch(onError);\n  return promise;\n};\nvar addAbortSignalListener = (abortSignal, callback) => {\n  abortSignal.addEventListener(\"abort\", callback, {\n    once: true\n  });\n  return () => abortSignal.removeEventListener(\"abort\", callback);\n};\nvar abortControllerWithReason = (abortController, reason) => {\n  const signal = abortController.signal;\n  if (signal.aborted) {\n    return;\n  }\n  if (!(\"reason\" in signal)) {\n    Object.defineProperty(signal, \"reason\", {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  abortController.abort(reason);\n};\n\n// src/listenerMiddleware/task.ts\nvar validateActive = (signal) => {\n  if (signal.aborted) {\n    const {\n      reason\n    } = signal;\n    throw new TaskAbortError(reason);\n  }\n};\nfunction raceWithSignal(signal, promise) {\n  let cleanup = noop2;\n  return new Promise((resolve, reject) => {\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason));\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise.finally(() => cleanup()).then(resolve, reject);\n  }).finally(() => {\n    cleanup = noop2;\n  });\n}\nvar runTask = async (task2, cleanUp) => {\n  try {\n    await Promise.resolve();\n    const value = await task2();\n    return {\n      status: \"ok\",\n      value\n    };\n  } catch (error) {\n    return {\n      status: error instanceof TaskAbortError ? \"cancelled\" : \"rejected\",\n      error\n    };\n  } finally {\n    cleanUp?.();\n  }\n};\nvar createPause = (signal) => {\n  return (promise) => {\n    return catchRejection(raceWithSignal(signal, promise).then((output) => {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\nvar createDelay = (signal) => {\n  const pause = createPause(signal);\n  return (timeoutMs) => {\n    return pause(new Promise((resolve) => setTimeout(resolve, timeoutMs)));\n  };\n};\n\n// src/listenerMiddleware/index.ts\nvar {\n  assign\n} = Object;\nvar INTERNAL_NIL_TOKEN = {};\nvar alm = \"listenerMiddleware\";\nvar createFork = (parentAbortSignal, parentBlockingPromises) => {\n  const linkControllers = (controller) => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));\n  return (taskExecutor, opts) => {\n    assertFunction(taskExecutor, \"taskExecutor\");\n    const childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    const result = runTask(async () => {\n      validateActive(parentAbortSignal);\n      validateActive(childAbortController.signal);\n      const result2 = await taskExecutor({\n        pause: createPause(childAbortController.signal),\n        delay: createDelay(childAbortController.signal),\n        signal: childAbortController.signal\n      });\n      validateActive(childAbortController.signal);\n      return result2;\n    }, () => abortControllerWithReason(childAbortController, taskCompleted));\n    if (opts?.autoJoin) {\n      parentBlockingPromises.push(result.catch(noop2));\n    }\n    return {\n      result: createPause(parentAbortSignal)(result),\n      cancel() {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nvar createTakePattern = (startListening, signal) => {\n  const take = async (predicate, timeout) => {\n    validateActive(signal);\n    let unsubscribe = () => {\n    };\n    const tuplePromise = new Promise((resolve, reject) => {\n      let stopListening = startListening({\n        predicate,\n        effect: (action, listenerApi) => {\n          listenerApi.unsubscribe();\n          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n        }\n      });\n      unsubscribe = () => {\n        stopListening();\n        reject();\n      };\n    });\n    const promises = [tuplePromise];\n    if (timeout != null) {\n      promises.push(new Promise((resolve) => setTimeout(resolve, timeout, null)));\n    }\n    try {\n      const output = await raceWithSignal(signal, Promise.race(promises));\n      validateActive(signal);\n      return output;\n    } finally {\n      unsubscribe();\n    }\n  };\n  return (predicate, timeout) => catchRejection(take(predicate, timeout));\n};\nvar getListenerEntryPropsFrom = (options) => {\n  let {\n    type,\n    actionCreator,\n    matcher,\n    predicate,\n    effect\n  } = options;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {\n  } else {\n    throw new Error( false ? 0 : \"Creating or removing a listener requires one of the known fields for matching an action\");\n  }\n  assertFunction(effect, \"options.listener\");\n  return {\n    predicate,\n    type,\n    effect\n  };\n};\nvar createListenerEntry = /* @__PURE__ */ assign((options) => {\n  const {\n    type,\n    predicate,\n    effect\n  } = getListenerEntryPropsFrom(options);\n  const entry = {\n    id: nanoid(),\n    effect,\n    type,\n    predicate,\n    pending: /* @__PURE__ */ new Set(),\n    unsubscribe: () => {\n      throw new Error( false ? 0 : \"Unsubscribe not initialized\");\n    }\n  };\n  return entry;\n}, {\n  withTypes: () => createListenerEntry\n});\nvar findListenerEntry = (listenerMap, options) => {\n  const {\n    type,\n    effect,\n    predicate\n  } = getListenerEntryPropsFrom(options);\n  return Array.from(listenerMap.values()).find((entry) => {\n    const matchPredicateOrType = typeof type === \"string\" ? entry.type === type : entry.predicate === predicate;\n    return matchPredicateOrType && entry.effect === effect;\n  });\n};\nvar cancelActiveListeners = (entry) => {\n  entry.pending.forEach((controller) => {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nvar createClearListenerMiddleware = (listenerMap) => {\n  return () => {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\nvar safelyNotifyError = (errorHandler, errorToNotify, errorInfo) => {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    setTimeout(() => {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\nvar addListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/add`), {\n  withTypes: () => addListener\n});\nvar clearAllListeners = /* @__PURE__ */ createAction(`${alm}/removeAll`);\nvar removeListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/remove`), {\n  withTypes: () => removeListener\n});\nvar defaultErrorHandler = (...args) => {\n  console.error(`${alm}/error`, ...args);\n};\nvar createListenerMiddleware = (middlewareOptions = {}) => {\n  const listenerMap = /* @__PURE__ */ new Map();\n  const {\n    extra,\n    onError = defaultErrorHandler\n  } = middlewareOptions;\n  assertFunction(onError, \"onError\");\n  const insertEntry = (entry) => {\n    entry.unsubscribe = () => listenerMap.delete(entry.id);\n    listenerMap.set(entry.id, entry);\n    return (cancelOptions) => {\n      entry.unsubscribe();\n      if (cancelOptions?.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  const startListening = (options) => {\n    const entry = findListenerEntry(listenerMap, options) ?? createListenerEntry(options);\n    return insertEntry(entry);\n  };\n  assign(startListening, {\n    withTypes: () => startListening\n  });\n  const stopListening = (options) => {\n    const entry = findListenerEntry(listenerMap, options);\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  assign(stopListening, {\n    withTypes: () => stopListening\n  });\n  const notifyListener = async (entry, action, api, getOriginalState) => {\n    const internalTaskController = new AbortController();\n    const take = createTakePattern(startListening, internalTaskController.signal);\n    const autoJoinPromises = [];\n    try {\n      entry.pending.add(internalTaskController);\n      await Promise.resolve(entry.effect(\n        action,\n        // Use assign() rather than ... to avoid extra helper functions added to bundle\n        assign({}, api, {\n          getOriginalState,\n          condition: (predicate, timeout) => take(predicate, timeout).then(Boolean),\n          take,\n          delay: createDelay(internalTaskController.signal),\n          pause: createPause(internalTaskController.signal),\n          extra,\n          signal: internalTaskController.signal,\n          fork: createFork(internalTaskController.signal, autoJoinPromises),\n          unsubscribe: entry.unsubscribe,\n          subscribe: () => {\n            listenerMap.set(entry.id, entry);\n          },\n          cancelActiveListeners: () => {\n            entry.pending.forEach((controller, _, set) => {\n              if (controller !== internalTaskController) {\n                abortControllerWithReason(controller, listenerCancelled);\n                set.delete(controller);\n              }\n            });\n          },\n          cancel: () => {\n            abortControllerWithReason(internalTaskController, listenerCancelled);\n            entry.pending.delete(internalTaskController);\n          },\n          throwIfCancelled: () => {\n            validateActive(internalTaskController.signal);\n          }\n        })\n      ));\n    } catch (listenerError) {\n      if (!(listenerError instanceof TaskAbortError)) {\n        safelyNotifyError(onError, listenerError, {\n          raisedBy: \"effect\"\n        });\n      }\n    } finally {\n      await Promise.all(autoJoinPromises);\n      abortControllerWithReason(internalTaskController, listenerCompleted);\n      entry.pending.delete(internalTaskController);\n    }\n  };\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  const middleware = (api) => (next) => (action) => {\n    if (!(0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action)) {\n      return next(action);\n    }\n    if (addListener.match(action)) {\n      return startListening(action.payload);\n    }\n    if (clearAllListeners.match(action)) {\n      clearListenerMiddleware();\n      return;\n    }\n    if (removeListener.match(action)) {\n      return stopListening(action.payload);\n    }\n    let originalState = api.getState();\n    const getOriginalState = () => {\n      if (originalState === INTERNAL_NIL_TOKEN) {\n        throw new Error( false ? 0 : `${alm}: getOriginalState can only be called synchronously`);\n      }\n      return originalState;\n    };\n    let result;\n    try {\n      result = next(action);\n      if (listenerMap.size > 0) {\n        const currentState = api.getState();\n        const listenerEntries = Array.from(listenerMap.values());\n        for (const entry of listenerEntries) {\n          let runListener = false;\n          try {\n            runListener = entry.predicate(action, currentState, originalState);\n          } catch (predicateError) {\n            runListener = false;\n            safelyNotifyError(onError, predicateError, {\n              raisedBy: \"predicate\"\n            });\n          }\n          if (!runListener) {\n            continue;\n          }\n          notifyListener(entry, action, api, getOriginalState);\n        }\n      }\n    } finally {\n      originalState = INTERNAL_NIL_TOKEN;\n    }\n    return result;\n  };\n  return {\n    middleware,\n    startListening,\n    stopListening,\n    clearListeners: clearListenerMiddleware\n  };\n};\n\n// src/dynamicMiddleware/index.ts\n\nvar createMiddlewareEntry = (middleware) => ({\n  middleware,\n  applied: /* @__PURE__ */ new Map()\n});\nvar matchInstance = (instanceId) => (action) => action?.meta?.instanceId === instanceId;\nvar createDynamicMiddleware = () => {\n  const instanceId = nanoid();\n  const middlewareMap = /* @__PURE__ */ new Map();\n  const withMiddleware = Object.assign(createAction(\"dynamicMiddleware/add\", (...middlewares) => ({\n    payload: middlewares,\n    meta: {\n      instanceId\n    }\n  })), {\n    withTypes: () => withMiddleware\n  });\n  const addMiddleware = Object.assign(function addMiddleware2(...middlewares) {\n    middlewares.forEach((middleware2) => {\n      getOrInsertComputed(middlewareMap, middleware2, createMiddlewareEntry);\n    });\n  }, {\n    withTypes: () => addMiddleware\n  });\n  const getFinalMiddleware = (api) => {\n    const appliedMiddleware = Array.from(middlewareMap.values()).map((entry) => getOrInsertComputed(entry.applied, api, entry.middleware));\n    return (0,redux__WEBPACK_IMPORTED_MODULE_0__.compose)(...appliedMiddleware);\n  };\n  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));\n  const middleware = (api) => (next) => (action) => {\n    if (isWithMiddleware(action)) {\n      addMiddleware(...action.payload);\n      return api.dispatch;\n    }\n    return getFinalMiddleware(api)(next)(action);\n  };\n  return {\n    middleware,\n    addMiddleware,\n    withMiddleware,\n    instanceId\n  };\n};\n\n// src/combineSlices.ts\n\nvar isSliceLike = (maybeSliceLike) => \"reducerPath\" in maybeSliceLike && typeof maybeSliceLike.reducerPath === \"string\";\nvar getReducers = (slices) => slices.flatMap((sliceOrMap) => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer]] : Object.entries(sliceOrMap));\nvar ORIGINAL_STATE = Symbol.for(\"rtk-state-proxy-original\");\nvar isStateProxy = (value) => !!value && !!value[ORIGINAL_STATE];\nvar stateProxyMap = /* @__PURE__ */ new WeakMap();\nvar createStateProxy = (state, reducerMap, initialStateCache) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {\n  get: (target, prop, receiver) => {\n    if (prop === ORIGINAL_STATE) return target;\n    const result = Reflect.get(target, prop, receiver);\n    if (typeof result === \"undefined\") {\n      const cached = initialStateCache[prop];\n      if (typeof cached !== \"undefined\") return cached;\n      const reducer = reducerMap[prop];\n      if (reducer) {\n        const reducerResult = reducer(void 0, {\n          type: nanoid()\n        });\n        if (typeof reducerResult === \"undefined\") {\n          throw new Error( false ? 0 : `The slice reducer for key \"${prop.toString()}\" returned undefined when called for selector(). If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);\n        }\n        initialStateCache[prop] = reducerResult;\n        return reducerResult;\n      }\n    }\n    return result;\n  }\n}));\nvar original = (state) => {\n  if (!isStateProxy(state)) {\n    throw new Error( false ? 0 : \"original must be used on state Proxy\");\n  }\n  return state[ORIGINAL_STATE];\n};\nvar emptyObject = {};\nvar noopReducer = (state = emptyObject) => state;\nfunction combineSlices(...slices) {\n  const reducerMap = Object.fromEntries(getReducers(slices));\n  const getReducer = () => Object.keys(reducerMap).length ? (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)(reducerMap) : noopReducer;\n  let reducer = getReducer();\n  function combinedReducer(state, action) {\n    return reducer(state, action);\n  }\n  combinedReducer.withLazyLoadedSlices = () => combinedReducer;\n  const initialStateCache = {};\n  const inject = (slice, config = {}) => {\n    const {\n      reducerPath,\n      reducer: reducerToInject\n    } = slice;\n    const currentReducer = reducerMap[reducerPath];\n    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {\n      if (typeof process !== \"undefined\" && \"development\" === \"development\") {\n        console.error(`called \\`inject\\` to override already-existing reducer ${reducerPath} without specifying \\`overrideExisting: true\\``);\n      }\n      return combinedReducer;\n    }\n    if (config.overrideExisting && currentReducer !== reducerToInject) {\n      delete initialStateCache[reducerPath];\n    }\n    reducerMap[reducerPath] = reducerToInject;\n    reducer = getReducer();\n    return combinedReducer;\n  };\n  const selector = Object.assign(function makeSelector(selectorFn, selectState) {\n    return function selector2(state, ...args) {\n      return selectorFn(createStateProxy(selectState ? selectState(state, ...args) : state, reducerMap, initialStateCache), ...args);\n    };\n  }, {\n    original\n  });\n  return Object.assign(combinedReducer, {\n    inject,\n    selector\n  });\n}\n\n// src/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n  return `Minified Redux Toolkit error #${code}; visit https://redux-toolkit.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;\n}\n\n//# sourceMappingURL=redux-toolkit.modern.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/@reduxjs+toolkit@2.8.2_reac_13e03a16b3880bf31db5b9783f72eaa4/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/redux@5.0.1/node_modules/redux/dist/redux.mjs":
/*!******************************************************************************!*\
  !*** ../../node_modules/.pnpm/redux@5.0.1/node_modules/redux/dist/redux.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __DO_NOT_USE__ActionTypes: () => (/* binding */ actionTypes_default),\n/* harmony export */   applyMiddleware: () => (/* binding */ applyMiddleware),\n/* harmony export */   bindActionCreators: () => (/* binding */ bindActionCreators),\n/* harmony export */   combineReducers: () => (/* binding */ combineReducers),\n/* harmony export */   compose: () => (/* binding */ compose),\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   isAction: () => (/* binding */ isAction),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   legacy_createStore: () => (/* binding */ legacy_createStore)\n/* harmony export */ });\n// src/utils/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n  return `Minified Redux error #${code}; visit https://redux.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;\n}\n\n// src/utils/symbol-observable.ts\nvar $$observable = /* @__PURE__ */ (() => typeof Symbol === \"function\" && Symbol.observable || \"@@observable\")();\nvar symbol_observable_default = $$observable;\n\n// src/utils/actionTypes.ts\nvar randomString = () => Math.random().toString(36).substring(7).split(\"\").join(\".\");\nvar ActionTypes = {\n  INIT: `@@redux/INIT${/* @__PURE__ */ randomString()}`,\n  REPLACE: `@@redux/REPLACE${/* @__PURE__ */ randomString()}`,\n  PROBE_UNKNOWN_ACTION: () => `@@redux/PROBE_UNKNOWN_ACTION${randomString()}`\n};\nvar actionTypes_default = ActionTypes;\n\n// src/utils/isPlainObject.ts\nfunction isPlainObject(obj) {\n  if (typeof obj !== \"object\" || obj === null)\n    return false;\n  let proto = obj;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(obj) === proto || Object.getPrototypeOf(obj) === null;\n}\n\n// src/utils/kindOf.ts\nfunction miniKindOf(val) {\n  if (val === void 0)\n    return \"undefined\";\n  if (val === null)\n    return \"null\";\n  const type = typeof val;\n  switch (type) {\n    case \"boolean\":\n    case \"string\":\n    case \"number\":\n    case \"symbol\":\n    case \"function\": {\n      return type;\n    }\n  }\n  if (Array.isArray(val))\n    return \"array\";\n  if (isDate(val))\n    return \"date\";\n  if (isError(val))\n    return \"error\";\n  const constructorName = ctorName(val);\n  switch (constructorName) {\n    case \"Symbol\":\n    case \"Promise\":\n    case \"WeakMap\":\n    case \"WeakSet\":\n    case \"Map\":\n    case \"Set\":\n      return constructorName;\n  }\n  return Object.prototype.toString.call(val).slice(8, -1).toLowerCase().replace(/\\s/g, \"\");\n}\nfunction ctorName(val) {\n  return typeof val.constructor === \"function\" ? val.constructor.name : null;\n}\nfunction isError(val) {\n  return val instanceof Error || typeof val.message === \"string\" && val.constructor && typeof val.constructor.stackTraceLimit === \"number\";\n}\nfunction isDate(val) {\n  if (val instanceof Date)\n    return true;\n  return typeof val.toDateString === \"function\" && typeof val.getDate === \"function\" && typeof val.setDate === \"function\";\n}\nfunction kindOf(val) {\n  let typeOfVal = typeof val;\n  if (true) {\n    typeOfVal = miniKindOf(val);\n  }\n  return typeOfVal;\n}\n\n// src/createStore.ts\nfunction createStore(reducer, preloadedState, enhancer) {\n  if (typeof reducer !== \"function\") {\n    throw new Error( false ? 0 : `Expected the root reducer to be a function. Instead, received: '${kindOf(reducer)}'`);\n  }\n  if (typeof preloadedState === \"function\" && typeof enhancer === \"function\" || typeof enhancer === \"function\" && typeof arguments[3] === \"function\") {\n    throw new Error( false ? 0 : \"It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.\");\n  }\n  if (typeof preloadedState === \"function\" && typeof enhancer === \"undefined\") {\n    enhancer = preloadedState;\n    preloadedState = void 0;\n  }\n  if (typeof enhancer !== \"undefined\") {\n    if (typeof enhancer !== \"function\") {\n      throw new Error( false ? 0 : `Expected the enhancer to be a function. Instead, received: '${kindOf(enhancer)}'`);\n    }\n    return enhancer(createStore)(reducer, preloadedState);\n  }\n  let currentReducer = reducer;\n  let currentState = preloadedState;\n  let currentListeners = /* @__PURE__ */ new Map();\n  let nextListeners = currentListeners;\n  let listenerIdCounter = 0;\n  let isDispatching = false;\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = /* @__PURE__ */ new Map();\n      currentListeners.forEach((listener, key) => {\n        nextListeners.set(key, listener);\n      });\n    }\n  }\n  function getState() {\n    if (isDispatching) {\n      throw new Error( false ? 0 : \"You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.\");\n    }\n    return currentState;\n  }\n  function subscribe(listener) {\n    if (typeof listener !== \"function\") {\n      throw new Error( false ? 0 : `Expected the listener to be a function. Instead, received: '${kindOf(listener)}'`);\n    }\n    if (isDispatching) {\n      throw new Error( false ? 0 : \"You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api/store#subscribelistener for more details.\");\n    }\n    let isSubscribed = true;\n    ensureCanMutateNextListeners();\n    const listenerId = listenerIdCounter++;\n    nextListeners.set(listenerId, listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n      if (isDispatching) {\n        throw new Error( false ? 0 : \"You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api/store#subscribelistener for more details.\");\n      }\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      nextListeners.delete(listenerId);\n      currentListeners = null;\n    };\n  }\n  function dispatch(action) {\n    if (!isPlainObject(action)) {\n      throw new Error( false ? 0 : `Actions must be plain objects. Instead, the actual type was: '${kindOf(action)}'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.`);\n    }\n    if (typeof action.type === \"undefined\") {\n      throw new Error( false ? 0 : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n    if (typeof action.type !== \"string\") {\n      throw new Error( false ? 0 : `Action \"type\" property must be a string. Instead, the actual type was: '${kindOf(action.type)}'. Value was: '${action.type}' (stringified)`);\n    }\n    if (isDispatching) {\n      throw new Error( false ? 0 : \"Reducers may not dispatch actions.\");\n    }\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n    const listeners = currentListeners = nextListeners;\n    listeners.forEach((listener) => {\n      listener();\n    });\n    return action;\n  }\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== \"function\") {\n      throw new Error( false ? 0 : `Expected the nextReducer to be a function. Instead, received: '${kindOf(nextReducer)}`);\n    }\n    currentReducer = nextReducer;\n    dispatch({\n      type: actionTypes_default.REPLACE\n    });\n  }\n  function observable() {\n    const outerSubscribe = subscribe;\n    return {\n      /**\n       * The minimal observable subscription method.\n       * @param observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe(observer) {\n        if (typeof observer !== \"object\" || observer === null) {\n          throw new Error( false ? 0 : `Expected the observer to be an object. Instead, received: '${kindOf(observer)}'`);\n        }\n        function observeState() {\n          const observerAsObserver = observer;\n          if (observerAsObserver.next) {\n            observerAsObserver.next(getState());\n          }\n        }\n        observeState();\n        const unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe\n        };\n      },\n      [symbol_observable_default]() {\n        return this;\n      }\n    };\n  }\n  dispatch({\n    type: actionTypes_default.INIT\n  });\n  const store = {\n    dispatch,\n    subscribe,\n    getState,\n    replaceReducer,\n    [symbol_observable_default]: observable\n  };\n  return store;\n}\nfunction legacy_createStore(reducer, preloadedState, enhancer) {\n  return createStore(reducer, preloadedState, enhancer);\n}\n\n// src/utils/warning.ts\nfunction warning(message) {\n  if (typeof console !== \"undefined\" && typeof console.error === \"function\") {\n    console.error(message);\n  }\n  try {\n    throw new Error(message);\n  } catch (e) {\n  }\n}\n\n// src/combineReducers.ts\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  const reducerKeys = Object.keys(reducers);\n  const argumentName = action && action.type === actionTypes_default.INIT ? \"preloadedState argument passed to createStore\" : \"previous state received by the reducer\";\n  if (reducerKeys.length === 0) {\n    return \"Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.\";\n  }\n  if (!isPlainObject(inputState)) {\n    return `The ${argumentName} has unexpected type of \"${kindOf(inputState)}\". Expected argument to be an object with the following keys: \"${reducerKeys.join('\", \"')}\"`;\n  }\n  const unexpectedKeys = Object.keys(inputState).filter((key) => !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key]);\n  unexpectedKeys.forEach((key) => {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === actionTypes_default.REPLACE)\n    return;\n  if (unexpectedKeys.length > 0) {\n    return `Unexpected ${unexpectedKeys.length > 1 ? \"keys\" : \"key\"} \"${unexpectedKeys.join('\", \"')}\" found in ${argumentName}. Expected to find one of the known reducer keys instead: \"${reducerKeys.join('\", \"')}\". Unexpected keys will be ignored.`;\n  }\n}\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach((key) => {\n    const reducer = reducers[key];\n    const initialState = reducer(void 0, {\n      type: actionTypes_default.INIT\n    });\n    if (typeof initialState === \"undefined\") {\n      throw new Error( false ? 0 : `The slice reducer for key \"${key}\" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);\n    }\n    if (typeof reducer(void 0, {\n      type: actionTypes_default.PROBE_UNKNOWN_ACTION()\n    }) === \"undefined\") {\n      throw new Error( false ? 0 : `The slice reducer for key \"${key}\" returned undefined when probed with a random type. Don't try to handle '${actionTypes_default.INIT}' or other actions in \"redux/*\" namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined, but can be null.`);\n    }\n  });\n}\nfunction combineReducers(reducers) {\n  const reducerKeys = Object.keys(reducers);\n  const finalReducers = {};\n  for (let i = 0; i < reducerKeys.length; i++) {\n    const key = reducerKeys[i];\n    if (true) {\n      if (typeof reducers[key] === \"undefined\") {\n        warning(`No reducer provided for key \"${key}\"`);\n      }\n    }\n    if (typeof reducers[key] === \"function\") {\n      finalReducers[key] = reducers[key];\n    }\n  }\n  const finalReducerKeys = Object.keys(finalReducers);\n  let unexpectedKeyCache;\n  if (true) {\n    unexpectedKeyCache = {};\n  }\n  let shapeAssertionError;\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n  return function combination(state = {}, action) {\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n    if (true) {\n      const warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n    let hasChanged = false;\n    const nextState = {};\n    for (let i = 0; i < finalReducerKeys.length; i++) {\n      const key = finalReducerKeys[i];\n      const reducer = finalReducers[key];\n      const previousStateForKey = state[key];\n      const nextStateForKey = reducer(previousStateForKey, action);\n      if (typeof nextStateForKey === \"undefined\") {\n        const actionType = action && action.type;\n        throw new Error( false ? 0 : `When called with an action of type ${actionType ? `\"${String(actionType)}\"` : \"(unknown type)\"}, the slice reducer for key \"${key}\" returned undefined. To ignore an action, you must explicitly return the previous state. If you want this reducer to hold no value, you can return null instead of undefined.`);\n      }\n      nextState[key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}\n\n// src/bindActionCreators.ts\nfunction bindActionCreator(actionCreator, dispatch) {\n  return function(...args) {\n    return dispatch(actionCreator.apply(this, args));\n  };\n}\nfunction bindActionCreators(actionCreators, dispatch) {\n  if (typeof actionCreators === \"function\") {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n  if (typeof actionCreators !== \"object\" || actionCreators === null) {\n    throw new Error( false ? 0 : `bindActionCreators expected an object or a function, but instead received: '${kindOf(actionCreators)}'. Did you write \"import ActionCreators from\" instead of \"import * as ActionCreators from\"?`);\n  }\n  const boundActionCreators = {};\n  for (const key in actionCreators) {\n    const actionCreator = actionCreators[key];\n    if (typeof actionCreator === \"function\") {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n  return boundActionCreators;\n}\n\n// src/compose.ts\nfunction compose(...funcs) {\n  if (funcs.length === 0) {\n    return (arg) => arg;\n  }\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n  return funcs.reduce((a, b) => (...args) => a(b(...args)));\n}\n\n// src/applyMiddleware.ts\nfunction applyMiddleware(...middlewares) {\n  return (createStore2) => (reducer, preloadedState) => {\n    const store = createStore2(reducer, preloadedState);\n    let dispatch = () => {\n      throw new Error( false ? 0 : \"Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.\");\n    };\n    const middlewareAPI = {\n      getState: store.getState,\n      dispatch: (action, ...args) => dispatch(action, ...args)\n    };\n    const chain = middlewares.map((middleware) => middleware(middlewareAPI));\n    dispatch = compose(...chain)(store.dispatch);\n    return {\n      ...store,\n      dispatch\n    };\n  };\n}\n\n// src/utils/isAction.ts\nfunction isAction(action) {\n  return isPlainObject(action) && \"type\" in action && typeof action.type === \"string\";\n}\n\n//# sourceMappingURL=redux.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/redux@5.0.1/node_modules/redux/dist/redux.mjs\n"));

/***/ })

}]);