"use strict";exports.id=2315,exports.ids=[2315],exports.modules={12315:(e,a,l)=>{l.a(e,async(e,r)=>{try{l.r(a),l.d(a,{default:()=>u});var i=l(8732),n=l(37783),t=l(82015),s=l.n(t),o=e([n]);n=(o.then?(await o)():o)[0];let h=[{value:"GMT-12:00",label:"(GMT-12:00) International Date Line West"},{value:"GMT-11:00",label:"(GMT-11:00) Midway Island, Samoa"},{value:"GMT-10:00",label:"(GMT-10:00) Hawaii"},{value:"GMT-09:00",label:"(GMT-09:00) Alaska"},{value:"GMT-08:00",label:"(GMT-08:00) Pacific Time (US & Canada)"},{value:"GMT-07:00",label:"(GMT-07:00) Mountain Time (US & Canada)"},{value:"GMT-06:00",label:"(GMT-06:00) Central Time (US & Canada)"},{value:"GMT-05:00",label:"(GMT-05:00) Eastern Time (US & Canada)"},{value:"GMT-04:00",label:"(GMT-04:00) Atlantic Time (Canada)"},{value:"GMT-03:00",label:"(GMT-03:00) Buenos Aires, Georgetown"},{value:"GMT-02:00",label:"(GMT-02:00) Mid-Atlantic"},{value:"GMT-01:00",label:"(GMT-01:00) Azores, Cape Verde Islands"},{value:"GMT+00:00",label:"(GMT+00:00) London, Dublin, Edinburgh"},{value:"GMT+01:00",label:"(GMT+01:00) Paris, Amsterdam, Berlin"},{value:"GMT+02:00",label:"(GMT+02:00) Athens, Istanbul, Helsinki"},{value:"GMT+03:00",label:"(GMT+03:00) Moscow, Baghdad, Kuwait"},{value:"GMT+04:00",label:"(GMT+04:00) Abu Dhabi, Dubai, Baku"},{value:"GMT+05:00",label:"(GMT+05:00) Karachi, Tashkent"},{value:"GMT+06:00",label:"(GMT+06:00) Dhaka, Almaty"},{value:"GMT+07:00",label:"(GMT+07:00) Bangkok, Jakarta"},{value:"GMT+08:00",label:"(GMT+08:00) Beijing, Singapore, Hong Kong"},{value:"GMT+09:00",label:"(GMT+09:00) Tokyo, Seoul, Osaka"},{value:"GMT+10:00",label:"(GMT+10:00) Sydney, Melbourne, Brisbane"},{value:"GMT+11:00",label:"(GMT+11:00) Solomon Islands"},{value:"GMT+12:00",label:"(GMT+12:00) Auckland, Wellington"}],u=s().memo(({session:e,onFormChange:a,initialData:l,motivationLabel:r="Why do you want to be a moderator?",motivationPlaceholder:s="Tell us about your motivation and what you can bring to the team..."})=>{let o=(0,t.useRef)(null),[u,d]=(0,t.useState)({age:l?.age||"",hoursPerWeek:l?.hoursPerWeek||"",timezone:l?.timezone||"",motivation:l?.motivation||""}),b=(0,t.useRef)(u);b.current=u,(0,t.useEffect)(()=>{o.current&&o.current.scrollIntoView({behavior:"smooth",block:"start"})},[]),(0,t.useEffect)(()=>{let e=setTimeout(()=>{a(b.current)},100);return()=>clearTimeout(e)},[u,a]);let T=(0,t.useCallback)((e,a)=>{d(l=>({...l,[e]:a}))},[]);return(0,i.jsx)(n.az,{ref:o,children:(0,i.jsxs)(n.Tk,{spacing:6,align:"stretch",children:[(0,i.jsx)(n.Zp,{bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",children:(0,i.jsx)(n.bw,{children:(0,i.jsxs)(n.rS,{columns:2,spacing:4,children:[(0,i.jsxs)(n.MJ,{children:[(0,i.jsx)(n.lR,{children:"Discord Username"}),(0,i.jsx)(n.pd,{value:e?.user?.name??"",isReadOnly:!0,bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"}})]}),(0,i.jsxs)(n.MJ,{children:[(0,i.jsx)(n.lR,{children:"Discord User ID"}),(0,i.jsx)(n.pd,{value:e?.user?.id??"",isReadOnly:!0,bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"}})]})]})})}),(0,i.jsx)(n.Zp,{bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",mt:4,children:(0,i.jsx)(n.bw,{children:(0,i.jsxs)(n.rS,{columns:3,spacing:4,children:[(0,i.jsxs)(n.MJ,{isRequired:!0,children:[(0,i.jsx)(n.lR,{children:"Age"}),(0,i.jsx)(n.pd,{type:"number",value:u.age,onChange:e=>T("age",e.target.value),min:13,placeholder:"Enter your age",bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"}})]}),(0,i.jsxs)(n.MJ,{isRequired:!0,children:[(0,i.jsx)(n.lR,{children:"Hours per Week"}),(0,i.jsx)(n.pd,{type:"number",value:u.hoursPerWeek,onChange:e=>T("hoursPerWeek",e.target.value),min:1,placeholder:"Hours available",bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"}})]}),(0,i.jsxs)(n.MJ,{isRequired:!0,children:[(0,i.jsx)(n.lR,{children:"Timezone"}),(0,i.jsx)(n.l6,{value:u.timezone,onChange:e=>T("timezone",e.target.value),placeholder:"Select timezone",bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"},children:h.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})})}),(0,i.jsx)(n.Zp,{bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",mt:4,children:(0,i.jsx)(n.bw,{children:(0,i.jsxs)(n.MJ,{isRequired:!0,children:[(0,i.jsx)(n.lR,{children:r}),(0,i.jsx)(n.TM,{value:u.motivation,onChange:e=>T("motivation",e.target.value),placeholder:s,minH:"200px",bg:"whiteAlpha.100",_hover:{bg:"whiteAlpha.200"},resize:"vertical"})]})})})]})})});r()}catch(e){r(e)}})},37783:(e,a,l)=>{l.a(e,async(e,r)=>{try{l.d(a,{MJ:()=>s.MJ,TM:()=>b.T,Tk:()=>T.T,Zp:()=>n.Z,az:()=>i.a,bw:()=>t.b,l6:()=>u.l,lR:()=>o.l,pd:()=>h.p,rS:()=>d.r});var i=l(45200),n=l(90846),t=l(60615),s=l(23678),o=l(63957),h=l(15376),u=l(29742),d=l(67981),b=l(37506),T=l(17335),M=e([i,n,t,s,o,h,u,d,b,T]);[i,n,t,s,o,h,u,d,b,T]=M.then?(await M)():M,r()}catch(e){r(e)}})}};